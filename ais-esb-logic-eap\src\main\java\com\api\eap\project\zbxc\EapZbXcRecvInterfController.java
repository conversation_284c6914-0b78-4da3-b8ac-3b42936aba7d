package com.api.eap.project.zbxc;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 淄博芯材EAP定义接口
 * 1.EAP询问机台是否在线
 * 2.EAP收到时间信号返回
 * 3.EAP调用此接口下发信息给设备
 * 4.EAP给收放板机下发载具允许置载或不允许置载指令
 * 5.EAP给收放板机下发开始、取消等事件报告
 * 6.EAP调用此接口下发Lot信息
 * 7.Panel信息请求信号返回
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-10
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/zbxc/interf/recv")
public class EapZbXcRecvInterfController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private EapZbXcRecvInterfFunc eapZbXcRecvInterfFunc;

    //EAP询问机台是否在线
    @RequestMapping(value = "/AreYouThereReply", method = {RequestMethod.POST,RequestMethod.GET})
    public String AreYouThereReply(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/zbxc/interf/recv/AreYouThereReply";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapZbXcRecvInterfFunc.aisAreYouThereReply(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //EAP收到时间信号返回
    @RequestMapping(value = "/DateTimeCommand", method = {RequestMethod.POST,RequestMethod.GET})
    public String DateTimeCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/zbxc/interf/recv/DateTimeCommand";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapZbXcRecvInterfFunc.aisDateTimeCommand(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //EAP调用此接口下发信息给设备
    @RequestMapping(value = "/CIMMessageCommand", method = {RequestMethod.POST,RequestMethod.GET})
    public String CIMMessageCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/zbxc/interf/recv/CIMMessageCommand";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapZbXcRecvInterfFunc.aisCIMMessageCommand(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //EAP给收放板机下发载具允许置载或不允许置载指令
    @RequestMapping(value = "/LoadControlCommand", method = {RequestMethod.POST,RequestMethod.GET})
    public String LoadControlCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/zbxc/interf/recv/LoadControlCommand";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapZbXcRecvInterfFunc.aisLoadControlCommand(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //EAP给收放板机下发开始、取消等事件报告
    @RequestMapping(value = "/CarrierControlCommand", method = {RequestMethod.POST,RequestMethod.GET})
    public String CarrierControlCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/zbxc/interf/recv/CarrierControlCommand";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapZbXcRecvInterfFunc.aisCarrierControlCommand(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //EAP调用此接口下发Lot信息
    @RequestMapping(value = "/CarrierLotInfoDownloadCommand", method = {RequestMethod.POST,RequestMethod.GET})
    public String CarrierLotInfoDownloadCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/zbxc/interf/recv/CarrierLotInfoDownloadCommand";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapZbXcRecvInterfFunc.aisCarrierLotInfoDownloadCommand(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //Panel信息请求信号返回
    @RequestMapping(value = "/PanelInformationRequestReply", method = {RequestMethod.POST,RequestMethod.GET})
    public String PanelInformationRequestReply(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/zbxc/interf/recv/PanelInformationRequestReply";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapZbXcRecvInterfFunc.aisPanelInformationRequestReply(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }
}
