package com.api.eap.project.sfjd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 晟丰健鼎操作对外API接口
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-14
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/sfjd")
public class EapProjectSfjdOpController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.保存配方数据到数据库
    @RequestMapping(value = "/EapProjectSfjdRecipeSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapProjectSfjdRecipeSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/sfjd/EapProjectSfjdRecipeSave";
        String transResult = "";
        String errorMsg = "";
        String apsRecipeTable = "a_eap_aps_recipe";
        try {
            String station_id = jsonParas.getString("station_id");
            JSONObject recipe = jsonParas.getJSONObject("recipe");
            //插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstRecipeDocuments = new ArrayList<>();
            Map<String, Object> mapBigDataRow = new HashMap<>();
            String recipe_id = CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("recipe_id", recipe_id);
            mapBigDataRow.put("station_id", Long.parseLong(station_id));
            mapBigDataRow.put("lot_num", recipe.getString("lot_num"));
            mapBigDataRow.put("batch_no", recipe.getString("batch_no"));
            mapBigDataRow.put("panel_length", recipe.getString("panel_length"));
            mapBigDataRow.put("panel_width", recipe.getString("panel_width"));
            mapBigDataRow.put("panel_tickness", recipe.getString("panel_tickness"));
            mapBigDataRow.put("ink_type", recipe.getString("ink_type"));
            mapBigDataRow.put("material", recipe.getString("material"));
            mapBigDataRow.put("min_aperture", recipe.getString("min_aperture"));
            mapBigDataRow.put("a_r", recipe.getString("a_r"));
            mapBigDataRow.put("jack_panel_tickness", recipe.getString("jack_panel_tickness"));
            mapBigDataRow.put("scraping_speed", recipe.getString("scraping_speed"));
            mapBigDataRow.put("scraping_pressure", recipe.getString("scraping_pressure"));
            mapBigDataRow.put("vacuum_degree", recipe.getString("vacuum_degree"));
            mapBigDataRow.put("pre_lifting_net_height", recipe.getString("pre_lifting_net_height"));
            mapBigDataRow.put("lifting_net_height", recipe.getString("lifting_net_height"));
            mapBigDataRow.put("plates_distance", recipe.getString("plates_distance"));
            mapBigDataRow.put("note", recipe.getString("note"));

            lstRecipeDocuments.add(mapBigDataRow);
            mongoTemplate.insert(lstRecipeDocuments, apsRecipeTable);
            EapProjectSfjdOpeLogIns("EAP返回配方", "EAP->EQP", jsonParas.toJSONString(), "下发成功");
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "插入成功", 0);
        } catch (Exception ex) {
            errorMsg = "保存配方数据到数据库异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.保存ANDON信息到数据库
    @RequestMapping(value = "/EapProjectSfjdAndonSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapProjectSfjdAndonSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/sfjd/EapProjectSfjdAndonSave";
        String transResult = "";
        String errorMsg = "";
        String apsAndonTable = "a_eap_andon_event";
        try {
            JSONObject andonInfo = JSONObject.parseObject(jsonParas.getString("andonInfo"));
            String station_id = andonInfo.getString("station_id");
            Long stationId = Long.parseLong(station_id);
            String andon_mode = andonInfo.getString("andon_mode");//andon模式，1标识触发；2标识复位
            String andon_type = andonInfo.getString("andon_type");
            String current_user = andonInfo.getString("current_user");

            String andon_id = "";
            String happen_date = "";
            String reset_date = "";
            Long cost_times = 0L;
            String operation_status = "";
            if (andon_mode.equals("1")) {
                operation_status = "触发" + andon_type;
                happen_date = CFuncUtilsSystem.GetNowDateTime("");
            } else {
                operation_status = "复位" + andon_type;
                reset_date = CFuncUtilsSystem.GetNowDateTime("");
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(stationId));
                queryBigData.addCriteria(Criteria.where("andon_type").is(andon_type));
                queryBigData.addCriteria(Criteria.where("happen_date").ne(""));
                queryBigData.addCriteria(Criteria.where("reset_date").is(""));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsAndonTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    andon_id = docItemBigData.getString("andon_id");
                    happen_date = docItemBigData.getString("happen_date");
                    iteratorBigData.close();
                }
                cost_times = CFuncUtilsSystem.GetDiffMsTimes(happen_date, reset_date);
            }

            //插入数据到数据库
            if (andon_id.equals("")) {
                Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                List<Map<String, Object>> lstAndonDocuments = new ArrayList<>();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                andon_id = CFuncUtilsSystem.CreateUUID(true);
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("andon_id", andon_id);
                mapBigDataRow.put("station_id", stationId);
                mapBigDataRow.put("andon_type", andon_type);
                mapBigDataRow.put("happen_date", happen_date);
                mapBigDataRow.put("reset_date", reset_date);
                mapBigDataRow.put("happen_user", current_user);
                mapBigDataRow.put("reset_user", "");
                mapBigDataRow.put("cost_times", cost_times);
                lstAndonDocuments.add(mapBigDataRow);
                mongoTemplate.insert(lstAndonDocuments, apsAndonTable);
            }
            //修改数据
            else {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("andon_id").is(andon_id));
                Update updateBigData = new Update();
                updateBigData.set("reset_date", reset_date);
                updateBigData.set("reset_user", current_user);
                updateBigData.set("cost_times", cost_times);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsAndonTable);
            }
            EapProjectSfjdOpeLogIns("上报ANDON", "EQP->EAP", jsonParas.toJSONString(), operation_status);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "插入成功", 0);
        } catch (Exception ex) {
            errorMsg = "保存andon数据到数据库异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.保存操作记录
    public String EapProjectSfjdOpeLogIns(String operation_code, String operation_type,
                                          String operation_des, String operation_status) throws Exception {
        String apiRoutePath = "eap/project/sfjd/EapProjectSfjdOpeLogIns";
        String transResult = "";
        String errorMsg = "";
        String apsOperationTable = "a_eap_operation_log";
        try {
            //插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstRecipeDocuments = new ArrayList<>();
            Map<String, Object> mapBigDataRow = new HashMap<>();
            String operation_id = CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("operation_id", operation_id);
            mapBigDataRow.put("operation_code", operation_code);
            mapBigDataRow.put("operation_type", operation_type);
            mapBigDataRow.put("operation_des", operation_des);
            mapBigDataRow.put("operation_status", operation_status);

            lstRecipeDocuments.add(mapBigDataRow);
            mongoTemplate.insert(lstRecipeDocuments, apsOperationTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "保存操作记录成功", 0);
        } catch (Exception ex) {
            errorMsg = "保存操作记录到数据库异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.修改rcs_event_timer的cycle_time
    @RequestMapping(value = "/EapProjectSfjdUpdateCycleTime", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapProjectSfjdUpdateCycleTime(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/sfjd/EapProjectSfjdUpdateCycleTime";
        String transResult = "";
        String errorMsg = "";
        try {
            String eventId = jsonParas.getString("eventId");
            String cycleTime = jsonParas.getString("cycleTime");
            String updateSql = "update rcs_event_timer set " +
                    "last_updated_by='EAP'," +
                    "last_update_date='" + CFuncUtilsSystem.GetNowDateTime("") + "'," +
                    "cycle_time=" + cycleTime +
                    " where event_timer_main_code='" + eventId + "'";
            int updateResult = cFuncDbSqlExecute.ExecUpdateSql("EAP", updateSql, true, request, apiRoutePath);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "修改成功", 0);
        } catch (Exception ex) {
            errorMsg = "保存andon数据到数据库异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
