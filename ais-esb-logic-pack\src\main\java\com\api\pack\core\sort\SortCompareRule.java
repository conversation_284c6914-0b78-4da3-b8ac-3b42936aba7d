package com.api.pack.core.sort;

import com.api.pack.core.board.BoardConst;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SortCompareRule
{
    protected String name;
    protected Object sourceValue;
    protected Object targetValue;

    public SortCompareRule(String name, String target)
    {
        this(name, null, target);
    }

    public SortCompareRule(String name)
    {
        this(name, null, null);
    }

    public SortCompareResult process(String dataName, String errMessage, boolean isEquals)
    {
        String err = String.format("%s未检测到%s信息或字符%s信息或二维码%s信息", dataName, this.name, this.name, this.name);
        if (ObjectUtils.isEmpty(this.sourceValue) || BoardConst.VALUE_NULL.equals(this.sourceValue))
        {
            return new SortCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
        }
        boolean skip = this.sourceValue.equals(BoardConst.VALUE_NC);
        if (this.sourceValue instanceof String && !(this.targetValue instanceof String))
        {
            this.targetValue = String.valueOf(this.targetValue);
        }
        //TODO 临时解决 2025/01/07
        if (this.name.contains("周期") && this.targetValue != null)
        {
            int srcLen = ((String) this.sourceValue).length();
            int tarLen = ((String) this.targetValue).length();
            if (srcLen > tarLen)
            {
                this.targetValue = String.format("%0" + srcLen + "d", Integer.parseInt((String) this.targetValue));
            }
        }
        if (!skip && !this.sourceValue.equals(this.targetValue) && !isEquals)
        {
            if (ObjectUtils.isEmpty(this.targetValue))
            {
                return new SortCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            err = !ObjectUtils.isEmpty(errMessage) ? errMessage
                                                   : String.format("%s字符%s{%s}信息不等于设定%s{%s}信息", dataName, this.name, this.sourceValue, this.name, this.targetValue);
            return new SortCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
        }
        else if (!skip && this.sourceValue.equals(this.targetValue) && isEquals)
        {
            err = !ObjectUtils.isEmpty(errMessage) ? errMessage
                                                   : String.format("%s检测到%s{%s}信息", dataName, this.name, this.sourceValue);
            return new SortCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
        }
        return new SortCompareResult(BoardConst.STATUS_OK, BoardConst.CODE_OK, BoardConst.BLANK);
    }

    public SortCompareResult process(String dataName, String errMessage)
    {
        return this.process(dataName, errMessage, false);
    }

    public SortCompareResult process(String dataName, boolean isEquals)
    {
        return this.process(dataName, null, isEquals);
    }

    public SortCompareResult process(String dataName)
    {
        return this.process(dataName, null, false);
    }
}