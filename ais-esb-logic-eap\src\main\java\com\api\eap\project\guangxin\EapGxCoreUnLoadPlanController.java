package com.api.eap.project.guangxin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.OpStaticElements;
import com.api.eap.core.share.PlanCommonFunc;
import com.api.eap.project.guangxin.event.BatchesFinishedEvent;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 收板机生产计划处理逻辑
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/gx/unload")
public class EapGxCoreUnLoadPlanController implements ApplicationEventPublisherAware {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    private ApplicationEventPublisher eventPublisher;

    //载具转换
    //放板机完工同步到收板机,一个放板机任务生成两个收板机任务时，判断实际投板数量，如果<=24，取消第二个收板任务，修改第一个收板任务数量=实际放板数量
    //如果>24，第一个收板任务数量=实际放板数量不动，修改第二个收板任务，修改数量=实际放板数量-24
    //不考虑一车多批情况
    @RequestMapping(value = "/EapCoreUnLoadPlanLoadFinish", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanLoadFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/gx/unload/EapCoreUnLoadPlanLoadFinish";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String station_code = jsonParas.getString("station_code");//收板机工位
            String sqlStation = "select station_id " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            JSONArray load_panel_list = jsonParas.getJSONArray("load_panel_list");
            JSONArray load_lot_list = jsonParas.getJSONArray("load_lot_list");
            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            if (load_lot_list != null && load_lot_list.size() > 0) {
                JSONObject jbItem = load_lot_list.getJSONObject(0);
                String group_lot_num = jbItem.getString("group_lot_num");
                String lot_num = jbItem.getString("lot_num");
                Integer fp_count = jbItem.getInteger("fp_count");
                Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                //1.如果实际投入板件数量<=0,直接取消任务，如果任务已开始，写入取消任务点位
                if (finish_ok_count <= 0) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    query.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                    query.addCriteria(Criteria.where("group_lot_num").regex(group_lot_num + ".*"));
                    query.addCriteria(Criteria.where("lot_num").is(lot_num));
                    query.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    query.with(Sort.by(Sort.Direction.ASC, "_id"));
                    List<Document> documentList = mongoTemplate.find(query, Document.class, apsPlanTable);
                    if (documentList != null && documentList.size() > 0) {
                        for (Document document : documentList) {
                            String plan_id = document.getString("plan_id");
                            String group_lot_status_now = document.getString("group_lot_status");
                            errorMsg = this.CancelTask(plan_id, group_lot_status_now, station_code);
                            if (!errorMsg.equals("")) {
                                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                return transResult;
                            }
                        }
                    }
                }
                //2.如果实际投入板件数量<=24,需要先判断收板任务数量，如果是两个任务，取消一个（优先处处理未开始的任务），修改另一个，并把实际投入板件条码绑定到这个任务上
                else if (finish_ok_count <= 24) {
                    //2.1 如果有两个任务，先取消一个
                    Query query = new Query();
                    List<String> plan_id_cancle_list = new ArrayList<>();
                    query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    query.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                    query.addCriteria(Criteria.where("group_lot_num").regex(group_lot_num + ".*"));
                    query.addCriteria(Criteria.where("lot_num").is(lot_num));
                    //query.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    query.with(Sort.by(Sort.Direction.ASC, "_id"));
                    List<Document> documentList = mongoTemplate.find(query, Document.class, apsPlanTable);
                    if (documentList != null && documentList.size() == 2) {
                        int index = 1;//需要取消的任务
                        for (int i = 1; i >= 0; i--) {
                            Document doc = documentList.get(i);
                            String status = doc.getString("group_lot_status");
                            if (status.equals("WAIT") || status.equals("PLAN")) {
                                index = i;
                                break;
                            }
                        }
                        Document document = documentList.get(index);
                        String plan_id = document.getString("plan_id");
                        plan_id_cancle_list.add(plan_id);
                        String group_lot_status_now = document.getString("group_lot_status");
                        errorMsg = this.CancelTask(plan_id, group_lot_status_now, station_code);
                        if (!errorMsg.equals("")) {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                    }
                    //2.2 修改剩下的任务
                    Query query2 = new Query();
                    query2.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    query2.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                    query2.addCriteria(Criteria.where("group_lot_num").regex(group_lot_num + ".*"));
                    query2.addCriteria(Criteria.where("lot_num").is(lot_num));
                    query2.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    query.with(Sort.by(Sort.Direction.ASC, "_id"));
                    List<Document> documentList2 = mongoTemplate.find(query2, Document.class, apsPlanTable);
                    if (documentList2 != null && documentList2.size() > 0) {
                        for (Document document : documentList) {
                            String plan_id = document.getString("plan_id");
                            String group_lot_status_now = document.getString("group_lot_status");
                            String port_code = document.getString("port_code");
                            errorMsg = this.UpdateTask(plan_id, group_lot_status_now, station_code, station_id, port_code, finish_ok_count, plan_id_cancle_list, "Y", request);
                            if (!errorMsg.equals("")) {
                                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                return transResult;
                            }
                        }
                    }
                }
                //3.如果实际投入板件数量>24,直接处理其中一个任务（优先处处理未开始的任务），另一个不做任何处理
                else {
                    //3.1 如果有两个任务，如果一个执行中，一个未开始，直接处理未开始的任务，否者直接处理其中一个
                    Query query = new Query();
                    query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    query.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                    query.addCriteria(Criteria.where("group_lot_num").regex(group_lot_num + ".*"));
                    query.addCriteria(Criteria.where("lot_num").is(lot_num));
                    //query.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    query.with(Sort.by(Sort.Direction.ASC, "_id"));
                    List<Document> documentList = mongoTemplate.find(query, Document.class, apsPlanTable);
                    if (documentList != null && documentList.size() == 2) {
                        int index = 1;//需要修改的任务
                        for (int i = 1; i >= 0; i--) {
                            Document doc = documentList.get(i);
                            String status = doc.getString("group_lot_status");
                            if (status.equals("WAIT") || status.equals("PLAN")) {
                                index = i;
                                break;
                            }
                        }
                        Document document = documentList.get(index);
                        String plan_id = document.getString("plan_id");
                        String group_lot_status_now = document.getString("group_lot_status");
                        String port_code = document.getString("port_code");
                        errorMsg = this.UpdateTask(plan_id, group_lot_status_now, station_code, station_id, port_code, finish_ok_count - 24, null, "Y", request);
                        if (!errorMsg.equals("")) {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        Document document2 = documentList.get(1 - index);
                        String plan_id2 = document2.getString("plan_id");
                        String group_lot_status_now2 = document2.getString("group_lot_status");
                        String port_code2 = document2.getString("port_code");
                        errorMsg = this.UpdateTask(plan_id2, group_lot_status_now2, station_code, station_id, port_code2, 24, null, "Y", request);
                        if (!errorMsg.equals("")) {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                    }
                }
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "放板机完工同步到收板机异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //放板机完工同步到收板机,一体机收板机任务属性attribute1='Y'，通过传入integratedFlag（一体机标识）判断
    @RequestMapping(value = "/EapCoreUnLoadPlanLoadFinish02", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanLoadFinish02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/gx/unload/EapCoreUnLoadPlanLoadFinish02";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            //如果系统参数配置了UnLoadFpCount，就调用新的方法
            String UnLoadFpCount = cFuncDbSqlResolve.GetParameterValue("UnLoadFpCount");//收板机分盘数量
            if (UnLoadFpCount != null && !UnLoadFpCount.equals("")) {
                transResult = EapCoreUnLoadPlanLoadFinish03(jsonParas, request);
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                return transResult;
            }
            String station_code = jsonParas.getString("station_code");//收板机工位
            String sqlStation = "select station_id " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            JSONArray load_panel_list = jsonParas.getJSONArray("load_panel_list");
            JSONArray load_lot_list = jsonParas.getJSONArray("load_lot_list");
            String integratedFlag = jsonParas.getString("integratedFlag");
            if (integratedFlag == null) {
                integratedFlag = "N";
            }
            String target_group_lot_num = "";
            Integer target_plan_count = 0;
            String group_lot_status_now = "";
            String port_code = "";

            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            if (load_lot_list != null && load_lot_list.size() > 0) {
                for (int i = 0; i < load_lot_list.size(); i++) {
                    JSONObject jbItem = load_lot_list.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    String lot_num = jbItem.getString("lot_num");
                    Integer finish_count = jbItem.getInteger("finish_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    Integer finish_ng_count = jbItem.getInteger("finish_ng_count");
                    target_plan_count += finish_ok_count;

                    //先查找数据
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    if (integratedFlag.equals("Y")) {
                        queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                    }
                    queryBigData.addCriteria(Criteria.where("group_lot_num").regex(group_lot_num + ".*"));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        Document docItemBigData = iteratorBigData.next();
                        target_group_lot_num = docItemBigData.getString("group_lot_num");
                        port_code = docItemBigData.getString("port_code");
                        iteratorBigData.close();
                    }

                    //更新
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    if (integratedFlag.equals("Y")) {
                        queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                    }
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    Update updateBigData = new Update();
                    updateBigData.set("target_lot_count", finish_ok_count);
                    updateBigData.set("target_update_count", finish_ok_count);
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }
            }

            Boolean isCancel = false;
            if (target_plan_count <= 0) {
                isCancel = true;
            }

            //判断放板数量是否为0
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && isCancel) {
                //判断是否写入点位CANCEL
                if (group_lot_status_now.equals("WORK")) {
                    String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                    String tagTaskCancelRequest = "";
                    if (aisMonitorModel.equals("AIS-PC")) {
                        tagTaskCancelRequest = "LoadAis/AisStatus/TaskCancelRequest";
                    } else if (aisMonitorModel.equals("AIS-SERVER")) {
                        tagTaskCancelRequest = "LoadAis_" + station_code + "/AisStatus/TaskCancelRequest";
                    } else {
                        OpStaticElements.unLoadPlanCountUpdLock.unlock();
                        errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagTaskCancelRequest, "1", true);
                    if (!errorMsg.equals("")) {
                        OpStaticElements.unLoadPlanCountUpdLock.unlock();
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }

                //再更新
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                if (integratedFlag.equals("Y")) {
                    queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                }
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                Update updateBigData2 = new Update();
                updateBigData2.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }

            //判断是否当前任务正在进行中,若是进行中,则需要把任务数量更新到PLC
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && target_plan_count > 0) {
                if (port_code != null && !port_code.equals("")) {
                    String sqlPort = "select port_index " + "from a_eap_fmod_station_port " + "where station_id=" + station_id + " and port_code='" + port_code + "'";
                    List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPort, false, request, apiRoutePath);
                    if (lstPort != null && lstPort.size() > 0) {
                        String port_index = lstPort.get(0).get("port_index").toString();
                        String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                        String tagUpdateLotCountStatus = "";
                        String tagUpdateLotCount = "";
                        String client_code = "UnLoadPlc";
                        if (integratedFlag.equals("Y")) {
                            client_code = "LoadPlc";
                        }
                        if (aisMonitorModel.equals("AIS-PC")) {
                            tagUpdateLotCountStatus = client_code + "/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = client_code + "/AisReqControl" + port_index + "/UpdateLotCount";
                        } else if (aisMonitorModel.equals("AIS-SERVER")) {
                            tagUpdateLotCountStatus = client_code + "_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = client_code + "_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCount";
                        } else {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        String tagOnlyKeyList = tagUpdateLotCount + "," + tagUpdateLotCountStatus;
                        String tagValueList = target_plan_count + "&1";
                        errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
                        if (!errorMsg.equals("")) {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                    }
                }
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "放板机完工同步到收板机异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //载具转换
    //放板机完工同步到收板机,一个放板机任务生成两个收板机任务时，判断实际投板数量，如果<=24，取消第二个收板任务，修改第一个收板任务数量=实际放板数量
    //如果>24，第一个收板任务数量=实际放板数量不动，修改第二个收板任务，修改数量=实际放板数量-24
    //不考虑一车多批情况
    @RequestMapping(value = "/EapCoreUnLoadPlanLoadFinish03", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanLoadFinish03(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/gx/unload/EapCoreUnLoadPlanLoadFinish03";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String integratedFlag = jsonParas.getString("integratedFlag");
            if (integratedFlag == null) {
                integratedFlag = "N";
            }
            String UnLoadFpCount = cFuncDbSqlResolve.GetParameterValue("UnLoadFpCount");
            ;//收板机分盘数量
            if (UnLoadFpCount == null || UnLoadFpCount.equals("")) UnLoadFpCount = "24";
            int UnLoadFpCount_int = Integer.parseInt(UnLoadFpCount);
            String station_code = jsonParas.getString("station_code");//收板机工位
            String sqlStation = "select station_id " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            JSONArray load_panel_list = jsonParas.getJSONArray("load_panel_list");
            JSONArray load_lot_list = jsonParas.getJSONArray("load_lot_list");
            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            if (load_lot_list != null && load_lot_list.size() > 0) {
                JSONObject jbItem = load_lot_list.getJSONObject(0);
                String group_lot_num = jbItem.getString("group_lot_num");
                String lot_num = jbItem.getString("lot_num");
                Integer fp_count = jbItem.getInteger("fp_count");
                Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                Integer plan_lot_count = jbItem.getInteger("plan_lot_count");
                int sb_task_count = 1;//原来收板机任务数量
                int fp_count_last = 0;//原来收板机最后一个任务收板数量
                int sb_task_count_target = 1;//实际收板机任务数量
                int fp_count_last_target = 0;//实际收板机最后一个任务收板数量
                if (UnLoadFpCount_int > 0) {
                    //计算原来收板机任务数量
                    fp_count_last = plan_lot_count % UnLoadFpCount_int;
                    sb_task_count = fp_count_last == 0 ? plan_lot_count / UnLoadFpCount_int : plan_lot_count / UnLoadFpCount_int + 1;
                    //计算实际收板机任务数量
                    fp_count_last_target = finish_ok_count % UnLoadFpCount_int;
                    sb_task_count_target = fp_count_last_target == 0 ? finish_ok_count / UnLoadFpCount_int : finish_ok_count / UnLoadFpCount_int + 1;
                }
                //1.如果实际投入板件数量<=0,直接取消任务，如果任务已开始，写入取消任务点位
                if (finish_ok_count <= 0) {
                    Query query = new Query();
                    query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    if (integratedFlag.equals("Y")) {
                        query.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                    }
                    query.addCriteria(Criteria.where("group_lot_num").regex(group_lot_num + ".*"));
                    query.addCriteria(Criteria.where("lot_num").is(lot_num));
                    query.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    query.with(Sort.by(Sort.Direction.ASC, "_id"));
                    List<Document> documentList = mongoTemplate.find(query, Document.class, apsPlanTable);
                    documentList.sort((o1, o2) -> o1.getInteger("lot_index").compareTo(o2.getInteger("lot_index")));
                    if (documentList != null && documentList.size() > 0) {
                        for (Document document : documentList) {
                            String plan_id = document.getString("plan_id");
                            String group_lot_status_now = document.getString("group_lot_status");
                            errorMsg = this.CancelTask(plan_id, group_lot_status_now, station_code);
                            if (!errorMsg.equals("")) {
                                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                return transResult;
                            }
                        }
                    }
                }
                //2.判断原来收板机任务数量和实际收板机任务数量来处理任务
                else {
                    //2.1 取消多余的任务
                    List<String> plan_id_cancle_list = new ArrayList<>();
                    Query query = new Query();
                    query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    if (integratedFlag.equals("Y")) {
                        query.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                    }
                    query.addCriteria(Criteria.where("group_lot_num").regex(group_lot_num + ".*"));
                    query.addCriteria(Criteria.where("lot_num").is(lot_num));
                    query.with(Sort.by(Sort.Direction.ASC, "_id"));
                    List<Document> documentList = mongoTemplate.find(query, Document.class, apsPlanTable);
                    documentList.sort((o1, o2) -> o1.getInteger("lot_index").compareTo(o2.getInteger("lot_index")));
                    if (documentList != null && documentList.size() > 0) {
                        for (int i = documentList.size() - 1; i >= sb_task_count_target; i--) {
                            Document doc = documentList.get(i);
                            String plan_id = doc.getString("plan_id");
                            plan_id_cancle_list.add(plan_id);
                            String group_lot_status_now = doc.getString("group_lot_status");
                            errorMsg = this.CancelTask(plan_id, group_lot_status_now, station_code);
                            if (!errorMsg.equals("")) {
                                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                return transResult;
                            }
                        }
                    }

                    //2.2 修改剩下的任务
                    if (fp_count_last_target > 0) {
                        Query query2 = new Query();
                        query2.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                        if (integratedFlag.equals("Y")) {
                            query2.addCriteria(Criteria.where("attribute1").is("Y"));//标识收板任务
                        }
                        query2.addCriteria(Criteria.where("group_lot_num").regex(group_lot_num + ".*"));
                        query2.addCriteria(Criteria.where("lot_num").is(lot_num));
                        query2.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                        query.with(Sort.by(Sort.Direction.ASC, "_id"));
                        List<Document> documentList2 = mongoTemplate.find(query2, Document.class, apsPlanTable);
                        documentList2.sort((o1, o2) -> o1.getInteger("lot_index").compareTo(o2.getInteger("lot_index")));
                        if (documentList2 != null && documentList2.size() > 0) {
                            for (int i = 0; i < documentList2.size(); i++) {
                                Document document = documentList2.get(i);
                                String plan_id = document.getString("plan_id");
                                String group_lot_status_now = document.getString("group_lot_status");
                                String port_code = document.getString("port_code");
                                int update_count = UnLoadFpCount_int;
                                if (i == documentList2.size() - 1)
                                    update_count = fp_count_last_target;
                                errorMsg = this.UpdateTask(plan_id, group_lot_status_now, station_code, station_id, port_code, update_count, plan_id_cancle_list, integratedFlag, request);
                                if (!errorMsg.equals("")) {
                                    OpStaticElements.unLoadPlanCountUpdLock.unlock();
                                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                    return transResult;
                                }
                            }
                        }
                    }
                }
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "放板机完工同步到收板机异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    private String CancelTask(String plan_id, String group_lot_status_now, String station_code) {
        String errorMsg = "";
        String transResult = "";
        String apsPlanTable = "a_eap_aps_plan";
        if (group_lot_status_now.equals("WORK")) {
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String tagTaskCancelRequest = "";
            if (aisMonitorModel.equals("AIS-PC")) {
                tagTaskCancelRequest = "LoadAis/AisStatus/TaskCancelRequest";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                tagTaskCancelRequest = "LoadAis_" + station_code + "/AisStatus/TaskCancelRequest";
            } else {
                errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagTaskCancelRequest, "1", true);
            if (!errorMsg.equals("")) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
        }
        //取消收板机任务
        Query queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
        Update updateBigData = new Update();
        updateBigData.set("group_lot_status", "CANCEL");
        mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
        return transResult;
    }

    //一体机，修改放板机点位
    private String UpdateTask(String plan_id, String group_lot_status_now, String station_code,
                              String station_id, String port_code, Integer finish_ok_count,
                              List<String> plan_id_cancle_list, String integratedFlag, HttpServletRequest request) {
        String errorMsg = "";
        String transResult = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanTableD = "a_eap_aps_plan_d";
        try {
            if (group_lot_status_now.equals("WORK")) {
                String port_index = "";
                if (port_code != null && !port_code.equals("")) {
                    String sqlPort = "select port_index " + "from a_eap_fmod_station_port " + "where station_id=" + station_id + " and port_code='" + port_code + "'";
                    List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPort, false, request, "");
                    if (lstPort != null && lstPort.size() > 0) {
                        port_index = lstPort.get(0).get("port_index").toString();
                    }
                }
                String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagUpdateLotCountStatus = "";
                String tagUpdateLotCount = "";
                String client_code = "UnLoadPlc";
                if (integratedFlag.equals("Y")) {
                    client_code = "LoadPlc";
                }
                if (aisMonitorModel.equals("AIS-PC")) {
                    tagUpdateLotCountStatus = client_code + "/AisReqControl" + port_index + "/UpdateLotCountStatus";
                    tagUpdateLotCount = client_code + "/AisReqControl" + port_index + "/UpdateLotCount";
                } else if (aisMonitorModel.equals("AIS-SERVER")) {
                    tagUpdateLotCountStatus = client_code + "_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCountStatus";
                    tagUpdateLotCount = client_code + "_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCount";
                } else {
                    errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                String tagOnlyKeyList = tagUpdateLotCount + "," + tagUpdateLotCountStatus;
                String tagValueList = finish_ok_count + "&1";
                errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
                if (!errorMsg.equals("")) {
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }
            //更新收板机任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            Update updateBigData = new Update();
            updateBigData.set("target_lot_count", finish_ok_count);
            updateBigData.set("target_update_count", finish_ok_count);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            //将取消的任务的板件信息挂到这个任务上
            if (plan_id_cancle_list != null && plan_id_cancle_list.size() > 0) {
                Query query = new Query();
                query.addCriteria(Criteria.where("plan_id").in(plan_id_cancle_list.toArray()));
                Update update = new Update();
                update.set("plan_id", plan_id);
                mongoTemplate.updateMulti(query, update, apsPlanTableD);
                return transResult;
            }
        } catch (Exception ex) {
            errorMsg = "放板机完工同步到收板机异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更改任务完工状态以及查询完工明细 载具转换，根据lot_num找到对应的放板机任务，根据放板机任务对应的放板信息当成收板信息上报,每次收24片，上报1/2/9

    /**
     * 更改任务完工状态以及查询完工明细
     * 1、载具转换机型(一体机)，按相同批次号(lot_num)计算,收板机任务属性attribute1='Y',放板机任务拆分两个任务到收板机，收板机group_lot_num=放板机group_lot_num+_1(_2)
     * 2、完批上报类型-子批完工/总工单进行中：1 当前任务ok数量 = 工单计划数量
     * 3、完批上报类型-母批正常完工/总工单完成：2 所有子批任务ok数量总和 = 工单计划数量
     * 4、完批上报类型-母批异常完工/总工单异常：9 所有子批任务ok数量总和 != 工单计划数量 (多板或少板)
     */
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateFinishStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/gx/unload/EapCoreUnLoadPlanUpdateFinishStatus";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            int panelCount = 0;
            int plan_lot_count = 0;
            List<String> lstPlanId = new ArrayList<>();//收板机任务ID集合
            List<String> lstPlanId2 = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();
            //added by chenru 2024-05-10 广芯完板1/2/9判定
            String GxFinishStatusJudge = cFuncDbSqlResolve.GetParameterValue("GxFinishStatusJudge");
            //1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num = docItemBigData.getString("lot_num");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer fp_count = docItemBigData.getInteger("fp_count");
                Integer target_lot_count = docItemBigData.getInteger("target_lot_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                String material_code = docItemBigData.getString("material_code");
                String other_attribute = docItemBigData.getString("other_attribute");
                panelCount = target_lot_count > 0 ? target_lot_count : fp_count;
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num);
                jbItem2.put("plan_lot_count", plan_lot_count);
                jbItem2.put("target_lot_count", target_lot_count);
                jbItem2.put("fp_count", fp_count);
                jbItem2.put("finish_count", finish_count);
                jbItem2.put("finish_ok_count", finish_ok_count);
                jbItem2.put("finish_ng_count", finish_ng_count);
                jbItem2.put("material_code", material_code);
                jbItem2.put("other_attribute", other_attribute);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) {
                iteratorBigData.close();
            }
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //根据lot_num和group_lot_num查询到对应的放板任务
            if (jaLotFinish != null && jaLotFinish.size() > 0) {
                for (int i = 0; i < jaLotFinish.size(); i++) {
                    JSONObject obj = (JSONObject) jaLotFinish.get(i);
                    String lot_num2 = obj.getString("lot_num");
                    String group_lot_num2 = obj.getString("group_lot_num").replace("_1", "").replace("_2", "");
                    Query queryBigData2 = new Query();
                    String[] group_lot_status = new String[]{"PLAN", "WORK", "FINISH"};
                    queryBigData2.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData2.addCriteria(Criteria.where("port_code").is("1"));
                    queryBigData2.addCriteria(Criteria.where("lot_num").is(lot_num2));
                    queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num2));
                    queryBigData2.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    queryBigData2.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
                    MongoCursor<Document> iteratorBigData2 = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                    while (iteratorBigData2.hasNext()) {
                        Document docItemBigData = iteratorBigData2.next();
                        String plan_id = docItemBigData.getString("plan_id");
                        lstPlanId2.add(plan_id);
                    }
                }
            }
            //根据放板机任务查询panel明细，根据任务数量截取板子信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId2));
            queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("panel_status").ne("NG"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) {
                iteratorBigData.close();
            }

            String task_finish_type = "";//任务完成类型，1，载具完板，工单未结束；2，载具完板，工单正常结束；9，载具完板，工单异常结束（计划数量！=板件数量）
            List<Map<String, Object>> resultList = new ArrayList<>();
            if (group_lot_num.indexOf("_1") != -1) {
                //第一个任务
                resultList = new ArrayList<>(itemList.subList(0, panelCount));
                if (plan_lot_count <= 24) {
                    if (plan_lot_count != itemList.size()) {
                        if (GxFinishStatusJudge.equals("1")) {
                            task_finish_type = "9";
                        } else {
                            task_finish_type = "2";
                        }
                    } else {
                        task_finish_type = "2";
                    }
                } else {
                    if (itemList.size() >= 24) {
                        task_finish_type = "1";
                    } else {
                        if (GxFinishStatusJudge.equals("1")) {
                            task_finish_type = "9";
                        } else {
                            task_finish_type = "2";
                        }
                    }
                }
            } else if (group_lot_num.indexOf("_2") != -1) {
                //第二个任务
                resultList = new ArrayList<>(itemList.subList(24, itemList.size()));
                if (plan_lot_count != itemList.size()) {
                    if (GxFinishStatusJudge.equals("1")) {
                        task_finish_type = "9";
                    } else {
                        task_finish_type = "2";
                    }
                } else {
                    task_finish_type = "2";
                }
            }
            //更改完工任务状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("task_error_code", task_error_code_int);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            if (jaLotFinish != null && jaLotFinish.size() > 0) {
                for (int i = 0; i < jaLotFinish.size(); i++) {
                    ((JSONObject) jaLotFinish.get(i)).put("task_finish_type", task_finish_type);
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, resultList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 更改任务完工状态以及查询完工明细
     * 1、所有机型，一体机收板机任务属性attribute1='Y'，通过传入integratedFlag（一体机标识）判断
     * 2、完批上报类型-子批完工/总工单进行中：1 当前任务ok数量 = 工单计划数量
     * 3、完批上报类型-母批正常完工/总工单完成：2 所有子批任务ok数量总和 = 工单计划数量
     * 4、完批上报类型-母批异常完工/总工单异常：9 所有子批任务ok数量总和 != 工单计划数量 (多板或少板)
     */
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateFinishStatus04", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateFinishStatus04(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/gx/unload/EapCoreUnLoadPlanUpdateFinishStatus04";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();//收板机任务ID集合
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            String integratedFlag = jsonParas.getString("integratedFlag");
            if (integratedFlag == null) {
                integratedFlag = "N";
            }
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();
            //added by chenru 2024-05-10 广芯完板1/2/9判定
            String GxFinishStatusJudge = cFuncDbSqlResolve.GetParameterValue("GxFinishStatusJudge");
            // 1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();//收板机任务集合
            //临时记录收板机工作模式，若任务中存在工作模式，目前认为是AOI的如果是双面模式则板件履历只返回B面
            Set<String> workModePlanFlag = new HashSet<String>();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num = docItemBigData.getString("lot_num");
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer fp_count = docItemBigData.getInteger("fp_count");
                Integer target_lot_count = docItemBigData.getInteger("target_lot_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                String material_code = docItemBigData.getString("material_code");
                String other_attribute = docItemBigData.getString("other_attribute");
                String workMode = docItemBigData.getString("work_mode");
                if (!StringUtils.isEmpty(workMode) && "TB".equals(workMode)) {
                    workModePlanFlag.add(lot_num);
                }
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num);
                jbItem2.put("plan_lot_count", plan_lot_count);
                jbItem2.put("target_lot_count", target_lot_count);
                jbItem2.put("fp_count", fp_count);
                jbItem2.put("finish_count", finish_count);
                jbItem2.put("finish_ok_count", finish_ok_count);
                jbItem2.put("finish_ng_count", finish_ng_count);
                jbItem2.put("material_code", material_code);
                jbItem2.put("other_attribute", other_attribute);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) {
                iteratorBigData.close();
            }
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }

            // 获取当前收板任务的板件履历
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                if (docItemBigData.containsKey("lot_num") && workModePlanFlag.contains(docItemBigData.getString("lot_num")) && (!Integer.valueOf(2).equals(docItemBigData.getInteger("face_code")))) {
                    continue;
                }
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));

                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) {
                iteratorBigData.close();
            }
            String task_finish_type = "1";
            String[] group_lot_status = new String[]{"FINISH", "WORK"};// 任务完成类型，1、子批完工/总工单进行中；2、母批正常完工/总工单完成；9、母批异常完工/总工单异常
            List<Map<String, Object>> resultList = new ArrayList<>();
            if (lstPlanId.size() > 0) {
                Map<String, Boolean> groupIsFinishMap = new HashMap<>(); // 母批是否完成/总工单是否结束
                for (int i = 0; i < jaLotFinish.size(); i++) {
                    Map<String, Integer> groupFinishOkCountMap = new HashMap<>(); // 每个母批的子批工单ok数量总和
                    JSONObject lot = ((JSONObject) jaLotFinish.get(i));

                    String groupLotNum = lot.getString("group_lot_num");
                    String LotNum = lot.getString("lot_num");
                    // 提取放板机实际母批单号，根据母批号查询放板机任务
                    String[] groupLotNums = groupLotNum.split("_");
                    groupLotNum = groupLotNums[0];
                    int planLotCount = lot.getInteger("plan_lot_count");//放板机计划任务母批总数量
                    int currentFinishOkCount = lot.getInteger("finish_ok_count");
                    int currentFinishNgCount = lot.getInteger("finish_ng_count");
                    int currentFinishCount = currentFinishOkCount + currentFinishNgCount;
                    int fpCount = lot.getInteger("fp_count"); // 子批计划数量
                    if (groupIsFinishMap.get(groupLotNum) == null) {
                        int fpCountSum = 0;
                        Query groupIsFinishQuery = new Query();
                        groupIsFinishQuery.addCriteria(Criteria.where("group_lot_num").regex(groupLotNum + ".*"));
                        if (integratedFlag.equals("Y")) {
                            groupIsFinishQuery.addCriteria(Criteria.where("attribute1").is("Y"));
                        }
                        groupIsFinishQuery.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                        for (Document docItemData : mongoTemplate.getCollection(apsPlanTable).find(groupIsFinishQuery.getQueryObject()).noCursorTimeout(false)) {
                            int itemFinishOkCount = docItemData.getInteger("finish_ok_count");
                            int itemFpCount = docItemData.getInteger("fp_count");
                            groupFinishOkCountMap.putIfAbsent(groupLotNum, 0);
                            groupFinishOkCountMap.put(groupLotNum, groupFinishOkCountMap.get(groupLotNum) + itemFinishOkCount);
                            fpCountSum += itemFpCount;
                        }
                        // 所有子批分盘数量fp_count总和 >= 母批计划数量时，母批完成/总工单结束
                        if (fpCountSum >= planLotCount) {
                            groupIsFinishMap.put(groupLotNum, true);
                        }
                    }
                    // 1、完批上报类型-子批完工/工单进行中：1 当前工单完成ok数量+当前工单完成ng数量/子批工单数量 = 当前工单计划数量/子批计划数量
                    if (currentFinishCount == fpCount) {
                        task_finish_type = "1";
                    }
                    // 2、完批上报类型-母批正常完工/工单完成：2 所有子批任务ok数量总和 = 工单计划数量
                    if (groupIsFinishMap.get(groupLotNum) != null) {
                        if (groupFinishOkCountMap.get(groupLotNum) == planLotCount) {
                            task_finish_type = "2";
                        } else {
                            if (GxFinishStatusJudge.equals("1")) {
                                if (GxFinishStatusJudge.equals("1")) {
                                    task_finish_type = "9";
                                } else {
                                    task_finish_type = "2";
                                }
                            } else {
                                task_finish_type = "2";
                            }
                        }
                    }
                    lot.put("task_finish_type", task_finish_type);
                }
            }
            // 更改完工任务状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("task_error_code", task_error_code_int);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 更改任务完工状态以及查询完工明细 - magazine载具
     * 1、所有机型，一体机收板机任务属性attribute1='Y'，通过传入integratedFlag（一体机标识）判断
     * 2、magazine载具：
     * 2.1、批次数量小于等于24时，不管实际收板数量，只要与放板机当前批次放板数量一致时，报母批完批（值为2）；
     * 2.2、批次数量大于24时，第1批报子批完批（值为1），第2批报母批完批（值为2）。 -- 批次数量大于24时，不管实际收板数量有多少，都会分成两框去收
     * 3、foup载具：
     * 3.1、批次数量小于等于24时，不管实际收板数量，只要与放板机当前批次放板数量一致时，报母批完批（值为2）；
     * 3.2、如果出现混批，导致收板数量没达到批次总数量时报子批完批（值为1）。
     */
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateFinishStatusWithEquipment", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateFinishStatusWithEquipment(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/gx/unload/EapCoreUnLoadPlanUpdateFinishStatusWithEquipment";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();//收板机任务ID集合
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            String equipment = jsonParas.getString("equipment");
            if (equipment == null) {
                equipment = EapGxConst.EQUIPMENT_TYPES_FOUP;
            }
            String integratedFlag = jsonParas.getString("integratedFlag");
            if (integratedFlag == null) {
                integratedFlag = "N";
            }
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();
            // 1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();//收板机任务集合
            //临时记录收板机工作模式，若任务中存在工作模式，目前认为是AOI的如果是双面模式则板件履历只返回B面
            Set<String> workModePlanFlag = new HashSet<String>();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num = docItemBigData.getString("lot_num");
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer fp_count = docItemBigData.getInteger("fp_count");
                Integer target_lot_count = docItemBigData.getInteger("target_lot_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                String material_code = docItemBigData.getString("material_code");
                String other_attribute = docItemBigData.getString("other_attribute");
                String workMode = docItemBigData.getString("work_mode");
                if (!StringUtils.isEmpty(workMode) && "TB".equals(workMode)) {
                    workModePlanFlag.add(lot_num);
                }
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num);
                jbItem2.put("plan_lot_count", plan_lot_count);
                jbItem2.put("target_lot_count", target_lot_count);
                jbItem2.put("fp_count", fp_count);
                jbItem2.put("finish_count", finish_count);
                jbItem2.put("finish_ok_count", finish_ok_count);
                jbItem2.put("finish_ng_count", finish_ng_count);
                jbItem2.put("material_code", material_code);
                jbItem2.put("other_attribute", other_attribute);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) {
                iteratorBigData.close();
            }
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }

            // 获取当前收板任务的板件履历
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                if (docItemBigData.containsKey("lot_num") && workModePlanFlag.contains(docItemBigData.getString("lot_num")) && (!Integer.valueOf(2).equals(docItemBigData.getInteger("face_code")))) {
                    continue;
                }
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) {
                iteratorBigData.close();
            }
            // 更改完工任务状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("task_error_code", task_error_code_int);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            // 发送完批上报事件
            if (lstPlanId.size() > 0 && this.eventPublisher != null) {
                this.eventPublisher.publishEvent(new BatchesFinishedEvent(jaLotFinish, equipment, "Y".equals(integratedFlag)));
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            log.error(errorMsg, ex.getMessage());
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //选择端口任务为进行中
    @RequestMapping(value = "/EapCoreUnLoadPlanWorkPlanSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanWorkPlanSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/gx/unload/EapCoreUnLoadPlanWorkPlanSelect";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String integratedFlag = jsonParas.getString("integratedFlag");//一体机标识
            if (integratedFlag == null) {
                integratedFlag = "N";
            }
            String group_lot_num = "";
            long station_id_long = Long.parseLong(station_id);

            //1.查询工位信息,并且针对一车多批不做处理
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id;
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr, "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //针对一批多车不进行CANCEL任务
            Query queryBigData = new Query();
            if (!OneCarMultyLotFlag.equals("2")) {
                //先将当前端口正在进行中的任务结束掉
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                Update updateBigData2 = new Update();
                updateBigData2.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
            } else {
                //1.先获取WORK任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                if (integratedFlag.equals("Y")) {
                    queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));
                }
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_lot_num = docItemBigData.getString("group_lot_num");
                    iteratorBigData.close();
                }
                if (!group_lot_num.equals("")) {
                    OpStaticElements.unLoadPlanCountUpdLock.unlock();
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
                    return transResult;
                }
            }

            //选择PLAN任务
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            if (integratedFlag.equals("Y")) {
                queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));
            }
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                iteratorBigData.close();
            }
            if (!group_lot_num.equals("")) {
                //2024-11-25修改,若发现target数据与计划数量不匹配则需要重新写入到PLC
                Integer plan_lot_count = 0;
                Integer target_lot_count = 0;
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                if (integratedFlag.equals("Y")) {
                    queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));
                }
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(
                        queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    plan_lot_count += docItemBigData.getInteger("plan_lot_count");
                    target_lot_count += docItemBigData.getInteger("target_lot_count");
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
                if (target_lot_count > 0) {
                    String sqlPort = "select port_index " +
                            "from a_eap_fmod_station_port " +
                            "where station_id=" + station_id + " and port_code='" + port_code + "'";
                    List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPort,
                            false, request, apiRoutePath);
                    if (lstPort != null && lstPort.size() > 0) {
                        String port_index = lstPort.get(0).get("port_index").toString();
                        String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                        String tagUpdateLotCountStatus = "";
                        String tagUpdateLotCount = "";
                        if (aisMonitorModel.equals("AIS-PC")) {
                            tagUpdateLotCountStatus = "UnLoadPlc/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "UnLoadPlc/AisReqControl" + port_index + "/UpdateLotCount";
                        } else if (aisMonitorModel.equals("AIS-SERVER")) {
                            tagUpdateLotCountStatus = "UnLoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "UnLoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCount";
                        } else {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        String tagOnlyKeyList = tagUpdateLotCount + "," + tagUpdateLotCountStatus;
                        String tagValueList = target_lot_count + "&1";
                        errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
                        if (!errorMsg.equals("")) {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                    }
                }
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                updateBigData.set("port_code", port_code);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "选择端口任务为进行中异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }
}
