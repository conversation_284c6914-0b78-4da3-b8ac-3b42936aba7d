package com.api.dcs.core.wms;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 抛丸(进)调度路线创建[从抛丸工位入库]
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Service
@Slf4j
public class DcsCarMainPwInFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsCarCommonFunc dcsCarCommonFunc;
    @Autowired
    private DcsStockCommonFunc dcsStockCommonFunc;

    //创建任务与路线规划
    public Map<String, Object> CreateTaskRoute(String userID, HttpServletRequest request, String apiRoutePath,
                                               String ware_house, String car_code) throws Exception{
        String errorMsg = "";
        String task_type="PW_KW";
        Map<String, Object> mapResult=new HashMap<>();
        mapResult.put("passFlag",false);//是否允许越过此调度
        mapResult.put("errorMsg",errorMsg);//当前调度错误信息

        //1.判断天车是否存在
        Map<String, Object> mapCar= dcsCarCommonFunc.GetCarInfo(car_code);
        if(mapCar==null){
            throw new Exception("天车编码{"+car_code+"}不存在天车基础数据中");
        }

        //2.查询是否存在抛丸入库任务
        Map<String, Object> mapCarTask=dcsCarCommonFunc.GetCarPlanTask(task_type);
        if(mapCarTask==null){
            errorMsg="未找到任务类型为{"+task_type+"}的抛丸入库任务";
            mapResult.put("passFlag",true);
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String task_id=mapCarTask.get("task_id").toString();
        String task_from=mapCarTask.get("task_from").toString();
        String task_way=mapCarTask.get("task_way").toString();
        String task_num=mapCarTask.get("task_num").toString();
        String serial_num=mapCarTask.get("serial_num").toString();
        String lot_num=mapCarTask.get("lot_num").toString();
        String model_type=mapCarTask.get("model_type").toString();
        String from_stock_code=mapCarTask.get("from_stock_code").toString();
        String need_check_gd_flag=mapCarTask.get("need_check_gd_flag").toString();
        String need_check_model_flag=mapCarTask.get("need_check_model_flag").toString();
        String need_tell_start_flag=mapCarTask.get("need_tell_start_flag").toString();
        String cq_if_tags_list=mapCarTask.get("cq_if_tags_list").toString();
        String cq_if_tags_value=mapCarTask.get("cq_if_tags_value").toString();
        String fz_if_tags_list=mapCarTask.get("fz_if_tags_list").toString();
        String fz_if_tags_value=mapCarTask.get("fz_if_tags_value").toString();
        String check_gd_tag=mapCarTask.get("check_gd_tag").toString();
        String check_gd_value=mapCarTask.get("check_gd_value").toString();
        String check_model_tag=mapCarTask.get("check_model_tag").toString();

        //3.根据机型查找机型信息
        Map<String, Object> mapModel= dcsStockCommonFunc.GetModelInfo("",model_type);
        if(mapModel==null){
            errorMsg="任务类型为{"+task_type+"}的抛丸入库任务,未能根据机型{"+model_type+"}查找到机型基础信息";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String model_id=mapModel.get("model_id").toString();
        Float m_height=Float.parseFloat(mapModel.get("m_height").toString());

        //4.根据来源库位获取库位信息
        Map<String, Object> mapStockFrom= dcsStockCommonFunc.GetStockInfo(from_stock_code);
        if(mapStockFrom==null){
            errorMsg="任务类型为{"+task_type+"}的抛丸入库任务,未能根据来源库位{"+from_stock_code+"}查找到库位基础信息";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String stock_region_code=mapStockFrom.get("stock_region_code").toString();
        if(!stock_region_code.equals("PW")){
            errorMsg="任务类型为{"+task_type+"}的抛丸入库任务,来源库位{"+from_stock_code+"}库位属性不等于{PW}";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        Integer from_location_x=Integer.parseInt(mapStockFrom.get("location_x").toString());
        Integer from_location_y=Integer.parseInt(mapStockFrom.get("location_y").toString());
        Integer from_location_z=Integer.parseInt(mapStockFrom.get("location_z").toString());
        String z_math_way=mapStockFrom.get("z_math_way").toString();
        if(z_math_way==null || z_math_way.equals("")) z_math_way="PLUS";
        if(z_math_way.equals("PLUS")){
            from_location_z=from_location_z+(int)((float)m_height);
        }
        else{
            from_location_z=from_location_z-(int)((float)m_height);
        }

        //5.查找目标库位
        Map<String, Object> mapStockTo= dcsStockCommonFunc.GetKwStockPwTo(ware_house,model_id,m_height);
        if(mapStockTo==null){
            errorMsg="任务类型为{"+task_type+"}的抛丸入库任务,未能找到可以入库的库位,此时库位空间不足";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String to_stock_code=mapStockTo.get("stock_code").toString();
        Integer to_location_x=Integer.parseInt(mapStockTo.get("location_x").toString());
        Integer to_location_y=Integer.parseInt(mapStockTo.get("location_y").toString());
        Integer to_location_z=Integer.parseInt(mapStockTo.get("location_z").toString());
        Integer to_stock_count=Integer.parseInt(mapStockTo.get("stock_count").toString());

        //6.插入调度任务
        Long car_task_id= dcsCarCommonFunc.LockCarTaskIns(userID,request,apiRoutePath,"N",car_code,ware_house,
                task_id,Long.parseLong(model_id),task_num,task_from,task_way,task_type,serial_num,lot_num,
                model_type,0L,from_stock_code,to_stock_code);

        Integer para_f=0;//旋转角度(0度,180度)
        Integer para_r=1;//R任务类型(1叉取、2放置、3避让、4寻中、5抛丸)
        Integer para_a=4;//A位置代码(1上料位、2下料位、3库位、4抛丸位)
        Integer para_h=0;//半高标志位,1为半高位
        //6.1 插取路线
        Long car_route_id= dcsCarCommonFunc.LockCarRouteIns(userID,request,apiRoutePath,car_task_id,ware_house,car_code,
                "CHA_QU",1,from_location_x,from_location_y,from_location_z,
                0L,"","",need_check_gd_flag,
                need_check_model_flag,cq_if_tags_list,cq_if_tags_value,1,
                check_gd_tag,check_gd_value,check_model_tag,
                para_f,para_r,para_a,para_h);

        para_r=2;//R任务类型(1叉取、2放置、3避让、4寻中、5抛丸)
        para_a=3;//A位置代码(1上料位、2下料位、3库位、4抛丸位)
        //6.2 放置路线
        car_route_id= dcsCarCommonFunc.LockCarRouteIns(userID,request,apiRoutePath,car_task_id,ware_house,car_code,
                "FANG_ZHI",2,to_location_x,to_location_y,to_location_z,
                0L,"","","N",
                "N",fz_if_tags_list,fz_if_tags_value,to_stock_count,
                "","","",
                para_f,para_r,para_a,para_h);
        return mapResult;
    }
}
