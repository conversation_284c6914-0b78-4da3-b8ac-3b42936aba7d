package com.api.dcs.project.fjrm.api;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 主任务公共方法
 * 1.根据任务类型查询主任务信息
 * 2.根据任务总量拆分主任务信息
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
public class DcsFjrmIntefTaskCommonFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.根据任务类型查询主任务信息
    public Map<String, Object> GetIntefTaskInfoByTaskType(String ware_house,String task_type) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlIntefTask = "select task_id,task_num,task_from,task_way,task_type," +
                "COALESCE(ware_house,'') ware_house," +
                "COALESCE(from_stock_code,'') from_stock_code," +
                "COALESCE(to_stock_code,'') to_stock_code," +
                "COALESCE(lot_num,'') lot_num," +
                "COALESCE(material_code,'') material_code," +
                "COALESCE(width,0) width, " +
                "COALESCE(error_min,0) error_min, " +
                "COALESCE(error_max,0) error_max, " +
                "COALESCE(task_order,0) task_order, " +
                "task_status," +
                "COALESCE(wharf_code,'') wharf_code," +
                "COALESCE(waste_box_code,'') waste_box_code, " +
                "COALESCE(split_task_num,'') split_task_num, " +
                "COALESCE(split_width,'') split_width " +
                "from b_dcs_wms_intef_task " +
                "where lock_flag='N' " +
                "and task_status='WORK' " +
                "and task_type='" + task_type + "' " +
                "and ware_house='" + ware_house + "' " +
                "order by task_order LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListModel=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlIntefTask,
                false,null,"");
        if(itemListModel!=null && itemListModel.size()>0){
            mapItem=itemListModel.get(0);
        }
        return mapItem;
    }

    //2.根据任务总量拆分主任务信息
    public Map<String, Object> SplitIntefTask(String task_id,Float stock_width,
                                              String from_stock_code,String to_stock_code,String waste_box_code) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlIntefTask = "select task_id,task_num,task_from,task_way,task_type," +
                "COALESCE(ware_house,'') ware_house," +
                "COALESCE(from_stock_code,'') from_stock_code," +
                "COALESCE(to_stock_code,'') to_stock_code," +
                "COALESCE(lot_num,'') lot_num," +
                "COALESCE(material_code,'') material_code," +
                "COALESCE(width,0) width, " +
                "COALESCE(error_min,0) error_min, " +
                "COALESCE(error_max,0) error_max, " +
                "COALESCE(task_order,0) task_order, " +
                "task_status," +
                "COALESCE(wharf_code,'') wharf_code," +
                "COALESCE(waste_box_code,'') waste_box_code, " +
                "COALESCE(split_task_num,'') split_task_num, " +
                "COALESCE(split_width,'') split_width " +
                "from b_dcs_wms_intef_task " +
                "where task_id='" + task_id + "' ";
        List<Map<String, Object>> itemListModel=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlIntefTask,
                false,null,"");
        if(itemListModel!=null && itemListModel.size()>0){
            //生成新的任务
            long taskIdSeq = cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_intef_task_id_seq", false);
            //任务来源 DATA_SOURCES：MES-MES系统、OWN-自身维护、RGV-RGV触发
            String task_from=itemListModel.get(0).get("task_from").toString();
            //自动生成任务号
            String task_num = task_from+ CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss");
            //任务方式 TASK_WAY：(自动/半自动/手动)
            String task_way="AUTO";
            //任务类型 APS_TASK_TYPE：
            // WASTE_BOX_IN_TASK-空废料框入库
            // WASTE_BOX_OUT_TASK-空废料框出库
            // FULL_IN_TASK-满框入库
            // FULL_OUT_TASK-满框出库任务
            // MOVE_TASK-倒垛
            String task_type="SPLIT_FULL_OUT_TASK";
            String split_task_num=itemListModel.get(0).get("task_num").toString();
            //库区域
            String ware_house=itemListModel.get(0).get("ware_house").toString();
            String lot_num=itemListModel.get(0).get("lot_num").toString();//批次号
            String material_code=itemListModel.get(0).get("material_code").toString();//物料编码
            String error_min=itemListModel.get(0).get("error_min").toString();//化学成分异常最小值
            String error_max=itemListModel.get(0).get("error_max").toString();//化学成分异常最大值
            String task_order=String.valueOf(taskIdSeq);//任务排序
            //任务状态 PROD_TASK_STATUS
            // PLAN-计划
            // WORK-进行中
            // FINISH-完成
            // CANCEL-取消
            String task_status="WORK";
            //新增
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            String insertSql = "INSERT INTO b_dcs_wms_intef_task (created_by, creation_date, " +
                    "task_id, task_num, task_from, task_way, task_type, ware_house, " +
                    "from_stock_code, to_stock_code, lot_num, material_code, width, " +
                    "error_min, error_max, task_order, task_status, wharf_code, waste_box_code, split_task_num) " +
                    "VALUES ('AIS','" + nowDateTime + "','" + taskIdSeq + "','" +
                    task_num + "','" +
                    task_from+ "','" +
                    task_way + "','" +
                    task_type + "','" +
                    ware_house + "','" +
                    from_stock_code + "','" +
                    to_stock_code + "','" +
                    lot_num + "','" +
                    material_code + "'," +
                    stock_width+ "," +
                    error_min + "," +
                    error_max + "," +
                    task_order + ",'" +
                    task_status+ "','" +
                    to_stock_code+ "','" +
                    waste_box_code+ "','" +
                    split_task_num + "')";
            cFuncDbSqlExecute.ExecUpdateSql("AIS", insertSql, true, null, "");

            mapItem=itemListModel.get(0);
            mapItem.put("task_id",taskIdSeq);
            mapItem.put("task_num",task_num);
            mapItem.put("task_type",task_type);
        }
        return mapItem;
    }

    //3.更新 拆分主任务
    public void UpdIntefTask(String task_id,Float width,Float split_width,String task_status) throws Exception{
        //1.修改 任务
        String sqlTaskUpd="update b_dcs_wms_intef_task set " +
                        "width="+width+", " +
                        "split_width="+split_width+", " +
                        "task_status="+task_status+" " +
                        "where task_id="+task_id;
        cFuncDbSqlExecute.ExecUpdateSql("AIS",sqlTaskUpd,false,null,"");
    }

}
