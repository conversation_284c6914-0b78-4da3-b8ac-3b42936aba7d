package com.api.eap.core.share;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工艺制造共用对外API接口
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/share")
public class EapCoreShareMeController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;

    //查询Port口对应的工单任务
    @RequestMapping(value = "/EapCoreShareMePortTaskSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreShareMePortTaskSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreShareMePortTaskSel";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");
            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //1.查询PortIndex对应的PortCode
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + " " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListPort = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlPort,
                    false, request, apiRoutePath);
            if (itemListPort != null && itemListPort.size() > 0) {
                port_code = itemListPort.get(0).get("port_code").toString();
            }
            if (port_code.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
                return selectResult;
            }
            List<Map<String, Object>> itemList = new ArrayList<>();
            String[] group_lot_status_list = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                itemList.add(mapBigDataRow);
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "查询Port口对应的工单任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //查询最后一个板件ID
    @RequestMapping(value = "/EapCoreShareMePortLastPanelSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreShareMePortLastPanelSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreShareMePortLastPanelSel";
        String selectResult = "";
        String errorMsg = "";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");
            String panel_index = jsonParas.getString("panel_index");
            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //1.查询PortIndex对应的PortCode
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + " " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListPort = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlPort,
                    false, request, apiRoutePath);
            if (itemListPort != null && itemListPort.size() > 0) {
                port_code = itemListPort.get(0).get("port_code").toString();
            }
            if (port_code.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
                return selectResult;
            }
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "查询最后一个板件ID异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //查询Port当前工作信息详细
    @RequestMapping(value = "/EapCoreStationCurInfoSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreStationCurInfoSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreStationCurInfoSelect";
        String selectResult = "";
        String errorMsg = "";
        String userName = "-1";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            userName = jsonParas.getString("user_name");
            Long station_id = jsonParas.getLong("station_id");
            String plan_id = jsonParas.getString("plan_id");
            //2.获取单元接口ip和端口号
            String sqlCell = "select COALESCE(ser.server_host_1,'127.0.0.1') server_host_1," +
                    " COALESCE(cell.cell_webapi_port,8089) cell_webapi_port " +
                    "from sys_core_server ser join sys_core_cell cell on ser.server_id=cell.server_id " +
                    "join sys_fmod_station station on station.cell_id=cell.cell_id where station.station_id=" + station_id;
            List<Map<String, Object>> itemListCell = cFuncDbSqlExecute.ExecSelectSql(userName, sqlCell, false, request, apiRoutePath);
            if (itemListCell == null || itemListCell.size() <= 0) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
                return selectResult;
            }
            String cellIp = itemListCell.get(0).get("server_host_1").toString();
            String webapiPort = itemListCell.get(0).get("cell_webapi_port").toString();
            Map<String, Object> apsPlanInfo = null;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                apsPlanInfo = iteratorBigData.next();
                iteratorBigData.close();
            }
            List<Map<String, Object>> itemList = new ArrayList<>();
            Map<String, Object> mapBigDataRow = new HashMap<>();
            if (apsPlanInfo != null) {
                mapBigDataRow.put("lot_num",apsPlanInfo.get("lot_num"));
                mapBigDataRow.put("lot_short_num",apsPlanInfo.get("lot_short_num"));
                mapBigDataRow.put("material_code",apsPlanInfo.get("material_code"));
                mapBigDataRow.put("panel_length",apsPlanInfo.get("panel_length"));
                mapBigDataRow.put("panel_width",apsPlanInfo.get("panel_width"));
                mapBigDataRow.put("panel_tickness",apsPlanInfo.get("panel_tickness"));
                mapBigDataRow.put("inspect_count",apsPlanInfo.get("inspect_count"));
                mapBigDataRow.put("plan_lot_count",apsPlanInfo.get("plan_lot_count"));
                mapBigDataRow.put("target_lot_count",apsPlanInfo.get("target_lot_count"));
                mapBigDataRow.put("fp_count",apsPlanInfo.get("fp_count"));
                mapBigDataRow.put("inspect_finish_count",apsPlanInfo.get("inspect_finish_count"));
                mapBigDataRow.put("finish_count",apsPlanInfo.get("finish_count"));
                mapBigDataRow.put("finish_ok_count",apsPlanInfo.get("finish_ok_count"));
                mapBigDataRow.put("finish_ng_count",apsPlanInfo.get("finish_ng_count"));
                mapBigDataRow.put("port_code",apsPlanInfo.get("port_code"));
            }else{
                mapBigDataRow.put("lot_num","");
                mapBigDataRow.put("lot_short_num","");
                mapBigDataRow.put("material_code","");
                mapBigDataRow.put("panel_length","");
                mapBigDataRow.put("panel_width","");
                mapBigDataRow.put("panel_tickness","");
                mapBigDataRow.put("inspect_count","");
                mapBigDataRow.put("plan_lot_count","");
                mapBigDataRow.put("target_lot_count","");
                mapBigDataRow.put("fp_count","");
                mapBigDataRow.put("inspect_finish_count","");
                mapBigDataRow.put("finish_count","");
                mapBigDataRow.put("finish_ok_count","");
                mapBigDataRow.put("finish_ng_count","");
                mapBigDataRow.put("port_code","");
            }

            //4.获取当前流程任务步骤
            JSONArray curFlowArray = new JSONArray();
            String url = "http://" + cellIp + ':' + webapiPort + "/cell/core/flow/CoreFlowTaskListSelect02";
            JSONObject result = restTemplate.postForObject(url, jsonParas, JSONObject.class);
            int code = result.getInteger("code");
            if (code == 0) {
                curFlowArray = result.getJSONArray("data");
            }
            mapBigDataRow.put("current_flow_task", curFlowArray);
            itemList.add(mapBigDataRow);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "查询工位当前任务计划发生异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
