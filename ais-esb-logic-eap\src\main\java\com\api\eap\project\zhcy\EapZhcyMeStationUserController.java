package com.api.eap.project.zhcy;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;

import lombok.extern.slf4j.Slf4j;


/**
 * <p>
 *  志圣-珠海超毅-贴膜机台用户信息查询接口
 * 1.工位当前登录信息查询
 * 2.员工扫卡登录
 * 3.员工登出
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-16
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/zhcy")
public class EapZhcyMeStationUserController {

    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //工位当前登录信息查询
    @RequestMapping(value = "/EapMeStationLoginInfoSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapMeStationLoginInfoSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/zhcy/EapMeStationLoginInfoSelect";
        String selectResult = "";
        String errorMsg = "";
        String userName = "-1";
        String tableName = "a_eap_me_station_user";
        try {
            userName = jsonParas.getString("user_code");
            if (userName != null && !userName.equals("")) {
                //userName = CFuncUtilsRSA.DecryptByPrivateKey(userName);
            }
            String station_id = jsonParas.getString("station_id");
            Query query = new Query();
            query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            query.addCriteria(Criteria.where("checkout_flag").is("N"));
            Document loginInfo = mongoTemplate.findOne(query, Document.class, tableName);
            List<Map<String, Object>> itemList = new ArrayList<>();
            if (loginInfo != null) {
                String user_name = loginInfo.getString("user_name");
                if (user_name == null || user_name.equals("")) user_name = "---";
                String dept_id = loginInfo.getString("dept_id");
                if (dept_id == null || dept_id.equals("")) dept_id = "---";
                String shift_id = loginInfo.getString("shift_id");
                if (shift_id == null || shift_id.equals("")) shift_id = "---";
                String nick_name = loginInfo.getString("nick_name");
                if (nick_name == null || nick_name.equals("")) nick_name = "---";
                String permission = loginInfo.getString("permission");
                if (permission == null || nick_name.equals("")) permission = "---";
                Map<String, Object> map = new HashMap<>();
                map.put("user_name", user_name);
                map.put("dept_id", dept_id);
                map.put("shift_id", shift_id);
                map.put("nick_name", nick_name);
                map.put("permission", permission);
                itemList.add(map);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "工位当前登录信息查询发生异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //员工扫卡登录
    @RequestMapping(value = "/EapMeStationUserLogin", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapMeStationUserLogin(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/EapMeStationUserLogin";
        String selectResult = "";
        String errorMsg = "";
        String userName = "-1";
        String tableName = "a_eap_me_station_user";
        try {
            userName = jsonParas.getString("user_code");
            if (userName != null && !userName.equals("")) {
                //userName = CFuncUtilsRSA.DecryptByPrivateKey(userName);
            }else {
            	return "";
            }
            // 在MongoDB的a_eap_account_list表中验证用户
            Query accountQuery = new Query();
            accountQuery.addCriteria(Criteria.where("UserName").is(userName));
            Document accountDoc = mongoTemplate.findOne(accountQuery, Document.class, "a_eap_account_list");

            // 如果在a_eap_account_list中找不到用户，则返回错误
            if (accountDoc == null) {
                errorMsg = "用户" + userName + "未在系统中注册，请联系管理员";
                log.warn("员工扫卡登录失败: 用户{}未在a_eap_account_list表中注册", userName);
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            // 获取用户权限信息
            String permission = accountDoc.getString("Permission");
            //log.info("用户{}在a_eap_account_list表中验证通过，权限级别: {}", userName, permission);
            
            String station_id = jsonParas.getString("station_id");
            String user_code = jsonParas.getString("user_code");
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");

            String sqlStation = "select COALESCE(station_attr,'') station_attr,station_code " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id;
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userName, sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "未能根据工位ID{" + station_id + "}查询到工位信息";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }


            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String station_code = itemListStation.get(0).get("station_code").toString();
            String userTag = "TmjPlc/PlcCraft/OpID";
            if (!userTag.equals("")) {
                cFuncUtilsCellScada.WriteTagByStation(userName, station_code, userTag, user_code, false);
            }

            //1.将当前工位未登出的用户标识为已登出
            Query query = new Query();
            query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            query.addCriteria(Criteria.where("checkout_flag").is("N"));
            Update updateBigData = new Update();
            updateBigData.set("checkout_date", nowDateTime);
            updateBigData.set("checkout_flag", "Y");
            mongoTemplate.updateMulti(query, updateBigData, tableName);
            //3.记录登录信息，并返回登录信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate(nowDateTime);
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String check_user_id = CFuncUtilsSystem.CreateUUID(true);
            String nick_name = "";
            String dept_id = "";
            String shift_id = "";
            String checkin_date = nowDateTime;
            String checkout_flag = "N";
            String sqlUser = "select staff_id,COALESCE(staff_name,'') staff_name,COALESCE(depart_code,'') depart_code " +
                    "from sys_user " +
                    "where user_code='" + user_code + "'";
            List<Map<String, Object>> itemListUser = cFuncDbSqlExecute.ExecSelectSql(userName, sqlUser, false, request, apiRoutePath);
            if (itemListUser != null && itemListUser.size() > 0) {
                shift_id = itemListUser.get(0).get("staff_id").toString();
                nick_name = itemListUser.get(0).get("staff_name").toString();
                dept_id = itemListUser.get(0).get("depart_code").toString();
            }
            Map<String, Object> mapSave = new HashMap<>();
            mapSave.put("item_date", item_date);
            mapSave.put("item_date_val", item_date_val);
            mapSave.put("check_user_id", check_user_id);
            mapSave.put("station_id", Long.parseLong(station_id));
            mapSave.put("user_name", userName);
            mapSave.put("dept_id", dept_id);
            mapSave.put("shift_id", shift_id);
            mapSave.put("nick_name", nick_name);
            mapSave.put("checkin_date", checkin_date);
            mapSave.put("checkout_date", "");
            mapSave.put("checkout_flag", checkout_flag);
            //权限（1：员工；2：工程师；3：管理员）
            mapSave.put("permission", permission);
            mongoTemplate.insert(mapSave, tableName);
            List<Map<String, Object>> itemListUsrInfo = new ArrayList<>();
            itemListUsrInfo.add(mapSave);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListUsrInfo, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "员工扫卡登录发生异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //员工登出
    @RequestMapping(value = "/EapMeStationUserLogout", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapMeStationUserLogout(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/zhcy/EapMeStationUserLogout";
        String selectResult = "";
        String errorMsg = "";
        String userName = "-1";
        String tableName = "a_eap_me_station_user";
        try {
            userName = jsonParas.getString("user_code");
            if (userName != null && !userName.equals("")) {
                //userName = CFuncUtilsRSA.DecryptByPrivateKey(userName);
            }
            String station_id = jsonParas.getString("station_id");
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");

            String sqlStation = "select COALESCE(station_attr,'') station_attr,station_code " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id;
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userName, sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "未能根据工位ID{" + station_id + "}查询到工位信息";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String station_code = itemListStation.get(0).get("station_code").toString();
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String userTag = "TmjPlc/PlcCraft/OpID";
            if (!userTag.equals("")) {
                cFuncUtilsCellScada.WriteTagByStation(userName, station_code, userTag, "", false);
            }
            //1.将当前工位未登出的用户标识为已登出
            Query query = new Query();
            query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            query.addCriteria(Criteria.where("checkout_flag").is("N"));
            Update updateBigData = new Update();
            updateBigData.set("checkout_date", nowDateTime);
            updateBigData.set("checkout_flag", "Y");
            mongoTemplate.updateMulti(query, updateBigData, tableName);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "员工登出发生异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}