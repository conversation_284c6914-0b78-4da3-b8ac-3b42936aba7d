package com.api.eap.core.load;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 放板机生产计划处理逻辑
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/load")
public class EapCoreLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //判断是否存在放板任务
    @RequestMapping(value = "/EapCoreLoadPlanExistJudge", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanExistJudge(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanExistJudge";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    String plan_id = docItemBigData.getString("plan_id");
                    mapBigDataRow.put("group_id", docItemBigData.getString("group_id"));
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    //增加panel_list
                    String panel_list = "";
                    Integer panel_index = 0;
                    Query queryBigDataD = new Query();
                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanBTable).find(queryBigDataD.getQueryObject()).
                            sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(20).iterator();
                    while (iteratorBigDataD.hasNext()) {
                        Document docItemBigDataD = iteratorBigDataD.next();
                        String panel_barcode = docItemBigDataD.getString("panel_barcode");
                        if (panel_index == 0) panel_list = panel_barcode;
                        else panel_list += "," + panel_barcode;
                        panel_index++;
                    }
                    if (iteratorBigDataD.hasNext()) iteratorBigDataD.close();
                    mapBigDataRow.put("panel_list", panel_list);
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在放板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //判断是否存在放板任务(方法2:针对端口和多任务模式)
    @RequestMapping(value = "/EapCoreLoadPlanExistJudgeM2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanExistJudgeM2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanExistJudgeM2";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            long station_id_long = Long.parseLong(station_id);
            if (pallet_num == null) pallet_num = "";

            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();

            //增加逻辑,2024-4-26
            //判断最近一条数据是否为WAIT状态,若是WAIT状态则进行更新为PLAN状态
            String[] group_lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            long planCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
            if (planCount <= 0) {
                String wait_group_id = "";
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigDataWait = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigDataWait.hasNext()) {
                    Document docItemBigData = iteratorBigDataWait.next();
                    wait_group_id = docItemBigData.getString("group_id");
                    iteratorBigDataWait.close();
                }
                if (!wait_group_id.equals("")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("group_id").is(wait_group_id));
                    Update updateBigData = new Update();
                    updateBigData.set("group_lot_status", "PLAN");
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }

            queryBigData = new Query();
            if (OneCarMultyLotFlag.equals("2")) {
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                }
            }
            if (group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                }
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                //修改载具ID
                Update updateBigData = new Update();
                if (OneCarMultyLotFlag.equals("2")) {
                    //创建一个虚拟的载具ID,防止存在重复的载具ID导致找不到台车比对数据
                    String virtu_pallet_num = CFuncUtilsSystem.CreateUUID(true);
                    updateBigData.set("pallet_num", pallet_num);
                    updateBigData.set("virtu_pallet_num", virtu_pallet_num);
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                } else {
                    if (!pallet_num.equals("")) {
                        updateBigData.set("pallet_num", pallet_num);
                        mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                    }
                }
                //再查询
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    String plan_id = docItemBigData.getString("plan_id");
                    mapBigDataRow.put("group_id", docItemBigData.getString("group_id"));
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    //增加panel_list
                    String panel_list = "";
                    Integer panel_index = 0;
                    Query queryBigDataD = new Query();
                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanBTable).find(queryBigDataD.getQueryObject()).
                            sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(20).iterator();
                    while (iteratorBigDataD.hasNext()) {
                        Document docItemBigDataD = iteratorBigDataD.next();
                        String panel_barcode = docItemBigDataD.getString("panel_barcode");
                        if (panel_index == 0) panel_list = panel_barcode;
                        else panel_list += "," + panel_barcode;
                        panel_index++;
                    }
                    if (iteratorBigDataD.hasNext()) iteratorBigDataD.close();
                    mapBigDataRow.put("panel_list", panel_list);
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在放板任务异常:" + ex;
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //放板机Panel校验
    @RequestMapping(value = "/EapCoreLoadPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批,2为一批多车
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String inspect_flag = jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String face_code2 = jsonParas.getString("face_code");//存储A面B面,1:A,2:B

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (group_lot_num == null) group_lot_num = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (inspect_flag == null || inspect_flag.equals("")) inspect_flag = "N";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (face_code2 == null || face_code2.equals("")) face_code2 = "0";
            Integer face_code2_int = Integer.parseInt(face_code2);

            long station_id_long = Long.parseLong(station_id);

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //1.判断是否存在PLAN状态下group_lot_num,若存在则进行更新为WORK状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "WORK");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            //查询该工单下全部的plan_id
            List<String> lstPlanId = new ArrayList<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前计划中或者执行中的子任务
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String port_code = "";
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;
            String old_plan_status = "";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            if (face_code2_int > 0) face_code = face_code2_int;//若有传递值,则用传递值

            //未找到任务报错
            if (plan_id.equals("")) {
                errorMsg = "未找到生产任务,请先导入生产任务";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            //4.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                            if (pnListCount > 0) {
                                Query queryBigDataD = new Query();
                                queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                if (okCount <= 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            } else {
                                if (!lot_short_num.equals("")) {//采用简码判断
                                    if (!panel_barcode.contains(lot_short_num)) {
                                        if (ng_auto_pass_value.equals("1")) {
                                            panel_status = "NG_PASS";
                                            panel_ng_code = 4;
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        } else {
                                            panel_status = "NG";
                                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if (panel_status.equals("OK")) {
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if (panelCount > 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
            String lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            //更新首检数量
            if (inspect_flag.equals("Y")) {//首检必须要全部合格件,否则再次从0开始
                if (panel_status.equals("OK") || panel_status.equals("NG_PASS")) inspect_finish_count++;
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //放板机Panel校验 根据当前端口序号查找任务
    @RequestMapping(value = "/EapCoreLoadPlanPanelCheckAndSave02", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanPanelCheckAndSave02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanPanelCheckAndSave02";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_count = 0;//计划首检数量
            Integer inspect_finish_count = 0;//实际完成首检数量
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";

            //查询该工单下全部的plan_id
            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            Map<String, Object> planData = new HashMap<String, Object>();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明没有WORK状态下的任务，需要按时间顺序将第一条任务改成WORK状态,然后再查询
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_count = docItemBigData.getInteger("inspect_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));

            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            if (plan_id.equals("")) {//当未找到任务时直接记录
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 99;
                }
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                panel_ng_code = 99;//Dummy模式返回100代码
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            //判断是否为Strip
                            if (strip_flag.equals("Y")) {
                                if (item_info != null && !item_info.equals("")) {
                                    JSONArray jaStripList = JSONArray.parseArray(item_info);
                                    if (jaStripList != null && jaStripList.size() > 0) {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        for (int m = 0; m < jaStripList.size(); m++) {
                                            JSONObject jbStripItem = jaStripList.getJSONObject(m);
                                            String strip_barcode = jbStripItem.getString("strip_barcode");
                                            String strip_level = jbStripItem.getString("strip_level");
                                            String strip_status = jbStripItem.getString("strip_status");
                                            if (strip_barcode.equals(panel_barcode)) {
                                                if (strip_status.equals("OK")) {
                                                    panel_status = "OK";
                                                    panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            } else {
                                long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                                if (pnListCount > 0) {
                                    Query queryBigDataD = new Query();
                                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                    long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                    if (okCount <= 0) {
                                        if (ng_auto_pass_value.equals("1")) {
                                            panel_status = "NG_PASS";
                                            panel_ng_code = 4;
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        } else {
                                            panel_status = "NG";
                                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                    }
                                } else {
                                    if (!lot_short_num.equals("")) {//采用简码判断
                                        if (!panel_barcode.contains(lot_short_num)) {
                                            if (ng_auto_pass_value.equals("1")) {
                                                panel_status = "NG_PASS";
                                                panel_ng_code = 4;
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            } else {
                                                panel_status = "NG";
                                                panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            }
                                        }
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if (panel_status.equals("OK")) {
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if (panelCount > 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            if (inspect_count > 0 && inspect_finish_count < inspect_count) {
                mapBigDataRow.put("inspect_flag", "Y");
                inspect_finish_count++;
            }

            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
            String lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (inspect_count > 0 && inspect_finish_count <= inspect_count) {
                updateBigData.set("inspect_finish_count", inspect_finish_count);
            }
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "DEMO异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //放板机Panel校验 根据当前端口序号查找任务，校验AIS记录EAP主動提前通知收板機對NG板收料到NG工位信息
    @RequestMapping(value = "/EapCoreLoadPlanPanelCheckAndSave03", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanPanelCheckAndSave03(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanPanelCheckAndSave03";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_count = 0;//计划首检数量
            Integer inspect_finish_count = 0;//实际完成首检数量
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";

            //查询该工单下全部的plan_id
            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            Map<String, Object> planData = new HashMap<String, Object>();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明没有WORK状态下的任务，需要按时间顺序将第一条任务改成WORK状态,然后再查询
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_count = docItemBigData.getInteger("inspect_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));

            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            if (plan_id.equals("")) {//当未找到任务时直接记录
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 99;
                }
                if (ng_auto_pass_value.equals("1")) {
                    panel_ng_code = 4;
                } else {
                    if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                        panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                    }
                }
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                panel_ng_code = 99;//Dummy模式返回100代码
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            //判断是否为Strip
                            if (strip_flag.equals("Y")) {
                                if (item_info != null && !item_info.equals("")) {
                                    JSONArray jaStripList = JSONArray.parseArray(item_info);
                                    if (jaStripList != null && jaStripList.size() > 0) {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        for (int m = 0; m < jaStripList.size(); m++) {
                                            JSONObject jbStripItem = jaStripList.getJSONObject(m);
                                            String strip_barcode = jbStripItem.getString("strip_barcode");
                                            String strip_level = jbStripItem.getString("strip_level");
                                            String strip_status = jbStripItem.getString("strip_status");
                                            if (strip_barcode.equals(panel_barcode)) {
                                                if (strip_status.equals("OK")) {
                                                    panel_status = "OK";
                                                    panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            } else {
                                long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                                if (pnListCount > 0) {
                                    Query queryBigDataD = new Query();
                                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                    long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                    if (okCount <= 0) {
                                        if (ng_auto_pass_value.equals("1")) {
                                            panel_status = "NG_PASS";
                                            panel_ng_code = 4;
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        } else {
                                            panel_status = "NG";
                                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                    }
                                } else {
                                    if (!lot_short_num.equals("")) {//采用简码判断
                                        if (!panel_barcode.contains(lot_short_num)) {
                                            if (ng_auto_pass_value.equals("1")) {
                                                panel_status = "NG_PASS";
                                                panel_ng_code = 4;
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            } else {
                                                panel_status = "NG";
                                                panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            }
                                        }
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if (panel_status.equals("OK")) {
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if (panelCount > 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            try {
                //查询是否为AIS记录EAP主動提前通知收板機對NG板收料到NG工位信息
                Query query = new Query();
                query.addCriteria(Criteria.where("lot_num").is(lot_num));
                query.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                long ngCount = mongoTemplate.getCollection("a_eap_aps_unload_ng_panel").countDocuments(query.getQueryObject());
                if (ngCount > 0) {
                    panel_status = "NG";
                    panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            } catch (Exception ex) {
            }
            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            if (inspect_count > 0 && inspect_finish_count < inspect_count) {
                mapBigDataRow.put("inspect_flag", "Y");
                inspect_finish_count++;
            }

            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
            String lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (inspect_count > 0 && inspect_finish_count <= inspect_count) {
                updateBigData.set("inspect_finish_count", inspect_finish_count);
            }
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "DEMO异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //放板机Panel校验 根据plan_id校验
    @RequestMapping(value = "/EapCoreLoadPlanPanelCheckAndSave04", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanPanelCheckAndSave04(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanPanelCheckAndSave04";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            String group_id = jsonParas.getString("group_id");
            String plan_id = jsonParas.getString("plan_id");
            Integer panel_attr = jsonParas.getInteger("panel_attr");//0正常端口板，1是NG工位板，2是陪镀板工位板
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            Integer ng_auto_pass = jsonParas.getInteger("ng_auto_pass");//NG自动越过标识,1为启用
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String lot_status = "";
            String group_lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:其他异常
            Integer panel_index = 0;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer inspect_count = 0;//首件数量
            Integer inspect_finish_count = 0;//首件完成数量

            if (panel_attr == 2) dummy_flag = "Y";
            if (sys_model == 0) offline_flag = "Y";
            if (sys_model > 0) {
                queryBigData = new Query();
                String[] lot_status_list = new String[]{"PLAN", "WORK"};
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    mapLotInfo = iteratorBigData.next();
                    iteratorBigData.close();
                }
                if (mapLotInfo == null) panel_ng_code = 8;
                else {
                    lot_status = mapLotInfo.get("lot_status").toString();
                    group_lot_status = mapLotInfo.get("group_lot_status").toString();
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    plan_lot_count = Integer.parseInt(mapLotInfo.get("plan_lot_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    if (dummy_flag.equals("N")) {
                        //是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
                        if (eap_flag.equals("Y")) {
                            panel_ng_code = Integer.parseInt(eap_ng_code);
                            if (panel_ng_code != 0 && ng_auto_pass == 1) {
                                panel_ng_code = 4;
                            }
                        } else {
                            if (panel_model == 1) {//有Panel读码模式
                                if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                    if (ng_auto_pass == 1) {
                                        panel_ng_code = 4;
                                    } else {
                                        if (panel_barcode.equals("")) panel_ng_code = 7;
                                        else panel_ng_code = 2;
                                    }
                                } else {
                                    long okCount = 0;
                                    Query queryBigDataD = new Query();
                                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                    MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                            sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                    if (iteratorBigDataD.hasNext()) {
                                        okCount = 1l;
                                        Document docItem = iteratorBigDataD.next();
                                        iteratorBigDataD.close();
                                    }
                                    if (okCount <= 0) {
                                        panel_ng_code = 1;
                                    } else {
                                        long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                        if (flowCount > 0) {
                                            panel_ng_code = 3;
                                        }
                                    }
                                }
                            } else {//无Panel模式
                                panel_barcode = mapLotInfo.get("lot_num").toString() +
                                        String.format("%03d", Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1);
                            }
                        }
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                        if (panel_ng_code == 0 || panel_ng_code == 4) {
                            if (inspect_finish_count < inspect_count) {
                                inspect_flag = "Y";
                                inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                            }
                            finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                            panel_index = finish_ok_count;
                        } else {
                            finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                            panel_index = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                        }
                    } else {
                        panel_index = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                    }
                }
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("port_code").toString());
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("pallet_num").toString());
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("pallet_type").toString());
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : panel_ng_code == 4 ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("straight_flag", "N");

            String first_flag = "N";//是否为第一片Dummy或者第一片板件
            String aysn_lot_unLoad = "N";//是否同步数据到收扳机
            JSONObject jbResult = new JSONObject();
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                Update updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                if ((panel_ng_code == 0 || panel_ng_code == 4) && finish_ok_count == 1) {
                    aysn_lot_unLoad = "Y";
                    if (lot_status.equals("PLAN")) {
                        first_flag = "Y";
                        updateBigData.set("lot_status", "WORK");
                    }
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                //载具任务组状态
                if ((panel_ng_code == 0 || panel_ng_code == 4) && finish_ok_count == 1 && group_lot_status.equals("PLAN")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                    updateBigData = new Update();
                    updateBigData.set("group_lot_status", "WORK");
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }
            //Dummy模式
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("Y")) {
                if ((panel_ng_code == 0 || panel_ng_code == 4) && lot_status.equals("PLAN")) {
                    first_flag = "Y";
                    Update updateBigData = new Update();
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                    updateBigData.set("lot_status", "WORK");
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                    //载具任务组状态
                    if (group_lot_status.equals("PLAN")) {
                        queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                        queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                        updateBigData = new Update();
                        updateBigData.set("group_lot_status", "WORK");
                        mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                    }
                }
            }

            //组合返回数据
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            jbResult.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("pallet_num").toString());
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("port_code").toString());
            jbResult.put("offline_flag", offline_flag);
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_status", panel_ng_code == 0 ? "OK" : panel_ng_code == 4 ? "NG_PASS" : "NG");
            jbResult.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(4));
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("aysn_lot_unLoad", aysn_lot_unLoad);
            jbResult.put("first_flag", first_flag);
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("plan_lot_count", plan_lot_count);
            jbResult.put("inspect_count", inspect_count);
            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //移走一片板子
    @RequestMapping(value = "/EapCoreLoadPlanRemoveOne", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanRemoveOne(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanRemoveOne";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String plan_id = "";
            Integer finish_count = -1;
            Integer finish_ng_count = -1;
            String station_flow_id = jsonParas.getString("station_flow_id");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                if (plan_id == null || plan_id.equals("")) plan_id = "0";
                iteratorBigData.close();
            }
            if (!plan_id.equals("")) {
                //先做删除
                mongoTemplate.remove(queryBigData, meStationFlowTable);
                //先查询数据
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    finish_count = docItemBigData.getInteger("finish_count");
                    finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                    finish_count = finish_count - 1;
                    finish_ng_count = finish_ng_count - 1;
                    if (finish_count < 0) finish_count = 0;
                    if (finish_ng_count < 0) finish_ng_count = 0;
                    iteratorBigData.close();
                }
                //再做更新
                if (finish_count >= 0) {
                    Update updateBigData = new Update();
                    updateBigData.set("finish_count", finish_count);
                    updateBigData.set("finish_ng_count", finish_ng_count);
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "移走一片板子发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更新板子合格标志和板件信息
    @RequestMapping(value = "/EapCoreLoadPlanPanelReplace", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanPanelReplace(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanPanelReplace";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String plan_id = "";
            Integer finish_ng_count = -1;
            Integer finish_ok_count = -1;
            Integer inspect_finish_count = -1;
            String station_flow_id = jsonParas.getString("station_flow_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String inspect_flag = jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                if (plan_id == null || plan_id.equals("")) plan_id = "0";
                iteratorBigData.close();
            }
            if (!plan_id.equals("")) {
                //先做标识更新
                Update updateBigData = new Update();
                updateBigData.set("panel_status", "NG_PASS");
                updateBigData.set("panel_ng_code", 4);
                updateBigData.set("panel_ng_msg", planCommonFunc.getPanelNgMsg(4));
                updateBigData.set("manual_judge_code", "1");
                updateBigData.set("panel_barcode", panel_barcode);
                mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);
                //先查询数据
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                    finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                    inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                    finish_ng_count = finish_ng_count - 1;
                    finish_ok_count = finish_ok_count + 1;
                    if (inspect_flag.equals("Y")) inspect_finish_count = inspect_finish_count + 1;
                    if (finish_ng_count < 0) finish_ng_count = 0;
                    if (finish_ok_count < 0) finish_ok_count = 0;
                    if (inspect_finish_count < 0) inspect_finish_count = 0;
                    iteratorBigData.close();
                }
                //再做更新
                if (finish_ng_count >= 0) {
                    updateBigData = new Update();
                    updateBigData.set("finish_ok_count", finish_ok_count);
                    updateBigData.set("finish_ng_count", finish_ng_count);
                    updateBigData.set("inspect_finish_count", inspect_finish_count);
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "更新板子合格标志和板件信息发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //判断首检是否完成
    @RequestMapping(value = "/EapCoreLoadPlanInspectIsFinish", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanInspectIsFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanInspectIsFinish";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String finishFlag = "N";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Integer inspect_count = docItemBigData.getInteger("inspect_count");
                Integer inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                if (inspect_finish_count >= inspect_count) finishFlag = "Y";
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, finishFlag, "", 0);
        } catch (Exception ex) {
            errorMsg = "判断首检是否完成异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //判断首检是否完成
    @RequestMapping(value = "/EapCoreLoadPlanInspectIsFinish2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanInspectIsFinish2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanInspectIsFinish2";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String finishFlag = "N";
        try {
            String station_id = jsonParas.getString("station_id");
            String plan_id = jsonParas.getString("plan_id");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Integer inspect_count = docItemBigData.getInteger("inspect_count");
                Integer inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                if (inspect_finish_count >= inspect_count) finishFlag = "Y";
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, finishFlag, "", 0);
        } catch (Exception ex) {
            errorMsg = "判断首检是否完成异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //判断首检是否完成并查询任务明细
    @RequestMapping(value = "/EapCoreLoadPlanInspectIsFinishWXJD", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanInspectIsFinishWXJD(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanInspectIsFinishWXJD";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        String finishFlag = "N";
        String group_lot_num = "";
        try {
            List<String> lstPlanId = new ArrayList<>();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            //1.查询当前是否存在进行中的任务
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Integer inspect_count = docItemBigData.getInteger("inspect_count");
                Integer inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                if (inspect_finish_count >= inspect_count) finishFlag = "Y";
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer fp_count2 = docItemBigData.getInteger("fp_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                String material_code = docItemBigData.getString("material_code");
                String other_attribute = docItemBigData.getString("other_attribute");
                String work_mode = docItemBigData.getString("work_mode");
                String pallet_num = docItemBigData.getString("pallet_num");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("finishFlag", finishFlag);
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("fp_count", fp_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("other_attribute", other_attribute);
                jbItem2.put("material_code", material_code);
                jbItem2.put("work_mode", work_mode);
                jbItem2.put("pallet_num", pallet_num);
                jbItem2.put("port_code", port_code);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            //查询panel明细
            List<Map<String, Object>> itemList = new ArrayList<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "判断首检是否完成异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //复位首检数量
    @RequestMapping(value = "/EapCoreLoadPlanInspectReset", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanInspectReset(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanInspectReset";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            Update updateBigData = new Update();
            updateBigData.set("inspect_finish_count", 0);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "复位首检数量异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //复位首检数量
    @RequestMapping(value = "/EapCoreLoadPlanInspectReset2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanInspectReset2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanInspectReset2";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String plan_id = jsonParas.getString("plan_id");
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            queryBigData.addCriteria(Criteria.where("lot_status").in(group_lot_status));
            Update updateBigData = new Update();
            updateBigData.set("inspect_finish_count", 0);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "复位首检数量异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更改任务完工状态以及查询完工明细
    @RequestMapping(value = "/EapCoreLoadPlanUpdateFinishStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanUpdateFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanUpdateFinishStatus";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();
            //判断是否需要做自动登出的动作
            String EapAutoFinishLoginOut = cFuncDbSqlResolve.GetParameterValue("Eap_Auto_FinishLoginOut");
            if (EapAutoFinishLoginOut.equals("Y")) {
                //设置自动登出
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                Query query = new Query();
                query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                query.addCriteria(Criteria.where("checkout_flag").is("N"));
                Update updateBigData = new Update();
                updateBigData.set("checkout_date", nowDateTime);
                updateBigData.set("checkout_flag", "Y");
                mongoTemplate.updateMulti(query, updateBigData, "a_eap_me_station_user");
            }
            //查询是否一批多车模式
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //1.查询当前是否存在进行中的任务
            Integer plan_all_count = 0;
            Integer finish_all_count = 0;
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                String pallet_num2 = docItemBigData.getString("pallet_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                Integer fp_count2 = docItemBigData.getInteger("fp_count");
                Integer inspect_finish_count2 = docItemBigData.getInteger("inspect_finish_count");
                Integer inspect_count2 = docItemBigData.getInteger("inspect_count");
                String group_lot_status2 = docItemBigData.getString("group_lot_status");
                Integer pdb_count = docItemBigData.getInteger("pdb_count");
                Integer pdb_rule = docItemBigData.getInteger("pdb_rule");
                Integer panel_model = docItemBigData.getInteger("panel_model");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("pallet_num", pallet_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("fp_count", fp_count2);
                jbItem2.put("inspect_finish_count", inspect_finish_count2);
                jbItem2.put("inspect_count", inspect_count2);
                jbItem2.put("group_lot_status", group_lot_status2);
                jbItem2.put("pdb_count", pdb_count);
                jbItem2.put("pdb_rule", pdb_rule);
                jbItem2.put("panel_model", panel_model);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
                plan_all_count += plan_lot_count2;
                finish_all_count += finish_ok_count2;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //判断是否需要更新
            Boolean isUpdTaskStatus = true;
            if (task_error_code_int < 4 && OneCarMultyLotFlag.equals("2")) {
                if (finish_all_count < plan_all_count) isUpdTaskStatus = false;
            }
            //若是一批多车，且为强制退载具
            if (task_error_code_int >= 4 && OneCarMultyLotFlag.equals("2")) {
                mongoTemplate.dropCollection("a_eap_me_pallet_queue");
            }
            //更改完工任务状态
            if (isUpdTaskStatus) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "FINISH");
                updateBigData.set("lot_status", "FINISH");
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                updateBigData.set("task_error_code", task_error_code_int);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更改任务完工状态以及查询完工明细,返回备用属性1，2，3
    @RequestMapping(value = "/EapCoreLoadPlanUpdateFinishStatus02", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanUpdateFinishStatus02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanUpdateFinishStatus02";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();
            //判断是否需要做自动登出的动作
            String EapAutoFinishLoginOut = cFuncDbSqlResolve.GetParameterValue("Eap_Auto_FinishLoginOut");
            if (EapAutoFinishLoginOut.equals("Y")) {
                //设置自动登出
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                Query query = new Query();
                query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                query.addCriteria(Criteria.where("checkout_flag").is("N"));
                Update updateBigData = new Update();
                updateBigData.set("checkout_date", nowDateTime);
                updateBigData.set("checkout_flag", "Y");
                mongoTemplate.updateMulti(query, updateBigData, "a_eap_me_station_user");
            }
            //查询是否一批多车模式
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //1.查询当前是否存在进行中的任务
            Integer plan_all_count = 0;
            Integer finish_all_count = 0;
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                String pallet_num2 = docItemBigData.getString("pallet_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                Integer fp_count2 = docItemBigData.getInteger("fp_count");
                Integer inspect_finish_count2 = docItemBigData.getInteger("inspect_finish_count");
                Integer inspect_count2 = docItemBigData.getInteger("inspect_count");
                String group_lot_status2 = docItemBigData.getString("group_lot_status");
                Integer pdb_count = docItemBigData.getInteger("pdb_count");
                Integer pdb_rule = docItemBigData.getInteger("pdb_rule");
                String attribute1 = docItemBigData.getString("attribute1");
                String attribute2 = docItemBigData.getString("attribute2");
                String attribute3 = docItemBigData.getString("attribute3");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("pallet_num", pallet_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("fp_count", fp_count2);
                jbItem2.put("inspect_finish_count", inspect_finish_count2);
                jbItem2.put("inspect_count", inspect_count2);
                jbItem2.put("group_lot_status", group_lot_status2);
                jbItem2.put("pdb_count", pdb_count);
                jbItem2.put("pdb_rule", pdb_rule);
                jbItem2.put("attribute1", attribute1);
                jbItem2.put("attribute2", attribute2);
                jbItem2.put("attribute3", attribute3);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
                plan_all_count += plan_lot_count2;
                finish_all_count += finish_ok_count2;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //判断是否需要更新
            Boolean isUpdTaskStatus = true;
            if (task_error_code_int < 4 && OneCarMultyLotFlag.equals("2")) {
                if (finish_all_count < plan_all_count) isUpdTaskStatus = false;
            }

            //更改完工任务状态
            if (isUpdTaskStatus) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "FINISH");
                updateBigData.set("lot_status", "FINISH");
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                updateBigData.set("task_error_code", task_error_code_int);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更改单批任务完工状态以及查询完工明细
    @RequestMapping(value = "/EapCoreLoadPlanSingleLotUpdateFinishStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanSingleLotUpdateFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanSingleLotUpdateFinishStatus";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            String[] lot_status = new String[]{"WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                String pallet_num = docItemBigData.getString("pallet_num");
                Integer inspect_finish_count2 = docItemBigData.getInteger("inspect_finish_count");
                Integer inspect_count2 = docItemBigData.getInteger("inspect_count");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("pallet_num", pallet_num);
                jbItem2.put("inspect_finish_count", inspect_finish_count2);
                jbItem2.put("inspect_count", inspect_count2);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //更改完工任务状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("task_error_code", task_error_code_int);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //查询当前正在作业的工单任务
    @RequestMapping(value = "/EapCoreLoadPlanFindWorkTask", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanFindWorkTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanFindWorkTask";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {

            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            List<Map<String, Object>> itemList = new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                String pallet_num2 = docItemBigData.getString("pallet_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                Integer fp_count2 = docItemBigData.getInteger("fp_count");
                Integer inspect_finish_count2 = docItemBigData.getInteger("inspect_finish_count");
                Integer inspect_count2 = docItemBigData.getInteger("inspect_count");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("pallet_num", pallet_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("fp_count", fp_count2);
                jbItem2.put("inspect_finish_count", inspect_finish_count2);
                jbItem2.put("inspect_count", inspect_count2);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "查询当前正在作业的工单任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //取消当前正在作业的工单任务
    @RequestMapping(value = "/EapCoreLoadPlanCancelWorkTask", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanCancelWorkTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanCancelWorkTask";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "CANCEL");
            updateBigData.set("lot_status", "CANCEL");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "取消当前正在作业的工单任务异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //根据母批号查询当前正在作业的工单任务
    @RequestMapping(value = "/EapCoreLoadPlanFindWorkTaskByGroupLotNum", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanFindWorkTaskByGroupLotNum(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanFindWorkTaskByGroupLotNum";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanTableD = "a_eap_aps_plan_d";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            List<Map<String, Object>> itemList = new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String plan_id = docItemBigData.getString("plan_id");
                JSONObject jbItem = (JSONObject) JSON.toJSON(docItemBigData);
                jaLotFinish.add(jbItem);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }

            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTableD).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("plan_id", docItemBigData.get("plan_id"));
                mapBigDataRow.put("panel_barcode", docItemBigData.get("panel_barcode"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "查询当前正在作业的工单任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //绑定天盖或者载具到任务
    @RequestMapping(value = "/EapCoreLoadPlanBindPallet", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanBindPallet(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanBindPallet";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");
            String pallet_num = jsonParas.getString("pallet_num");
            String group_lot_num = jsonParas.getString("group_lot_num");
            long station_id_long = Long.parseLong(station_id);
            String plan_id = "";
            String pallet_num_new = "";

            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort == null || lstPort.size() <= 0) {
                errorMsg = "未能根据端口序号{" + port_index + "}查找到端口编号";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String port_code = lstPort.get(0).get("port_code").toString();

            //选择PLAN任务
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                String pallet_num1 = docItemBigData.getString("pallet_num");
                if (pallet_num1.equals("")) pallet_num_new = pallet_num;
                else pallet_num_new = pallet_num1 + "|" + pallet_num;
                iteratorBigData.close();
            }

            //更新
            if (!plan_id.equals("")) {
                //计划表更新
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                Update updateBigData = new Update();
                updateBigData.set("pallet_num", pallet_num_new);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "绑定天盖或者载具到任务异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
