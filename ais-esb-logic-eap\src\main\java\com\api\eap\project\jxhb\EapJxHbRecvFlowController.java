package com.api.eap.project.jxhb;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * (江西红森)投收扳机标准接受流程接口
 * 1.配方下发
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@RestController
@Slf4j
public class EapJxHbRecvFlowController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapJxHbRecvFlowFunc eapJxHbRecvFlowFunc;

    //1.载具批次信息下发
    @RequestMapping(value = "/eip_eap_api_LoadRecipe",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String eip_eap_api_LoadRecipe(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eip_eap_api_LoadRecipe";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapJxHbRecvFlowFunc.EipEapApiLoadRecipe(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }
}
