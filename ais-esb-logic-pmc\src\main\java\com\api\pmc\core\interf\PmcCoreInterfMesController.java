package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.pmc.core.flow.PmcCoreStationCrosBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 上位MES接口
 * 1.MES上位过点信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfMesController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private PmcCoreStationCrosBase pmcCoreStationCrosBase;

    //1.MES上位过点信息
    @Transactional
    @RequestMapping(value = "/PmcCoreMesReportStation", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreMesReportStation(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/interf/PmcCoreMesReportStation";
        String selectResult="";
        String errorMsg="";
        try{
            log.info("MES上位过点信息回传:"+jsonParas.toString());

            String vin=jsonParas.getString("vin");//VIN码
            String station_code=jsonParas.getString("station");//工位
            String work_center_code=jsonParas.getString("workshop");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String make_order=jsonParas.getString("order_prod");//订单号
            //过站时间
            String arrive_date=jsonParas.getString("entry_time");//到达时间
            //订单信息
            String dms="";//DMS号
            String item_project="";//行项目
            String serial_num="";//工件编号或RFID(DMS+行 不满20位前面补0)
            String pallet_num="";//托盘号
            String main_material_code="";//物料编号
            String small_model_type="";//型号
            String material_color="";//颜色
            String material_size="";//尺寸
            String shaft_proc_num="";//拧紧程序号
            String engine_num="";//发动机
            String driver_way="";//驱动形式
            String station_status="1";//工位状态(1正常、2缓存区、3空板过点、4拉入、5拉出)
            //过站状态
            String check_status="OK";//过站校验状态(OK/NG)
            String check_code="0";//过站校验代码
            String check_msg="MES过站成功";//过站校验描述
            //1、获取工位信息
            String prod_line_code="";//产线
            String station_des="";//工位描述
            String bg_proceduce_code="";//报工工序号
            String bg_proceduce_des="";//报工工序描述
            String line_section_code="";//产线分段编码(来自快速编码)
            String flow_taglist="";//触发tagOnlyKeyList(做一个json格式,对应不同的工位状态触发对应的tag点)
            String up_interf_flag="";//过站上传标识(MES,VIN打刻,油液加注,LES)
            String sqlStation="select COALESCE(b.prod_line_code,'') prod_line_code," +
                    "COALESCE(a.station_des,'') station_des," +
                    "COALESCE(a.bg_proceduce_code,'') bg_proceduce_code, " +
                    "COALESCE(a.bg_proceduce_des,'') bg_proceduce_des, " +
                    "COALESCE(a.line_section_code,'') line_section_code," +
                    "COALESCE(a.attribute2,'') flow_taglist, " +
                    "COALESCE(a.attribute5,'') up_interf_flag " +
                    "from sys_fmod_station a," +
                    "     sys_fmod_prod_line b " +
                    "where a.prod_line_id=b.prod_line_id " +
                    "and a.enable_flag='Y' " +
                    "and b.enable_flag='Y' " +
                    "and a.station_code='"+station_code+"' "+
                    "and b.work_center_code='"+work_center_code+"' "+
                    "LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlStation,false,null,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                errorMsg= "工位号{"+station_code+"},系统中不存在";
                selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            prod_line_code=itemListStation.get(0).get("prod_line_code").toString();
            station_des=itemListStation.get(0).get("station_des").toString();
            bg_proceduce_code=itemListStation.get(0).get("bg_proceduce_code").toString();
            bg_proceduce_des=itemListStation.get(0).get("bg_proceduce_des").toString();
            line_section_code=itemListStation.get(0).get("line_section_code").toString();
            flow_taglist=itemListStation.get(0).get("flow_taglist").toString();
            up_interf_flag=itemListStation.get(0).get("up_interf_flag").toString();
            //2.获取线首信息
            String sqlOnline="select COALESCE(make_order,'') make_order," +
                    "COALESCE(serial_num,'') serial_num," +
                    "COALESCE(pallet_num,'') pallet_num," +
                    "COALESCE(dms,'') dms," +
                    "COALESCE(item_project,'') item_project, " +
                    "COALESCE(vin,'') vin, " +
                    "COALESCE(small_model_type,'') small_model_type, " +
                    "COALESCE(main_material_code,'') main_material_code, " +
                    "COALESCE(material_color,'') material_color, " +
                    "COALESCE(material_size,'') material_size, " +
                    "COALESCE(shaft_proc_num,'') shaft_proc_num, " +
                    "COALESCE(engine_num,'') engine_num, " +
                    "COALESCE(driver_way,'') driver_way, " +
                    "COALESCE(publish_number,'') publish_number " +
                    "from d_pmc_me_flow_online " +
                    "where enable_flag='Y' " +
                    "and work_center_code='"+work_center_code+"' "+
                    "and make_order='"+make_order+"' "+
                    "limit 1 offset 0";
            List<Map<String,Object>> itemListOnline=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlOnline,false,null,apiRoutePath);
            if(itemListOnline!=null && itemListOnline.size()>0){
                serial_num=itemListOnline.get(0).get("serial_num").toString();
                pallet_num=itemListOnline.get(0).get("pallet_num").toString();
                dms=itemListOnline.get(0).get("dms").toString();
                item_project=itemListOnline.get(0).get("item_project").toString();
                vin=itemListOnline.get(0).get("vin").toString();
                main_material_code=itemListOnline.get(0).get("main_material_code").toString();
                small_model_type=itemListOnline.get(0).get("small_model_type").toString();
                material_color=itemListOnline.get(0).get("material_color").toString();
                material_size=itemListOnline.get(0).get("material_size").toString();
                shaft_proc_num=itemListOnline.get(0).get("shaft_proc_num").toString();
                engine_num=itemListOnline.get(0).get("engine_num").toString();
                driver_way=itemListOnline.get(0).get("driver_way").toString();
            }
            //3、生成过站
            String staff_id="MES";//操作者(默认工位号)
            String allow_way="2";//允许信息来源(1AIS流程图过站；2MES过站；3AGV过站)
            String stationFlowId=pmcCoreStationCrosBase.StationCrosIntefTask(request, apiRoutePath, staff_id, allow_way,
                    station_code, prod_line_code, work_center_code,
                    make_order, dms, item_project, serial_num, pallet_num,
                    vin, main_material_code, small_model_type, material_color,
                    material_size, shaft_proc_num, engine_num, driver_way,
                    station_status, arrive_date,
                    check_status, check_code, check_msg,
                    station_des, bg_proceduce_code, bg_proceduce_des,
                    line_section_code, up_interf_flag,flow_taglist);
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,stationFlowId,"",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "MES上位过点信息回传异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
