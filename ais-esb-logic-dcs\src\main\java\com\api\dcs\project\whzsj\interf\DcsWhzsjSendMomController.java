package com.api.dcs.project.whzsj.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * WMS发送流程数据定义接口
 * 1.下发库存同步
 * 2.移库计划报工
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/whzsj/interf/mom/send")
public class DcsWhzsjSendMomController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsWhzsjSendMomFunc dcsWhzsjSendMomFunc;

    //1.下发库存同步
    @RequestMapping(value = "/DcsWhzsjSendInventory", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsWhzsjSendInventory(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/whzsj/interf/mom/send/DcsWhzsjSendInventory";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        String apsTaskTable="b_dcs_aps_task";
        String apsTaskResTable="b_dcs_aps_task_resolve";
        try{
            String station_code=jsonParas.getString("station_code");
            String mo_id=jsonParas.getString("mo_id");

            //1.根据任务ID获取任务信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(!iteratorBigData.hasNext()){
                errorMsg="未能根据任务ID{"+mo_id+"}查找到中控任务信息,该任务是否被人为删除";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Document docItemBigData = iteratorBigData.next();
            String task_num=docItemBigData.getString("task_num");
            String model_type=docItemBigData.getString("model_type");
            Double m_length=docItemBigData.getDouble("m_length");
            Double m_width=docItemBigData.getDouble("m_width");
            Double m_height=docItemBigData.getDouble("m_height");
            iteratorBigData.close();

            //2.根据任务ID查找到喷码解析信息
            JSONArray jsonArrayM=new JSONArray();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            queryBigData.addCriteria(Criteria.where("print_top_y").gte(1));
            queryBigData.addCriteria(Criteria.where("print_top_x").gte(1));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            iteratorBigData = mongoTemplate.getCollection(apsTaskResTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(100).iterator();
            while(iteratorBigData.hasNext()){
                Document docItemBigDataD = iteratorBigData.next();
                JSONObject jbItem=new JSONObject();
                String part_barcode=docItemBigDataD.getString("part_barcode");//零件条码
                String print_text=docItemBigDataD.getString("print_text");//喷码内容
                String part_code=docItemBigDataD.getString("part_code");//零件编码
                String part_draw=docItemBigDataD.getString("part_draw");//零件图号
                String craft_path_code=docItemBigDataD.getString("craft_path_code");//工艺路线编码
                String print_center_x=docItemBigDataD.getDouble("print_top_x").toString();//喷码顶点X坐标
                String print_center_y=docItemBigDataD.getDouble("print_top_y").toString();//喷码顶点Y坐标
                Integer part_type=docItemBigDataD.getInteger("part_type");//零件类型(1代表超小件、2小件、3中件、4大件)
                Double rotation_angle=docItemBigDataD.getDouble("rotation_angle");//旋转角
                Double part_length=docItemBigDataD.getDouble("part_length");//零件长
                Double part_width=docItemBigDataD.getDouble("part_width");//零件宽
                // 徐工下料分拣中控项目使用 2024-04-09 added by jay-y
                String next_process=docItemBigDataD.getString("next_process");//零件加工工序
                String part_attr=docItemBigDataD.getString("part_attr");//零件物理配送地点

                // 徐工下料分拣中控项目使用 2024-04-11 modified by jay-y
                String newPartBarcode = String.format("%s-%s-%s", part_barcode, next_process, part_attr);
                String newPrintText = String.format("%s-%s-%s", print_text, next_process, part_attr);
                jbItem.put("part_barcode",newPartBarcode);
                jbItem.put("print_text",newPrintText);
                jbItem.put("part_code",part_code);
                jbItem.put("part_draw",part_draw);
                jbItem.put("craft_path_code",craft_path_code);
                jbItem.put("print_center_x",print_center_x);
                jbItem.put("print_center_y",print_center_y);
                jbItem.put("part_type",part_type);
                jbItem.put("rotation_angle",rotation_angle);
                jbItem.put("part_length",part_length);
                jbItem.put("part_width",part_width);
                jbItem.put("next_process", next_process);
                jbItem.put("part_attr", part_attr);
                jsonArrayM.add(jbItem);
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();

            //3.判断若喷码信息为空,则报错
            if(jsonArrayM.size()<=0){
                errorMsg="未能根据任务ID{"+mo_id+"}查找到中控解析的喷码信息,需确认套料图是否有解析数据";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            dcsWhzsjSendMomFunc.PrintSendTask(station_code,mo_id,task_num,model_type,
                    m_length,m_width,m_height,jsonArrayM);

            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "下发库存同步异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
