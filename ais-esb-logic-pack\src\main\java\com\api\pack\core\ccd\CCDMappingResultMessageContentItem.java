package com.api.pack.core.ccd;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@ApiModel(value = "CCDMappingResultMessageContentItem", description = "CCD MES发送json下的 mappingResult")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class CCDMappingResultMessageContentItem
{

    @ApiModelProperty(value = "数据本体")
    @JsonProperty("mappingResult")
    @JSONField(name = "mappingResult")
    private List<CCDMappingResultMessageContentItemDetail> results;
}
