/**
 * CodeReaderInfoWebservice_PortType.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.api.eap.project.wxjd.wcf;

public interface CodeReaderInfoWebservice_PortType extends java.rmi.Remote {
    public java.lang.String codeReaderInfo(java.lang.String message) throws java.rmi.RemoteException;
    public void bandPnlASet(java.lang.String lotId, java.lang.Object[] setList, java.util.Calendar traceDate, java.lang.String eqpId, java.lang.String pnlId, java.lang.String traceEqpCode) throws java.rmi.RemoteException;
}
