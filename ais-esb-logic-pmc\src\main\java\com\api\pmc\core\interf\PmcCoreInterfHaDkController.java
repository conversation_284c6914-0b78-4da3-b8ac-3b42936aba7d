package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 焊装打刻系统接口
 * 1.焊装打刻标识回传结果
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfHaDkController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.焊装打刻标识回传结果
    @RequestMapping(value = "/PmcCoreHaDkResultUpload", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreHaDkResultUpload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String selectResult="";
        String errorMsg="";
        String dkFillTable="d_pmc_me_station_quality_hadk";
        try{
            log.info("焊装打刻标识回传结果:"+jsonParas.toString());

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            //获取参数
            String request_uuid=jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time=jsonParas.getString("request_time");//请求时间
            String request_attr=jsonParas.getString("request_attr");//预留请求属性
            String data_from_sys=jsonParas.getString("data_from_sys");//来源系统
            //新增
            String order_prod=jsonParas.getString("order_prod");//订单号
            String dk_code=jsonParas.getString("dk_code");//打刻内容
            String nowDateTime= CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapDataItem=new HashMap<>();
            List<Map<String, Object>> lstDocuments=new ArrayList<>();
            mapDataItem.put("item_date",item_date);
            mapDataItem.put("item_date_val",item_date_val);
            mapDataItem.put("quality_trace_id",quality_trace_id);

            mapDataItem.put("order_prod",order_prod);
            mapDataItem.put("dk_type",data_from_sys);
            mapDataItem.put("dk_code",dk_code);
            mapDataItem.put("dk_time",nowDateTime);
            mapDataItem.put("up_flag","N");
            mapDataItem.put("up_code",-1);
            mapDataItem.put("up_msg","");
            lstDocuments.add(mapDataItem);
            mongoTemplate.insert(lstDocuments,dkFillTable);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "焊装打刻标识回传结果异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.焊装订单信息发送
    @RequestMapping(value = "/PmcCoreHaDkMakeOrderUpload", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreHaDkMakeOrderUpload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/interf/PmcCoreHaDkMakeOrderUpload";
        String selectResult="";
        String errorMsg="";
        String method="/aisEsbOra/pmc/core/interf/PmcCoreMesMakeOrderSel";
        String esbInterfCode="AisMakeOrderToHzdk";//AIS订单信息下发焊装Hzdk
        try{
            String work_center_code=jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String station_code=jsonParas.getString("station_code");//工位
            String prod_line_code=jsonParas.getString("prod_line_code");//产线
            String make_order=jsonParas.getString("make_order");//订单号
            //1、获取oracle 订单信息
            if(PmcCoreServer.EsbUrl==null ||
                    PmcCoreServer.EsbUrl.isEmpty()){
                pmcCoreServerInit.ServerInit();
            }
            JSONObject jsonObjectMoReq = new JSONObject();
            jsonObjectMoReq.put("workshop",work_center_code);//车间
            jsonObjectMoReq.put("order_prod",make_order);//订单编号
            JSONObject jsonObjectMoRes= cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl+method, jsonObjectMoReq);
            Integer moCode=jsonObjectMoRes.getInteger("code");
            String msg=jsonObjectMoRes.getString("msg");
            if(moCode!=0) {
                errorMsg= "订单{"+make_order+"},车间{"+work_center_code+"},获取上位MES订单异常:"+msg;
                selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            JSONArray oraArry=jsonObjectMoRes.getJSONArray("data");
            if(oraArry==null || oraArry.size()<=0) {
                errorMsg= "订单{"+make_order+"},车间{"+work_center_code+"},获取上位MES订单异常:"+msg;
                selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            JSONObject oraJsonObject = oraArry.getJSONObject(0);
            String order_prod = oraJsonObject.getString("ORDER_PROD");//生产订单
            String dms_num = oraJsonObject.getString("DMS_NUM");//DMS号
            String dms_row = oraJsonObject.getString("DMS_ROW");//DMS行项目
            String factory_code = oraJsonObject.getString("FACTORY_CODE");//工厂编号
            String factory_description = oraJsonObject.getString("FACTORY_DESCRIPTION");//工厂描述
            String material_code = oraJsonObject.getString("MATERIAL_CODE");//物料编码

            //2、调用 下发焊装Hzdk
            String interfParas=PmcCoreServer.InterfBaseInfoList.get(esbInterfCode).toString();
            //workshop=interfParas.split("&&")[0].toString();
            String interfUrl=interfParas.split("&&")[1].toString();

            //格式化接口数据
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("request_uuid",CFuncUtilsSystem.CreateUUID(true));
            jsonObjectReq.put("request_time",CFuncUtilsSystem.GetNowDateTime(""));
            jsonObjectReq.put("request_attr","");
            jsonObjectReq.put("ORDER_PROD",order_prod);
            jsonObjectReq.put("DMS_NUM",dms_num);
            jsonObjectReq.put("DMS_ROW",dms_row);
            jsonObjectReq.put("FACTORY_CODE",factory_code);
            jsonObjectReq.put("FACTORY_DESCRIPTION",factory_description);
            jsonObjectReq.put("MATERIAL_CODE",material_code);

            log.info("【PmcCoreHaDkMakeOrderUpload】下发接口传参："+jsonObjectReq.toString());
            JSONObject jsonObjectRes= cFuncUtilsRest.PostJbBackJb(interfUrl,jsonObjectReq);
            log.info("【PmcCoreHaDkMakeOrderUpload】下发接口返回值："+jsonObjectRes.toString());
            Integer backCode=jsonObjectRes.getInteger("code");
            String backMsg=jsonObjectRes.getString("msg");
            if(backCode!=0){

            }
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "焊装订单信息发送异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }


}
