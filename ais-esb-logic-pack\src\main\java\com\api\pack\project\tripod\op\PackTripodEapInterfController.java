package com.api.pack.project.tripod.op;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pack.core.board.BoardConst;
import com.api.pack.core.board.PCSBoard;
import com.api.pack.core.board.SETBoard;
import com.api.pack.core.board.SETBoardService;
import com.api.pack.core.extra.Extra;
import com.api.pack.core.extra.ExtraService;
import com.api.pack.core.pile.Pile;
import com.api.pack.core.pile.PileService;
import com.api.pack.core.plan.Plan;
import com.api.pack.core.plan.PlanService;
import com.api.pack.core.recipe.RecipeService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 包装机EAP接口
 * 1.扫描工单，EAP响应任务配方详细
 * 2.获取申请内标码的请求参数
 * 3.获取板件履历的请求参数
 * 4.获取板件流水的请求参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-5-8
 */
@RestController
@Slf4j
@RequestMapping("/pack/project/tripod/op/eap/interf")
public class PackTripodEapInterfController
{
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private ExtraService extraService;

    @Autowired
    private PlanService planService;

    @Autowired
    private PileService pileService;

    @Autowired
    private SETBoardService setBoardService;

    @Autowired
    private RecipeService recipeService;

    //1.扫描工单，EAP响应任务配方详细
    @RequestMapping(value = "/PackPlanTaskInfoSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String PackPlanTaskInfoSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/tripod/op/eap/interf/PackPlanTaskInfoSave";
        String tranResult = "";
        String errorMsg = "";
        String userName = "-1";
        String apsPlanTable = "a_pack_aps_plan";
        try
        {
            userName = "EAP";
            String recipe = jsonParas.getString("recipe");
            JSONObject recipeObj = JSON.parseObject(recipe);
            for (String key : recipeObj.keySet())
            {
                jsonParas.put(key, recipeObj.get(key));
            }
            jsonParas.remove("recipe");
            String lotNum = jsonParas.getString("lot_num"); //批號
            String plantOrderNum = jsonParas.getString("plant_order_num"); //工厂订单号
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("lot_num").is(lotNum));
            if (!ObjectUtils.isEmpty(plantOrderNum))
            {
                queryBigData.addCriteria(Criteria.where("plant_order_num").is(plantOrderNum));
            }
            long flowCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
            if (flowCount > 0)
            {
                log.warn("工单{" + lotNum + "-" + plantOrderNum + "}已存在，不允许重复创建");
                return CFuncUtilsLayUiResut.GetStandJson(true, null, "", apiRoutePath, 0);
            }
            SimpleDateFormat yyyyMMddHHmmss = new SimpleDateFormat("yyyyMMddHHmmss");
            Date date = new Date();
            jsonParas.put("item_date_val", Long.parseLong(yyyyMMddHHmmss.format(date)));
            jsonParas.put("item_date", date);
            jsonParas.put("plan_id", CFuncUtilsSystem.CreateUUID(true));
            if (jsonParas.containsKey("plan_lot_count"))
            {
                jsonParas.put("plan_lot_count", StringUtils.isEmpty(jsonParas.get("plan_lot_count")) ? 0
                                                                                                     : Integer.parseInt(jsonParas.getString("plan_lot_count")));
            }
            if (jsonParas.containsKey("unit_count"))
            {
                jsonParas.put("unit_count", StringUtils.isEmpty(jsonParas.get("unit_count")) ? 0
                                                                                             : Integer.parseInt(jsonParas.getString("unit_count")));
            }
            if (jsonParas.containsKey("cycle_period"))
            {
                jsonParas.put("cycle_period", ObjectUtils.isEmpty(jsonParas.get("cycle_period")) ? ""
                                                                                                 : jsonParas.get("cycle_period"));
            }
            if (jsonParas.containsKey("finish_ok_count"))
            {
                jsonParas.put("finish_ok_count", StringUtils.isEmpty(jsonParas.get("finish_ok_count")) ? 0
                                                                                                       : Integer.parseInt(jsonParas.getString("finish_ok_count")));
            }
            if (jsonParas.containsKey("finish_ng_count"))
            {
                jsonParas.put("finish_ng_count", StringUtils.isEmpty(jsonParas.get("finish_ng_count")) ? 0
                                                                                                       : Integer.parseInt(jsonParas.getString("finish_ng_count")));
            }
            if (jsonParas.containsKey("task_cost_time"))
            {
                jsonParas.put("task_cost_time", StringUtils.isEmpty(jsonParas.get("task_cost_time")) ? 0L
                                                                                                     : Long.parseLong(jsonParas.getString("task_cost_time")));
            }
            if (jsonParas.containsKey("m_length"))
            {
                jsonParas.put("m_length", StringUtils.isEmpty(jsonParas.get("m_length")) ? 0.0
                                                                                         : Double.parseDouble(jsonParas.getString("m_length")));
            }
            if (jsonParas.containsKey("m_width"))
            {
                jsonParas.put("m_width", StringUtils.isEmpty(jsonParas.get("m_width")) ? 0.0
                                                                                       : Double.parseDouble(jsonParas.getString("m_width")));
            }
            if (jsonParas.containsKey("m_tickness"))
            {
                jsonParas.put("m_tickness", StringUtils.isEmpty(jsonParas.get("m_tickness")) ? 0.0
                                                                                             : Double.parseDouble(jsonParas.getString("m_tickness")));
            }
            if (jsonParas.containsKey("m_weight"))
            {
                jsonParas.put("m_weight", StringUtils.isEmpty(jsonParas.get("m_weight")) ? 0.0
                                                                                         : Double.parseDouble(jsonParas.getString("m_weight")));
            }
            if (!jsonParas.containsKey("task_from"))
            {
                jsonParas.put("task_from", "EAP");
            }
            if (!jsonParas.containsKey("task_type"))
            {
                jsonParas.put("task_type", "");
            }
            if (!jsonParas.containsKey("model_type"))
            {
                jsonParas.put("model_type", "");
            }
            if (!jsonParas.containsKey("model_version"))
            {
                jsonParas.put("model_version", "");
            }
            if (!jsonParas.containsKey("plan_lot_count"))
            {
                jsonParas.put("plan_lot_count", 1000);
            }
            if (!jsonParas.containsKey("finish_ok_count"))
            {
                jsonParas.put("finish_ok_count", 0);
            }
            if (!jsonParas.containsKey("finish_ng_count"))
            {
                jsonParas.put("finish_ng_count", 0);
            }
            if (!jsonParas.containsKey("unit_count"))
            {
                jsonParas.put("unit_count", 25);
            }
            if (!jsonParas.containsKey("array_type"))
            {
                jsonParas.put("array_type", "Double");
            }
            if (!jsonParas.containsKey("bd_type"))
            {
                jsonParas.put("bd_type", "None");
            }
            if (!jsonParas.containsKey("m_length"))
            {
                jsonParas.put("m_length", 0.0d);
            }
            if (!jsonParas.containsKey("m_width"))
            {
                jsonParas.put("m_width", 0.0d);
            }
            if (!jsonParas.containsKey("m_tickness"))
            {
                jsonParas.put("m_tickness", 0.0d);
            }
            if (!jsonParas.containsKey("m_weight"))
            {
                jsonParas.put("m_weight", 0.0d);
            }
            if (!jsonParas.containsKey("plant_code"))
            {
                jsonParas.put("plant_code", "");
            }
            if (!jsonParas.containsKey("sales_order"))
            {
                jsonParas.put("sales_order", "");
            }
            if (!jsonParas.containsKey("sales_item"))
            {
                jsonParas.put("sales_item", "");
            }
            if (!jsonParas.containsKey("sales_org"))
            {
                jsonParas.put("sales_org", "");
            }
            if (!jsonParas.containsKey("sales_type"))
            {
                jsonParas.put("sales_type", "");
            }
            if (!jsonParas.containsKey("custom_pn"))
            {
                jsonParas.put("custom_pn", "");
            }
            if (!jsonParas.containsKey("custom_po"))
            {
                jsonParas.put("custom_po", "");
            }
            if (!jsonParas.containsKey("custom_code"))
            {
                jsonParas.put("custom_code", "");
            }
            if (!jsonParas.containsKey("custom_name"))
            {
                jsonParas.put("custom_name", "");
            }
            if (!jsonParas.containsKey("split_lot"))
            {
                jsonParas.put("split_lot", "");
            }
            if (!jsonParas.containsKey("split_model"))
            {
                jsonParas.put("split_model", "");
            }
            if (!jsonParas.containsKey("lot_status"))
            {
                jsonParas.put("lot_status", "PLAN");
            }
            if (!jsonParas.containsKey("task_start_time"))
            {
                jsonParas.put("task_start_time", "");
            }
            if (!jsonParas.containsKey("task_end_time"))
            {
                jsonParas.put("task_end_time", "");
            }
            if (!jsonParas.containsKey("task_cost_time"))
            {
                jsonParas.put("task_cost_time", 0L);
            }
            if (!jsonParas.containsKey("recipe_paras"))
            {
                jsonParas.put("recipe_paras", "");
            }
            if (!jsonParas.containsKey("sort_paras"))
            {
                jsonParas.put("sort_paras", "");
            }
            if (!jsonParas.containsKey("enable_flag"))
            {
                jsonParas.put("enable_flag", "Y");
            }
            if (!jsonParas.containsKey("finish_pile_count"))
            {
                jsonParas.put("finish_pile_count", 0);
            }
            if (!jsonParas.containsKey("finish_lable_count"))
            {
                jsonParas.put("finish_lable_count", 0);
            }

            // 新增字段 add by jay-y
            jsonParas.putIfAbsent("double_layer_count", 0); // 双层板数
            jsonParas.putIfAbsent("typesetting_no", 0); // 排版数
            String[] keys = new String[]{
                    "batch_no", // 批号
                    "laser_batch_no", // 镭射批号
                    "customer_mn", // 客户料号
                    "ul_code", // UL
                    "attribute1", // 预留属性1
                    "attribute2", // 预留属性2
                    "attribute3", // 预留属性3
                    "plant_order_num", // 工厂订单号
                    "qc_code", // QC编码
                    "ship_address", // 发货地址
                    "label_due_date", // 标签到期日期
                    "note", // 备注
            };
            for (String key : keys)
            {
                jsonParas.putIfAbsent(key, "");
            }
            recipeService.saveByEAPData(jsonParas, cFuncDbSqlResolve);
            mongoTemplate.insert(jsonParas, apsPlanTable);
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", apiRoutePath, 0);
        }
        catch (Exception ex)
        {
            log.error(ex.getMessage(), ex);
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(ex.getMessage());
        }
        return tranResult;
    }

    //2.获取申请内标码的请求参数
    @RequestMapping(value = "/GetApplicationParams", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetApplicationParams(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/tripod/op/eap/interf/GetApplicationParams";
        String tranResult = "";
        try
        {
            String pileBarcode = jsonParas.getString("pile_barcode"); // 包条码
            // S15F29$廠別_批號_P_料號_OK版狀態_週期_鐳射批_數量_OK版類型_打印時間_分板人員_套袋人員_是否整包_訂單號_標籤類型_單報位置_雙報位置_修金品_重工類型_重工次數
            // S15F29$5001_M2409050124_P_X012010UU4041-07_0_2436_53Z_11_0_190924_LJ1_A22_1_0014241625_I_00000000000_00000000000_1_重工OSP_1
            // 5001_M2409050124_P_X012010UU4041-07_0_2436_53Z_11_0_190924_LJ1_A22_1_0014241625_I_00000000000_00000000000_1_重工OSP_1
            QueryWrapper<Extra> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("enable_flag", "Y");
            List<Extra> extras = this.extraService.list(queryWrapper);
            if (extras == null || extras.size() == 0)
            {
                throw new Exception("额外参数未配置");
            }
            Map<String, String> extraMap = extras.stream().collect(Collectors.toMap(Extra::getCode, Extra::getValue));
            Plan plan = Plan.current(this.planService);
            if (plan == null)
            {
                throw new Exception("未找到当前计划");
            }
            List<SETBoard> boards = setBoardService.findAllByPileBarcodeAndBoardStatus(pileBarcode, BoardConst.OK);
            String okBoardStatus = "0";
            String okBoardType = "0";
            int xoutActualNumber = !boards.isEmpty() ? boards.get(0).getXoutActualNumber() : 0;
            if (xoutActualNumber > 0)
            {
                okBoardType = String.valueOf(xoutActualNumber);
                okBoardStatus = "1";
            }
            Integer boardCount = boards.size();
            List<String> params = new ArrayList<>();
            params.add(plan.getPlantCode()); // 厂别
            params.add(plan.getLotNum()); // 批号
            params.add("P"); // 固定参数
            params.add(plan.getPartNumber()); // 料号
            params.add(okBoardStatus); // OK板状态
            params.add(String.valueOf(plan.getCyclePeriod())); // 周期
            params.add(plan.getLaserBatchNumber()); // 镭射批
            params.add(String.valueOf(boardCount));
            params.add(okBoardType); // OK板类型
            params.add(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))); // 打印时间
            params.add(extraMap.getOrDefault("fb_station_code", "")); // 分板人员 配置
            params.add(extraMap.getOrDefault("td_station_code", "")); // 套袋人员 配置
            params.add(!boardCount.equals(plan.getSetBoardUnitNumber()) ? "1" : "0"); // 是否整包
            params.add(plan.getPlantOrderNum()); // 订单号
            params.add(extraMap.getOrDefault("label_type", "0")); // 标签类型 配置
//            params.add(xoutBoardsString); // 单报位置
//            params.add(xoutBoardsString); // 双报位置
            params.add(BoardConst.BLANK); // 单报位置 应需求固定空值
            params.add(BoardConst.BLANK); // 双报位置 应需求固定空值
            String tinkering = extraMap.getOrDefault("tinkering", "0");
            params.add(tinkering); // 修金品 配置
            params.add("0".equals(tinkering) ? BoardConst.BLANK
                                             : extraMap.getOrDefault("cg_type", BoardConst.BLANK)); // 重工类型 配置
            params.add("0".equals(tinkering) ? BoardConst.BLANK
                                             : extraMap.getOrDefault("cg_count", BoardConst.BLANK)); // 重工次数 配置
            String extraParams = String.join("_", params);
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, extraParams, apiRoutePath, 0);
        }
        catch (Exception ex)
        {
            log.error("GetApplicationParams error: " + ex.getMessage(), ex);
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(ex.getMessage());
        }
        return tranResult;
    }

    //3.获取板件履历的请求参数
    @RequestMapping(value = "/GetBoardInfoParams", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetBoardInfoParams(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/tripod/op/eap/interf/GetBoardInfoParams";
        String tranResult = "";
        try
        {
            String stationCode = jsonParas.getString("station_code"); // 工站代码
            String pileBarcode = jsonParas.getString("pile_barcode"); // 包条码
            String eapPileBarcode = jsonParas.getString("eap_pile_barcode"); // EAP包条码
            // S6F11$EventId$參數1$參數2$參數3$參數4$.................$參數n;
            // S6F11$RD0101$記錄發生時間$廠內料號版序$廠別-批號$人員工號$鐳刻碼$週期$單據編號$板邊序列號$Xout$客户料號$到期日$打件廠料號$M批號$客戶料號+X-out信息$濕度等級$set好板數量$set內X板量$set號$PNL號$內包標籤號碼$排版數$生產面次$TOT面二維碼信息$TOT面二維碼等級$TOT面二維碼等級判定$生產面次$BOT面二維碼信息$BOT面二維碼等級$BOT面二維碼等級判定$比對內容$正反面信息比對$批間重複比對$鐳刻碼判定$疊板數量$疊板重量$TOP掃碼信息$BOT掃碼信息;
            // S6F11$RD0101$2024-08-21 13:53:51$M046008BS0203-01$5001-M2408140112$123$F3LH$2433$001$TRID4HH33243C02$00000000$3736-10-01-A$09 15 2025 12:00AM$SubconPN$M2408140112$3736-10-01-A$MSL2$1$0$02$3C$1234567$8$TOP$TRID4HH33243C02$A$OK$BOT$TRID4HH33243C02$A$OK$OK$OK$OK$OK$1$67$OK_38$OK_35;
            // RD0101$2024-08-21 13:53:51$M046008BS0203-01$5001-M2408140112$123$F3LH$2433$001$TRID4HH33243C02$00000000$3736-10-01-A$09 15 2025 12:00AM$SubconPN$M2408140112$3736-10-01-A$MSL2$1$0$02$3C$1234567$8$TOP$TRID4HH33243C02$A$OK$BOT$TRID4HH33243C02$A$OK$OK$OK$OK$OK$1$67$OK_38$OK_35
            QueryWrapper<Extra> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("enable_flag", "Y");
            List<Extra> extras = this.extraService.list(queryWrapper);
            if (extras == null || extras.size() == 0)
            {
                throw new Exception("额外参数未配置");
            }
            Map<String, String> extraMap = extras.stream().collect(Collectors.toMap(Extra::getCode, Extra::getValue));
            Plan plan = Plan.current(this.planService);
            if (plan == null)
            {
                throw new Exception("未找到当前计划");
            }
            Pile pile = this.pileService.findOneByPileBarcode(pileBarcode);
            if (pile == null)
            {
                throw new Exception("未找到堆板信息");
            }
            JSONObject result = new JSONObject();
            long count = this.setBoardService.countAllByPileBarcodeAndBoardStatus(pileBarcode, BoardConst.OK, "up_flag", BoardConst.FLAG_N);
            result.put("is_last", count <= 1);
            SETBoard set = this.setBoardService.findLatestOneByPileBarcodeAndBoardStatus(pileBarcode, BoardConst.OK, "up_flag", BoardConst.FLAG_N);
            StringBuilder params = new StringBuilder();
            List<PCSBoard> pcs = set.getChildrenBoards();
            int pcsCount = pcs.size();
            String xoutBoards = pcs.stream().map(it -> it.isXout() ? "1" : "0").collect(Collectors.joining(""));
            params.append("S6F11").append("$");
            params.append("RD0101").append("$");// RD0101 紀錄類型代碼, 按健鼎提供設定
            params.append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("$");// 記錄發生時間
            params.append(plan.getPartNumber()).append("$");// 廠內料號版序
            params.append(plan.getPlantCode()).append("-").append(plan.getLotNum()).append("$");// 廠別-批號
            params.append(stationCode).append("$");// 人員工號
            params.append(plan.getLaserBatchNumber()).append("$");// 鐳刻碼
            params.append(plan.getCyclePeriod()).append("$");// 週期
            params.append(pileBarcode).append("$");// 單據編號
            params.append(set.getBoardBarcode()).append("$");// 板邊序列號
            params.append(xoutBoards).append("$");// Xout
            params.append(plan.getCustomerMn()).append("$");// 客户料號
            params.append(plan.getLabelDueDate()).append("$");// 到期日
            params.append(extraMap.getOrDefault("factory_part_num", "MSL2")).append("$");// 打件廠料號 配置
            params.append(plan.getLotNum()).append("$");// M批號
            params.append(plan.getCustomerMn()).append(set.isXout() ? "-X" : "").append("$");// 客戶料號+X-out信息
            params.append(extraMap.getOrDefault("humidity_level", "MSL2")).append("$");// 濕度等級 配置
            params.append(pcsCount - set.getXoutActualNumber()).append("$");// set好板數量
            params.append(set.getXoutActualNumber()).append("$");// set內X板量
            params.append(set.getBoardBarcode().length() >= 15 ? set.getBoardBarcode().substring(13, 15)
                                                               : "").append("$"); // set號, SET二維碼的第13碼開始取2碼
            params.append(set.getBoardBarcode().length() >= 13 ? set.getBoardBarcode().substring(11, 13)
                                                               : "").append("$"); // PNL號, SET二維碼的第11碼開始取2碼
            params.append(eapPileBarcode).append("$");// 內包標籤號碼
            params.append(plan.getLayoutNum()).append("$");// 排版數
            String front = set.getMultiAspectFront();
            if (!ObjectUtils.isEmpty(front))
            {
                SETBoard frontSet = new SETBoard(BoardConst.ORIENT_FRONT, JSON.parseObject(front));
                params.append("Top面").append("$");// 生產面次
                params.append(frontSet.getBoardBarcode()).append("$");// TOT面二維碼信息
                params.append(frontSet.getBoardLevel()).append("$");// TOT面二維碼等級
                params.append(BoardConst.OK).append("$");// TOT面二維碼等級判定 A/B/C級判定OK, D/E/F判定NG 走到这一步时已经都通过线扫的分选比对校验了，所以默认为都是OK
            }
            String back = set.getMultiAspectBack();
            if (!ObjectUtils.isEmpty(back))
            {
                SETBoard backSet = new SETBoard(BoardConst.ORIENT_BACK, JSON.parseObject(back));
                params.append("Bot面").append("$");// 生產面次
                params.append(backSet.getBoardBarcode()).append("$");// BOT面二維碼信息
                params.append(backSet.getBoardLevel()).append("$");// BOT面二維碼等級
                params.append(BoardConst.OK).append("$");// BOT面二維碼等級判定 A/B/C級判定OK, D/E/F判定NG 走到这一步时已经都通过线扫的分选比对校验了，所以默认为都是OK
            }
            // 走到这一步时已经都通过线扫的分选比对校验了，所以默认为都是OK
            params.append(BoardConst.OK);// 比對內容
            params.append("$").append(BoardConst.OK);// 正反面信息比對
            params.append("$").append(BoardConst.OK);// 批間重複比對
            params.append("$").append(BoardConst.OK);// 鐳刻碼判定
            params.append("$").append(pile.getArrayCount());// 疊板數量
            params.append("$").append(pile.getPileWeight());// 疊板重量
            params.append("$").append(BoardConst.OK).append("_").append(set.getTopReadDuration());// TOP掃碼信息
            params.append("$").append(BoardConst.OK).append("_").append(set.getBotReadDuration());// BOT掃碼信息
//            params.append(";");
            String boardInfoParams = params.delete(0, 6). // 删除第一个指令符
//                    deleteCharAt(params.length() - 1). // 删除最后一个分号
                    toString();
            result.put("params", boardInfoParams);
            result.put("array_id", set.getBoardId());
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result.toJSONString(), apiRoutePath, 0);
        }
        catch (Exception ex)
        {
            log.error("GetBoardInfoParams error: " + ex.getMessage(), ex);
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(ex.getMessage());
        }
        return tranResult;
    }

    //4.获取板件流水的请求参数
    @RequestMapping(value = "/GetBoardSnOfUnbindParams", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetBoardSnOfUnbindParams(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/tripod/op/eap/interf/GetBoardSnOfUnbindParams";
        String tranResult = "";
        try
        {
            String barcode = jsonParas.getString("barcode"); // 条码
            // S15F29$廠別_批號_參數D_內部流水碼（廠商自行編碼）
            // S15F29$5001_m1908240096_D_內部流水碼_員工工號
            // 5001_m1908240096_D_內部流水碼_員工工號
            QueryWrapper<Extra> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("enable_flag", "Y");
            List<Extra> extras = this.extraService.list(queryWrapper);
            if (extras == null || extras.isEmpty())
            {
                throw new Exception("额外参数未配置");
            }
            Map<String, String> extraMap = extras.stream().collect(Collectors.toMap(Extra::getCode, Extra::getValue));
            JSONObject result = new JSONObject();
            long count = 0;
            Plan currentPlan = Plan.current(this.planService);
            Plan plan = currentPlan;
            SETBoard set;
            Pile pile = this.pileService.findOneByPileBarcode(barcode);
            if (pile == null)
            {
                if (currentPlan == null)
                {
                    throw new Exception("未找到当前计划");
                }
                set = this.setBoardService.findOneByBoardBarcodeAndBoardStatus(barcode, BoardConst.OK);
            }
            else
            {
                if (pile.getPlanId() != null && !pile.getPlanId().isEmpty())
                {
                    plan = Plan.byId(pile.getPlanId(), this.planService);
                }
                if (plan == null)
                {
                    plan = currentPlan;
                }
                if (plan == null)
                {
                    throw new Exception("未找到此包对应的计划");
                }
                count = this.setBoardService.countAllByPileBarcodeAndBoardStatus(barcode, BoardConst.OK, "pile_use_flag", BoardConst.FLAG_Y);
                set = this.setBoardService.findLatestOneByPileBarcodeAndBoardStatus(barcode, BoardConst.OK, "pile_use_flag", BoardConst.FLAG_Y);
            }
            if (set == null)
            {
                throw new Exception("未找到板信息");
            }
            result.put("is_last", count <= 1);
            List<String> params = new ArrayList<>();
            params.add(plan.getPlantCode()); // 厂别
            params.add(plan.getLotNum()); // 批号
            params.add("D"); // 固定参数
            params.add(set.getBoardSn()); // 内部流水码
            params.add(extraMap.getOrDefault("fb_station_code", "")); // 分板人员 配置
            String extraParams = String.join("_", params);
            result.put("params", extraParams);
            result.put("array_id", set.getBoardId());
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result.toJSONString(), apiRoutePath, 0);
        }
        catch (Exception ex)
        {
            log.error("GetBoardSnOfUnbindParams error: " + ex.getMessage(), ex);
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(ex.getMessage());
        }
        return tranResult;
    }
}
