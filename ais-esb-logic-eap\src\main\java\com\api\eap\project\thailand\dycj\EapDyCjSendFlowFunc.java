package com.api.eap.project.thailand.dycj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.dy.EapDyInterfCommon;
import com.api.eap.project.dy.p1.api.EapDyP1SendFlowSubFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 泰国定颖(持久)EAP发送流程功能函数
 * 1.PortStatusChangeReport:[接口]端口状态发生变化推送到EAP
 * 2.CarrierIDReport:[接口]载具扫描上报验证
 * 3.CarrierStatusReport:[接口]开始/结束/取消/终止投板（收板）时上报
 * 4.CCDDataReport:[接口]CCD读码上报
 * 5.FetchOutReport:[接口]每放一片板需要上报（针对放板机）
 * 6.StoreInReport:[接口]每收一片板需要上报（针对收板机）
 * 7.JobCountReport:[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
 * 8.PanelDataUploadReport:[接口]每片Panel收放台車事件上報
 * 9.WIPTrackingReport:[接口]每一lot完板上报
 * 10.CarrierDataUploadReport:[接口]最后全部任务完板后上报
 * 11.JobDataCreateModifyReport:[接口]人工操作界面拿走一片板子(手工录入)
 * 12.JobRemoveRecoveryReport:[接口]人工操作界面放回板子(手工录入)
 * 13.EachPositionReport:[接口]暂存位置信息动态上报
 * 14.EachPanelDataDownLoad:[上下游接口]当片信息(上游传递给下游)
 * 15.BcOffLineLotDownLoad:[上下游接口]BC离线工单信息下发
 * 16.BcOffLineLoginInDownLoad:[上下游接口]BC离线同步登入
 * 17.LotFinishDownLoad:[上下游接口]传递到下游设备结批信息
 * 18.ProductionModeDownLoad:[上下游接口]首件状态下发
 * 19.HeartBeatCheckDownLoad:[上下游接口]检测下游心跳状态
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@Service
@Slf4j
public class EapDyCjSendFlowFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private EapDyCjSendFlowSubFunc eapDyCjSendFlowSubFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyP1SendFlowSubFunc eapDyP1SendFlowSubFunc;

    //1.[接口]载具扫描上报验证
    public JSONObject CarrierIDReport(String station_code, String pallet_num, String port_code, String port_type,
                                int model, String production_mode, String manual_scan_flag,
                                JSONArray lot_list,String first_scan_flag,String read_type) throws Exception{
        JSONObject response_body=null;
        Boolean isP1Factory=eapDyInterfCommon.CheckDyFactory("P1");
        if(isP1Factory) return response_body;
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.CarrierIDReport(station_code,pallet_num,port_code,port_type,
                model,production_mode,manual_scan_flag,lot_list,first_scan_flag,read_type);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String existCarryID="";
        if(jbResult.containsKey("existCarryID")) existCarryID=jbResult.getString("existCarryID");
        if(jbResult.containsKey("responseBody")) response_body=jbResult.getJSONObject("responseBody");
        if(!existCarryID.equals("Y")) response_body=null;
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag && existCarryID.equals("")){
            throw new Exception(message);
        }
        return response_body;
    }

    //3.[接口]开始/结束/取消/终止投板（收板）时上报
    @Async
    public void CarrierStatusReport(String station_code, String pallet_num, String task_status,
                                    JSONArray lot_list,String port_mode,
                                    String port_type,String port_code,
                                    String manual_wip_flag) throws Exception{
        Boolean isP1Factory=eapDyInterfCommon.CheckDyFactory("P1");
        String tagCode="CarryStatus"+Integer.parseInt(port_code);
        if(isP1Factory){
            //写入点位改变端口状态
            opCommonFunc.WriteCellOneTagValue(station_code,port_type,"Eap","EapStatus",tagCode,
                    "AIS",task_status,false);
            return;
        }
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.CarrierStatusReport(station_code,pallet_num,task_status,lot_list,port_mode);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        //写入点位改变端口状态
        if(!manual_wip_flag.equals("Y")){
            opCommonFunc.WriteCellOneTagValue(station_code,port_type,"Eap","EapStatus",tagCode,
                    "AIS",task_status,false);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //7.[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
    @Async
    public void JobCountReport(String station_code, int task_left_count,String port_no) throws Exception{
        Boolean isP1Factory=eapDyInterfCommon.CheckDyFactory("P1");
        if(isP1Factory) return;
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.JobCountReport(station_code,task_left_count,port_no);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //9.[接口]每一lot完板上报
    @Async
    public void WIPTrackingReport(Long station_id,String station_code,String user_name,String dept_id,String shift_id,
                                  String start_date,String end_date,String lot_num,int task_count,int lot_count,
                                  int lot_finish_count,String material_code,String lot_short_num,String lot_version,
                                  String prod_mode,String attribute1,String attribute2,
                                  JSONArray item_attr_list,String offline_flag,String local_flag,JSONObject attr_else) throws Exception{
        Boolean isP1Factory=eapDyInterfCommon.CheckDyFactory("P1");
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=null;
        if(isP1Factory){
            jbResult= eapDyP1SendFlowSubFunc.WIPTrackingReport(station_id,station_code,user_name,dept_id,shift_id,
                    start_date,end_date,lot_num,task_count,lot_count,lot_finish_count,material_code,lot_short_num,lot_version,
                    prod_mode,attribute1,attribute2,item_attr_list,offline_flag,local_flag,attr_else);
        }
        else{
            jbResult= eapDyCjSendFlowSubFunc.WIPTrackingReport(station_id,station_code,user_name,dept_id,shift_id,
                    start_date,end_date,lot_num,task_count,lot_count,lot_finish_count,material_code,lot_short_num,lot_version,
                    prod_mode,attribute1,attribute2,item_attr_list,offline_flag,local_flag,attr_else);
        }
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //14.[上下游接口]当片信息(上游传递给下游)
    @Async
    public void EachPanelDataDownLoad(String station_code,String lot_id,String panel_id,String panel_status) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.EachPanelDataDownLoad(station_code,lot_id,panel_id,panel_status);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //15.[上下游接口]BC离线工单信息下发
    public void BcOffLineLotDownLoad(String station_code,String lot_id,String prod_id,int pnl_count,
                                     String lot_short_id,JSONArray item_list) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.BcOffLineLotDownLoad(station_code,lot_id,prod_id,pnl_count,lot_short_id,item_list);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //16.[上下游接口]BC离线同步登入
    public void BcOffLineLoginInDownLoad(String station_code,String user_id,String dept_id,
                                         String shift_id, String nick_name) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.BcOffLineLoginInDownLoad(station_code,user_id,dept_id,shift_id,nick_name);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //17.[上下游接口]传递到下游设备结批信息
    @Async
    public void LotFinishDownLoad(String station_code,String lot_id,int lot_count, String lot_short_id) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.LotFinishDownLoad(station_code,lot_id,lot_count,lot_short_id);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //18.[上下游接口]首件状态下发
    public void ProductionModeDownLoad(String station_code,int inspectCount) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.ProductionModeDownLoad(station_code,inspectCount);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //19.[上下游接口]检测下游心跳状态
    public void HeartBeatCheckDownLoad(String station_code,int force_end_a,int force_end_b) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.HeartBeatCheckDownLoad(station_code,force_end_a,force_end_b);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //进片报告
    @Async
    public void ReceiveReport(String station_code, String pnl_id,String lot_id,String seq_no) throws Exception{
        Boolean isP1Factory=eapDyInterfCommon.CheckDyFactory("P1");
        if(isP1Factory) return;
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.ReceiveReport(station_code,pnl_id,lot_id,seq_no);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //出片报告
    @Async
    public void SendOutReport(String station_code, String pnl_id,String lot_id,String seq_no) throws Exception{
        Boolean isP1Factory=eapDyInterfCommon.CheckDyFactory("P1");
        if(isP1Factory) return;
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjSendFlowSubFunc.SendOutReport(station_code,pnl_id,lot_id,seq_no);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }
}
