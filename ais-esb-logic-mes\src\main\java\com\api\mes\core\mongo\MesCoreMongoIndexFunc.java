package com.api.mes.core.mongo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbMongoIndex;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.mes.project.gx.MesGxMongoIndexFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 1.MES标准MongoDB索引创建
 * 2.根据系统参数读取判断项目索引创建
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */

@Service
@Slf4j
public class MesCoreMongoIndexFunc {
    @Autowired
    private CFuncDbMongoIndex cFuncDbMongoIndex;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MesGxMongoIndexFunc mesGxMongoIndexFunc;

    //创建表索引
    @Async
    public void CreateMesIndex(){
        String Mes_MongoDbIndexList=cFuncDbSqlResolve.GetParameterValue("Mes_MongoDbIndexList");
        if(Mes_MongoDbIndexList!=null && !Mes_MongoDbIndexList.equals("")){
            String[] lst=Mes_MongoDbIndexList.split(",",-1);
            if(lst!=null && lst.length>0){
                for(String itemName : lst){
                    if(itemName.equals("MES")){
                        CreateMesCoreIndex();
                    }
                    else if(itemName.equals("GX")){//国轩项目
                        mesGxMongoIndexFunc.CreateMesIndex();
                    }
                }
            }
        }
    }

    //标准表索引
    @Async
    public void CreateMesCoreIndex(){
        CreateMesMeOnlineFlowIndex();//上线信息
        CreateMesMeStationFlowIndex();//过站信息
        CreateMesMeStationQualityIndex();//质量数据
        CreateMesMeStationMaterialIndex();//物料数据
        CreateMesMeMaterialRelIndex();//主从工件关系
        CreateMesMeRecipeDataIndex();//配方传递临时数据
        CreateMesMeInvalidRelIndex();//解绑数据
        CreateMesMeStationMoFinishIndex();//MO工位工单完成数量
    }

    //创建c_mes_me_online_flow动态索引
    @Async
    public void CreateMesMeOnlineFlowIndex(){
        String tableName="c_mes_me_online_flow";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","prod_line_code","station_code","serial_type","serial_num",
                "make_order","enable_flag"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表c_mes_me_online_flow索引成功");
            else log.warn("创建Mongo表c_mes_me_online_flow索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表c_mes_me_online_flow索引失败:"+ex.getMessage());
        }
    }

    //创建c_mes_me_station_flow动态索引
    @Async
    public void CreateMesMeStationFlowIndex(){
        String tableName="c_mes_me_station_flow";
        JSONArray jArrayIndex=new JSONArray();
        //"item_date",
        String[] indexList=new String[]{"item_date_val","station_flow_id","prod_line_code","station_code","serial_type",
        "serial_num","make_order","small_model_type","quality_sign","repair_flag","print_barcode","online_flag","offline_flag",
        "up_flag","enable_flag","up_ng_code"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表c_mes_me_station_flow索引成功");
            else log.warn("创建Mongo表c_mes_me_station_flow索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表c_mes_me_station_flow索引失败:"+ex.getMessage());
        }
    }

    //创建c_mes_me_station_quality动态索引
    @Async
    public void CreateMesMeStationQualityIndex(){
        String tableName="c_mes_me_station_quality";
        JSONArray jArrayIndex=new JSONArray();
        //"item_date",
        String[] indexList=new String[]{"item_date_val","quality_trace_id","station_flow_id","prod_line_code","station_code","quality_for",
                "tag_des","quality_d_sign","serial_num"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表c_mes_me_station_quality索引成功");
            else log.warn("创建Mongo表c_mes_me_station_quality索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表c_mes_me_station_quality索引失败:"+ex.getMessage());
        }
    }

    //创建c_mes_me_station_material动态索引
    @Async
    public void CreateMesMeStationMaterialIndex(){
        String tableName="c_mes_me_station_material";
        JSONArray jArrayIndex=new JSONArray();
        //"item_date",
        String[] indexList=new String[]{"item_date_val","material_trace_id","station_flow_id","prod_line_code","station_code","material_code",
                "exact_barcode","lable_barcode","serial_num"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表c_mes_me_station_material索引成功");
            else log.warn("创建Mongo表c_mes_me_station_material索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表c_mes_me_station_material索引失败:"+ex.getMessage());
        }
    }

    //创建c_mes_me_material_rel动态索引
    @Async
    public void CreateMesMeMaterialRelIndex(){
        String tableName="c_mes_me_material_rel";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","material_rel_id","main_prod_line_code","main_serial_num","main_material_type",
                "sub_prod_line_code","sub_serial_num","sub_material_type","up_flag","up_ng_code","enable_flag"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表c_mes_me_material_rel索引成功");
            else log.warn("创建Mongo表c_mes_me_material_rel索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表c_mes_me_material_rel索引失败:"+ex.getMessage());
        }
    }

    //创建c_mes_me_recipe_data动态索引
    @Async
    public void CreateMesMeRecipeDataIndex(){
        String tableName="c_mes_me_recipe_data";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","prod_line_code","station_code","station_id","serial_num",
                "tag_id","tag_code","enable_flag"};
        int keep_days=30;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表c_mes_me_recipe_data索引成功");
            else log.warn("创建Mongo表c_mes_me_recipe_data索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表c_mes_me_recipe_data索引失败:"+ex.getMessage());
        }
    }

    //创建c_mes_me_invalid_rel动态索引
    @Async
    public void CreateMesMeInvalidRelIndex(){
        String tableName="c_mes_me_invalid_rel";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","make_order","station_code","small_model_type","main_serial_num",
                "main_serial_type","sub_serial_num","sub_serial_type","sub_index","remarks"};
        int keep_days=30;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表c_mes_me_invalid_rel索引成功");
            else log.warn("创建Mongo表c_mes_me_invalid_rel索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表c_mes_me_invalid_rel索引失败:"+ex.getMessage());
        }
    }

    //创建c_mes_me_station_mo_finish动态索引
    @Async
    public void CreateMesMeStationMoFinishIndex(){
        String tableName="c_mes_me_station_mo_finish";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"make_order","station_code","prod_line_code"};
        int keep_days=180;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表c_mes_me_station_mo_finish索引成功");
            else log.warn("创建Mongo表c_mes_me_station_mo_finish索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表c_mes_me_station_mo_finish索引失败:"+ex.getMessage());
        }
    }
}
