package com.api.pack.core.ccd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.pack.core.board.BoardConst;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
//@AllArgsConstructor
@NoArgsConstructor
public class CCDBoardsMessage extends CCDMessage<List<JSONObject>>
{
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private String transmitId;

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private Map<String, JSONObject> multiAspect;

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private Map<String, JSONArray> multiAspectChildren;

    public static CCDBoardsMessage fromJSON(String msg)
    {
        CCDBoardsMessage origin = JSON.parseObject(msg, CCDBoardsMessage.class);
        if (origin.getContent() != null && !origin.getContent().isEmpty())
        {
            JSONObject contentItem = origin.getContent().get(0);
            if (origin.multiAspect == null)
            {
                origin.multiAspect = new HashMap<>();
            }
            if (origin.multiAspectChildren == null)
            {
                origin.multiAspectChildren = new HashMap<>();
            }
            if (contentItem.containsKey(CCDConst.TRANSMIT_ID))
            {
                origin.transmitId = contentItem.getString(CCDConst.TRANSMIT_ID);
            }
            if (contentItem.containsKey(CCDConst.SET_FRONT_CONTENT))
            {
                String orient = CCDConst.FRONT.toLowerCase();
                JSONObject set = contentItem.getJSONObject(CCDConst.SET_FRONT_CONTENT);
                JSONArray pcs = set.getJSONArray(CCDConst.PCS_MSG_LIST);
                origin.multiAspect.putIfAbsent(orient, set);
                origin.multiAspectChildren.putIfAbsent(orient, pcs);
            }
            if (contentItem.containsKey(CCDConst.SET_BACK_CONTENT))
            {
                String orient = CCDConst.BACK.toLowerCase();
                JSONObject set = contentItem.getJSONObject(CCDConst.SET_BACK_CONTENT);
                JSONArray pcs = set.getJSONArray(CCDConst.PCS_MSG_LIST);
                origin.multiAspect.putIfAbsent(orient, set);
                origin.multiAspectChildren.putIfAbsent(orient, pcs);
            }
        }
        return origin;
    }
}
