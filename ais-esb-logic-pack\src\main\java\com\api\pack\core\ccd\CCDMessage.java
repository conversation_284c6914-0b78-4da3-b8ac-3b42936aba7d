package com.api.pack.core.ccd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public abstract class CCDMessage<Content>
{
    @ApiModelProperty(value = "调用来源")
    @JsonProperty("From")
    @JSONField(name = "From")
    private String from;

    @ApiModelProperty(value = "报文头部")
    @JsonProperty("Header")
    @JSONField(name = "Header")
    private String header;

    @ApiModelProperty(value = "系统时间")
    @JsonProperty("DataTime")
    @JSONField(name = "DataTime")
    private String dataTime;

    @ApiModelProperty(value = "数据内容")
    @JsonProperty("Content")
    @JSONField(name = "Content")
    private Content content;

    public String toJSON()
    {
        return JSON.toJSONString(this);
    }
}
