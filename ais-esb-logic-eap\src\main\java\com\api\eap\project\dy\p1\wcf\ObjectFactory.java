
package com.api.eap.project.dy.p1.wcf;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.api.eap.project.dy.p1.wcf package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.api.eap.project.dy.p1.wcf
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link AlarmReport }
     * 
     */
    public AlarmReport createAlarmReport() {
        return new AlarmReport();
    }

    /**
     * Create an instance of {@link RequestHead }
     * 
     */
    public RequestHead createRequestHead() {
        return new RequestHead();
    }

    /**
     * Create an instance of {@link AlarmReportRequestBody }
     * 
     */
    public AlarmReportRequestBody createAlarmReportRequestBody() {
        return new AlarmReportRequestBody();
    }

    /**
     * Create an instance of {@link AlarmReportResponse }
     * 
     */
    public AlarmReportResponse createAlarmReportResponse() {
        return new AlarmReportResponse();
    }

    /**
     * Create an instance of {@link Response }
     * 
     */
    public Response createResponse() {
        return new Response();
    }

    /**
     * Create an instance of {@link UserVerify }
     * 
     */
    public UserVerify createUserVerify() {
        return new UserVerify();
    }

    /**
     * Create an instance of {@link UserVerifyRequestBody }
     * 
     */
    public UserVerifyRequestBody createUserVerifyRequestBody() {
        return new UserVerifyRequestBody();
    }

    /**
     * Create an instance of {@link UserVerifyResponse }
     * 
     */
    public UserVerifyResponse createUserVerifyResponse() {
        return new UserVerifyResponse();
    }

    /**
     * Create an instance of {@link UserVerifyResponseItem }
     * 
     */
    public UserVerifyResponseItem createUserVerifyResponseItem() {
        return new UserVerifyResponseItem();
    }

    /**
     * Create an instance of {@link RTMReport }
     * 
     */
    public RTMReport createRTMReport() {
        return new RTMReport();
    }

    /**
     * Create an instance of {@link RtmReportRequestBody }
     * 
     */
    public RtmReportRequestBody createRtmReportRequestBody() {
        return new RtmReportRequestBody();
    }

    /**
     * Create an instance of {@link RTMReportResponse }
     * 
     */
    public RTMReportResponse createRTMReportResponse() {
        return new RTMReportResponse();
    }

    /**
     * Create an instance of {@link PanelDataUploadReport }
     * 
     */
    public PanelDataUploadReport createPanelDataUploadReport() {
        return new PanelDataUploadReport();
    }

    /**
     * Create an instance of {@link PanelDataUploadReportRequestBody }
     * 
     */
    public PanelDataUploadReportRequestBody createPanelDataUploadReportRequestBody() {
        return new PanelDataUploadReportRequestBody();
    }

    /**
     * Create an instance of {@link PanelDataUploadReportResponse }
     * 
     */
    public PanelDataUploadReportResponse createPanelDataUploadReportResponse() {
        return new PanelDataUploadReportResponse();
    }

    /**
     * Create an instance of {@link PanelDataUploadReportResponseItem }
     * 
     */
    public PanelDataUploadReportResponseItem createPanelDataUploadReportResponseItem() {
        return new PanelDataUploadReportResponseItem();
    }

    /**
     * Create an instance of {@link ParamVerify }
     * 
     */
    public ParamVerify createParamVerify() {
        return new ParamVerify();
    }

    /**
     * Create an instance of {@link ParamVerifyRequestBody }
     * 
     */
    public ParamVerifyRequestBody createParamVerifyRequestBody() {
        return new ParamVerifyRequestBody();
    }

    /**
     * Create an instance of {@link ParamVerifyResponse }
     * 
     */
    public ParamVerifyResponse createParamVerifyResponse() {
        return new ParamVerifyResponse();
    }

    /**
     * Create an instance of {@link ParamVerifyResponseItem }
     * 
     */
    public ParamVerifyResponseItem createParamVerifyResponseItem() {
        return new ParamVerifyResponseItem();
    }

    /**
     * Create an instance of {@link WIPTrackingReport }
     * 
     */
    public WIPTrackingReport createWIPTrackingReport() {
        return new WIPTrackingReport();
    }

    /**
     * Create an instance of {@link WipTrackingReportRequestBody }
     * 
     */
    public WipTrackingReportRequestBody createWipTrackingReportRequestBody() {
        return new WipTrackingReportRequestBody();
    }

    /**
     * Create an instance of {@link WIPTrackingReportResponse }
     * 
     */
    public WIPTrackingReportResponse createWIPTrackingReportResponse() {
        return new WIPTrackingReportResponse();
    }

    /**
     * Create an instance of {@link RecipeChangeReport }
     * 
     */
    public RecipeChangeReport createRecipeChangeReport() {
        return new RecipeChangeReport();
    }

    /**
     * Create an instance of {@link RecipeChangeReportRequestBody }
     * 
     */
    public RecipeChangeReportRequestBody createRecipeChangeReportRequestBody() {
        return new RecipeChangeReportRequestBody();
    }

    /**
     * Create an instance of {@link RecipeChangeReportResponse }
     * 
     */
    public RecipeChangeReportResponse createRecipeChangeReportResponse() {
        return new RecipeChangeReportResponse();
    }

    /**
     * Create an instance of {@link HelloWorld }
     * 
     */
    public HelloWorld createHelloWorld() {
        return new HelloWorld();
    }

    /**
     * Create an instance of {@link HelloWorldResponse }
     * 
     */
    public HelloWorldResponse createHelloWorldResponse() {
        return new HelloWorldResponse();
    }

    /**
     * Create an instance of {@link RecipeReport }
     * 
     */
    public RecipeReport createRecipeReport() {
        return new RecipeReport();
    }

    /**
     * Create an instance of {@link RecipeReportRequestBody }
     * 
     */
    public RecipeReportRequestBody createRecipeReportRequestBody() {
        return new RecipeReportRequestBody();
    }

    /**
     * Create an instance of {@link RecipeReportResponse }
     * 
     */
    public RecipeReportResponse createRecipeReportResponse() {
        return new RecipeReportResponse();
    }

    /**
     * Create an instance of {@link RealStatusReport }
     * 
     */
    public RealStatusReport createRealStatusReport() {
        return new RealStatusReport();
    }

    /**
     * Create an instance of {@link RealStatusReportRequestBody }
     * 
     */
    public RealStatusReportRequestBody createRealStatusReportRequestBody() {
        return new RealStatusReportRequestBody();
    }

    /**
     * Create an instance of {@link RealStatusReportResponse }
     * 
     */
    public RealStatusReportResponse createRealStatusReportResponse() {
        return new RealStatusReportResponse();
    }

    /**
     * Create an instance of {@link UtilityReport }
     * 
     */
    public UtilityReport createUtilityReport() {
        return new UtilityReport();
    }

    /**
     * Create an instance of {@link UtilityReportRequestBody }
     * 
     */
    public UtilityReportRequestBody createUtilityReportRequestBody() {
        return new UtilityReportRequestBody();
    }

    /**
     * Create an instance of {@link UtilityReportResponse }
     * 
     */
    public UtilityReportResponse createUtilityReportResponse() {
        return new UtilityReportResponse();
    }

    /**
     * Create an instance of {@link EQPInfoVerify }
     * 
     */
    public EQPInfoVerify createEQPInfoVerify() {
        return new EQPInfoVerify();
    }

    /**
     * Create an instance of {@link EQPInfoVerifyResponse }
     * 
     */
    public EQPInfoVerifyResponse createEQPInfoVerifyResponse() {
        return new EQPInfoVerifyResponse();
    }

    /**
     * Create an instance of {@link EqpInfoVerifyResponseItem }
     * 
     */
    public EqpInfoVerifyResponseItem createEqpInfoVerifyResponseItem() {
        return new EqpInfoVerifyResponseItem();
    }

    /**
     * Create an instance of {@link MaterialStatusReport }
     * 
     */
    public MaterialStatusReport createMaterialStatusReport() {
        return new MaterialStatusReport();
    }

    /**
     * Create an instance of {@link MaterialStatusReportRequestBody }
     * 
     */
    public MaterialStatusReportRequestBody createMaterialStatusReportRequestBody() {
        return new MaterialStatusReportRequestBody();
    }

    /**
     * Create an instance of {@link MaterialStatusReportResponse }
     * 
     */
    public MaterialStatusReportResponse createMaterialStatusReportResponse() {
        return new MaterialStatusReportResponse();
    }

    /**
     * Create an instance of {@link MaterialStatusReportResponseItem }
     * 
     */
    public MaterialStatusReportResponseItem createMaterialStatusReportResponseItem() {
        return new MaterialStatusReportResponseItem();
    }

    /**
     * Create an instance of {@link StatusChangeReport }
     * 
     */
    public StatusChangeReport createStatusChangeReport() {
        return new StatusChangeReport();
    }

    /**
     * Create an instance of {@link StatusChangeReportRequestBody }
     * 
     */
    public StatusChangeReportRequestBody createStatusChangeReportRequestBody() {
        return new StatusChangeReportRequestBody();
    }

    /**
     * Create an instance of {@link StatusChangeReportResponse }
     * 
     */
    public StatusChangeReportResponse createStatusChangeReportResponse() {
        return new StatusChangeReportResponse();
    }

    /**
     * Create an instance of {@link MaterialVerify }
     * 
     */
    public MaterialVerify createMaterialVerify() {
        return new MaterialVerify();
    }

    /**
     * Create an instance of {@link MaterialVerifyRequestBody }
     * 
     */
    public MaterialVerifyRequestBody createMaterialVerifyRequestBody() {
        return new MaterialVerifyRequestBody();
    }

    /**
     * Create an instance of {@link MaterialVerifyResponse }
     * 
     */
    public MaterialVerifyResponse createMaterialVerifyResponse() {
        return new MaterialVerifyResponse();
    }

    /**
     * Create an instance of {@link MaterialVerifyResponseItem }
     * 
     */
    public MaterialVerifyResponseItem createMaterialVerifyResponseItem() {
        return new MaterialVerifyResponseItem();
    }

    /**
     * Create an instance of {@link ResponseHead }
     * 
     */
    public ResponseHead createResponseHead() {
        return new ResponseHead();
    }

    /**
     * Create an instance of {@link UserVerifyResponseBody }
     * 
     */
    public UserVerifyResponseBody createUserVerifyResponseBody() {
        return new UserVerifyResponseBody();
    }

    /**
     * Create an instance of {@link RtmInfos }
     * 
     */
    public RtmInfos createRtmInfos() {
        return new RtmInfos();
    }

    /**
     * Create an instance of {@link ArrayOfItem }
     * 
     */
    public ArrayOfItem createArrayOfItem() {
        return new ArrayOfItem();
    }

    /**
     * Create an instance of {@link Item }
     * 
     */
    public Item createItem() {
        return new Item();
    }

    /**
     * Create an instance of {@link AttrInfos }
     * 
     */
    public AttrInfos createAttrInfos() {
        return new AttrInfos();
    }

    /**
     * Create an instance of {@link PanelDataUploadReportRequestEDC }
     * 
     */
    public PanelDataUploadReportRequestEDC createPanelDataUploadReportRequestEDC() {
        return new PanelDataUploadReportRequestEDC();
    }

    /**
     * Create an instance of {@link PanelDataUploadReportResponseBody }
     * 
     */
    public PanelDataUploadReportResponseBody createPanelDataUploadReportResponseBody() {
        return new PanelDataUploadReportResponseBody();
    }

    /**
     * Create an instance of {@link ParamVerifyResponseBody }
     * 
     */
    public ParamVerifyResponseBody createParamVerifyResponseBody() {
        return new ParamVerifyResponseBody();
    }

    /**
     * Create an instance of {@link LotInfos }
     * 
     */
    public LotInfos createLotInfos() {
        return new LotInfos();
    }

    /**
     * Create an instance of {@link EdcInfos }
     * 
     */
    public EdcInfos createEdcInfos() {
        return new EdcInfos();
    }

    /**
     * Create an instance of {@link RecipeInfos }
     * 
     */
    public RecipeInfos createRecipeInfos() {
        return new RecipeInfos();
    }

    /**
     * Create an instance of {@link UtlInfos }
     * 
     */
    public UtlInfos createUtlInfos() {
        return new UtlInfos();
    }

    /**
     * Create an instance of {@link EqpInfoVerifyResponseBody }
     * 
     */
    public EqpInfoVerifyResponseBody createEqpInfoVerifyResponseBody() {
        return new EqpInfoVerifyResponseBody();
    }

    /**
     * Create an instance of {@link MtrlInfos }
     * 
     */
    public MtrlInfos createMtrlInfos() {
        return new MtrlInfos();
    }

    /**
     * Create an instance of {@link MaterialStatusReportResponseBody }
     * 
     */
    public MaterialStatusReportResponseBody createMaterialStatusReportResponseBody() {
        return new MaterialStatusReportResponseBody();
    }

    /**
     * Create an instance of {@link MaterialVerifyResponseBody }
     * 
     */
    public MaterialVerifyResponseBody createMaterialVerifyResponseBody() {
        return new MaterialVerifyResponseBody();
    }

}
