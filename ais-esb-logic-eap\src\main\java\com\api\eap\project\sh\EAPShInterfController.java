package com.api.eap.project.sh;


import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 胜宏接口处理
 * 1.扫描上报MES获取JDE料号
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/sh/interf")
public class EAPShInterfController {

    @Resource
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    //申请下发

    /**
     * 申请下发配方到PLC 查询所有PLC 并按 key1,key2 ，value1&value2 排列
     * @param jsonParas 配方名称， 配方名称中包含配方名称以及配方的版本
     * @param request
     * @return
     */
    @RequestMapping(value = "/EAPApplyDistributeRecipe", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexApplyDistributeRecipe(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/sh/interf/EAPApplyDistributeRecipe";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String recipe_name = jsonParas.getString("recipe_name");
            String[] split = null;
            log.debug("下发的配方：{}",recipe_name);
            Assert.isTrue(!(StringUtils.isEmpty(recipe_name)||(split=recipe_name.split("_")).length<2),"要下发的配方信息为空");
            //将配方所有的点位拼接在一起以及值拼接在一起  key:DesPlc/PlcRecipe/PlcRecipe271,DesPlc/PlcRecipe/PlcRecipe272 value:222,0,0,0
            //RECIPE_RANGE 不下发
            String recipeRange=cFuncDbSqlResolve.GetParameterValue("RECIPE_RANGE");
            String select="SELECT string_agg(tag.tag,',')as tag_key,string_agg(d.parameter_val,'&') as tag_value FROM ( SELECT * FROM a_eap_fmod_recipe WHERE recipe_name = '" + split[0] + "'  and recipe_version = '"+split[1]+"') AS recipe LEFT JOIN a_eap_fmod_recipe_detail AS d ON recipe.recipe_id = d.recipe_id LEFT JOIN ( SELECT concat ( client_code, '/',tag_group_code, '/', tag_code ) AS tag,tag.tag_id  FROM scada_client client LEFT JOIN scada_tag_group gro ON client.client_id = gro.client_id LEFT JOIN scada_tag AS tag ON gro.tag_group_id = tag.tag_group_id  ) tag on tag.tag_id=d.tag_id ";
            if(!StringUtils.isEmpty(recipeRange)){

                String parameterCodeCondition="";
                String[] recipeRangeArray = recipeRange.split(",");
                for (String s : recipeRangeArray) {
                    parameterCodeCondition+=StringUtils.isEmpty(parameterCodeCondition)?"'"+s+"'":",'"+s+"'";
                }
                if(!StringUtils.isEmpty(parameterCodeCondition)){
                    select+="WHERE d.parameter_code NOT IN("+parameterCodeCondition+")";
                }

            }
            List<Map<String, Object>> maps = cFuncDbSqlExecute.ExecSelectSql(station_code, select, false, request, apiRoutePath);
            Assert.notEmpty(maps,"没有需要下发的配方");
            maps=maps.stream().map(m->{
                Object tagValues = m.get("tag_value");
                String[] tagValueArray = tagValues.toString().split("&");
                String newTagValues="";
                for (String tagValue : tagValueArray) {
                    Double number = isNumber(tagValue);
                    tagValue=number==null?tagValue:Double.valueOf((number*100d)).intValue()+"";
                    newTagValues+=StringUtils.isEmpty(newTagValues)?tagValue:"&"+tagValue;
                }
                m.put("tag_value",newTagValues);
                return m;
            }).collect(Collectors.toList());
            log.debug("下发的配方点位：{}",maps);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, JSONObject.toJSONString(maps.stream().findFirst().get()), "", 0);
        } catch (Exception ex) {
            errorMsg = "下发配方异常：" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
    public Double isNumber(String value){
        try{
           return Double.parseDouble(value);
        }catch (Exception e){
            return null;
        }
    }
}
