package com.api.eap.project.hsql;


import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <p>
 * 志圣EAP接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @Date 2025-04-07
 * @since 
 */
@Service
public class EapHsqlInterfCommon {
    /**
     * 转换响应格式，给event模块使用。
     * @param originalResponse 原始响应
     * @return 转换后的响应
     */
    public static JSONObject convertToEventFormat(JSONObject originalResponse) {
        JSONObject convertedResponse = new JSONObject();
        if(!originalResponse.getBoolean("Success")) {
        	convertedResponse.put("error", originalResponse.getString("Msg"));
        }
        convertedResponse.put("result", originalResponse.getBoolean("Success") ? "success" : "error");
        convertedResponse.put("data", originalResponse.getJSONObject("Content"));
        return convertedResponse;
    }
    
    /**
     * 转换响应格式，给event模块使用。
     * @param originalResponse 原始响应
     * @return 转换后的响应
     */
    public static JSONObject convertToWebFormat(JSONObject originalResponse) {
        JSONObject convertedResponse = new JSONObject();
        if(!originalResponse.getBoolean("Success")) {
        	convertedResponse.put("msg", originalResponse.getString("Msg"));
        }else{
        	convertedResponse.put("Success", true);
        }
        convertedResponse.put("result", originalResponse.getBoolean("Success") ? "success" : "error");
        convertedResponse.put("code", originalResponse.getBoolean("Success") ? "0" : "9999");
        convertedResponse.put("data", originalResponse.getJSONObject("Content"));
        return convertedResponse;
    }
    
}