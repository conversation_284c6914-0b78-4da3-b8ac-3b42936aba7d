package com.api.pack.core.mongo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbMongoIndex;
import com.api.common.db.CFuncDbSqlResolve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 1.PACK标准MongoDB索引创建
 * 2.根据系统参数读取判断项目索引创建
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-03
 */

@Service
@Slf4j
public class PackCoreMongoIndexFunc {
    @Autowired
    private CFuncDbMongoIndex cFuncDbMongoIndex;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //创建表索引
    @Async
    public void CreatePackIndex(){
        String Pack_MongoDbIndexList=cFuncDbSqlResolve.GetParameterValue("Pack_MongoDbIndexList");
        if(Pack_MongoDbIndexList!=null && !Pack_MongoDbIndexList.equals("")){
            String[] lst=Pack_MongoDbIndexList.split(",",-1);
            if(lst!=null && lst.length>0){
                for(String itemName : lst){
                    if(itemName.equals("PACK")){
                        CreatePackCoreIndex();
                    }
                }
            }
        }
    }

    //标准表索引
    @Async
    public void CreatePackCoreIndex(){
        CreatePackApsPlanIndex();
        CreatePackMeArrayIndex();
        CreatePackMeBdIndex();
        CreatePackMePileIndex();
    }

    //创建a_pack_aps_plan动态索引
    @Async
    public void CreatePackApsPlanIndex(){
        String tableName="a_pack_aps_plan";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","plan_id","task_from","task_type","lot_num","model_type",
                "model_version","plan_lot_count","array_type","bd_type","cycle_period","finish_ok_count","lot_status","enable_flag"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表a_pack_aps_plan索引成功");
            else log.warn("创建Mongo表a_pack_aps_plan索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表a_pack_aps_plan索引失败:"+ex.getMessage());
        }
    }

    //创建a_pack_me_array动态索引
    @Async
    public void CreatePackMeArrayIndex(){
        String tableName="a_pack_me_array";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","array_id","pile_barcode","array_barcode","lot_num","array_index",
                "array_level","array_mark","board_sn","board_result","board_turn","deposit_position","xout_flag","xout_set_num","xout_act_num",
                "array_status","array_ng_code","user_name","task_type","model_type","model_version","array_type","bd_type","cycle_period",
                "up_flag","pile_use_flag","enable_flag","unbind_flag"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表a_pack_me_array索引成功");
            else log.warn("创建Mongo表a_pack_me_array索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表a_pack_me_array索引失败:"+ex.getMessage());
        }
    }

    //创建a_pack_me_bd动态索引
    @Async
    public void CreatePackMeBdIndex(){
        String tableName="a_pack_me_bd";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","bd_id","array_id","array_barcode","array_status",
                "bd_barcode","bd_index","bd_level","xout_flag","bd_status","bd_ng_code","enable_flag"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表a_pack_me_bd索引成功");
            else log.warn("创建Mongo表a_pack_me_bd索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表a_pack_me_bd索引失败:"+ex.getMessage());
        }
    }

    //创建a_pack_me_pile动态索引
    @Async
    public void CreatePackMePileIndex(){
        String tableName="a_pack_me_pile";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","pile_id","pile_barcode","custom_barcode","lot_num","model_type",
                                        "pile_index","array_count","trunk_flag","pile_user","pile_status","pile_ng_code"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表a_pack_me_pile索引成功");
            else log.warn("创建Mongo表a_pack_me_pile索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表a_pack_me_pile索引失败:"+ex.getMessage());
        }
    }
}
