package com.api.dcs.core.wms;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 避让路线创建(回归到近点)
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Service
@Slf4j
public class DcsCarMainBiRangFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsCarCommonFunc dcsCarCommonFunc;

    //创建任务与路线规划
    public Map<String, Object> CreateTaskRoute(String userID, HttpServletRequest request, String apiRoutePath,
                                               String ware_house, String car_code,Integer car_location_x) throws Exception{
        String errorMsg = "";
        Map<String, Object> mapResult=new HashMap<>();
        mapResult.put("passFlag",false);//是否允许越过此调度
        mapResult.put("errorMsg",errorMsg);//当前调度错误信息

        //1.判断天车是否存在
        Map<String, Object> mapCar= dcsCarCommonFunc.GetCarInfo(car_code);
        if(mapCar==null){
            throw new Exception("天车编码{"+car_code+"}不存在天车基础数据中");
        }
        Integer zero_location_x=Integer.parseInt(mapCar.get("zero_location_x").toString());
        Integer zero_location_y=Integer.parseInt(mapCar.get("zero_location_y").toString());
        Integer zero_location_z=Integer.parseInt(mapCar.get("zero_location_z").toString());
        Integer safe_width=Integer.parseInt(mapCar.get("safe_width").toString());
        if(Math.abs(zero_location_x-car_location_x)<=safe_width){
            mapResult.put("passFlag",true);
            return mapResult;
        }
        Integer para_f=0;//旋转角度(0度,180度)
        Integer para_r=3;//R任务类型(1叉取、2放置、3避让、4寻中、5抛丸)
        Integer para_a=3;//A位置代码(1上料位、2下料位、3库位、4抛丸位)
        Integer para_h=0;//半高标志位,1为半高位

        //2.避让路线
        Long car_route_id= dcsCarCommonFunc.LockCarRouteIns(userID,request,apiRoutePath,0L,ware_house,car_code,
                "BI_RANG",1,zero_location_x,zero_location_y,zero_location_z,
                0L,"","","N",
                "N","","",0,
                "","","",
                para_f,para_r,para_a,para_h);
        return mapResult;
    }
}
