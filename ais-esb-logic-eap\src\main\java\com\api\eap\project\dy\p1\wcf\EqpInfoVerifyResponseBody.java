
package com.api.eap.project.dy.p1.wcf;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>eqpInfoVerifyResponseBody complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="eqpInfoVerifyResponseBody"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="now" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="hold_flag" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="cim_msg" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "eqpInfoVerifyResponseBody", propOrder = {
    "now",
    "holdFlag",
    "cimMsg"
})
public class EqpInfoVerifyResponseBody {

    @XmlElement(required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar now;
    @XmlElement(name = "hold_flag")
    protected String holdFlag;
    @XmlElement(name = "cim_msg")
    protected String cimMsg;

    /**
     * 获取now属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getNow() {
        return now;
    }

    /**
     * 设置now属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setNow(XMLGregorianCalendar value) {
        this.now = value;
    }

    /**
     * 获取holdFlag属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getHoldFlag() {
        return holdFlag;
    }

    /**
     * 设置holdFlag属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setHoldFlag(String value) {
        this.holdFlag = value;
    }

    /**
     * 获取cimMsg属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCimMsg() {
        return cimMsg;
    }

    /**
     * 设置cimMsg属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCimMsg(String value) {
        this.cimMsg = value;
    }

}
