package com.api.dcs.core.interf.print;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 喷码接受流程功能函数
 * 1.接受喷码完成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Service
@Slf4j
public class DcsCorePrintRecvFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsInterfCommon dcsInterfCommon;

    //1.接受喷码完成
    public JSONObject PrintRecvTaskFinish(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String esbInterfCode="PrintRecvTaskFinish";
        String token="";
        String moID="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        Integer code=0;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String apsTaskTable="b_dcs_aps_task";
        try{
            jbResult.put("code",code);
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("moID",moID);

            //1.接受参数
            String task_num=jsonParas.getString("task_num");
            String station_code=jsonParas.getString("station_code");
            request_uuid=jsonParas.getString("request_uuid");
            Integer unload_code=jsonParas.getInteger("unload_code");
            if(task_num==null) task_num="";
            if(station_code==null) station_code="";
            if(unload_code==null) unload_code=-1;

            //2.判断工位是否存在
            String sqlStationCount="select count(1) " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            Integer stationCount=cFuncDbSqlResolve.GetSelectCount(sqlStationCount);
            if(stationCount<=0){
                code=-1;
                errorMsg="喷码传递工位号{"+station_code+"}未存在中控系统";
                responseParas=dcsInterfCommon.CreateResponse(request_uuid,code,errorMsg);
                jbResult.put("code",code);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            token=station_code;
            jbResult.put("token",token);

            //3.根据工位号寻找token
            String sqlEsbInterf="select " +
                    "COALESCE(b.paras_list,'') paras_list " +
                    "from sys_core_esb_interf a inner join sys_core_esb_interf_d b " +
                    "on a.esb_interf_id=b.esb_interf_id " +
                    "where a.esb_interf_code='"+esbInterfCode+"' " +
                    "and b.enable_flag='Y' and b.token='"+station_code+"' " +
                    "order by b.esb_interf_d_id LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListEsb=cFuncDbSqlExecute.ExecSelectSql("AIS",
                    sqlEsbInterf,false,request,apiRoutePath);
            String paras_list="";
            if(itemListEsb!=null && itemListEsb.size()>0){
                paras_list=itemListEsb.get(0).get("paras_list").toString();
            }
            String[] lstParas=paras_list.split(",",-1);
            if(lstParas==null || lstParas.length<2){
                code=-2;
                errorMsg="中控喷码完成接口未设置token{"+station_code+"}对应正确的paras_list{完成代码TAG,喷码完成标志位TAG},用于通知流程喷码完成";
                responseParas=dcsInterfCommon.CreateResponse(request_uuid,code,errorMsg);
                jbResult.put("code",code);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String printFinishCodeTag=lstParas[0];//完成代码TAG
            String printFinishTag=lstParas[1];//喷码完成标志位TAG

            //4.根据任务号查找mo_id
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                moID=docItemBigData.getString("mo_id");
                iteratorBigData.close();
            }
            if(moID.equals("")){
                code=-3;
                errorMsg="喷码系统传递任务号{"+task_num+"}不存在中控生产任务计划中";
                responseParas=dcsInterfCommon.CreateResponse(request_uuid,code,errorMsg);
                jbResult.put("code",code);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            jbResult.put("moID",moID);

            //5.写入到SCADA
            String tagOnlyKeyList=printFinishCodeTag+","+printFinishTag;
            String tagValueList=unload_code+"&1";
            errorMsg= cFuncUtilsCellScada.WriteTagByStation(station_code,station_code,tagOnlyKeyList,tagValueList,true);
            if(!errorMsg.equals("")){
                code=-4;
                errorMsg="通知中控下料完成失败:"+errorMsg;
                responseParas=dcsInterfCommon.CreateResponse(request_uuid,code,errorMsg);
                jbResult.put("code",code);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            responseParas=dcsInterfCommon.CreateResponse(request_uuid,code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=dcsInterfCommon.CreateResponse(request_uuid,code,errorMsg);
            jbResult.put("code",code);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
