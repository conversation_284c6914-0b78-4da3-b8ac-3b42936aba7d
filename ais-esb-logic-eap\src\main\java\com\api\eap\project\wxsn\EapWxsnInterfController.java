package com.api.eap.project.wxsn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 无锡深南接口处理
 * 1.被动接生产任务
 * 2.上报完板信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/wxsn/interf")
public class EapWxsnInterfController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapWxsnSharePlanController eapWxsnSharePlanController;

    //1.被动接生产任务(从MES下载配方)
    @RequestMapping(value = "/MesRecvTask", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesRecvTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/wxsn/interf/MesRecvTask";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        log.info("深南保存配方时接收的数据：" + jsonParas.toJSONString());
        JSONObject jbResult = mesRecvTask(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //处理任务接受
    private JSONObject mesRecvTask(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "MesRecvTask";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            //处理EAP数据
            String station_code = jsonParas.getString("station_code");
            JSONArray arrlist = jsonParas.getJSONArray("arrlist");
            if (arrlist == null || arrlist.size() <= 0) {
                errorMsg = "参数arrlist为null,或者arrlist未传递任务信息";
                responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            JSONArray plan_list = new JSONArray();
            for (int i = 0; i < arrlist.size(); i++) {
                JSONObject jbItem = new JSONObject();
                JSONObject jsonObject = arrlist.getJSONObject(i);
                String PortNum = jsonObject.getString("PortNum");//端口号
                String group_lot_num = jsonObject.getString("GroupLotNum");
                String LotNum = jsonObject.getString("LotNum");//工单ID
                String MaterialCode = jsonObject.getString("MaterialCode");//物资编码
                String ProcedureCode = jsonObject.getString("ProcedureCode");//工序代码
                String LotPlanCount = jsonObject.getString("LotPlanCount");//批次数量
                String LotLength = jsonObject.getString("LotLength");//板长
                String LotWidth = jsonObject.getString("LotWidth");//板宽
                String LotTickness = jsonObject.getString("LotTickness");//板厚
                String PalletNum = jsonObject.getString("PalletNum");//载具编码
                String LineSpeed = jsonObject.getString("LineSpeed");//线速度
                String PalletType = jsonObject.getString("PalletType");//载具类型
                String QRCodeFlag = jsonObject.getString("QRCodeFlag");//有无二维码
                String PEFilmFlag = jsonObject.getString("PEFilmFlag");//有无PE膜
                String TurnRoundFlag = jsonObject.getString("TurnRoundFlag");//是否转向
                String FaceCode = jsonObject.getString("FaceCode");//板件面次
                String LoadIntervalTime = jsonObject.getString("LoadIntervalTime");//放板间隔时间
                String HoistStatus = jsonObject.getString("HoistStatus");//提升机状态
                String LotProductModel = jsonObject.getString("LotProductModel");//陪镀板类型
                String UnLoadPEFilmFlag = jsonObject.getString("UnLoadPEFilmFlag");//收板机有无PE膜
                String UnLoadTurnRoundFlag = jsonObject.getString("UnLoadTurnRoundFlag");//收板机是否转向
                String UnLoadUseFlag = jsonObject.getString("UnLoadUseFlag");//收板机是否使用

                //合成标准
                jbItem.put("task_from", "MES");
                jbItem.put("group_lot_num", group_lot_num);
                jbItem.put("lot_num", LotNum);
                jbItem.put("lot_index", i + 1);
                jbItem.put("plan_lot_count", LotPlanCount);
                jbItem.put("port_code", PortNum);
                jbItem.put("material_code", MaterialCode);
                jbItem.put("pallet_num", PalletNum);
                jbItem.put("pallet_type", PalletType);
                jbItem.put("panel_length", LotLength);
                jbItem.put("panel_width", LotWidth);
                jbItem.put("panel_tickness", LotTickness);
                jbItem.put("face_code", FaceCode);

                JSONObject otherAttribute = new JSONObject();
                otherAttribute.put("ProcedureCode", ProcedureCode);
                otherAttribute.put("LineSpeed", LineSpeed);
                otherAttribute.put("QRCodeFlag", QRCodeFlag);
                otherAttribute.put("PEFilmFlag", PEFilmFlag);
                otherAttribute.put("TurnRoundFlag", TurnRoundFlag);
                otherAttribute.put("LoadIntervalTime", LoadIntervalTime);
                otherAttribute.put("HoistStatus", HoistStatus);
                otherAttribute.put("LotProductModel", LotProductModel);
                otherAttribute.put("UnLoadPEFilmFlag", UnLoadPEFilmFlag);
                otherAttribute.put("UnLoadTurnRoundFlag", UnLoadTurnRoundFlag);
                otherAttribute.put("UnLoadUseFlag", UnLoadUseFlag);
                jbItem.put("other_attribute", otherAttribute.toJSONString());
                plan_list.add(jbItem);
            }
            JSONObject postParas = new JSONObject();
            postParas.put("station_code", station_code);
            postParas.put("plan_list", plan_list);
            // String sharePlanUrl = "http://127.0.0.1:9090/aisEsbApi/eap/core/share/EapCoreSharePlanSave";
            // JSONObject jbPlanResult = cFuncUtilsRest.PostJbBackJb(sharePlanUrl, postParas);
            String result = eapWxsnSharePlanController.EapWxsnSharePlanSave(postParas, request);
            JSONObject jbPlanResult = JSONObject.parseObject(result);
            if (jbPlanResult.getInteger("code") != 0) {
                errorMsg = jbPlanResult.getString("error");
                responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            responseParas = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "");
        } catch (Exception ex) {
            errorMsg = "AIS接受任务发生未知异常:" + ex.getMessage();
            responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //获取当前执行的放板任务
    @RequestMapping(value = "/GetWorkLoadPlan", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetWorkLoadPlan(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/interf/GetWorkLoadPlan";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                mapBigDataRow.put("port_code", docItemBigData.getString("port_code"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("other_attribute", docItemBigData.containsKey("other_attribute") ? docItemBigData.getString("other_attribute") : "");
                itemList.add(mapBigDataRow);
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在放板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更新任务状态为WORK
    @RequestMapping(value = "/UpdateLoadPlanWorkStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String UpdateLoadPlanWorkStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/interf/UpdateLoadPlanWorkStatus";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String group_lot_num = "";
            long station_id_long = Long.parseLong(station_id);

            //先将当前端口正在进行中的任务结束掉
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("attribute1").ne("Y"));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            Update updateBigData2 = new Update();
            updateBigData2.set("group_lot_status", "CANCEL");
            mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);

            //选择PLAN任务
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("attribute1").ne("Y"));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                iteratorBigData.close();
            }
            if (!group_lot_num.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("attribute1").ne("Y"));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                updateBigData.set("port_code", port_code);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);

        } catch (Exception ex) {
            errorMsg = "选择端口任务为进行中异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //判断是否存在收板任务
    @RequestMapping(value = "/EapWxsnUnLoadPlanExistJudge", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnUnLoadPlanExistJudge(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/interf/EapWxsnUnLoadPlanExistJudge";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    mapBigDataRow.put("target_update_count", docItemBigData.getInteger("target_update_count"));
                    mapBigDataRow.put("port_code", docItemBigData.getString("port_code"));
                    mapBigDataRow.put("other_attribute", docItemBigData.containsKey("other_attribute") ? docItemBigData.getString("other_attribute") : "");
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在收板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更新任务状态为FINISH
    @RequestMapping(value = "/UpdateLoadPlanFinishStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String UpdateLoadPlanFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/interf/UpdateLoadPlanFinishStatus";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String group_lot_num = jsonParas.getString("group_lot_num");
            long station_id_long = Long.parseLong(station_id);

            Query queryBigData = new Query();
            if (!group_lot_num.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "FINISH");
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);

        } catch (Exception ex) {
            errorMsg = "选择端口任务为进行中异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //离线板件信息存储
    @RequestMapping(value = "/EapWxsnLoadMeOffPanelSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnLoadMeOffPanelSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/interf/EapWxsnLoadMeOffPanelSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String user_name = "";
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");
            String panel_index = jsonParas.getString("panel_index");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");
            String dummy_flag = jsonParas.getString("dummy_flag");
            String manual_judge_code = jsonParas.getString("manual_judge_code");
            String panel_status = jsonParas.getString("panel_status");
            String inspect_flag = jsonParas.getString("inspect_flag");
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";

            long station_id_long = Long.parseLong(station_id);
            String port_code = port_index;
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) {
                port_code = lstPort.get(0).get("port_code").toString();
            }
            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //存储
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", "");
            mapBigDataRow.put("task_from", "AIS");
            mapBigDataRow.put("group_lot_num", "");
            mapBigDataRow.put("lot_num", "");
            mapBigDataRow.put("lot_short_num", "");
            mapBigDataRow.put("lot_index", 0);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", "");
            mapBigDataRow.put("pallet_num", "");
            mapBigDataRow.put("pallet_type", "");
            mapBigDataRow.put("lot_level", "");
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", 0d);
            mapBigDataRow.put("panel_width", 0d);
            mapBigDataRow.put("panel_tickness", 0d);
            mapBigDataRow.put("panel_index", Integer.parseInt(panel_index));
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", 0);
            mapBigDataRow.put("panel_ng_msg", "");
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", "Y");
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, station_flow_id, "", 0);
        } catch (Exception ex) {
            errorMsg = "离线板件信息存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
