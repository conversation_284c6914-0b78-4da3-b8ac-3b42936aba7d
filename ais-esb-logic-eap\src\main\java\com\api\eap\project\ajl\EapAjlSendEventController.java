package com.api.eap.project.ajl;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * (安捷利)投收扳机标准发送事件接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-25
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/ajl/event/send")
public class EapAjlSendEventController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private EapAjlSendEventFunc eapAjlSendEventFunc;

    //1.[接口]离线补报保存
    @RequestMapping(value = "/EapAjlOffLineDataSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapAjlOffLineDataSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/ajl/event/send/EapAjlOffLineDataSave";
        String transResult = "";
        String errorMsg = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String event_name = jsonParas.getString("event_name");
            JSONObject jbSend = jsonParas.getJSONObject("jbSend");
            opCommonFunc.SaveUnFinishInterfData(station_id, event_name, jbSend);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "EAP请求叫料异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]离线补报上报
    @RequestMapping(value = "/EapAjlOffLineDataReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapAjlOffLineDataReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/ajl/event/send/EapAjlOffLineDataReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String interf_offline_id = "";
            String station_code = "";
            String tagList = "";
            String tagWriteValue = "";
            String offLineTable = "a_eap_me_interf_offline";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("up_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(offLineTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                interf_offline_id = docItemBigData.getString("interf_offline_id");
                String send_msg = docItemBigData.getString("send_msg");
                if (send_msg != null && !send_msg.equals("")) {
                    JSONObject jbSend = JSONObject.parseObject(send_msg);
                    if (jbSend != null) {
                        station_code = jbSend.getString("station_code");
                        tagList = jbSend.getString("tag_key");
                        tagWriteValue = jbSend.getString("tag_value");
                    }
                }
                iteratorBigData.close();
            }
            if (!tagList.equals("")) {
                errorMsg = cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, tagList, tagWriteValue, true);
                if (errorMsg.equals("")) {
                    Query queryBigData1 = new Query();
                    queryBigData1.addCriteria(Criteria.where("interf_offline_id").is(interf_offline_id));

                    Update updateBigData = new Update();
                    updateBigData.set("up_flag", "Y");
                    mongoTemplate.updateFirst(queryBigData1, updateBigData, offLineTable);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "EAP请求叫料异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.[接口]响应secs事件
    @RequestMapping(value = "/EapAjlSecsRcmdResponse", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapAjlSecsRcmdResponse(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/ajl/event/send/EapAjlSecsRcmdResponse";
        String transResult = "";
        String errorMsg = "";
        try {
            String rcmd_code = jsonParas.getString("rcmd_code");
            if (rcmd_code.indexOf("agv_before_in") != -1 || rcmd_code.indexOf("execute") != -1) {
                transResult = eapAjlSendEventFunc.EapAjlAgvStaus(jsonParas, request);
            }
        } catch (Exception ex) {
            errorMsg = "EAP请求叫料异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


}
