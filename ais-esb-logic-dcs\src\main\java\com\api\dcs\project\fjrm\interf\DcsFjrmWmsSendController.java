package com.api.dcs.project.fjrm.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.dcs.project.shzy.interf.DcsShzyWmsSendFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * WMS发送流程数据定义接口
 * 2.通知MES库存变化
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/fjrm/interf/wms/send")
public class DcsFjrmWmsSendController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsFjrmWmsSendFunc dcsFjrmWmsSendFunc;

    @Resource
    private CFuncLogInterf cFuncLogInterf;

    //1.通知MES库存变化
    @RequestMapping(value = "/DcsFjrmWmsSendMseStockChange", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsFjrmWmsSendMseStockChange(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/fjrm/interf/wms/send/DcsFjrmWmsSendMseStockChange";
        String transResult = "";
        String errorMsg = "";
        try {
            String task_num= jsonParas.getString("task_num");
            String status= jsonParas.getString("status");
            String status_msg= jsonParas.getString("status_msg");
            dcsFjrmWmsSendFunc.SendMseStockChange(task_num,status,status_msg);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "通知中控上料完成异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
