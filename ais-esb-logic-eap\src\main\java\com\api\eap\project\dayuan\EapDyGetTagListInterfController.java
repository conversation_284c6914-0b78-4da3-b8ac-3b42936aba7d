package com.api.eap.project.dayuan;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * EapDyGetTagListInterfController
 * 根據client_code,tag_group_code查詢tag_code列表
 *
 * <AUTHOR>
 * @date 2023-04-03 10:22
 */
@Slf4j
@RestController
@RequestMapping("/eap/project/dayuan/interf")
public class EapDyGetTagListInterfController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    //查詢數據
    @RequestMapping(value = "/GetTagListByClientGroupCode", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetTagListByClientGroupCode(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/dayuan/interf/GetTagListByClientGroupCode";
        String selectResult = "";
        String errorMsg = "";
        try {
            String client_code = jsonParas.getString("client_code");
            String tag_group_code = jsonParas.getString("tag_group_code");
            String sql = "select cli.client_code,gp.tag_group_code,tag.tag_code,tag.tag_des from scada_tag tag " +
                    " left join scada_tag_group gp on tag.tag_group_id = gp.tag_group_id" +
                    " left join scada_client cli on gp.client_id = cli.client_id" +
                    " where 1=1";
            if (!StringUtils.isEmpty(tag_group_code)) {
                StringBuilder sb = new StringBuilder();
                sb.append(" and gp.tag_group_code in (");
                String[] codeList = tag_group_code.split(",");
                for (int i = 0; i < codeList.length; i++) {
                    sb.append("'" + codeList[i] + "'");
                    if (i < codeList.length - 1) {
                        sb.append(",");
                    }
                }
                sb.append(")");
                sql += sb.toString();
            }
            if (!StringUtils.isEmpty(client_code)) {
                sql += " and cli.client_code = '" + client_code + "'";
            }
            sql += " order by tag.tag_id";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("ais",sql,false,request,apiRoutePath);
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        } catch (Exception ex) {
            errorMsg = "查詢Tag数据发生异常" + ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
