package com.api.eap.project.thailand.dycj;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 泰国定颖(持久)EAP接受事件信息定义接口
 * 1.CimMessage:接受EAP下发弹窗消息
 * 2.UserLoginRequest:被动接受EAP下发指令登入登出
 * 3.HeartBeatCheckDownLoad:上游设备检测下游设备是否在线
 * 4.BcOffLineLoginInDownLoad:上游通知下游离线登入
 * 5.AreYouThere:接受EAP询问是否在线
 * 6.CIMModeChangeCommand:EAP远程修改CimMode
 * 7.DateTimeCalibration:EAP请求校正时间
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/dycj/interf/recv")
public class EapDyCjRecvEventController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapDyCjRecvEventFunc eapDyCjRecvEventFunc;

    //1.接受EAP下发弹窗消息
    @RequestMapping(value = "/CimMessage",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvEventCimMessage(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/CimMessage";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjRecvEventFunc.CimMessage(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //2.被动接受EAP下发指令登入登出
    @RequestMapping(value = "/UserLoginRequest",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvEventUserLoginRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/UserLoginRequest";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjRecvEventFunc.UserLoginRequest(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //3.上游设备检测下游设备是否在线
    @RequestMapping(value = "/HeartBeatCheckDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvEventHeartBeatCheckDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/HeartBeatCheckDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjRecvEventFunc.HeartBeatCheckDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //4.上游通知下游离线登入
    @RequestMapping(value = "/BcOffLineLoginInDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvEventBcOffLineLoginInDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/BcOffLineLoginInDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjRecvEventFunc.BcOffLineLoginInDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //5.接受EAP询问是否在线
    @RequestMapping(value = "/AreYouThere",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvEventAreYouThere(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/AreYouThere";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjRecvEventFunc.AreYouThere(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //6.EAP远程修改CimMode
    @RequestMapping(value = "/CIMModeChangeCommand",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvCIMModeChangeCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/CIMModeChangeCommand";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjRecvEventFunc.CIMModeChangeCommand(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //7.EAP请求校正时间
    @RequestMapping(value = "/DateTimeCalibration",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvDateTimeCalibration(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/DateTimeCalibration";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapDyCjRecvEventFunc.DateTimeCalibration(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }
}
