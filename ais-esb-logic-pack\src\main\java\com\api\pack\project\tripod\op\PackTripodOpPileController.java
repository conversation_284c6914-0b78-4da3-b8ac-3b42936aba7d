package com.api.pack.project.tripod.op;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pack.core.plan.Plan;
import com.api.pack.core.plan.PlanService;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * Pile堆叠逻辑
 * 1.保存与判断堆叠
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-20
 */
@RestController
@Slf4j
@RequestMapping("/pack/project/tripod/op")
public class PackTripodOpPileController
{
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanService planService;

    //1.打包前校验PLC传入的SetSNList
    @RequestMapping(value = "/SetSNListCheck", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String PackCoreOpSetSNListCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/tripod/op/SetSNListCheck";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String meArrayTable = "a_pack_me_array";
        String mePileTable = "a_pack_me_pile";
        String apsPlanTable = "a_pack_aps_plan";
        String result = "";//model_type + "," + pile_ng_code + "," + pile_ng_msg + "," + pile_index + "," + pile_status + "," + plan_finish_flag

        String pileId = CFuncUtilsSystem.CreateUUID(true);
        int plcStackQtyValueInt = 0;
        String plcGrossWeightValue = "";
        String[] setSnArray = new String[]{};
        List<String> setSnList = new ArrayList<>();
        String sumSunValues = "";

        String user_name = "";
        String pile_barcode = "";
        int pile_index = 0;
        String pile_status = "NG";
        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
        String lot_num = "";
        String model_type = "";
        Integer unit_count = 0;//当前工作中订单单包set数量
        int xout_act_num = 0;//写入当前包内单SET板件最大的XOUT数量
        String batch_no = "";
//        Document plan = null;//当前工作中的订单
        Plan plan;
        int pile_ng_code = 1;//1:ok；2、数据重复；3、长度异常；4、PLC和PC数据校验异常；5、数据量异常；6：其他，
        String pile_ng_msg = "";
        String plan_finish_flag = "0";
        StringBuilder pc_array_list = new StringBuilder();
        try
        {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            pile_barcode = jsonParas.getString("pile_barcode");
            if (ObjectUtils.isEmpty(pile_barcode))
            {
                pile_barcode = CFuncUtilsSystem.GetOnlySign("PL");
            }
            sumSunValues = jsonParas.getString("sumSunValues");
            String plcFinishType = jsonParas.getString("plcFinishType");//完板类型，1正常完板，2强制完板，强制完板不需要进行板件数量校验
            String plcStackQtyValue = jsonParas.getString("plcStackQtyValue");//打包数量
            if (plcStackQtyValue != null && !plcStackQtyValue.isEmpty()) {
                plcStackQtyValueInt = Integer.parseInt(plcStackQtyValue);
            }
            plcGrossWeightValue = jsonParas.getString("plcGrossWeightValue");//包重

            BigDecimal plcGrossWeightValueDecimal = new BigDecimal(0);
            if (plcGrossWeightValue != null && !plcGrossWeightValue.equals(""))
            {
                plcGrossWeightValueDecimal = new BigDecimal(plcGrossWeightValue);
            }

            //0.获取当前工作订单信息
            plan = Plan.current(this.planService);
            if (plan != null)
            {
                unit_count = plan.getSetBoardUnitNumber();
                model_type = plan.getPartNumber();
                lot_num = plan.getLotNum();
                batch_no = plan.getLaserBatchNumber();
            }

            setSnArray = sumSunValues.split(",");
            setSnList = Arrays.asList(setSnArray);

            // 判断setSn集合是否为空
            if (setSnArray.length == 0)
            {
                pile_ng_code = 5;
//                pile_ng_msg = "传入SetSn集合为空";
                // 英化
                pile_ng_msg = "The incoming SetSn collection is empty";
                result = model_type + "," + pile_ng_code + "," + pile_ng_msg + "," + pile_index + "," + pile_status + "," +
                        plan_finish_flag + "," + batch_no + "," + xout_act_num;
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            // 判断PLC堆叠数量与实际数量是否一致 added by jay-y 2024/08/17
            if (plcStackQtyValueInt != setSnArray.length)
            {
                pile_ng_code = 5;
                pile_ng_msg = "PLC给出堆叠数量{" + plcStackQtyValue + "}与实际SetSn集合不一致";
            }

            //1.2.判断setSn数量是否一致
            if (plcFinishType.equals("1"))
            {
                if (plcStackQtyValueInt != unit_count)
                {
                    pile_ng_code = 5;
                    pile_ng_msg = "PLC给出堆叠数量{" + plcStackQtyValue + "}与设定SetSn集合不一致";
                }
            }

            //1.3.判断setSn是否在a_pack_me_array，并且状态是OK,并需要判定是否绑定pile_barcode，如果存在表示已经被打包，不能继续使用，需要先解绑
            String sqlSortCount = "select count(1) from a_pack_fmod_sort where enable_flag='Y' and sort_flag='Y' and sort_code='SetComparison'";
            int sortCount = cFuncDbSqlResolve.GetSelectCount(sqlSortCount);

            List<String> setSnListPC = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.addCriteria(Criteria.where("pile_use_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("unbind_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
            MongoCursor<Document> iterator = mongoTemplate.getCollection(meArrayTable).find(queryBigData.getQueryObject()).limit(plcStackQtyValueInt).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            while (iterator.hasNext())
            {
                Document arrayDoc = iterator.next();
                String board_sn = arrayDoc.getString("board_sn");
                setSnListPC.add(board_sn);
                if (pc_array_list.length() == 0)
                {
                    pc_array_list = new StringBuilder(board_sn);
                }
                else
                {
                    pc_array_list.append(",").append(board_sn);
                }
            }
            iterator.close();
            Set<String> set = new HashSet<>();
            for (String setSn : setSnArray)
            {
                if (setSn.equals("@NC@"))
                {
                    continue;
                }
                if (!set.add(setSn) && pile_ng_code == 1)
                {
                    pile_ng_code = 2;
                    pile_ng_msg = "PLC给出的堆叠SetSn{" + setSn + "}存在重复项";
                }
                if (setSn.length() != 14 && pile_ng_code == 1)
                {
                    pile_ng_code = 3;
                    pile_ng_msg = "PLC给出的堆叠SetSn{" + setSn + "}长度不为14位校验失败";
                }
                //开启校验
                if (sortCount > 0)
                {
                    if (!setSnListPC.contains(setSn) && pile_ng_code == 1)
                    {
                        pile_ng_code = 4;
                        pile_ng_msg = "PLC反馈SET数据和PC计划SET数据校验异常";
                    }
                }
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("board_sn").is(setSn));
                queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
                iterator = mongoTemplate.getCollection(meArrayTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (!iterator.hasNext())
                {
                    if (pile_ng_code == 1)
                    {
                        pile_ng_code = 3;
                        pile_ng_msg = "PLC给出的堆叠SetSN{" + setSn + "}未通过OK校验";
                    }
                }
                else
                {
                    Document arrayDoc = iterator.next();
                    String array_barcode = arrayDoc.getString("array_barcode");//对应set码
                    String pile_barcode_array = arrayDoc.getString("pile_barcode");//绑定的pile_barcode
                    int xout_act_num_set = arrayDoc.getInteger("xout_act_num");//xout_act_num
                    if (xout_act_num_set > xout_act_num)
                    {
                        xout_act_num = xout_act_num_set;
                    }
                    iterator.close();

                    if (pile_barcode_array != null && !pile_barcode_array.equals(""))
                    {
                        if (pile_ng_code == 1)
                        {
                            pile_ng_code = 2;
                            pile_ng_msg = "PLC给出的堆叠SetSN{" + setSn + "}已经被包{" + pile_barcode_array + "}绑定，不能重复使用";
                        }
                    }
                }
            }

            //2.获取当前用户信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext())
            {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //3.获取当前最大pile_index
            queryBigData = new Query();
            queryBigData.with(Sort.by(Sort.Direction.DESC, "pile_index"));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            iteratorBigData = mongoTemplate.getCollection(mePileTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext())
            {
                Document docItemBigData = iteratorBigData.next();
                pile_index = docItemBigData.getInteger("pile_index");
                iteratorBigData.close();
            }

            //4.保存pile信息，并更新array上pile_barcode信息
            if (pile_ng_code == 1)
            {
                //校验OK才做数据绑定
                for (String setSn : setSnArray)
                {
                    //修改array对应的pile_barcode和解绑状态为N
                    Query query = new Query();
                    query.addCriteria(Criteria.where("board_sn").is(setSn));
                    query.addCriteria(Criteria.where("enable_flag").is("Y"));
                    query.addCriteria(Criteria.where("pile_use_flag").is("N"));
                    query.addCriteria(Criteria.where("array_status").is("OK"));
                    Update updateBigData = new Update();
                    if (plan != null) updateBigData.set("plan_id", plan.getId());
                    updateBigData.set("pile_barcode", pile_barcode);
                    updateBigData.set("pile_use_flag", "Y");
                    mongoTemplate.updateMulti(query, updateBigData, meArrayTable);
                }
            }
            //新增pile数据
            List<Map<String, Object>> lstPileSets = new ArrayList<>();
            Map<String, Object> mapQDataItem = new HashMap<>();
            mapQDataItem.put("item_date", item_date);
            mapQDataItem.put("item_date_val", item_date_val);
            mapQDataItem.put("pile_id", CFuncUtilsSystem.CreateUUID(true));
            mapQDataItem.put("pile_barcode", pile_barcode);
            mapQDataItem.put("pile_weight", plcGrossWeightValueDecimal.doubleValue());
            mapQDataItem.put("custom_barcode", "");
            mapQDataItem.put("lot_num", lot_num);
            mapQDataItem.put("model_type", model_type);
            mapQDataItem.put("pile_index", pile_index + 1);
            mapQDataItem.put("array_count", setSnArray.length);
            mapQDataItem.put("pc_array_list", pc_array_list.toString());
            mapQDataItem.put("plc_array_list", sumSunValues);
            mapQDataItem.put("trunk_flag", "N");
            mapQDataItem.put("pile_user", user_name);
            if (pile_ng_code == 1) pile_status = "OK";
            mapQDataItem.put("pile_status", pile_status);
            mapQDataItem.put("pile_ng_code", pile_ng_code);
            mapQDataItem.put("pile_ng_msg", pile_ng_msg);
            if (plan != null) mapQDataItem.put("plan_id", plan.getId());
            mapQDataItem.put("enable_flag", "Y");
            mapQDataItem.put("unbind_flag", "N");
            mapQDataItem.put("unbind_user", "");
            mapQDataItem.put("unbind_time", "");
            mapQDataItem.put("unbind_way", "");
            lstPileSets.add(mapQDataItem);
            if (lstPileSets.size() > 0)
            {
                mongoTemplate.insert(lstPileSets, mePileTable);
            }
            //5.修改任务信息中的 获取所有ok与ng的pile信息 finish_pile_count 完成打包数量 finish_lable_count 打包次数
            Query lotNumQuery = new Query().addCriteria(Criteria.where("lot_num").is(lot_num));
            List<Document> piles = mongoTemplate.find(lotNumQuery, Document.class, mePileTable);
            Update updateBigData = null;
            if (!CollectionUtils.isEmpty(piles) && plan != null)
            {
                updateBigData = new Update();
                //完成打包次数
                Long count = piles.stream().filter(f -> f.containsKey("pile_status") && f.get("pile_status").equals("OK")).count();
                //完成打包数量
                Long sum = piles.stream().mapToLong(m -> {
                    if (m.containsKey("array_count") && m.containsKey("pile_status") && m.get("pile_status").equals("OK"))
                    {
                        return Long.parseLong(m.get("array_count").toString());
                    }
                    return 0;
                }).sum();

                updateBigData.set("finish_pile_count", sum);
                updateBigData.set("finish_lable_count", count);
                //6.若是发现完成打包数量等于计划数量则将任务状态变更为 finish
                if (plan.getSetBoardUnitNumber() != null // plan.containsKey("unit_count")
                        && plan.getFinishPileCount() != null // && plan.containsKey("finish_pile_count")
                        && plan.getSetBoardPlanNumber() != null // && plan.containsKey("plan_lot_count")
                        && plan.getTaskStartTime() != null // && plan.containsKey("task_start_time")
                )
                {
                    String task_start_time = plan.getTaskStartTime();
                    Integer plan_lot_count = plan.getSetBoardPlanNumber();
                    Integer lable_count = plan_lot_count / unit_count;
                    lable_count = (plan_lot_count % unit_count) != 0 ? lable_count + 1 : lable_count;
                    if (lable_count.equals(count.intValue()))
                    {
                        updateBigData.set("lot_status", "FINISH");
                        String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                        updateBigData.set("task_end_time", task_end_time);
                        long cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                        updateBigData.set("task_cost_time", String.valueOf(cost_time));
                        plan_finish_flag = "1";
                    }
                }
            }
            if (updateBigData != null)
            {
                Query query = new Query();
                query.addCriteria(Criteria.where("lot_num").is(lot_num));
                query.addCriteria(Criteria.where("lot_status").is("WORK"));
                mongoTemplate.updateFirst(query, updateBigData, apsPlanTable);
            }
            result = model_type + "," + pile_ng_code + "," + pile_ng_msg + "," + pile_index + "," + pile_status + "," + plan_finish_flag + "," + batch_no + "," + xout_act_num + "," + pile_barcode;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        }
        catch (Exception ex)
        {
            errorMsg = "打包前校验PLC传入的SetSNList发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
