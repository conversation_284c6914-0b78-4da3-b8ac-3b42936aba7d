package com.api.pack.core.sort;


import lombok.EqualsAndHashCode;
import org.springframework.context.ApplicationEvent;

@EqualsAndHashCode(callSuper = true)
public class SortCompareResultEvent extends ApplicationEvent
{
    public SortCompareResultEvent(Object source)
    {
        super(source);
    }

    @Override
    public SortCompareResult getSource()
    {
        return (SortCompareResult) super.getSource();
    }
}
