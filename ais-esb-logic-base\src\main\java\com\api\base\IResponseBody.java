package com.api.base;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class IResponseBody
{
    private int code;

    private long count;

    private Object data;

    private String result;

    private String msg;

    public static IResponseBody ok(Object data, long count, String result, String message)
    {
        return new IResponseBody(Const.CODE_OK, count, data, result, message);
    }

    public static IResponseBody ok(Object data, long count, String message)
    {
        return new IResponseBody(Const.CODE_OK, count, data, Const.BLANK, message);
    }

    public static IResponseBody ok(Object data, String message)
    {
        if (data instanceof Collection)
        {
            return ok(data, ((Collection<?>) data).size(), message);
        }
        return ok(data, data != null ? 1 : 0, message);
    }

    public static IResponseBody ok(String result, String message)
    {
        return ok(null, 0, result, message);
    }

    public static IResponseBody ok(Object data, long count)
    {
        return ok(data, count, Const.OK);
    }

    public static IResponseBody ok(Object data)
    {
        return ok(data, Const.OK);
    }

    public static IResponseBody error(Object data, String result, String message)
    {
        if (data instanceof Collection)
        {
            return new IResponseBody(Const.CODE_ERROR, ((Collection<?>) data).size(), data, result, message);
        }
        return new IResponseBody(Const.CODE_ERROR, data != null ? 1 : 0, data, result, message);
    }

    public static IResponseBody error(Object data, String message)
    {
        return error(data, Const.BLANK, message);
    }

    public static IResponseBody error(String result, String message)
    {
        return error(null, result, message);
    }

    public static IResponseBody error(String message)
    {
        return error(null, message);
    }
}
