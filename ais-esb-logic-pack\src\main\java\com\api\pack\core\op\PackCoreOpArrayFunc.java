package com.api.pack.core.op;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 * Array方法
 * 1.获取Array与BD解析数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Service
public class PackCoreOpArrayFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //根据规则获取解析结果
    private JSONObject getSplitResult(String strName,String str,String ruleName,String rule) throws Exception{
        String split_result="";
        String split_error="";
        JSONObject jbResult=new JSONObject();
        jbResult.put("split_result","");
        jbResult.put("split_error","");
        if(rule.startsWith("Left") || rule.startsWith("Right")){
            String[] ruleParas=rule.split("\\|",-1);
            String direct=ruleParas[0];
            Integer start=Integer.parseInt(ruleParas[1]);
            Integer length=Integer.parseInt(ruleParas[2]);
            if(length<=0){
                split_error="{"+strName+"}根据规则名称{"+ruleName+"}规则方法{"+rule+"}截取字符失败:{截取设置长度"+length+"必须大于0}";
                jbResult.put("split_error",split_error);
                return jbResult;
            }
            if(direct.equals("Left")){
                if(str.length()<start+length){
                    split_error="{"+strName+"}根据规则名称{"+ruleName+"}规则方法{"+rule+"}截取字符失败:{字符截止位"+(start+length)+"超出界限}";
                    jbResult.put("split_error",split_error);
                    return jbResult;
                }
                split_result=str.substring(start,start+length);
                jbResult.put("split_result",split_result);
            }
            else {
                Integer start2=str.length()-start-1;
                if(start2<0){
                    split_error="{"+strName+"}根据规则名称{"+ruleName+"}规则方法{"+rule+"}截取字符失败:{起始位置"+start+"超出界限}";
                    jbResult.put("split_error",split_error);
                    return jbResult;
                }
                if(str.length()<start2+length){
                    split_error="{"+strName+"}根据规则名称{"+ruleName+"}规则方法{"+rule+"}截取字符失败:{字符截止位"+(start2+length)+"超出界限}";
                    jbResult.put("split_error",split_error);
                    return jbResult;
                }
                split_result=str.substring(start2,start2+length);
                jbResult.put("split_result",split_result);
            }
        }
        return jbResult;
    }

    //判断Level等级
    private Integer getLevelInt(String level) throws Exception{
        Integer levelInt=0;
        if(level.equals("A")) levelInt=1;
        else if(level.equals("B")) levelInt=2;
        else if(level.equals("C")) levelInt=3;
        else if(level.equals("D")) levelInt=4;
        else if(level.equals("E")) levelInt=5;
        else levelInt=6;
        return levelInt;
    }

    //获取用于存储的SET条码
    //完成分选条件：正反面码比对
    private JSONObject getSaveArrayBarCode(JSONObject jbSort, String array_type,
                                           String array_barcode_front,String array_barcode_back) throws Exception{
        String array_barcode="";
        String array_status="OK";
        Integer array_ng_code=0;
        String array_ng_msg="";
        String use_front_flag="Y";
        JSONObject jbResult=new JSONObject();
        if(array_type.equals("None")){//无码
            array_barcode="";
        }
        else if(array_type.equals("Single")){//单面
            if(!array_barcode_front.equals("@NC@") && !array_barcode_front.equals("@NULL@")){
                array_barcode=array_barcode_front;
            }
            else{
                if(!array_barcode_back.equals("@NC@") && !array_barcode_back.equals("@NULL@")){
                    array_barcode=array_barcode_back;
                    use_front_flag="N";
                }
            }
            if(array_barcode.equals("@NC@") || array_barcode.equals("@NULL@")){
                array_barcode="NoRead";
                array_status="NG";
                array_ng_code=-1;
                array_ng_msg="SET条码读取失败";
                use_front_flag="Y";
            }
        }
        else{//双面
            if(!array_barcode_front.equals("@NC@") && !array_barcode_front.equals("@NULL@")){
                array_barcode=array_barcode_front;
            }
            else{
                if(!array_barcode_back.equals("@NC@") && !array_barcode_back.equals("@NULL@")){
                    array_barcode=array_barcode_back;
                }
            }
            if(array_barcode.equals("@NC@") || array_barcode.equals("@NULL@")){
                array_barcode="NoRead";
                array_status="NG";
                array_ng_code=-1;
                array_ng_msg="SET条码读取失败";
            }
            else{
                if(!array_barcode_front.equals(array_barcode_back)){
                    //若正面和反面条码不一致,则选择正面条码作为存储
                    array_barcode=array_barcode_front;
                }
                if(jbSort.containsKey("FrontAndBackSort")){
                    if(!array_barcode_front.equals(array_barcode_back)){
                        array_status="NG";
                        array_ng_code=-2;
                        array_ng_msg="SET正面条码{"+array_barcode_front+"}与反面条码{"+array_barcode_back+"}不一致";
                    }
                }
            }
        }
        jbResult.put("array_barcode",array_barcode);
        jbResult.put("array_status",array_status);
        jbResult.put("array_ng_code",array_ng_code);
        jbResult.put("array_ng_msg",array_ng_msg);
        jbResult.put("use_front_flag",use_front_flag);//是否使用正面
        return jbResult;
    }

    //获取用于存储的SET等级
    //完成分选条件：SET等级判定
    private JSONObject getSaveArrayLevel(JSONObject jbSort, String array_type,String use_front_flag,
                                         String array_level_front,String array_level_back) throws Exception{
        String array_level="";
        String array_status="OK";
        Integer array_ng_code=0;
        String array_ng_msg="";
        JSONObject jbResult=new JSONObject();
        if(array_type.equals("None")){//无码
            array_level="";
        }
        else if(array_type.equals("Single")){//单面
            if(use_front_flag.equals("Y")) array_level=array_level_front;
            else array_level=array_level_back;
            Integer array_level_int=getLevelInt(array_level);
            if(array_level_int>=6) array_level="F";
        }
        else{
            if(use_front_flag.equals("Y")){
                array_level=array_level_front;
                Integer array_level_a=getLevelInt(array_level_front);
                if(array_level_a>=6) array_level="F";
                Integer array_level_b=getLevelInt(array_level_back);
                if(array_level_b>array_level_a){
                    array_level=array_level_back;
                    if(array_level_b>=6) array_level="F";
                }
            }
        }
        if(!array_level.equals("")){
            if(jbSort.containsKey("SetLevelSort")){
                String stand_array_level=jbSort.getString("SetLevelSort");
                Integer array_level_now=getLevelInt(array_level);
                Integer array_level_stand=getLevelInt(stand_array_level);
                if(array_level_now>array_level_stand){
                    array_status="NG";
                    array_ng_code=-3;
                    array_ng_msg="SET二维码等级{"+array_level+"}低于设定等级{"+stand_array_level+"}";
                }
            }
        }
        jbResult.put("array_level",array_level);
        jbResult.put("array_status",array_status);
        jbResult.put("array_ng_code",array_ng_code);
        jbResult.put("array_ng_msg",array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET光学点检测结果
    private JSONObject getSaveArrayMark(String array_type,String use_front_flag,
                                        String array_mark_front,String array_mark_back) throws Exception{
        String array_mark="";
        String array_status="OK";
        Integer array_ng_code=0;
        String array_ng_msg="";
        JSONObject jbResult=new JSONObject();
        if(array_type.equals("None")){//无码
            array_mark="";
        }
        else if(array_type.equals("Single")){//单面
            if(use_front_flag.equals("Y")) array_mark=array_mark_front;
            else array_mark=array_mark_back;
            if(array_mark.equals("@NC@")) array_mark="";
        }
        else{
            array_mark="";
            if(array_mark_front.equals("NG")) array_mark="NG";
            if(array_mark_back.equals("NG")) array_mark="NG";
            if(array_mark_front.equals("OK") && array_mark_back.equals("OK")) array_mark="OK";
        }
        if(array_mark.equals("NG")){
            array_status="NG";
            array_ng_code=-4;
            array_ng_msg="SET二维码光学点检测NG";
        }
        jbResult.put("array_mark",array_mark);
        jbResult.put("array_status",array_status);
        jbResult.put("array_ng_code",array_ng_code);
        jbResult.put("array_ng_msg",array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET的BD数量
    private JSONObject getSaveArrayBdCount(String bd_type,
                                           Integer array_bd_count_front,Integer array_bd_count_back) throws Exception{
        Integer array_bd_count=0;
        String array_status="OK";
        Integer array_ng_code=0;
        String array_ng_msg="";
        JSONObject jbResult=new JSONObject();
        if(array_bd_count_front>=array_bd_count_back) array_bd_count=array_bd_count_front;
        else array_bd_count=array_bd_count_back;
        if(bd_type.equals("Double")){
            if(array_bd_count_front!=array_bd_count_back){
                array_status="NG";
                array_ng_code=-5;
                array_ng_msg="正面PCS数量{"+array_bd_count_front+"}不等于反面PCS数量{"+array_bd_count_back+"}";
            }
        }
        if(!bd_type.equals("None")){
            if(array_bd_count<=0){
                array_status="NG";
                array_ng_code=-6;
                array_ng_msg="线扫反馈SET里面的PCS数量<=0";
            }
        }
        jbResult.put("array_bd_count",array_bd_count);
        jbResult.put("array_status",array_status);
        jbResult.put("array_ng_code",array_ng_code);
        jbResult.put("array_ng_msg",array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET的旋转方向
    private JSONObject getSaveArrayBoardTurn(String use_front_flag,
                                             Integer board_turn_front,Integer board_turn_back) throws Exception{
        Integer board_turn=2;
        String array_status="OK";
        Integer array_ng_code=0;
        String array_ng_msg="";
        JSONObject jbResult=new JSONObject();
        if(use_front_flag.equals("Y")){
            if(board_turn_front>0) board_turn=1;
        }
        else{
            if(board_turn_back>0) board_turn=1;
        }
        jbResult.put("board_turn",board_turn);
        jbResult.put("array_status",array_status);
        jbResult.put("array_ng_code",array_ng_code);
        jbResult.put("array_ng_msg",array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET的XOUT实际数量
    private JSONObject getSaveArrayXoutCount(String bd_type,String xout_flag,Integer xout_set_num,
                                             Integer xout_act_num_front,Integer xout_act_num_back) throws Exception{
        Integer xout_act_num=0;
        String array_status="OK";
        Integer array_ng_code=0;
        String array_ng_msg="";
        JSONObject jbResult=new JSONObject();
        if(xout_act_num_front>=xout_act_num_back) xout_act_num=xout_act_num_front;
        else xout_act_num=xout_act_num_back;
        if(xout_act_num>xout_set_num && xout_flag.equals("Y")){
            array_status="NG";
            array_ng_code=-7;
            array_ng_msg="X位实际数量{"+xout_act_num+"}大于设定X位数量{"+xout_set_num+"}";
        }
        if(bd_type.equals("Double")){
            if(xout_act_num_front!=xout_act_num_back && xout_flag.equals("Y")){
                array_status="NG";
                array_ng_code=-8;
                array_ng_msg="正面X位数量{"+xout_act_num_front+"}不等于反面X位数量{"+xout_act_num_back+"}";
            }
        }
        jbResult.put("xout_act_num",xout_act_num);
        jbResult.put("array_status",array_status);
        jbResult.put("array_ng_code",array_ng_code);
        jbResult.put("array_ng_msg",array_ng_msg);
        return jbResult;
    }

    //对Array合格性进行校验
    private JSONObject checkArrayResult(JSONObject jbRecipe,JSONObject jbSort,String array_type,
                                        Map<String, Object> mapRowArray) throws Exception{
        String meArrayTable="a_pack_me_array";
        String array_status="OK";
        Integer array_ng_code=0;
        String array_ng_msg="";
        JSONObject jbResult=new JSONObject();
        String array_barcode=mapRowArray.get("array_barcode").toString();
        String split_lot=mapRowArray.get("split_lot").toString();
        String split_model=mapRowArray.get("split_model").toString();
        //1.周期比对
        if(jbSort.containsKey("CyclePeriodSort") && !array_type.equals("None")){
            String cycle_period = String.valueOf(mapRowArray.get("cycle_period"));
            String array_cycle_split_rule=jbRecipe.getString("array_cycle_split_rule");
            JSONObject jbSplitResult=getSplitResult("SET条码",array_barcode,"SET截取周期",array_cycle_split_rule);
            String split_result=jbSplitResult.getString("split_result");
            String split_error=jbSplitResult.getString("split_error");
            if(!split_error.isEmpty()){
                jbResult.put("array_status","NG");
                jbResult.put("array_ng_code",-21);
                jbResult.put("array_ng_msg",split_error);
                return jbResult;
            }
            if(!split_result.isEmpty()){
                try{
                    // cycle_period正则替换非数字字符为空
                    cycle_period = cycle_period.replaceAll("[^0-9]", "");
                    int cycle_period_int = Integer.parseInt(cycle_period);
                    int cycle_period_set = Integer.parseInt(split_result);
                    if(cycle_period_set > cycle_period_int){
                        jbResult.put("array_status","NG");
                        jbResult.put("array_ng_code",-22);
                        jbResult.put("array_ng_msg","SET周期{"+cycle_period_set+"}超过设定周期{"+cycle_period+"}");
                        return jbResult;
                    }
                }
                catch (NumberFormatException nE){
                    jbResult.put("array_status","NG");
                    jbResult.put("array_ng_code",-23);
                    jbResult.put("array_ng_msg","截取SET周期{"+split_result+"}字符转换Int类型失败");
                    return jbResult;
                }
            }
        }
        //2.SET批次比对
        if(jbSort.containsKey("BatchNumSort") && !array_type.equals("None")){
            String array_lot_split_rule=jbRecipe.getString("array_lot_split_rule");
            JSONObject jbSplitResult=getSplitResult("SET条码",array_barcode,"SET截取批次",array_lot_split_rule);
            String split_result=jbSplitResult.getString("split_result");
            String split_error=jbSplitResult.getString("split_error");
            if(!split_error.equals("")){
                jbResult.put("array_status","NG");
                jbResult.put("array_ng_code",-24);
                jbResult.put("array_ng_msg",split_error);
                return jbResult;
            }
            if(!split_result.equals("")){
                if(split_lot.equals("")){
                    split_lot=split_result;
                    jbResult.put("split_lot",split_lot);
                }
                else{
                    if(!split_lot.equals(split_result)){
                        jbResult.put("array_status","NG");
                        jbResult.put("array_ng_code",-25);
                        jbResult.put("array_ng_msg","SET截取批次{"+split_result+"}与订单批次{"+split_lot+"}不同");
                        return jbResult;
                    }
                }
            }
        }
        //3.SET料号比对
        if(jbSort.containsKey("ModelSort") && !array_type.equals("None")){
            String array_model_split_rule=jbRecipe.getString("array_model_split_rule");
            JSONObject jbSplitResult=getSplitResult("SET条码",array_barcode,"SET截取料号",array_model_split_rule);
            String split_result=jbSplitResult.getString("split_result");
            String split_error=jbSplitResult.getString("split_error");
            if(!split_error.equals("")){
                jbResult.put("array_status","NG");
                jbResult.put("array_ng_code",-26);
                jbResult.put("array_ng_msg",split_error);
                return jbResult;
            }
            if(!split_result.equals("")){
                if(split_model.equals("")){
                    split_model=split_result;
                    jbResult.put("split_model",split_model);
                }
                else{
                    if(!split_model.equals(split_result)){
                        jbResult.put("array_status","NG");
                        jbResult.put("array_ng_code",-27);
                        jbResult.put("array_ng_msg","SET截取料号{"+split_result+"}与订单料号{"+split_model+"}不同");
                        return jbResult;
                    }
                }
            }
        }
        //4.条码长度比对
        if(jbSort.containsKey("BarLengthSort") && !array_type.equals("None")){
            Integer array_length=jbRecipe.getInteger("array_length");
            if(array_length>0){
                if(array_barcode.length()!=array_length){
                    jbResult.put("array_status","NG");
                    jbResult.put("array_ng_code",-28);
                    jbResult.put("array_ng_msg","SET条码长度{"+array_barcode.length()+"}与配方规定长度{"+array_length+"}不等");
                    return jbResult;
                }
            }
        }
        //5.条码大小写比对
        if(jbSort.containsKey("BarCaseSort") && !array_type.equals("None")){
            String array_case=jbRecipe.getString("array_case");
            if(array_case.equals("Upper") || array_case.equals("Lower")){
                if(array_case.equals("Upper")){
                    Boolean isAllUpper=array_barcode.matches("[A-Z\\d]+");
                    if(!isAllUpper){
                        jbResult.put("array_status","NG");
                        jbResult.put("array_ng_code",-29);
                        jbResult.put("array_ng_msg","SET条码{"+array_barcode+"}与配方要求全大写不符合");
                        return jbResult;
                    }
                }
                else{
                    Boolean isAllLower=array_barcode.matches("[a-z\\d]+");
                    if(!isAllLower){
                        jbResult.put("array_status","NG");
                        jbResult.put("array_ng_code",-30);
                        jbResult.put("array_ng_msg","SET条码{"+array_barcode+"}与配方要求全小写不符合");
                        return jbResult;
                    }
                }
            }
        }
        //6.SET重码对比
        if(jbSort.containsKey("MultiCodeSort") && !array_type.equals("None")){
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.addCriteria(Criteria.where("unbind_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
            queryBigData.addCriteria(Criteria.where("array_barcode").is(array_barcode));
            long arrayCount=mongoTemplate.getCollection(meArrayTable).countDocuments(queryBigData.getQueryObject());
            if(arrayCount>0){
                jbResult.put("array_status","NG");
                jbResult.put("array_ng_code",-31);
                jbResult.put("array_ng_msg","SET条码{"+array_barcode+"}重复,请先解绑SET条码");
                return jbResult;
            }
        }
        jbResult.put("array_status",array_status);
        jbResult.put("array_ng_code",array_ng_code);
        jbResult.put("array_ng_msg",array_ng_msg);
        return jbResult;
    }

    //获取PCS存储信息以及PCS影响SET分选解析
    private JSONObject checkBdResult(String bd_type,JSONObject jbRecipe,JSONObject jbSort,
                                     Map<String, Object> mapRowArray,
                                     JSONArray bd_list_front,JSONArray bd_list_back) throws Exception{
        String meBdTable="a_pack_me_bd";
        String array_status="OK";
        Integer array_ng_code=0;
        String array_ng_msg="";
        Boolean use_front_flag=true;
        String array_id=mapRowArray.get("array_id").toString();
        String array_barcode=mapRowArray.get("array_barcode").toString();
        String split_lot=mapRowArray.get("split_lot").toString();
        String split_model=mapRowArray.get("split_model").toString();
        String xout_flag=mapRowArray.get("xout_flag").toString();
        Integer pcs_level_int=0;
        String pcs_level="";
        if(jbSort.containsKey("PcsLevelSort")){
            pcs_level=jbSort.getString("PcsLevelSort");
            pcs_level_int=getLevelInt(pcs_level);
        }
        Boolean isCheckBdIndex=false;
        Integer bd_index_stand=0;
        Integer bd_index_start=jbRecipe.getInteger("bd_index_start");
        Integer bd_index_incre=jbRecipe.getInteger("bd_index_incre");
        if(jbSort.containsKey("PcsIndexSort")){
            if(bd_index_start>=0 && bd_index_incre>=1) isCheckBdIndex=true;
        }

        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
        JSONObject jbResult=new JSONObject();
        List<Map<String, Object>> bdRowsList=new ArrayList<>();//BD解析结果

        if(bd_list_front==null || bd_list_front.size()<=0){
            jbResult.put("array_status","NG");
            jbResult.put("array_ng_code",-51);
            jbResult.put("array_ng_msg","线扫返回正面PCS集合为空");
            return jbResult;
        }
        if(bd_list_back==null || bd_list_back.size()<=0){
            jbResult.put("array_status","NG");
            jbResult.put("array_ng_code",-52);
            jbResult.put("array_ng_msg","线扫返回反面PCS集合为空");
            return jbResult;
        }
        if(bd_list_front.size()!=bd_list_back.size()){
            jbResult.put("array_status","NG");
            jbResult.put("array_ng_code",-53);
            jbResult.put("array_ng_msg","线扫返回正面PCS集合数量{"+bd_list_front.size()+"}不等于反面PCS集合数量{"+bd_list_back.size()+"}");
            return jbResult;
        }
        //判断使用正面还是反面作为存储与判断依据
        if(bd_type.equals("Single")){//单面
            JSONObject jbPcsItemFrontFirst=bd_list_front.getJSONObject(0);
            String bd_barcode_front_first=jbPcsItemFrontFirst.getString("PcsQRC");
            if(bd_barcode_front_first.equals("@NC@")) use_front_flag=false;
        }
        JSONArray bd_list_front_last=bd_list_front;
        JSONArray bd_list_back_last=bd_list_back;
        if(!use_front_flag){
            bd_list_front_last=bd_list_back;
            bd_list_back_last=bd_list_front;
        }
        //循环对PCS数据进行判断
        List<String> lstPcsBarCode=new ArrayList<>();
        for(int i=0;i<bd_list_front_last.size();i++){
            JSONObject jbPcsItemFront=bd_list_front_last.getJSONObject(i);
            JSONObject jbPcsItemBack=bd_list_back_last.getJSONObject(i);
            String bd_id=CFuncUtilsSystem.CreateUUID(true);
            Integer bd_index=i+1;
            String bd_barcode="";
            String bd_level="";
            String bd_mark="";
            String xout_flag_bd="N";
            String bd_status="OK";
            Integer bd_ng_code=0;
            String bd_ng_msg="";
            if(isCheckBdIndex){
                if(i==0) bd_index_stand=bd_index_start;
                else bd_index_stand=bd_index_stand+bd_index_incre;
            }
            //正面信息
            String bd_barcode_front=jbPcsItemFront.getString("PcsQRC");
            String bd_level_front=jbPcsItemFront.getString("PcsQRCLevel");
            String bd_mark_front=jbPcsItemFront.getString("DirMarkChkRtl");
            Boolean xout_flag_front=jbPcsItemFront.getBoolean("IsXout");
            if(bd_barcode_front.equals("@NC@") || bd_barcode_front.equals("@NULL@") || bd_barcode_front.equals("")) bd_barcode_front="NoRead";
            if(bd_mark_front.equals("@NC@") || bd_mark_front.equals("")) bd_mark_front="";
            Integer bd_level_front_int=getLevelInt(bd_level_front);
            if(bd_level_front_int>=6) bd_level_front="F";
            //反面信息
            String bd_barcode_back=jbPcsItemBack.getString("PcsQRC");
            String bd_level_back=jbPcsItemBack.getString("PcsQRCLevel");
            String bd_mark_back=jbPcsItemBack.getString("DirMarkChkRtl");
            Boolean xout_flag_back=jbPcsItemBack.getBoolean("IsXout");
            if(bd_barcode_back.equals("@NC@") || bd_barcode_back.equals("@NULL@") || bd_barcode_back.equals("")) bd_barcode_back="NoRead";
            if(bd_mark_back.equals("@NC@") || bd_mark_back.equals("")) bd_mark_back="";
            Integer bd_level_back_int=getLevelInt(bd_level_back);
            if(bd_level_back_int>=6) bd_level_back="F";
            //1.正反面X位判断
            if(xout_flag_front || xout_flag_back) xout_flag_bd="Y";
            if(xout_flag.equals("Y") && xout_flag_bd.equals("Y")){
                if(xout_flag_front && !xout_flag_back){
                    if(array_status.equals("OK")){
                        array_status="NG";
                        array_ng_code=-54;
                        array_ng_msg="PCS序号{"+bd_index+"}正面画X,反面未画X";
                    }
                    if(bd_status.equals("OK")){
                        bd_status="NG";
                        bd_ng_code=-1;
                        bd_ng_msg="正面画X,反面未画X";
                    }
                }
                if(!xout_flag_front && xout_flag_back){
                    if(array_status.equals("OK")){
                        array_status="NG";
                        array_ng_code=-55;
                        array_ng_msg="PCS序号{"+bd_index+"}反面画X,正面未画X";
                    }
                    if(bd_status.equals("OK")){
                        bd_status="NG";
                        bd_ng_code=-2;
                        bd_ng_msg="反面画X,正面未画X";
                    }
                }
            }
            //2.条码逻辑判断
            if(bd_type.equals("Single")){//单面
                bd_barcode=bd_barcode_front;
                bd_mark=bd_mark_front;
                bd_level=bd_level_front;
            }
            if(bd_type.equals("Double")){
                bd_barcode="NoRead";
                if(!bd_barcode_front.equals("NoRead")) bd_barcode=bd_barcode_front;
                else{
                    if(!bd_barcode_back.equals("NoRead")) bd_barcode=bd_barcode_back;
                }
                if(jbSort.containsKey("FrontAndBackSort")){
                    if(!bd_barcode_front.equals(bd_barcode_back)){
                        if(array_status.equals("OK")){
                            array_status="NG";
                            array_ng_code=-56;
                            array_ng_msg="PCS序号{"+bd_index+"},正面PCS条码为{"+bd_barcode_front+"},反面PCS条码为{"+bd_barcode_back+"},二者不一致";
                        }
                        if(bd_status.equals("OK")){
                            bd_status="NG";
                            bd_ng_code=-3;
                            bd_ng_msg="正面PCS条码为{"+bd_barcode_front+"},反面PCS条码为{"+bd_barcode_back+"},二者不一致";
                        }
                    }
                }
                if(bd_mark_front.equals("NG") || bd_mark_back.equals("NG")) bd_mark="NG";
                if(bd_mark_front.equals("OK") && bd_mark_back.equals("OK")) bd_mark="OK";
                bd_level=bd_level_front;
                if(bd_level_back_int>bd_level_front_int) bd_level=bd_level_back;
            }
            if(!bd_type.equals("None")){
                if(!xout_flag_bd.equals("Y") && bd_status.equals("OK")){
                    if(bd_mark.equals("NG")){
                        if(array_status.equals("OK")){
                            array_status="NG";
                            array_ng_code=-57;
                            array_ng_msg="PCS序号{"+bd_index+"},检测光学点为NG";
                        }
                        if(bd_status.equals("OK")){
                            bd_status="NG";
                            bd_ng_code=-4;
                            bd_ng_msg="检测光学点为NG";
                        }
                    }
                    if(pcs_level_int>0 && bd_status.equals("OK")){
                        Integer bd_level_int=getLevelInt(bd_level);
                        if(bd_level_int>pcs_level_int){
                            if(array_status.equals("OK")){
                                array_status="NG";
                                array_ng_code=-58;
                                array_ng_msg="PCS序号{"+bd_index+"},PCS二维码等级{"+bd_level+"}大于设定等级{"+pcs_level+"}";
                            }
                            if(bd_status.equals("OK")){
                                bd_status="NG";
                                bd_ng_code=-5;
                                bd_ng_msg="PCS二维码等级{"+bd_level+"}大于设定等级{"+pcs_level+"}";
                            }
                        }
                    }
                    if(bd_barcode.equals("NoRead")){
                        if(array_status.equals("OK")){
                            array_status="NG";
                            array_ng_code=-59;
                            array_ng_msg="PCS序号{"+bd_index+"},PCS条码读取失败";
                        }
                        if(bd_status.equals("OK")){
                            bd_status="NG";
                            bd_ng_code=-6;
                            bd_ng_msg="条码读取失败";
                        }
                    }
                    else{
                        //验证重码
                        if(jbSort.containsKey("MultiCodeSort")){
                            if(lstPcsBarCode.contains(bd_barcode)){
                                if(array_status.equals("OK")){
                                    array_status="NG";
                                    array_ng_code=-60;
                                    array_ng_msg="PCS序号{"+bd_index+"},当前SET中存在相同的PCS条码{"+bd_barcode+"}";
                                }
                                if(bd_status.equals("OK")){
                                    bd_status="NG";
                                    bd_ng_code=-7;
                                    bd_ng_msg="当前SET中存在相同的PCS条码{"+bd_barcode+"}";
                                }
                            }
                            else lstPcsBarCode.add(bd_barcode);
                            if(bd_status.equals("OK")){
                                Query queryBigData = new Query();
                                queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                                queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
                                queryBigData.addCriteria(Criteria.where("bd_barcode").is(bd_barcode));
                                long arrayCount=mongoTemplate.getCollection(meBdTable).countDocuments(queryBigData.getQueryObject());
                                if(arrayCount>0){
                                    if(array_status.equals("OK")){
                                        array_status="NG";
                                        array_ng_code=-61;
                                        array_ng_msg="PCS序号{"+bd_index+"},PCS条码{"+bd_barcode+"}重码,请先解绑";
                                    }
                                    if(bd_status.equals("OK")){
                                        bd_status="NG";
                                        bd_ng_code=-8;
                                        bd_ng_msg="PCS条码{"+bd_barcode+"}重码,请先解绑";
                                    }
                                }
                            }
                        }
                        //批次比对
                        if(jbSort.containsKey("BatchNumSort") && !split_lot.equals("") && bd_status.equals("OK")){
                            String bd_lot_split_rule=jbRecipe.getString("bd_lot_split_rule");
                            JSONObject jbSplitResult=getSplitResult("PCS条码",bd_barcode,"PCS截取批次",bd_lot_split_rule);
                            String split_result=jbSplitResult.getString("split_result");
                            String split_error=jbSplitResult.getString("split_error");
                            if(!split_error.equals("")){
                                if(array_status.equals("OK")){
                                    array_status="NG";
                                    array_ng_code=-62;
                                    array_ng_msg="PCS序号{"+bd_index+"},"+split_error;
                                }
                                if(bd_status.equals("OK")){
                                    bd_status="NG";
                                    bd_ng_code=-9;
                                    bd_ng_msg=split_error;
                                }
                            }
                            else{
                                if(!split_result.equals("")){
                                    if(!split_result.equals(split_lot)){
                                        if(array_status.equals("OK")){
                                            array_status="NG";
                                            array_ng_code=-63;
                                            array_ng_msg="PCS序号{"+bd_index+"},PCS条码{"+bd_barcode+"}截取批次{"+split_result+"}不等于比对批次{"+split_lot+"}";
                                        }
                                        if(bd_status.equals("OK")){
                                            bd_status="NG";
                                            bd_ng_code=-10;
                                            bd_ng_msg="PCS条码{"+bd_barcode+"}截取批次{"+split_result+"}不等于比对批次{"+split_lot+"}";
                                        }
                                    }
                                }
                            }
                        }
                        //比对料号
                        if(jbSort.containsKey("ModelSort") && !split_model.equals("") && bd_status.equals("OK")){
                            String bd_model_split_rule=jbRecipe.getString("bd_model_split_rule");
                            JSONObject jbSplitResult=getSplitResult("PCS条码",bd_barcode,"PCS截取料号",bd_model_split_rule);
                            String split_result=jbSplitResult.getString("split_result");
                            String split_error=jbSplitResult.getString("split_error");
                            if(!split_error.equals("")){
                                if(array_status.equals("OK")){
                                    array_status="NG";
                                    array_ng_code=-64;
                                    array_ng_msg="PCS序号{"+bd_index+"},"+split_error;
                                }
                                if(bd_status.equals("OK")){
                                    bd_status="NG";
                                    bd_ng_code=-11;
                                    bd_ng_msg=split_error;
                                }
                            }
                            else{
                                if(!split_result.equals("")){
                                    if(!split_result.equals(split_model)){
                                        if(array_status.equals("OK")){
                                            array_status="NG";
                                            array_ng_code=-65;
                                            array_ng_msg="PCS序号{"+bd_index+"},PCS条码{"+bd_barcode+"}截取料号{"+split_result+"}不等于比对料号{"+split_model+"}";
                                        }
                                        if(bd_status.equals("OK")){
                                            bd_status="NG";
                                            bd_ng_code=-12;
                                            bd_ng_msg="PCS条码{"+bd_barcode+"}截取料号{"+split_result+"}不等于比对料号{"+split_model+"}";
                                        }
                                    }
                                }
                            }
                        }
                        //条码长度比对
                        if(jbSort.containsKey("BarLengthSort") && bd_status.equals("OK")){
                            Integer bd_length=jbRecipe.getInteger("bd_length");
                            if(bd_length>0){
                                if(bd_barcode.length()!=bd_length){
                                    if(array_status.equals("OK")){
                                        array_status="NG";
                                        array_ng_code=-66;
                                        array_ng_msg="PCS序号{"+bd_index+"},PCS条码{"+bd_barcode+"}条码长度{"+bd_barcode.length()+"}不等于设定长度{"+bd_length+"}";
                                    }
                                    if(bd_status.equals("OK")){
                                        bd_status="NG";
                                        bd_ng_code=-13;
                                        bd_ng_msg="PCS条码{"+bd_barcode+"}条码长度{"+bd_barcode.length()+"}不等于设定长度{"+bd_length+"}";
                                    }
                                }
                            }
                        }
                        //条码大小比对
                        if(jbSort.containsKey("BarCaseSort") && bd_status.equals("OK")){
                            String bd_case=jbRecipe.getString("bd_case");
                            if(bd_case.equals("Upper") || bd_case.equals("Lower")){
                                if(bd_case.equals("Upper")){
                                    Boolean isAllUpper=bd_barcode.matches("[A-Z\\d]+");
                                    if(!isAllUpper){
                                        if(array_status.equals("OK")){
                                            array_status="NG";
                                            array_ng_code=-67;
                                            array_ng_msg="PCS序号{"+bd_index+"},PCS条码{"+bd_barcode+"}不全为大写";
                                        }
                                        if(bd_status.equals("OK")){
                                            bd_status="NG";
                                            bd_ng_code=-14;
                                            bd_ng_msg="PCS条码{"+bd_barcode+"}不全为大写";
                                        }
                                    }
                                }
                                else{
                                    Boolean isAllLower=bd_barcode.matches("[a-z\\d]+");
                                    if(!isAllLower){
                                        if(array_status.equals("OK")){
                                            array_status="NG";
                                            array_ng_code=-68;
                                            array_ng_msg="PCS序号{"+bd_index+"},PCS条码{"+bd_barcode+"}不全为小写";
                                        }
                                        if(bd_status.equals("OK")){
                                            bd_status="NG";
                                            bd_ng_code=-15;
                                            bd_ng_msg="PCS条码{"+bd_barcode+"}不全为小写";
                                        }
                                    }
                                }
                            }
                        }
                        //PCS序号比对
                        if(isCheckBdIndex){
                            String bd_index_split_rule=jbRecipe.getString("bd_index_split_rule");
                            JSONObject jbSplitResult=getSplitResult("PCS条码",bd_barcode,"PCS截取序号",bd_index_split_rule);
                            String split_result=jbSplitResult.getString("split_result");
                            String split_error=jbSplitResult.getString("split_error");
                            if(!split_error.equals("")){
                                if(array_status.equals("OK")){
                                    array_status="NG";
                                    array_ng_code=-69;
                                    array_ng_msg="PCS序号{"+bd_index+"},"+split_error;
                                }
                                if(bd_status.equals("OK")){
                                    bd_status="NG";
                                    bd_ng_code=-16;
                                    bd_ng_msg=split_error;
                                }
                            }
                            else{
                                if(!split_result.equals("")){
                                    try{
                                        Integer bd_index_now=Integer.parseInt(split_result);
                                        if(bd_index_now!=bd_index_stand){
                                            if(array_status.equals("OK")){
                                                array_status="NG";
                                                array_ng_code=-70;
                                                array_ng_msg="PCS序号{"+bd_index+"},PCS条码{"+bd_barcode+"}截取序号{"+bd_index_now+"}不等于要求序号{"+bd_index_stand+"}";
                                            }
                                            if(bd_status.equals("OK")){
                                                bd_status="NG";
                                                bd_ng_code=-17;
                                                bd_ng_msg="PCS条码{"+bd_barcode+"}截取序号{"+bd_index_now+"}不等于要求序号{"+bd_index_stand+"}";
                                            }
                                        }
                                    }
                                    catch (Exception convError){
                                        if(array_status.equals("OK")){
                                            array_status="NG";
                                            array_ng_code=-71;
                                            array_ng_msg="PCS序号{"+bd_index+"},PCS条码{"+bd_barcode+"}截取序号{"+split_result+"}转换Int类型失败";
                                        }
                                        if(bd_status.equals("OK")){
                                            bd_status="NG";
                                            bd_ng_code=-18;
                                            bd_ng_msg="PCS条码{"+bd_barcode+"}截取序号{"+split_result+"}转换Int类型失败";
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //创建保存集合
            Map<String, Object> mapBdItem=new HashMap<>();
            mapBdItem.put("item_date",item_date);
            mapBdItem.put("item_date_val",item_date_val);
            mapBdItem.put("bd_id",bd_id);
            mapBdItem.put("array_id",array_id);
            mapBdItem.put("array_barcode",array_barcode);
            mapBdItem.put("array_status","");
            mapBdItem.put("bd_barcode",bd_barcode);
            mapBdItem.put("bd_index",bd_index);
            mapBdItem.put("bd_level",bd_level);
            mapBdItem.put("bd_mark",bd_mark);
            mapBdItem.put("xout_flag",xout_flag_bd);
            mapBdItem.put("bd_status",bd_status);
            mapBdItem.put("bd_ng_code",bd_ng_code);
            mapBdItem.put("bd_ng_msg",bd_ng_msg);
            mapBdItem.put("enable_flag","Y");
            bdRowsList.add(mapBdItem);
        }
        jbResult.put("array_status",array_status);
        jbResult.put("array_ng_code",array_ng_code);
        jbResult.put("array_ng_msg",array_ng_msg);
        jbResult.put("bd",bdRowsList);
        return jbResult;
    }

    //3.获取Array与BD解析数据
    public JSONObject ResolveCcdResult(String user_name,JSONObject jbPlan,JSONObject jbRecipe,
                                       JSONObject jbSort,JSONObject ccd_data) throws Exception{
        JSONObject jbCcdResolveResult=new JSONObject();
        Map<String, Object> mapRowArray=new HashMap<>();//Array解析结果
        List<Map<String, Object>> bdRowsList=new ArrayList<>();//BD解析结果

        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
        String array_id=CFuncUtilsSystem.CreateUUID(true);
        String lot_num=jbPlan.getString("lot_num");
        String model_type=jbPlan.getString("model_type");
        String array_type=jbPlan.getString("array_type");
        String bd_type=jbPlan.getString("bd_type");
        String array_status="OK";
        String modelVersion = String.valueOf(jbPlan.get("model_version"));
        mapRowArray.put("item_date",item_date);
        mapRowArray.put("item_date_val",item_date_val);
        mapRowArray.put("array_id",array_id);
        mapRowArray.put("pile_barcode","");
        mapRowArray.put("array_barcode","");
        mapRowArray.put("lot_num",lot_num);
        mapRowArray.put("array_index",jbPlan.getInteger("finish_ok_count")+1);
        mapRowArray.put("board_sn","");
        mapRowArray.put("array_level","");
        mapRowArray.put("array_mark","");
        mapRowArray.put("array_bd_count",0);
        mapRowArray.put("board_result",0);
        mapRowArray.put("board_turn",2);
        mapRowArray.put("deposit_position",0);
        mapRowArray.put("xout_flag","N");
        mapRowArray.put("xout_set_num",0);
        mapRowArray.put("xout_act_num",0);
        mapRowArray.put("array_status",array_status);
        mapRowArray.put("array_ng_code",0);
        mapRowArray.put("array_ng_msg","");
        mapRowArray.put("array_front_info","");
        mapRowArray.put("array_back_info","");
        mapRowArray.put("user_name",user_name);
        mapRowArray.put("task_type",jbPlan.getString("task_type"));
        mapRowArray.put("model_type",model_type);
        mapRowArray.put("model_version",modelVersion);
        mapRowArray.put("array_type",array_type);
        mapRowArray.put("bd_type",bd_type);
        mapRowArray.put("m_length",jbPlan.getDouble("m_length"));
        mapRowArray.put("m_width",jbPlan.getDouble("m_width"));
        mapRowArray.put("m_tickness",jbPlan.getDouble("m_tickness"));
        mapRowArray.put("m_weight",jbPlan.getDouble("m_weight"));
        mapRowArray.put("cycle_period",jbPlan.getString("cycle_period"));
        mapRowArray.put("split_lot","");
        mapRowArray.put("split_model","");
        mapRowArray.put("up_flag","N");
        mapRowArray.put("up_ng_code",0);
        mapRowArray.put("up_ng_msg","");
        mapRowArray.put("pile_use_flag","N");
        mapRowArray.put("enable_flag","Y");
        mapRowArray.put("unbind_flag","N");
        mapRowArray.put("unbind_user","");
        mapRowArray.put("unbind_time","");
        mapRowArray.put("unbind_way","");

        //1.获取任务截取批次与料号信息
        String split_lot="";
        String split_model="";
        String order_lot_split_rule=jbRecipe.getString("order_lot_split_rule");
        String custom_model_split_rule=jbRecipe.getString("custom_model_split_rule");
        //订单号截取批次规则
        JSONObject jbSplitResult=getSplitResult("订单号",lot_num,"订单号截取批次",order_lot_split_rule);
        String split_result=jbSplitResult.getString("split_result");
        String split_error=jbSplitResult.getString("split_error");
        if(!split_error.equals("")){
            array_status="NG";
            mapRowArray.put("array_status",array_status);
            mapRowArray.put("array_ng_code",-101);
            mapRowArray.put("array_ng_msg",split_error);
        }
        else{
            if(!split_result.equals("")) split_lot=split_result;
        }
        //客户料号截取料号规则
        jbSplitResult=getSplitResult("料号",model_type,"料号截取批次",custom_model_split_rule);
        split_result=jbSplitResult.getString("split_result");
        split_error=jbSplitResult.getString("split_error");
        if(!split_error.equals("")){
            if(array_status.equals("OK")){
                array_status="NG";
                mapRowArray.put("array_status",array_status);
                mapRowArray.put("array_ng_code",-102);
                mapRowArray.put("array_ng_msg",split_error);
            }
        }
        else{
            if(!split_result.equals("")) split_model=split_result;
        }
        mapRowArray.put("split_lot",split_lot);
        mapRowArray.put("split_model",split_model);

        //2.判断当前设置为几X
        String xout_flag="N";
        Integer xout_set_num=0;
        if(jbSort.containsKey("XoutSort")){
            xout_set_num=Integer.parseInt(jbSort.getString("XoutSort").replaceAll("X",""));
            if(xout_set_num<0) xout_set_num=0;
            if(xout_set_num>=0) xout_flag="Y";
        }
        mapRowArray.put("xout_flag",xout_flag);
        mapRowArray.put("xout_set_num",xout_set_num);

        //3.解析CCD数据,目前架构为一次一片,正反面都存在数据
        String board_sn="";
        String array_front_info="";
        String array_back_info="";
        JSONArray jaCcdData=ccd_data.getJSONArray("Content");
        JSONObject jbFirst=jaCcdData.getJSONObject(0);
        board_sn=jbFirst.getString("TransmitID");//线扫流水号
        JSONObject SetFrontContent=jbFirst.getJSONObject("SetFrontContent");//正面
        JSONObject SetBackContent=jbFirst.getJSONObject("SetBackContent");//反面
        array_front_info=SetFrontContent.toString();
        array_back_info=SetBackContent.toString();
        mapRowArray.put("board_sn",board_sn);
        mapRowArray.put("array_front_info",array_front_info);
        mapRowArray.put("array_back_info",array_back_info);

        //结果数据判断
        String array_barcode="";
        String array_level="";
        String array_mark="";
        Integer array_bd_count=0;
        Integer board_turn=2;
        Integer xout_act_num=0;
        Integer board_result=0;
        Integer deposit_position=0;
        //正面数据
        String array_barcode_front=SetFrontContent.getString("SetQRC");
        String array_level_front=SetFrontContent.getString("SetQRCLevel");
        String array_mark_front=SetFrontContent.getString("DirMarkChkRtl");
        Integer array_bd_count_front=SetFrontContent.getInteger("LayoutQty");
        Integer board_turn_front=SetFrontContent.getInteger("Direction");
        Integer xout_act_num_front=SetFrontContent.getInteger("XoutQty");
        JSONArray bd_list_front=SetFrontContent.getJSONArray("PcsMsgList");
        //反面数据
        String array_barcode_back=SetBackContent.getString("SetQRC");
        String array_level_back=SetBackContent.getString("SetQRCLevel");
        String array_mark_back=SetBackContent.getString("DirMarkChkRtl");
        Integer array_bd_count_back=SetBackContent.getInteger("LayoutQty");
        Integer board_turn_back=SetBackContent.getInteger("Direction");
        Integer xout_act_num_back=SetBackContent.getInteger("XoutQty");
        JSONArray bd_list_back=SetBackContent.getJSONArray("PcsMsgList");

        //4.先获取基本的存储数据,需要进行双面比对然后选择合适的数据存储
        //4.1 获取用于存储的SET条码
        JSONObject jbResolveResult=getSaveArrayBarCode(jbSort,array_type,array_barcode_front,array_barcode_back);
        String use_front_flag=jbResolveResult.getString("use_front_flag");//使用正面Y,使用反面N
        array_barcode=jbResolveResult.getString("array_barcode");
        String array_status_result=jbResolveResult.getString("array_status");
        Integer array_ng_code_result=jbResolveResult.getInteger("array_ng_code");
        String array_ng_msg_result=jbResolveResult.getString("array_ng_msg");
        if(array_status_result.equals("NG") && array_status.equals("OK")){
            array_status=array_status_result;
            mapRowArray.put("array_status",array_status_result);
            mapRowArray.put("array_ng_code",array_ng_code_result);
            mapRowArray.put("array_ng_msg",array_ng_msg_result);
        }
        //4.2 获取用于存储的SET等级
        jbResolveResult=getSaveArrayLevel(jbSort,array_type,use_front_flag,array_level_front,array_level_back);
        array_level=jbResolveResult.getString("array_level");
        array_status_result=jbResolveResult.getString("array_status");
        array_ng_code_result=jbResolveResult.getInteger("array_ng_code");
        array_ng_msg_result=jbResolveResult.getString("array_ng_msg");
        if(array_status_result.equals("NG") && array_status.equals("OK")){
            array_status=array_status_result;
            mapRowArray.put("array_status",array_status_result);
            mapRowArray.put("array_ng_code",array_ng_code_result);
            mapRowArray.put("array_ng_msg",array_ng_msg_result);
        }
        //4.3 获取用于存储的SET光学点检测结果
        jbResolveResult=getSaveArrayMark(array_type,use_front_flag,array_mark_front,array_mark_back);
        array_mark=jbResolveResult.getString("array_mark");
        array_status_result=jbResolveResult.getString("array_status");
        array_ng_code_result=jbResolveResult.getInteger("array_ng_code");
        array_ng_msg_result=jbResolveResult.getString("array_ng_msg");
        if(array_status_result.equals("NG") && array_status.equals("OK")){
            array_status=array_status_result;
            mapRowArray.put("array_status",array_status_result);
            mapRowArray.put("array_ng_code",array_ng_code_result);
            mapRowArray.put("array_ng_msg",array_ng_msg_result);
        }
        //4.4 获取用于存储的SET的BD数量
        jbResolveResult=getSaveArrayBdCount(bd_type,array_bd_count_front,array_bd_count_back);
        array_bd_count=jbResolveResult.getInteger("array_bd_count");
        array_status_result=jbResolveResult.getString("array_status");
        array_ng_code_result=jbResolveResult.getInteger("array_ng_code");
        array_ng_msg_result=jbResolveResult.getString("array_ng_msg");
        if(array_status_result.equals("NG") && array_status.equals("OK")){
            array_status=array_status_result;
            mapRowArray.put("array_status",array_status_result);
            mapRowArray.put("array_ng_code",array_ng_code_result);
            mapRowArray.put("array_ng_msg",array_ng_msg_result);
        }
        //4.5 获取用于存储的SET的旋转方向
        jbResolveResult=getSaveArrayBoardTurn(use_front_flag,board_turn_front,board_turn_back);
        board_turn=jbResolveResult.getInteger("board_turn");
        array_status_result=jbResolveResult.getString("array_status");
        array_ng_code_result=jbResolveResult.getInteger("array_ng_code");
        array_ng_msg_result=jbResolveResult.getString("array_ng_msg");
        if(array_status_result.equals("NG") && array_status.equals("OK")){
            array_status=array_status_result;
            mapRowArray.put("array_status",array_status_result);
            mapRowArray.put("array_ng_code",array_ng_code_result);
            mapRowArray.put("array_ng_msg",array_ng_msg_result);
        }
        //4.6 获取用于存储的SET的画X数量
        jbResolveResult=getSaveArrayXoutCount(bd_type,xout_flag,xout_set_num,xout_act_num_front,xout_act_num_back);
        xout_act_num=jbResolveResult.getInteger("xout_act_num");
        array_status_result=jbResolveResult.getString("array_status");
        array_ng_code_result=jbResolveResult.getInteger("array_ng_code");
        array_ng_msg_result=jbResolveResult.getString("array_ng_msg");
        if(array_status_result.equals("NG") && array_status.equals("OK")){
            array_status=array_status_result;
            mapRowArray.put("array_status",array_status_result);
            mapRowArray.put("array_ng_code",array_ng_code_result);
            mapRowArray.put("array_ng_msg",array_ng_msg_result);
        }
        //Array基本存储
        mapRowArray.put("array_barcode",array_barcode);
        mapRowArray.put("array_level",array_level);
        mapRowArray.put("array_mark",array_mark);
        mapRowArray.put("array_bd_count",array_bd_count);
        mapRowArray.put("board_turn",board_turn);
        mapRowArray.put("xout_act_num",xout_act_num);

        //5.针对SET条码进行分选条件解析
        Boolean isMultiCode=false;//代表是否重码
        if(array_status.equals("OK")){
            jbResolveResult=checkArrayResult(jbRecipe,jbSort,array_type,mapRowArray);
            array_status_result=jbResolveResult.getString("array_status");
            array_ng_code_result=jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result=jbResolveResult.getString("array_ng_msg");
            if(array_status_result.equals("NG")){
                array_status=array_status_result;
                mapRowArray.put("array_status",array_status_result);
                mapRowArray.put("array_ng_code",array_ng_code_result);
                mapRowArray.put("array_ng_msg",array_ng_msg_result);
                if(array_ng_code_result==-31) isMultiCode=true;
            }
            if(jbResolveResult.containsKey("split_lot")){
                split_lot=jbResolveResult.getString("split_lot");
                mapRowArray.put("split_lot",split_lot);
            }
            if(jbResolveResult.containsKey("split_model")){
                split_model=jbResolveResult.getString("split_model");
                mapRowArray.put("split_model",split_model);
            }
        }

        //6.获取PCS存储信息以及PCS影响SET分选解析
        jbResolveResult=checkBdResult(bd_type,jbRecipe,jbSort,mapRowArray,bd_list_front,bd_list_back);
        array_status_result=jbResolveResult.getString("array_status");
        array_ng_code_result=jbResolveResult.getInteger("array_ng_code");
        array_ng_msg_result=jbResolveResult.getString("array_ng_msg");
        if(array_status_result.equals("NG")){
            array_status=array_status_result;
            mapRowArray.put("array_status",array_status_result);
            mapRowArray.put("array_ng_code",array_ng_code_result);
            mapRowArray.put("array_ng_msg",array_ng_msg_result);
            if(array_ng_code_result==-60 || array_ng_code_result==-61) isMultiCode=true;
        }
        if(jbResolveResult.containsKey("bd")){
            bdRowsList=(List<Map<String, Object>>)jbResolveResult.get("bd");
            for(Map<String, Object> bdMapItem:bdRowsList){
                bdMapItem.put("array_status",array_status);
            }
        }

        //7.综合判断board_result和deposit_position
        board_result=3;
        if(isMultiCode) board_result=4;
        else{
            if(array_status.equals("OK")){
                if(xout_flag.equals("Y")) board_result=2;
                else board_result=1;
            }
        }
        deposit_position=2;
        if(array_status.equals("OK")) deposit_position=1;
        mapRowArray.put("board_result",board_result);
        mapRowArray.put("deposit_position",deposit_position);
        //返回数据
        jbCcdResolveResult.put("array",mapRowArray);
        jbCcdResolveResult.put("bd",bdRowsList);
        return jbCcdResolveResult;
    }
}
