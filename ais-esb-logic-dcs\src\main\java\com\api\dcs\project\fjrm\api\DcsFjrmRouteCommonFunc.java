package com.api.dcs.project.fjrm.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.project.shzy.api.DcsShzyWmsMapStockFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 调度路线公共方法
 * 1.锁定天车任务完成
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
@Slf4j
public class DcsFjrmRouteCommonFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsFjrmStockCommonFunc dcsFjrmStockCommonFunc;

    //1.锁定天车任务完成
    public void RouteFinishUpd(String userID,String car_code,String car_task_id,String car_task_type) throws Exception{
        String errorMsg="";
        //String wmsTaskTable = "b_dcs_wms_car_task";

        //1.先查询任务是否存在
        String sqlLockCarTaskSel="select " +
                "a.task_id,a.model_id,a.task_num,a.from_stock_code,a.to_stock_code,a.task_type," +
                "a.ware_house,a.task_from,a.task_way,a.model_type," +
                "COALESCE(a.serial_num,'') serial_num," +
                "COALESCE(a.lot_num,'') lot_num," +
                "TO_CHAR(a.task_start_date, 'yyyy-MM-dd HH:mm:ss') task_start_date," +
                "COALESCE(a.m_length,0) m_length," +
                "COALESCE(a.m_width,0) m_width," +
                "COALESCE(a.m_height,0) m_height," +
                "COALESCE(a.m_weight,0) m_weight " +
                "from b_dcs_wms_lock_car_task a " +
                "where a.car_task_id="+car_task_id;
        List<Map<String, Object>> itemListLockCarTask=cFuncDbSqlExecute.ExecSelectSql(userID,sqlLockCarTaskSel,
                false,null,"");
        if(itemListLockCarTask==null || itemListLockCarTask.size()<=0){
            errorMsg="未能根据car_task_id={"+car_task_id+"}查找到天车锁定任务,此时人为操作将锁定调度任务删除了";
            throw new Exception(errorMsg);
        }
        Map<String, Object> mapItem=itemListLockCarTask.get(0);
        //任务类型:
        // WASTE_BOX_IN_TASK 空废料框入库、WASTE_BOX_OUT_TASK 空废料框出库
        // FULL_IN_TASK 满框入库、FULL_OUT_TASK/SPLIT_FULL_OUT_TASK 满框出库
        // MOVE_TASK 倒垛
        String task_id=mapItem.get("task_id").toString();
        String task_type=mapItem.get("task_type").toString();
        String task_num=mapItem.get("task_num").toString();
        String ware_house=mapItem.get("ware_house").toString();

        //2.查询任务总量
        String sqlIntefTaskTaskSel="select " +
                "COALESCE(width,0) width " +
                "from b_dcs_wms_intef_task " +
                "where task_id="+task_id;
        List<Map<String, Object>> itemListIntefTask=cFuncDbSqlExecute.ExecSelectSql(userID,sqlIntefTaskTaskSel,
                false,null,"");
        String width="0";//总重量(KG)
        if(itemListIntefTask!=null && itemListIntefTask.size()>0){
            width=itemListIntefTask.get(0).get("width").toString();
        }

        //3.查询路线明细
        String sqlLockCarRouteSel="select " +
                "car_task_type,location_x,location_y,location_z " +
                "from b_dcs_wms_lock_car_route " +
                "where car_task_id="+car_task_id+" " +
                "order by car_route_id";
        List<Map<String, Object>> itemListLockCarRoute=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlLockCarRouteSel,
                false,null,"");

        //3.解析task_type
        //String[] lstTaskType=task_type.split("_",-1);
        //String from_code=lstTaskType[0];
        //String to_code=lstTaskType[1];
        //if(task_type.startsWith("TKW")) from_code="KW";

        //4.组合参数
        Map<String, Object> mapStockParas=new HashMap<>();
        JSONObject mapEventParas=new JSONObject();
        String stock_code="";
        Integer from_location_x=0;
        Integer from_location_y=0;
        Integer from_location_z=0;
        Integer to_location_x=0;
        Integer to_location_y=0;
        Integer to_location_z=0;
        String event_status="OP_EVENT";
        String stock_way="";
        if(itemListLockCarRoute!=null && itemListLockCarRoute.size()>0){
            for(Map<String, Object> mapRoute : itemListLockCarRoute){
                String car_task_type2=mapRoute.get("car_task_type").toString();
                Integer location_x=Integer.parseInt(mapRoute.get("location_x").toString());
                Integer location_y=Integer.parseInt(mapRoute.get("location_y").toString());
                Integer location_z=Integer.parseInt(mapRoute.get("location_z").toString());
                if(car_task_type2.equals("CHA_QU")){
                    from_location_x=location_x;
                    from_location_y=location_y;
                    from_location_z=location_z;
                }
                if(car_task_type2.equals("FANG_ZHI")){
                    to_location_x=location_x;
                    to_location_y=location_y;
                    to_location_z=location_z;
                }
            }
        }
        if(car_task_type.equals("CHA_QU")){
            event_status="KW_EVENT";

            //任务类型:
            if(task_type.equals("WASTE_BOX_IN_TASK")){
                //空废料框入库
            } else if(task_type.equals("WASTE_BOX_OUT_TASK")){
                //空废料框出库
                stock_code=mapItem.get("from_stock_code").toString();
                stock_way="OUT";
            }else if(task_type.equals("FULL_IN_TASK")){
                //满框入库
            }else if(task_type.equals("SPLIT_FULL_OUT_TASK")){
                //满框出库
                stock_code=mapItem.get("from_stock_code").toString();
                stock_way="OUT";
            }else if(task_type.equals("MOVE_TASK")){
                //倒垛
                stock_code=mapItem.get("from_stock_code").toString();
                stock_way="OUT";
            }
        }
        if(car_task_type.equals("FANG_ZHI")){
            event_status="KW_EVENT";

            //任务类型:
            if(task_type.equals("WASTE_BOX_IN_TASK")){
                //空废料框入库
                stock_code=mapItem.get("to_stock_code").toString();
                stock_way="IN";
            } else if(task_type.equals("WASTE_BOX_OUT_TASK")){
                //空废料框出库
            }else if(task_type.equals("FULL_IN_TASK")){
                //满框入库
                stock_code=mapItem.get("to_stock_code").toString();
                stock_way="IN";
            }else if(task_type.equals("SPLIT_FULL_OUT_TASK")){
                //满框出库
            }else if(task_type.equals("MOVE_TASK")){
                //倒垛
                stock_code=mapItem.get("to_stock_code").toString();
                stock_way="IN";
            }
        }
        //4.1 mapStockParas
        mapStockParas.put("stock_code",stock_code);
        mapStockParas.put("model_id",mapItem.get("model_id").toString());
        mapStockParas.put("model_type",mapItem.get("model_type").toString());//型号(waste_box_code)
        mapStockParas.put("task_from",mapItem.get("task_from").toString());
        mapStockParas.put("task_num",mapItem.get("task_num").toString());
        mapStockParas.put("serial_num",mapItem.get("serial_num").toString());//序列号(material_code)
        mapStockParas.put("lot_num",mapItem.get("lot_num").toString());//批次号
        mapStockParas.put("location_z",to_location_z);
        mapStockParas.put("task_way",mapItem.get("task_way").toString());
        mapStockParas.put("task_type",task_type);
        mapStockParas.put("xz_location_y",0);
        mapStockParas.put("xz_finish_flag","N");
        mapStockParas.put("m_height",mapItem.get("m_height").toString());
        mapStockParas.put("alarm_flag","N");
        mapStockParas.put("stock_width",width);//总重量(KG)

        //4.2 mapEventParas
        mapEventParas.put("task_way",mapItem.get("task_way").toString());//任务方式(自动/半自动/手动)
        mapEventParas.put("task_type",mapItem.get("task_type").toString());//任务类型(WASTE_BOX_IN_TASK 空废料框入库、WASTE_BOX_OUT_TASK 空废料框出库、FULL_IN_TASK 满框入库、FULL_OUT_TASK/SPLIT_FULL_OUT_TASK 满框出库、MOVE_TASK 倒垛)
        mapEventParas.put("task_from",mapItem.get("task_from").toString());//任务来源
        mapEventParas.put("task_num",mapItem.get("task_num").toString());//任务号
        mapEventParas.put("serial_num",mapItem.get("serial_num").toString());//序列号(material_code)
        mapEventParas.put("lot_num",mapItem.get("lot_num").toString());//批次号
        mapEventParas.put("model_type",mapItem.get("model_type").toString());//型号(waste_box_code)
        mapEventParas.put("material_code",mapItem.get("model_type").toString());
        mapEventParas.put("material_des","");
        mapEventParas.put("material_draw","");
        mapEventParas.put("m_length",mapItem.get("m_length").toString());
        mapEventParas.put("m_width",mapItem.get("m_width").toString());
        mapEventParas.put("m_height",mapItem.get("m_height").toString());
        mapEventParas.put("m_weight",mapItem.get("m_weight").toString());
        mapEventParas.put("m_texture","");
        mapEventParas.put("from_stock_code",mapItem.get("from_stock_code").toString());
        mapEventParas.put("from_location_x",from_location_x);
        mapEventParas.put("from_location_y",from_location_y);
        mapEventParas.put("from_location_z",from_location_z);
        mapEventParas.put("to_stock_code",mapItem.get("to_stock_code").toString());
        mapEventParas.put("to_location_x",to_location_x);
        mapEventParas.put("to_location_y",to_location_y);
        mapEventParas.put("to_location_z",to_location_z);
        mapEventParas.put("event_status",event_status);

        //查询信息
        // WASTE_BOX_IN_TASK 空废料框入库、WASTE_BOX_OUT_TASK 空废料框出库
        // FULL_IN_TASK 满框入库、FULL_OUT_TASK/SPLIT_FULL_OUT_TASK 满框出库
        // MOVE_TASK 倒垛
        String stock_id="0";
        String sqlStockSel="";
        if((car_task_type.equals("CHA_QU") && task_type.equals("WASTE_BOX_IN_TASK")) ||
                (car_task_type.equals("CHA_QU") && task_type.equals("FULL_IN_TASK")) ||
                (car_task_type.equals("FANG_ZHI") && task_type.equals("WASTE_BOX_OUT_TASK")) ||
                (car_task_type.equals("FANG_ZHI") && task_type.equals("FULL_OUT_TASK"))){
            //查询 码头
            /*
            sqlStockSel="select " +
                    "stock_id " +
                    "from b_dcs_wms_map_me_stock " +
                    "where stock_code='"+stock_code+"' " +
                    "order by stock_id LIMIT 1 OFFSET 0";*/
        }
        else{
            sqlStockSel="select " +
                    "stock_id " +
                    "from b_dcs_wms_fmod_stock " +
                    "where stock_code='"+stock_code+"' " +
                    "order by stock_id LIMIT 1 OFFSET 0";
        }
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql(userID,sqlStockSel,
                false,null,"");
        if(itemListStock!=null && itemListStock.size()>0){
            stock_id=itemListStock.get(0).get("stock_id").toString();
        }
        mapEventParas.put("stock_id",stock_id);
        mapEventParas.put("car_code",car_code);
        mapEventParas.put("stock_code",stock_code);
        mapEventParas.put("stock_way",stock_way);

        //5.依次根据任务类型进行更新处理
        if(car_task_type.equals("CHA_QU")){
            //任务类型:
            if(task_type.equals("WASTE_BOX_IN_TASK")){
                //空废料框入库
            } else if(task_type.equals("WASTE_BOX_OUT_TASK")){
                //空废料框出库
                dcsFjrmStockCommonFunc.UpdateKwStockCount(userID,false,car_code,mapStockParas);
            }else if(task_type.equals("FULL_IN_TASK")){
                //满框入库
            }else if(task_type.equals("SPLIT_FULL_OUT_TASK")){
                //满框出库
                dcsFjrmStockCommonFunc.UpdateKwStockCount(userID,false,car_code,mapStockParas);
            }else if(task_type.equals("MOVE_TASK")){
                //倒垛
                dcsFjrmStockCommonFunc.UpdateKwStockCount(userID,false,car_code,mapStockParas);
            }
            dcsFjrmStockCommonFunc.StockEventIns(userID,car_code,stock_code,stock_way,mapEventParas);
        }
        if(car_task_type.equals("FANG_ZHI")){
            //任务类型:
            if(task_type.equals("WASTE_BOX_IN_TASK")){
                //空废料框入库
                dcsFjrmStockCommonFunc.UpdateKwStockCount(userID,true,car_code,mapStockParas);
            } else if(task_type.equals("WASTE_BOX_OUT_TASK")){
                //空废料框出库
            }else if(task_type.equals("FULL_IN_TASK")){
                //满框入库
                dcsFjrmStockCommonFunc.UpdateKwStockCount(userID,true,car_code,mapStockParas);
            }else if(task_type.equals("SPLIT_FULL_OUT_TASK")){
                //满框出库
            }else if(task_type.equals("MOVE_TASK")){
                //倒垛
                dcsFjrmStockCommonFunc.UpdateKwStockCount(userID,true,car_code,mapStockParas);
            }

            dcsFjrmStockCommonFunc.StockEventIns(userID,car_code,stock_code,stock_way,mapEventParas);
            //当任务FINISH时,此时要将任务进行FINISH
            String task_start_date="";
            if(mapItem.containsKey("task_start_date") && mapItem.get("task_start_date")!=null){
                task_start_date=mapItem.get("task_start_date").toString();
            }
            if(task_start_date==null || task_start_date.equals("")){
                task_start_date=CFuncUtilsSystem.GetNowDateTime("");
            }
            String task_end_date=CFuncUtilsSystem.GetNowDateTime("");
            Long cost_times=CFuncUtilsSystem.GetDiffMsTimes(task_start_date,task_end_date);
            cost_times=cost_times/1000L;//转换成秒
            String sqlTaskUpd="update b_dcs_wms_lock_car_task set " +
                    "last_updated_by='"+userID+"'," +
                    "last_update_date='"+task_end_date+"'," +
                    "task_end_date='"+task_end_date+"'," +
                    "cost_time="+cost_times+"," +
                    "execute_status='FINISH' " +
                    "where car_task_id="+car_task_id;
            cFuncDbSqlExecute.ExecUpdateSql(userID,sqlTaskUpd,false,null,"");
        }
    }
}
