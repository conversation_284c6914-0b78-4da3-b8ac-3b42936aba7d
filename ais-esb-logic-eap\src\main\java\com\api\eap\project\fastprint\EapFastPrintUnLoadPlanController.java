package com.api.eap.project.fastprint;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 收板机生产计划处理逻辑
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-22
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/fastprint/unload")
public class EapFastPrintUnLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //根据LotNum判断是否存在收板任务
    @RequestMapping(value = "/EapUnLoadPlanExistJudgeByLotNum", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapUnLoadPlanExistJudgeByLotNum(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/unload/EapUnLoadPlanExistJudgeByLotNum";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String lot_num = jsonParas.getString("lot_num");
            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            long station_id_long = Long.parseLong(station_id);
            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("plan_id", docItemBigData.getString("plan_id"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据LotNum判断是否存在收板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //将panel绑定到已有的任务  802BL机型
    @RequestMapping(value = "/EapUnLoadPlanSavePanel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapUnLoadPlanSavePanel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/unload/EapUnLoadPlanSavePanel";
        String transResult = "";
        String errorMsg = "";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String plan_id = jsonParas.getString("plan_id");
            String panel_list = jsonParas.getString("panel_list");
            //插入数据到数据库
            List<Map<String, Object>> lstPlanBDocuments = new ArrayList<>();
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String[] panelList = panel_list.split(",", -1);
            if (!panel_list.equals("")) {
                if (panelList != null && panelList.length > 0) {
                    for (int j = 0; j < panelList.length; j++) {
                        String panel_barcode = panelList[j];
                        String plan_d_id = CFuncUtilsSystem.CreateUUID(true);
                        Map<String, Object> mapBigDataRowB = new HashMap<>();
                        mapBigDataRowB.put("item_date", item_date);
                        mapBigDataRowB.put("item_date_val", item_date_val);
                        mapBigDataRowB.put("plan_d_id", plan_d_id);
                        mapBigDataRowB.put("plan_id", plan_id);
                        mapBigDataRowB.put("panel_barcode", panel_barcode);
                        lstPlanBDocuments.add(mapBigDataRowB);
                    }
                }
            }
            if (lstPlanBDocuments.size() > 0) mongoTemplate.insert(lstPlanBDocuments, apsPlanBTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "将panel绑定到已有的任务异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //根据工位号判断是否存在收板任务
    @RequestMapping(value = "/EapUnLoadPlanExistJudge", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapUnLoadPlanExistJudge(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/unload/EapUnLoadPlanExistJudge";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            long station_id_long = Long.parseLong(station_id);
            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("plan_id", docItemBigData.getString("plan_id"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("group_lot_status", docItemBigData.getString("group_lot_status"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据LotNum判断是否存在收板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //根据工位号判断是否存在收板任务
    @RequestMapping(value = "/EapUnLoadPlanExistJudgeByPort", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapUnLoadPlanExistJudgeByPort(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/unload/EapUnLoadPlanExistJudgeByPort";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            long station_id_long = Long.parseLong(station_id);
            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("plan_id", docItemBigData.getString("plan_id"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("group_lot_status", docItemBigData.getString("group_lot_status"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据LotNum判断是否存在收板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    @RequestMapping(value = "/EapCoreUnLoadTaskQuantityCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadTaskQuantityCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "eap/project/fastprint/unload/EapCoreUnLoadTaskQuantityCheck";
        String aEapMeStationFlow = "a_eap_me_station_flow";
        String aEapApsPlanD = "a_eap_aps_plan_d";
        String aEapApsPlan = "a_eap_aps_plan";
        String errorMsg = "";
        try {
            Query query = new Query();
            query.addCriteria(Criteria.where("group_lot_status").regex("WORK"));
            query.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            if (jsonParas.containsKey("port_code")) {
                query.addCriteria(Criteria.where("port_code").is(jsonParas.getInteger("port_code").toString()));
            }

            MongoCursor<Document> iterator = mongoTemplate.getCollection(aEapApsPlan).find(query.getQueryObject()).iterator();
            if (iterator.hasNext()) {
                Document next = iterator.next();
                String panelBarcode = jsonParas.getString("panel_barcode");
                MongoCursor<Document> panels = mongoTemplate.getCollection(aEapApsPlanD).find(new Query().addCriteria(Criteria.where("panel_barcode").is(panelBarcode)).addCriteria(Criteria.where("plan_id").is(next.getString("plan_id"))).getQueryObject()).iterator();
                String result = (next.containsKey("finish_count") ? next.get("finish_count").toString() : "0") + "," + (next.containsKey("target_lot_count") ? next.get("target_lot_count") : "0");
                //如果查询不到则给空，若ok数量小于计划数量则走正常板，但是可能出现上述条件成立的情况下出现dummy板，则查询板件条码，若存在则返回若不存在则返回空
                result += panels.hasNext() ? "," + panelBarcode : ",";
                return CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
            }
        } catch (Exception e) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + e.getMessage();
            return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
    }

    //修改收板机任务数量
    @RequestMapping(value = "/EapUnLoadPlanCountUpdate", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapUnLoadPlanCountUpdate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/unload/EapUnLoadPlanCountUpdate";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");
            String plan_id = jsonParas.getString("plan_id");
            String plan_count = jsonParas.getString("plan_count");
            String updateLotCountStatusFlag = jsonParas.getString("updateLotCountStatusFlag");
            String updateLotCountFlag = jsonParas.getString("updateLotCountFlag");
            String group_lot_status = "";
            int plan_count_int = Integer.parseInt(plan_count);
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_status = docItemBigData.getString("group_lot_status");
            }

            if (iteratorBigData.hasNext()) iteratorBigData.close();

            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("plan_id").is(plan_id));
            Update updateBigData = new Update();
            updateBigData.set("target_lot_count", plan_count_int);
            mongoTemplate.updateMulti(queryBigData2, updateBigData, apsPlanTable);
            //如果是WORK，修改plc点位
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            if (group_lot_status.equals("WORK")) {
                String clientCode1 = updateLotCountStatusFlag.split("/")[0];
                String groupCode1 = updateLotCountStatusFlag.split("/")[1];
                String tagCode1 = updateLotCountStatusFlag.split("/")[2];

                String clientCode2 = updateLotCountFlag.split("/")[0];
                String groupCode2 = updateLotCountFlag.split("/")[1];
                String tagCode2 = updateLotCountFlag.split("/")[2];
                if (aisMonitorModel.equals("AIS-SERVER")) {
                    updateLotCountStatusFlag = clientCode1 + "_" + station_code + "/" + groupCode1 + "/" + tagCode1 + "";
                    updateLotCountFlag = clientCode2 + "_" + station_code + "/" + groupCode2 + "/" + tagCode2 + "";
                }
                if (!updateLotCountStatusFlag.equals("")) {
                    errorMsg = cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, updateLotCountFlag, plan_count, true);
                    errorMsg = cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, updateLotCountStatusFlag, "1", true);
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "修改收板机任务数量异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //根据lot_num修改收板机任务数量
    @RequestMapping(value = "/EapUnLoadPlanCountUpdate2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapUnLoadPlanCountUpdate2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/unload/EapUnLoadPlanCountUpdate2";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");
            String lot_num = jsonParas.getString("lot_num");
            Integer plan_count = jsonParas.getInteger("plan_count");
            String updateLotCountStatusFlag = jsonParas.getString("updateLotCountStatusFlag");
            String updateLotCountFlag = jsonParas.getString("updateLotCountFlag");
            String sqlStation = "select station_id " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            List<Map<String, Object>> itemList = new ArrayList<>();
            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            //1.如果实际投入板件数量<=0,直接取消任务，如果任务已开始，写入取消任务点位
            if (plan_count <= 0) {
                Query query = new Query();
                query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                query.addCriteria(Criteria.where("lot_num").is(lot_num));
                query.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                query.with(Sort.by(Sort.Direction.ASC, "_id"));
                List<Document> documentList = mongoTemplate.find(query, Document.class, apsPlanTable);
                if (documentList != null && documentList.size() > 0) {
                    for (Document document : documentList) {
                        String plan_id = document.getString("plan_id");
                        String group_lot_status_now = document.getString("group_lot_status");
                        errorMsg = this.CancelTask(plan_id, group_lot_status_now, station_code);
                        if (!errorMsg.equals("")) {
                            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return selectResult;
                        }
                    }
                }
            }
            //2.如果实际投入板件数量<=24,需要先判断收板任务数量，如果是两个任务，取消一个（优先处处理未开始的任务），修改另一个，并把实际投入板件条码绑定到这个任务上
            else if (plan_count <= 24) {
                //2.1 如果有两个任务，先取消一个
                Query query = new Query();
                String plan_id_cancle = "";
                query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                query.addCriteria(Criteria.where("lot_num").is(lot_num));
                query.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                query.with(Sort.by(Sort.Direction.ASC, "_id"));
                List<Document> documentList = mongoTemplate.find(query, Document.class, apsPlanTable);
                if (documentList != null && documentList.size() == 2) {
                    int index = 1;//需要取消的任务
                    for (int i = 1; i >= 0; i--) {
                        Document doc = documentList.get(i);
                        String status = doc.getString("group_lot_status");
                        if (status.equals("WAIT") || status.equals("PLAN")) {
                            index = i;
                            break;
                        }
                    }
                    Document document = documentList.get(index);
                    String plan_id = document.getString("plan_id");
                    plan_id_cancle = plan_id;
                    String group_lot_status_now = document.getString("group_lot_status");
                    errorMsg = this.CancelTask(plan_id, group_lot_status_now, station_code);
                    if (!errorMsg.equals("")) {
                        selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return selectResult;
                    }
                }
                //2.2 修改剩下的任务
                Query query2 = new Query();
                query2.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                query2.addCriteria(Criteria.where("lot_num").is(lot_num));
                query2.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                query.with(Sort.by(Sort.Direction.ASC, "_id"));
                List<Document> documentList2 = mongoTemplate.find(query2, Document.class, apsPlanTable);
                if (documentList2 != null && documentList2.size() > 0) {
                    for (Document document : documentList) {
                        String plan_id = document.getString("plan_id");
                        String group_lot_status_now = document.getString("group_lot_status");
                        String port_code = document.getString("port_code");
                        errorMsg = this.UpdateTask(plan_id, group_lot_status_now, station_code, station_id,
                                port_code, plan_count, plan_id_cancle, updateLotCountStatusFlag, updateLotCountFlag, request);
                        if (!errorMsg.equals("")) {
                            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return selectResult;
                        }
                    }
                }
            }
            //3.如果实际投入板件数量>24,直接处理其中一个任务（优先处处理未开始的任务），另一个不做任何处理
            else {
                //3.1 如果有两个任务，如果一个执行中，一个未开始，直接处理未开始的任务，否者直接处理其中一个
                Query query = new Query();
                query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                query.addCriteria(Criteria.where("lot_num").is(lot_num));
                query.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                query.with(Sort.by(Sort.Direction.ASC, "_id"));
                List<Document> documentList = mongoTemplate.find(query, Document.class, apsPlanTable);
                if (documentList != null && documentList.size() == 2) {
                    int index = 1;//需要修改的任务
                    for (int i = 1; i >= 0; i--) {
                        Document doc = documentList.get(i);
                        String status = doc.getString("group_lot_status");
                        if (status.equals("WAIT") || status.equals("PLAN")) {
                            index = i;
                            break;
                        }
                    }
                    Document document = documentList.get(index);
                    String plan_id = document.getString("plan_id");
                    String group_lot_status_now = document.getString("group_lot_status");
                    String port_code = document.getString("port_code");
                    errorMsg = this.UpdateTask(plan_id, group_lot_status_now, station_code, station_id, port_code,
                            plan_count - 24, "", updateLotCountStatusFlag, updateLotCountFlag, request);
                    if (!errorMsg.equals("")) {
                        selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return selectResult;
                    }
                    Document document2 = documentList.get(1 - index);
                    String plan_id2 = document2.getString("plan_id");
                    String group_lot_status_now2 = document2.getString("group_lot_status");
                    String port_code2 = document2.getString("port_code");
                    errorMsg = this.UpdateTask(plan_id2, group_lot_status_now2, station_code, station_id, port_code2,
                            24, "", updateLotCountStatusFlag, updateLotCountFlag, request);
                    if (!errorMsg.equals("")) {
                        selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return selectResult;
                    }
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "修改收板机任务数量异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //取消任务
    private String CancelTask(String plan_id, String group_lot_status_now, String station_code) {
        String errorMsg = "";
        String transResult = "";
        String apsPlanTable = "a_eap_aps_plan";
        if (group_lot_status_now.equals("WORK")) {
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String tagTaskCancelRequest = "";
            if (aisMonitorModel.equals("AIS-PC")) {
                tagTaskCancelRequest = "LoadAis/AisStatus/TaskCancelRequest";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                tagTaskCancelRequest = "LoadAis_" + station_code + "/AisStatus/TaskCancelRequest";
            } else {
                errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagTaskCancelRequest, "1", true);
            if (!errorMsg.equals("")) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
        }
        //取消收板机任务
        Query queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
        Update updateBigData = new Update();
        updateBigData.set("group_lot_status", "CANCEL");
        mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
        return transResult;
    }

    //修改任务数量
    private String UpdateTask(String plan_id, String group_lot_status_now, String station_code,
                              String station_id, String port_code, Integer finish_ok_count,
                              String plan_id_cancle, String updateLotCountStatusFlag, String updateLotCountFlag,
                              HttpServletRequest request) {
        String errorMsg = "";
        String transResult = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanTableD = "a_eap_aps_plan_d";
        try {
            if (group_lot_status_now.equals("WORK")) {
                String port_index = "";
                if (port_code != null && !port_code.equals("")) {
                    String sqlPort = "select port_index " + "from a_eap_fmod_station_port " + "where station_id=" + station_id + " and port_code='" + port_code + "'";
                    List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPort, false, request, "");
                    if (lstPort != null && lstPort.size() > 0) {
                        port_index = lstPort.get(0).get("port_index").toString();
                    }
                }
                String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagUpdateLotCountStatus = "";
                String tagUpdateLotCount = "";

                String clientCode1 = updateLotCountStatusFlag.split("/")[0];
                String groupCode1 = updateLotCountStatusFlag.split("/")[1];
                String tagCode1 = updateLotCountStatusFlag.split("/")[2];

                String clientCode2 = updateLotCountFlag.split("/")[0];
                String groupCode2 = updateLotCountFlag.split("/")[1];
                String tagCode2 = updateLotCountFlag.split("/")[2];

                if (aisMonitorModel.equals("AIS-PC")) {
                    tagUpdateLotCountStatus = updateLotCountStatusFlag;
                    tagUpdateLotCount = updateLotCountFlag;
                } else if (aisMonitorModel.equals("AIS-SERVER")) {
                    tagUpdateLotCountStatus = clientCode1 + "_" + station_code + "/" + groupCode1 + "/" + tagCode1 + "";
                    tagUpdateLotCount = clientCode2 + "_" + station_code + "/" + groupCode2 + "/" + tagCode2 + "";
                } else {
                    errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                String tagOnlyKeyList = tagUpdateLotCount + "," + tagUpdateLotCountStatus;
                String tagValueList = finish_ok_count + "&1";
                errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
                if (!errorMsg.equals("")) {
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }
            //更新收板机任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            Update updateBigData = new Update();
            updateBigData.set("target_lot_count", finish_ok_count);
            updateBigData.set("target_update_count", finish_ok_count);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            //将取消的任务的板件信息挂到这个任务上
            if (!plan_id_cancle.equals("")) {
                Query query = new Query();
                query.addCriteria(Criteria.where("plan_id").is(plan_id_cancle));
                Update update = new Update();
                update.set("plan_id", plan_id);
                mongoTemplate.updateMulti(query, update, apsPlanTableD);
                return transResult;
            }
        } catch (Exception ex) {
            errorMsg = "放板机完工同步到收板机异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //判断是否为Dummy板(珠海快捷)
    @RequestMapping(value = "/EapZhFpUnLoadDummyPanelJudge", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapZhFpUnLoadDummyPanelJudge(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/unload/EapZhFpUnLoadDummyPanelJudge";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String dummy_flag = "N";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序

            //防止null数据
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("") || panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (port_index == null || port_index.equals("")) port_index = "0";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            Query queryBigData = null;
            MongoCursor<Document> iteratorBigData = null;

            //2.获取当前计划中或者执行中的子任务
            String plan_id = "";
            Integer plan_lot_count = 0;//target_lot_count
            Integer finish_count = 0;

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                plan_lot_count = docItemBigData.getInteger("target_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                iteratorBigData.close();
            }

            if (plan_id.equals("")) {//当未找到任务时直接记录
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, dummy_flag, "", 0);
                return transResult;
            }

            //若是NoRead
            if (panel_barcode.equals("NoRead")) {
                if (finish_count >= plan_lot_count) {
                    dummy_flag = "Y";
                }
            } else {
                Query queryBigDataD = new Query();
                queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                if (okCount <= 0) dummy_flag = "Y";
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, dummy_flag, "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否为Dummy板异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
