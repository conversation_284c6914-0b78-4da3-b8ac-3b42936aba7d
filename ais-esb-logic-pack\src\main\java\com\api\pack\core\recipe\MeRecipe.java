package com.api.pack.core.recipe;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.api.base.IMongoBasic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <p>
 * MeRecipe: 配方
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@ApiModel(value = "MeRecipe", description = "配方")
@Document("a_pack_me_recipe")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class MeRecipe extends IMongoBasic
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "料号(型号)")
    @JsonProperty("model_type")
    @JSONField(name = "model_type")
    @Field(value = "model_type")
    private String modelType;

    @ApiModelProperty(value = "料号(型号)版本")
    @JsonProperty("model_version")
    @JSONField(name = "model_version")
    @Field(value = "model_version")
    private String modelVersion;

    @ApiModelProperty(value = "PCS长度")
    @JsonProperty("pcs_length")
    @JSONField(name = "pcs_length")
    @Field(value = "pcs_length")
    private Integer pcsLength;

    @ApiModelProperty(value = "PCS宽度")
    @JsonProperty("pcs_width")
    @JSONField(name = "pcs_width")
    @Field(value = "pcs_width")
    private Integer pcsWidth;

    @ApiModelProperty(value = "PCS厚度")
    @JsonProperty("pcs_thickness")
    @JSONField(name = "pcs_thickness")
    @Field(value = "pcs_thickness")
    private Integer pcsThickness;

    @ApiModelProperty(value = "PCS重量")
    @JsonProperty("pcs_weight")
    @JSONField(name = "pcs_weight")
    @Field(value = "pcs_weight")
    private Integer pcsWeight;

    @ApiModelProperty(value = "Tray高度")
    @JsonProperty("tray_height")
    @JSONField(name = "tray_height")
    @Field(value = "tray_height")
    private Integer trayHeight;

    @ApiModelProperty(value = "Tray重量")
    @JsonProperty("tray_weight")
    @JSONField(name = "tray_weight")
    @Field(value = "tray_weight")
    private Integer trayWeight;

    @ApiModelProperty(value = "Tray容积")
    @JsonProperty("tray_volume")
    @JSONField(name = "tray_volume")
    @Field(value = "tray_volume")
    private Integer trayVolume;

    @ApiModelProperty(value = "Overlay高度")
    @JsonProperty("overlay_height")
    @JSONField(name = "overlay_height")
    @Field(value = "overlay_height")
    private Integer overlayHeight;

    @ApiModelProperty(value = "Overlay重量")
    @JsonProperty("overlay_weight")
    @JSONField(name = "overlay_weight")
    @Field(value = "overlay_weight")
    private Integer overlayWeight;

    @ApiModelProperty(value = "重量误差")
    @JsonProperty("weight_error")
    @JSONField(name = "weight_error")
    @Field(value = "weight_error")
    private String weightError;

    @ApiModelProperty(value = "束带方式(一字)")
    @JsonProperty("bd_way1")
    @JSONField(name = "bd_way1")
    @Field(value = "bd_way1")
    private String bdWay1;

    @ApiModelProperty(value = "束带方式(三字)")
    @JsonProperty("bd_way3")
    @JSONField(name = "bd_way3")
    @Field(value = "bd_way3")
    private String bdWay3;

    @ApiModelProperty(value = "喷墨模板")
    @JsonProperty("inkjet_tpl")
    @JSONField(name = "inkjet_tpl")
    @Field(value = "inkjet_tpl")
    private String inkjetTpl;

    public static MeRecipe byModelTypeAndModelVersion(String modelType, String modelVersion, MeRecipeService service)
    {
        MeRecipe query = new MeRecipe();
        query.setModelType(modelType);
        query.setModelVersion(modelVersion);
        return service.findOne(Example.of(query)).orElse(null);
    }
}
