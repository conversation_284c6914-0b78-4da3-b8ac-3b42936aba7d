package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 定颖放板机定制生产计划处理逻辑
 * 1.放板机Panel校验
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/dy/load")
public class EapDyLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private EapDySendEventFunc eapDySendEventFunc;

    //1.放板机Panel校验
    @RequestMapping(value = "/EapDyLoadPlanPanelCheckAndSave", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/load/EapDyLoadPlanPanelCheckAndSave";
        String transResult="";
        String errorMsg="";
        String meUserTable="a_eap_me_station_user";
        String apsPlanTable="a_eap_aps_plan";
        String apsPlanDTable="a_eap_aps_plan_d";
        String meStationFlowTable="a_eap_me_station_flow";
        String result="";
        try{
            String station_id=jsonParas.getString("station_id");
            String group_lot_num=jsonParas.getString("group_lot_num");
            String panel_barcode=jsonParas.getString("panel_barcode");
            String tray_barcode=jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value=jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value=jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value=jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value=jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批,2为一批多车
            String onecar_multybatch_check_value=jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code=jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String inspect_flag=jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag=jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag=jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code=jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String face_code2=jsonParas.getString("face_code");//存储A面B面,1:A,2:B

            //防止null数据
            String panel_flag="N";
            Integer panel_ng_code=0;
            String panel_ng_msg="";
            String panel_status="OK";
            String user_name="";
            if(group_lot_num==null) group_lot_num="";
            if(panel_barcode==null) panel_barcode="";
            if(panel_barcode.equals("FFFFFFFF")) panel_barcode="NoRead";
            if(tray_barcode==null) tray_barcode="";
            if(ng_auto_pass_value==null) ng_auto_pass_value="";
            if(ng_manual_pass_value==null) ng_manual_pass_value="";
            if(panel_model_value==null) panel_model_value="";
            if(panel_model_value.equals("1")) panel_flag="Y";
            if(onecar_multybatch_value==null) onecar_multybatch_value="";
            if(onecar_multybatch_check_value==null) onecar_multybatch_check_value="";
            if(manual_judge_code==null || manual_judge_code.equals("")) manual_judge_code="0";
            if(inspect_flag==null || inspect_flag.equals("")) inspect_flag="N";
            if(dummy_flag==null || dummy_flag.equals("")) dummy_flag="N";
            if(eap_flag==null || eap_flag.equals("")) eap_flag="N";
            if(eap_ng_code==null || eap_ng_code.equals("")) eap_ng_code="0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if(face_code2==null || face_code2.equals("")) face_code2="0";
            Integer face_code2_int=Integer.parseInt(face_code2);

            //针对条码进行校验
            String[] lstPanelBarCode=panel_barcode.split(",",-1);
            if(lstPanelBarCode!=null && lstPanelBarCode.length==2){
                panel_barcode=lstPanelBarCode[0];
                face_code2=lstPanelBarCode[1];
                if(face_code2.equals("Z")) face_code2_int=1;
                else if(face_code2.equals("F")) face_code2_int=2;
            }
            //若出现多个集合,则直接判断为NoRead
            if(lstPanelBarCode!=null && lstPanelBarCode.length>2){
                panel_barcode="NoRead";
            }

            long station_id_long=Long.parseLong(station_id);
            String station_code="";//工位号
            String sqlStation="select station_code " +
                    "from sys_fmod_station " +
                    "where station_id="+station_id+"";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStation,false,request,apiRoutePath);
            if(itemListStation!=null && itemListStation.size()>0){
                station_code=itemListStation.get(0).get("station_code").toString();
            }

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                user_name=docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //1.判断是否存在PLAN状态下group_lot_num,若存在则进行更新为WORK状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "WORK");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            //查询该工单下全部的plan_id
            List<String> lstPlanId=new ArrayList<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前计划中或者执行中的子任务
            String plan_id="";
            String task_from="";
            String lot_num="";
            String lot_short_num="";
            Integer lot_index=1;
            String port_code="";
            String material_code="";
            String pallet_num="";
            String pallet_type="";
            String lot_level="";
            Integer fp_index=0;
            Double panel_length=0d;
            Double panel_width=0d;
            Double panel_tickness=0d;
            Integer plan_lot_count=0;
            Integer finish_count=0;
            Integer finish_ok_count=0;
            Integer finish_ng_count=0;
            Integer face_code=0;
            Integer eap_face_code=0;
            Integer pallet_use_count=0;
            Integer inspect_finish_count=0;
            Integer panel_index=0;
            String old_plan_status="";
            String item_info="";
            String pnl_infos="";
            String holding_time="";
            String over_times="0";
            String virtu_pallet_num="";

            String[] lot_status_list=new String[]{"PLAN","WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(!iteratorBigData.hasNext()){
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                plan_id=docItemBigData.getString("plan_id");
                task_from=docItemBigData.getString("task_from");
                lot_num=docItemBigData.getString("lot_num");
                lot_short_num=docItemBigData.getString("lot_short_num");
                lot_index=docItemBigData.getInteger("lot_index");
                port_code=docItemBigData.getString("port_code");
                material_code=docItemBigData.getString("material_code");
                pallet_num=docItemBigData.getString("pallet_num");
                pallet_type=docItemBigData.getString("pallet_type");
                lot_level=docItemBigData.getString("lot_level");
                panel_length=docItemBigData.getDouble("panel_length");
                panel_width=docItemBigData.getDouble("panel_width");
                panel_tickness=docItemBigData.getDouble("panel_tickness");
                plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                finish_count=docItemBigData.getInteger("finish_count");
                finish_ok_count=docItemBigData.getInteger("finish_ok_count");
                finish_ng_count=docItemBigData.getInteger("finish_ng_count");
                face_code=docItemBigData.getInteger("face_code");
                eap_face_code=docItemBigData.getInteger("face_code");
                pallet_use_count=docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count=docItemBigData.getInteger("inspect_finish_count");
                old_plan_status=docItemBigData.getString("lot_status");
                item_info=docItemBigData.getString("item_info");
                if(docItemBigData.containsKey("pnl_infos")) pnl_infos=docItemBigData.getString("pnl_infos");
                virtu_pallet_num=pallet_num;
                if(docItemBigData.containsKey("virtu_pallet_num")) virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                iteratorBigData.close();
            }
            face_code=face_code2_int;
            //未找到任务报错
            if(plan_id.equals("")){
                errorMsg="未找到生产任务,请先导入生产任务";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            panel_index=finish_ok_count+1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id=CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("station_flow_id",station_flow_id);
            mapBigDataRow.put("station_id",station_id_long);
            mapBigDataRow.put("plan_id",plan_id);
            mapBigDataRow.put("task_from",task_from);
            mapBigDataRow.put("group_lot_num",group_lot_num);
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("lot_short_num",lot_short_num);
            mapBigDataRow.put("lot_index",lot_index);
            mapBigDataRow.put("port_code",port_code);
            mapBigDataRow.put("material_code",material_code);
            mapBigDataRow.put("pallet_num",pallet_num);
            mapBigDataRow.put("pallet_type",pallet_type);
            mapBigDataRow.put("lot_level",lot_level);
            mapBigDataRow.put("fp_index",fp_index);
            mapBigDataRow.put("panel_barcode",panel_barcode);
            mapBigDataRow.put("panel_length",panel_length);
            mapBigDataRow.put("panel_width",panel_width);
            mapBigDataRow.put("panel_tickness",panel_tickness);
            mapBigDataRow.put("panel_index",panel_index);
            mapBigDataRow.put("panel_status",panel_status);
            mapBigDataRow.put("panel_ng_code",panel_ng_code);
            mapBigDataRow.put("panel_ng_msg",panel_ng_msg);
            mapBigDataRow.put("inspect_flag",inspect_flag);
            mapBigDataRow.put("dummy_flag",dummy_flag);
            mapBigDataRow.put("manual_judge_code",manual_judge_code);
            mapBigDataRow.put("panel_flag",panel_flag);
            mapBigDataRow.put("user_name",user_name);
            mapBigDataRow.put("eap_flag",eap_flag);
            mapBigDataRow.put("tray_barcode",tray_barcode);
            mapBigDataRow.put("face_code",face_code);
            mapBigDataRow.put("offline_flag","N");
            mapBigDataRow.put("virtu_pallet_num",virtu_pallet_num);

            //4.若为Dummy板则直接先存储
            if(dummy_flag.equals("Y")){
                result=station_flow_id+","+lot_num+","+pallet_num+","+ panel_index+","+panel_barcode+","+
                        panel_ng_code+","+inspect_finish_count+","+tray_barcode+",WORK"+","+group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow,meStationFlowTable);
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if(eap_flag.equals("Y")){
                panel_ng_code=Integer.parseInt(eap_ng_code);
                if(panel_ng_code!=0){
                    panel_status="NG";
                    panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if((ng_auto_pass_value.equals("1")  || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")){
                    panel_status="NG_PASS";
                    panel_ng_code=4;
                    panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else{
                //6.1 有panel模式
                if(panel_model_value.equals("1")){
                    if(ng_manual_pass_value.equals("1")){
                        panel_status="NG_PASS";
                        panel_ng_code=4;
                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                    }
                    else {
                        if(panel_barcode.equals("NoRead") || panel_barcode.equals("")){
                            if(ng_auto_pass_value.equals("1")){
                                panel_status="NG_PASS";
                                panel_ng_code=4;
                                panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                            }
                            else{
                                panel_status="NG";
                                panel_ng_code=2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                            }
                        }
                        else{
                            long pnListCount =  mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                            if(pnListCount>0){
                                Query queryBigDataD = new Query();
                                queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                long okCount=mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                if(okCount<=0){
                                    if(ng_auto_pass_value.equals("1")){
                                        panel_status="NG_PASS";
                                        panel_ng_code=4;
                                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                    else{
                                        panel_status="NG";
                                        panel_ng_code=1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                            if(panel_ng_code==0){
                                Boolean isNewVersion=eapDyInterfCommon.CheckDyVersion(3);
                                //需要判断简码LIST信息,新增板件判断逻辑,240315 by ZhouJun
                                List<String> lot_short_num_list=new ArrayList<>();
                                Boolean isCheckPnlHold=true;
                                if(isNewVersion) isCheckPnlHold=false;
                                if (item_info != null && !item_info.equals("")){
                                    JSONArray item_info_list = JSONArray.parseArray(item_info);
                                    if (item_info_list != null && item_info_list.size() > 0){
                                        int lotShortNumListCount=0;
                                        for (int i = 0; i < item_info_list.size(); i++){
                                            JSONObject jbItem = item_info_list.getJSONObject(i);
                                            String item_id = jbItem.getString("item_id");
                                            String item_value = jbItem.getString("item_value");
                                            if(item_id.equals("S026") && item_value!=null && !item_value.equals("") && !item_value.contains("NA")){
                                                try{
                                                    lotShortNumListCount=Integer.parseInt(item_value);
                                                }
                                                catch (Exception intError){lotShortNumListCount=0;}
                                            }
                                            if(item_id.equals("S047") && !item_value.equals("1")){
                                                if(!isNewVersion) isCheckPnlHold=false;
                                            }
                                            if(isNewVersion && item_id.equals("S022") && item_value.equals("1")){
                                                isCheckPnlHold=true;
                                            }
                                        }
                                        if(lotShortNumListCount>0){
                                            List<String> lotShortColList=new ArrayList<>();
                                            for(int i=1;i<=lotShortNumListCount;i++){
                                                String SName = "S"+String.format("%03d",26+i);
                                                lotShortColList.add(SName);
                                            }
                                            for (int i = 0; i < item_info_list.size(); i++){
                                                JSONObject jbItem = item_info_list.getJSONObject(i);
                                                String item_id = jbItem.getString("item_id");
                                                String item_value = jbItem.getString("item_value");
                                                if(lotShortColList.contains(item_id) &&
                                                        item_value!=null && !item_value.equals("")){
                                                    if(!lot_short_num_list.contains(item_value)) lot_short_num_list.add(item_value);
                                                }
                                            }
                                        }
                                    }
                                }
                                if(isNewVersion){
                                    if(lot_short_num.length()>=12){
                                        lot_short_num=lot_short_num.substring(0,12);
                                    }
                                }
                                if(!lot_short_num.equals("") && !lot_short_num_list.contains(lot_short_num)) lot_short_num_list.add(lot_short_num);
                                //新增逻辑,简码判断
                                if(lot_short_num_list.size()>0 && pnListCount<=0){
                                    //默认混批
                                    panel_status="NG";
                                    panel_ng_code=1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                    panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);

                                    for(int i=0;i<lot_short_num_list.size();i++){
                                        String lot_short_num_item=lot_short_num_list.get(i);
                                        if(panel_barcode.startsWith(lot_short_num_item)){
                                            panel_status="OK";
                                            panel_ng_code=0;
                                            panel_ng_msg="";
                                            break;
                                        }
                                    }
                                    if(panel_ng_code>0 && ng_auto_pass_value.equals("1")){
                                        panel_status="NG_PASS";
                                        panel_ng_code=4;
                                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }

                                //针对过期数据进行判断
                                Boolean isExistHoldPanelInfo=false;
                                if(panel_ng_code==0 && pnl_infos!=null && !pnl_infos.equals("") && isCheckPnlHold){
                                    JSONArray pnl_infos_list = JSONArray.parseArray(pnl_infos);
                                    if(pnl_infos_list!=null && pnl_infos_list.size()>0){
                                        for (int i = 0; i < pnl_infos_list.size(); i++){
                                            JSONObject jbItem = pnl_infos_list.getJSONObject(i);
                                            String panel_id = jbItem.getString("panel_id");
                                            String panel_time = jbItem.getString("panel_time");
                                            if(panel_id.equals(panel_barcode)){
                                                long difTimes= CFuncUtilsSystem.GetDiffMsTimes(CFuncUtilsSystem.GetNowDateTime(""),panel_time);
                                                holding_time=panel_time;
                                                isExistHoldPanelInfo=true;
                                                if(difTimes<0){
                                                    long difTimesShow=(0-difTimes)/1000;
                                                    over_times=String.valueOf(difTimesShow);
                                                    panel_status="NG";
                                                    panel_ng_code=5;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5Hold,6其他定义
                                                    panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                    //增加弹窗报错以及Alarm报错
                                                    opCommonFunc.SaveCimMessage(station_id_long,"1","-5","AIS",
                                                            "检测到pnl号{"+panel_barcode+"}HoldingTime超时{"+difTimesShow+"}秒",5);
                                                    eapDySendEventFunc.AlarmReport(station_id_long,station_code,
                                                            "N","N","0C02",
                                                            "检测到pnl号{"+panel_barcode+"}HoldingTime超时{"+difTimesShow+"}秒",
                                                            "N","Run","N");
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                                //若不存在Hold信息则卡住NG
                                if(!isExistHoldPanelInfo && panel_ng_code==0 && isCheckPnlHold){
                                    panel_status="NG";
                                    panel_ng_code=5;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5Hold,6其他定义
                                    panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    //增加弹窗报错以及Alarm报错
                                    opCommonFunc.SaveCimMessage(station_id_long,"1","-5","AIS",
                                            "检测到pnl号{"+panel_barcode+"}不存在超时PNL集合中,判定为NG",5);
                                    eapDySendEventFunc.AlarmReport(station_id_long,station_code,
                                            "N","N","0C02",
                                            "检测到pnl号{"+panel_barcode+"}不存在超时PNL集合中,判定为NG",
                                            "N","Run","N");
                                }

                                //判断是否做正反面判断
                                if(eap_face_code>0){
                                    if(face_code!=eap_face_code){
                                        String eap_face_code_str="正面";
                                        if(eap_face_code!=1) eap_face_code_str="反面";
                                        String face_code_str="正面";
                                        if(face_code!=1) face_code_str="反面";

                                        panel_status="NG";
                                        panel_ng_code=6;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5Hold,6面次错误,7其他定义
                                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        //增加弹窗报错以及Alarm报错
                                        opCommonFunc.SaveCimMessage(station_id_long,"1","-6","AIS",
                                                "检测到pnl号{"+panel_barcode+"}识别面次为{"+face_code_str+"}与工单要求面次{"+eap_face_code_str+"}不一致,判定为NG",5);
                                    }
                                }
                                if(panel_ng_code>0 && ng_auto_pass_value.equals("1")){
                                    panel_status="NG_PASS";
                                    panel_ng_code=4;
                                    panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                }
                            }
                        }
                    }
                }
                else{
                    //如果是无Panel模式,则需要进行组合
                    if(!lot_short_num.equals("") && lot_short_num.length()>=10){
                        String noPanelLotShortNum=lot_short_num.substring(0,10);
                        panel_barcode=noPanelLotShortNum+String.format("%03d", panel_index);
                        mapBigDataRow.put("panel_barcode",panel_barcode);
                    }
                    else{
                        panel_barcode=String.format("%03d", panel_index);
                        mapBigDataRow.put("panel_barcode",panel_barcode);
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status",panel_status);
            mapBigDataRow.put("panel_ng_code",panel_ng_code);
            mapBigDataRow.put("panel_ng_msg",panel_ng_msg);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow,meStationFlowTable);
            //更新完工数量
            finish_count++;
            if(panel_status.equals("NG"))  finish_ng_count++;
            else finish_ok_count++;
            String lot_status="WORK";
            String lot_status2="WORK";
            if(finish_ok_count>=plan_lot_count) lot_status="FINISH";
            lot_status2=lot_status;
            if(old_plan_status.equals("FINISH")) lot_status2="FINISH2";
            updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if(finish_count==1){
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if(lot_status.equals("FINISH")){
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            //更新首检数量
            if(inspect_flag.equals("Y")){//首检必须要全部合格件,否则再次从0开始
                if(panel_status.equals("OK") || panel_status.equals("NG_PASS")) inspect_finish_count++;
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            //更新EAP状态
            if(finish_count==1){
                String writeTags="LoadEap/EapStatus/CurrentLotNum,LoadEap/EapStatus/CurrentLotCount";
                String tagValues=lot_num+"&"+plan_lot_count;
                cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, writeTags, tagValues, false);
            }

            //判断是否为第一次成功放板
            String first_panel_flag="N";
            if(finish_ok_count==1){
                //求和
                String[] panel_status_list=new String[]{"OK","NG_PASS"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                queryBigData.addCriteria(Criteria.where("panel_status").in(panel_status_list));
                queryBigData.addCriteria(Criteria.where("plan_id").ne(plan_id));
                long okCount=mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                if(okCount<=0) first_panel_flag="Y";
            }

            //返回数据
            result=station_flow_id+","+lot_num+","+pallet_num+","+panel_index+","+panel_barcode+","+
                    panel_ng_code+","+inspect_finish_count+","+tray_barcode+","+lot_status2+","+group_lot_num+","+
                    holding_time+","+over_times+","+face_code+","+first_panel_flag;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态+母批号+Hold时间+超时时间
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
        }
        catch (Exception ex){
            errorMsg= "异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //1.放板机小板Panel校验
    @RequestMapping(value = "/EapDyLoadPlanSmallPanelCheckAndSave", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyLoadPlanSmallPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/load/EapDyLoadPlanSmallPanelCheckAndSave";
        String transResult="";
        String errorMsg="";
        String meUserTable="a_eap_me_station_user";
        String apsPlanTable="a_eap_aps_plan";
        String meStationFlowTable="a_eap_me_station_flow";
        String meLaserTable="a_eap_me_laser";
        String result="";
        try{
            String station_id=jsonParas.getString("station_id");
            String station_code=jsonParas.getString("station_code");
            String group_lot_num=jsonParas.getString("group_lot_num");
            String panel_barcode=jsonParas.getString("panel_barcode");
            String tray_barcode=jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value=jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value=jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value=jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value=jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批,2为一批多车
            String onecar_multybatch_check_value=jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code=jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String inspect_flag=jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag=jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag=jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code=jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String face_code2=jsonParas.getString("face_code");

            //防止null数据
            String panel_flag="N";
            String user_name="";
            if(group_lot_num==null) group_lot_num="";
            if(panel_barcode==null) panel_barcode="";
            if(tray_barcode==null) tray_barcode="";
            if(ng_auto_pass_value==null) ng_auto_pass_value="";
            if(ng_manual_pass_value==null) ng_manual_pass_value="";
            if(panel_model_value==null) panel_model_value="";
            if(panel_model_value.equals("1")) panel_flag="Y";
            if(onecar_multybatch_value==null) onecar_multybatch_value="";
            if(onecar_multybatch_check_value==null) onecar_multybatch_check_value="";
            if(manual_judge_code==null || manual_judge_code.equals("")) manual_judge_code="0";
            if(inspect_flag==null || inspect_flag.equals("")) inspect_flag="N";
            if(dummy_flag==null || dummy_flag.equals("")) dummy_flag="N";
            if(eap_flag==null || eap_flag.equals("")) eap_flag="N";
            if(eap_ng_code==null || eap_ng_code.equals("")) eap_ng_code="0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if(face_code2==null || face_code2.equals("")) face_code2="1";//默认正面
            Integer face_code2_int=Integer.parseInt(face_code2);
            if(face_code2_int<=0) face_code2_int=1;//默认正面
            long station_id_long=Long.parseLong(station_id);

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                user_name=docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.判断是否存在PLAN状态下group_lot_num,若存在则进行更新为WORK状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            long taskCount =  mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
            Update updateBigData = new Update();
            if(taskCount>0){
                updateBigData.set("group_lot_status", "WORK");
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }

            //3.获取当前计划中或者执行中的子任务
            String plan_id="";
            String task_from="";
            String lot_num="";
            String lot_short_num="";
            Integer lot_index=1;
            String port_code="";
            String material_code="";
            String pallet_num="";
            String pallet_type="";
            String lot_level="";
            Integer fp_index=0;
            Double panel_length=0d;
            Double panel_width=0d;
            Double panel_tickness=0d;
            Integer plan_lot_count=0;
            Integer finish_count=0;
            Integer finish_ok_count=0;
            Integer finish_ng_count=0;
            Integer face_code=face_code2_int;
            Integer eap_face_code=0;
            Integer pallet_use_count=0;
            Integer inspect_finish_count=0;
            String old_plan_status="";
            String item_info="";
            String pnl_infos="";
            String holding_time="";
            String over_times="0";
            String virtu_pallet_num="";
            Integer old_finish_count=0;
            Integer old_finish_ok_count=0;

            String[] lot_status_list=new String[]{"PLAN","WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(!iteratorBigData.hasNext()){
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                plan_id=docItemBigData.getString("plan_id");
                task_from=docItemBigData.getString("task_from");
                lot_num=docItemBigData.getString("lot_num");
                lot_short_num=docItemBigData.getString("lot_short_num");
                lot_index=docItemBigData.getInteger("lot_index");
                port_code=docItemBigData.getString("port_code");
                material_code=docItemBigData.getString("material_code");
                pallet_num=docItemBigData.getString("pallet_num");
                pallet_type=docItemBigData.getString("pallet_type");
                lot_level=docItemBigData.getString("lot_level");
                panel_length=docItemBigData.getDouble("panel_length");
                panel_width=docItemBigData.getDouble("panel_width");
                panel_tickness=docItemBigData.getDouble("panel_tickness");
                plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                finish_count=docItemBigData.getInteger("finish_count");
                finish_ok_count=docItemBigData.getInteger("finish_ok_count");
                finish_ng_count=docItemBigData.getInteger("finish_ng_count");
                eap_face_code=docItemBigData.getInteger("face_code");
                pallet_use_count=docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count=docItemBigData.getInteger("inspect_finish_count");
                old_plan_status=docItemBigData.getString("lot_status");
                item_info=docItemBigData.getString("item_info");
                if(docItemBigData.containsKey("pnl_infos")) pnl_infos=docItemBigData.getString("pnl_infos");
                virtu_pallet_num=pallet_num;
                if(docItemBigData.containsKey("virtu_pallet_num")) virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                old_finish_count=finish_count;
                old_finish_ok_count=finish_ok_count;
                iteratorBigData.close();
            }

            //4.判断是否存在List信息
            Query queryBigDataLaser = new Query();
            queryBigDataLaser.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigDataLaser.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            long laserAllCount =  mongoTemplate.getCollection(meLaserTable).countDocuments(queryBigDataLaser.getQueryObject());

            //5.解析条码
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstDocuments=new ArrayList<>();
            String[] lstPanelBarCode=panel_barcode.split("@",-1);
            JSONArray jaResult=new JSONArray();
            Integer final_ng_code=0;//最终错误代码
            String final_first_panel_flag="N";
            for(String panelBarCodeItem : lstPanelBarCode){
                String station_flow_id=CFuncUtilsSystem.CreateUUID(true);
                Map<String, Object> mapBigDataRow=new HashMap<>();
                //单项判断参数
                Integer panel_ng_code_item=0;
                String panel_ng_msg_item="";
                String panel_status_item="OK";
                Integer panel_index_item=finish_ok_count+1;;
                String offline_flag="N";
                String panel_barcode_item=panelBarCodeItem;
                if(plan_id.equals("")){
                    panel_ng_code_item=8;
                    panel_status_item="NG";
                    offline_flag="Y";
                    final_ng_code=panel_ng_code_item;
                }
                else{
                    if(dummy_flag.equals("Y")){
                        panel_ng_code_item=11;
                        final_ng_code=panel_ng_code_item;
                    }
                    else{
                        if(manual_judge_code.equals("7")){//取消不计数,但是记录过站信息
                            panel_ng_code_item=30;
                            panel_status_item="NG";
                            final_ng_code=panel_ng_code_item;
                        }
                        else{
                            if(panel_model_value.equals("1")){
                                if(ng_manual_pass_value.equals("1")){
                                    panel_ng_code_item=4;
                                    panel_status_item="NG_PASS";
                                    finish_count++;
                                    finish_ok_count++;
                                    final_ng_code=panel_ng_code_item;
                                }
                                else{
                                    if(panel_barcode_item.equals("NoRead") || panel_barcode_item.equals("")){
                                        if(panel_barcode.equals("NoRead")) panel_ng_code_item=2;
                                        else panel_ng_code_item=7;
                                        panel_status_item="NG_PASS";
                                        finish_count++;
                                        finish_ok_count++;
                                    }
                                    else{
                                        //对混批进行判断
                                        if(laserAllCount>0){
                                            queryBigDataLaser = new Query();
                                            queryBigDataLaser.addCriteria(Criteria.where("station_id").is(station_id_long));
                                            queryBigDataLaser.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                            queryBigDataLaser.addCriteria(Criteria.where("laser_barcode").is(panel_barcode_item));
                                            long laserCount =  mongoTemplate.getCollection(meLaserTable).countDocuments(queryBigDataLaser.getQueryObject());
                                            if(laserCount<=0){
                                                panel_ng_code_item=1;
                                                panel_status_item="NG";
                                                if(ng_auto_pass_value.equals("1")){
                                                    panel_status_item="NG_PASS";
                                                    finish_count++;
                                                    finish_ok_count++;
                                                    final_ng_code=4;
                                                }
                                                else{
                                                    finish_count++;
                                                    finish_ng_count++;
                                                    final_ng_code=1;
                                                }
                                            }
                                            else{
                                                finish_count++;
                                                finish_ok_count++;
                                            }
                                        }
                                        else{
                                            finish_count++;
                                            finish_ok_count++;
                                        }
                                    }
                                }
                            }
                            else{
                                panel_barcode_item="";
                                finish_count++;
                                finish_ok_count++;
                            }
                        }
                    }
                }
                //记录数据
                panel_ng_msg_item=planCommonFunc.getPanelNgMsg(panel_ng_code_item);
                mapBigDataRow.put("item_date",item_date);
                mapBigDataRow.put("item_date_val",item_date_val);
                mapBigDataRow.put("station_flow_id",station_flow_id);
                mapBigDataRow.put("station_id",station_id_long);
                mapBigDataRow.put("plan_id",plan_id);
                mapBigDataRow.put("task_from",task_from);
                mapBigDataRow.put("group_lot_num",group_lot_num);
                mapBigDataRow.put("lot_num",lot_num);
                mapBigDataRow.put("lot_short_num",lot_short_num);
                mapBigDataRow.put("lot_index",lot_index);
                mapBigDataRow.put("port_code",port_code);
                mapBigDataRow.put("material_code",material_code);
                mapBigDataRow.put("pallet_num",pallet_num);
                mapBigDataRow.put("pallet_type",pallet_type);
                mapBigDataRow.put("lot_level",lot_level);
                mapBigDataRow.put("fp_index",fp_index);
                mapBigDataRow.put("panel_barcode",panel_barcode_item);
                mapBigDataRow.put("panel_length",panel_length);
                mapBigDataRow.put("panel_width",panel_width);
                mapBigDataRow.put("panel_tickness",panel_tickness);
                mapBigDataRow.put("panel_index",panel_index_item);
                mapBigDataRow.put("panel_status",panel_status_item);
                mapBigDataRow.put("panel_ng_code",panel_ng_code_item);
                mapBigDataRow.put("panel_ng_msg",panel_ng_msg_item);
                mapBigDataRow.put("inspect_flag",inspect_flag);
                mapBigDataRow.put("dummy_flag",dummy_flag);
                mapBigDataRow.put("manual_judge_code",manual_judge_code);
                mapBigDataRow.put("panel_flag",panel_flag);
                mapBigDataRow.put("user_name",user_name);
                mapBigDataRow.put("eap_flag",eap_flag);
                mapBigDataRow.put("tray_barcode",tray_barcode);
                mapBigDataRow.put("face_code",face_code);
                mapBigDataRow.put("offline_flag",offline_flag);
                mapBigDataRow.put("virtu_pallet_num",virtu_pallet_num);
                lstDocuments.add(mapBigDataRow);
                //合成result
                String lot_status2="WORK";
                String first_panel_flag="N";
                if(!plan_id.equals("")){
                    if(finish_ok_count==plan_lot_count) lot_status2="FINISH";
                    if(finish_ok_count>plan_lot_count) lot_status2="FINISH2";
                    if(old_finish_ok_count<=0){
                        if(panel_status_item.equals("OK") || panel_status_item.equals("NG_PASS")){
                            if(finish_ok_count==1 && !dummy_flag.equals("Y")){
                                first_panel_flag="Y";
                                final_first_panel_flag=first_panel_flag;
                            }
                        }
                    }
                }
                String result_item=station_flow_id+","+lot_num+","+pallet_num+","+panel_index_item+","+panel_barcode_item+","+
                        panel_ng_code_item+","+inspect_finish_count+","+tray_barcode+","+lot_status2+","+group_lot_num+","+
                        holding_time+","+over_times+","+face_code+","+first_panel_flag+","+panel_status_item;
                JSONObject jbResult=new JSONObject();
                jbResult.put("result_item",result_item);
                jaResult.add(jbResult);
            }
            JSONObject jbFinalResult=new JSONObject();
            jbFinalResult.put("final_ng_code",final_ng_code);
            jbFinalResult.put("first_panel_flag",final_first_panel_flag);
            jbFinalResult.put("result_list",jaResult);
            result=jbFinalResult.toString();
            if(lstDocuments.size()>0) mongoTemplate.insert(lstDocuments,meStationFlowTable);

            //6.更新标识
            if(!plan_id.equals("")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                //更新完工数量
                String lot_status="WORK";
                if(finish_ok_count>=plan_lot_count) lot_status="FINISH";
                updateBigData = new Update();
                updateBigData.set("lot_status", lot_status);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                if(old_finish_count<=0){
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                if(lot_status.equals("FINISH")){
                    updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }
            //7.更新EAP状态
            if(old_finish_count<=0){
                String writeTags="LoadEap/EapStatus/CurrentLotNum,LoadEap/EapStatus/CurrentLotCount";
                String tagValues=lot_num+"&"+plan_lot_count;
                cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, writeTags, tagValues, false);
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
        }
        catch (Exception ex){
            errorMsg= "Exception:"+ex;
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
