package com.api.eap.project.thailand.guanghe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * (泰国广合)EAP发送流程功能函数
 * 1.[接口]请求叫料
 * 2.[接口]准备生产请求
 * 3.[接口]开始生产报告
 * 4.[接口]板件读码报告
 * 5.[接口]批次完工报告
 * 6.[接口]请求卸料报告
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class EapTlGhSendFlowFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private EapTlGhInterfCommon eapTlGhInterfCommon;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private EapTlGhSendFlowSubFunc eapTlGhSendFlowSubFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.[接口]请求叫料
    public void LoadRequestReport(String station_code, String port_code, String request_mode) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.LoadRequestReport(station_code, port_code, request_mode);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //2.[接口]准备生产报告
    public void LotReadyReport(String station_code, String port_code,
                               String lot_num, String pallet_num, String plan_lot_num,
                               String recipe_id, String vehicleno_eq) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.LotReadyReport(station_code, port_code,
                lot_num, pallet_num, plan_lot_num, recipe_id, vehicleno_eq);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //3.[接口]开始生产报告
    public void LotStartReport(String station_code, String port_code,
                               String lot_num, String pallet_num, String plan_lot_num,
                               String recipe_id, String vehicleno_eq, String requestmode) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.LotStartReport(station_code, port_code,
                lot_num, pallet_num, plan_lot_num, recipe_id, vehicleno_eq, requestmode);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //4.[接口]板件读码报告
    @Async
    public void PanelReadReport(String station_code, String port_code,
                                String lot_num, String panel_barcode, String inspect_flag,
                                String mid_flag, String first_flag, String lot_type,
                                String recipe_id, String panel_result) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.PanelReadReport(station_code, port_code, lot_num, panel_barcode, inspect_flag,
                mid_flag, first_flag, lot_type, recipe_id, panel_result);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //5.[接口]批次完工报告
    public void LotEndReport(String station_code, String port_code,
                             String lot_num, String pallet_num, String firstmode, Integer finish_count,
                             String recipe_id, String vehicleno_eq, JSONArray finish_result,
                             String requestmode, String last_lot_flag,String heatnumber) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.LotEndReport(station_code, port_code, lot_num, pallet_num, firstmode, finish_count,
                recipe_id, vehicleno_eq, finish_result, requestmode, last_lot_flag,heatnumber);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //6.[接口]请求卸料报告
    public Integer UnLoadRequestReport(String station_code, String port_code, String pre_call, String unload_type, JSONArray arrlist, String requestmode) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.UnLoadRequestReport(station_code, port_code, pre_call, unload_type, arrlist, requestmode);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        JSONObject jsonResult = JSONObject.parseObject(responseParas);
        Integer rtn_code = jsonResult.getInteger("code");
        if (!successFlag && rtn_code != 501) {
            throw new Exception(message);
        }
        return rtn_code;
    }

    //7.[接口]设备读板件ID请求批次信息
    public void PanelInfoRequestReport(String station_code, String panel_barcode) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.PanelInfoRequestReport(station_code, panel_barcode);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //8.[接口]读载具ID报告事件
    public void CarriertLotInfoRequest(String station_code, String pallet_num, String port_code) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.CarriertLotInfoRequest(station_code, pallet_num, port_code);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //8.[接口]设备空闲端口数上报--配套机
    public void PortQtyReport(String station_code, JSONArray port_list) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.PortQtyReport(station_code, port_list);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //9.[接口]配套外层批完工报告--配套机
    public void LayerLotEndReport(String station_code, String port_code, String recipe_id,
                                  String heatnumber, String layerlot, String setsqty,
                                  String vehicleno_eq, JSONArray ink_lot_list) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapTlGhSendFlowSubFunc.LayerLotEndReport(station_code, port_code, recipe_id,
                heatnumber, layerlot, setsqty, vehicleno_eq, ink_lot_list);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }
}
