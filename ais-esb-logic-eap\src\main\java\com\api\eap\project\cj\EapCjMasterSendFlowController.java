package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 定颖EAP发送流程数据定义接口
 * 1.PortStatusChangeReport:[接口]端口状态发生变化推送到EAP
 * 2.CarrierIDReport:[接口]载具扫描上报验证
 * 3.CarrierStatusReport:[接口]开始/结束/取消/终止投板（收板）时上报
 * 4.CCDDataReport:[接口]CCD读码上报
 * 5.FetchOutReport:[接口]每放一片板需要上报（针对放板机）
 * 6.StoreInReport:[接口]每收一片板需要上报（针对收板机）
 * 7.JobCountReport:[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
 * 8.PanelDataUploadReport:[接口]每片Panel收放台車事件上報
 * 9.WIPTrackingReport:[接口]每一lot完板上报
 * 10.CarrierDataUploadReport:[接口]最后全部任务完板后上报
 * 11.JobDataCreateModifyReport:[接口]人工操作界面拿走一片板子(手工录入)
 * 12.JobRemoveRecoveryReport:[接口]人工操作界面放回板子(手工录入)
 * 13.EachPositionReport:[接口]暂存位置信息动态上报
 * 14.EachPanelDataDownLoad:[上下游接口]当片信息(上游传递给下游)
 * 15.BcOffLineLotDownLoad:[上下游接口]BC离线工单信息下发
 * 16.BcOffLineLoginInDownLoad:[上下游接口]BC离线同步登入
 * 17.LotFinishDownLoad:[上下游接口]传递到下游设备结批信息
 * 18.ProductionModeDownLoad:[上下游接口]首件状态下发
 * 19.HeartBeatCheckDownLoad:[上下游接口]检测下游心跳状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/dy/main/interf/send")
public class EapCjMasterSendFlowController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapCjMasterSendFlowFunc eapCjMasterSendFlowFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private EapCjInterfCommon eapCjInterfCommon;

    //1.[接口]端口状态发生变化推送到EAP
    @RequestMapping(value = "/PortStatusChangeReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyPortStatusChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/PortStatusChangeReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String port_status = jsonParas.getString("port_status");//UDCM、LDRQ、WAIT、PROC、ABOT、CANE、PREN、UDRQ、
            String pallet_num = jsonParas.getString("pallet_num");
            Integer left_count = jsonParas.getInteger("left_count");
            if (pallet_num == null) pallet_num = "";
            if (left_count == null) left_count = 0;

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String allow_sb_emptypallet_flag = "N";
            String user_name = "";
            String port_ng_flag = "N";
            String pallet_reload_flag = "N";
            if (port_status.equals("LDRQ")) pallet_reload_flag = "Y";
            if (port_status.equals("UDCM") || port_status.equals("LDRQ")) {
                pallet_num = "";
                left_count = 0;
            }

            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //3.判断是否为WAIT状态,若为WAIT状态,判断是否参数要求控制有无PANEL
            if (port_status.equals("WAIT")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                Integer panel_model = -1;
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    panel_model = docItemBigData.getInteger("panel_model");
                    iteratorBigData.close();
                }
                if (panel_model >= 0) {
                    errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr, "Ais", "AisConfig", "NgPanelPassFlag", "EAP", String.valueOf(panel_model), true);
                    if (!errorMsg.equals("")) {
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
            }

            //端口状态上报
            eapCjMasterSendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, port_status, allow_sb_emptypallet_flag, pallet_num, String.valueOf(left_count), user_name, port_ng_flag, pallet_reload_flag);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "端口状态上报EAP异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]载具扫描上报验证
    @RequestMapping(value = "/CarrierIDReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCarrierIDReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/CarrierIDReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String mePalletQueueTable = "a_eap_me_pallet_queue";
        try {
            String result = "OK";
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String production_mode = jsonParas.getString("production_mode");//制程分类
            String ccdNo = jsonParas.getString("ccd_no");
            if (production_mode == null || production_mode.equals("")) production_mode = "Default";
            String pre_pallet_scan = jsonParas.getString("pre_pallet_scan");//是否提前扫描载具
            if (pre_pallet_scan == null || pre_pallet_scan.equals("")) pre_pallet_scan = "N";
            String read_type = jsonParas.getString("read_type");

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String user_name = "";

            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //查询是否为提前扫描
            String ZanCunCcdFlag = "0";
            if (station_attr.equals("Load")) {
                ZanCunCcdFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr, "Ais", "AisConfig", "ZanCunCcdFlag");
                if (ZanCunCcdFlag == null || ZanCunCcdFlag.equals("")) {
                    errorMsg = "读取点位{ZanCunCcdFlag}失败";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }

            //3.若是来自暂存,但是非扫描工位
            if (ZanCunCcdFlag.equals("1") && !pre_pallet_scan.equals("Y")) {
                //1.查找载具是否最后一批状态
                Integer rtn_fuder = -1;
                String lot_num = "";
                String i_id = "";
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("pallet_num").is(pallet_num));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(mePalletQueueTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    i_id = docItemBigData.getObjectId("_id").toString();
                    lot_num = docItemBigData.getString("lot_num");
                    rtn_fuder = docItemBigData.getInteger("rtn_fuder");//1最后一车标识,0不是最后一车标识
                    iteratorBigData.close();
                }
                if (rtn_fuder >= 0 && !lot_num.equals("")) {
                    queryBigData = new Query();
                    String[] group_lot_status_list2 = new String[]{"WAIT", "PLAN", "WORK"};
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list2));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    Update updateBigData2 = new Update();
                    updateBigData2.set("pdb_rule", rtn_fuder);
                    mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
                }
                //成功后发送LDCM
//                eapCjMasterSendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "LDCM", "N", pallet_num, "0", user_name, "N", "N");

                //删除数据
                if (!i_id.equals("")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("_id").lte(new ObjectId(i_id)));
                    mongoTemplate.remove(queryBigData, mePalletQueueTable);
                }

                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //4.提前扫描，需要判断是否最后一车
            if (ZanCunCcdFlag.equals("1") && pre_pallet_scan.equals("Y")) {
                String old_lot_num = "";
                String new_lot_num = "";
                String first_scan_flag = "N";
                Integer old_rtn_fuder = 0;
                String read_type2 = "B";
                //4.1 读取当前批次号
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(mePalletQueueTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    old_lot_num = docItemBigData.getString("lot_num");
                    old_rtn_fuder = docItemBigData.getInteger("rtn_fuder");//1最后一车标识,0不是最后一车标识
                    iteratorBigData.close();
                }
                if (old_rtn_fuder == 1) {
                    first_scan_flag = "Y";
                    read_type2 = "S";
                } else {
                    if (old_lot_num.equals("")) {
                        first_scan_flag = "Y";
                        read_type2 = "S";
                    }
                }

                //
                JSONObject response_body = eapCjMasterSendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, station_attr, 0, production_mode, "N", null, first_scan_flag, read_type2);
                if (response_body == null) {
                    result = "NG";
                } else {
                    Integer interf_rtn_fuder = 0;
                    //先获取参数中的rtn_fuder值
                    if (response_body.containsKey("rtn_fuder")) {
                        try {
                            interf_rtn_fuder = response_body.getInteger("rtn_fuder");
                            if (interf_rtn_fuder == null) interf_rtn_fuder = 0;
                        } catch (Exception ex) {
                        }
                    }

                    //获取到lot_id信息
                    if (response_body.containsKey("lot_infos")) {
                        JSONObject lot_infos = response_body.getJSONObject("lot_infos");
                        if (lot_infos != null) {
                            if (lot_infos.containsKey("lot")) {
                                JSONArray lot_list = lot_infos.getJSONArray("lot");
                                if (lot_list != null && lot_list.size() > 0) {
                                    JSONObject first_lot_obj = lot_list.getJSONObject(0);
                                    if (first_lot_obj != null && first_lot_obj.containsKey("lot_id")) {
                                        new_lot_num = first_lot_obj.getString("lot_id");
                                    }
                                }
                            }
                        }
                    }

                    if (!first_scan_flag.equals("Y")) {
                        if (!old_lot_num.equals(new_lot_num)) {
                            result = "NG";
                            //记录到CIM消息
                            String cimMessage = "当前载具{" + pallet_num + "}对应工单{" + new_lot_num + "}与当前作业工单{" + old_lot_num + "}不一致,不允许上机";
                            opCommonFunc.SaveCimMessage(station_id_long, "1", "-1", "EAP", cimMessage,5);
                        }
                    }

                    if (result.equals("OK")) {
                        //增加载具队列数据
                        Map<String, Object> mapPalletQueueRow = new HashMap<>();
                        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                        mapPalletQueueRow.put("item_date", item_date);
                        mapPalletQueueRow.put("item_date_val", item_date_val);
                        mapPalletQueueRow.put("pallet_queue_id", CFuncUtilsSystem.CreateUUID(true));
                        mapPalletQueueRow.put("station_id", station_id_long);
                        mapPalletQueueRow.put("lot_num", new_lot_num);
                        mapPalletQueueRow.put("pallet_num", pallet_num);
                        mapPalletQueueRow.put("rtn_fuder", interf_rtn_fuder);
                        mongoTemplate.insert(mapPalletQueueRow, mePalletQueueTable);
                    }
                }
                //CCDDataReport:[接口]CCD读码上报
                eapCjMasterSendFlowFunc.CCDDataReport(station_code, pallet_num, ccdNo);

                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //其他
            JSONObject response_body2 = eapCjMasterSendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, station_attr, 0, production_mode, "N", null, "Y", read_type);
            if (response_body2 == null) result = "NG";

            //成功后发送LDCM
//            eapCjMasterSendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "LDCM", "N", pallet_num, "0", user_name, "N", "N");

            //1.CCDDataReport:[接口]CCD读码上报
            eapCjMasterSendFlowFunc.CCDDataReport(station_code, pallet_num, ccdNo);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "载具扫描上报验证异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.[接口]开始/结束/取消/终止上报
    @RequestMapping(value = "/CarrierStatusReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCarrierStatusReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/main/interf/send/CarrierStatusReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String task_status = jsonParas.getString("task_status");
            String pallet_num = jsonParas.getString("pallet_num");
            String lot_list_str = jsonParas.getString("lot_list");
            String sqlStation = "select " + "COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, request, apiRoutePath);
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            JSONArray lot_list = JSONArray.parseArray(lot_list_str);
            eapCjMasterSendFlowFunc.CarrierStatusReport(station_code, pallet_num, task_status, lot_list, "0", station_attr, port_code);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "开始/结束/取消/终止上报异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.综合下游下发任务以及其他任务相关信息
    @RequestMapping(value = "/DownDeviceTaskInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyDownDeviceTaskInfo(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/DownDeviceTaskInfo";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        MongoCursor<Document> iteratorBigData = null;
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String sys_model = jsonParas.getString("sys_model");
            String group_lot_num = jsonParas.getString("group_lot_num");
            //远程模式不需要下发到下游
            if (sys_model.equals("1")) {
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String lot_num = docItemBigData.getString("lot_num");
                String lot_short_num = docItemBigData.getString("lot_short_num");
                String material_code = docItemBigData.getString("material_code");
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                String item_info = docItemBigData.getString("item_info");
                JSONArray ja = new JSONArray();
                if (item_info != null && !item_info.equals("")) {
                    ja = JSONArray.parseArray(item_info);
                }
                eapCjMasterSendFlowFunc.BcOffLineLotDownLoad(station_code, lot_num, material_code, plan_lot_count, lot_short_num, ja);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            try {
                if (iteratorBigData != null) {
                    if (iteratorBigData.hasNext()) iteratorBigData.close();
                }
            } catch (Exception exx) {
            }
            errorMsg = "综合下游下发任务信息异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

//    @RequestMapping(value = "/JobCountReport", method = {RequestMethod.POST, RequestMethod.GET})
//    public String EapDyPanelResultReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
//        String apiRoutePath = "eap/project/dy/main/interf/send/JobCountReport";
//        String transResult = "";
//        String errorMsg = "";
//        String meStationFlowTable = "a_eap_me_station_flow";
//        MongoCursor<Document> iteratorBigData = null;
//        try {
//            Query query = new Query();
//            query.addCriteria(Criteria.where("group_lot_num").is(jsonParas.getString("group_lot_num")));
//            List<Document> documents = mongoTemplate.find(query, Document.class, meStationFlowTable);
//            Map<String, List<Document>> groupByInOrOut = documents.stream().collect(Collectors.groupingBy(g -> g.getString("in_or_out")));
//            int inCount = groupByInOrOut.containsKey("in") ? groupByInOrOut.get("in").size() : 0;
//            int out = groupByInOrOut.containsKey("out") ? groupByInOrOut.get("out").size() : 0;
//            eapCjMasterSendFlowFunc.JobCountReport(jsonParas.getString("station_code"), stainCount - out, jsonParas.getString("port_code"));
//            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
//        } catch (Exception ex) {
//            errorMsg = "板件传递到下游综合异常:" + ex.getMessage();
//            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
//        }
//        return transResult;
//    }

    //6.板件传递到下游综合
    @RequestMapping(value = "/DownDevicePanelReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyDownDevicePanelReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/DownDevicePanelReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String panel_result = jsonParas.getString("panel_result");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String onff_line = jsonParas.getString("onff_line");//在线离线
            String panel_model = jsonParas.getString("panel_model");//有无Panel模式
            String inspect_flag = jsonParas.getString("inspect_flag");//是否首件
            String[] panelListParas = panel_result.split(",", -1);
            String station_flow_id = panelListParas[0];
            String lot_num = panelListParas[1];
            Integer panel_index = Integer.parseInt(panelListParas[3]);
            String panel_barcode = panelListParas[4];
            Integer panel_ng_code = Integer.parseInt(panelListParas[5]);
            Integer inspect_finish_count = Integer.parseInt(panelListParas[6]);
            String lot_status = panelListParas[8];
            String group_lot_num = panelListParas[9];
            String slot_no = String.format("%03d", panel_index);
            String panel_status = "OK";
            //0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (panel_ng_code == 4) panel_status = "NG_PASS";
            else {
                if (panel_ng_code > 0) panel_status = "NG";
            }
            if (panel_model.equals("1")) {
                if (panel_barcode == null || panel_barcode.equals("") || panel_barcode.equals("FFFFFFFF"))
                    panel_barcode = "NoRead";
            }
            eapCjMasterSendFlowFunc.EachPanelDataDownLoad(station_code, lot_num, panel_barcode, panel_status);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "板件传递到下游综合异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //7.EAP子批次完板上报
    @RequestMapping(value = "/WIPTrackingReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String WIPTrackingReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/main/interf/send/WIPTrackingReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        MongoCursor<Document> iteratorBigData = null;
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String lot_num = jsonParas.getString("lot_num");
            String offline_flag = jsonParas.getString("offline_flag");//在线、离线模式
            if (offline_flag == null) offline_flag = "N";
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String prod_mode = jsonParas.getString("prod_mode");
            String nw_value = jsonParas.getString("nw_value");//内外层代码
            String short_count = jsonParas.getString("short_count");//少片数量
            if (short_count == null || short_count.equals("")) short_count = "0";
            String noread_count = jsonParas.getString("noread_count");//NoRead数量
            if (noread_count == null || noread_count.equals("")) noread_count = "0";
            String local_flag = "Y";
            if (sys_model.equals("1")) local_flag = "N";

            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OnOffLineTag = "";
            String OnOffLine = "";//在线离线
            if (aisMonitorModel.equals("AIS-PC")) {
                OnOffLineTag = station_attr + "Plc/PlcConfig/OnOffLine";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OnOffLineTag = station_attr + "Plc_" + station_code + "/PlcConfig/OnOffLine";
            } else {
                errorMsg = "AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, OnOffLineTag);
            if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                for (int i = 0; i < jsonArrayTag.size(); i++) {
                    JSONObject jbItem = jsonArrayTag.getJSONObject(i);
                    String tag_key = jbItem.getString("tag_key");
                    String tag_value = jbItem.getString("tag_value");
                    if (tag_key.equals(OnOffLineTag)) OnOffLine = tag_value;
                }
            }
            if (!OnOffLine.equals("1")) offline_flag = "Y";
            else offline_flag = "N";

            //新增一批多车多级
            String out_code = jsonParas.getString("out_code");
            if (out_code == null || out_code.equals("")) out_code = "-1";

            //根据母批查询信息
            Integer sum_plan_count = 0;
            Integer sum_finish_count = 0;
            Integer sum_finish_ok_count = 0;
            String material_code_P001 = "";
            String lot_version_P002 = "";
            Integer first_plan_count_P004 = 0;
            Integer lot_finish_count = 0;
            String lot_short_num = "";
            String attribute1_P005 = "";
            String attribute2_P006 = "";
            String user_name = "";
            String dept_id = "";
            String shift_id = "";
            String task_start_time = CFuncUtilsSystem.GetNowDateTime("");
            String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
            String item_info = "";
            JSONArray item_attr_list = null;
            String splitseq = "0";
            String lastSplit = "1";
            if (prod_mode == null || prod_mode.equals("")) prod_mode = "0";
            JSONObject attr_else = null;

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                String lot_num2 = docItemBigData.getString("lot_num");
                sum_plan_count += plan_lot_count;
                sum_finish_count += finish_count;
                sum_finish_ok_count += finish_ok_count;
                if (lot_num2.equals(lot_num)) {
                    material_code_P001 = docItemBigData.getString("material_code");
                    lot_version_P002 = docItemBigData.getString("lot_level");
                    first_plan_count_P004 = plan_lot_count;
                    lot_finish_count = finish_ok_count;
                    lot_short_num = docItemBigData.getString("lot_short_num");
                    item_info = docItemBigData.getString("item_info");
                    attribute1_P005 = docItemBigData.getString("attribute1");
                    attribute2_P006 = docItemBigData.getString("attribute2");
                    if (docItemBigData.containsKey("attribute3")) {
                        String attribute_else = docItemBigData.getString("attribute3");
                        if (attribute_else != null && !attribute_else.equals("")) {
                            attr_else = JSONObject.parseObject(attribute_else);
                        }
                    }
                    task_start_time = docItemBigData.getString("task_start_time");
                    task_end_time = docItemBigData.getString("task_end_time");
                    if (task_start_time == null || task_start_time.equals("")) {
                        task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    }
                    if (task_end_time == null || task_end_time.equals("")) {
                        task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    }
                }
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前用户
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                dept_id = docItemBigData.getString("dept_id");
                shift_id = docItemBigData.getString("shift_id");
                iteratorBigData.close();
            }

            JSONObject jsonObject = eapCjInterfCommon.GetParamsMapping();
            //3.处理Item_Info
            if (item_info != null && !item_info.equals("")) {
                for (int j = 1; j <= 200; j++) {
                    String SSource = "S" + String.format("%03d", j);
                    String TTarget = "T" + String.format("%03d", j + 1);
                    if (jsonObject != null && jsonObject.containsKey(TTarget)) {
                        SSource = jsonObject.getString(TTarget);
                    }
                    item_info = item_info.replace(SSource, TTarget);
                }
                item_attr_list = JSONArray.parseArray(item_info);
                if (nw_value == null || nw_value.equals("")) nw_value = "0";
                JSONObject nw_obj = new JSONObject();
                nw_obj.put("item_id", "T001");
                nw_obj.put("item_value", nw_value);
                item_attr_list.add(nw_obj);
            }

            if (!eapCjInterfCommon.CheckDyVersion(3)) {
                if (item_attr_list == null) item_attr_list = new JSONArray();
                for (int k = item_attr_list.size() - 1; k >= 0; k--) {
                    JSONObject jbItemAttr = item_attr_list.getJSONObject(k);
                    String item_id = jbItemAttr.getString("item_id");
                    if (item_id.equals("T030") || item_id.equals("T031")) {
                        item_attr_list.remove(k);
                    }
                }
                JSONObject T030 = new JSONObject();
                T030.put("item_id", "T030");
                T030.put("item_value", noread_count);
                item_attr_list.add(T030);
                JSONObject T031 = new JSONObject();
                T031.put("item_id", "T031");
                T031.put("item_value", short_count);
                item_attr_list.add(T031);
            }

            //用于DataUpLoad参数
            Integer lot_left_count = 0;
            JSONArray carr_infos = new JSONArray();
            JSONObject jbCarr = new JSONObject();
            JSONObject jbLot = new JSONObject();
            if (!eapCjInterfCommon.CheckDyVersion(3)) {
                lot_left_count = first_plan_count_P004;
            } else {
                if (station_attr.equals("Load")) {
                    lot_left_count = first_plan_count_P004 - lot_finish_count;
                    if (lot_left_count < 0) lot_left_count = 0;
                } else {
                    lot_left_count = lot_finish_count;
                }
            }
            jbLot.put("lot_id", lot_num);
            jbLot.put("lot_count", lot_left_count);
            jbCarr.put("lot", jbLot);
            JSONObject pnl_infos = new JSONObject();
            JSONArray jaSlot = new JSONArray();
            //查询明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(100).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String panel_barcode = docItemBigData.getString("panel_barcode");
                Integer panel_index = docItemBigData.getInteger("panel_index");
                JSONObject jbDetail = new JSONObject();
                jbDetail.put("slot_no", String.format("%03d", panel_index));
                jbDetail.put("panel_id", panel_barcode);
                jaSlot.add(jbDetail);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            pnl_infos.put("slot", jaSlot);
            jbCarr.put("pnl_infos", pnl_infos);
            carr_infos.add(jbCarr);

            //判断是否发送WIP数据
            Boolean isSendWip = true;
            if (OneCarMultyLotFlag.equals("2")) {
                Integer out_code_int = Integer.parseInt(out_code);
                if (out_code_int > 0 && out_code_int < 4) {
                    if (sum_plan_count > sum_finish_ok_count) isSendWip = false;
                }
            }

            //上报WIP【异步】
            if (isSendWip) {
                eapCjMasterSendFlowFunc.WIPTrackingReport(Long.parseLong(station_id), station_code, user_name, dept_id, shift_id, task_start_time, task_end_time, lot_num, sum_plan_count, first_plan_count_P004, lot_finish_count, material_code_P001, lot_short_num, lot_version_P002, prod_mode, attribute1_P005, attribute2_P006, item_attr_list, offline_flag, local_flag, attr_else);
            }
            //上报DataUpLoad【异步】
//            eapCjMasterSendFlowFunc.CarrierDataUploadReport(Long.parseLong(station_id), station_code, pallet_num, "0", splitseq, lastSplit, carr_infos, offline_flag, local_flag);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            try {
                if (iteratorBigData != null) {
                    if (iteratorBigData.hasNext()) iteratorBigData.close();
                }
            } catch (Exception exx) {
            }
            errorMsg = "EAP子批次完板上报异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //8.通知下游批次结束
    @RequestMapping(value = "/DownDeviceSubLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyDownDeviceSubLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/DownDeviceSubLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String panel_result = jsonParas.getString("panel_result");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String[] panelListParas = panel_result.split(",", -1);
            String station_flow_id = panelListParas[0];
            String lot_num = panelListParas[1];
            String lot_status = panelListParas[8];
            String group_lot_num = panelListParas[9];

            //根据批次查询信息
            Integer lot_finish_count = 0;
            String lot_short_num = "";

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lot_finish_count = docItemBigData.getInteger("finish_ok_count");
                if (station_attr.equals("UnLoad")) {
                    pallet_num = docItemBigData.getString("pallet_num");
                }
                iteratorBigData.close();
            }

            //通知下游结批信号
            eapCjMasterSendFlowFunc.LotFinishDownLoad(station_code, lot_num, lot_finish_count, lot_short_num);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "通知下游批次结束异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //10.母批完板通知下游批次结束
    @RequestMapping(value = "/DownDeviceGroupLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyDownDeviceGroupLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/DownDeviceGroupLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        try {
            JSONObject jsonParas2 = jsonParas;
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String lot_list = jsonParas.getString("lot_list");
            String local_flag = "Y";
            if (sys_model.equals("1")) local_flag = "N";
            //针对lot_list进行处理
            JSONArray lotArray = JSONArray.parseArray(lot_list);
            if (lotArray != null && lotArray.size() > 0) {
                for (int i = 0; i < lotArray.size(); i++) {
                    JSONObject jbItem = lotArray.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    String lot_num = jbItem.getString("lot_num");
                    String pallet_num = jbItem.getString("pallet_num");
                    Integer plan_lot_count = jbItem.getInteger("plan_lot_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    if (plan_lot_count > finish_ok_count) {
                        //说明未完成工单,需要执行WIP动作
                        String panel_result = "," + lot_num + "," + pallet_num + ",0,,0,0,,WORK," + group_lot_num;
                        jsonParas2.put("panel_result", panel_result);
                        jsonParas2.put("pallet_num", pallet_num);
                        EapDyDownDeviceSubLotFinishReport(jsonParas2, request);
                    }
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "母批完板通知下游批次结束异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //暂存每片上报
    @RequestMapping(value = "/EachPositionReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyEachPositionReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/EachPositionReport";
        String transResult = "";
        String errorMsg = "";
        try {
            JSONObject jsonParas2 = jsonParas;
            String station_code = jsonParas.getString("station_code");
            JSONArray jaList = jsonParas.getJSONArray("panel_list");
            eapCjMasterSendFlowFunc.EachPositionReport(station_code, jaList);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "暂存每片上报异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //ReceiveReport 进片
//    @RequestMapping(value = "/ReceiveReport", method = {RequestMethod.POST, RequestMethod.GET})
//    public String ReceiveReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
//        String apiRoutePath = "eap/project/dy/main/interf/send/ReceiveReport";
//        String transResult = "";
//        String errorMsg = "";
//        String userId = "-1";
//        String meUserTable = "a_eap_me_station_user";
//        try {
//            String lot_num = jsonParas.getString("lot_num");
//            String panel_barcode = jsonParas.getString("panel_barcode");
//            String station_code = jsonParas.getString("station_code");
//            String panel_index = jsonParas.getString("panel_index");
//            Long station_id = jsonParas.getLong("station_id");
//            String user_name = "";
//            Query queryBigData = new Query();
//            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
//            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
//            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
//            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
//            if (iteratorBigData.hasNext()) {
//                Document docItemBigData = iteratorBigData.next();
//                user_name = docItemBigData.getString("user_name");
//                iteratorBigData.close();
//            }
//            //上报
//            eapCjMasterSendFlowFunc.ReceiveReport(station_code, lot_num, panel_barcode, panel_index, user_name);
//            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
//        } catch (Exception ex) {
//            errorMsg = "CimMode模式变化后事件上报异常:" + ex.getMessage();
//            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
//        }
//        return transResult;
//    }

    //SendOutReport 出片
//    @RequestMapping(value = "/SendOutReport", method = {RequestMethod.POST, RequestMethod.GET})
//    public String SendOutReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
//        String apiRoutePath = "eap/project/dy/main/interf/send/SendOutReport";
//        String transResult = "";
//        String errorMsg = "";
//        String userId = "-1";
//        String meUserTable = "a_eap_me_station_user";
//        try {
//            String lot_num = jsonParas.getString("lot_num");
//            String panel_barcode = jsonParas.getString("panel_barcode");
//            String station_code = jsonParas.getString("station_code");
//            String panel_index = jsonParas.getString("panel_index");
//            Long station_id = jsonParas.getLong("station_id");
//            String user_name = "";
//            Query queryBigData = new Query();
//            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
//            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
//            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
//            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
//            if (iteratorBigData.hasNext()) {
//                Document docItemBigData = iteratorBigData.next();
//                user_name = docItemBigData.getString("user_name");
//                iteratorBigData.close();
//            }
//            //上报
//            eapCjMasterSendFlowFunc.SendOutReport(station_code, lot_num, panel_barcode, panel_index, user_name);
//            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
//        } catch (Exception ex) {
//            errorMsg = "CimMode模式变化后事件上报异常:" + ex.getMessage();
//            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
//        }
//        return transResult;
//    }
}
