package com.api.dcs.project.fjrm.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.dcs.core.wms.DcsCarCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 满框出库路线创建
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
@Slf4j
public class DcsFjrmCarMainStockOutFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsCarCommonFunc dcsCarCommonFunc;
    @Autowired
    private DcsFjrmIntefTaskCommonFunc dcsFjrmIntefTaskCommonFunc;//主任务
    @Autowired
    private DcsFjrmStockCommonFunc dcsFjrmStockCommonFunc;//库位
    @Autowired
    private DcsFjrmWharfCommonFunc dcsFjrmWharfCommonFunc;//码头
    @Autowired
    private DcsFjrmWasteBoxCommonFunc dcsFjrmWasteBoxCommonFunc;//废料框
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //创建任务与路线规划
    public Map<String, Object> CreateTaskRoute(String userID, HttpServletRequest request, String apiRoutePath,
                                  String ware_house, String car_code) throws Exception{
        String errorMsg = "";
        String task_type="FULL_OUT_TASK";//FULL_OUT_TASK-满框出库任务  -  拆分 SPLIT_FULL_OUT_TASK
        Map<String, Object> mapResult=new HashMap<>();
        mapResult.put("passFlag",false);//是否允许越过此调度
        mapResult.put("errorMsg",errorMsg);//当前调度错误信息

        //1.判断天车是否存在
        Map<String, Object> mapCar= dcsCarCommonFunc.GetCarInfo(car_code);
        if(mapCar==null){
            throw new Exception("天车编码{"+car_code+"}不存在天车基础数据中");
        }
        //2.查询是否存在-空废料框出库-任务
        Map<String, Object> mapCarTask=dcsFjrmIntefTaskCommonFunc.GetIntefTaskInfoByTaskType(ware_house,task_type);
        if(mapCarTask==null){
            errorMsg="未找到任务类型为{"+task_type+"}的满框出库任务";
            mapResult.put("passFlag",true);
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String task_id=mapCarTask.get("task_id").toString();
        //String task_num=mapCarTask.get("task_num").toString();
        String task_from=mapCarTask.get("task_from").toString();
        String task_way=mapCarTask.get("task_way").toString();
        //String from_stock_code=mapCarTask.get("from_stock_code").toString();
        //String to_stock_code=mapCarTask.get("to_stock_code").toString();
        String lot_num=mapCarTask.get("lot_num").toString();
        String material_code=mapCarTask.get("material_code").toString();
        Float width=Float.valueOf(mapCarTask.get("width").toString());//总重量(KG)
        Float split_width=Float.valueOf(mapCarTask.get("split_width").toString());//已拆分总重量(KG)
        //Float error_min=Float.valueOf(mapCarTask.get("error_min").toString());
        //Float error_max=Float.valueOf(mapCarTask.get("error_max").toString());
        //String wharf_code=mapCarTask.get("wharf_code").toString();
        //String waste_box_code=mapCarTask.get("waste_box_code").toString();

        //3.查找来源库位
        Map<String, Object> mapStockFrom= dcsFjrmStockCommonFunc.GetKwStockOut(ware_house,lot_num,material_code);
        if(mapStockFrom==null){
            errorMsg="任务类型为{"+task_type+"}的满框出库任务,未能找到可以出库的库位和库存";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String from_stock_code=mapStockFrom.get("stock_code").toString();
        Integer from_location_x=Integer.parseInt(mapStockFrom.get("location_x").toString());
        Integer from_location_y=Integer.parseInt(mapStockFrom.get("location_y").toString());
        Integer from_location_z=Integer.parseInt(mapStockFrom.get("location_z").toString());
        Integer from_stock_count=Integer.parseInt(mapStockFrom.get("stock_count").toString());
        String waste_box_code=mapStockFrom.get("waste_box_code").toString();
        Float stock_width=Float.parseFloat(mapStockFrom.get("stock_width").toString());//总重量(KG)
        Float differ_width=0f;//总重量差(KG)
        String task_status="WORK";//任务状态(PLAN-计划、WORK-进行中、FINISH-完成、CANCEL-取消)
        if(stock_width>=width){
            split_width=split_width+width;
            width=0f;
            task_status="FINISH";
        }
        else if(stock_width<width){
            split_width=split_width+stock_width;
            width=width-stock_width;
            task_status="WORK";
        }

        //4.查找废料框信息-废料框高度
        Map<String, Object> mapWasteBox= dcsFjrmWasteBoxCommonFunc.GetWasteBoxInfoByWasteBoxCode(waste_box_code);
        Float waste_box_height=Float.parseFloat(mapWasteBox.get("waste_box_height").toString());//高度

        //5.选择：码头
        String sqlWharfSel="select a.wharf_id," +
                "COALESCE(a.wharf_code,'') wharf_code," +
                "COALESCE(a.wharf_des,'') wharf_des," +
                "COALESCE(a.location_x,0) location_x," +
                "COALESCE(a.location_y,0) location_y," +
                "COALESCE(a.location_z,0) location_z," +
                "COALESCE(b.rgv_code,'') rgv_code, " +
                "COALESCE(b.ready_tag,'') ready_tag, " +
                "COALESCE(b.location_tag,'') location_tag " +
                "from b_dcs_fmod_wharf a left join b_dcs_fmod_rgv b " +
                "on a.rgv_code=b.rgv_code " +
                "where a.enable_flag='Y' "+
                "and a.lock_flag='N' "+
                "and (a.wharf_type='OUT' or a.wharf_type='IN_OUT') "+
                "and ware_house='"+ware_house+"' " +
                "order by wharf_order asc ";
        List<Map<String, Object>> wharfList=cFuncDbSqlExecute.ExecSelectSql(userID,sqlWharfSel,
                false,request,apiRoutePath);
        Map<String, Object> mapSplitTask=null; //拆分任务
        String to_stock_code = "";//起始库位=码头
        Integer to_location_x=0;//X坐标
        Integer to_location_y=0;//Y坐标
        Integer to_location_z=0;//Z坐标
        if(wharfList!=null && wharfList.size()>0){
            for(Map<String, Object> mapItem : wharfList){
                to_stock_code=mapItem.get("ready_tag").toString();
                to_location_x=Integer.parseInt(mapStockFrom.get("location_x").toString());
                to_location_y=Integer.parseInt(mapStockFrom.get("location_y").toString());
                to_location_z=Integer.parseInt(mapStockFrom.get("location_z").toString());

                String ready_tag=mapItem.get("ready_tag").toString();//读取点位:RGV就绪:可用 空闲
                //String reach_tag=mapItem.get("reach_tag").toString();//读取点位点位:RGV到达上料位置
                String location_tag=mapItem.get("location_tag").toString();//写入点位:通知RGV到取料位置
                //5.1 读取到SCADA
                String ready_tag_value="";//读取点位的值
                if (!ready_tag.equals("")) {
                    JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation("P01",ready_tag);
                    if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                        JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                        ready_tag_value = jbItem.getString("tag_value");
                    } else {
                        errorMsg="任务类型为{"+task_type+"}的满框出库任务,未读取到标签{" + ready_tag + "}对应值";
                        mapResult.put("errorMsg",errorMsg);
                        return mapResult;
                    }
                }
                //判断是否空闲
                if(ready_tag_value.equals("0")){
                    continue;
                }
                //5.2 写入到SCADA
                String tagOnlyKeyList = location_tag;
                String tagValueList = "1";
                errorMsg = cFuncUtilsCellScada.WriteTagByStation(userID, "P01", tagOnlyKeyList, tagValueList, true);
                if (!errorMsg.equals("")) {
                } else {
                    errorMsg="任务类型为{"+task_type+"}的满框出库任务,写入到标签{" + ready_tag + "}对应值";
                    mapResult.put("errorMsg",errorMsg);
                    return mapResult;
                }
                break;
            }
            //5.3 修改 源任务
            dcsFjrmIntefTaskCommonFunc.UpdIntefTask(task_id,width,split_width,task_status);
            //5.4 拆分成新任务
            mapSplitTask=dcsFjrmIntefTaskCommonFunc.SplitIntefTask(task_id,stock_width,from_stock_code,to_stock_code,waste_box_code);
        }

        //5.查找码头信息-来源位置
        if(mapSplitTask==null){
            errorMsg="任务类型为{"+task_type+"}的满框入库任务,未能根据码头编码{"+to_stock_code+"}查找到码头基础信息";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        to_location_z=to_location_z+waste_box_height.intValue();
        String split_task_id=mapSplitTask.get("task_id").toString();
        String split_task_num=mapSplitTask.get("task_num").toString();
        String split_task_type=mapSplitTask.get("task_type").toString();

        //6.插入调度任务：b_dcs_wms_lock_car_task PLAN
        String serial_num=material_code;//序列号(material_code)
        Long car_task_id= dcsCarCommonFunc.LockCarTaskIns(userID,request,apiRoutePath,"N",car_code,ware_house,
                split_task_id,0L,split_task_num,task_from,task_way,split_task_type,serial_num,lot_num,
                "",0L,from_stock_code,to_stock_code);

        Integer para_f=0;//旋转角度(0度,180度)
        Integer para_r=1;//R任务类型(1叉取、2放置、3避让、4寻中、5抛丸)
        Integer para_a=3;//A位置代码(1上料位、2下料位、3库位、4抛丸位)
        Integer para_h=0;//半高标志位,1为半高位
        //6.1 插取路线：b_dcs_wms_lock_car_route
        Long car_route_id= dcsCarCommonFunc.LockCarRouteIns(userID,request,apiRoutePath,car_task_id,ware_house,car_code,
                "CHA_QU",1,from_location_x,from_location_y,from_location_z,
                0L,"","","N",
                "N","","",from_stock_count,
                "","","",
                para_f,para_r,para_a,para_h);

        para_r=2;//R任务类型(1叉取、2放置、3避让、4寻中、5抛丸)
        //6.2 放置路线
        car_route_id= dcsCarCommonFunc.LockCarRouteIns(userID,request,apiRoutePath,car_task_id,ware_house,car_code,
                "FANG_ZHI",2,to_location_x,to_location_y,to_location_z,
                0L,"","","N",
                "N","","",0,
                "","","",
                para_f,para_r,para_a,para_h);
        return mapResult;
    }

}
