package com.api.eap.project.guanghe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 广合接口处理
 * 1.被动接生产任务
 * 2.上报完板信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/guanghe/interf")
public class EapGhInterfController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    //1.被动接生产任务
    @RequestMapping(value = "/EapGhRecvTask", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGhRecvTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="/eap/project/guanghe/interf/EapGhRecvTask";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapGhRecvTask(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //处理任务接受
    private JSONObject eapGhRecvTask(JSONObject jsonParas,HttpServletRequest request,String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String esbInterfCode="EapGhRecvTask";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbBack=null;
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            //处理EAP数据
            JSONArray arrlist=jsonParas.getJSONArray("arrlist");
            if(arrlist==null || arrlist.size()<=0){
                errorMsg="参数arrlist为null,或者arrlist未传递任务信息";
                jbBack=new JSONObject();
                jbBack.put("code",500);
                jbBack.put("info",errorMsg);
                responseParas=jbBack.toString();
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //查询当前工位
            String sqlStation="select station_code " +
                    "from sys_fmod_station where station_attr='Load' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> lstStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,
                    false,request,apiRoutePath);
            if(lstStation==null || lstStation.size()<=0){
                errorMsg="AIS系统未配置放板工位信息";
                jbBack=new JSONObject();
                jbBack.put("code",500);
                jbBack.put("info",errorMsg);
                responseParas=jbBack.toString();
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String station_code=lstStation.get(0).get("station_code").toString();
            JSONArray plan_list=new JSONArray();
            String group_lot_num=CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss");
            for(int i=0;i<arrlist.size();i++) {
                JSONObject jbItem=new JSONObject();
                JSONObject jsonObject = arrlist.getJSONObject(i);
                String prodno = jsonObject.getString("prodno");//料号
                String layer = jsonObject.getString("layer");//层次
                String lotno = jsonObject.getString("lotno");//批次，所有计划的唯一标识，根据批次号判重
                String number = jsonObject.getString("number");//数量
                int sortno = jsonObject.getInteger("sortno");//批次序号
                JSONArray pnlids =jsonObject.getJSONArray("pnlids");//pnl
                Integer plan_lot_count=Integer.parseInt(number);
                if(pnlids!=null && pnlids.size()!=plan_lot_count){
                    errorMsg="EAP下发批次{"+lotno+"}计划数量{"+number+"}与pnlids{"+pnlids.toString()+"}数量不匹配";
                    jbBack=new JSONObject();
                    jbBack.put("code",500);
                    jbBack.put("info",errorMsg);
                    responseParas=jbBack.toString();
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
                String panel_list="";
                if(pnlids!=null && pnlids.size()>0){
                    for(int j=0;j<pnlids.size();j++){
                        String pnlid=pnlids.getString(j);
                        if(j==0) panel_list=pnlid;
                        else panel_list+=","+pnlid;
                    }
                }
                //合成标准
                jbItem.put("task_from","EAP");
                jbItem.put("group_lot_num",group_lot_num);
                jbItem.put("lot_num",lotno);
                jbItem.put("lot_index",sortno);
                jbItem.put("plan_lot_count",plan_lot_count);
                jbItem.put("target_lot_count",plan_lot_count);
                jbItem.put("port_code","1");
                jbItem.put("material_code",prodno);
                jbItem.put("lot_level",layer);
                jbItem.put("panel_list",panel_list);
                plan_list.add(jbItem);
            }
            JSONObject postParas=new JSONObject();
            postParas.put("station_code",station_code);
            postParas.put("plan_list",plan_list);
            String sharePlanUrl="http://127.0.0.1:9090/aisEsbApi/eap/core/share/EapCoreSharePlanSave";
            JSONObject jbPlanResult= cFuncUtilsRest.PostJbBackJb(sharePlanUrl,postParas);
            if(jbPlanResult.getInteger("code")!=0){
                errorMsg=jbPlanResult.getString("error");
                jbBack=new JSONObject();
                jbBack.put("code",500);
                jbBack.put("info",errorMsg);
                responseParas=jbBack.toString();
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            jbBack=new JSONObject();
            jbBack.put("code",200);
            jbBack.put("info","接受任务成功");
            responseParas=jbBack.toString();
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","");
        }
        catch (Exception ex){
            errorMsg="AIS接受任务发生未知异常:"+ex.getMessage();
            jbBack=new JSONObject();
            jbBack.put("code",500);
            jbBack.put("info",errorMsg);
            responseParas=jbBack.toString();
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //2.上报完板信息
    @RequestMapping(value = "/EapGhReportTaskFinishInfo", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGhReportTaskFinishInfo(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="/eap/project/guanghe/interf/EapGhReportTaskFinishInfo";
        String transResult="";
        String errorMsg="";
        try{
            String station_code=jsonParas.getString("station_code");
            JSONArray finish_panel_list=jsonParas.getJSONArray("finish_panel_list");

            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=upLotFinishToEap(station_code,finish_panel_list);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult=CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "上报完板信息异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //上传完板信息到EAP
    private JSONObject upLotFinishToEap(String station_code,JSONArray finish_panel_list){
        JSONObject jbResult=null;
        String errorMsg="";
        String esbInterfCode="UpLotFinishToEap";
        String token="";
        String requestParas="";
        String responseParas="";
        try{
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONArray jsonArray=new JSONArray();
            for(int i=0;i<finish_panel_list.size();i++){
                JSONObject jbItem=finish_panel_list.getJSONObject(i);
                jbItem.put("prodno",jbItem.getString("material_code"));
                jbItem.put("layer",jbItem.getString("lot_level"));
                jbItem.put("lotno",jbItem.getString("lot_num"));
                jbItem.put("pnlid",jbItem.getString("panel_barcode"));
                jbItem.put("checktime",jbItem.getString("item_date"));
                jbItem.put("faceinfo",jbItem.getInteger("face_code")==0?"S":"C");
                jsonArray.add(jbItem);
            }
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("eqno",station_code);
            jsonObjectReq.put("arrlist",jsonArray);
            JSONObject jsonObjectEap= cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url,jsonObjectReq);
            responseParas=jsonObjectEap.toString();
            Integer backCode=jsonObjectEap.getInteger("code");
            String backMsg=jsonObjectEap.getString("info");
            if(backCode!=200){
                errorMsg="上报完板信息调用接口返回失败："+backMsg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //上传成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","上传成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //3.实时上报Panel信息
    @RequestMapping(value = "/EapGhReportPanelInfo", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGhReportPanelInfo(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="/eap/project/guanghe/interf/EapGhReportPanelInfo";
        String transResult="";
        String errorMsg="";
        try{
            String panel_barcode=jsonParas.getString("panel_barcode");
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=upPanelToEap(panel_barcode);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult=CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "上报完板信息异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //实时上报Panel信息到EAP
    private JSONObject upPanelToEap(String panel_barcode){
        JSONObject jbResult=null;
        String errorMsg="";
        String esbInterfCode="UpPanelToEap";
        String token="";
        String requestParas="";
        String responseParas="";
        try{
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("pnlid",panel_barcode);
            JSONObject jsonObjectEap= cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url,jsonObjectReq);
            responseParas=jsonObjectEap.toString();
            Integer backCode=jsonObjectEap.getInteger("code");
            String backMsg=jsonObjectEap.getString("info");
            if(backCode!=200){
                errorMsg="上报Panel信息调用接口返回失败："+backMsg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //上传成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","上传成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
