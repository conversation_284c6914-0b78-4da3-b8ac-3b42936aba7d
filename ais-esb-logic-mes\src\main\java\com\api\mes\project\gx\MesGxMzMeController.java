package com.api.mes.project.gx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.FindIterable;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.model.Sorts;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.SortOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 处理模组工艺逻辑
 * 1.获取当前选择订单模组配方信息并下发到PLC
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Slf4j
@RestController
@RequestMapping("/mes/project/gx")
public class MesGxMzMeController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    private final static SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    //根据当前选择订单查询配方数据,并执行下发到PLC
    @Transactional
    @RequestMapping(value = "/MesGxMzMoRecipeAutoSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzMoRecipeAutoSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzMoRecipeAutoSel";
        String selectResult = "";
        String errorMsg = "";
        try {
            String userName = jsonParas.getString("userID");
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String prod_line_id = jsonParas.getString("prod_line_id");
            String sqlMo = "select mo_id,small_model_type,make_order " + "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " + "and (mo_status='SELECTED' or mo_status='CARRY_ON') and enable_flag='Y' " + "and mo_sign='AUTO' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMo, false, request, apiRoutePath);

            //先做删除
            String sqlDelete01 = "delete from c_mes_aps_station_mo where station_id=" + station_id + "";
            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlDelete01, false, request, apiRoutePath);

            if (itemListMo == null || itemListMo.size() <= 0) {
                sqlMo = "select mo_id,small_model_type,make_order " + "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " + "and mo_status='LINE_UP' and enable_flag='Y' and mo_sign='AUTO' " + "order by mo_order_by LIMIT 1 OFFSET 0";
                itemListMo = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMo, false, request, apiRoutePath);
                if (itemListMo == null || itemListMo.size() <= 0) {
                    errorMsg = "模组自动订单已消耗完毕,请补充订单";
                    selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return selectResult;
                }
                String mo_id_new = itemListMo.get(0).get("mo_id").toString();
                String sqlMoUpdate = "update c_mes_aps_plan_mo set " + "mo_status='CARRY_ON' where mo_id=" + mo_id_new + "";
                cFuncDbSqlExecute.ExecUpdateSql(userName, sqlMoUpdate, false, request, apiRoutePath);
            }
            String mo_id = itemListMo.get(0).get("mo_id").toString();
            String small_model_type = itemListMo.get(0).get("small_model_type").toString();
            String make_order = itemListMo.get(0).get("make_order").toString();

            //更新当前工位选择订单
            String sqlInsert01 = "insert into c_mes_aps_station_mo " + "(station_id,make_order,station_code,mo_id) values " + "(" + station_id + ",'" + make_order + "','" + station_code + "'," + mo_id + ")";
            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlInsert01, false, request, apiRoutePath);
            //查询是否具备模组配方
            String sqlRecipe = "select b.recipe_id,b.recipe_name,b.recipe_version " + "from c_mes_aps_plan_mo_recipe a inner join c_mes_fmod_recipe b on a.recipe_id=b.recipe_id " + "where a.mo_id=" + mo_id + " and b.recipe_type='MODELDX' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListRecipe = cFuncDbSqlExecute.ExecSelectSql(userName, sqlRecipe, false, request, apiRoutePath);
            if (itemListRecipe == null || itemListRecipe.size() <= 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                errorMsg = "根据当前选择订单{" + make_order + "}未找到模组配方版本";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String recipe_id = itemListRecipe.get(0).get("recipe_id").toString();
            String recipe_name = itemListRecipe.get(0).get("recipe_name").toString();
            String recipe_version = itemListRecipe.get(0).get("recipe_version").toString();
            recipe_name = recipe_name + recipe_version;
            String sqlMzRecipe = "select " + "'" + make_order + "' as make_order," + "'" + small_model_type + "' as small_model_type," + "'" + recipe_name + "' as recipe_name," + "a.mz_num,a.mz_name," + "COALESCE(a.dx_width_size,0) dx_width_size," + "COALESCE(a.dx_height_size,0) dx_height_size," + "COALESCE(a.dx_column,0) dx_column," + "COALESCE(a.dx_layer_num,0) dx_layer_num,a.dx_count," + "COALESCE(a.mz_p_count,0) mz_p_count," + "COALESCE(a.mz_s_count,0) mz_s_count," + "COALESCE(a.mz_tj_technology,0) mz_tj_technology," + "COALESCE(a.mz_type,'0') mz_type," + "COALESCE(a.dx_lk_layer_num,'0') dx_lk_layer_num," + "COALESCE(a.mz_end_width,'0') mz_end_width," + "COALESCE(a.mid_partition,'0') mid_partition," + "COALESCE(a.mid_partition_layer,'0') mid_partition_layer," + "COALESCE(a.mid_partition_breadth,'0') mid_partition_breadth," + "b.dx_num," + "COALESCE(b.line_num,0) line_num," + "COALESCE(b.dx_direct,0) dx_direct," + "COALESCE(b.dx_gear,0) dx_gear,b.sheet_type, b.dx_type  " + "from c_mes_fmod_recipe_mz a inner join c_mes_fmod_recipe_mz_dx b on a.mz_id=b.mz_id " + "where a.recipe_id=" + recipe_id + " and a.enable_flag='Y' and b.enable_flag='Y' order by b.dx_num";
            List<Map<String, Object>> itemListMzRecipe = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMzRecipe, true, request, apiRoutePath);
            if (itemListMzRecipe == null || itemListMzRecipe.size() <= 0) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                errorMsg = "根据当前选择订单{" + make_order + "}未找到模组配方基础数据";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMzRecipe, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "查询订单模组配方数据异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更新模组自动上线数量
    @Transactional
    @RequestMapping(value = "/MesGxMzMoUpdateOnLineCount", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzMoUpdateOnLineCount(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzMoUpdateOnLineCount";
        String transResult = "";
        String errorMsg = "";
        try {
            String userName = jsonParas.getString("userID");
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String prod_line_id = jsonParas.getString("prod_line_id");
            String make_order = jsonParas.getString("make_order");
            String sqlMo = "select mo_id,mo_plan_count,mo_online_count " + "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " + "and (mo_status='SELECTED' or mo_status='CARRY_ON') and enable_flag='Y' " + "and make_order='" + make_order + "' " + "and mo_sign='AUTO' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMo, false, request, apiRoutePath);
            if (itemListMo == null || itemListMo.size() <= 0) {
                errorMsg = "人为修改为订单{" + make_order + "}状态,导致无法增加该订单上线完工数量";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String mo_id = itemListMo.get(0).get("mo_id").toString();
            Integer mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
            Integer mo_online_count = Integer.parseInt(itemListMo.get(0).get("mo_online_count").toString());
            String mo_status = "CARRY_ON";
            if (mo_online_count + 1 >= mo_plan_count) mo_status = "COMPLETE";
            String sqlUpdate = "update c_mes_aps_plan_mo set " + "mo_online_count=mo_online_count+1," + "mo_status='" + mo_status + "' " + "where mo_id=" + mo_id;
            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlUpdate, false, request, apiRoutePath);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "更新模组自动上线数量异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更新模组自动上线数量 唐山/金寨国轩 判断PACK订单是否做完
    @Transactional
    @RequestMapping(value = "/MesGxMzMoUpdateOnLineCount2", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzMoUpdateOnLineCount2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzMoUpdateOnLineCount2";
        String transResult = "";
        String errorMsg = "";
        try {
            String userName = jsonParas.getString("userID");
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String prod_line_id = jsonParas.getString("prod_line_id");
            String make_order = jsonParas.getString("make_order");
            String sqlMo = "select mo_id,mo_plan_count,mo_online_count,small_model_type " + "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " + "and (mo_status='SELECTED' or mo_status='CARRY_ON') and enable_flag='Y' " + "and make_order='" + make_order + "' " + "and mo_sign='AUTO' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMo, false, request, apiRoutePath);
            if (itemListMo == null || itemListMo.size() <= 0) {
                errorMsg = "人为修改为订单{" + make_order + "}状态,导致无法增加该订单上线完工数量";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String mo_id = itemListMo.get(0).get("mo_id").toString();
            Integer mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
            Integer mo_online_count = Integer.parseInt(itemListMo.get(0).get("mo_online_count").toString());
            String small_model_type = itemListMo.get(0).get("small_model_type").toString();
            String mo_status = "CARRY_ON";
            if (mo_online_count + 1 >= mo_plan_count) mo_status = "COMPLETE";
            String sqlUpdate = "update c_mes_aps_plan_mo set " + "mo_online_count=mo_online_count+1," + "mo_status='" + mo_status + "' " + "where mo_id=" + mo_id;
            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlUpdate, false, request, apiRoutePath);

            if (mo_status.equals("COMPLETE")) {
                //判断是否存在当前运行状态的PACK订单
                String sqlPackMz = "select COALESCE(pack_mz.mz_model_type,'') mz_model_type," + "COALESCE(pack_mz.mz_type_count,0) mz_type_count," + "COALESCE(pack_mz.mz_sequence,0) mz_sequence " + "from c_mes_aps_plan_mo mo join c_mes_aps_plan_mo_recipe mo_recipe on mo.mo_id=mo_recipe.mo_id " + "join c_mes_fmod_recipe_pack recipe_pack on mo_recipe.recipe_id=recipe_pack.recipe_id " + "join c_mes_fmod_recipe_pack_mz pack_mz on recipe_pack.pack_id=pack_mz.pack_id " + "where mo.enable_flag='Y' and recipe_pack.enable_flag='Y' and  pack_mz.enable_flag='Y' and mo.mo_type='PACK' " + "group by pack_mz.mz_model_type,pack_mz.mz_type_count,pack_mz.mz_sequence " + "order by pack_mz.mz_sequence";
                List<Map<String, Object>> itemPackMz = cFuncDbSqlExecute.ExecSelectSql(userName, sqlPackMz, true, request, apiRoutePath);
                if (itemPackMz != null || itemPackMz.size() > 0) {
                    String mz_model_type_last = itemPackMz.get(itemPackMz.size() - 1).get("mz_model_type").toString();
                    //判断是否是PACK的最后一个模组
                    if (mz_model_type_last.equals(small_model_type)) {
                        Integer mo_plan_count_pack = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
                        Integer mo_online_count_pack = Integer.parseInt(itemListMo.get(0).get("mo_online_count").toString());
                        if (mo_plan_count_pack > mo_online_count_pack) {
                            //先复位所有的模组订单
                            String sqlMoMz = "select mo.mo_id,mo.small_model_type from c_mes_aps_plan_mo mo join c_mes_aps_plan_mo_recipe mo_recipe on mo.mo_id=mo_recipe.mo_id " + "join c_mes_fmod_recipe_mz recipe_mz on mo_recipe.recipe_id=recipe_mz.recipe_id " + "join c_mes_fmod_small_model model on model.small_model_type=mo.small_model_type " + "where mo.enable_flag='Y' and recipe_mz.enable_flag='Y' and model.enable_flag='Y' order by mo_order_by";
                            List<Map<String, Object>> itemMoMz = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMoMz, true, request, apiRoutePath);
                            if (itemMoMz != null && itemMoMz.size() > 0) {
                                for (int i = 0; i < itemMoMz.size(); i++) {
                                    String mo_id_i = itemMoMz.get(i).get("mo_id").toString();
                                    String updateSqlMoMz = "update c_mes_aps_plan_mo set " + "mo_status='LINE_UP'," + "mo_order_by='" + (i + 1000) + "' " + "where mo_id= " + mo_id_i;
                                    cFuncDbSqlExecute.ExecUpdateSql(userName, updateSqlMoMz, true, request, apiRoutePath);
                                }
                                for (int i = 0; i < itemPackMz.size(); i++) {
                                    String updateSqlMoMz = "";
                                    String mz_model_type_i = itemPackMz.get(i).get("mz_model_type").toString();
                                    int mz_type_count_i = Integer.parseInt(itemPackMz.get(i).get("mz_type_count").toString());
                                    int mz_sequence_i = Integer.parseInt(itemPackMz.get(i).get("mz_sequence").toString());
                                    Map<String, Object> item = itemMoMz.stream().filter(e -> e.get("small_model_type").toString().equals(mz_model_type_i)).findFirst().orElse(null);
                                    if (item != null) {
                                        String mo_id_j = itemMoMz.get(i).get("mo_id").toString();
                                        if (i == 0) {
                                            updateSqlMoMz = "update c_mes_aps_plan_mo set " + "mo_status='CARRY_ON'," + "mo_plan_count=" + mz_type_count_i + "," + "mo_online_count=0," + "mo_order_by='" + mz_sequence_i + "' " + "where mo_id= " + mo_id_j;
                                        } else {
                                            updateSqlMoMz = "update c_mes_aps_plan_mo set " + "mo_status='LINE_UP'," + "mo_plan_count=" + mz_type_count_i + "," + "mo_online_count=0," + "mo_order_by='" + mz_sequence_i + "' " + "where mo_id= " + mo_id_j;
                                        }
                                        cFuncDbSqlExecute.ExecUpdateSql(userName, updateSqlMoMz, true, request, apiRoutePath);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "更新模组自动上线数量异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

//    @Transactional
//    @RequestMapping(value = "/MesGxMzMoUpdateOnLineCount3", method = {RequestMethod.POST, RequestMethod.GET})
//    public  String MesGxMzMoUpdateOnLineCount3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
//        String apiRoutePath = "mes/project/gx/MesGxMzMoUpdateOnLineCount3";
//        String transResult = "";
//        String errorMsg = "";
//        try {
//            String userName = jsonParas.getString("userID");
//            String station_id = jsonParas.getString("station_id");
//            String station_code = jsonParas.getString("station_code");
//            String prod_line_id = jsonParas.getString("prod_line_id");
//            String make_order = jsonParas.getString("make_order");
//            String sqlMo = "select mo_id,mo_plan_count,mo_online_count,small_model_type " + "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " + "and (mo_status='SELECTED' or mo_status='CARRY_ON') and enable_flag='Y' " + "and make_order='" + make_order + "' " + "and mo_sign='AUTO' LIMIT 1 OFFSET 0";
//            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMo, false, request, apiRoutePath);
//            if (itemListMo == null || itemListMo.size() <= 0) {
//                errorMsg = "人为修改为订单{" + make_order + "}状态,导致无法增加该订单上线完工数量";
//                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
//                return transResult;
//            }
//            String mo_id = itemListMo.get(0).get("mo_id").toString();
//            Integer mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
//            Integer mo_online_count = Integer.parseInt(itemListMo.get(0).get("mo_online_count").toString());
//            String small_model_type = itemListMo.get(0).get("small_model_type").toString();
//            String mo_status = "CARRY_ON";
//            if (mo_online_count + 1 >= mo_plan_count) mo_status = "COMPLETE";
//            String sqlUpdate = "update c_mes_aps_plan_mo set " + "mo_online_count=mo_online_count+1," + "mo_status='" + mo_status + "' " + "where mo_id=" + mo_id;
//            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlUpdate, false, request, apiRoutePath);
//
//            if (mo_status.equals("COMPLETE")) {
//                //判断是否存在当前运行状态的PACK订单
//                String sqlPackMz = "select COALESCE(pack_mz.mz_model_type,'') mz_model_type," + "COALESCE(pack_mz.mz_type_count,0) mz_type_count," + "COALESCE(pack_mz.mz_sequence,0) mz_sequence " + "from c_mes_aps_plan_mo mo join c_mes_aps_plan_mo_recipe mo_recipe on mo.mo_id=mo_recipe.mo_id " + "join c_mes_fmod_recipe_pack recipe_pack on mo_recipe.recipe_id=recipe_pack.recipe_id " + "join c_mes_fmod_recipe_pack_mz pack_mz on recipe_pack.pack_id=pack_mz.pack_id " + "where mo.enable_flag='Y' and recipe_pack.enable_flag='Y' and  pack_mz.enable_flag='Y' and mo.mo_type='PACK' " + "group by pack_mz.mz_model_type,pack_mz.mz_type_count,pack_mz.mz_sequence " + "order by pack_mz.mz_sequence";
//                List<Map<String, Object>> itemPackMz = cFuncDbSqlExecute.ExecSelectSql(userName, sqlPackMz, true, request, apiRoutePath);
//                //查询到pack运行的订单
//                if (itemPackMz != null || itemPackMz.size() > 0) {
//                    String mz_model_type_last = itemPackMz.get(itemPackMz.size() - 1).get("mz_model_type").toString();
//                    //判断是否是PACK的最后一个模组
//                    if (mz_model_type_last.equals(small_model_type)) {
//                        Integer mo_plan_count_pack = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
//                        Integer mo_online_count_pack = Integer.parseInt(itemListMo.get(0).get("mo_online_count").toString());
//                        String sqlSelect = "select mo_plan_count,mo_online_count  from c_mes_aps_plan_mo where mo_type='PACK'";
//                        Map<String, Object> pack = cFuncDbSqlExecute.ExecSelectSql(userName, sqlSelect, true, request, apiRoutePath).stream().findFirst().get();
//                        //先复位所有的模组订单
//                        String sqlMoMz = "select mo.mo_id,mo.small_model_type from c_mes_aps_plan_mo mo join c_mes_aps_plan_mo_recipe mo_recipe on mo.mo_id=mo_recipe.mo_id " + "join c_mes_fmod_recipe_mz recipe_mz on mo_recipe.recipe_id=recipe_mz.recipe_id " + "join c_mes_fmod_small_model model on model.small_model_type=mo.small_model_type " + "where mo.enable_flag='Y' and recipe_mz.enable_flag='Y' and model.enable_flag='Y' order by mo_order_by";
//                        // 修改pack数量
//                        sqlUpdate = "update c_mes_aps_plan_mo set " + "mo_online_count=mo_online_count+1";
//                        String sqlUpdateWhere = " where mo_status='CARRY_ON'" + " and mo_type='PACK' ";
//                        List<Map<String, Object>> itemMoMz = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMoMz, true, request, apiRoutePath);
//                        //判断计划数量以及实际完成数量是否一致
//                        if ((pack.containsKey("mo_plan_count") && pack.containsKey("mo_online_count")) && (Integer.parseInt(pack.get("mo_plan_count").toString()) <= (Integer.parseInt(pack.get("mo_online_count").toString()) + 1))) {
//                            //若一致则修改pack订单任务状态以及完成数量
//                            sqlUpdate += ",mo_status='COMPLETE'";
//                            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlUpdate + sqlUpdateWhere, false, request, apiRoutePath);
//                            //获取所有模组的订单id
//                            String mo_ids = itemMoMz.stream().map(m -> "'" + m.get("mo_id") + "'").collect(Collectors.toList()).toString().replace("[", "").replace("]", "");
//                            String updateMzOrder = "update c_mes_aps_plan_mo set " + "mo_status='COMPLETE'," + "mo_order_by='" + (itemMoMz.size() + 1000) + "' " + "where mo_id in( " + mo_ids + ")";
//                            //如果pack数量到位了就把模组订单任务状态变更为完成
//                            cFuncDbSqlExecute.ExecUpdateSql(userName, updateMzOrder, false, request, apiRoutePath);
//                        } else if (mo_plan_count_pack > mo_online_count_pack) {
//                            if (itemMoMz != null && itemMoMz.size() > 0) {
//                                for (int i = 0; i < itemMoMz.size(); i++) {
//                                    String mo_id_i = itemMoMz.get(i).get("mo_id").toString();
//                                    String updateSqlMoMz = "update c_mes_aps_plan_mo set " + "mo_status='LINE_UP'," + "mo_order_by='" + (i + 1000) + "' " + "where mo_id= " + mo_id_i;
//                                    cFuncDbSqlExecute.ExecUpdateSql(userName, updateSqlMoMz, true, request, apiRoutePath);
//                                }
//                                for (int i = 0; i < itemPackMz.size(); i++) {
//                                    String updateSqlMoMz = "";
//                                    String mz_model_type_i = itemPackMz.get(i).get("mz_model_type").toString();
//                                    int mz_type_count_i = Integer.parseInt(itemPackMz.get(i).get("mz_type_count").toString());
//                                    int mz_sequence_i = Integer.parseInt(itemPackMz.get(i).get("mz_sequence").toString());
//                                    Map<String, Object> item = itemMoMz.stream().filter(e -> e.get("small_model_type").toString().equals(mz_model_type_i)).findFirst().orElse(null);
//                                    if (item != null) {
//                                        String mo_id_j = itemMoMz.get(i).get("mo_id").toString();
//                                        if (i == 0) {
//                                            updateSqlMoMz = "update c_mes_aps_plan_mo set " + "mo_status='CARRY_ON'," + "mo_plan_count=" + mz_type_count_i + "," + "mo_online_count=0," + "mo_order_by='" + mz_sequence_i + "' " + "where mo_id= " + mo_id_j;
//                                        } else {
//                                            updateSqlMoMz = "update c_mes_aps_plan_mo set " + "mo_status='LINE_UP'," + "mo_plan_count=" + mz_type_count_i + "," + "mo_online_count=0," + "mo_order_by='" + mz_sequence_i + "' " + "where mo_id= " + mo_id_j;
//                                        }
//                                        cFuncDbSqlExecute.ExecUpdateSql(userName, updateSqlMoMz, true, request, apiRoutePath);
//                                    }
//                                }
//                            }
//                            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlUpdate + sqlUpdateWhere, false, request, apiRoutePath);
//                        }
//                    }
//                }
//            }
//
//            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
//        } catch (Exception ex) {
//            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
//            errorMsg = "更新模组自动上线数量异常" + ex.getMessage();
//            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
//        }
//        return transResult;
//    }

    @Transactional
    @RequestMapping(value = "/MesGxMzMoUpdateOnLineCount3", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzMoUpdateOnLineCount3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzMoUpdateOnLineCount3";
        String transResult = "";
        String errorMsg = "";
        try {
            String userName = jsonParas.getString("userID");
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String prod_line_id = jsonParas.getString("prod_line_id");
            String make_order = jsonParas.getString("make_order");
            String sqlMo = "select mo_id,mo_plan_count,mo_online_count,small_model_type  from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " + "and (mo_status='SELECTED' or mo_status='CARRY_ON') and enable_flag='Y' " + "and make_order='" + make_order + "' " + "and mo_sign='AUTO' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMo, false, request, apiRoutePath);
            if (itemListMo == null || itemListMo.size() <= 0) {
                log.error("当前无可用订单");
                return CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
            }
            String mo_id = itemListMo.get(0).get("mo_id").toString();
            Integer mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
            Integer mo_online_count = Integer.parseInt(itemListMo.get(0).get("mo_online_count").toString());
            String small_model_type = itemListMo.get(0).get("small_model_type").toString();
            String mo_status = "CARRY_ON";
            if (mo_online_count + 1 >= mo_plan_count) mo_status = "COMPLETE";
            String sqlUpdate = "update c_mes_aps_plan_mo set " + "mo_online_count=mo_online_count+1," + "mo_status='" + mo_status + "' " + "where mo_id=" + mo_id;
            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlUpdate, false, request, apiRoutePath);
            if (mo_status.equals("COMPLETE")) {
                //查询当前进行中的所有pack订单下模组订单
                String selectPackAllMzSql = "SELECT mo.mo_id, mo.small_model_type,mo.mo_status,mo_order_by FROM c_mes_aps_plan_mo mo WHERE  mo.enable_flag = 'Y'  AND mo.small_model_type IN (SELECT C.mz_model_type  FROM ( SELECT *  FROM ( SELECT cmfr.recipe_id, cmfr.recipe_type,cmfr.recipe_name,cmfr.recipe_version FROM c_mes_fmod_small_model cmfsm INNER JOIN c_mes_fmod_small_model_recipe cmfsmr ON cmfsm.small_type_id = cmfsmr.small_type_id INNER JOIN c_mes_fmod_recipe cmfr ON cmfsmr.recipe_id = cmfr.recipe_id AND cmfr.enable_flag = 'Y' WHERE cmfsm.small_model_type = (select small_model_type from c_mes_aps_plan_mo where mo_type='PACK' and mo_status ='CARRY_ON' LIMIT 1) ) AS A LEFT JOIN c_mes_fmod_recipe_pack AS b ON A.recipe_id = b.recipe_id WHERE b.pack_id IS NOT NULL ) AS b LEFT JOIN c_mes_fmod_recipe_pack_mz AS C ON b.pack_id = C.pack_id ) ORDER BY mo_order_by ";
                List<Map<String, Object>> maps = cFuncDbSqlExecute.ExecSelectSql(userName, selectPackAllMzSql, false, request, apiRoutePath);
                //过滤掉不是进行中的，如果模组订单全部完成了就判断pack
                List<Map<String, Object>> filter = maps.stream().filter(f -> ("CARRY_ON".equals((f.containsKey("mo_status") ? f.get("mo_status") : "")) || "LINE_UP".equals((f.containsKey("mo_status") ? f.get("mo_status") : "")))).collect(Collectors.toList());
                //说明模组订单全部完成了
                if (CollectionUtils.isEmpty(filter)) {
                    //判断pack是否还有剩余
                    String selectPackSql = "select * from c_mes_aps_plan_mo where mo_type='PACK' and mo_status ='CARRY_ON' LIMIT 1";
                    List<Map<String, Object>> packInfos = cFuncDbSqlExecute.ExecSelectSql(userName, selectPackSql, false, request, apiRoutePath);
                    //如果pack没有了那就无需操作
                    if (!CollectionUtils.isEmpty(packInfos)) {
                        //拿到Pack
                        Map<String, Object> pack = packInfos.stream().findFirst().get();
                        String updatePack = "update c_mes_aps_plan_mo set mo_online_count=mo_online_count+1";
                        String packWhere = " where mo_id=" + pack.get("mo_id");
                        // 如果pack数量到位了就数量加1并变更状态
                        if ((pack.containsKey("mo_plan_count") && pack.containsKey("mo_online_count"))) {
                            if ((Integer.parseInt(pack.get("mo_plan_count").toString()) <= (Integer.parseInt(pack.get("mo_online_count").toString()) + 1))) {
                                updatePack += " ,mo_status='COMPLETE'";
                                cFuncDbSqlExecute.ExecUpdateSql(userName, updatePack + packWhere, false, request, apiRoutePath);
                            } else {
                                //如果数量不大于等于那就将所有模组订单归位
                                String mo_ids = maps.stream().map(m -> "'" + m.get("mo_id") + "'").collect(Collectors.toList()).toString().replace("[", "").replace("]", "");
                                String updateSqlMoMz = "update c_mes_aps_plan_mo set  mo_status='LINE_UP', mo_online_count=0 where mo_id in (" + mo_ids + ")";
                                cFuncDbSqlExecute.ExecUpdateSql(userName, updateSqlMoMz, false, request, apiRoutePath);
                                cFuncDbSqlExecute.ExecUpdateSql(userName, updatePack + packWhere, false, request, apiRoutePath);
                            }
                        }
                    }
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "更新模组自动上线数量异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //根据当前选择订单查询配方数据,并执行下发到PLC
    @RequestMapping(value = "/MesGxMzMoRecipeSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzMoRecipeSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzMoRecipeSel";
        String selectResult = "";
        String errorMsg = "";
        try {
            String userName = jsonParas.getString("userID");
            String station_id = jsonParas.getString("station_id");
            String prod_line_id = jsonParas.getString("prod_line_id");
            String sqlMoStation = "select COALESCE(make_order,'') make_order " + "from c_mes_aps_station_mo where station_id=" + station_id;
            List<Map<String, Object>> itemListMoStation = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMoStation, false, request, apiRoutePath);
            if (itemListMoStation == null || itemListMoStation.size() <= 0) {
                errorMsg = "当前工位未选择订单";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String make_order = itemListMoStation.get(0).get("make_order").toString();
            String sqlMo = "select mo_id,small_model_type " + "from c_mes_aps_plan_mo where prod_line_id='" + prod_line_id + "' " + "and (mo_status='LINE_UP' or mo_status='CARRY_ON' or mo_status='SELECTED') " + "and enable_flag='Y' and make_order='" + make_order + "'";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMo, false, request, apiRoutePath);
            if (itemListMo == null || itemListMo.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}已失效,需要重新选择订单";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String mo_id = itemListMo.get(0).get("mo_id").toString();
            String small_model_type = itemListMo.get(0).get("small_model_type").toString();
            //查询是否具备模组配方
            String sqlRecipe = "select b.recipe_id,b.recipe_name,b.recipe_version " + "from c_mes_aps_plan_mo_recipe a inner join c_mes_fmod_recipe b on a.recipe_id=b.recipe_id " + "where a.mo_id=" + mo_id + " and b.recipe_type='MODELDX' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListRecipe = cFuncDbSqlExecute.ExecSelectSql(userName, sqlRecipe, false, request, apiRoutePath);
            if (itemListRecipe == null || itemListRecipe.size() <= 0) {
                errorMsg = "根据当前选择订单{" + make_order + "}未找到模组配方版本";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String recipe_id = itemListRecipe.get(0).get("recipe_id").toString();
            String recipe_name = itemListRecipe.get(0).get("recipe_name").toString();
            String recipe_version = itemListRecipe.get(0).get("recipe_version").toString();
            recipe_name = recipe_name + recipe_version;
            String sqlMzRecipe = "select " + "'" + make_order + "' as make_order," + "'" + small_model_type + "' as small_model_type," + "'" + recipe_name + "' as recipe_name," + "a.mz_num,a.mz_name," + "COALESCE(a.dx_width_size,0) dx_width_size," + "COALESCE(a.dx_height_size,0) dx_height_size," + "COALESCE(a.dx_column,0) dx_column," + "COALESCE(a.dx_layer_num,0) dx_layer_num,a.dx_count," + "COALESCE(a.mz_p_count,0) mz_p_count," + "COALESCE(a.mz_s_count,0) mz_s_count," + "COALESCE(a.mz_tj_technology,0) mz_tj_technology," + "COALESCE(a.mz_type,'0') mz_type," + "COALESCE(a.dx_lk_layer_num,'0') dx_lk_layer_num," + "COALESCE(a.mz_end_width,'0') mz_end_width," + "COALESCE(a.mid_partition,'0') mid_partition," + "COALESCE(a.mid_partition_layer,'0') mid_partition_layer," + "COALESCE(a.mid_partition_breadth,'0') mid_partition_breadth," + "b.dx_num," + "COALESCE(b.line_num,0) line_num," + "COALESCE(b.dx_direct,0) dx_direct," + "COALESCE(b.dx_gear,0) dx_gear,b.sheet_type, b.dx_type  " + "from c_mes_fmod_recipe_mz a inner join c_mes_fmod_recipe_mz_dx b on a.mz_id=b.mz_id " + "where a.recipe_id=" + recipe_id + " and a.enable_flag='Y' and b.enable_flag='Y' order by b.dx_num";
            List<Map<String, Object>> itemListMzRecipe = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMzRecipe, true, request, apiRoutePath);
            if (itemListMzRecipe == null || itemListMzRecipe.size() <= 0) {
                errorMsg = "根据当前选择订单{" + make_order + "}未找到模组配方基础数据";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMzRecipe, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "查询订单模组配方数据异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //模组堆叠判断与保存数据
    @RequestMapping(value = "/MesGxMzDDCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzDDCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzDDCheck";
        String tranResult = "";
        String errorMsg = "";
        try {
            //是否是测试阶段，测试阶段不需要判定，直接给合格
            String testModeFlag = cFuncDbSqlResolve.GetParameterValue("TestModeFlag");
            String station_code = jsonParas.getString("station_code");//工位号
            String prod_line_id = jsonParas.getString("prod_line_id");//生产线ID
            String make_order = jsonParas.getString("make_order");//订单号
            Integer col_num = jsonParas.getInteger("col_num");//单列或者双列
            String mz_barcode_list = jsonParas.getString("mz_barcode_list");//模组条码集合
            String dx_barcode_list = jsonParas.getString("dx_barcode_list");//电芯条码集合
            String[] mzBarCodeList = mz_barcode_list.split(",", -1);
            String[] dxBarCodeList = dx_barcode_list.split("@", -1);
            if (!testModeFlag.equals("Y")) {
                if (mzBarCodeList == null || dxBarCodeList == null || mzBarCodeList.length <= 0 || dxBarCodeList.length <= 0) {
                    errorMsg = "传递模组条码集合{" + mz_barcode_list + "},或者电芯条码集合{" + dx_barcode_list + "},不能为空";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                if (mzBarCodeList.length != dxBarCodeList.length) {
                    errorMsg = "传递模组条码集合{" + mz_barcode_list + "},或者电芯条码集合{" + dx_barcode_list + "},二者数量不匹配";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                if (col_num != 1 && col_num != 2) {
                    errorMsg = "模组只能为单列或者双列,从PLC获取该属性值{" + col_num + "}不符合要求";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
            }
            //合成全部的电芯条码用于重码判断
            List<String> lstAllDxBarCode = new ArrayList<>();
            for (int i = 0; i < dxBarCodeList.length; i++) {
                String dxBarCodeList2 = dxBarCodeList[i];
                String[] tempList = dxBarCodeList2.split(",", -1);
                for (String dxBarCode : tempList) {
                    lstAllDxBarCode.add(dxBarCode);
                }
            }

            //查找配方
            String sqlMoRecipe = "select c.recipe_id,d.mz_id,d.mz_p_count,d.mz_s_count,a.small_model_type " + "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " + "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " + "on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_mz d " + "on c.recipe_id=d.recipe_id " + "where a.enable_flag='Y' and c.enable_flag='Y' and d.enable_flag='Y' " + "and c.recipe_type='MODELDX' and a.make_order='" + make_order + "' " + "and a.prod_line_id=" + prod_line_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe, false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}不可用或者未配置模组配方";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String mz_id = itemListMoRecipe.get(0).get("mz_id").toString();
            String small_model_type = itemListMoRecipe.get(0).get("small_model_type").toString();
            int mz_p_count = Integer.parseInt(itemListMoRecipe.get(0).get("mz_p_count").toString());
            int mz_s_count = Integer.parseInt(itemListMoRecipe.get(0).get("mz_s_count").toString());
            //1.根据模组配方ID查找LIMIT项目
            String sqlMzLimitRecipe = "select a.mz_limit_code,a.down_limit,a.upper_limit,a.ng_rack_code," + "COALESCE(b.mz_limit_id,0) mz_limit_id," + "COALESCE(b.down_limit,0) down_limit2," + "COALESCE(b.upper_limit,0) upper_limit2," + "COALESCE(b.ng_rack_code,0) ng_rack_code2 " + "from c_mes_fmod_recipe_mz_limit_must a left join c_mes_fmod_recipe_mz_limit b " + "on a.mz_limit_code=b.mz_limit_code and b.mz_id=" + mz_id + " and b.enable_flag='Y' " + "order by a.mz_limit_index";
            List<Map<String, Object>> itemListMzLimitRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzLimitRecipe, false, request, apiRoutePath);
            //2.根据模组配方ID查找条码约束项目
            String sqlMzNgBarRecipe = "select mz_dxbar_ng_id,dxbar_ng_name,start_index,end_index," + "value_list,ng_way,ng_rack_num " + "from c_mes_fmod_recipe_mz_dxbar_ng " + "where mz_id=" + mz_id + " and enable_flag='Y'";
            List<Map<String, Object>> itemListMzNgBarRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzNgBarRecipe, false, request, apiRoutePath);
            //3.查找模组电芯配方,用于核对每个电芯
            String sqlMzDxRecipe = "select dx_num,dx_gear " + "from c_mes_fmod_recipe_mz_dx " + "where mz_id=" + mz_id + " and enable_flag='Y' " + "order by dx_num";
            List<Map<String, Object>> itemListMzDxRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzDxRecipe, false, request, apiRoutePath);

            int ng_rack_num_gear = 2;//档位NG放入曹号
            int ng_rack_num_mz_chongma = 1;//模组重码放入曹号
            int ng_rack_num_dx_chongma = 1;//电芯重码放入曹号
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    int ng_rack_code = Integer.parseInt(map.get("ng_rack_code").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    int ng_rack_code2 = Integer.parseInt(map.get("ng_rack_code2").toString());
                    if (mz_limit_id > 0) ng_rack_code = ng_rack_code2;
                    if (ng_rack_code <= 0) ng_rack_code = 1;
                    if (ng_rack_code > 3) ng_rack_code = 3;
                    if (mz_limit_code.equals("mz_gear")) ng_rack_num_gear = ng_rack_code;
                    if (mz_limit_code.equals("mz_chongma")) ng_rack_num_mz_chongma = ng_rack_code;
                    if (mz_limit_code.equals("mz_dx_chongma")) ng_rack_num_dx_chongma = ng_rack_code;
                }
            }

            Map<String, Object> mapDxRecipe = new HashMap<>();
            if (!testModeFlag.equals("Y")) {
                //判断模组电芯配方是否正确
                if (itemListMzDxRecipe == null || itemListMzDxRecipe.size() <= 0) {
                    errorMsg = "当前选择订单{" + make_order + "},查询模组电芯配方数据为空";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                for (Map<String, Object> map : itemListMzDxRecipe) {
                    String dx_num = map.get("dx_num").toString();
                    int dx_gear = Integer.parseInt(map.get("dx_gear").toString());
                    if (!mapDxRecipe.containsKey(dx_num)) mapDxRecipe.put(dx_num, dx_gear);
                }
                if (mapDxRecipe.size() != (mz_p_count * mz_s_count) / col_num) {
                    errorMsg = "当前选择订单{" + make_order + "},查询模组电芯配方数据量不等于{" + (mz_p_count * mz_s_count) / col_num + "}";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                for (int i = 1; i <= (mz_p_count * mz_s_count) / col_num; i++) {
                    String dx_num = String.valueOf(i);
                    if (!mapDxRecipe.containsKey(dx_num)) {
                        errorMsg = "当前选择订单{" + make_order + "},未找到电芯序号为{" + dx_num + "}数据";
                        tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                }
                //1.根据列数,判断电芯数量
                for (int i = 0; i < mzBarCodeList.length; i++) {
                    String mz_barcode2 = mzBarCodeList[i];
                    String dx_barcode_list2 = dxBarCodeList[i];
                    String[] dxBarCodeList2 = dx_barcode_list2.split(",", -1);
                    if (mz_barcode2.equals("") || dxBarCodeList2 == null || dxBarCodeList2.length <= 0) {
                        errorMsg = "传递模组条码{" + mz_barcode2 + "}对应的电芯条码集合{" + dx_barcode_list2 + "},模组条码不能为空或者电芯数量不能<=0不匹配";
                        tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                    int needDxCount = mz_p_count * mz_s_count;
                    if (dxBarCodeList2.length != needDxCount) {
                        errorMsg = "传递模组条码{" + mz_barcode2 + "}对应的电芯条码集合{" + dx_barcode_list2 + "},列{" + col_num + "},需求电芯数量{" + needDxCount + "}," + "实际电芯数量为{" + dxBarCodeList2.length + "}";
                        tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                }
            }
            //开始执行判断
            String resultMzStatus = "";
            String resultMzId = "";
            for (int i = 0; i < mzBarCodeList.length; i++) {
                Integer mk_num = 0;
                String mz_quality_id = CFuncUtilsSystem.CreateUUID(true);
                String mz_barcode2 = mzBarCodeList[i];
                String dx_barcode_list2 = dxBarCodeList[i];
                String[] dxBarCodeList2 = dx_barcode_list2.split(",", -1);
                int[] mk_num_list = new int[dxBarCodeList2.length];
                for (int j = 0; j < dxBarCodeList2.length; j++) {
                    if (j % mz_p_count == 0) mk_num = mk_num + 1;//计算串
                    mk_num_list[j] = mk_num;
                }
                String mzResult = GetMzBarCodeNgCode(mz_quality_id, mz_barcode2, dxBarCodeList2, mk_num_list, col_num, mz_p_count, ng_rack_num_gear, ng_rack_num_mz_chongma, ng_rack_num_dx_chongma, itemListMzLimitRecipe, itemListMzNgBarRecipe, mapDxRecipe, lstAllDxBarCode, make_order, small_model_type, station_code);
                if (i == 0) {
                    resultMzStatus = mzResult;
                    resultMzId = mz_quality_id;
                } else {
                    resultMzStatus += "," + mzResult;
                    resultMzId += "," + mz_quality_id;
                }
            }
            String result = resultMzStatus + "|" + resultMzId;
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "模组堆叠验证异常" + ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    /**
     * 模组堆叠判断与保存数据
     * version 2 ng时返回正常的模组码 并添加一列状态，若ok为0ng为1
     *
     * @param jsonParas
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/MesGxMzDDCheck2", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzDDCheck2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzDDCheck2";
        String tranResult = "";
        String errorMsg = "";
        try {
            //是否是测试阶段，测试阶段不需要判定，直接给合格
            String testModeFlag = cFuncDbSqlResolve.GetParameterValue("TestModeFlag");
            String station_code = jsonParas.getString("station_code");//工位号
            String prod_line_id = jsonParas.getString("prod_line_id");//生产线ID
            String make_order = jsonParas.getString("make_order");//订单号
            Integer col_num = jsonParas.getInteger("col_num");//单列或者双列
            String mz_barcode_list = jsonParas.getString("mz_barcode_list");//模组条码集合
            String dx_barcode_list = jsonParas.getString("dx_barcode_list");//电芯条码集合
            String[] mzBarCodeList = mz_barcode_list.split(",", -1);
            String[] dxBarCodeList = dx_barcode_list.split("@", -1);
            if (!testModeFlag.equals("Y")) {
                if (mzBarCodeList == null || dxBarCodeList == null || mzBarCodeList.length <= 0 || dxBarCodeList.length <= 0) {
                    errorMsg = "传递模组条码集合{" + mz_barcode_list + "},或者电芯条码集合{" + dx_barcode_list + "},不能为空";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                if (mzBarCodeList.length != dxBarCodeList.length) {
                    errorMsg = "传递模组条码集合{" + mz_barcode_list + "},或者电芯条码集合{" + dx_barcode_list + "},二者数量不匹配";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                if (col_num != 1 && col_num != 2 && col_num != 3) {
                    errorMsg = "模组只能为单列或者双列,从PLC获取该属性值{" + col_num + "}不符合要求";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
            }
            //合成全部的电芯条码用于重码判断
            List<String> lstAllDxBarCode = new ArrayList<>();
            for (int i = 0; i < dxBarCodeList.length; i++) {
                String dxBarCodeList2 = dxBarCodeList[i];
                String[] tempList = dxBarCodeList2.split(",", -1);
                for (String dxBarCode : tempList) {
                    lstAllDxBarCode.add(dxBarCode);
                }
            }

            //查找配方
            String sqlMoRecipe = "select c.recipe_id,d.mz_id,d.mz_p_count,d.mz_s_count,a.small_model_type " + "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " + "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " + "on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_mz d " + "on c.recipe_id=d.recipe_id " + "where a.enable_flag='Y' and c.enable_flag='Y' and d.enable_flag='Y' " + "and c.recipe_type='MODELDX' and a.make_order='" + make_order + "' " + "and a.prod_line_id=" + prod_line_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe, false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}不可用或者未配置模组配方";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String mz_id = itemListMoRecipe.get(0).get("mz_id").toString();
            String small_model_type = itemListMoRecipe.get(0).get("small_model_type").toString();
            int mz_p_count = Integer.parseInt(itemListMoRecipe.get(0).get("mz_p_count").toString());
            int mz_s_count = Integer.parseInt(itemListMoRecipe.get(0).get("mz_s_count").toString());
            //1.根据模组配方ID查找LIMIT项目
            String sqlMzLimitRecipe = "select a.mz_limit_code,a.down_limit,a.upper_limit,a.ng_rack_code," + "COALESCE(b.mz_limit_id,0) mz_limit_id," + "COALESCE(b.down_limit,0) down_limit2," + "COALESCE(b.upper_limit,0) upper_limit2," + "COALESCE(b.ng_rack_code,0) ng_rack_code2 " + "from c_mes_fmod_recipe_mz_limit_must a left join c_mes_fmod_recipe_mz_limit b " + "on a.mz_limit_code=b.mz_limit_code and b.mz_id=" + mz_id + " and b.enable_flag='Y' " + "order by a.mz_limit_index";
            List<Map<String, Object>> itemListMzLimitRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzLimitRecipe, false, request, apiRoutePath);
            //2.根据模组配方ID查找条码约束项目
            String sqlMzNgBarRecipe = "select mz_dxbar_ng_id,dxbar_ng_name,start_index,end_index," + "value_list,ng_way,ng_rack_num " + "from c_mes_fmod_recipe_mz_dxbar_ng " + "where mz_id=" + mz_id + " and enable_flag='Y'";
            List<Map<String, Object>> itemListMzNgBarRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzNgBarRecipe, false, request, apiRoutePath);
            //3.查找模组电芯配方,用于核对每个电芯
            String sqlMzDxRecipe = "select dx_num,dx_gear " + "from c_mes_fmod_recipe_mz_dx " + "where mz_id=" + mz_id + " and enable_flag='Y' " + "order by dx_num";
            List<Map<String, Object>> itemListMzDxRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzDxRecipe, false, request, apiRoutePath);

            int ng_rack_num_gear = 2;//档位NG放入曹号
            int ng_rack_num_mz_chongma = 1;//模组重码放入曹号
            int ng_rack_num_dx_chongma = 1;//电芯重码放入曹号
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    int ng_rack_code = Integer.parseInt(map.get("ng_rack_code").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    int ng_rack_code2 = Integer.parseInt(map.get("ng_rack_code2").toString());
                    if (mz_limit_id > 0) ng_rack_code = ng_rack_code2;
                    if (ng_rack_code <= 0) ng_rack_code = 1;
                    if (ng_rack_code > 3) ng_rack_code = 3;
                    if (mz_limit_code.equals("mz_gear")) ng_rack_num_gear = ng_rack_code;
                    if (mz_limit_code.equals("mz_chongma")) ng_rack_num_mz_chongma = ng_rack_code;
                    if (mz_limit_code.equals("mz_dx_chongma")) ng_rack_num_dx_chongma = ng_rack_code;
                }
            }

            Map<String, Object> mapDxRecipe = new HashMap<>();
            if (!testModeFlag.equals("Y")) {
                //判断模组电芯配方是否正确
                if (itemListMzDxRecipe == null || itemListMzDxRecipe.size() <= 0) {
                    errorMsg = "当前选择订单{" + make_order + "},查询模组电芯配方数据为空";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                for (Map<String, Object> map : itemListMzDxRecipe) {
                    String dx_num = map.get("dx_num").toString();
                    int dx_gear = Integer.parseInt(map.get("dx_gear").toString());
                    if (!mapDxRecipe.containsKey(dx_num)) mapDxRecipe.put(dx_num, dx_gear);
                }
                if (mapDxRecipe.size() != (mz_p_count * mz_s_count)) {
                    errorMsg = "当前选择订单{" + make_order + "},查询模组电芯配方数据量不等于{" + (mz_p_count * mz_s_count) / col_num + "}";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                for (int i = 1; i <= (mz_p_count * mz_s_count); i++) {
                    String dx_num = String.valueOf(i);
                    if (!mapDxRecipe.containsKey(dx_num)) {
                        errorMsg = "当前选择订单{" + make_order + "},未找到电芯序号为{" + dx_num + "}数据";
                        tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                }
                //1.根据列数,判断电芯数量
                for (int i = 0; i < mzBarCodeList.length; i++) {
                    String mz_barcode2 = mzBarCodeList[i];
                    String dx_barcode_list2 = dxBarCodeList[i];
                    String[] dxBarCodeList2 = dx_barcode_list2.split(",", -1);
                    if (mz_barcode2.equals("") || dxBarCodeList2 == null || dxBarCodeList2.length <= 0) {
                        errorMsg = "传递模组条码{" + mz_barcode2 + "}对应的电芯条码集合{" + dx_barcode_list2 + "},模组条码不能为空或者电芯数量不能<=0不匹配";
                        tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                    int needDxCount = mz_p_count * mz_s_count;
                    if (dxBarCodeList2.length != needDxCount) {
                        errorMsg = "传递模组条码{" + mz_barcode2 + "}对应的电芯条码集合{" + dx_barcode_list2 + "},列{" + col_num + "},需求电芯数量{" + needDxCount + "}," + "实际电芯数量为{" + dxBarCodeList2.length + "}";
                        tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                }
            }
            //开始执行判断
            String resultMzStatus = "";
            String resultMzId = "";
            String resultMzCode = "";
            for (int i = 0; i < mzBarCodeList.length; i++) {
                Integer mk_num = 0;
                String mz_quality_id = CFuncUtilsSystem.CreateUUID(true);
                String mz_barcode2 = mzBarCodeList[i];
                String dx_barcode_list2 = dxBarCodeList[i];
                String[] dxBarCodeList2 = dx_barcode_list2.split(",", -1);
                int[] mk_num_list = new int[dxBarCodeList2.length];
                for (int j = 0; j < dxBarCodeList2.length; j++) {
                    if (j % mz_p_count == 0) mk_num = mk_num + 1;//计算串
                    mk_num_list[j] = mk_num;
                }
                String mzResult = GetMzBarCodeNgCode(mz_quality_id, mz_barcode2, dxBarCodeList2, mk_num_list, col_num, mz_p_count, ng_rack_num_gear, ng_rack_num_mz_chongma, ng_rack_num_dx_chongma, itemListMzLimitRecipe, itemListMzNgBarRecipe, mapDxRecipe, lstAllDxBarCode, make_order, small_model_type, station_code);
                log.debug("模组码校验结果：{}  模组码为：{}", mzResult, mz_barcode2);
                String status = "NG000".equals(mzResult) ? "2" : "1";
                resultMzStatus += StringUtils.isEmpty(resultMzStatus) ? status : "," + status;
                mzResult = "NG000".equals(mzResult) ? mz_barcode2 : mzResult;
                resultMzCode += StringUtils.isEmpty(resultMzCode) ? mzResult : "," + mzResult;
                resultMzId += StringUtils.isEmpty(resultMzId) ? mz_quality_id : "," + mz_quality_id;
                //NG000

            }
            String result = resultMzCode + "|" + resultMzId + "|" + resultMzStatus;
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "模组堆叠验证异常" + ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    /**
     * 模组堆叠判断与保存数据 二次校验
     * version 2 ng时返回正常的模组码 并添加一列状态，若ok为0ng为1
     *
     * @param jsonParas
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/MesGxMzDDCheck3", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzDDCheck3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzDDCheck3";
        String tranResult = "";
        String errorMsg = "";
        try {
            //是否是测试阶段，测试阶段不需要判定，直接给合格
            String testModeFlag = cFuncDbSqlResolve.GetParameterValue("TestModeFlag");
            String station_code = jsonParas.getString("station_code");//工位号
            String prod_line_id = jsonParas.getString("prod_line_id");//生产线ID
            String make_order = jsonParas.getString("make_order");//订单号
            Integer col_num = jsonParas.getInteger("col_num");//单列或者双列
            String mz_barcode_list = jsonParas.getString("mz_barcode_list");//模组条码集合
            String dx_barcode_list = jsonParas.getString("dx_barcode_list");//电芯条码集合
            String[] mzBarCodeList = mz_barcode_list.split(",", -1);
            String[] dxBarCodeList = dx_barcode_list.split("@", -1);
            if (!testModeFlag.equals("Y")) {
                if (mzBarCodeList == null || dxBarCodeList == null || mzBarCodeList.length <= 0 || dxBarCodeList.length <= 0) {
                    errorMsg = "传递模组条码集合{" + mz_barcode_list + "},或者电芯条码集合{" + dx_barcode_list + "},不能为空";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                if (mzBarCodeList.length != dxBarCodeList.length) {
                    errorMsg = "传递模组条码集合{" + mz_barcode_list + "},或者电芯条码集合{" + dx_barcode_list + "},二者数量不匹配";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                if (col_num != 1 && col_num != 2) {
                    errorMsg = "模组只能为单列或者双列,从PLC获取该属性值{" + col_num + "}不符合要求";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
            }
            //合成全部的电芯条码用于重码判断
            List<String> lstAllDxBarCode = new ArrayList<>();
            for (int i = 0; i < dxBarCodeList.length; i++) {
                String dxBarCodeList2 = dxBarCodeList[i];
                String[] tempList = dxBarCodeList2.split(",", -1);
                for (String dxBarCode : tempList) {
                    lstAllDxBarCode.add(dxBarCode);
                }
            }

            //查找配方
            String sqlMoRecipe = "select c.recipe_id,d.mz_id,d.mz_p_count,d.mz_s_count,a.small_model_type " + "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " + "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " + "on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_mz d " + "on c.recipe_id=d.recipe_id " + "where a.enable_flag='Y' and c.enable_flag='Y' and d.enable_flag='Y' " + "and c.recipe_type='MODELDX' and a.make_order='" + make_order + "' " + "and a.prod_line_id=" + prod_line_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe, false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}不可用或者未配置模组配方";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String mz_id = itemListMoRecipe.get(0).get("mz_id").toString();
            String small_model_type = itemListMoRecipe.get(0).get("small_model_type").toString();
            int mz_p_count = Integer.parseInt(itemListMoRecipe.get(0).get("mz_p_count").toString());
            int mz_s_count = Integer.parseInt(itemListMoRecipe.get(0).get("mz_s_count").toString());
            //1.根据模组配方ID查找LIMIT项目
            String sqlMzLimitRecipe = "select a.mz_limit_code,a.down_limit,a.upper_limit,a.ng_rack_code," + "COALESCE(b.mz_limit_id,0) mz_limit_id," + "COALESCE(b.down_limit,0) down_limit2," + "COALESCE(b.upper_limit,0) upper_limit2," + "COALESCE(b.ng_rack_code,0) ng_rack_code2 " + "from c_mes_fmod_recipe_mz_limit_must a left join c_mes_fmod_recipe_mz_limit b " + "on a.mz_limit_code=b.mz_limit_code and b.mz_id=" + mz_id + " and b.enable_flag='Y' " + "order by a.mz_limit_index";
            List<Map<String, Object>> itemListMzLimitRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzLimitRecipe, false, request, apiRoutePath);
            //2.根据模组配方ID查找条码约束项目
            String sqlMzNgBarRecipe = "select mz_dxbar_ng_id,dxbar_ng_name,start_index,end_index," + "value_list,ng_way,ng_rack_num " + "from c_mes_fmod_recipe_mz_dxbar_ng " + "where mz_id=" + mz_id + " and enable_flag='Y'";
            List<Map<String, Object>> itemListMzNgBarRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzNgBarRecipe, false, request, apiRoutePath);
            //3.查找模组电芯配方,用于核对每个电芯
            String sqlMzDxRecipe = "select dx_num,dx_gear " + "from c_mes_fmod_recipe_mz_dx " + "where mz_id=" + mz_id + " and enable_flag='Y' " + "order by dx_num";
            List<Map<String, Object>> itemListMzDxRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzDxRecipe, false, request, apiRoutePath);

            int ng_rack_num_gear = 2;//档位NG放入曹号
            int ng_rack_num_mz_chongma = 1;//模组重码放入曹号
            int ng_rack_num_dx_chongma = 1;//电芯重码放入曹号
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    int ng_rack_code = Integer.parseInt(map.get("ng_rack_code").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    int ng_rack_code2 = Integer.parseInt(map.get("ng_rack_code2").toString());
                    if (mz_limit_id > 0) ng_rack_code = ng_rack_code2;
                    if (ng_rack_code <= 0) ng_rack_code = 1;
                    if (ng_rack_code > 3) ng_rack_code = 3;
                    if (mz_limit_code.equals("mz_gear")) ng_rack_num_gear = ng_rack_code;
                    if (mz_limit_code.equals("mz_chongma")) ng_rack_num_mz_chongma = ng_rack_code;
                    if (mz_limit_code.equals("mz_dx_chongma")) ng_rack_num_dx_chongma = ng_rack_code;
                }
            }

            Map<String, Object> mapDxRecipe = new HashMap<>();
            if (!testModeFlag.equals("Y")) {
                //判断模组电芯配方是否正确
                if (itemListMzDxRecipe == null || itemListMzDxRecipe.size() <= 0) {
                    errorMsg = "当前选择订单{" + make_order + "},查询模组电芯配方数据为空";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                for (Map<String, Object> map : itemListMzDxRecipe) {
                    String dx_num = map.get("dx_num").toString();
                    int dx_gear = Integer.parseInt(map.get("dx_gear").toString());
                    if (!mapDxRecipe.containsKey(dx_num)) mapDxRecipe.put(dx_num, dx_gear);
                }
                if (mapDxRecipe.size() != (mz_p_count * mz_s_count)) {
                    errorMsg = "当前选择订单{" + make_order + "},查询模组电芯配方数据量不等于{" + (mz_p_count * mz_s_count) / col_num + "}";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                for (int i = 1; i <= (mz_p_count * mz_s_count); i++) {
                    String dx_num = String.valueOf(i);
                    if (!mapDxRecipe.containsKey(dx_num)) {
                        errorMsg = "当前选择订单{" + make_order + "},未找到电芯序号为{" + dx_num + "}数据";
                        tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                }
                //1.根据列数,判断电芯数量
                for (int i = 0; i < mzBarCodeList.length; i++) {
                    String mz_barcode2 = mzBarCodeList[i];
                    String dx_barcode_list2 = dxBarCodeList[i];
                    String[] dxBarCodeList2 = dx_barcode_list2.split(",", -1);
                    if (mz_barcode2.equals("") || dxBarCodeList2 == null || dxBarCodeList2.length <= 0) {
                        errorMsg = "传递模组条码{" + mz_barcode2 + "}对应的电芯条码集合{" + dx_barcode_list2 + "},模组条码不能为空或者电芯数量不能<=0不匹配";
                        tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                    int needDxCount = mz_p_count * mz_s_count;
                    if (dxBarCodeList2.length != needDxCount) {
                        errorMsg = "传递模组条码{" + mz_barcode2 + "}对应的电芯条码集合{" + dx_barcode_list2 + "},列{" + col_num + "},需求电芯数量{" + needDxCount + "}," + "实际电芯数量为{" + dxBarCodeList2.length + "}";
                        tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                }
            }
            //开始执行判断
            String resultMzStatus = "";
            String resultMzId = "";
            String resultMzCode = "";
            for (int i = 0; i < mzBarCodeList.length; i++) {
                Integer mk_num = 0;
                String mz_quality_id = CFuncUtilsSystem.CreateUUID(true);
                String mz_barcode2 = mzBarCodeList[i];
                String dx_barcode_list2 = dxBarCodeList[i];
                String[] dxBarCodeList2 = dx_barcode_list2.split(",", -1);
                int[] mk_num_list = new int[dxBarCodeList2.length];
                for (int j = 0; j < dxBarCodeList2.length; j++) {
                    if (j % mz_p_count == 0) mk_num = mk_num + 1;//计算串
                    mk_num_list[j] = mk_num;
                }
                String mzResult = GetMzBarCodeNgCode2(mz_quality_id, mz_barcode2, dxBarCodeList2, mk_num_list, col_num, mz_p_count, ng_rack_num_gear, ng_rack_num_mz_chongma, ng_rack_num_dx_chongma, itemListMzLimitRecipe, itemListMzNgBarRecipe, mapDxRecipe, lstAllDxBarCode, make_order, small_model_type, station_code);
                String status = "NG000".equals(mzResult) ? "2" : "1";
                resultMzStatus += StringUtils.isEmpty(resultMzStatus) ? status : "," + status;
                mzResult = "NG000".equals(mzResult) ? mz_barcode2 : mzResult;
                resultMzCode += StringUtils.isEmpty(resultMzCode) ? mzResult : "," + mzResult;
                resultMzId += StringUtils.isEmpty(resultMzId) ? mz_quality_id : "," + mz_quality_id;
            }
            String result = resultMzCode + "&" + resultMzId + "&" + resultMzStatus;
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "模组堆叠验证异常" + ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //修改模组存储状态为OK
    @RequestMapping(value = "/MesGxMzDDUpdateOkStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzDDUpdateOkStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzDDUpdateOkStatus";
        String tranResult = "";
        String errorMsg = "";
        String mzQualityTable = "c_mes_me_mz_quality";
        String mzDxRelTable = "c_mes_me_mz_dx_rel";
        try {
            String station_code = jsonParas.getString("station_code");//工位号
            String make_order = jsonParas.getString("make_order");//订单号
            String prod_line_id = jsonParas.getString("prod_line_id");//生产线ID
            String mz_quality_id_list = jsonParas.getString("mz_quality_id_list");//模组ID集合
            String[] mzQualityIdList = mz_quality_id_list.split(",", -1);
            if (mzQualityIdList != null && mzQualityIdList.length > 0) {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("mz_quality_id").in(mzQualityIdList));
                Update updateBigData = new Update();
                updateBigData.set("mz_status", "OK");
                mongoTemplate.updateMulti(queryBigData, updateBigData, mzQualityTable);
                mongoTemplate.updateMulti(queryBigData, updateBigData, mzDxRelTable);
            }
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "修改模组存储状态为OK异常" + ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //判断模组与电芯合格状态
    private String GetMzBarCodeNgCode(String mz_quality_id, String mz_barcode, String[] dx_barcode_list, int[] mk_num_list, int col_num, int mz_p_count, int ng_rack_num_gear, int ng_rack_num_mz_chongma, int ng_rack_num_dx_chongma, List<Map<String, Object>> itemListMzLimitRecipe, List<Map<String, Object>> itemListMzNgBarRecipe, Map<String, Object> mapDxRecipe, List<String> lstAllDxBarCode, String make_order, String small_model_type, String station_code) throws Exception {
        String result_code = "NG000";
        String dxQualityTable = "c_mes_me_dx_quality";
        String mzQualityTable = "c_mes_me_mz_quality";
        String mzDxRelTable = "c_mes_me_mz_dx_rel";
        try {
            double mz_capacity_diff = 0;
            double mz_capacity_plus = 0;
            double mz_min_capacity = 0;
            double mz_max_capacity = 0;
            String mz_status = "NG";
            int ng_rack = 1;
            int ng_code = -1;
            String ng_msg = "";
            //是否是测试阶段，测试阶段不需要判定，直接给合格
            String testModeFlag = cFuncDbSqlResolve.GetParameterValue("TestModeFlag");
            if (testModeFlag.equals("Y")) {
                //终于全部判断完成OK
                result_code = mz_barcode;
                ng_rack = 0;
                ng_code = 0;
                ng_msg = "";
                mz_status = "WAIT";
                InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                return result_code;
            }
            //1.判断模组码是否重码
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mz_barcode").is(mz_barcode));
            queryBigData.addCriteria(Criteria.where("mz_status").is("OK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(mzQualityTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            Boolean isExistMz = false;
            if (iteratorBigData.hasNext()) {
                isExistMz = true;
                iteratorBigData.close();
            }
            if (isExistMz) {
                result_code = "NG" + String.format("%03d", ng_rack_num_mz_chongma - 1);
                ng_rack = ng_rack_num_mz_chongma;
                ng_code = -1;
                ng_msg = "模组码{" + mz_barcode + "},发生重码";
                InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                return result_code;
            }

            //2.判断电芯是否重码
            //2.1 本身判断重码
            for (String dx_barcode : dx_barcode_list) {
                int index = 0;
                for (int i = 0; i < lstAllDxBarCode.size(); i++) {
                    if (lstAllDxBarCode.get(i).equals(dx_barcode)) index++;
                }
                if (index > 1) {
                    result_code = "NG" + String.format("%03d", ng_rack_num_dx_chongma - 1);
                    ng_rack = ng_rack_num_dx_chongma;
                    ng_code = -2;
                    ng_msg = "模组码{" + mz_barcode + "},内电芯码{" + dx_barcode + "}发生重码,PLC检查R1拍照或者托盘RFID";
                    InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                    return result_code;
                }
            }
            //2.2 数据库判断重码
            List<String> lstExsitDx = new ArrayList<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").in(dx_barcode_list));
            queryBigData.addCriteria(Criteria.where("mz_status").is("OK"));
            iteratorBigData = mongoTemplate.getCollection(mzDxRelTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(dx_barcode_list.length).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String dx_barcode = docItemBigData.getString("dx_barcode");
                lstExsitDx.add(dx_barcode);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (lstExsitDx.size() > 0) {
                String dx_barcode_exist = "";
                for (String dx_barcode : lstExsitDx) {
                    dx_barcode_exist += dx_barcode + ",";
                }
                result_code = "NG" + String.format("%03d", ng_rack_num_dx_chongma - 1);
                ng_rack = ng_rack_num_dx_chongma;
                ng_code = -2;
                ng_msg = "模组码{" + mz_barcode + "},内电芯码{" + dx_barcode_exist + "}发生重码,电芯已做其他模组绑定";
                InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                return result_code;
            }

            //3.判断档位是否一致
            Map<String, String> mapDxQuality = new HashMap<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").in(dx_barcode_list));
            queryBigData.addCriteria(Criteria.where("dx_status").is("OK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(dxQualityTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(dx_barcode_list.length).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String dx_barcode = docItemBigData.getString("dx_barcode");
                int dx_gear = docItemBigData.getInteger("dx_gear");
                String dx_ori_batch = docItemBigData.getString("dx_ori_batch");
                double dx_ori_capacity = docItemBigData.getDouble("dx_ori_capacity");
                String value_list = dx_gear + "," + dx_ori_batch + "," + dx_ori_capacity;
                if (!mapDxQuality.containsKey(dx_barcode)) mapDxQuality.put(dx_barcode, value_list);
                if (mapDxQuality.size() >= dx_barcode_list.length) break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            int single_dx_count = dx_barcode_list.length / col_num;
            for (int i = 0; i < col_num; i++) {
                for (int j = 0; j < single_dx_count; j++) {
                    int index = j + (i * single_dx_count);
                    String dx_barcode = dx_barcode_list[index];
                    if (!mapDxQuality.containsKey(dx_barcode)) {
                        result_code = "NG" + String.format("%03d", ng_rack_num_gear - 1);
                        ng_rack = ng_rack_num_gear;
                        ng_code = -3;
                        ng_msg = "模组码{" + mz_barcode + "},电芯码{" + dx_barcode + "},未经过电芯OCV测试工位测试OK";
                        InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                        return result_code;
                    }
                    if (!mapDxRecipe.containsKey(String.valueOf(j + 1))) {
                        result_code = "NG" + String.format("%03d", ng_rack_num_gear - 1);
                        ng_rack = ng_rack_num_gear;
                        ng_code = -4;
                        ng_msg = "模组码{" + mz_barcode + "},电芯码{" + dx_barcode + "},电芯序号{" + j + 1 + "}未设置在模组电芯配方中";
                        InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                        return result_code;
                    }
                    //电芯质量数据中的dx_gear
                    String dxQualityValue = mapDxQuality.get(dx_barcode);
                    String[] lstQValue = dxQualityValue.split(",", -1);
                    int dx_gear_Q = Integer.parseInt(lstQValue[0]);
                    int dx_gear = Integer.parseInt(mapDxRecipe.get(String.valueOf(j + 1)).toString());
                    if (dx_gear_Q != dx_gear) {
                        result_code = "NG" + String.format("%03d", ng_rack_num_gear - 1);
                        ng_rack = ng_rack_num_gear;
                        ng_code = -5;
                        ng_msg = "模组码{" + mz_barcode + "},电芯码{" + dx_barcode + "},条码档位为{" + dx_gear_Q + "},与配方中要求档位{" + dx_gear + "}不符合";
                        InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                        return result_code;
                    }
                }
            }


            //计算电容和、电容差、最大电容、最小电容
            List<String> lstBatch = new ArrayList<>();
            Map<String, Object> mapContainer = new HashMap<>();
            Integer mk_num = 0;
            for (int i = 0; i < col_num; i++) {
                double p_sum_container = 0;//P范围内容器和
                for (int j = 0; j < single_dx_count; j++) {
                    int index = j + (i * single_dx_count);
                    String dx_barcode = dx_barcode_list[index];
                    String dxQualityValue = mapDxQuality.get(dx_barcode);
                    String[] lstQValue = dxQualityValue.split(",", -1);
                    String dx_batch_Q = lstQValue[1];
                    double dx_capacity_Q = Double.parseDouble(lstQValue[2]);
                    if (!lstBatch.contains(dx_batch_Q)) lstBatch.add(dx_batch_Q);
                    if (j % mz_p_count == 0) mk_num = mk_num + 1;//计算串
                    Integer leftCount = (j + 1) % mz_p_count;
                    mz_capacity_plus += dx_capacity_Q;//计算电容和
                    p_sum_container += dx_capacity_Q;
                    if (leftCount == 0) {
                        if (mk_num == 1) {
                            mz_max_capacity = p_sum_container;
                            mz_min_capacity = p_sum_container;
                        } else {
                            if (p_sum_container > mz_max_capacity) mz_max_capacity = p_sum_container;
                            if (p_sum_container < mz_min_capacity) mz_min_capacity = p_sum_container;
                        }
                        mapContainer.put(String.valueOf(mk_num), p_sum_container);
                        p_sum_container = 0;
                    }
                }
            }
            mz_capacity_diff = mz_max_capacity - mz_min_capacity;

            //4.依次做对配方设置做判断
            //4.1 ☆☆☆依次对模组逻辑进行判断
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    double down_limit = Double.parseDouble(map.get("down_limit").toString());
                    double upper_limit = Double.parseDouble(map.get("upper_limit").toString());
                    int ng_rack_code = Integer.parseInt(map.get("ng_rack_code").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    double down_limit2 = Double.parseDouble(map.get("down_limit2").toString());
                    double upper_limit2 = Double.parseDouble(map.get("upper_limit2").toString());
                    int ng_rack_code2 = Integer.parseInt(map.get("ng_rack_code2").toString());
                    if (mz_limit_id > 0) ng_rack_code = ng_rack_code2;
                    if (ng_rack_code <= 0) ng_rack_code = 1;
                    if (ng_rack_code > 3) ng_rack_code = 3;
                    if (mz_limit_id > 0) {
                        down_limit = down_limit2;
                        upper_limit = upper_limit2;
                    }
                    //模组批次NG槽位设定(判断批次数量)
                    if (mz_limit_code.equals("mz_batch") && mz_limit_id > 0) {
                        if (lstBatch.size() < down_limit || lstBatch.size() > upper_limit) {
                            result_code = "NG" + String.format("%03d", ng_rack_code - 1);
                            ng_rack = ng_rack_code;
                            ng_code = -6;
                            ng_msg = "模组码{" + mz_barcode + "},批次总数量为{" + lstBatch.size() + "},大于配方中要求批次数量{" + upper_limit + "}";
                            InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                            return result_code;
                        }
                    }
                    //模组双电芯电容范围判断
                    if (mz_limit_code.equals("mz_dd_container") && mz_limit_id > 0) {
                        Set<Map.Entry<String, Object>> entries = mapContainer.entrySet();
                        for (Map.Entry<String, Object> entry : entries) {
                            String mk_num2 = entry.getKey();
                            double mz_dd_container = Double.parseDouble(entry.getValue().toString());
                            if (mz_dd_container < down_limit || mz_dd_container > upper_limit) {
                                result_code = "NG" + String.format("%03d", ng_rack_code - 1);
                                ng_rack = ng_rack_code;
                                ng_code = -7;
                                ng_msg = "模组码{" + mz_barcode + "},串{" + mk_num2 + "},电容和为{" + mz_dd_container + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                                return result_code;
                            }
                        }
                    }
                }
            }

            //终于全部判断完成OK
            result_code = mz_barcode;
            ng_rack = 0;
            ng_code = 0;
            ng_msg = "";
            mz_status = "WAIT";
            InsertMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
        } catch (Exception ex) {
            throw ex;
        }
        return result_code;
    }

    /**
     * 判断模组与电芯合格状态  替换电芯后的二次校验
     *
     * @param mz_quality_id
     * @param mz_barcode
     * @param dx_barcode_list
     * @param mk_num_list
     * @param col_num
     * @param mz_p_count
     * @param ng_rack_num_gear
     * @param ng_rack_num_mz_chongma
     * @param ng_rack_num_dx_chongma
     * @param itemListMzLimitRecipe
     * @param itemListMzNgBarRecipe
     * @param mapDxRecipe
     * @param lstAllDxBarCode
     * @param make_order
     * @param small_model_type
     * @param station_code
     * @return
     * @throws Exception
     */
    private String GetMzBarCodeNgCode2(String mz_quality_id, String mz_barcode, String[] dx_barcode_list, int[] mk_num_list, int col_num, int mz_p_count, int ng_rack_num_gear, int ng_rack_num_mz_chongma, int ng_rack_num_dx_chongma, List<Map<String, Object>> itemListMzLimitRecipe, List<Map<String, Object>> itemListMzNgBarRecipe, Map<String, Object> mapDxRecipe, List<String> lstAllDxBarCode, String make_order, String small_model_type, String station_code) throws Exception {
        String result_code = "NG000";
        String dxQualityTable = "c_mes_me_dx_quality";
        String mzQualityTable = "c_mes_me_mz_quality";
        String mzDxRelTable = "c_mes_me_mz_dx_rel";
        try {
            double mz_capacity_diff = 0;
            double mz_capacity_plus = 0;
            double mz_min_capacity = 0;
            double mz_max_capacity = 0;
            String mz_status = "NG";
            int ng_rack = 1;
            int ng_code = -1;
            String ng_msg = "";
            //是否是测试阶段，测试阶段不需要判定，直接给合格
            String testModeFlag = cFuncDbSqlResolve.GetParameterValue("TestModeFlag");
            if (testModeFlag.equals("Y")) {
                //终于全部判断完成OK
                result_code = mz_barcode;
                ng_rack = 0;
                ng_code = 0;
                ng_msg = "";
                mz_status = "OK";
                updateMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                return result_code;
            }
            //1.判断模组码是否重码
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mz_barcode").is(mz_barcode));
            queryBigData.addCriteria(Criteria.where("mz_status").is("NG"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(mzQualityTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            Boolean isExistMz = false;
            if (iteratorBigData.hasNext()) {
                isExistMz = true;
                Document next = iteratorBigData.next();
                mz_quality_id = next.containsKey("mz_quality_id") ? next.getString("mz_quality_id") : mz_quality_id;
                iteratorBigData.close();
            } else {
                //二次校验如果没有模组质量直接报错
                throw new IllegalStateException("模组质量信息查询不到");
            }
            //2.判断电芯是否重码
            //2.1 本身判断重码
            for (String dx_barcode : dx_barcode_list) {
                int index = 0;
                for (int i = 0; i < lstAllDxBarCode.size(); i++) {
                    if (lstAllDxBarCode.get(i).equals(dx_barcode)) index++;
                }
                if (index > 1) {
                    result_code = "NG" + String.format("%03d", ng_rack_num_dx_chongma - 1);
                    ng_rack = ng_rack_num_dx_chongma;
                    ng_code = -2;
                    ng_msg = "模组码{" + mz_barcode + "},内电芯码{" + dx_barcode + "}发生重码,PLC检查R1拍照或者托盘RFID";
                    updateMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                    return result_code;
                }
            }
            //2.2 数据库判断重码
            List<String> lstExsitDx = new ArrayList<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").in(dx_barcode_list));
            queryBigData.addCriteria(Criteria.where("mz_status").is("OK"));
            iteratorBigData = mongoTemplate.getCollection(mzDxRelTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(dx_barcode_list.length).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String dx_barcode = docItemBigData.getString("dx_barcode");
                lstExsitDx.add(dx_barcode);
            }
            //去重后的
            List<String> distinct = lstExsitDx.stream().distinct().collect(Collectors.toList());
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (lstExsitDx.size() != distinct.size()) {
                result_code = "NG" + String.format("%03d", ng_rack_num_dx_chongma - 1);
                ng_rack = ng_rack_num_dx_chongma;
                ng_code = -2;
                ng_msg = "模组码{" + mz_barcode + "},内电芯码{" + lstExsitDx.toString().replace("[", "").replace("]", "") + "}发生重码,电芯已做其他模组绑定";
                updateMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                throw new IllegalStateException(ng_msg);
            }

            //3.判断档位是否一致
            Map<String, String> mapDxQuality = new HashMap<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").in(dx_barcode_list));
            queryBigData.addCriteria(Criteria.where("dx_status").is("OK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(dxQualityTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(dx_barcode_list.length).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String dx_barcode = docItemBigData.getString("dx_barcode");
                int dx_gear = docItemBigData.getInteger("dx_gear");
                String dx_ori_batch = docItemBigData.getString("dx_ori_batch");
                double dx_ori_capacity = docItemBigData.getDouble("dx_ori_capacity");
                String value_list = dx_gear + "," + dx_ori_batch + "," + dx_ori_capacity;
                if (!mapDxQuality.containsKey(dx_barcode)) mapDxQuality.put(dx_barcode, value_list);
                if (mapDxQuality.size() >= dx_barcode_list.length) break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            int single_dx_count = dx_barcode_list.length / col_num;
            for (int i = 0; i < col_num; i++) {
                for (int j = 0; j < single_dx_count; j++) {
                    int index = j + (i * single_dx_count);
                    String dx_barcode = dx_barcode_list[index];
                    if (!mapDxQuality.containsKey(dx_barcode)) {
                        result_code = "NG" + String.format("%03d", ng_rack_num_gear - 1);
                        ng_rack = ng_rack_num_gear;
                        ng_code = -3;
                        ng_msg = "模组码{" + mz_barcode + "},电芯码{" + dx_barcode + "},未经过电芯OCV测试工位测试OK";
                        updateMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                        throw new IllegalStateException(ng_msg);
                    }
                    if (!mapDxRecipe.containsKey(String.valueOf(j + 1))) {
                        result_code = "NG" + String.format("%03d", ng_rack_num_gear - 1);
                        ng_rack = ng_rack_num_gear;
                        ng_code = -4;
                        ng_msg = "模组码{" + mz_barcode + "},电芯码{" + dx_barcode + "},电芯序号{" + j + 1 + "}未设置在模组电芯配方中";
                        updateMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                        throw new IllegalStateException(ng_msg);
                    }
                    //电芯质量数据中的dx_gear
                    String dxQualityValue = mapDxQuality.get(dx_barcode);
                    String[] lstQValue = dxQualityValue.split(",", -1);
                    int dx_gear_Q = Integer.parseInt(lstQValue[0]);
                    int dx_gear = Integer.parseInt(mapDxRecipe.get(String.valueOf(j + 1)).toString());
                    if (dx_gear_Q != dx_gear) {
                        result_code = "NG" + String.format("%03d", ng_rack_num_gear - 1);
                        ng_rack = ng_rack_num_gear;
                        ng_code = -5;
                        ng_msg = "模组码{" + mz_barcode + "},电芯码{" + dx_barcode + "},条码档位为{" + dx_gear_Q + "},与配方中要求档位{" + dx_gear + "}不符合";
                        updateMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                        throw new IllegalStateException(ng_msg);
                    }
                }
            }


            //计算电容和、电容差、最大电容、最小电容
            List<String> lstBatch = new ArrayList<>();
            Map<String, Object> mapContainer = new HashMap<>();
            Integer mk_num = 0;
            for (int i = 0; i < col_num; i++) {
                double p_sum_container = 0;//P范围内容器和
                for (int j = 0; j < single_dx_count; j++) {
                    int index = j + (i * single_dx_count);
                    String dx_barcode = dx_barcode_list[index];
                    String dxQualityValue = mapDxQuality.get(dx_barcode);
                    String[] lstQValue = dxQualityValue.split(",", -1);
                    String dx_batch_Q = lstQValue[1];
                    double dx_capacity_Q = Double.parseDouble(lstQValue[2]);
                    if (!lstBatch.contains(dx_batch_Q)) lstBatch.add(dx_batch_Q);
                    if (j % mz_p_count == 0) mk_num = mk_num + 1;//计算串
                    Integer leftCount = (j + 1) % mz_p_count;
                    mz_capacity_plus += dx_capacity_Q;//计算电容和
                    p_sum_container += dx_capacity_Q;
                    if (leftCount == 0) {
                        if (mk_num == 1) {
                            mz_max_capacity = p_sum_container;
                            mz_min_capacity = p_sum_container;
                        } else {
                            if (p_sum_container > mz_max_capacity) mz_max_capacity = p_sum_container;
                            if (p_sum_container < mz_min_capacity) mz_min_capacity = p_sum_container;
                        }
                        mapContainer.put(String.valueOf(mk_num), p_sum_container);
                        p_sum_container = 0;
                    }
                }
            }
            mz_capacity_diff = mz_max_capacity - mz_min_capacity;

            //4.依次做对配方设置做判断
            //4.1 ☆☆☆依次对模组逻辑进行判断
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    double down_limit = Double.parseDouble(map.get("down_limit").toString());
                    double upper_limit = Double.parseDouble(map.get("upper_limit").toString());
                    int ng_rack_code = Integer.parseInt(map.get("ng_rack_code").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    double down_limit2 = Double.parseDouble(map.get("down_limit2").toString());
                    double upper_limit2 = Double.parseDouble(map.get("upper_limit2").toString());
                    int ng_rack_code2 = Integer.parseInt(map.get("ng_rack_code2").toString());
                    if (mz_limit_id > 0) ng_rack_code = ng_rack_code2;
                    if (ng_rack_code <= 0) ng_rack_code = 1;
                    if (ng_rack_code > 3) ng_rack_code = 3;
                    if (mz_limit_id > 0) {
                        down_limit = down_limit2;
                        upper_limit = upper_limit2;
                    }
                    //模组批次NG槽位设定(判断批次数量)
                    if (mz_limit_code.equals("mz_batch") && mz_limit_id > 0) {
                        if (lstBatch.size() < down_limit || lstBatch.size() > upper_limit) {
                            result_code = "NG" + String.format("%03d", ng_rack_code - 1);
                            ng_rack = ng_rack_code;
                            ng_code = -6;
                            ng_msg = "模组码{" + mz_barcode + "},批次总数量为{" + lstBatch.size() + "},大于配方中要求批次数量{" + upper_limit + "}";
                            updateMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                            throw new IllegalStateException(ng_msg);
                        }
                    }
                    //模组双电芯电容范围判断
                    if (mz_limit_code.equals("mz_dd_container") && mz_limit_id > 0) {
                        Set<Map.Entry<String, Object>> entries = mapContainer.entrySet();
                        for (Map.Entry<String, Object> entry : entries) {
                            String mk_num2 = entry.getKey();
                            double mz_dd_container = Double.parseDouble(entry.getValue().toString());
                            if (mz_dd_container < down_limit || mz_dd_container > upper_limit) {
                                result_code = "NG" + String.format("%03d", ng_rack_code - 1);
                                ng_rack = ng_rack_code;
                                ng_code = -7;
                                ng_msg = "模组码{" + mz_barcode + "},串{" + mk_num2 + "},电容和为{" + mz_dd_container + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                updateMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
                                throw new IllegalStateException(ng_msg);
                            }
                        }
                    }
                }
            }

            //终于全部判断完成OK
            result_code = mz_barcode;
            ng_rack = 0;
            ng_code = 0;
            ng_msg = "";
            mz_status = "OK";
            updateMzQuality(mz_quality_id, mz_barcode, make_order, small_model_type, col_num, mz_capacity_diff, mz_capacity_plus, mz_min_capacity, mz_max_capacity, dx_barcode_list, mk_num_list, mz_status, ng_rack, ng_code, ng_msg, station_code);
        } catch (Exception ex) {
            throw ex;
        }
        return result_code;
    }

    //插入模组质量数据以及电芯关系数据
    private void InsertMzQuality(String mz_quality_id, String mz_barcode, String make_order, String small_model_type, int col_num, double mz_capacity_diff, double mz_capacity_plus, double mz_min_capacity, double mz_max_capacity, String[] dx_barcode_list, int[] mk_num_list, String mz_status, int ng_rack, int ng_code, String ng_msg, String station_code) throws Exception {
        try {
            String mzQualityTable = "c_mes_me_mz_quality";
            String mzDxRelTable = "c_mes_me_mz_dx_rel";
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("mz_quality_id", mz_quality_id);
            mapBigDataRow.put("make_order", make_order);
            mapBigDataRow.put("small_model_type", small_model_type);
            mapBigDataRow.put("station_code", station_code);
            mapBigDataRow.put("mz_barcode", mz_barcode);
            mapBigDataRow.put("mz_status", mz_status);
            mapBigDataRow.put("col_num", col_num);
            mapBigDataRow.put("ng_rack", ng_rack);
            mapBigDataRow.put("ng_code", ng_code);
            mapBigDataRow.put("ng_msg", ng_msg);
            mapBigDataRow.put("mz_capacity_diff", mz_capacity_diff);
            mapBigDataRow.put("mz_capacity_plus", mz_capacity_plus);
            mapBigDataRow.put("mz_min_capacity", mz_min_capacity);
            mapBigDataRow.put("mz_max_capacity", mz_max_capacity);
            mapBigDataRow.put("up_flag", "N");
            mapBigDataRow.put("up_ng_code", 0);
            mapBigDataRow.put("up_ng_msg", "");
            mapBigDataRow.put("enable_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, mzQualityTable);
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            for (int i = 0; i < dx_barcode_list.length; i++) {
                Map<String, Object> mapDataItem = new HashMap<>();
                String mz_dx_rel_id = CFuncUtilsSystem.CreateUUID(true);
                String dx_barcode = dx_barcode_list[i];
                int mk_num = mk_num_list[i];
                mapDataItem.put("item_date", item_date);
                mapDataItem.put("item_date_val", item_date_val);
                mapDataItem.put("mz_dx_rel_id", mz_dx_rel_id);
                mapDataItem.put("mz_quality_id", mz_quality_id);
                mapDataItem.put("dx_barcode", dx_barcode);
                mapDataItem.put("mk_num", mk_num);
                mapDataItem.put("mz_status", mz_status);
                mapDataItem.put("dx_index", i + 1);
                lstDocuments.add(mapDataItem);
            }
            if (lstDocuments.size() > 0) {
                mongoTemplate.insert(lstDocuments, mzDxRelTable);
            }
//            if ("JZGX".equals(cFuncDbSqlResolve.GetParameterValue("ProjectCode"))) {
//                reportDxBindingRelationship(mapBigDataRow, lstDocuments);
//            }
        } catch (Exception ex) {
            throw ex;
        }
    }

    public List<Document> getLatestRecords(String collectionName, String groupField, String sortField) {
        Aggregation aggregation = Aggregation.newAggregation(Aggregation.group(groupField) // 分组字段
                        .last(sortField).as(sortField), // 取每个组中最新的文档字段
                Aggregation.sort(Sort.Direction.DESC, sortField) // 根据时间戳字段降序排序
        );

        AggregationResults<Document> result = mongoTemplate.aggregate(aggregation, collectionName, Document.class);
        return result.getMappedResults();
    }

    //上报金寨绑定关系
    @Async
    public void reportDxBindingRelationship(Map<String, Object> mzInfo, List<Map<String, Object>> dxInfos) {
        String errorMsg = "";
        String mesTenantID = cFuncDbSqlResolve.GetParameterValue("MesTenantID");
        String processInfo = cFuncDbSqlResolve.GetParameterValue("ProcessInfo");
        String esbInterfCode = "MesGxQualityUpToTopMesTask";
        String requestParas = "";
        JSONObject jbResult = null;
        String token = null;
        String responseParas = "";
        boolean isSaveFlag = true;
        boolean successFlag = true;
        String message = "上传成功";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        try {
            List<Document> latestRecords = getLatestRecords("c_mes_me_dx_quality", "dx_barcode", "item_date_val");
            Aggregation aggregation = Aggregation.newAggregation(Aggregation.group("dx_barcode").max("item_date_val").as("item_date_val").last("$$ROOT").as("latest"), Aggregation.match(Criteria.where("_id").in(dxInfos.stream().map(m -> {
                if (m.containsKey("dx_barcode") && !StringUtils.isEmpty(m.get("dx_barcode"))) {
                    return m.get("dx_barcode").toString();
                }
                return "";
            }).collect(Collectors.toList())).and("up_flag").exists(false)));
            // 执行聚合操作
            List<Document> dxQuelity = mongoTemplate.aggregate(aggregation, "c_mes_me_dx_quality", Document.class).getMappedResults();
            JSONObject processJson = JSONObject.parseObject(processInfo);
            JSONObject request = new JSONObject();
            String station_code = mzInfo.get("station_code").toString();
            JSONObject jsonObject = processJson.getJSONObject(station_code);
            if (jsonObject == null) {
                return;
            }
            request.put("tenantID", mesTenantID);
            request.put("produceOrderCode", mzInfo.get("make_order"));
            request.put("technicsProcessCode", jsonObject.getString("technicsProcessCode"));
            request.put("technicsProcessName", jsonObject.getString("technicsProcessName"));
            request.put("technicsStepCode", "");
            request.put("technicsStepName", "");
            request.put("productCode", mzInfo.get("mz_barcode"));
            request.put("productCount", 1);
            request.put("productQuality", "NG".equals(mzInfo.get("mz_status")) ? "0" : "1");
            request.put("ngReason", mzInfo.get("ng_msg"));
            String date = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            request.put("produceDate", date);
            request.put("startTime", date);
            request.put("endTime", date);
            request.put("userName", "");
            request.put("userAccount", "");
            request.put("deviceCode", jsonObject.getString("deviceCode"));
            request.put("deviceName", jsonObject.getString("deviceName"));
            request.put("remarks", "");
            JSONObject processMapping = jsonObject.getJSONObject("processMapping");
            List<JSONObject> jsonList = new ArrayList<>();
            for (int i = 0; i < dxQuelity.size(); i++) {
                Set<String> keys = processMapping.keySet();
                Document dxDocument = dxQuelity.get(i);
                if (!dxDocument.containsKey("latest")) {
                    continue;
                }
                Document latest = dxDocument.get("latest", Document.class);
                for (String key : keys) {
                    if (latest.containsKey(key)) {
                        JSONObject json = new JSONObject();
                        JSONObject jsonObject1 = processMapping.getJSONObject(key);
                        json.put("productCode", latest.get("dx_barcode"));
                        json.put("technicsParamName", jsonObject1.get("tag_des"));
                        json.put("technicsParamCode", jsonObject1.get("group_name"));
                        if (latest.get(key) instanceof Date) {
                            json.put("technicsParamValue", sdf.format(latest.get(key)));
                        } else {
                            json.put("technicsParamValue", StringUtils.isEmpty(latest.get(key)) ? "1" : latest.get(key));
                        }
                        json.put("technicsParamUnit", "");
                        json.put("technicsParamQuality", "NG".equals(latest.get("dx_status")) ? "1" : "0");
                        json.put("desc", "");
                        jsonList.add(json);
                    }
                }
            }
            Map<String, List<JSONObject>> productCode = jsonList.stream().collect(Collectors.groupingBy(g -> g.get("productCode").toString()));
            request.put("produceParamEntityList", jsonList);
            JSONArray produceInEntityList = new JSONArray();
            Set<String> keys = productCode.keySet();
            for (String key : keys) {
                JSONObject entity = new JSONObject();
                entity.put("productCode", key);
                entity.put("productCount", 1);
                produceInEntityList.add(entity);
            }
            request.put("produceInEntityList", produceInEntityList);
            log.debug(" request info{}", request.toJSONString());

            //            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            log.debug("interface config:{}", itemList);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            String sqlFactory = "select COALESCE(factory_code,'') factory_code " + "from sys_fmod_prod_line LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListFactory = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlFactory, false, null, "");
            String factoryCode = itemListFactory.get(0).get("factory_code").toString();
            if (factoryCode == null || factoryCode.equals("")) {
                errorMsg = "生产线未设置factory_code";
                throw new Exception(errorMsg);
            }
            requestParas = request.toJSONString();
            //调用国轩MES接口
            JSONObject jsonObjectMes = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, request);
            log.debug("response :{}", jsonObjectMes);
            responseParas = jsonObjectMes.toString();
            Integer backCode = jsonObjectMes.getInteger("code");
            String backMsg = jsonObjectMes.getString("message");
            Assert.isTrue(backCode == 200, "上传失败,国轩MES返回错误代码为{" + backCode + "},错误信息为{" + backMsg + "}");
            //上传成功
            UpdateMzQualityUpStatus(dxQuelity.stream().map(m -> m.getObjectId("_id").toString()).collect(Collectors.toList()));
        } catch (Exception e) {
            //上传成功
            isSaveFlag = true;
            successFlag = false;
            message = "上传失败：" + e.getMessage();
        }
        Boolean prodFlag = true;
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
        }

    }

    //修改上传标识
    private void UpdateMzQualityUpStatus(List<String> ids) {
        String stationQualityTable = "c_mes_me_dx_quality";
        try {
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("_id").in(ids));
            Update updateBigData = new Update();
            updateBigData.set("up_flag", "Y");
            mongoTemplate.updateFirst(queryBigData, updateBigData, stationQualityTable);
        } catch (Exception ex) {
            log.error("ID{" + ids + "},上传消息发生异常:" + ex.getMessage());
        }
    }

    private void updateMzQuality(String mz_quality_id, String mz_barcode, String make_order, String small_model_type, int col_num, double mz_capacity_diff, double mz_capacity_plus, double mz_min_capacity, double mz_max_capacity, String[] dx_barcode_list, int[] mk_num_list, String mz_status, int ng_rack, int ng_code, String ng_msg, String station_code) throws Exception {
        try {
            String mzQualityTable = "c_mes_me_mz_quality";
            String mzDxRelTable = "c_mes_me_mz_dx_rel";
            Query query = new Query();
            query.addCriteria(Criteria.where("mz_quality_id").is(mz_quality_id));
            Update update = new Update();
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            update.set("item_date", item_date);
            update.set("item_date_val", item_date_val);
            update.set("mz_quality_id", mz_quality_id);
            update.set("make_order", make_order);
            update.set("small_model_type", small_model_type);
            update.set("station_code", station_code);
            update.set("mz_barcode", mz_barcode);
            update.set("mz_status", mz_status);
            update.set("col_num", col_num);
            update.set("ng_rack", ng_rack);
            update.set("ng_code", ng_code);
            update.set("ng_msg", ng_msg);
            update.set("mz_capacity_diff", mz_capacity_diff);
            update.set("mz_capacity_plus", mz_capacity_plus);
            update.set("mz_min_capacity", mz_min_capacity);
            update.set("mz_max_capacity", mz_max_capacity);
            update.set("up_flag", "N");
            update.set("up_ng_code", 0);
            update.set("up_ng_msg", "");
            update.set("enable_flag", "Y");
            mongoTemplate.updateMulti(query, update, mzQualityTable);
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            Query removeQuery = new Query();
            removeQuery.addCriteria(Criteria.where("mz_quality_id").is(mz_quality_id));
            mongoTemplate.remove(removeQuery, mzDxRelTable);
            for (int i = 0; i < dx_barcode_list.length; i++) {
                Map<String, Object> mapDataItem = new HashMap<>();
                String mz_dx_rel_id = CFuncUtilsSystem.CreateUUID(true);
                String dx_barcode = dx_barcode_list[i];
                int mk_num = mk_num_list[i];
                mapDataItem.put("item_date", item_date);
                mapDataItem.put("item_date_val", item_date_val);
                mapDataItem.put("mz_dx_rel_id", mz_dx_rel_id);
                mapDataItem.put("mz_quality_id", mz_quality_id);
                mapDataItem.put("dx_barcode", dx_barcode);
                mapDataItem.put("mk_num", mk_num);
                mapDataItem.put("mz_status", mz_status);
                mapDataItem.put("dx_index", i + 1);
                lstDocuments.add(mapDataItem);
            }
            if (lstDocuments.size() > 0) {
                mongoTemplate.insert(lstDocuments, mzDxRelTable);
            }
//            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
//            if ("JZGX".equals(projectCode)) {
//                reportDxBindingRelationship(update.getUpdateObject(), lstDocuments);
//            }
        } catch (Exception ex) {
            throw ex;
        }
    }
}
