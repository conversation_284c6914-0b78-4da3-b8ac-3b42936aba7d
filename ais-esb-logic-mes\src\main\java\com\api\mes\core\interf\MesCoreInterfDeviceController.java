package com.api.mes.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 1.设备上报MES质量数据
 * 2.设备上报MES报警数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-05
 */
@RestController
@Slf4j
@RequestMapping("/mes/interf/core")
public class MesCoreInterfDeviceController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //1.设备上报MES质量数据
    @RequestMapping(value = "/MesInterfCoreQTraceData", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesInterfCoreQTraceData(@RequestBody JSONObject jsonParas,HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/interf/core/MesInterfCoreQTraceData";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=recvDeviceQualityData(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //处理设备接收质量数据
    private JSONObject recvDeviceQualityData(JSONObject jsonParas,HttpServletRequest request,String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String esbInterfCode="MesInterfCoreQTraceData";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbResponse=null;
        String request_uuid=CFuncUtilsSystem.CreateUUID(false);
        String meQualityTable="c_mes_me_station_quality";
        String meStationFlowTable="c_mes_me_station_flow";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            request_uuid=jsonParas.getString("request_uuid");
            String station_code=jsonParas.getString("station_code");
            String serial_num=jsonParas.getString("serial_num");
            JSONArray quanlity_list=jsonParas.getJSONArray("quanlity_list");

            //返回初始化
            jbResponse=new JSONObject();
            jbResponse.put("response_uuid",request_uuid);
            jbResponse.put("response_time", CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
            jbResponse.put("response_attr","");
            jbResponse.put("code",0);
            jbResponse.put("msg","");
            jbResponse.put("data",new JSONObject());

            if(serial_num==null || serial_num.equals("")){
                errorMsg="工件编号不能为空";
                jbResponse.put("code",-1);
                jbResponse.put("msg",errorMsg);
                responseParas= jbResponse.toString();
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            if(station_code==null || station_code.equals("")){
                errorMsg="工位号不能为空";
                jbResponse.put("code",-2);
                jbResponse.put("msg",errorMsg);
                responseParas= jbResponse.toString();
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            if(quanlity_list==null || quanlity_list.size()<=0){
                errorMsg="传递质量数组不能为空";
                jbResponse.put("code",-3);
                jbResponse.put("msg",errorMsg);
                responseParas= jbResponse.toString();
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //1.判断工位号是否符合设定
            String sqlProdLine="select b.prod_line_code " +
                    "from sys_fmod_station a inner join sys_fmod_prod_line b " +
                    "on a.prod_line_id=b.prod_line_id " +
                    "where a.station_code='"+station_code+"'";
            List<Map<String, Object>> itemListLine=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlProdLine,
                    false,request,apiRoutePath);
            if(itemListLine==null || itemListLine.size()<=0){
                errorMsg="工位号{"+station_code+"}传递错误,请和MES设置工位号一致";
                jbResponse.put("code",-4);
                jbResponse.put("msg",errorMsg);
                responseParas= jbResponse.toString();
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String prod_line_code=itemListLine.get(0).get("prod_line_code").toString();

            //1.根据工位号和序列号查找到过站ID
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
            long flowCount=mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
            if(flowCount<=0){//若无过站则需要进行增加
                JSONObject postParas=new JSONObject();
                postParas.put("prod_line_code",prod_line_code);
                postParas.put("station_code",station_code);
                postParas.put("serial_num",serial_num);
                postParas.put("arrive_date",CFuncUtilsSystem.GetNowDateTime(""));
                postParas.put("serial_num",serial_num);
                String url="http://127.0.0.1:9090/aisEsbApi/mes/core/recipe/MesCoreStationFlowSave";
                JSONObject jbFlowResult= cFuncUtilsRest.PostJbBackJb(url,postParas);
                if(jbFlowResult.getInteger("code")!=0){
                    errorMsg=jbFlowResult.getString("error");
                    jbResponse.put("code",-5);
                    jbResponse.put("msg",errorMsg);
                    responseParas= jbResponse.toString();
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
            }
            String station_flow_id="";
            String start_date="";
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                station_flow_id=docItemBigData.getString("station_flow_id");
                start_date=docItemBigData.getString("arrive_date");
                if(start_date==null || start_date.equals("")) start_date=CFuncUtilsSystem.GetNowDateTime("");
                iteratorBigData.close();
            }
            if(station_flow_id==null || station_flow_id.equals("")){
                errorMsg="存储与查找过站ID失败";
                jbResponse.put("code",-6);
                jbResponse.put("msg",errorMsg);
                responseParas= jbResponse.toString();
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            String main_quality_sign="OK";
            String trace_d_time= CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(trace_d_time);
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstQDocuments=new ArrayList<>();
            //存储质量数据
            for(int i=0;i<quanlity_list.size();i++){
                JSONObject jbItem=quanlity_list.getJSONObject(i);
                //String group_name=jbItem.getString("item_object");
                Integer group_order_int=i+1;
                String quality_d_sign=jbItem.getString("whole_quality_sign");
                if(!quality_d_sign.equals("OK")){
                    quality_d_sign="NG";
                    main_quality_sign="NG";
                }
                JSONArray datacollection=jbItem.getJSONArray("datacollection");
                if(datacollection!=null && datacollection.size()>0){
                    for(int j=0;j<datacollection.size();j++){
                        Map<String, Object> mapQDataItem=new HashMap<>();
                        JSONObject jbItemTag=datacollection.getJSONObject(j);
                        long tag_id=(long)0;
                        Integer tag_col_order=(j+1);
                        Integer tag_col_inner_order=1;
                        double down_limit=-1;
                        double upper_limit=-1;
                        Integer bolt_code=0;
                        Integer progm_num=0;
                        String group_name=jbItemTag.getString("data_code");
                        String tag_des=jbItemTag.getString("data_name");
                        String tag_uom=jbItemTag.getString("data_unit");
                        String tag_value=jbItemTag.getString("data_value");

                        mapQDataItem.put("item_date",item_date);
                        mapQDataItem.put("item_date_val",item_date_val);
                        mapQDataItem.put("quality_trace_id",CFuncUtilsSystem.CreateUUID(true));
                        mapQDataItem.put("station_flow_id",station_flow_id);
                        mapQDataItem.put("prod_line_code",prod_line_code);
                        mapQDataItem.put("station_code",station_code);
                        mapQDataItem.put("serial_num",serial_num);
                        mapQDataItem.put("group_order",group_order_int);
                        mapQDataItem.put("group_name",group_name);
                        mapQDataItem.put("tag_col_order",tag_col_order);
                        mapQDataItem.put("tag_id",tag_id);
                        mapQDataItem.put("tag_col_inner_order",tag_col_inner_order);
                        mapQDataItem.put("quality_for",tag_des);
                        mapQDataItem.put("tag_des",tag_des);
                        mapQDataItem.put("tag_uom",tag_uom);
                        mapQDataItem.put("theory_value","-1");
                        mapQDataItem.put("down_limit",down_limit);
                        mapQDataItem.put("upper_limit",upper_limit);
                        mapQDataItem.put("bolt_code",bolt_code);
                        mapQDataItem.put("progm_num",progm_num);
                        mapQDataItem.put("tag_value",tag_value);
                        mapQDataItem.put("quality_d_sign",quality_d_sign);
                        mapQDataItem.put("mes_quality_d_sign",quality_d_sign);
                        mapQDataItem.put("trace_d_time",trace_d_time);
                        //为了后续的上传
                        mapQDataItem.put("attribute1","1");
                        lstQDocuments.add(mapQDataItem);
                    }
                }
            }

            //1.存储质量数据
            if(lstQDocuments.size()>0){
                mongoTemplate.insert(lstQDocuments,meQualityTable);
            }
            //2.更改过站合格标志与离开时间
            Long cost_time=CFuncUtilsSystem.GetDiffMsTimes(start_date,trace_d_time);
            cost_time=cost_time/1000;
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            Update updateBigData = new Update();
            updateBigData.set("leave_date", trace_d_time);
            updateBigData.set("cost_time", cost_time);
            updateBigData.set("quality_sign", main_quality_sign);
            mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);

            //成功返回
            responseParas=jbResponse.toString();
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受设备质量数据成功");
        }
        catch (Exception ex){
            errorMsg="MES处理设备接收质量数据接口发生未知异常:"+ex.getMessage();
            jbResponse=new JSONObject();
            jbResponse.put("response_uuid", request_uuid);
            jbResponse.put("response_time", CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
            jbResponse.put("response_attr","");
            jbResponse.put("code",-99);
            jbResponse.put("msg",errorMsg);
            responseParas= jbResponse.toString();
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }


    //2.设备上报MES报警数据
    @Transactional
    @RequestMapping(value = "/MesInterfCoreAlarmData", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesInterfCoreAlarmData(@RequestBody JSONObject jsonParas) throws Exception{
        String tranResult="";
        String errorMsg="";
        String userName="-1";
        String request_uuid="";
        try{
            request_uuid=jsonParas.getString("request_uuid");

            JSONObject jsonObject=new JSONObject();
            jsonObject.put("response_uuid",request_uuid);
            jsonObject.put("response_time", CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
            jsonObject.put("response_attr","");
            jsonObject.put("code",0);
            jsonObject.put("msg","");
            jsonObject.put("data",new JSONObject());
            tranResult= jsonObject.toString();
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "设备上报MES报警数据接口发生异常"+ex.getMessage();
            JSONObject jsonObject=new JSONObject();
            jsonObject.put("request_uuid", request_uuid);
            jsonObject.put("response_time", CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
            jsonObject.put("response_attr","");
            jsonObject.put("code",-1);
            jsonObject.put("msg",errorMsg);
            jsonObject.put("data",new JSONObject());
            tranResult=jsonObject.toString();
        }
        return tranResult;
    }
}
