/**
 * CodeReaderInfoWebserviceServiceLocator.java
 * <p>
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.api.eap.project.wxjd.wcf;

public class CodeReaderInfoWebserviceServiceLocator extends org.apache.axis.client.Service implements com.api.eap.project.wxjd.wcf.CodeReaderInfoWebserviceService {

    public CodeReaderInfoWebserviceServiceLocator(String url) {
        this.codeReaderInfoWebservice_address = url;
    }


    public CodeReaderInfoWebserviceServiceLocator(org.apache.axis.EngineConfiguration config) {
        super(config);
    }

    public CodeReaderInfoWebserviceServiceLocator(java.lang.String wsdlLoc, javax.xml.namespace.QName sName) throws javax.xml.rpc.ServiceException {
        super(wsdlLoc, sName);
    }

    // Use to get a proxy class for codeReaderInfoWebservice
    private java.lang.String codeReaderInfoWebservice_address = "http://**************:8080/console/services/codeReaderInfoWebservice";

    public java.lang.String getcodeReaderInfoWebserviceAddress() {
        return codeReaderInfoWebservice_address;
    }

    // The WSDD service name defaults to the port name.
    private java.lang.String codeReaderInfoWebserviceWSDDServiceName = "codeReaderInfoWebservice";

    public java.lang.String getcodeReaderInfoWebserviceWSDDServiceName() {
        return codeReaderInfoWebserviceWSDDServiceName;
    }

    public void setcodeReaderInfoWebserviceWSDDServiceName(java.lang.String name) {
        codeReaderInfoWebserviceWSDDServiceName = name;
    }

    public com.api.eap.project.wxjd.wcf.CodeReaderInfoWebservice_PortType getcodeReaderInfoWebservice() throws javax.xml.rpc.ServiceException {
        java.net.URL endpoint;
        try {
            endpoint = new java.net.URL(codeReaderInfoWebservice_address);
        } catch (java.net.MalformedURLException e) {
            throw new javax.xml.rpc.ServiceException(e);
        }
        return getcodeReaderInfoWebservice(endpoint);
    }

    public com.api.eap.project.wxjd.wcf.CodeReaderInfoWebservice_PortType getcodeReaderInfoWebservice(java.net.URL portAddress) throws javax.xml.rpc.ServiceException {
        try {
            com.api.eap.project.wxjd.wcf.CodeReaderInfoWebserviceSoapBindingStub _stub = new com.api.eap.project.wxjd.wcf.CodeReaderInfoWebserviceSoapBindingStub(portAddress, this);
            _stub.setPortName(getcodeReaderInfoWebserviceWSDDServiceName());
            return _stub;
        } catch (org.apache.axis.AxisFault e) {
            return null;
        }
    }

    public void setcodeReaderInfoWebserviceEndpointAddress(java.lang.String address) {
        codeReaderInfoWebservice_address = address;
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        try {
            if (com.api.eap.project.wxjd.wcf.CodeReaderInfoWebservice_PortType.class.isAssignableFrom(serviceEndpointInterface)) {
                com.api.eap.project.wxjd.wcf.CodeReaderInfoWebserviceSoapBindingStub _stub = new com.api.eap.project.wxjd.wcf.CodeReaderInfoWebserviceSoapBindingStub(new java.net.URL(codeReaderInfoWebservice_address), this);
                _stub.setPortName(getcodeReaderInfoWebserviceWSDDServiceName());
                return _stub;
            }
        } catch (java.lang.Throwable t) {
            throw new javax.xml.rpc.ServiceException(t);
        }
        throw new javax.xml.rpc.ServiceException("There is no stub implementation for the interface:  " + (serviceEndpointInterface == null ? "null" : serviceEndpointInterface.getName()));
    }

    /**
     * For the given interface, get the stub implementation.
     * If this service has no port for the given interface,
     * then ServiceException is thrown.
     */
    public java.rmi.Remote getPort(javax.xml.namespace.QName portName, Class serviceEndpointInterface) throws javax.xml.rpc.ServiceException {
        if (portName == null) {
            return getPort(serviceEndpointInterface);
        }
        java.lang.String inputPortName = portName.getLocalPart();
        if ("codeReaderInfoWebservice".equals(inputPortName)) {
            return getcodeReaderInfoWebservice();
        } else {
            java.rmi.Remote _stub = getPort(serviceEndpointInterface);
            ((org.apache.axis.client.Stub) _stub).setPortName(portName);
            return _stub;
        }
    }

    public javax.xml.namespace.QName getServiceName() {
        return new javax.xml.namespace.QName("http://**************:8080/console/services/codeReaderInfoWebservice", "codeReaderInfoWebserviceService");
    }

    private java.util.HashSet ports = null;

    public java.util.Iterator getPorts() {
        if (ports == null) {
            ports = new java.util.HashSet();
            ports.add(new javax.xml.namespace.QName("http://**************:8080/console/services/codeReaderInfoWebservice", "codeReaderInfoWebservice"));
        }
        return ports.iterator();
    }

    /**
     * Set the endpoint address for the specified port name.
     */
    public void setEndpointAddress(java.lang.String portName, java.lang.String address) throws javax.xml.rpc.ServiceException {

        if ("codeReaderInfoWebservice".equals(portName)) {
            setcodeReaderInfoWebserviceEndpointAddress(address);
        } else { // Unknown Port Name
            throw new javax.xml.rpc.ServiceException(" Cannot set Endpoint Address for Unknown Port" + portName);
        }
    }

    /**
     * Set the endpoint address for the specified port name.
     */
    public void setEndpointAddress(javax.xml.namespace.QName portName, java.lang.String address) throws javax.xml.rpc.ServiceException {
        setEndpointAddress(portName.getLocalPart(), address);
    }

}
