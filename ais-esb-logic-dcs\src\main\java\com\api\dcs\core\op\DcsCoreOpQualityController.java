package com.api.dcs.core.op;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 工位逻辑
 * 1.根据工位号查询质量配置
 * 2.保存质量数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/op")
public class DcsCoreOpQualityController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.根据工位号查询质量配置
    @RequestMapping(value = "/DcsCoreOpFmodQualitySel", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreOpFmodQualitySel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/op/DcsCoreOpFmodQualitySel";
        String selectResult = "";
        String errorMsg = "";
        String station_code="";
        try{
            //1.获取参数
            station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");

            //2.查询配置的质量基础数据
            String sqlQualitySel="select " +
                    "quality_id,group_order,COALESCE(tag_id,0) tag_id," +
                    "COALESCE(tag_quality_sign_id,0) tag_quality_sign_id " +
                    "from b_dcs_fmod_quality " +
                    "where enable_flag='Y' and station_id="+station_id+" " +
                    "and quality_from='SCADA' " +
                    "order by group_order,tag_col_order,tag_col_inner_order";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlQualitySel,
                    false,request,apiRoutePath);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex) {
            errorMsg= "根据工位{"+station_code+"}查询质量配置发生异常"+ex.getMessage();
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.保存质量数据
    @RequestMapping(value = "/DcsCoreOpQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreOpQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/op/DcsCoreOpQualitySave";
        String transResult="";
        String errorMsg="";
        String station_code="";
        String task_num="";
        String serial_num="";
        String meQualityTable="b_dcs_me_station_quality";
        String meStationFlowTable="b_dcs_me_station_flow";
        try{
            String last_main_quality_sign="";
            String main_quality_sign="OK";
            String trace_d_time= CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(trace_d_time);
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);

            String station_id=jsonParas.getString("station_id");//工位ID
            station_code = jsonParas.getString("station_code");//工位号
            task_num= jsonParas.getString("task_num");//任务号
            serial_num= jsonParas.getString("serial_num");//序列号
            String station_flow_id=jsonParas.getString("station_flow_id");//过站ID
            JSONArray jsonArrayData=jsonParas.getJSONArray("quality_data");
            if(serial_num==null) serial_num="";

            //1.查询过站信息合格标志
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                last_main_quality_sign=docItemBigData.getString("quality_sign");
                iteratorBigData.close();
            }

            //2.查询当前需要保存的质量数据
            String sqlQuality="select quality_id,quality_from," +
                    "COALESCE(tag_id,0) tag_id," +
                    "group_order,group_name," +
                    "COALESCE(tag_col_order,1) tag_col_order," +
                    "COALESCE(tag_col_inner_order,1) tag_col_inner_order," +
                    "quality_for,tag_des,tag_uom," +
                    "COALESCE(theory_value,'-1') theory_value," +
                    "COALESCE(down_limit,'-1') down_limit," +
                    "COALESCE(upper_limit,'-1') upper_limit," +
                    "quality_save_flag " +
                    "from b_dcs_fmod_quality " +
                    "where station_id="+station_id+" " +
                    "order by group_order,tag_col_order,tag_col_inner_order";
            List<Map<String, Object>> itemListCrQuality=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlQuality,
                    false,request,apiRoutePath);
            //4.解析质量数据
            Map<String,String> mapQualityValue=new HashMap<>();
            Map<String,String> mapQualitySign=new HashMap<>();
            Map<String,String> mapTagValue=new HashMap<>();
            List<Map<String, Object>> lstQDocuments=new ArrayList<>();//保存质量数据集合
            if(jsonArrayData!=null && jsonArrayData.size()>0){
                for(int i=0;i<jsonArrayData.size();i++){
                    JSONObject jbItem=jsonArrayData.getJSONObject(i);
                    String quality_id=jbItem.getString("quality_id");
                    String tag_id=jbItem.getString("tag_id");
                    String tag_value=jbItem.getString("tag_value");
                    String quality_sign=jbItem.getString("quality_sign");
                    if(quality_sign.equals("OK")) quality_sign="OK";
                    else quality_sign="NG";
                    if(!mapQualityValue.containsKey(quality_id)) mapQualityValue.put(quality_id,tag_value);
                    if(!mapQualitySign.containsKey(quality_id)) mapQualitySign.put(quality_id,quality_sign);
                    if(!mapTagValue.containsKey(tag_id)) mapTagValue.put(tag_id,tag_value);
                }
            }
            //5.将要保存的信息塞入到实例
            if(itemListCrQuality!=null && itemListCrQuality.size()>0){
                for(Map<String, Object> map : itemListCrQuality){
                    String quality_id=map.get("quality_id").toString();
                    int group_order_int=Integer.parseInt(map.get("group_order").toString());
                    String group_name=map.get("group_name").toString();
                    int tag_col_order=Integer.parseInt(map.get("tag_col_order").toString());
                    long tag_id=Long.parseLong(map.get("tag_id").toString());
                    int tag_col_inner_order=Integer.parseInt(map.get("tag_col_inner_order").toString());
                    String quality_for=map.get("quality_for").toString();
                    String tag_des=map.get("tag_des").toString();
                    String tag_uom=map.get("tag_uom").toString();
                    String theory_value=map.get("theory_value").toString();
                    String down_limit_str=map.get("down_limit").toString();
                    String upper_limit_str=map.get("upper_limit").toString();
                    double down_limit=-1;
                    double upper_limit=-1;
                    try{
                        down_limit=Double.parseDouble(down_limit_str);
                    }catch (Exception ex1){}
                    try{
                        upper_limit=Double.parseDouble(upper_limit_str);
                    }catch (Exception ex2){}
                    String quality_save_flag=map.get("quality_save_flag").toString();
                    String tag_value="";
                    String quality_d_sign="NG";
                    if(mapQualityValue.containsKey(quality_id)) tag_value=mapQualityValue.get(quality_id).toString();
                    if(mapQualitySign.containsKey(quality_id)) quality_d_sign=mapQualitySign.get(quality_id).toString();
                    //只针对需要保存质量数据的进行判断
                    if(quality_save_flag.equals("Y")){
                        if(quality_d_sign.equals("NG")) main_quality_sign="NG";
                        Map<String, Object> mapQDataItem=new HashMap<>();
                        mapQDataItem.put("item_date",item_date);
                        mapQDataItem.put("item_date_val",item_date_val);
                        mapQDataItem.put("quality_trace_id",CFuncUtilsSystem.CreateUUID(true));
                        mapQDataItem.put("station_flow_id",station_flow_id);
                        mapQDataItem.put("station_code",station_code);
                        mapQDataItem.put("task_num",task_num);
                        mapQDataItem.put("serial_num",serial_num);
                        mapQDataItem.put("group_order",group_order_int);
                        mapQDataItem.put("group_name",group_name);
                        mapQDataItem.put("tag_col_order",tag_col_order);
                        mapQDataItem.put("tag_id",tag_id);
                        mapQDataItem.put("tag_col_inner_order",tag_col_inner_order);
                        mapQDataItem.put("quality_for",quality_for);
                        mapQDataItem.put("tag_des",tag_des);
                        mapQDataItem.put("tag_uom",tag_uom);
                        mapQDataItem.put("theory_value",theory_value);
                        mapQDataItem.put("down_limit",down_limit);
                        mapQDataItem.put("upper_limit",upper_limit);
                        mapQDataItem.put("tag_value",tag_value);
                        mapQDataItem.put("quality_d_sign",quality_d_sign);
                        mapQDataItem.put("trace_d_time",trace_d_time);
                        lstQDocuments.add(mapQDataItem);
                    }
                }
            }
            //6.保存质量数据
            if(lstQDocuments.size()>0){
                mongoTemplate.insert(lstQDocuments,meQualityTable);
            }
            //8.更新工位合格标志
            if(last_main_quality_sign.equals("")){
                last_main_quality_sign=main_quality_sign;
            }
            else{
                if(last_main_quality_sign.equals("OK") && main_quality_sign.equals("NG")){
                    last_main_quality_sign="NG";
                }
            }
            if(lstQDocuments.size()>0){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
                Update updateBigData = new Update();
                updateBigData.set("quality_sign", last_main_quality_sign);
                mongoTemplate.updateMulti(queryBigData, updateBigData, meStationFlowTable);
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "保存当前工位{"+station_code+"},当前工件{"+task_num+"}质量数据异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
