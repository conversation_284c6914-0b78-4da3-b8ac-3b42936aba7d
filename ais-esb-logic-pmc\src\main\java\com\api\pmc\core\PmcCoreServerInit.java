package com.api.pmc.core;

import com.api.common.db.CFuncDbSqlMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * PMC公共定义初始化
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@Service
@Slf4j
public class PmcCoreServerInit {
    @Autowired
    private CFuncDbSqlMapper cFuncDbSqlMapper;

    //初始化加载参数
    public void ServerInit(){
        String errorMsg="";
        try{
            //1、查询ESB服务信息
            String server_host_1="";
            String server_host_2="";
            String server_host_3="";
            String server_host_4="";
            String serverIp="";
            String sqlEsbServer="select server_id,server_code," +
                    "COALESCE(server_host_1,'') server_host_1," +
                    "COALESCE(server_host_1,'') server_host_2," +
                    "COALESCE(server_host_3,'') server_host_3," +
                    "COALESCE(server_host_4,'') server_host_4 " +
                    "from sys_core_server " +
                    "where enable_flag = 'Y' "+
                    "and esb_server_flag = 'Y' ";
            List<Map<String,Object>> serverList=cFuncDbSqlMapper.ExecSelectSql(sqlEsbServer);
            if(serverList==null || serverList.size()<=0){
                errorMsg="PMC系统未维护ESB系统基础数据，请及时排查";
                log.error(errorMsg);
            }
            server_host_1=serverList.get(0).get("server_host_1").toString();
            server_host_2=serverList.get(0).get("server_host_2").toString();
            server_host_3=serverList.get(0).get("server_host_3").toString();
            server_host_4=serverList.get(0).get("server_host_4").toString();
            if(!server_host_1.isEmpty()){
                serverIp=server_host_1;
            } else if(!server_host_2.isEmpty()){
                serverIp=server_host_2;
            } else if(!server_host_3.isEmpty()){
                serverIp=server_host_3;
            } else if(!server_host_4.isEmpty()){
                serverIp=server_host_4;
            }
            PmcCoreServer.EsbIp=serverIp;
            PmcCoreServer.EsbUrl="http://"+serverIp+":"+PmcCoreServer.EsbPort;

            //2、查询接口信息(只获取外部系统)
            String esb_interf_code="";//ESB接口名称
            String data_to_sys="";//接口数据到那个系统去
            String esb_prod_intef_url="";//接口PROD正式地址
            String paras_list="";//传参值
            String sqlEsbIntef="select a.esb_interf_code,a.esb_interf_des," +
                    "a.data_from_sys,a.data_to_sys,a.esb_interf_way," +
                    "COALESCE(a.esb_dev_intef_url,'') esb_dev_intef_url," +
                    "COALESCE(a.esb_prod_intef_url,'') esb_prod_intef_url," +
                    "COALESCE(b.token,'') token," +
                    "COALESCE(b.remarks,'') remarks," +
                    "COALESCE(b.cell_id,0) cell_id," +
                    "COALESCE(b.paras_list,'') paras_list " +
                    "from sys_core_esb_interf a left join sys_core_esb_interf_d b " +
                    "on a.esb_interf_id = b.esb_interf_id " +
                    "where a.data_from_sys = 'AIS' " +
                    "and a.data_to_sys <> 'AIS' " +
                    "and a.esb_interf_way = 'WebApi' " +
                    "and a.enable_flag = 'Y' ";
            List<Map<String,Object>> itemList=cFuncDbSqlMapper.ExecSelectSql(sqlEsbIntef);
            if(itemList==null || itemList.size()<=0){
                errorMsg="PMC系统未维护接口基础数据，请及时排查";
                log.error(errorMsg);
                return;
            }
            if(itemList != null && itemList.size() > 0){
                String value="";//值：ESB接口名称
                String url="";//路由
                for (Map mapItem : itemList) {
                    esb_interf_code=mapItem.get("esb_interf_code").toString();
                    data_to_sys=mapItem.get("data_to_sys").toString();
                    esb_prod_intef_url=mapItem.get("esb_prod_intef_url").toString();
                    paras_list=mapItem.get("paras_list").toString();
                    if(data_to_sys.equals("AIS")){
                        url="http://"+serverIp+":9090"+esb_prod_intef_url;
                    } else {
                        url=esb_prod_intef_url;
                    }
                    value=paras_list +"&&"+ url;
                    PmcCoreServer.InterfBaseInfoList.put(esb_interf_code,value);
                }
            }

            log.info("PMC初始化加载接口参数成功");
        }
        catch (Exception ex){
            errorMsg="PMC初始化加载接口参数异常:"+ex.getMessage();
            log.error(errorMsg);
        }
    }

}