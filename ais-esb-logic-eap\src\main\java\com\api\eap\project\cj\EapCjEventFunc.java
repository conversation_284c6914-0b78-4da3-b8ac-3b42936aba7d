package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 定颖放板机定制生产计划处理逻辑
 * 1.放板机Panel校验
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/cj/event")
public class EapCjEventFunc {
    @Resource
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Resource
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private OpCommonFunc opCommonFunc;
    @Resource
    private EapCjMasterSendFlowFunc eapCjMasterSendFlowFunc;
    @Resource
    private EapCjInterfCommon eapCjInterfCommon;
    @Resource
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    /**
     * 查询任务
     * 若没有任务则上报消息 WaitVerify、 CarrierIDReport、WaitData，然后查询任务
     * 上报WaitProc、Process
     * 保存板件信息
     * 每片上报 ReceiveReport
     * 每片任务上报 JobCountReport
     *
     * @param jsonParas
     * @param request
     * @return
     */
    public void InOrOutPanelEvent(JSONObject jsonParas) {
        //查询任务
        try {
            //获取任务
            Document task = GetTask(jsonParas);
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select " + "COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            Map<String, Object> stringObjectMap = itemListStation.stream().findFirst().get();
            String station_attr = stringObjectMap.get("station_attr").toString();
            String port_code = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr, "Plc", "PlcStatus", "PlcWorkPortIndex");
            String carryStatus = "WaitVerify";
            String pallet_num = jsonParas.containsKey("pallet_num") ? jsonParas.getString("pallet_num") : "123456";
            JSONArray lot_list = jsonParas.containsKey("lot_list") ? jsonParas.getJSONArray("lot_list") : new JSONArray();
            //防止多线程双重判断
            if (task == null) {
                synchronized (this) {
                    if (task == null) {
                        if (CollectionUtils.isEmpty(lot_list)) {
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("lot_id", "");
                            jsonObject.put("lot_count", 0);
                            lot_list.add(jsonObject);
                        }
                        //若没有任务则上报消息 WaitVerify、 CarrierIDReport、WaitData，然后查询任务
                        eapCjMasterSendFlowFunc.CarrierStatusReport(station_code, pallet_num, carryStatus, lot_list, "0", station_attr, port_code);
                        EapDyCarrierIDReport(jsonParas);
                        carryStatus = "WaitData";
                        eapCjMasterSendFlowFunc.CarrierStatusReport(station_code, pallet_num, carryStatus, lot_list, "0", station_attr, port_code);
                        task = GetTask(jsonParas);
                        //根据现场要求一直等待任务的下发
                        while (task == null) {
                            task = GetTask(jsonParas);
                        }
                        carryStatus = "WaitStart";
                        eapCjMasterSendFlowFunc.CarrierStatusReport(station_code, pallet_num, carryStatus, lot_list, "0", station_attr, port_code);
                        task = GetTask(jsonParas);
                        pallet_num = task.getString("pallet_num");
                        //等待变更任务状态
                        pallet_num = waitUpdateTaskStatus(jsonParas, task, station_id, station_code, station_attr, port_code, pallet_num);
                    }
                }
            }
            //保存板件信息
            JSONObject jsonObject = EapCoreMainPlanPanelCheckAndSave(jsonParas);
            task = GetTask(jsonParas);
            jsonParas.putAll(jsonObject);
            if (jsonParas.containsKey("type") && !"out".equals(jsonParas.getString("type"))) {
                //每片上报 ReceiveReport 进板
                ReceiveReport(jsonParas);
            } else {
                //每片上报 SendOutReport 出版
                SendOutReport(jsonParas);
                Integer out_finish_count = task.getInteger("out_finish_count");
                //WIPTrackingReport
                if (out_finish_count == task.getInteger("plan_lot_count")) {
                    WIPTrackingReport(task);
                    //ProcessEnd
                    carryStatus = "ProcessEnd";
                    JSONObject lot_info = new JSONObject();
                    lot_info.put("lot_id", task.getString("lot_num"));
                    lot_info.put("lot_count", JSONArray.parseArray(task.getString("pnl_infos")).size());
                    lot_list = new JSONArray();
                    lot_list.add(lot_info);
                    eapCjMasterSendFlowFunc.CarrierStatusReport(station_code, pallet_num, carryStatus, lot_list, "0", station_attr, port_code);
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(task.getString("group_lot_num")));
                    Update updateBigData = new Update();
                    updateBigData.set("group_lot_status", "FINISH");
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                    mongoTemplate.updateMulti(queryBigData, updateBigData, "a_eap_aps_plan");
                }
            }
            //每片任务上报 JobCountReport
            JobCountReport(jsonParas);
        } catch (Exception ex) {
            log.error("判断是否存在放板任务异常" + ex.getMessage());
        }

    }

    private String waitUpdateTaskStatus(JSONObject jsonParas, Document task, String station_id, String station_code, String station_attr, String port_code, String pallet_num) throws Exception {
        String carryStatus;
        JSONArray lot_list;
        String currentCarryStatus = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr, "Eap", "EapStatus", "CarryStatus" + Integer.parseInt(port_code));
        while ("WaitStart".equals(currentCarryStatus)) {
            //每次查询前等一会儿，可能存在EAP下发任务不及时
            //最終还是没查询到任务
            task = GetTask(jsonParas);
            if ("PLAN".equals(task.getString("group_lot_status"))) {
                JSONObject lot_info = new JSONObject();
                pallet_num = task.getString("pallet_num");
                lot_info.put("lot_id", task.getString("lot_num"));
                lot_info.put("lot_count", JSONArray.parseArray(task.getString("pnl_infos")).size());
                lot_list = new JSONArray();
                lot_list.add(lot_info);
                carryStatus = "WaitProc";
                eapCjMasterSendFlowFunc.CarrierStatusReport(station_code, pallet_num, carryStatus, lot_list, "0", station_attr, port_code);
                carryStatus = "Process";
                eapCjMasterSendFlowFunc.CarrierStatusReport(station_code, pallet_num, carryStatus, lot_list, "0", station_attr, port_code);
                break;
            }
        }
        Query queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
        queryBigData.addCriteria(Criteria.where("group_lot_num").is(task.getString("group_lot_num")));
        queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
        Update updateBigData = new Update();
        updateBigData.set("group_lot_status", "WORK");
        mongoTemplate.updateMulti(queryBigData, updateBigData, "a_eap_aps_plan");
        return pallet_num;
    }

    public void WIPTrackingReport(Document jsonParas) throws Exception {
        String apsPlanTable = "a_eap_aps_plan";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        MongoCursor<Document> iteratorBigData = null;
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String lot_num = jsonParas.getString("lot_num");
            String offline_flag = jsonParas.getString("offline_flag");//在线、离线模式
            if (offline_flag == null) offline_flag = "N";
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String prod_mode = jsonParas.getString("prod_mode");
            String nw_value = jsonParas.getString("nw_value");//内外层代码
            String short_count = jsonParas.getString("short_count");//少片数量
            if (short_count == null || short_count.equals("")) short_count = "0";
            String noread_count = jsonParas.getString("noread_count");//NoRead数量
            if (noread_count == null || noread_count.equals("")) noread_count = "0";
            String local_flag = "Y";
            if (sys_model.equals("1")) local_flag = "N";

            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OnOffLineTag = "";
            String OnOffLine = "";//在线离线
            if (aisMonitorModel.equals("AIS-PC")) {
                OnOffLineTag = station_attr + "Plc/PlcConfig/OnOffLine";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OnOffLineTag = station_attr + "Plc_" + station_code + "/PlcConfig/OnOffLine";
            } else {
                throw new IllegalArgumentException("AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER");
            }
            JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, OnOffLineTag);
            if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                for (int i = 0; i < jsonArrayTag.size(); i++) {
                    JSONObject jbItem = jsonArrayTag.getJSONObject(i);
                    String tag_key = jbItem.getString("tag_key");
                    String tag_value = jbItem.getString("tag_value");
                    if (tag_key.equals(OnOffLineTag)) OnOffLine = tag_value;
                }
            }
            if (!OnOffLine.equals("1")) offline_flag = "Y";
            else offline_flag = "N";

            //新增一批多车多级
            String out_code = jsonParas.getString("out_code");
            if (out_code == null || out_code.equals("")) out_code = "-1";

            //根据母批查询信息
            Integer sum_plan_count = 0;
            Integer sum_finish_count = 0;
            Integer sum_finish_ok_count = 0;
            String material_code_P001 = "";
            String lot_version_P002 = "";
            Integer first_plan_count_P004 = 0;
            Integer lot_finish_count = 0;
            String lot_short_num = "";
            String attribute1_P005 = "";
            String attribute2_P006 = "";
            String user_name = "";
            String dept_id = "";
            String shift_id = "";
            String task_start_time = CFuncUtilsSystem.GetNowDateTime("");
            String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
            String item_info = "";
            JSONArray item_attr_list = null;
            String splitseq = "0";
            String lastSplit = "1";
            if (prod_mode == null || prod_mode.equals("")) prod_mode = "0";
            JSONObject attr_else = null;

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                String lot_num2 = docItemBigData.getString("lot_num");
                sum_plan_count += plan_lot_count;
                sum_finish_count += finish_count;
                sum_finish_ok_count += finish_ok_count;
                if (lot_num2.equals(lot_num)) {
                    material_code_P001 = docItemBigData.getString("material_code");
                    lot_version_P002 = docItemBigData.getString("lot_level");
                    first_plan_count_P004 = plan_lot_count;
                    lot_finish_count = finish_ok_count;
                    lot_short_num = docItemBigData.getString("lot_short_num");
                    item_info = docItemBigData.getString("item_info");
                    attribute1_P005 = docItemBigData.getString("attribute1");
                    attribute2_P006 = docItemBigData.getString("attribute2");
                    if (docItemBigData.containsKey("attribute3")) {
                        String attribute_else = docItemBigData.getString("attribute3");
                        if (attribute_else != null && !attribute_else.equals("")) {
                            attr_else = JSONObject.parseObject(attribute_else);
                        }
                    }
                    task_start_time = docItemBigData.getString("task_start_time");
                    task_end_time = docItemBigData.getString("task_end_time");
                    if (task_start_time == null || task_start_time.equals("")) {
                        task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    }
                    if (task_end_time == null || task_end_time.equals("")) {
                        task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    }
                }
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前用户
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                dept_id = docItemBigData.getString("dept_id");
                shift_id = docItemBigData.getString("shift_id");
                iteratorBigData.close();
            }

            JSONObject jsonObject = eapCjInterfCommon.GetParamsMapping();
            //3.处理Item_Info
            if (item_info != null && !item_info.equals("")) {
                for (int j = 1; j <= 200; j++) {
                    String SSource = "S" + String.format("%03d", j);
                    String TTarget = "T" + String.format("%03d", j + 1);
                    if (jsonObject != null && jsonObject.containsKey(TTarget)) {
                        SSource = jsonObject.getString(TTarget);
                    }
                    item_info = item_info.replace(SSource, TTarget);
                }
                item_attr_list = JSONArray.parseArray(item_info);
                if (nw_value == null || nw_value.equals("")) nw_value = "0";
                JSONObject nw_obj = new JSONObject();
                nw_obj.put("item_id", "T001");
                nw_obj.put("item_value", nw_value);
                item_attr_list.add(nw_obj);
            }

            if (!eapCjInterfCommon.CheckDyVersion(3)) {
                if (item_attr_list == null) item_attr_list = new JSONArray();
                for (int k = item_attr_list.size() - 1; k >= 0; k--) {
                    JSONObject jbItemAttr = item_attr_list.getJSONObject(k);
                    String item_id = jbItemAttr.getString("item_id");
                    if (item_id.equals("T030") || item_id.equals("T031")) {
                        item_attr_list.remove(k);
                    }
                }
                JSONObject T030 = new JSONObject();
                T030.put("item_id", "T030");
                T030.put("item_value", noread_count);
                item_attr_list.add(T030);
                JSONObject T031 = new JSONObject();
                T031.put("item_id", "T031");
                T031.put("item_value", short_count);
                item_attr_list.add(T031);
            }

            //用于DataUpLoad参数
            Integer lot_left_count = 0;
            JSONArray carr_infos = new JSONArray();
            JSONObject jbCarr = new JSONObject();
            JSONObject jbLot = new JSONObject();
            lot_left_count = first_plan_count_P004;
            jbLot.put("lot_id", lot_num);
            jbLot.put("lot_count", lot_left_count);
            jbCarr.put("lot", jbLot);
            JSONObject pnl_infos = new JSONObject();
            JSONArray jaSlot = new JSONArray();
            //查询明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(100).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String panel_barcode = docItemBigData.getString("panel_barcode");
                Integer panel_index = docItemBigData.getInteger("panel_index");
                JSONObject jbDetail = new JSONObject();
                jbDetail.put("slot_no", String.format("%03d", panel_index));
                jbDetail.put("panel_id", panel_barcode);
                jaSlot.add(jbDetail);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            pnl_infos.put("slot", jaSlot);
            jbCarr.put("pnl_infos", pnl_infos);
            carr_infos.add(jbCarr);

            //判断是否发送WIP数据
            Boolean isSendWip = true;
            if (OneCarMultyLotFlag.equals("2")) {
                Integer out_code_int = Integer.parseInt(out_code);
                if (out_code_int > 0 && out_code_int < 4) {
                    if (sum_plan_count > sum_finish_ok_count) isSendWip = false;
                }
            }
            //上报WIP【异步】
            if (isSendWip) {
                eapCjMasterSendFlowFunc.WIPTrackingReport(Long.parseLong(station_id), station_code, user_name, dept_id, shift_id, task_start_time, task_end_time, lot_num, sum_plan_count, first_plan_count_P004, lot_finish_count, material_code_P001, lot_short_num, lot_version_P002, prod_mode, attribute1_P005, attribute2_P006, item_attr_list, offline_flag, local_flag, attr_else);
            }
        } catch (Exception ex) {
            log.error("EAP子批次完板上报异常:" + ex.getMessage());
        } finally {
            if (iteratorBigData != null) {
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
        }
    }


    public void JobCountReport(JSONObject jsonParas) throws Exception {
        String meStationFlowTable = "a_eap_me_station_flow";
        MongoCursor<Document> iteratorBigData = null;
        Query query = new Query();
        query.addCriteria(Criteria.where("group_lot_num").is(jsonParas.getString("group_lot_num")));
        List<Document> documents = mongoTemplate.find(query, Document.class, meStationFlowTable);
        Map<String, List<Document>> groupByInOrOut = documents.stream().collect(Collectors.groupingBy(g -> g.getString("type")));
        int inCount = groupByInOrOut.containsKey("in") ? groupByInOrOut.get("in").size() : 0;
        int out = groupByInOrOut.containsKey("out") ? groupByInOrOut.get("out").size() : 0;
        String station_code = jsonParas.getString("station_code");
        String station_attr = jsonParas.getString("station_attr");
        eapCjMasterSendFlowFunc.JobCountReport(station_code, station_attr, inCount - out, jsonParas.getString("port_code"));
    }

    public void ReceiveReport(JSONObject jsonParas) throws Exception {
        String lot_num = jsonParas.getString("lot_num");
        String panel_barcode = jsonParas.getString("panel_barcode");
        String station_code = jsonParas.getString("station_code");
        String station_attr = jsonParas.getString("station_attr");
        String panel_index = jsonParas.getString("panel_index");
        eapCjMasterSendFlowFunc.ReceiveReport(station_code, station_attr, lot_num, panel_barcode, panel_index);
    }

    public void SendOutReport(JSONObject jsonParas) throws Exception {
        String lot_num = jsonParas.getString("lot_num");
        String panel_barcode = jsonParas.getString("panel_barcode");
        String station_code = jsonParas.getString("station_code");
        String station_attr = jsonParas.getString("station_attr");
        String panel_index = jsonParas.getString("panel_index");
        //上报
        eapCjMasterSendFlowFunc.SendOutReport(station_code, station_attr, lot_num, panel_barcode, panel_index);
    }

    public JSONObject EapCoreMainPlanPanelCheckAndSave(JSONObject jsonParas) {
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        String mePanelQueueTable = "a_eap_me_main_panel_queue";
        //获取板件队列，以时间升序
        Long station_id = jsonParas.getLong("station_id");
        String in_or_out = jsonParas.getString("type");
        String status = jsonParas.getString("status");
        Query queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
        queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
        queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
        MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
        Assert.isTrue(iteratorBigData.hasNext(), "查询不到当前可用订单任务");
        Document docItemBigData = iteratorBigData.next();
        iteratorBigData.close();
        String lot_num = docItemBigData.getString("lot_num");
        String plan_id = docItemBigData.getString("plan_id");
        String task_from = docItemBigData.getString("task_from");
        String group_lot_num = docItemBigData.getString("group_lot_num");
        String lot_short_num = docItemBigData.getString("lot_short_num");
        Integer lot_index = docItemBigData.getInteger("lot_index");
        String port_code = docItemBigData.getString("port_code");
        //任务计划数量
        Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
        //变更任务完成数量
        Integer finish_count = docItemBigData.getInteger(in_or_out + "_finish_count");
        Integer finish_ok_count = docItemBigData.getInteger(in_or_out + "_finish_ok_count");
        Integer panel_index = 0;
        //查询板件队列
        queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
        queryBigData.addCriteria(Criteria.where(in_or_out + "_use_flag").is("N"));
        queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
        List<JSONObject> panelQueue = mongoTemplate.find(queryBigData, JSONObject.class, mePanelQueueTable);
        Assert.notEmpty(panelQueue, "当前任务没有板腱信息");
        JSONObject jsonObject = panelQueue.stream().findFirst().get();
        String panel_barcode = jsonObject.getString("panel_barcode");
        //查询板件履历
        Query countQuery = new Query();
        countQuery.addCriteria(Criteria.where("station_id").is(station_id));
        countQuery.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
        countQuery.addCriteria(Criteria.where("plan_id").in(plan_id));
        countQuery.addCriteria(Criteria.where("in_or_out").in(in_or_out));
        Long stationFlowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(countQuery.getQueryObject());
        panel_index = stationFlowCount.intValue() + 1;
        queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
        queryBigData.addCriteria(Criteria.where(in_or_out + "_use_flag").is("N"));
        queryBigData.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
        Update update = new Update();
        update.set(in_or_out + "_use_flag", "Y");
        update.set("panel_status", status);
        update.set(in_or_out + "_use_date", CFuncUtilsSystem.GetMongoDataValue(CFuncUtilsSystem.GetMongoISODate("")));
        //将板件队列中的标记进行变更
        mongoTemplate.updateMulti(queryBigData, update, mePanelQueueTable);
        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
        String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
        Map<String, Object> mapBigDataRow = new HashMap<>();
        mapBigDataRow.put("item_date", item_date);
        mapBigDataRow.put("item_date_val", item_date_val);
        mapBigDataRow.put("station_flow_id", station_flow_id);
        mapBigDataRow.put("station_id", station_id);
        mapBigDataRow.put("plan_id", plan_id);
        mapBigDataRow.put("task_from", task_from);
        mapBigDataRow.put("group_lot_num", group_lot_num);
        mapBigDataRow.put("lot_num", lot_num);
        mapBigDataRow.put("lot_short_num", lot_short_num);
        mapBigDataRow.put("lot_index", lot_index);
        mapBigDataRow.put("port_code", port_code);
        mapBigDataRow.put("panel_barcode", panel_barcode);
        mapBigDataRow.put("panel_index", panel_index);
        mapBigDataRow.put("panel_status", "OK");
        mapBigDataRow.put("in_or_out", in_or_out);
        mapBigDataRow.put("dummy_flag", "N");
        mapBigDataRow.put("panel_flag", "OK");
        mapBigDataRow.put("offline_flag", "N");
        mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
        //更新任务数量
        finish_count += 1;
        finish_ok_count += 1;
        queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
        queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
        Update apsPlanUpdate = new Update();
        apsPlanUpdate.set(in_or_out + "_finish_ok_count", finish_count);
        apsPlanUpdate.set(in_or_out + "_finish_count", finish_count);
        if ("in".equals(in_or_out) && docItemBigData.getInteger(in_or_out + "_finish_count") == 0) {
            apsPlanUpdate.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
        }
        //将板件队列中的标记进行变更
        mongoTemplate.updateMulti(queryBigData, apsPlanUpdate, apsPlanTable);
        return jsonObject;
    }

    public void EapDyCarrierIDReport(JSONObject jsonParas) throws Exception {
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String mePalletQueueTable = "a_eap_me_pallet_queue";
        try {
            String result = "OK";
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String production_mode = jsonParas.getString("production_mode");//制程分类
            String ccdNo = jsonParas.getString("ccd_no");
            if (production_mode == null || production_mode.equals("")) production_mode = "Default";
            String pre_pallet_scan = jsonParas.getString("pre_pallet_scan");//是否提前扫描载具
            if (pre_pallet_scan == null || pre_pallet_scan.equals("")) pre_pallet_scan = "N";
            String read_type = jsonParas.getString("read_type");
            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("", sqlStation, false, null, "");
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String user_name = "";
            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //查询是否为提前扫描
            String ZanCunCcdFlag = "0";
            if (station_attr.equals("Load")) {
                ZanCunCcdFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr, "Ais", "AisConfig", "ZanCunCcdFlag");
                Assert.isTrue(!(ZanCunCcdFlag == null || ZanCunCcdFlag.equals("")), "读取点位{ZanCunCcdFlag}失败");
            }
            //3.若是来自暂存,但是非扫描工位
            if (ZanCunCcdFlag.equals("1") && !pre_pallet_scan.equals("Y")) {
                //1.查找载具是否最后一批状态
                Integer rtn_fuder = -1;
                String lot_num = "";
                String i_id = "";
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("pallet_num").is(pallet_num));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(mePalletQueueTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    i_id = docItemBigData.getObjectId("_id").toString();
                    lot_num = docItemBigData.getString("lot_num");
                    rtn_fuder = docItemBigData.getInteger("rtn_fuder");//1最后一车标识,0不是最后一车标识
                    iteratorBigData.close();
                }
                if (rtn_fuder >= 0 && !lot_num.equals("")) {
                    queryBigData = new Query();
                    String[] group_lot_status_list2 = new String[]{"WAIT", "PLAN", "WORK"};
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list2));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    Update updateBigData2 = new Update();
                    updateBigData2.set("pdb_rule", rtn_fuder);
                    mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
                }
                //删除数据
                if (!i_id.equals("")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("_id").lte(new ObjectId(i_id)));
                    mongoTemplate.remove(queryBigData, mePalletQueueTable);
                }
                return;
            }
            //4.提前扫描，需要判断是否最后一车
            if (ZanCunCcdFlag.equals("1") && pre_pallet_scan.equals("Y")) {
                String old_lot_num = "";
                String new_lot_num = "";
                String first_scan_flag = "N";
                Integer old_rtn_fuder = 0;
                String read_type2 = "B";
                //4.1 读取当前批次号
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(mePalletQueueTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    old_lot_num = docItemBigData.getString("lot_num");
                    old_rtn_fuder = docItemBigData.getInteger("rtn_fuder");//1最后一车标识,0不是最后一车标识
                    iteratorBigData.close();
                }
                if (old_rtn_fuder == 1) {
                    first_scan_flag = "Y";
                    read_type2 = "S";
                } else {
                    if (old_lot_num.equals("")) {
                        first_scan_flag = "Y";
                        read_type2 = "S";
                    }
                }
                JSONObject response_body = eapCjMasterSendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, station_attr, 0, production_mode, "N", null, first_scan_flag, read_type2);
                if (response_body == null) {
                    result = "NG";
                } else {
                    Integer interf_rtn_fuder = 0;
                    //先获取参数中的rtn_fuder值
                    if (response_body.containsKey("rtn_fuder")) {
                        try {
                            interf_rtn_fuder = response_body.getInteger("rtn_fuder");
                            if (interf_rtn_fuder == null) interf_rtn_fuder = 0;
                        } catch (Exception ex) {
                        }
                    }
                    //获取到lot_id信息
                    if (response_body.containsKey("lot_infos")) {
                        JSONObject lot_infos = response_body.getJSONObject("lot_infos");
                        if (lot_infos != null) {
                            if (lot_infos.containsKey("lot")) {
                                JSONArray lot_list = lot_infos.getJSONArray("lot");
                                if (lot_list != null && lot_list.size() > 0) {
                                    JSONObject first_lot_obj = lot_list.getJSONObject(0);
                                    if (first_lot_obj != null && first_lot_obj.containsKey("lot_id")) {
                                        new_lot_num = first_lot_obj.getString("lot_id");
                                    }
                                }
                            }
                        }
                    }
                    if (!first_scan_flag.equals("Y")) {
                        if (!old_lot_num.equals(new_lot_num)) {
                            result = "NG";
                            //记录到CIM消息
                            String cimMessage = "当前载具{" + pallet_num + "}对应工单{" + new_lot_num + "}与当前作业工单{" + old_lot_num + "}不一致,不允许上机";
                            opCommonFunc.SaveCimMessage(station_id_long, "1", "-1", "EAP", cimMessage,5);
                        }
                    }
                    if (result.equals("OK")) {
                        //增加载具队列数据
                        Map<String, Object> mapPalletQueueRow = new HashMap<>();
                        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                        mapPalletQueueRow.put("item_date", item_date);
                        mapPalletQueueRow.put("item_date_val", item_date_val);
                        mapPalletQueueRow.put("pallet_queue_id", CFuncUtilsSystem.CreateUUID(true));
                        mapPalletQueueRow.put("station_id", station_id_long);
                        mapPalletQueueRow.put("lot_num", new_lot_num);
                        mapPalletQueueRow.put("pallet_num", pallet_num);
                        mapPalletQueueRow.put("rtn_fuder", interf_rtn_fuder);
                        mongoTemplate.insert(mapPalletQueueRow, mePalletQueueTable);
                    }
                }
                //CCDDataReport:[接口]CCD读码上报
                eapCjMasterSendFlowFunc.CCDDataReport(station_code, pallet_num, ccdNo);
                return;
            }
            //其他
            JSONObject response_body2 = eapCjMasterSendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, station_attr, 0, production_mode, "N", null, "Y", read_type);
            if (response_body2 == null) result = "NG";
            //1.CCDDataReport:[接口]CCD读码上报
            eapCjMasterSendFlowFunc.CCDDataReport(station_code, pallet_num, ccdNo);
        } catch (Exception ex) {
            log.error("载具扫描上报验证异常:" + ex.getMessage());
        }
    }

    private Document GetTask(JSONObject jsonParas) throws Exception {
        String apsPlanTable = "a_eap_aps_plan";
        String station_id = jsonParas.getString("station_id");
        long station_id_long = Long.parseLong(station_id);
        Query queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
        String[] group_lot_status = null;
        //如果是进板任务，那么只查Plan状态的，若为出板则查询Work
        if (jsonParas.containsKey("type") && "out".equals(jsonParas.getString("type"))) {
            group_lot_status = new String[]{"WORK"};
        } else {
            group_lot_status = new String[]{"WORK", "PLAN"};
        }
        queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
        queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
        List<Document> documents = mongoTemplate.find(queryBigData, Document.class, apsPlanTable);
        List<Map<String, Object>> collect = documents.stream().map(m -> {
            Map<String, Object> resultMap = new HashMap<String, Object>();
            resultMap.putAll(m);
            return resultMap;
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(collect)) {
            return documents.stream().findFirst().get();
        } else {
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            List<Document> waitDocuments = mongoTemplate.find(queryBigData, Document.class, apsPlanTable);
            if (!CollectionUtils.isEmpty(waitDocuments)) {
                return waitDocuments.stream().findFirst().get();
            }
        }
        return null;
    }

    @EventListener(CjInOrOutPnlEvent.class)
    public void onApplicationEvent(CjInOrOutPnlEvent cjInOrOutPnlEvent) {
        JSONObject params = cjInOrOutPnlEvent.getParams();
        try{
            InOrOutPanelEvent(params);
        }catch (Exception e) {
            log.error("进出板事件异常报错：", e.getMessage());
        }
    }
}
