package com.api.dcs.core.interf.cut;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import com.api.dcs.core.interf.print.DcsCorePrintSendSubFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * DCS切割发送流程功能函数
 * 1.下发切割任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@Slf4j
public class DcsCoreCutSendFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private DcsCoreCutSendSubFunc dcsCoreCutSendSubFunc;
    @Autowired
    private DcsInterfCommon dcsInterfCommon;

    //1.下发切割任务
    public void CutSendTask(String station_code, String mo_id, String task_num, String model_type,
                              Double m_length, Double m_width, Double m_height, String m_texture,
                              String cut_texture, Double npa_startx,Double npa_starty,String cut_way,
                              Double cut_speed,Double cut_ampere,Double cut_offset,String nc_name,
                              String nc_content,Integer cut_plan_minutes) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= dcsCoreCutSendSubFunc.CutSendTask(station_code,task_num,model_type,
                m_length,m_width,m_height,m_texture,cut_texture,npa_startx,npa_starty,
                cut_way,cut_speed,cut_ampere,cut_offset,nc_name,nc_content,cut_plan_minutes);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        Integer code=jbResult.getInteger("code");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
            //记录事件
            dcsInterfCommon.InsertApsTaskEvent(mo_id,station_code,esbInterfCode,"下发切割任务",
                    requestParas,responseParas,code,message,successFlag,startDate,endDate,"");
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }
}
