package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 定颖EAP接受事件信息定义接口
 * 1.CimMessage:接受EAP下发弹窗消息
 * 2.UserLoginRequest:被动接受EAP下发指令登入登出
 * 3.HeartBeatCheckDownLoad:上游设备检测下游设备是否在线
 * 4.BcOffLineLoginInDownLoad:上游通知下游离线登入
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/cj/master/receive")
public class EapCjMasterReceiveEapInfoController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapCjMasterRecvEventFunc eapCjMasterRecvEventFunc;

    @Autowired
    private EapCjMasterRecvFlowFunc eapCjMasterRecvFlowFunc;

    //接受EAP下发弹窗消息
    @RequestMapping(value = "/CimMessage",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvEventCimMessage(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/CimMessage";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvEventFunc.CimMessage(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //被动接受EAP下发指令登入登出
    @RequestMapping(value = "/UserLoginRequest",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvEventUserLoginRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/UserLoginRequest";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvEventFunc.UserLoginRequest(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //上游设备检测下游设备是否在线
    @RequestMapping(value = "/HeartBeatCheckDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvEventHeartBeatCheckDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/HeartBeatCheckDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvEventFunc.HeartBeatCheckDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //上游通知下游离线登入
    @RequestMapping(value = "/BcOffLineLoginInDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvEventBcOffLineLoginInDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/BcOffLineLoginInDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvEventFunc.BcOffLineLoginInDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //接受EAP询问是否在线
    @RequestMapping(value = "/AreYouThere",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvEventAreYouThere(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/AreYouThere";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvEventFunc.AreYouThere(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //EAP远程修改CimMode
    @RequestMapping(value = "/CIMModeChangeCommand",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvCIMModeChangeCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/CIMModeChangeCommand";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvEventFunc.CIMModeChangeCommand(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //EAP请求校正时间
    @RequestMapping(value = "/DateTimeCalibration",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvDateTimeCalibration(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/DateTimeCalibration";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvEventFunc.DateTimeCalibration(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }
    @RequestMapping(value = "/ProductionInfoDownload",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvFlowProductionInfoDownload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/ProductionInfoDownload";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvFlowFunc.ProductionInfoDownload(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //(在线)接受EAP通知下发任务全部完成命令
    @RequestMapping(value = "/LotCommandDownload",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvFlowLotCommandDownload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/LotCommandDownload";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvFlowFunc.LotCommandDownload(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //(在线)收板机接受EAP下发修正计划
    @RequestMapping(value = "/JobRemoveRecoveryRequest",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvFlowJobRemoveRecoveryRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/JobRemoveRecoveryRequest";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvFlowFunc.JobRemoveRecoveryRequest(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //(本地)上游通知下游工单信息
    @RequestMapping(value = "/BcOffLineLotDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvFlowBcOffLineLotDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/main/interf/recv/BcOffLineLotDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvFlowFunc.BcOffLineLotDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //(本地)结批完成
    @RequestMapping(value = "/LotFinishDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvFlowLotFinishDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/LotFinishDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvFlowFunc.LotFinishDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //接受上游设备发送过来的当片信息
    @RequestMapping(value = "/EachPanelDataDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvFlowEachPanelDataDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/EachPanelDataDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvFlowFunc.EachPanelDataDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //:(在线)接受EAP下发將產品規格
    @RequestMapping(value = "/ProductionSpecDownload",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRecvFlowProductionSpecDownload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/recv/ProductionSpecDownload";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapCjMasterRecvFlowFunc.ProductionSpecDownload(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }
}
