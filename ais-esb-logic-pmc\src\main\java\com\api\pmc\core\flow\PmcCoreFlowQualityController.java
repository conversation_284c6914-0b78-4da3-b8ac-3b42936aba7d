package com.api.pmc.core.flow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <p>
 * 采集质量数据流程
 * 1.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/flow")
public class PmcCoreFlowQualityController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlMapper cFuncDbSqlMapper;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private RestTemplate restTemplate;

    //1.工位数据采集定义
    @RequestMapping(value = "/PmcCoreFlowQualitySel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowQualitySel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowQualitySel";
        String selectResult = "";
        String errorMsg = "";
        String stationQTable = "d_pmc_me_station_quality";
        try {
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String serial_num = jsonParas.getString("serial_num");//工件编号
            //1、获取工位信息
            String station_id = "";//工位ID
            String sqlStation = "select COALESCE(b.work_center_code,'') work_center_code, COALESCE(a.station_id,0 ) station_id " +
                    "from sys_fmod_station a," +
                    "     sys_fmod_prod_line b " +
                    "where a.prod_line_id=b.prod_line_id " +
                    "and a.enable_flag='Y' " +
                    "and b.enable_flag='Y' " +
                    "and a.station_code='" + station_code + "' " +
                    "and b.prod_line_code='" + prod_line_code + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, null, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "工位号{" + station_code + "},系统中不存在";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            station_id = itemListStation.get(0).get("station_id").toString();
            String work_center_code = itemListStation.get(0).get("work_center_code").toString();
            //2.根据工位查询对应cellID
            String sqlCellIp = "SELECT cell.cell_webapi_port, ser.server_host_1 " +
                    "FROM  sys_core_cell cell, " +
                    "sys_core_server ser, " +
                    "sys_fmod_station station  " +
                    "WHERE cell.server_id = ser.server_id  " +
                    "AND cell.cell_id = station.cell_id " +
                    "AND station.station_id=" + station_id;
            List<Map<String, Object>> itemListCellIp = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlCellIp, false, null, apiRoutePath);
            if (itemListCellIp == null || itemListCellIp.size() <= 0) {
                errorMsg = "工位号{" + station_code + "},系统中未配置对应的单元";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String server_host_1 = itemListCellIp.get(0).get("server_host_1").toString();
            String cell_webapi_port = itemListCellIp.get(0).get("cell_webapi_port").toString();
            String cell_ip = "http://" + server_host_1 + ":" + cell_webapi_port;
            //3、获取工位数据采集定义
            String sqlQuality = "select client.client_code,tagGroup.tag_group_code,tag.tag_code," +
                    "tag.tag_id,COALESCE(qulity.quality_for,'') quality_for,COALESCE(qulity.tag_des,'') tag_des " +
                    "from scada_client client,scada_tag_group tagGroup,scada_tag tag,d_pmc_fmod_recipe_quality qulity " +
                    "where client.client_id=tagGroup.client_id and tagGroup.tag_group_id=tag.tag_group_id " +
                    "and tag.tag_id=qulity.tag_id and qulity.enable_flag='Y' and qulity.station_id=" + station_id;
            List<Map<String, Object>> itemListQuality = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlQuality, false, null, apiRoutePath);
            if (itemListQuality == null || itemListQuality.size() <= 0) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListQuality, "", "", 0);
                return selectResult;
            }
            //4.获取点位信息
            JSONArray readTagList = new JSONArray();
            for (Map<String, Object> map : itemListQuality) {
                JSONObject readTag = new JSONObject();
                String tagKey = map.get("client_code").toString() + "/" + map.get("tag_group_code").toString() + "/" + map.get("tag_code").toString();
                map.put("tag_key", tagKey);
                readTag.put("tag_key", tagKey);
                readTagList.add(readTag);
            }
            JSONObject tagResult = cFuncUtilsRest.PostJaBackJb(cell_ip + "/cell/core/scada/CoreScadaReadTag", readTagList);
            if (tagResult == null && !"0".equals(tagResult.getString("code"))) {
                return CFuncUtilsLayUiResut.GetErrorJson("点位数据获取失败");
            }

            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            JSONArray qulityTagValueList = tagResult.getJSONArray("data");
            for (int i = 0; i < qulityTagValueList.size(); i++) {
                JSONObject obj = qulityTagValueList.getJSONObject(i);
                String tag_key = obj.getString("tag_key");
                String tag_value = obj.getString("tag_value");
                for (int j = 0; j < itemListQuality.size(); j++) {
                    String tagKey = itemListQuality.get(j).get("tag_key").toString();
                    if (tag_key.equals(tagKey)) {
                        String tag_id = itemListQuality.get(j).get("tag_id").toString();
                        String quality_for = itemListQuality.get(j).get("quality_for").toString();
                        String tag_des = itemListQuality.get(j).get("tag_des").toString();
                        //组成数据串
                        Map<String, Object> mapDataItem = new HashMap<>();
                        String quality_trace_id = CFuncUtilsSystem.CreateUUID(true);
                        mapDataItem.put("item_date", item_date);
                        mapDataItem.put("item_date_val", item_date_val);
                        mapDataItem.put("quality_trace_id", quality_trace_id);
                        mapDataItem.put("work_center_code", work_center_code);
                        mapDataItem.put("prod_line_code", prod_line_code);
                        mapDataItem.put("station_code", station_code);
                        mapDataItem.put("serial_num", serial_num);
                        mapDataItem.put("tag_id", tag_id);
                        mapDataItem.put("quality_for", quality_for);
                        mapDataItem.put("tag_des", tag_des);
                        mapDataItem.put("tag_value", tag_value);
                        mapDataItem.put("quality_d_sign", "OK");
                        mapDataItem.put("up_flag", "N");
                        mapDataItem.put("up_code", "");
                        mapDataItem.put("up_msg", "");
                        lstDocuments.add(mapDataItem);
                        break;
                    }
                }
            }
            //5.保存质量数据到数据库
            mongoTemplate.insert(lstDocuments, stationQTable);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListQuality, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "获取工位数据采集定义异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.工序完成
    @Transactional
    @RequestMapping(value = "/PmcCoreFlowQualityFinish", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowQualityFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws
            Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowQualityFinish";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String make_order = jsonParas.getString("make_order");//订单号
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //1、获取工位生产订单
            String station_mo_id = "";//ID
            String sqlStationMo = "select COALESCE(station_mo_id,0) station_mo_id " +
                    "from d_pmc_me_station_mo " +
                    "where work_status='PLAN' " +
                    "and set_sign='NONE' " +
                    "and station_code='" + station_code + "' " +
                    "and make_order='" + make_order + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, null, apiRoutePath);
            if (itemListStationMo != null && itemListStationMo.size() > 0) {
                station_mo_id = itemListStationMo.get(0).get("station_mo_id").toString();
                //修改为完成
                String updSql = "update d_pmc_me_station_mo set " +
                        "last_updated_by='" + station_code + "'," +
                        "last_update_date='" + nowDateTime + "'," +
                        "work_status='FINISH' " +
                        "where station_mo_id=" + station_mo_id;
                cFuncDbSqlExecute.ExecUpdateSql(station_code, updSql, true, null, apiRoutePath);
            }
            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("station_mo_id", station_mo_id);
            itemListMo.add(map);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "工序完成异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
