package com.api.eap.core.interf.ais1;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 投收扳机标准发送流程接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/interf/send")
public class Eap1CoreSendFlowController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private Eap1CoreSendFlowFunc eap1CoreSendFlowFunc;

    //1.[接口]AIS请求搬入载具(EAP发布)
    @RequestMapping(value = "/CallPalletEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String CallPalletEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/CallPalletEvent";
        String transResult = "";
        String errorMsg = "";
        try {
            String esbInterfCode = "CallPalletEvent";
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String call_type = jsonParas.getString("call_type");
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("port_code", port_code);
            postParas.put("call_type", call_type);
            eap1CoreSendFlowFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS请求搬入载具异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]AIS上报AGV安全验证进度(EAP发布)
    @RequestMapping(value = "/UpAgvSaftCheckStatusEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String UpAgvSaftCheckStatusEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/UpAgvSaftCheckStatusEvent";
        String transResult = "";
        String errorMsg = "";
        try {
            String esbInterfCode = "UpAgvSaftCheckStatusEvent";
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String move_type = jsonParas.getString("move_type");
            String check_status = jsonParas.getString("check_status");
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("port_code", port_code);
            postParas.put("move_type", move_type);
            postParas.put("check_status", check_status);
            eap1CoreSendFlowFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS上报AGV安全验证进度异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.[接口]AIS主动请求任务(EAP发布)
    @RequestMapping(value = "/AskDownTaskEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String AskDownTaskEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/AskDownTaskEvent";
        String transResult = "";
        String errorMsg = "";
        try {
            String esbInterfCode = "AskDownTaskEvent";
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String request_barcode = jsonParas.getString("request_barcode");
            String request_type = jsonParas.getString("request_type");
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("port_code", port_code);
            postParas.put("request_barcode", request_barcode);
            postParas.put("request_type", request_type);
            eap1CoreSendFlowFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS主动请求任务异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.[接口]AIS申请允许放板(EAP发布)
    @RequestMapping(value = "/AskEapAllowStartEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String AskEapAllowStartEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/AskEapAllowStartEvent";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String esbInterfCode = "AskEapAllowStartEvent";
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String plan_id = jsonParas.getString("plan_id");
            String group_lot_num = "";
            String lot_num = "";
            String request_barcode = jsonParas.getString("request_barcode");
            String request_type = jsonParas.getString("request_type");
            Long station_id = jsonParas.getLong("station_id");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                lot_num = docItemBigData.getString("lot_num");
                iteratorBigData.close();
            }
            //获取当前登入者
            String user_name = "";
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("port_code", port_code);
            postParas.put("group_lot_num", group_lot_num);
            postParas.put("lot_num", lot_num);
            postParas.put("request_barcode", request_barcode);
            postParas.put("request_type", request_type);
            eap1CoreSendFlowFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS申请允许放板异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.[接口]AIS主动询问PanelID合格标志(EAP发布)
    @RequestMapping(value = "/CheckPanelStatusEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String CheckPanelStatusEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/CheckPanelStatusEvent";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String esbInterfCode = "CheckPanelStatusEvent";
            String station_code = jsonParas.getString("station_code");
            String group_id = jsonParas.getString("group_id");
            String plan_id = jsonParas.getString("plan_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_num = jsonParas.getString("tray_num");
            String port_code = "";
            String group_lot_num = "";
            String lot_num = "";
            String short_lot_num = "";
            String pallet_num = "";
            String top_tray_num = "";
            int panel_index = 0;
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            queryBigData = new Query();
            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                port_code = docItemBigData.getString("port_code");
                group_lot_num = docItemBigData.getString("group_lot_num");
                lot_num = docItemBigData.getString("lot_num");
                short_lot_num = docItemBigData.getString("short_lot_num");
                pallet_num = docItemBigData.getString("pallet_num");
                top_tray_num = docItemBigData.getString("top_tray_num");
                panel_index = Integer.parseInt(docItemBigData.get("finish_ok_count").toString()) + 1;
                iteratorBigData.close();
            }

            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("port_code", port_code);
            postParas.put("group_lot_num", group_lot_num);
            postParas.put("lot_num", lot_num);
            postParas.put("short_lot_num", short_lot_num);
            postParas.put("pallet_num", pallet_num);
            postParas.put("top_tray_num", top_tray_num);
            postParas.put("tray_num", tray_num);
            postParas.put("panel_index", panel_index);
            postParas.put("panel_barcode", panel_barcode);
            String responseParas = eap1CoreSendFlowFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, responseParas, "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS主动询问PanelID合格标志异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //6.[接口]AIS实时上报PanelID事件(EAP发布)
    @RequestMapping(value = "/PanelResultUpEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String PanelResultUpEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/PanelResultUpEvent";
        String transResult = "";
        String errorMsg = "";
        try {
            String esbInterfCode = "PanelResultUpEvent";
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String lot_num = jsonParas.getString("lot_num");
            String lot_short_num = jsonParas.getString("lot_short_num");
            String pallet_num = jsonParas.getString("pallet_num");
            String offline_flag = jsonParas.getString("offline_flag");
            String tray_num = jsonParas.getString("tray_num");
            int panel_index = jsonParas.getInteger("panel_index");
            String td_tray_num = jsonParas.getString("td_tray_num");
            int panel_ng_code = jsonParas.getInteger("panel_ng_code");//上报扫描代码0合格,1混板,2读码异常,3重码,4强制越过,5其他
            String panel_status = jsonParas.getString("panel_status");
            String panel_ng_msg = jsonParas.getString("panel_ng_msg");
            String panel_barcode = jsonParas.getString("panel_barcode");
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("port_code", port_code);
            postParas.put("offline_flag", offline_flag);
            postParas.put("group_lot_num", group_lot_num);
            postParas.put("lot_num", lot_num);
            postParas.put("short_lot_num", lot_short_num);
            postParas.put("pallet_num", pallet_num);
            postParas.put("td_tray_num", td_tray_num);
            postParas.put("tray_num", tray_num);
            postParas.put("panel_barcode", panel_barcode);
            postParas.put("panel_status", panel_status);
            postParas.put("panel_code", panel_ng_code);
            postParas.put("panel_msg", panel_ng_msg);
            postParas.put("panel_index", panel_index);
            eap1CoreSendFlowFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS实时上报PanelID事件异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //7.[接口]AIS完板上报事件(EAP发布)
    @RequestMapping(value = "/TaskTrackFinishEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String TaskTrackFinishEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/TaskTrackFinishEvent";
        String transResult = "";
        String errorMsg = "";
        String meStationFlowTable = "a_eap_me_station_flow";
        String apsPlanTable = "a_eap_aps_plan";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String esbInterfCode = "TaskTrackFinishEvent";
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发配方任务中工位号{" + station_code + "}在AIS系统中未配置";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String port_code = jsonParas.getString("port_code");
            String group_id = jsonParas.getString("group_id");
            String pallet_num = jsonParas.getString("pallet_num");
            String offline_flag = jsonParas.getString("offline_flag");
            String td_tray_num = jsonParas.getString("td_tray_num");

            String group_lot_num = "";
            JSONArray lot_wip_list = new JSONArray();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            while (iteratorBigData.hasNext()) {
                Document lotItem = iteratorBigData.next();
                group_lot_num = lotItem.getString("group_lot_num");
                String plan_id = lotItem.getString("plan_id");
                String lot_num = lotItem.getString("lot_num");
                String short_lot_num = lotItem.getString("short_lot_num");
                String plan_count = lotItem.getString("plan_count");
                int finish_count = lotItem.getInteger("finish_count");
                int finish_ok_count = lotItem.getInteger("finish_ok_count");
                int finish_ng_count = lotItem.getInteger("finish_ng_count");

                //查询过站明细
                String[] panel_status_list = new String[]{"OK", "NG_PASS"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("panel_status").in(panel_status_list));

                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).
                        find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                        noCursorTimeout(true).batchSize(100).iterator();
                JSONArray panel_list = new JSONArray();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    String panel_barcode = docItemBigData.getString("panel_barcode");
                    String panel_status = docItemBigData.getString("panel_status");
                    int panel_index = docItemBigData.getInteger("panel_index");
                    String panel_time = sdf.format(docItemBigData.getDate("item_date"));
                    int panel_code = docItemBigData.getInteger("panel_ng_code");
                    String panel_msg = docItemBigData.getString("panel_ng_msg");
                    String tray_num = docItemBigData.getString("tray_barcode");
                    String panel_user_id = user_name;

                    JSONObject jbDetail = new JSONObject();
                    jbDetail.put("panel_barcode", panel_barcode);
                    jbDetail.put("panel_status", panel_status);
                    jbDetail.put("panel_index", panel_index);
                    jbDetail.put("panel_time", panel_time);
                    jbDetail.put("panel_code", panel_code);
                    jbDetail.put("panel_msg", panel_msg);
                    jbDetail.put("tray_num", tray_num);
                    jbDetail.put("panel_user_id", panel_user_id);
                    panel_list.add(jbDetail);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
                JSONObject objLot = new JSONObject();
                objLot.put("lot_num", lot_num);
                objLot.put("short_lot_num", short_lot_num);
                objLot.put("plan_count", plan_count);
                objLot.put("finish_count", finish_count);
                objLot.put("finish_ok_count", finish_ok_count);
                objLot.put("finish_ng_count", finish_ng_count);
                objLot.put("panel_list", panel_list);
                lot_wip_list.add(objLot);
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("port_code", port_code);
            postParas.put("offline_flag", offline_flag);
            postParas.put("group_lot_num", group_lot_num);
            postParas.put("pallet_num", pallet_num);
            postParas.put("td_tray_num", td_tray_num);
            postParas.put("lot_wip_list", lot_wip_list);
            eap1CoreSendFlowFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS完板上报事件异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //8.[接口]AIS请求退载具(EAP发布)
    @RequestMapping(value = "/AskPalletMoveOutEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String AskPalletMoveOutEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/AskPalletMoveOutEvent";
        String transResult = "";
        String errorMsg = "";
        String meStationFlowTable = "a_eap_me_station_flow";
        String apsPlanTable = "a_eap_aps_plan";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String esbInterfCode = "AskPalletMoveOutEvent";
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            String station_code = jsonParas.getString("station_code");
            String group_id = jsonParas.getString("group_id");
            String port_code = jsonParas.getString("port_code");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String pallet_num = jsonParas.getString("pallet_num");
            String td_tray_num = jsonParas.getString("td_tray_num");
            int out_code = jsonParas.getInteger("out_code");
            String lot_num = "";
            String out_msg = "";
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            while (iteratorBigData.hasNext()) {
                Document lotItem = iteratorBigData.next();
                String lot_num_i = lotItem.getString("lot_num");
                if (lot_num.equals("")) lot_num = lot_num_i;
                else lot_num += "," + lot_num_i;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("port_code", port_code);
            postParas.put("group_lot_num", group_lot_num);
            postParas.put("lot_num", lot_num);
            postParas.put("pallet_num", pallet_num);
            postParas.put("td_tray_num", td_tray_num);
            postParas.put("out_code", out_code);
            postParas.put("out_msg", out_msg);
            eap1CoreSendFlowFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS完板上报事件异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //9.[接口]首板通过请求放行(EAP发布)
    @RequestMapping(value = "/InspectConfirmEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String InspectConfirmEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/InspectConfirmEvent";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String esbInterfCode = "InspectConfirmEvent";
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            String station_code = jsonParas.getString("station_code");
            String plan_id = jsonParas.getString("plan_id");
            String group_lot_num = "";
            int inspect_count = 0;
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                inspect_count = docItemBigData.getInteger("inspect_finish_count");
                iteratorBigData.close();
            }

            JSONArray inspect_list = new JSONArray();
            if (!plan_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                queryBigData.addCriteria(Criteria.where("inspect_flag").is("Y"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).
                        find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                        noCursorTimeout(true).batchSize(100).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    String lot_num = docItemBigData.getString("lot_num");
                    String panel_barcode = docItemBigData.getString("panel_barcode");
                    String panel_time = sdf.format(docItemBigData.getDate("item_date"));
                    JSONObject jbDetail = new JSONObject();
                    jbDetail.put("lot_num", lot_num);
                    jbDetail.put("panel_barcode", panel_barcode);
                    jbDetail.put("inspect_time", panel_time);
                    inspect_list.add(jbDetail);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("group_lot_num", group_lot_num);
            postParas.put("inspect_count", inspect_count);
            postParas.put("inspect_list", inspect_list);
            String result = eap1CoreSendFlowFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS完板上报事件异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
