package com.api.eap.project.fz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.EapCoreSharePlanController;
import com.api.eap.project.glorysoft.EapGloryInterfCommon;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 方正AP接受接口方法内容
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Service
@Slf4j
public class EapFzRecvInterfFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private EapFzInterfCommon eapFzInterfCommon;
    @Autowired
    private EapCoreSharePlanController eapCoreSharePlanController;

    //EAP初始数据请求
    public JSONObject aisInitialDataRequest(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "InitialDataRequest";
        String esbInterfCode = "AisInitialDataRequest";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("header");
            jbRecvBody = jsonParas.getJSONObject("body");
            String message_name = jbRecvHeader.getString("messagename");

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //获取当前工位信息
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "当前AIS投收板机系统中未维护设备编号";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            String station_code = itemList.get(0).get("station_code").toString();
            String station_attr = itemList.get(0).get("station_attr").toString();
            //获取当前模式
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String tagSysModel = "";
            String tagDeviceModel = "";
            String tagTotalWipCount = "";
            String tagDeviceStatus = "";
            String tagPnName = "";
            String tagLotNum = "";
            String tagOrderId = "";
            String tagRecipeName = "";

            String SysModel = "";
            String DeviceModel = "";
            String TotalWipCount = "0";
            String DeviceStatus = "";
            String PnName = "";
            String LotNum = "";
            String OrderId = "";
            String RecipeName = "";
            if (aisMonitorModel.equals("AIS-PC")) {
                if (station_attr.equals("Load")) {
                    tagSysModel = "LoadPlc/PlcConfig/SysModel";
                    tagDeviceModel = "LoadPlc/PlcStatus/DeviceModel";
                    tagDeviceStatus = "LoadPlc/PlcStatus/DeviceStatus";
                } else {
                    tagSysModel = "UnLoadPlc/PlcConfig/SysModel";
                    tagDeviceModel = "UnLoadPlc/PlcStatus/DeviceModel";
                    tagDeviceStatus = "UnLoadPlc/PlcStatus/DeviceStatus";
                }
            } else {
                if (station_attr.equals("Load")) {
                    tagSysModel = "LoadPlc_" + station_code + "/PlcConfig/SysModel";
                    tagDeviceModel = "LoadPlc_" + station_code + "/PlcStatus/DeviceModel";
                    tagDeviceStatus = "LoadPlc_" + station_code + "/PlcStatus/DeviceStatus";
                } else {
                    tagSysModel = "UnLoadPlc_" + station_code + "/PlcConfig/SysModel";
                    tagDeviceModel = "UnLoadPlc_" + station_code + "/PlcStatus/DeviceModel";
                    tagDeviceStatus = "UnLoadPlc_" + station_code + "/PlcStatus/DeviceStatus";
                }
            }

            String sumReadTags = tagSysModel + "," + tagDeviceModel + "," + tagTotalWipCount + "," + tagDeviceStatus + "," +
                    tagPnName + "," + tagLotNum + "," + tagOrderId + "," + tagRecipeName;
            JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, sumReadTags);
            if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                for (int i = 0; i < jsonArrayTag.size(); i++) {
                    JSONObject jbItem = jsonArrayTag.getJSONObject(i);
                    String tag_key = jbItem.getString("tag_key");
                    String tag_value = jbItem.getString("tag_value");
                    if (tag_key.equals(tagSysModel)) SysModel = tag_value;
                    if (tag_key.equals(tagDeviceModel)) DeviceModel = tag_value;
                    if (tag_key.equals(tagTotalWipCount)) TotalWipCount = tag_value;
                    if (tag_key.equals(tagDeviceStatus)) DeviceStatus = tag_value;
                    if (tag_key.equals(tagPnName)) PnName = tag_value;
                    if (tag_key.equals(tagLotNum)) LotNum = tag_value;
                    if (tag_key.equals(tagOrderId)) OrderId = tag_value;
                    if (tag_key.equals(tagRecipeName)) RecipeName = tag_value;
                }
            }
            JSONObject result = new JSONObject();
            String nowDate = CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss");
            result.put("date_time", nowDate);
            result.put("operation_mode", SysModel);
            result.put("total_wip_count", TotalWipCount);
            result.put("equipment_status", DeviceStatus);
            result.put("pn_name", PnName);
            result.put("lotID", LotNum);
            result.put("order_id", OrderId);
            result.put("recipe_name", RecipeName);

            jbResponse = eapFzInterfCommon.CreateResult(true, null, "");
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "EAP初始数据请求成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP请求点检数据
    public JSONObject aisTraceDataRequest(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "TraceDataRequest";
        String esbInterfCode = "AisTraceDataRequest";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("header");
            jbRecvBody = jsonParas.getJSONObject("body");
            String message_name = jbRecvHeader.getString("messagename");

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //获取当前工位信息
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "当前AIS投收板机系统中未维护设备编号";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String station_code = itemList.get(0).get("station_code").toString();
            String station_attr = itemList.get(0).get("station_attr").toString();

            //获取当前模式
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String tagPanelTotalQty = "";
            String tagRuntime = "";
            String tagUtilizationRate = "";

            String PanelTotalQty = "";
            String Runtime = "";
            String UtilizationRate = "";
            if (aisMonitorModel.equals("AIS-PC")) {
                if (station_attr.equals("Load")) {
                    tagPanelTotalQty = "LoadPlc/PlcConfig/SysModel";
                    tagRuntime = "LoadPlc/PlcStatus/DeviceModel";
                    tagUtilizationRate = "LoadPlc/PlcStatus/DeviceStatus";
                } else {
                    tagPanelTotalQty = "UnLoadPlc/PlcConfig/SysModel";
                    tagRuntime = "UnLoadPlc/PlcStatus/DeviceModel";
                    tagUtilizationRate = "UnLoadPlc/PlcStatus/DeviceStatus";
                }
            } else {
                if (station_attr.equals("Load")) {
                    tagPanelTotalQty = "LoadPlc_" + station_code + "/PlcConfig/SysModel";
                    tagRuntime = "LoadPlc_" + station_code + "/PlcStatus/DeviceModel";
                    tagUtilizationRate = "LoadPlc_" + station_code + "/PlcStatus/DeviceStatus";
                } else {
                    tagPanelTotalQty = "UnLoadPlc_" + station_code + "/PlcConfig/SysModel";
                    tagRuntime = "UnLoadPlc_" + station_code + "/PlcStatus/DeviceModel";
                    tagUtilizationRate = "UnLoadPlc_" + station_code + "/PlcStatus/DeviceStatus";
                }
            }

            String sumReadTags = tagPanelTotalQty + "," + tagRuntime + "," + tagUtilizationRate;
            JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, sumReadTags);
            if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                for (int i = 0; i < jsonArrayTag.size(); i++) {
                    JSONObject jbItem = jsonArrayTag.getJSONObject(i);
                    String tag_key = jbItem.getString("tag_key");
                    String tag_value = jbItem.getString("tag_value");
                    if (tag_key.equals(tagPanelTotalQty)) PanelTotalQty = tag_value;
                    if (tag_key.equals(tagRuntime)) Runtime = tag_value;
                    if (tag_key.equals(tagUtilizationRate)) UtilizationRate = tag_value;
                }
            }
            jbResponse = new JSONObject();
            JSONArray trace_data = new JSONArray();
            JSONObject trace_data_item1 = new JSONObject();
            trace_data_item1.put("data_no", "1");
            trace_data_item1.put("data_item", "EQPID");
            trace_data_item1.put("data_value", station_code);
            trace_data.add(trace_data_item1);

            JSONObject trace_data_item2 = new JSONObject();
            trace_data_item2.put("data_no", "2");
            trace_data_item2.put("data_item", "PanelTotalQty");
            trace_data_item2.put("data_value", PanelTotalQty);
            trace_data.add(trace_data_item2);

            JSONObject trace_data_item3 = new JSONObject();
            trace_data_item3.put("data_no", "3");
            trace_data_item3.put("data_item", "Runtime");
            trace_data_item3.put("data_value", Runtime);
            trace_data.add(trace_data_item3);

            JSONObject trace_data_item4 = new JSONObject();
            trace_data_item4.put("data_no", "4");
            trace_data_item4.put("data_item", "UtilizationRate");
            trace_data_item4.put("data_value", UtilizationRate);
            trace_data.add(trace_data_item4);

            JSONObject result = new JSONObject();
            result.put("trace_data_list", trace_data);
            jbResponse = eapFzInterfCommon.CreateResult(true, result, "");
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "EAP请求点检数据成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP远程讯息命令
    public JSONObject aisCIMMessageCommand(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CIMMessageCommand";
        String esbInterfCode = "AisCIMMessageCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        String cimTableName = "a_eap_me_station_hmi_show";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("header");
            jbRecvBody = jsonParas.getJSONObject("body");
            String message_name = jbRecvHeader.getString("messagename");
            String action_type = jbRecvBody.getString("action_type");
            String interval_second_time = jbRecvBody.getString("interval_second_time");
            String cim_message = jbRecvBody.getString("cim_message");

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //获取当前工位信息
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "当前AIS投收板机系统中未维护设备编号";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String station_code = itemList.get(0).get("station_code").toString();
            String station_attr = itemList.get(0).get("station_attr").toString();

            if (cim_message == null || cim_message.equals("")) {
                errorMsg = "CIM消息不能为空";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //插入CIM消息
            long station_id = Long.parseLong(itemList.get(0).get("station_id").toString());
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("hmi_show_id", CFuncUtilsSystem.CreateUUID(true));
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("screen_control", "1");
            mapBigDataRow.put("screen_code", "EAP");
            mapBigDataRow.put("cim_from", "EAP");
            mapBigDataRow.put("cim_msg", cim_message);
            mapBigDataRow.put("finish_flag", "N");
            mongoTemplate.insert(mapBigDataRow, cimTableName);

            //成功
            jbResponse = eapFzInterfCommon.CreateResult(true, null, "");
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受CIM消息成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP主动下发档案路径和批次信息
    public JSONObject aisFilePathAndLotInformationDownload(JSONObject jsonParas, HttpServletRequest request) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "FilePathAndLotInformationDownload";
        String esbInterfCode = "AisFilePathAndLotInformationDownload";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("header");
            jbRecvBody = jsonParas.getJSONObject("body");
            String message_name = jbRecvHeader.getString("messagename");

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //获取当前工位信息
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "当前AIS投收板机系统中未维护设备编号";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String station_code = itemList.get(0).get("station_code").toString();
            String station_attr = itemList.get(0).get("station_attr").toString();

            //2.数据解析
            if (jbRecvBody != null) {
                String lot_id = jbRecvBody.getString("lot_id");
                String pn_name = jbRecvBody.getString("pn_name");
                String port_id = jbRecvBody.getString("port_id");
                String panel_count = jbRecvBody.getString("panel_count");
                JSONObject panel_list = jbRecvBody.getJSONObject("panel_list");
                JSONArray panel_id = panel_list.getJSONArray("panel_id");
                String panel_id_list = "";
                for (int i = 0; i < panel_id.size(); i++) {
                    if (i == 0) panel_id_list = panel_id.getString(i);
                    else panel_id_list += "," + panel_id.getString(i);
                }
                String panel_width = "";
                String panel_length = "";
                JSONObject parameter_list = jbRecvBody.getJSONObject("parameter_list");
                if (parameter_list != null) {
                    JSONArray parameter = parameter_list.getJSONArray("parameter");
                    if (parameter != null && parameter.size() > 0) {
                        for (int i = 0; i < parameter.size(); i++) {
                            String parameter_name = parameter.getJSONObject(i).getString("parameter_name");
                            String parameter_value = parameter.getJSONObject(i).getString("parameter_value");
                            if (parameter_name.equals("PanelWidth"))
                                panel_width = parameter_value;
                            if (parameter_name.equals("BotYScale"))
                                panel_length = parameter_value;
                        }
                    }
                }
                //2.1 数据校验
                //查询端口号是否正确
                String sqlPortCount = "select count(1) " +
                        "from sys_fmod_station a inner join a_eap_fmod_station_port b " +
                        "on a.station_id=b.station_id " +
                        "where station_code='" + station_code + "' and b.port_code='" + port_id + "'";
                Integer count = cFuncDbSqlResolve.GetSelectCount(sqlPortCount);
                if (count <= 0) {
                    errorMsg = "AIS系统未配置工位{" + station_code + "}以及端口{" + port_id + "}匹配关系信息,检查AIS或者EAP参数";
                    jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                    responseParas = jbResponse.toString();
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                //2.3 插入数据到数据库
                //2.3.1.获取配方参数
                jbRecvBody.remove("lot_id");
                jbRecvBody.remove("pn_name");
                jbRecvBody.remove("port_id");
                jbRecvBody.remove("panel_count");
                jbRecvBody.remove("panel_list");
                jbRecvBody.remove("parameter_list");
                String item_info=jbRecvBody.toString();

                JSONArray plan_list = new JSONArray();
                JSONObject jbItem = new JSONObject();
                jbItem.put("task_from", "EAP");
                jbItem.put("group_lot_num", lot_id);
                jbItem.put("lot_num", lot_id);
                jbItem.put("lot_index", 1);
                jbItem.put("plan_lot_count", Integer.parseInt(panel_count));
                jbItem.put("target_lot_count", Integer.parseInt(panel_count));
                jbItem.put("port_code", port_id);
                jbItem.put("panel_length", Double.parseDouble(panel_length));
                jbItem.put("panel_width", Double.parseDouble(panel_width));
                jbItem.put("material_code", pn_name);
                jbItem.put("panel_list", panel_id_list);
                jbItem.put("item_info", item_info);
                plan_list.add(jbItem);

                JSONObject paras = new JSONObject();
                paras.put("station_code", station_code);
                paras.put("plan_list", plan_list);
                JSONObject jbPlanResult = JSONObject.parseObject(eapCoreSharePlanController.EapCoreSharePlanSave(paras, request));

                if (jbPlanResult.getInteger("code") != 0) {
                    errorMsg = "保存批次号{" + lot_id + "}任务信息失败：" + jbPlanResult.getString("error");
                    jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                    responseParas = jbResponse.toString();
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
            }

            //成功
            jbResponse = eapFzInterfCommon.CreateResult(true, null, "");
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "EAP主动下发档案路径和批次信息成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP批次数据删除请求
    public JSONObject aisDeleteLotInfoDataRequest(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "DeleteLotInfoDataRequest";
        String esbInterfCode = "AisDeleteLotInfoDataRequest";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        String apsPlanTable = "a_eap_aps_plan";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("header");
            jbRecvBody = jsonParas.getJSONObject("body");
            String message_name = jbRecvHeader.getString("messagename");
            String equip_id = jbRecvBody.getString("equip_id");
            String lot_id = jbRecvBody.getString("lot_id");
            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //获取当前工位信息
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + equip_id + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + equip_id + "}不存在AIS投收板机系统中";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            String station_id = itemList.get(0).toString();
            Long station_id_long = Long.parseLong(station_id);
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_id));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document plan = iteratorBigData.next();
                String group_lot_status = plan.getString("group_lot_status");
                if (group_lot_status.equals("WORK")) {
                    errorMsg = "设备编号{" + equip_id + "}上批次号{" + lot_id + "}WORK状态，不允许删除";
                    jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                    responseParas = jbResponse.toString();
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                mongoTemplate.remove(queryBigData, apsPlanTable);
                iteratorBigData.close();
            }
            //成功
            jbResponse = eapFzInterfCommon.CreateResult(true, null, "");
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "EAP批次数据删除请求成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP询问配方名称是否存在
    public JSONObject aisRecipeNameExistRequest(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "RecipeNameExistRequest";
        String esbInterfCode = "AisRecipeNameExistRequest";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        String apsPlanTable = "a_eap_aps_plan";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("header");
            jbRecvBody = jsonParas.getJSONObject("body");
            String message_name = jbRecvHeader.getString("messagename");
            String equip_id = jbRecvBody.getString("equip_id");
            String recipe_name = jbRecvBody.getString("recipe_name");
            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //获取当前工位信息
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + equip_id + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + equip_id + "}不存在AIS投收板机系统中";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            String station_id = itemList.get(0).toString();
            Long station_id_long = Long.parseLong(station_id);
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("attribute1").is(recipe_name));
            long allCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());

            String judge_operator = "0";
            if (allCount > 0) {
                judge_operator = "1";
            }
            JSONObject result = new JSONObject();
            result.put("judge_operator", judge_operator);
            //成功
            jbResponse = eapFzInterfCommon.CreateResult(true, result, "");
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "EAP询问配方名称是否存在成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP向设备询问当前的配方名及配方参数
    public JSONObject aisRecipeParameterRequest(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "RecipeParameterRequest";
        String esbInterfCode = "AisRecipeParameterRequest";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        String apsPlanTable = "a_eap_aps_plan";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("header");
            jbRecvBody = jsonParas.getJSONObject("body");
            String message_name = jbRecvHeader.getString("messagename");
            String equip_id = jbRecvBody.getString("equip_id");
            String recipe_name = jbRecvBody.getString("recipe_name");
            String lot_id = jbRecvBody.getString("lot_id");
            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //获取当前工位信息
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + equip_id + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + equip_id + "}不存在AIS投收板机系统中";
                jbResponse = eapFzInterfCommon.CreateResult(false, null, errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            String station_id = itemList.get(0).toString();
            Long station_id_long = Long.parseLong(station_id);
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("attribute1").is(recipe_name));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            String panel_width = "";
            String panel_length = "";
            if (iteratorBigData.hasNext()) {
                Document plan = iteratorBigData.next();
                panel_width = plan.getDouble("panel_width").toString();
                panel_length = plan.getDouble("panel_length").toString();
                iteratorBigData.close();
            }

            JSONObject result = new JSONObject();
            result.put("recipe_name", recipe_name);
            JSONObject parameter_list = new JSONObject();
            JSONArray parameter = new JSONArray();
            JSONObject parameter_item1=new JSONObject();
            parameter_item1.put("parameter_name","PanelWidth");
            parameter_item1.put("parameter_value",panel_width);
            parameter.add(parameter_item1);

            JSONObject parameter_item2=new JSONObject();
            parameter_item2.put("parameter_name","BotYScale");
            parameter_item2.put("parameter_value",panel_length);
            parameter.add(parameter_item2);
            parameter_list.put("parameter", parameter);
            result.put("recipe_name", recipe_name);
            result.put("parameter_list", parameter_list);
            //成功
            jbResponse = eapFzInterfCommon.CreateResult(true, result, "");
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "EAP向设备询问当前的配方名及配方参数成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
