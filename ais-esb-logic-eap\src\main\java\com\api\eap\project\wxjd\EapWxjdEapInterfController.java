package com.api.eap.project.wxjd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.api.eap.project.dy.EapDySendEventFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 定颖放板机定制生产计划处理逻辑
 * 1.放板机Panel校验
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/wxjd")
public class EapWxjdEapInterfController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapWxjdEapInterfFunc eapWxjdEapInterfFunc;

    //1.AIS记录投板機讀碼失敗板補碼下發(避免收板讀到失敗碼後被當作混批板處理)
    @RequestMapping(value = "/EapLoadOkPanelAdvanceSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapLoadOkPanelAdvanceSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxjd/EapLoadOkPanelAdvanceSave";
        String transResult = "";
        String errorMsg = "";
        String apsLoadOkPanelTable = "a_eap_aps_load_ok_panel";
        String result = "";
        try {
            String prodLineCode = jsonParas.getString("prodLineCode");
            String stationCode = jsonParas.getString("stationCode");
            String portCode = jsonParas.getString("portCode");
            String carryNum = jsonParas.getString("carryNum");//tray盘码
            String okPanelList = jsonParas.getString("okPanelList");//用_分割
            String[] array = okPanelList.split("_");
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            if (array != null && array.length > 0) {
                for (int i = 0; i < array.length; i++) {
                    Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                    long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                    String ok_panel_id = CFuncUtilsSystem.CreateUUID(true);
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("item_date", item_date);
                    mapBigDataRow.put("item_date_val", item_date_val);
                    mapBigDataRow.put("ok_panel_id", ok_panel_id);
                    mapBigDataRow.put("prod_line_code", prodLineCode);
                    mapBigDataRow.put("station_code", stationCode);
                    mapBigDataRow.put("port_code", portCode);
                    mapBigDataRow.put("pallet_num", carryNum);
                    mapBigDataRow.put("panel_barcode", array[i]);
                    lstDocuments.add(mapBigDataRow);
                }
            }
            if (lstDocuments.size() > 0) {
                mongoTemplate.insert(lstDocuments, apsLoadOkPanelTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.AIS记录EAP主動提前通知收板機對NG板收料到NG工位信息
    @RequestMapping(value = "/EapUnloadNgPanelAdvanceSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapUnloadNgPanelAdvanceSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxjd/EapUnloadNgPanelAdvanceSave";
        String transResult = "";
        String errorMsg = "";
        String apsUnLoadNgPanelTable = "a_eap_aps_unload_ng_panel";
        String result = "";
        try {
            String prodLineCode = jsonParas.getString("prodLineCode");
            String stationCode = jsonParas.getString("stationCode");
            String lotNum = jsonParas.getString("lotNum");
            String ngPanelList = jsonParas.getString("ngPanelList");//用_分割
            String ngMsg = jsonParas.getString("ngMsg");//
            String[] array = ngPanelList.split("_");
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            if (array != null && array.length > 0) {
                for (int i = 0; i < array.length; i++) {
                    Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                    long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                    String ok_panel_id = CFuncUtilsSystem.CreateUUID(true);
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("item_date", item_date);
                    mapBigDataRow.put("item_date_val", item_date_val);
                    mapBigDataRow.put("ok_panel_id", ok_panel_id);
                    mapBigDataRow.put("prod_line_code", prodLineCode);
                    mapBigDataRow.put("station_code", stationCode);
                    mapBigDataRow.put("lot_num", lotNum);
                    mapBigDataRow.put("panel_barcode", array[i]);
                    mapBigDataRow.put("ng_msg", ngMsg);
                    lstDocuments.add(mapBigDataRow);
                }
            }
            if (lstDocuments.size() > 0) {
                mongoTemplate.insert(lstDocuments, apsUnLoadNgPanelTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.AIS读码上报EAP
    @RequestMapping(value = "/EapPanelReadReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapPanelReadReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxjd/EapPanelReadReport";
        String transResult = "";
        String errorMsg = "";
        String result = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            String station_code = jsonParas.getString("station_code");
            String plcPlcWorkPortIndexValue = jsonParas.getString("plcPlcWorkPortIndexValue");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String pallet_num = jsonParas.getString("pallet_num");
            String ccd_code = jsonParas.getString("ccd_code");
            Integer panel_index = jsonParas.getInteger("panel_index");
            JSONObject jbResult = eapWxjdEapInterfFunc.PanelReadReport(station_code, plcPlcWorkPortIndexValue,
                    panel_barcode, pallet_num, ccd_code,panel_index);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                throw new Exception(message);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
