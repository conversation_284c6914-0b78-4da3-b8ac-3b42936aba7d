package com.api.pack.core.board;

import com.api.base.Const;
import com.api.base.IMongoBasicService;
import com.api.pack.core.sort.SortCompareResultEvent;
import com.api.pack.core.sort.SortCompareResult;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class SETBoardService extends IMongoBasicService<SETBoard, SETBoardRepository>
{
    private final MongoTemplate mongoTemplate;

    private final PCSBoardRepository pcsBoardRepository;

    public SETBoardService(SETBoardRepository repository, MongoTemplate mongoTemplate, PCSBoardRepository pcsBoardRepository)
    {
        super(repository);
        this.mongoTemplate = mongoTemplate;
        this.pcsBoardRepository = pcsBoardRepository;
    }

    @EventListener
    public void handleSortCompareResultEvent(SortCompareResultEvent event)
    {
        SortCompareResult compareResult = event.getSource();
        if (compareResult == null || compareResult.getSetBoard() == null)
        {
            return;
        }
        SETBoard setBoard = compareResult.getSetBoard();
        this.save(setBoard);
    }

    @Transactional(readOnly = true)
    public long countAllByPileBarcodeAndBoardStatus(String pileBarcode, String boardStatus, String flagKey, String flagValue)
    {
        Query query = new Query();
        query.addCriteria(Criteria.where("pile_barcode").is(pileBarcode));
        query.addCriteria(Criteria.where("array_status").is(boardStatus));
        if (!ObjectUtils.isEmpty(flagKey) && !ObjectUtils.isEmpty(flagValue))
        {
            query.addCriteria(Criteria.where(flagKey).is(flagValue));
        }
        query.addCriteria(Criteria.where(Const.PROPERTY_ENABLE_FLAG).is(Const.FLAG_Y));
        return this.mongoTemplate.count(query, SETBoard.class);
    }

    @Transactional(readOnly = true)
    public SETBoard findLatestOneByPileBarcodeAndBoardStatus(String pileBarcode, String boardStatus, String flagKey, String flagValue)
    {
        Query query = new Query();
        query.addCriteria(Criteria.where("pile_barcode").is(pileBarcode));
        query.addCriteria(Criteria.where("array_status").is(boardStatus));
        if (!ObjectUtils.isEmpty(flagKey) && !ObjectUtils.isEmpty(flagValue))
        {
            query.addCriteria(Criteria.where(flagKey).is(flagValue));
        }
        query.addCriteria(Criteria.where(Const.PROPERTY_ENABLE_FLAG).is(Const.FLAG_Y));
        query.with(Sort.by(Sort.Direction.DESC, Const.PROPERTY_ITEM_DATE_VAL));
        SETBoard set = this.mongoTemplate.findOne(query, SETBoard.class);
        if (set != null)
        {
            List<PCSBoard> pcs = PCSBoard.listByParentBoardId(set.getBoardId(), pcsBoardRepository);
            set.setChildrenBoards(pcs);
        }
        return set;
    }

    @Transactional(readOnly = true)
    public List<SETBoard> findAllByPileBarcodeAndBoardStatus(String pileBarcode, String boardStatus)
    {
        Query query = new Query();
        query.addCriteria(Criteria.where("pile_barcode").is(pileBarcode));
        query.addCriteria(Criteria.where("array_status").is(boardStatus));
        query.addCriteria(Criteria.where("enable_flag").is(Const.FLAG_Y));
        query.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
        List<SETBoard> sets = this.mongoTemplate.find(query, SETBoard.class);
        for (SETBoard set : sets)
        {
            List<PCSBoard> pcs = PCSBoard.listByParentBoardId(set.getBoardId(), pcsBoardRepository);
            set.setChildrenBoards(pcs);
        }
        return sets;
    }

    @Transactional(readOnly = true)
    public SETBoard findOneByBoardBarcodeAndBoardStatusAndUnbindFlagAndPileUseFlag(String boardBarcode, String boardStatus, String unbindFlag, String pileUseFlag)
    {
        Query query = new Query();
        query.addCriteria(Criteria.where("array_barcode").is(boardBarcode));
        query.addCriteria(Criteria.where("array_status").is(boardStatus));
        if (!ObjectUtils.isEmpty(unbindFlag)) query.addCriteria(Criteria.where("unbind_flag").is(unbindFlag));
        if (!ObjectUtils.isEmpty(pileUseFlag)) query.addCriteria(Criteria.where("pile_use_flag").is(pileUseFlag));
        query.addCriteria(Criteria.where(Const.PROPERTY_ENABLE_FLAG).is(Const.FLAG_Y));
        query.with(Sort.by(Sort.Direction.DESC, Const.PROPERTY_ITEM_DATE_VAL));
        return this.mongoTemplate.findOne(query, SETBoard.class);
    }

    @Transactional(readOnly = true)
    public SETBoard findOneByBoardBarcodeAndBoardStatus(String boardBarcode, String boardStatus)
    {
        return this.findOneByBoardBarcodeAndBoardStatusAndUnbindFlagAndPileUseFlag(boardBarcode, boardStatus, null, null);
    }

    @Transactional(readOnly = true)
    public SETBoard findOneByBoardIdAndBoardStatus(String boardId, String boardStatus)
    {
        Query query = new Query();
        query.addCriteria(Criteria.where("array_id").is(boardId));
        query.addCriteria(Criteria.where("array_status").is(boardStatus));
        query.addCriteria(Criteria.where(Const.PROPERTY_ENABLE_FLAG).is(Const.FLAG_Y));
        return this.mongoTemplate.findOne(query, SETBoard.class);
    }

    public void unbindByBoardBarcode(String boardCode)
    {
        Query query = new Query();
        query.addCriteria(Criteria.where("array_barcode").is(boardCode));
        query.addCriteria(Criteria.where(Const.PROPERTY_ENABLE_FLAG).is(BoardConst.FLAG_Y));
        Update update = new Update();
        update.set("unbind_flag", BoardConst.FLAG_Y);
        update.set("pile_use_flag", BoardConst.FLAG_N);
        update.set(Const.PROPERTY_ENABLE_FLAG, BoardConst.FLAG_N);
        mongoTemplate.updateMulti(query, update, mongoTemplate.getCollectionName(SETBoard.class));
    }
}
