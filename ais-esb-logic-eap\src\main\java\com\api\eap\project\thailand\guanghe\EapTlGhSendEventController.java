package com.api.eap.project.thailand.guanghe;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * (泰国广合)投收扳机标准发送事件接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/guanghe/interf/send")
public class EapTlGhSendEventController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
}
