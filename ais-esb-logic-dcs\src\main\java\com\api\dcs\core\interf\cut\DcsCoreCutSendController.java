package com.api.dcs.core.interf.cut;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;

/**
 * <p>
 * DCS切割发送流程数据定义接口
 * 1.下发切割任务到切割机
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/interf/cut/send")
public class DcsCoreCutSendController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsCoreCutSendFunc dcsCoreCutSendFunc;

    //1.下发切割任务到切割机
    @RequestMapping(value = "/DcsCoreCutSendTask", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreCutSendTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/interf/cut/send/DcsCoreCutSendTask";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        String apsTaskTable="b_dcs_aps_task";
        try{
            String station_code=jsonParas.getString("station_code");
            String mo_id=jsonParas.getString("mo_id");

            //1.根据任务ID获取任务信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(!iteratorBigData.hasNext()){
                errorMsg="未能根据任务ID{"+mo_id+"}查找到中控任务信息,该任务是否被人为删除";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Document docItemBigData = iteratorBigData.next();
            String task_num=docItemBigData.getString("task_num");
            String model_type=docItemBigData.getString("model_type");
            Double m_length=docItemBigData.getDouble("m_length");
            Double m_width=docItemBigData.getDouble("m_width");
            Double m_height=docItemBigData.getDouble("m_height");
            String m_texture=docItemBigData.getString("m_texture");
            String cut_texture=docItemBigData.getString("cut_texture");
            Double npa_startx=docItemBigData.getDouble("npa_startx");
            Double npa_starty=docItemBigData.getDouble("npa_starty");
            String cut_way=docItemBigData.getString("cut_way");
            Double cut_speed=docItemBigData.getDouble("cut_speed");
            Double cut_ampere=docItemBigData.getDouble("cut_ampere");
            Double cut_offset=docItemBigData.getDouble("cut_offset");
            String nc_name=docItemBigData.getString("nc_name");
            String nc_url=docItemBigData.getString("nc_url");
            String cut_code=docItemBigData.getString("cut_code");
            String cut_type=docItemBigData.getString("cut_type");
            Integer cut_plan_minutes=docItemBigData.getInteger("cut_plan_minutes");
            String nc_content="";
            iteratorBigData.close();

            //2.验证工位号与切割机工位号是否一致
            if(!station_code.equals(cut_code)){
                errorMsg="任务{"+task_num+"}分配的切割机工位号{"+cut_code+"}与传输工位号{"+station_code+"}不一致";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //3.根据NC文件URL获取文件内容
            if(nc_name.equals("") || nc_url.equals("")){
                errorMsg="任务{"+task_num+"}未上传NC文件信息";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //4.从URL读取到文本
            URL url = new URL(nc_url);
            BufferedReader reader = new BufferedReader(new InputStreamReader(url.openStream()));
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line).append(System.lineSeparator());
            }
            reader.close();
            nc_content=sb.toString();
            if(nc_content==null || nc_content.equals("")){
                errorMsg="任务{"+task_num+"},NC文件URL{"+nc_url+"}读取文件内容为空";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //modified by chenru 2023-03-27 文件内容太大，不通过接口传递了，直接让切割机系统去共享目录取
            dcsCoreCutSendFunc.CutSendTask(station_code,mo_id,task_num,model_type,
                    m_length,m_width,m_height,m_texture,cut_texture,npa_startx,npa_starty,
                    cut_way,cut_speed,cut_ampere,cut_offset,nc_name,"",cut_plan_minutes);

            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "下发切割机任务到切割机异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
