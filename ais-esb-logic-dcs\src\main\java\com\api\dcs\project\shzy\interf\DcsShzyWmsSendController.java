package com.api.dcs.project.shzy.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 中冶WMS发送流程数据定义接口
 * 1.通知执行分拣
 * 2.通知中控上料完成
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/shzy/interf/wms/send")
public class DcsShzyWmsSendController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsShzyWmsSendFunc dcsShzyWmsSendFunc;
    @Resource
    private CFuncLogInterf cFuncLogInterf;

    //1.通知执行分拣
    @RequestMapping(value = "/DcsShzyWmsSendFjExecuteTask", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsShzyWmsSendFjExecuteTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/shzy/interf/wms/send/DcsShzyWmsSendFjExecuteTask";
        String transResult = "";
        String errorMsg = "";
        String wmsFjTaskTable = "b_dcs_wms_fj_task";
        try {
            String task_id = jsonParas.getString("task_id");
            String task_num= jsonParas.getString("task_num");
            //1.根据任务获取任务信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("fj_task_id").is(task_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(wmsFjTaskTable).
                    find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                    noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                errorMsg = "未能根据任务ID{" + task_id + "}查找到分拣入库任务信息,该任务是否被人为删除";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Document docItemBigData = iteratorBigData.next();
            Integer position_x = docItemBigData.getInteger("position_x");
            Integer position_y = docItemBigData.getInteger("position_y");
            Integer position_z = docItemBigData.getInteger("position_z");
            String stock_code = docItemBigData.getString("stock_code");
            iteratorBigData.close();

            dcsShzyWmsSendFunc.SendFjExecuteTask(task_num, position_x, position_y, position_z,stock_code);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "通知执行分拣入库异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.通知中控上料完成
    @RequestMapping(value = "/DcsShzyWmsSendZkFeedFinish", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsShzyWmsSendZkFeedFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/shzy/interf/wms/send/DcsShzyWmsSendZkFeedFinish";
        String transResult = "";
        String errorMsg = "";
        try {
            String task_num= jsonParas.getString("task_num");
            String status= jsonParas.getString("status");
            String status_msg= jsonParas.getString("status_msg");
            dcsShzyWmsSendFunc.SendZkFeedFinish(task_num,status,status_msg);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "通知中控上料完成异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
