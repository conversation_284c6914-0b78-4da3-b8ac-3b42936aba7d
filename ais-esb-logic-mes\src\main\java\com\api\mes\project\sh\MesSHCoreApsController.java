package com.api.mes.project.sh;

import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.Cache;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/mes/project/sh")
public class MesSHCoreApsController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Qualifier("jetCache")
    @Resource
    private Cache cache;

    //双环更新自动下线数量
    @Transactional
    @RequestMapping(value = "/MesSHCoreApsMoUpdateOfflineCount", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesSHCoreApsMoUpdateOfflineCount(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/sh/MesSHCoreApsMoUpdateOfflineCount";
        String transResult = "";
        String errorMsg = "";
        try {
            String prod_line_id = jsonParas.getString("prod_line_id");
            String station_code = "RAL01-OP010-2-A";
            String make_order = "";
//            String serial_num = jsonParas.getString("serial_num");
            String sqlmake_order = "select make_order from c_mes_aps_station_mo";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlmake_order, false, request, apiRoutePath);
            make_order = itemList.get(0).get("make_order").toString();
            //订单类型：WAIT_PUBLISH(待发布与验证)、LINE_UP(排队)、CARRY_ON(进行)、COMPLETE(完成)、HAND_UP(挂起)、SHUT_DOWN(关闭)
            String mo_id = "";
            Integer mo_plan_count = 0;//计划数量
            Integer mo_offline_count = 0;//实际下线数量
            String mo_status = "";
            String sqlMo = "select mo_id," + "mo_plan_count," + "mo_offline_count," + "mo_status " + "from c_mes_aps_plan_mo " + "where prod_line_id=" + prod_line_id + " " + "and make_order='" + make_order + "' " + "and mo_plan_count>mo_offline_count " + "and enable_flag='Y' " + "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, false, request, apiRoutePath);
            if (itemListMo != null && itemListMo.size() > 0) {
                mo_id = itemListMo.get(0).get("mo_id").toString();
                mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
                mo_offline_count = Integer.parseInt(itemListMo.get(0).get("mo_offline_count").toString());
                mo_status = itemListMo.get(0).get("mo_status").toString();

                if(mo_offline_count+1>=mo_plan_count){
                    mo_status="SHUT_DOWN";
                }

                String sqlUpdate = "update c_mes_aps_plan_mo set " + "mo_offline_count=mo_offline_count+1," + "mo_status='" + mo_status + "' " + "where mo_id=" + mo_id;
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdate, false, request, apiRoutePath);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, mo_id, "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "更新自动下线数量异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
