package com.api.dcs.core.interf.fj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * DCS分拣发送流程数据定义接口
 * 1.发送分拣任务
 * 2.通知工位开始分拣
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/interf/fj/send")
public class DcsCoreFjSendController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsCoreFjSendFunc dcsCoreFjSendFunc;
    @Resource
    private CFuncLogInterf cFuncLogInterf;

    //1.发送分拣任务
    @RequestMapping(value = "/DcsCoreFjSendTask", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreFjSendTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/interf/fj/send/DcsCoreFjSendTask";
        String transResult = "";
        String errorMsg = "";
        String userID = "AIS";
        String apsTaskTable = "b_dcs_aps_task";
        try {
            String mo_id_list = jsonParas.getString("mo_id_list");
            String[] lstMoIds = mo_id_list.split(",", -1);
            if (lstMoIds == null || lstMoIds.length <= 0) {
                errorMsg = "传递mo_id集合不能为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            JSONArray task_list = new JSONArray();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").in(lstMoIds));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                JSONObject jbItem = new JSONObject();
                String mo_id = docItemBigData.getString("mo_id");
                String task_num = docItemBigData.getString("task_num");
                String model_type = docItemBigData.getString("model_type");
                String material_code = docItemBigData.getString("material_code");
                String material_des = docItemBigData.getString("material_des");
                String material_draw = docItemBigData.getString("material_draw");
                String m_texture = docItemBigData.getString("m_texture");
                Double m_length = docItemBigData.getDouble("m_length");
                Double m_width = docItemBigData.getDouble("m_width");
                Double m_height = docItemBigData.getDouble("m_height");
                Double m_weight = docItemBigData.getDouble("m_weight");
                String dxf_name = docItemBigData.getString("dxf_name");
                String dxf_url = docItemBigData.getString("dxf_url");
                String json_name = docItemBigData.getString("json_name");
                String json_url = docItemBigData.getString("json_url");
                jbItem.put("task_no", task_num);
                jbItem.put("draw_code", material_draw);
                jbItem.put("steel_model", model_type);
                jbItem.put("steel_name", material_des);
                jbItem.put("steel_texture", m_texture);
                jbItem.put("length", m_length);
                jbItem.put("width", m_width);
                jbItem.put("thickness", m_height);
                jbItem.put("weight", m_weight);
                jbItem.put("file_name", dxf_name);
                jbItem.put("file_url", dxf_url);
                jbItem.put("json_name", json_name);
                jbItem.put("json_url", json_url);
                //判断文件是否存在
                if ((dxf_url == null || dxf_url.equals("")) && (json_url == null || json_url.equals(""))) {
                    iteratorBigData.close();
                    errorMsg = "任务{" + task_num + "}未上传DXF文件或者JSON文件";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                task_list.add(jbItem);
            }

            if (task_list.size() <= 0) {
                errorMsg = "传递mo_id集合未找到任务列表,请确认传递的mo_id_list是否正确";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //先将消息更新
            Update updateBigData = new Update();
            updateBigData.set("task_msg", "");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsTaskTable);

            dcsCoreFjSendFunc.FjSendTask(userID, mo_id_list, task_list);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "通知工位开始分拣发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.通知工位开始分拣
    @RequestMapping(value = "/DcsCoreFjSendTaskStart", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreFjSendTaskStart(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/interf/fj/send/DcsCoreFjSendTaskStart";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String apsTaskTable = "b_dcs_aps_task";
        try {
            String station_code = jsonParas.getString("station_code");
            String mo_id = jsonParas.getString("mo_id");

            //1.根据任务ID获取任务信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                errorMsg = "未能根据任务ID{" + mo_id + "}查找到中控任务信息,该任务是否被人为删除";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Document docItemBigData = iteratorBigData.next();
            String task_num = docItemBigData.getString("task_num");
            String cut_type = docItemBigData.getString("cut_type");
            iteratorBigData.close();

            //LASER(激光)|PLASMA(等离子)
//            String is_frame = "N";
//            if (cut_type.toUpperCase().equals("LASER")) is_frame = "Y";
            //徐工修改默认字段is_frame等于Y，米追飞说不需要做LASER的判断
            String is_frame = "Y";

            dcsCoreFjSendFunc.FjSendTaskStart(station_code, mo_id, task_num, is_frame);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "通知工位开始分拣发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


    /**
     * 接口描述：切割分拣完成报工；
     * 报工接口 在G15工位的流程图增加一个步骤 生产任务结束报工MOM
     * task_num任务号 读取到当前工位的任务传入接口
     * cut_count切割总数量 为当前任务解析的数量
     * real_weight钢板重量 根据当前任务号查询b_dcs_aps_task表是哪个钢板型号 根据钢板型号去查询
     * b_dcs_fmod_model表得到m_weight重量传入到这个字段；
     * 根据任务号去b_dcs_aps_task表查询 task_num=’task_num’ 得到mo_id
     * 根据mo_id去查询b_dcs_aps_task_resolve分拣解析表 mo_id=‘mo_id’得到总数量count 传入对应的数量数字到cut_count
     * part_material_num 零件物料号 根据任务号去b_dcs_aps_task表查询 task_num=’task_num’
     * 得到mo_id 再去查询b_dcs_me_sort_result分拣结果表的material_num字段 根据material_num进行group分组一下看这个任务下面有
     * 几个不一样的物料号 每一个物料号下面有几个零件 就是零件数量 然后遍历传入list里面的sort_num
     *
     * @param jsonParas
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/DcsCoreSortingCompletedReportMom", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreSortingCompletedReportMom(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/dcs/core/interf/fj/send/DcsCoreSortingCompletedReportMom";
        String transResult = "";
        String errorMsg = "";
        String apsTaskTable = "b_dcs_aps_task";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = new JSONObject();
        JSONObject response = new JSONObject();
        try {
            String mo_id = jsonParas.getString("mo_id");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            queryBigData.addCriteria(Criteria.where("bg_flag").is("Y"));
            Document one = mongoTemplate.findOne(queryBigData, Document.class, apsTaskTable);
            String task_num = one.getString("task_num");
            String material_code = one.getString("material_code");

            String sqlInterf = "select m_weight,material_code  from b_dcs_fmod_model where material_code='" + material_code + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (CollectionUtils.isEmpty(itemList)) {
                return CFuncUtilsLayUiResut.GetStandJson(true, null, null, "", 0);
            }
            Map<String, Object> item = itemList.stream().findFirst().get();
            String real_weight = item.get("m_weight").toString();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            long cut_count = mongoTemplate.getCollection("b_dcs_aps_task_resolve").countDocuments(queryBigData.getQueryObject());
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            queryBigData.addCriteria(Criteria.where("material_num").exists(true));
            MongoCursor<Document> sortResults = mongoTemplate.getCollection("b_dcs_me_sort_result").find(queryBigData.getQueryObject(), Document.class).cursor();
            List<Document> documents = new ArrayList<Document>();
            while (sortResults.hasNext()) {
                documents.add(sortResults.next());
            }
            JSONArray resolveArray = new JSONArray();
            Map<Integer, List<Document>> groupByMaterialNum = documents.stream().collect(Collectors.groupingBy(g -> g.getInteger("material_num")));
            Set<Integer> keys = groupByMaterialNum.keySet();
            for (Integer key : keys) {
                JSONObject resolveJson = new JSONObject();
                resolveJson.put("part_material_num", key);
                resolveJson.put("sort_num", groupByMaterialNum.get(key).size());
                resolveArray.add(resolveJson);
            }
            String response_time = CFuncUtilsSystem.GetNowDateTime("");
            String request_uuid = CFuncUtilsSystem.GetOnlySign("");
            jbResult.put("request_uuid", request_uuid);
            jbResult.put("request_time", response_time);
            jbResult.put("task_num", task_num);
            jbResult.put("cut_count", cut_count);
            jbResult.put("real_weight", real_weight);
            jbResult.put("sort_report_list", resolveArray);
            response = dcsCoreFjSendFunc.SortingCompletedReportMom(jbResult);
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            Integer code = response.getInteger("code");
            cFuncLogInterf.Insert(startDate, endDate, "DcsCoreSortingCompletedReportMom", true, null, jbResult.toJSONString(), response.toJSONString(), Integer.valueOf(0).equals(code) ? true : false, response.getString("msg"), null);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, null, "", 0);
        } catch (Exception ex) {
            errorMsg = "报工出现未知错误：" + ex.getMessage();
            log.error(errorMsg);
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            cFuncLogInterf.Insert(startDate, endDate, "DcsCoreSortingCompletedReportMom", true, null, jbResult.toJSONString(), response.toJSONString(), false, errorMsg, null);
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
