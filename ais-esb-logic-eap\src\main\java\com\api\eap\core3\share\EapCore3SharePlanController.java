package com.api.eap.core3.share;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * AIS3.0 生产计划共用对外API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-7-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/core3/share")
public class EapCore3SharePlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //取消端口任务
    @RequestMapping(value = "/EapCore3SharePlanPortCancel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore3SharePlanPortCancel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core3/share/EapCore3SharePlanPortCancel";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");

            //更改母批状态
            String[] group_lot_status_list = new String[]{"WAIT","PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "CANCEL");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            //更改子批状态
            String[] lot_status = new String[]{"WAIT","PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status));
            updateBigData = new Update();
            updateBigData.set("lot_status", "CANCEL");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("task_error_code", 5);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "取消端口任务异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
