package com.api.eap.project.zhcy;

import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;

import lombok.extern.slf4j.Slf4j;

/**
 * 志圣-珠海超毅-EAP发送接口功能实现
 * <AUTHOR>
 */
@Service
@Slf4j
public class EapZhcySendInterfFunc {

    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    // EapZhcyInterfCommon is used statically

    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    @Autowired
    private org.springframework.data.mongodb.core.MongoTemplate mongoTemplate;

    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    @Autowired
    private com.api.common.log.CFuncLogInterf cFuncLogInterf;

    // MongoDB集合名称常量在其他方法中使用

    //处理所有功能
    public JSONObject processFunc(JSONObject jsonParas, String funcName,HttpServletRequest request,String apiRoutePath) {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = funcName;
        String requestParas = jsonParas.toString();
        String code="0000";
        JSONObject jbResponse = null;
        String requestId = jsonParas.getString("RequestId");
        if(requestId==null || "".equals(requestId)) {
        	requestId = CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmssSSS");
        	jsonParas.put("RequestId", requestId);
        }
        try {
        	jbResult.put("isSaveFlag", true);
            jbResult.put("requestParas", requestParas);
            // 查询接口配置
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url,"
            		+ "COALESCE(esb_dev_intef_url,'') esb_dev_intef_url," +
                    "COALESCE(esb_test_result,'') esb_test_result from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                //throw new Exception(errorMsg);
                return EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, requestParas);
            }

            //全部的事件调用，都有工站id。先用工站id，查询出工站的CODE。
            String stationId = jsonParas.getString("stationId");
            if (stationId == null || "".equals(stationId)) {
                errorMsg = "stationId不能为空";
                return EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, requestParas);
            }
            //1.查询工位信息
            String sqlStation = "select station_code,COALESCE(station_attr,'') station_attr,pc_host  from sys_fmod_station where station_id=" + stationId + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("-1", sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size()<=0) {
                errorMsg = "stationId错误："+stationId;
                return EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, requestParas);
            }
            String eqpId = itemListStation.get(0).get("station_code").toString();
            String pcHost = itemListStation.get(0).get("pc_host").toString();

            //频率很高的单独方法处设置为false
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            //测试地址是否配置
            String esb_dev_intef_url = itemList.get(0).get("esb_dev_intef_url").toString();
            // 检查是否有测试数据
            String testResult = itemList.get(0).get("esb_test_result").toString();
            //否则继续按正常逻辑执行。
            String esbProdIntefUrl = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esbProdIntefUrl == null || esbProdIntefUrl.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                //throw new Exception(errorMsg);
                return EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, requestParas);
            }
            // 检查是否使用测试数据
            boolean useTestData = StringUtils.startsWithIgnoreCase(esb_dev_intef_url, "Y") && (testResult != null && !testResult.isEmpty());

            // 根据功能名调用不同方法
            switch (funcName) {
                case "EQP_AliveCheck": // 连线检查
                    jbResponse = processEQPAliveCheck(jsonParas, request, apiRoutePath, esbProdIntefUrl, eqpId, pcHost, useTestData, testResult, esbInterfCode);
                    break;
                case "EQP_EquipmentCurrentStatus": //设备状态上报
                    jbResponse = processEQPEquipmentCurrentStatus(jsonParas, request, apiRoutePath, esbProdIntefUrl, eqpId, useTestData, testResult, esbInterfCode);
                    break;
                case "EQP_EquipmentControlMode": //设备控制模式： Local ：离线本地模式 Remote ：在线远程模式
                    jbResponse = processEQPEquipmentControlMode(jsonParas, request, apiRoutePath, esbProdIntefUrl, eqpId, useTestData, testResult, esbInterfCode);
                    break;
                case "EQP_EquipmentAlarmReport": // 设备报警上报
                    jbResponse = processEQPEquipmentAlarmReport(jsonParas, request, apiRoutePath, esbProdIntefUrl, eqpId, useTestData, testResult, esbInterfCode);
                    break;
                case "EQP_EquipmentJobDataProcessReport": // 设备任务进展信息上报
                    jbResponse = processEQPEquipmentJobDataProcessReport(jsonParas, request, apiRoutePath, esbProdIntefUrl, eqpId, useTestData, testResult, esbInterfCode);
                    break;
                case "EQP_ProcessDataReport": // 设备制程/量测数据报告
                    jbResponse = processEQPProcessDataReport(jsonParas, request, apiRoutePath, esbProdIntefUrl, eqpId, useTestData, testResult, esbInterfCode);
                    break;
                case "EQP_QRCodeReadReport": // 扫码枪扫描二维码信息上报
                    jbResponse = processEQPQRCodeReadReport(jsonParas, request, apiRoutePath, esbProdIntefUrl, eqpId, useTestData, testResult, esbInterfCode);
                    break;
                case "EQP_IcCodeReport": // 设备请求身份信息
                    jbResponse = processEQPIcCodeReport(jsonParas, request, apiRoutePath, esbProdIntefUrl, eqpId, useTestData, testResult, esbInterfCode);
                    break;
                default: // 不支持的功能名称处理
                    errorMsg = "不支持的接口功能: " + funcName;
                    code="9999";
                    jbResponse = EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, requestParas);
            }
            jbResult.put("requestParas", jbResponse.getString("requestParas"));
            jbResult.put("responseParas", jbResponse.toString());
            jbResult.put("Success", jbResponse.getBoolean("Success"));
            jbResult.put("Msg", jbResponse.getString("Msg"));
            String responseCode = jbResponse.getString("Code");
            if(StringUtils.hasText(responseCode)) {
               code = responseCode;
            }
            jbResult.put("Code", code);
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常: " + ex.getMessage();
            log.error("接口处理异常: {}", errorMsg, ex);
            jbResponse = EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, requestParas);
            jbResult.put("responseParas", jbResponse.toString());
            jbResult.put("Success", false);
            jbResult.put("Msg", errorMsg);
            jbResult.put("Code", "9999");
        }
        return jbResult;
    }

    /**
     * 连线检查
     * 处理EQP_AliveCheck请求，检查设备连接状态
     *
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbProdIntefUrl 接口URL
     * @param eqpId 设备ID
     * @param iPAddress IP地址
     * @param useTestData 是否使用测试数据
     * @param testResult 测试数据结果
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEQPAliveCheck(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath,
            String esbProdIntefUrl, String eqpId, String iPAddress, boolean useTestData, String testResult, String esbInterfCode) {
        String ipAddress = jsonParas.getString("IPAddress");
        String requestId = jsonParas.getString("RequestId");
        log.info("连线检查: EqpId ={}, IPAddress={}", eqpId, ipAddress);
        try {
            //通过工站code，查询PLC结尾的client_code。
            String sqlClientCode = "SELECT client_code from  scada_client where station_code='" + eqpId + "' and client_code like '%Ais' and enable_flag='Y'";
            List<Map<String, Object>> lstsqlClientCode = cFuncDbSqlExecute.ExecSelectSql(eqpId, sqlClientCode, false, request, apiRoutePath);
            String client_code = lstsqlClientCode.get(0).get("client_code").toString();

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("EqpId", eqpId);
            if(ipAddress==null) {
            	ipAddress= cFuncDbSqlResolve.GetParameterValue("LOCAL_IP");
            }
            requestBody.put("IPAddress", ipAddress);
            JSONObject paraJsonObject = EapZhcyInterfCommon.CreateHeader(esbInterfCode);
            paraJsonObject.put("Content", requestBody);

            //先设置//离线
            cFuncUtilsCellScada.WriteTagByStation("ais", eqpId, client_code+"/AisStatus/EapOnOffLine", "0", true);

            // 获取响应结果
            JSONObject resObj = getResponseObject(esbProdIntefUrl, paraJsonObject, useTestData, testResult, esbInterfCode, request, apiRoutePath);

            //如果返回正常就设置为1
            if(resObj!=null && resObj.getBoolean("Success")) {
            	//在线
            	cFuncUtilsCellScada.WriteTagByStation("ais", eqpId, client_code+"/AisStatus/EapOnOffLine", "1", true);
            }
            return resObj;
        } catch (Exception ex) {
            String errorMsg = "连线检查异常: " + ex.getMessage();
            log.error("连线检查异常: EqpId ={}, 错误信息={}", eqpId, errorMsg, ex);
            return EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
        }
    }

    /**
     * 获取响应对象，处理测试数据和正常接口调用的通用方法
     *
     * @param esbProdIntefUrl 接口URL
     * @param paraJsonObject 请求参数
     * @param useTestData 是否使用测试数据
     * @param testResult 测试数据结果
     * @param esbInterfCode 接口代码
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @return 响应结果JSON对象
     * @throws Exception 异常情况
     */
    public JSONObject getResponseObject(String esbProdIntefUrl, JSONObject paraJsonObject, boolean useTestData,String testResult, String esbInterfCode, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject resObj;
        if (useTestData) {
            log.info("{}使用测试数据返回: {}", esbInterfCode, testResult);
            resObj = JSONObject.parseObject(testResult);
            resObj.put("Msg", "使用数据库配置测试数据返回");
        } else {
            // 正常调用接口
            resObj = PostJbBackJb(esbProdIntefUrl, paraJsonObject, esbInterfCode, request, apiRoutePath);
        }
        return resObj;
    }
    /**
     * 设备状态上报
     * 处理EQP_EquipmentCurrentStatus请求，上报设备当前状态
     * 设备状态：
     *   Run： 运行
     *   Pause： 暂停
     *   Idle： 待机
     *   Down：故障
     *   PM： 保养
     *   Ready： 准备
     *
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbProdIntefUrl 接口URL
     * @param eqpId 设备ID
     * @param useTestData 是否使用测试数据
     * @param testResult 测试数据结果
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEQPEquipmentCurrentStatus(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath,
            String esbProdIntefUrl, String eqpId, boolean useTestData, String testResult, String esbInterfCode) {
        String requestId = jsonParas.containsKey("RequestId") ? jsonParas.getString("RequestId") : "";
        String deviceStatus = jsonParas.getString("deviceStatus");
        log.info("开始更新设备状态: EqpId={}, EqpStatus={}", eqpId, deviceStatus);
        try {
            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("EqpId", eqpId);
            requestBody.put("EqpStatus", deviceStatus);
            JSONObject paraJsonObject = EapZhcyInterfCommon.CreateHeader(esbInterfCode);
            paraJsonObject.put("Content", requestBody);
            // 获取响应结果
            return getResponseObject(esbProdIntefUrl, paraJsonObject, useTestData, testResult, esbInterfCode, request, apiRoutePath);
        } catch (Exception ex) {
            log.error("更新设备状态时发生异常: EqpId ={}, 错误信息={}", eqpId, ex.getMessage(), ex);
            return EapZhcyInterfCommon.CreateFaillResult("设备状态更新失败：" + ex.getMessage(), requestId, esbInterfCode, jsonParas.toString());
        }
    }

    /**
     * 设备当前控制模式上报
     * 处理EQP_EquipmentControlMode请求，上报设备当前控制模式
     *
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbProdIntefUrl 接口URL
     * @param eqpId 设备ID
     * @param useTestData 是否使用测试数据
     * @param testResult 测试数据结果
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEQPEquipmentControlMode(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath,
            String esbProdIntefUrl, String eqpId, boolean useTestData, String testResult, String esbInterfCode) {
        String requestId = jsonParas.containsKey("RequestId") ? jsonParas.getString("RequestId") : "";
        String deviceMode = jsonParas.getString("deviceMode");
        log.info("开始更新设备状态: EqpId={}, deviceMode={}", eqpId, deviceMode);
        try {
        	//模式0:离线; 1:在线/远程;2:在线/本地、 目前把离线也归为本地。
        	String deviceModeInfo="Local";
            if("1".equals(deviceMode)) {
            	deviceModeInfo="Remote";
            }else if("2".equals(deviceMode)) {
            	deviceModeInfo="Local";
            }else {
            	//离线模式不上报，
            	return EapZhcyInterfCommon.CreateFaillResult("离线模式不上报", requestId, esbInterfCode, jsonParas.toString());
            }
            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("EqpId", eqpId);
            requestBody.put("ControlMode", deviceModeInfo);
            JSONObject paraJsonObject = EapZhcyInterfCommon.CreateHeader(esbInterfCode);
            paraJsonObject.put("Content", requestBody);
            // 获取响应结果
            return getResponseObject(esbProdIntefUrl, paraJsonObject, useTestData, testResult, esbInterfCode, request, apiRoutePath);
        } catch (Exception ex) {
            log.error("上报设备当前控制模式时发生异常: EqpId ={}, 错误信息={}", eqpId, ex.getMessage(), ex);
            return EapZhcyInterfCommon.CreateFaillResult("上报设备当前控制模式更新失败：" + ex.getMessage(), requestId, esbInterfCode, jsonParas.toString());
        }
    }

    /**
     * 设备报警上报
     * 处理EQP_EquipmentAlarmReport请求，上报设备报警信息
     *
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbProdIntefUrl 接口URL
     * @param eqpId 设备ID
     * @param useTestData 是否使用测试数据
     * @param testResult 测试数据结果
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEQPEquipmentAlarmReport(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath,
            String esbProdIntefUrl, String eqpId, boolean useTestData, String testResult, String esbInterfCode) {
    	//C# 传过来的参数
        //string paras = $"stationId={stationId}&ReportType={ReportType}&AlarmType={AlarmType}&AlarmCode={AlarmCode}&alarmText={alarmText}&alarmTime={alarmTime}";
        //警报状态：Reset:警报清除  Occur:警报发生
        String reportType = jsonParas.getString("ReportType");
        //警报类型：  S: 重大 L: 一般 W：预警
        String alarmType = jsonParas.getString("AlarmType");
        String alarmCode = jsonParas.getString("AlarmCode");
        String requestId = jsonParas.getString("RequestId");
        String alarmText = jsonParas.getString("alarmText");
        log.info("设备报警上报: eqpId ={}, ReportType={}, AlarmType={}, AlarmCode={},alarmText={}", eqpId, reportType, alarmType, alarmCode,alarmText);
        try {
	        // 构建请求参数
	        JSONObject requestBody = new JSONObject();
	        requestBody.put("EqpId", eqpId);
	        requestBody.put("ReportType", reportType);
	        requestBody.put("AlarmType", alarmType);
	        requestBody.put("AlarmCode", alarmCode);
	        JSONObject paraJsonObject = EapZhcyInterfCommon.CreateHeader(esbInterfCode);
	        paraJsonObject.put("Content", requestBody);
	        // 获取响应结果
	        return getResponseObject(esbProdIntefUrl, paraJsonObject, useTestData, testResult, esbInterfCode, request, apiRoutePath);
        } catch (Exception ex) {
            log.error("志圣设备报警上报处理逻辑时发生异常: eqpId ={}, 错误信息={}", eqpId, ex.getMessage(), ex);
            return EapZhcyInterfCommon.CreateFaillResult("志圣设备报警上报处理逻辑失败：" + ex.getMessage(), requestId, esbInterfCode, jsonParas.toString());
        }
    }

    /**
     * 设备任务进展信息上报
     * 处理EQP_EquipmentJobDataProcessReport请求，上报设备任务进展状态
     *
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbProdIntefUrl 接口URL
     * @param eqpId 设备ID
     * @param useTestData 是否使用测试数据
     * @param testResult 测试数据结果
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEQPEquipmentJobDataProcessReport(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath,
            String esbProdIntefUrl, String eqpId, boolean useTestData, String testResult, String esbInterfCode) {
        try {
        	//测试数据：{"JobId":"001","TotalCount":100,"OkCount":99,"ProcessCode":"0001"}
            //event传uploadPlanInfoJson对象。这边解析，直接上报。
            JSONObject content = jsonParas.getJSONObject("uploadPlanInfoJson");
            String jobId = content.getString("JobId");
            int totalCount = content.getIntValue("TotalCount");
            int okCount = content.getIntValue("OkCount");
            String processCode = content.getString("ProcessCode");
            log.info("任务进展上报: stationId ={}, JobId={}, ProcessCode={}, TotalCount={}, OkCount={}",eqpId, jobId, processCode, totalCount, okCount);

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("EqpId", eqpId);
            requestBody.put("JobId", jobId);
            requestBody.put("TotalCount", totalCount);
            requestBody.put("OkCount", okCount);
            requestBody.put("ProcessCode", processCode);
            JSONObject paraJsonObject = EapZhcyInterfCommon.CreateHeader(esbInterfCode);
            paraJsonObject.put("Content", requestBody);

            // 获取响应结果
            return getResponseObject(esbProdIntefUrl, paraJsonObject, useTestData, testResult, esbInterfCode, request, apiRoutePath);
        } catch (Exception ex) {
            String errorMsg = "任务进展上报异常: " + ex.getMessage();
            log.error("任务进展上报异常: {}", errorMsg, ex);
            return EapZhcyInterfCommon.CreateFaillResult(errorMsg, jsonParas.getString("RequestId"), esbInterfCode, jsonParas.toString());
        }
    }

    /**
     * 设备制程/量测数据报告
     * 处理EQP_ProcessDataReport请求，上报设备制程/量测数据
     *
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbProdIntefUrl 接口URL
     * @param eqpId 设备ID
     * @param useTestData 是否使用测试数据
     * @param testResult 测试数据结果
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEQPProcessDataReport(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath,
            String esbProdIntefUrl, String eqpId, boolean useTestData, String testResult, String esbInterfCode) {
        try {
        	JSONObject content = jsonParas.getJSONObject("content");
        	//{"PanelId":"","ProcDataList":[{"ItemValue":"","ItemName":"LotID"},{"ItemValue":"","ItemName":"PartNo"},{"ItemValue":"","ItemName":"PanelID"},{"ItemValue":"","ItemName":"RecipeCode"},{"ItemValue":"","ItemName":"DeviceCode"},{"ItemValue":"","ItemName":"DeviceID"},{"ItemValue":"","ItemName":"OpID"},{"ItemValue":"","ItemName":"OpName"},{"ItemValue":"","ItemName":"Spare1"},{"ItemValue":"0","ItemName":"Paras1"},{"ItemValue":"0","ItemName":"Paras2"},{"ItemValue":"0","ItemName":"Paras3"},{"ItemValue":"0","ItemName":"Paras4"},{"ItemValue":"0","ItemName":"Paras5"},{"ItemValue":"0","ItemName":"Paras6"},{"ItemValue":"0","ItemName":"Paras7"},{"ItemValue":"0","ItemName":"Paras8"},{"ItemValue":"0","ItemName":"Paras9"},{"ItemValue":"0","ItemName":"Paras10"},{"ItemValue":"0","ItemName":"Paras11"},{"ItemValue":"0","ItemName":"Paras12"},{"ItemValue":"0","ItemName":"Paras13"},{"ItemValue":"0","ItemName":"Paras14"},{"ItemValue":"0","ItemName":"Paras15"},{"ItemValue":"0","ItemName":"Paras16"},{"ItemValue":"0","ItemName":"Paras17"},{"ItemValue":"0","ItemName":"Paras18"},{"ItemValue":"0","ItemName":"Paras19"},{"ItemValue":"0","ItemName":"Paras20"},{"ItemValue":"0","ItemName":"Paras21"},{"ItemValue":"0","ItemName":"Paras22"}],"EqpId":"DY2024029","JobId":"","Pn":""}
        	log.info("制程数据上报: eqpId ={}, content={}", eqpId, content.toString());
            String jobId = content.containsKey("JobId") ? content.getString("JobId") : "";
            String panelId = content.containsKey("PanelId") ? content.getString("PanelId") : "";
            String pn = content.getString("Pn");
            JSONArray procDataList = content.getJSONArray("ProcDataList");

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("EqpId", eqpId);
            requestBody.put("JobId", jobId);
            requestBody.put("PanelId", panelId);
            requestBody.put("Pn", pn);
            requestBody.put("ProcDataList", procDataList);
            JSONObject paraJsonObject = EapZhcyInterfCommon.CreateHeader(esbInterfCode);
            paraJsonObject.put("Content", requestBody);

            // 获取响应结果
            return getResponseObject(esbProdIntefUrl, paraJsonObject, useTestData, testResult, esbInterfCode, request, apiRoutePath);
        } catch (Exception ex) {
            String errorMsg = "制程数据上报异常: " + ex.getMessage();
            log.error("制程数据上报异常: {}", errorMsg, ex);
            return EapZhcyInterfCommon.CreateFaillResult(errorMsg, jsonParas.getString("RequestId"), esbInterfCode, jsonParas.toString());
        }
    }

    /**
     * 扫码枪扫描二维码信息上报
     * 处理扫码上报并触发上机流程
     *
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbProdIntefUrl 接口URL
     * @param eqpId 设备ID
     * @param useTestData 是否使用测试数据
     * @param testResult 测试数据结果
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEQPQRCodeReadReport(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath,
            String esbProdIntefUrl, String eqpId, boolean useTestData, String testResult, String esbInterfCode) {
        try {
            String qrCode = jsonParas.getString("qrCode");
            log.info("二维码扫描上报: stationId ={}, QRCode={}", eqpId, qrCode);

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("EqpId", eqpId);
            requestBody.put("QRCode", qrCode);
            JSONObject paraJsonObject = EapZhcyInterfCommon.CreateHeader(esbInterfCode);
            paraJsonObject.put("Content", requestBody);

            // 获取响应结果
            return getResponseObject(esbProdIntefUrl, paraJsonObject, useTestData, testResult, esbInterfCode, request, apiRoutePath);
        } catch (Exception ex) {
            String errorMsg = "二维码扫描上报异常: " + ex.getMessage();
            log.error("二维码扫描上报异常: {}", errorMsg, ex);
            return EapZhcyInterfCommon.CreateFaillResult(errorMsg, jsonParas.getString("RequestId"), esbInterfCode, jsonParas.toString());
        }
    }

    /**
     * 设备请求身份信息
     * 处理EQP_IcCodeReport请求，获取并同步账号信息
     *
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbProdIntefUrl 接口URL
     * @param eqpId 设备ID
     * @param useTestData 是否使用测试数据
     * @param testResult 测试数据结果
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEQPIcCodeReport(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath,
            String esbProdIntefUrl, String eqpId, boolean useTestData, String testResult, String esbInterfCode) {
        try {
            log.info("身份信息请求: eqpId ={}", eqpId);

            // 构建请求参数
            JSONObject requestBody = new JSONObject();
            requestBody.put("EqpId", eqpId);
            JSONObject paraJsonObject = EapZhcyInterfCommon.CreateHeader(esbInterfCode);
            paraJsonObject.put("Content", requestBody);

            // 获取响应结果
            JSONObject res = getResponseObject(esbProdIntefUrl, paraJsonObject, useTestData, testResult, esbInterfCode, request, apiRoutePath);

            // 只保存AccountList到MongoDB的a_eap_account_list集合，先清空再插入，实现全量覆盖
            if (res != null && res.getBoolean("Success") && res.containsKey("Content")) {
                JSONObject content = res.getJSONObject("Content");
                if (content.containsKey("AccountList")) {
                    processAccountList(content.getJSONArray("AccountList"), eqpId, jsonParas.getString("RequestId"));
                }
            }
            return res;
        } catch (Exception ex) {
            String errorMsg = "账号信息同步异常: " + ex.getMessage();
            log.error("账号信息同步异常: {}", errorMsg, ex);
            return EapZhcyInterfCommon.CreateFaillResult(errorMsg, jsonParas.getString("RequestId"), esbInterfCode, jsonParas.toString());
        }
    }

    // 处理账号列表信息
    private void processAccountList(JSONArray accountList, String eqpId, String requestId) {
        try {
            // 先清空账号表
            mongoTemplate.getCollection("a_eap_account_list").deleteMany(new org.bson.Document());
            // 批量插入AccountList
            if (accountList != null && accountList.size() > 0) {
                java.util.List<org.bson.Document> docs = new java.util.ArrayList<>();
                for (int i = 0; i < accountList.size(); i++) {
                    JSONObject acc = accountList.getJSONObject(i);
                    org.bson.Document doc = org.bson.Document.parse(acc.toJSONString());
                    doc.put("eqp_id", eqpId);
                    doc.put("request_id", requestId);
                    doc.put("create_time", new java.util.Date());
                    docs.add(doc);
                }
                if (!docs.isEmpty()) {
                    mongoTemplate.getCollection("a_eap_account_list").insertMany(docs);
                }
            }
            log.info("账号信息已全量覆盖到MongoDB: eqpId={}, requestId={}", eqpId, requestId);
        } catch (Exception mongoEx) {
            log.error("保存账号信息到MongoDB异常: eqpId={}, error={}", eqpId, mongoEx.getMessage(), mongoEx);
        }
    }

    /***
     * 由于珠海超毅 MES 返回的正常和异常消息格式不一致。这里统一一下。
     * @param esbProdIntefUrl 接口URL
     * @param paraJsonObject 请求参数
     * @param esbInterfCode 接口代码，用于日志记录
     * @param request HTTP请求对象，用于日志记录
     * @param apiRoutePath API路径，用于日志记录
     * @return 响应结果
     * @throws Exception 异常情况
     */
    public JSONObject PostJbBackJb(String esbProdIntefUrl, JSONObject paraJsonObject, String esbInterfCode,HttpServletRequest request, String apiRoutePath) throws Exception {
        // 记录请求开始时间
        String startDate = com.api.common.utils.CFuncUtilsSystem.GetNowDateTime("");
        // 发送请求
        JSONObject resObj = cFuncUtilsRest.PostJbBackJb(esbProdIntefUrl, paraJsonObject);
        String resStr = resObj.toString();
        // 记录请求结束时间
        String endDate = com.api.common.utils.CFuncUtilsSystem.GetNowDateTime("");
        // 统一异常响应格式
        boolean success = true;
        String message = "成功";
        if (resObj != null && resObj.getInteger("code") != null && resObj.getInteger("code").intValue() != 0) {
            // 由于珠海超毅MES-客户- 异常信息和正常信息 返回的数据结构不一致。在这里把异常信息统一为一个格式。方便处理。
            // {"code":500,"message":"字段：IPAddress 必填"}
            // {"Msg":"成功","RequestId":"20250425131823340","Content":{},"Code":"0000","Success":true,"DateTime":"2025-04-25 13:18:30"}
            resObj.put("Success", false);
            resObj.put("Msg", resObj.get("message"));
            success = false;
            message = resObj.getString("message");
        } else if (resObj != null && resObj.containsKey("Success")) {
            success = resObj.getBoolean("Success");
            message = resObj.getString("Msg");
        }
        // 如果未提供接口代码，尝试从请求中提取
        if (esbInterfCode == null || esbInterfCode.isEmpty()) {
            if (paraJsonObject.containsKey("Message")) {
                esbInterfCode = paraJsonObject.getString("Message");
            }
        }
        // 获取令牌
        String token = "";
        // 记录接口日志
        cFuncLogInterf.Insert(
            startDate,
            endDate,
            esbInterfCode,
            true,
            token,
            paraJsonObject.toString(),
            resStr,
            success,
            message,
            request
        );
        return resObj;
    }

    /**
     * 兼容旧版本的PostJbBackJb方法，不提供日志参数
     * @param esbProdIntefUrl 接口URL
     * @param paraJsonObject 请求参数
     * @return 响应结果
     * @throws Exception 异常情况
     */
//    public JSONObject PostJbBackJb(String esbProdIntefUrl, JSONObject paraJsonObject) throws Exception {
//        return PostJbBackJb(esbProdIntefUrl, paraJsonObject, "", null, "");
//    }
}