package com.api.base;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.net.URI;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class IRestController<ID extends Serializable, VO extends Serializable, Service extends IService<VO>>
{
    protected final Service service;

    public IRestController(Service service)
    {
        this.service = service;
    }

    public <T> ResponseEntity<IResponseBody> ok()
    {
        return this.ok(IResponseBody.ok(null));
    }

    /**
     * [GET]：服务器成功返回用户请求的数据
     *
     * @param <T> data
     * @return ResponseEntity
     */
    public <T> ResponseEntity<IResponseBody> ok(T data)
    {
        return ResponseEntity.ok(IResponseBody.ok(data));
    }

    /**
     * [POST/PUT/PATCH]：用户新建或修改数据成功
     *
     * @param resourceId
     * @param <T>
     * @return
     */
    public <T> ResponseEntity<IResponseBody> created(String resourceId)
    {
        return this.created(resourceId, IResponseBody.ok(null));
    }

    public <T> ResponseEntity<IResponseBody> created(String resourceId, T data)
    {
        return ResponseEntity.created(URI.create(resourceId)).body(IResponseBody.ok(data));
    }

    public <T> ResponseEntity<IResponseBody> accepted()
    {
        return this.accepted(IResponseBody.ok(null));
    }

    public <T> ResponseEntity<IResponseBody> accepted(T data)
    {
        return ResponseEntity.accepted().body(IResponseBody.ok(data));
    }

    public <T> ResponseEntity<T> noContent()
    {
        return ResponseEntity.noContent().build();
    }

    public <T> ResponseEntity<T> notFound()
    {
        return ResponseEntity.notFound().build();
    }

    @ApiOperation("删除指定资源（指定IDs）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功（当前接口无此状态返回）"), // 200
            @ApiResponse(code = 204, message = "删除完成"), // 204
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @DeleteMapping
    public ResponseEntity<?> delete(@RequestParam List<ID> ids)
    {
        this.service.removeByIds(ids);
        return this.noContent();
    }

    @ApiOperation("删除指定资源（指定ID）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功（当前接口无此状态返回）"), // 200
            @ApiResponse(code = 204, message = "删除完成"), // 204
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteById(@PathVariable ID id)
    {
        if (!this.service.removeById(id))
        {
            return this.notFound();
        }
        return this.noContent();
    }

    @ApiOperation("获取资源列表（可选过滤条件）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"), // 200
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @GetMapping
    public ResponseEntity<?> get(@ApiParam(hidden = true) @RequestParam(required = false) Map<String, Object> parameters)
    {
        PageRequest pageRequest = this.generatePageRequestByParameters(parameters);
        if (pageRequest != null)
        {
            return this.getPage(parameters, pageRequest);
        }
        else
        {
            Sort sort = this.generateSortByParameters(parameters);
            return this.getList(parameters, sort);
        }
    }

    @ApiOperation("获取单个资源（指定ID）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"), // 200
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @GetMapping("/{id}")
    public ResponseEntity<?> getById(@PathVariable(required = false) ID id)
    {
        VO o = this.service.getById(id);
        return o != null ? this.ok(o) : this.notFound();
    }

    @ApiOperation("修改指定资源（指定ID）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "修改完成"), // 200
            @ApiResponse(code = 201, message = "修改完成"), // 201
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @PatchMapping({"", "/{id}"})
    public ResponseEntity<?> patch(@PathVariable(required = false) ID id, @RequestBody VO o)
    {
        this.service.updateById(o);
        return this.ok(o);
    }

    @ApiOperation("更替指定资源（指定ID）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "修改完成"), // 200
            @ApiResponse(code = 201, message = "修改完成"), // 201
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @PutMapping({"", "/{id}"})
    public ResponseEntity<?> put(@PathVariable(required = false) ID id, @RequestBody VO o)
    {
        if (!ObjectUtils.isEmpty(id))
        {
            if (this.service.getById(id) == null)
            {
                return this.notFound();
            }
            else
            {
                this.service.removeById(id);
            }
        }
        this.service.save(o);
        return this.ok(o);
    }

    @ApiOperation("新增资源")
    @ApiResponses({
            @ApiResponse(code = 200, message = "新增完成"), // 200
            @ApiResponse(code = 201, message = "新增完成"), // 201
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @PostMapping
    public ResponseEntity<?> post(@RequestBody VO o)
    {
        if (!this.service.save(o))
        {
            return this.notFound();
        }
        return this.ok(o);
    }

    public ResponseEntity<?> getList(Map<String, Object> parameters, Sort sort)
    {
        QueryWrapper<VO> queryWrapper = new QueryWrapper<>();
        queryWrapper.allEq(parameters);
        if (sort != null)
        {
            sort.forEach(order -> queryWrapper.orderBy(true, order.isAscending(), order.getProperty()));
        }
        return this.ok(this.service.list(queryWrapper));
    }

    public ResponseEntity<?> getPage(Map<String, Object> parameters, Pageable pageable)
    {
        QueryWrapper<VO> queryWrapper = new QueryWrapper<>();
        queryWrapper.allEq(parameters);
        Page<VO> page = new Page<>(pageable.getPageNumber(), pageable.getPageSize());
        pageable.getSort().forEach(order -> queryWrapper.orderBy(true, order.isAscending(), order.getProperty()));
        IPage<VO> result = this.service.page(page, queryWrapper);
        return this.ok(result.getRecords());
    }

    public Sort generateSortByParameters(Map<String, Object> parameters)
    {
        parameters.remove(Const.KEY_USER_NAME);
        Boolean sorted = Optional.ofNullable(parameters.get(Const.KEY_SORTED)).map(it -> {
            try
            {
                parameters.remove(Const.KEY_SORTED);
                return Boolean.parseBoolean(it.toString());
            }
            catch (NumberFormatException e)
            {
                return null;
            }
        }).orElse(null);
        if (sorted != null && !sorted)
        {
            return null;
        }
        return Optional.ofNullable(parameters.get(Const.KEY_SORT)).map(it -> {
            try
            {
                parameters.remove(Const.KEY_SORT);
                String sortString = it.toString();
                List<Sort.Order> orders = Arrays.stream(sortString.split(",")).map(oit -> {
                    String[] order = oit.split(" ");
                    String property = order[0];
                    Sort.Direction direction = null;
                    if (order.length == 2)
                    {
                        direction = Sort.Direction.fromString(order[1]);
                    }
                    return direction != null && direction.isDescending() ? Sort.Order.desc(property) : Sort.Order.asc(property);
                }).collect(Collectors.toList());
                return Sort.by(orders);
            }
            catch (NumberFormatException e)
            {
                return null;
            }
        }).orElse(null);
    }

    public PageRequest generatePageRequestByParameters(Map<String, Object> parameters)
    {
        parameters.remove(Const.KEY_USER_NAME);
        Integer page = Optional.ofNullable(parameters.get(Const.KEY_PAGE)).map(it -> {
            try
            {
                parameters.remove(Const.KEY_PAGE);
                return Integer.parseInt(it.toString());
            }
            catch (NumberFormatException e)
            {
                return null;
            }
        }).orElse(null);
        Integer size = Optional.ofNullable(parameters.get(Const.KEY_SIZE)).map(it -> {
            try
            {
                parameters.remove(Const.KEY_SIZE);
                return Integer.parseInt(it.toString());
            }
            catch (NumberFormatException e)
            {
                return null;
            }
        }).orElse(null);
        // 分页查询
        if (page != null && size != null)
        {
            Sort sort = this.generateSortByParameters(parameters);
            if (sort != null)
            {
                return PageRequest.of(page, size, sort);
            }
            return PageRequest.of(page, size);
        }
        return null;
    }
}
