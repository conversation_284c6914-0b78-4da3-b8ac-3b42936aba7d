package com.api.eap.project.wxjd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.dy.p1.wcf.AlarmReportRequestBody;
import com.api.eap.project.dy.p1.wcf.DyServiceSoap;
import com.api.eap.project.dy.p1.wcf.RequestHead;
import com.api.eap.project.thailand.guanghe.EapTlGhInterfCommon;
import com.api.eap.project.thailand.guanghe.EapTlGhSendFlowSubFunc;
import com.api.eap.project.wxjd.wcf.CodeReaderInfoWebserviceService;
import com.api.eap.project.wxjd.wcf.CodeReaderInfoWebserviceServiceLocator;
import com.api.eap.project.wxjd.wcf.CodeReaderInfoWebserviceSoapBindingStub;
import lombok.extern.slf4j.Slf4j;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.util.GregorianCalendar;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 1.[接口]板件读码报告
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class EapWxjdEapInterfFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private EapTlGhInterfCommon eapTlGhInterfCommon;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.[接口]板件读码报告
    public JSONObject PanelReadReport(String station_code, String plcPlcWorkPortIndexValue,
                                      String panel_barcode, String pallet_num,
                                      String ccd_code, Integer panel_index) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "PanelReadReport";
        String esbInterfCode = "PanelReadReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        String station_id = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            //投料工位端口号
            String port_code=plcPlcWorkPortIndexValue;
            String sqlPort="select port_code " +
                    "from   a_eap_fmod_station_port a " +
                    "JOIN   sys_fmod_station s on a.station_id=s.station_id " +
                    "where station_code ='"+station_code+"' and port_index="+plcPlcWorkPortIndexValue+"";
            List<Map<String, Object>> lstPort2=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlPort,false,null,"");
            if(lstPort2!=null && lstPort2.size()>0){
                port_code=lstPort2.get(0).get("port_code").toString();
            }
            //1.创建参数
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject postParas = new JSONObject(new LinkedHashMap<>());
            postParas.put("traceEqpCode", station_code);//设备编号
            postParas.put("traceDate", startDate);//读码时间
            postParas.put("traceType", "0");//读码类型
            postParas.put("goodsSn", panel_barcode);//板件条码
            postParas.put("picCode", "1");//CCD编号
            postParas.put("axleCode", "");//临时码
            postParas.put("drillingCode", port_code);//投料工位
            postParas.put("picDrilling", pallet_num);//载具码
            postParas.put("countVal", panel_index);//板件序号
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            CodeReaderInfoWebserviceService service=new CodeReaderInfoWebserviceServiceLocator(esb_prod_intef_url);
            CodeReaderInfoWebserviceSoapBindingStub binding=(CodeReaderInfoWebserviceSoapBindingStub)service.getcodeReaderInfoWebservice();
            responseParas= binding.codeReaderInfo(requestParas);
            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }


}
