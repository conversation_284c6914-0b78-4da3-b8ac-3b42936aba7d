package com.api.config;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

/**
 * <AUTHOR>
 * @description swagger配置类
 * @createDate 2023/4/27 10:13
 */
@Configuration
@EnableSwagger2
@Slf4j
public class Swagger2Config {
    @Bean
    public Docket accessToken() {
        log.info("【初始化SWAGGER】");
        return new Docket(DocumentationType.SWAGGER_2).groupName("asi-esb-api").select()
                //筛选所有添加@ApiOperation描述的方法  因之前的接口全部没有加@ApiOperation注解,所以暂时注释掉  日后规范化之后放开
//                .apis(RequestHandlerSelectors.withMethodAnnotation(ApiOperation.class))
                .paths(PathSelectors.any()).build().apiInfo(getApiInfo());
    }

    private ApiInfo getApiInfo() {
        return new ApiInfoBuilder().title("asi-esb-api").description("asi-esb-api").version("V1.0").build();
    }

}
