package com.api.eap.project.fastprint;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * dummy任务对外API接口
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-16
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/fastprint/dummy")
public class EapFastPrintDummyPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //保存Dummy任务信息到数据库
    @RequestMapping(value = "/EapCoreShareDummyPlanSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreShareDummyPlanSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/dummy/EapCoreShareDummyPlanSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_dummy_plan";
        String apsPlanDTable = "a_eap_aps_dummy_plan_d";
        try {
            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            String station_attr = lstStation.get(0).get("station_attr").toString();
            if (station_attr != null && station_attr.equals("UnLoad")) {
                //收板机,需要判断是否为离线模式,若为离线模式则不执行任务接受
                String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagOnOffLine = "";
                if (aisMonitorModel.equals("AIS-PC")) {
                    tagOnOffLine = "UnLoadPlc/PlcConfig/OnOffLine";
                } else if (aisMonitorModel.equals("AIS-SERVER")) {
                    tagOnOffLine = "UnLoadPlc_" + station_code + "/PlcConfig/OnOffLine";
                } else {
                    errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                log.error("读取收板机离线在线点位：{}", tagOnOffLine);
                JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, tagOnOffLine);
                if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                    JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                    String tagOnOffLineValue = jbItem.getString("tag_value");
                    if (tagOnOffLineValue.equals("")) {
                        errorMsg = "查询收板机在线与离线状态时收板机PLC断网";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    if (tagOnOffLineValue.equals("0")) {//离线
                        transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "当前模式为离线模式", 0);
                        return transResult;
                    }
                } else {
                    errorMsg = "未查询到收板机在线与离线状态";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }

            JSONArray plan_list = jsonParas.getJSONArray("plan_list");
            if (plan_list == null || plan_list.size() <= 0) {
                errorMsg = "保存任务信息不能为空集合";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //若是收板机,判断是否存在PLAN或者WORK的任务,若存在则不执行再次插入
            if (station_attr.equals("UnLoad")) {
                String[] group_lot_status2 = new String[]{"PLAN", "WORK"};
                JSONObject jbItem = plan_list.getJSONObject(0);
                String group_lot_num_unload = jbItem.getString("group_lot_num") == null ? "" : jbItem.getString("group_lot_num");
                Query queryBigDataUnLoad = new Query();
                queryBigDataUnLoad.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigDataUnLoad.addCriteria(Criteria.where("group_lot_num").is(group_lot_num_unload));
                queryBigDataUnLoad.addCriteria(Criteria.where("group_lot_status").in(group_lot_status2));
                long allCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigDataUnLoad.getQueryObject());
                if (allCount > 0) {
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "存在PLAN或者WORK的任务", 0);
                    return transResult;
                }
            }

            //若是收板机且当前模式为收板机上游决定生产顺序,则不能进行设定lot_group_status的PLAN
            String final_lot_group_status = "PLAN";
            if (station_attr.equals("UnLoad")) {
                String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagTaskOrderByUpDevice = "";
                String clientCode = "";
                if (aisMonitorModel.equals("AIS-PC")) {
                    tagTaskOrderByUpDevice = "UnLoadAis/AisConfig/TaskOrderByUpDevice";
                    clientCode = "UnLoadAis";
                } else if (aisMonitorModel.equals("AIS-SERVER")) {
                    tagTaskOrderByUpDevice = "UnLoadAis_" + station_code + "/AisConfig/TaskOrderByUpDevice";
                    clientCode = "UnLoadAis_" + station_code;
                }
                //1.先判断是否存在tag点
                String sqlOnlyCountTag = "select count(1) " + "from scada_tag st inner join scada_tag_group stg " + "on st.tag_group_id=stg.tag_group_id inner join scada_client sc " + "on stg.client_id=sc.client_id " + "where sc.enable_flag='Y' and stg.enable_flag='Y' and st.enable_flag='Y' " + "and sc.client_code='" + clientCode + "' and stg.tag_group_code='AisConfig' " + "and st.tag_code='TaskOrderByUpDevice'";
                Integer OnlyCountTag = cFuncDbSqlResolve.GetSelectCount(sqlOnlyCountTag);
                if (OnlyCountTag > 0) {
                    JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, tagTaskOrderByUpDevice);
                    if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                        JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                        String tagTaskOrderByUpDeviceValue = jbItem.getString("tag_value");
                        if (tagTaskOrderByUpDeviceValue.equals("")) {
                            errorMsg = "查询收板机工单任务生产顺序来源于上游设备状态时收板机PLC断网";
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        if (tagTaskOrderByUpDeviceValue.equals("1")) {
                            final_lot_group_status = "WAIT";
                        }
                    } else {
                        errorMsg = "未查询到收板机工单任务生产顺序来源于上游设备状态";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
            }

            //查询当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstPlanDocuments = new ArrayList<>();
            List<Map<String, Object>> lstPlanBDocuments = new ArrayList<>();
            String group_id = CFuncUtilsSystem.CreateUUID(true);
            for (int i = 0; i < plan_list.size(); i++) {
                JSONObject jbItem = plan_list.getJSONObject(i);
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String plan_id = CFuncUtilsSystem.CreateUUID(true);
                String port_code = jbItem.getString("port_code") == null ? "" : jbItem.getString("port_code");
                if (station_attr.equals("UnLoad")) port_code = "";
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("plan_id", plan_id);
                mapBigDataRow.put("station_id", Long.parseLong(station_id));
                mapBigDataRow.put("task_from", jbItem.getString("task_from") == null ? "EAP" : jbItem.getString("task_from"));
                mapBigDataRow.put("group_lot_num", jbItem.getString("group_lot_num") == null ? "" : jbItem.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", jbItem.getString("lot_num") == null ? "" : jbItem.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", jbItem.getString("lot_short_num") == null ? "" : jbItem.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", jbItem.getInteger("lot_index") == null ? 1 : jbItem.getInteger("lot_index"));
                mapBigDataRow.put("plan_lot_count", jbItem.getInteger("plan_lot_count") == null ? 0 : jbItem.getInteger("plan_lot_count"));
                mapBigDataRow.put("target_lot_count", jbItem.getInteger("target_lot_count") == null ? 0 : jbItem.getInteger("target_lot_count"));
                mapBigDataRow.put("port_code", port_code);
                mapBigDataRow.put("material_code", jbItem.getString("material_code") == null ? "" : jbItem.getString("material_code"));
                mapBigDataRow.put("pallet_num", jbItem.getString("pallet_num") == null ? "" : jbItem.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", jbItem.getString("pallet_type") == null ? "" : jbItem.getString("pallet_type"));
                mapBigDataRow.put("lot_level", jbItem.getString("lot_level") == null ? "" : jbItem.getString("lot_level"));
                mapBigDataRow.put("panel_length", jbItem.getDouble("panel_length") == null ? 0d : jbItem.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", jbItem.getDouble("panel_width") == null ? 0d : jbItem.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", jbItem.getDouble("panel_tickness") == null ? 0d : jbItem.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_model", jbItem.getInteger("panel_model") == null ? -1 : jbItem.getInteger("panel_model"));
                mapBigDataRow.put("inspect_count", jbItem.getInteger("inspect_count") == null ? 0 : jbItem.getInteger("inspect_count"));
                mapBigDataRow.put("inspect_finish_count", 0);
                mapBigDataRow.put("pdb_count", jbItem.getInteger("pdb_count") == null ? 0 : jbItem.getInteger("pdb_count"));
                mapBigDataRow.put("pdb_rule", jbItem.getInteger("pdb_rule") == null ? 0 : jbItem.getInteger("pdb_rule"));
                mapBigDataRow.put("fp_count", jbItem.getInteger("fp_count") == null ? 0 : jbItem.getInteger("fp_count"));
                mapBigDataRow.put("group_lot_status", final_lot_group_status);
                mapBigDataRow.put("lot_status", "PLAN");
                mapBigDataRow.put("finish_count", 0);
                mapBigDataRow.put("finish_ok_count", 0);
                mapBigDataRow.put("finish_ng_count", 0);
                mapBigDataRow.put("task_error_code", 0);
                mapBigDataRow.put("item_info", jbItem.getString("item_info") == null ? "" : jbItem.getString("item_info"));
                mapBigDataRow.put("task_start_time", "");
                mapBigDataRow.put("task_end_time", "");
                mapBigDataRow.put("task_cost_time", (long) 0);
                mapBigDataRow.put("attribute1", jbItem.getString("attribute1") == null ? "" : jbItem.getString("attribute1"));
                mapBigDataRow.put("attribute2", jbItem.getString("attribute2") == null ? "" : jbItem.getString("attribute2"));
                mapBigDataRow.put("attribute3", jbItem.getString("attribute3") == null ? "" : jbItem.getString("attribute3"));
                mapBigDataRow.put("face_code", jbItem.getInteger("face_code") == null ? 0 : jbItem.getInteger("face_code"));
                mapBigDataRow.put("pallet_use_count", jbItem.getInteger("pallet_use_count") == null ? 0 : jbItem.getInteger("pallet_use_count"));
                mapBigDataRow.put("user_name", user_name);
                mapBigDataRow.put("group_id", group_id);
                mapBigDataRow.put("offline_flag", "N");
                mapBigDataRow.put("target_update_count", 0);

                lstPlanDocuments.add(mapBigDataRow);
                String panel_list = jbItem.getString("panel_list") == null ? "" : jbItem.getString("panel_list");
                String[] panelList = panel_list.split(",", -1);
                if (!panel_list.equals("")) {
                    if (panelList != null && panelList.length > 0) {
                        for (int j = 0; j < panelList.length; j++) {
                            String panel_barcode = panelList[j];
                            String plan_d_id = CFuncUtilsSystem.CreateUUID(true);
                            Map<String, Object> mapBigDataRowB = new HashMap<>();
                            mapBigDataRowB.put("item_date", item_date);
                            mapBigDataRowB.put("item_date_val", item_date_val);
                            mapBigDataRowB.put("plan_d_id", plan_d_id);
                            mapBigDataRowB.put("plan_id", plan_id);
                            mapBigDataRowB.put("panel_barcode", panel_barcode);
                            lstPlanBDocuments.add(mapBigDataRowB);
                        }
                    }
                }
            }
            log.info("同步任务信息：{}", lstPlanDocuments);
            if (lstPlanBDocuments.size() > 0) mongoTemplate.insert(lstPlanBDocuments, apsPlanDTable);
            mongoTemplate.insert(lstPlanDocuments, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "插入成功", 0);
        } catch (Exception ex) {
            errorMsg = "保存任务信息到数据库异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


    //放板机Panel校验 Dummy任务
    @RequestMapping(value = "/EapCoreLoadDummyPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadDummyPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/dummy/EapCoreLoadDummyPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_dummy_plan";
        String apsPlanDTable = "a_eap_aps_dummy_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批,2为一批多车
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String inspect_flag = jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String face_code2 = jsonParas.getString("face_code");//存储A面B面,1:A,2:B

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (group_lot_num == null) group_lot_num = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (inspect_flag == null || inspect_flag.equals("")) inspect_flag = "N";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (face_code2 == null || face_code2.equals("")) face_code2 = "0";
            Integer face_code2_int = Integer.parseInt(face_code2);

            long station_id_long = Long.parseLong(station_id);

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //1.判断是否存在PLAN状态下group_lot_num,若存在则进行更新为WORK状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "WORK");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            //查询该工单下全部的plan_id
            List<String> lstPlanId = new ArrayList<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前计划中或者执行中的子任务
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String port_code = "";
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;
            String old_plan_status = "";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            if (face_code2_int > 0) face_code = face_code2_int;//若有传递值,则用传递值

            //未找到任务报错
            if (plan_id.equals("")) {
                errorMsg = "未找到生产任务,请先导入生产任务";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            //4.若不为Dummy板则直接先存储
            if (!dummy_flag.equals("Y")) {
                panel_ng_code = 5;
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK," + plan_lot_count;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_count >= plan_lot_count) lot_status = "FINISH";
            String lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + plan_lot_count;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更改任务完工状态以及查询完工明细 Dummy任务
    @RequestMapping(value = "/EapCoreLoadDummyPlanUpdateFinishStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadDummyPlanUpdateFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/dummy/EapCoreLoadDummyPlanUpdateFinishStatus";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_dummy_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();
            //判断是否需要做自动登出的动作
            String EapAutoFinishLoginOut = cFuncDbSqlResolve.GetParameterValue("Eap_Auto_FinishLoginOut");
            if (EapAutoFinishLoginOut.equals("Y")) {
                //设置自动登出
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                Query query = new Query();
                query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                query.addCriteria(Criteria.where("checkout_flag").is("N"));
                Update updateBigData = new Update();
                updateBigData.set("checkout_date", nowDateTime);
                updateBigData.set("checkout_flag", "Y");
                mongoTemplate.updateMulti(query, updateBigData, "a_eap_me_station_user");
            }
            //查询是否一批多车模式
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //1.查询当前是否存在进行中的任务
            Integer plan_all_count = 0;
            Integer finish_all_count = 0;
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                String pallet_num2 = docItemBigData.getString("pallet_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                Integer fp_count2 = docItemBigData.getInteger("fp_count");
                Integer inspect_finish_count2 = docItemBigData.getInteger("inspect_finish_count");
                Integer inspect_count2 = docItemBigData.getInteger("inspect_count");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("pallet_num", pallet_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("fp_count", fp_count2);
                jbItem2.put("inspect_finish_count", inspect_finish_count2);
                jbItem2.put("inspect_count", inspect_count2);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
                plan_all_count += plan_lot_count2;
                finish_all_count += finish_ok_count2;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //判断是否需要更新
            Boolean isUpdTaskStatus = true;
            if (task_error_code_int < 4 && OneCarMultyLotFlag.equals("2")) {
                if (finish_all_count < plan_all_count) isUpdTaskStatus = false;
            }

            //更改完工任务状态
            if (isUpdTaskStatus) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "FINISH");
                updateBigData.set("lot_status", "FINISH");
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                updateBigData.set("task_error_code", task_error_code_int);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //放板机完工同步到收板机 Dummy任务
    @RequestMapping(value = "/EapCoreUnLoadDummyPlanLoadFinish", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadDummyPlanLoadFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/dummy/EapCoreUnLoadDummyPlanLoadFinish";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_dummy_plan";
        try {
            String station_code = jsonParas.getString("station_code");//收板机工位
            String sqlStation = "select station_id " +
                    "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            JSONArray load_panel_list = jsonParas.getJSONArray("load_panel_list");
            JSONArray load_lot_list = jsonParas.getJSONArray("load_lot_list");
            String target_group_lot_num = "";
            Integer target_plan_count = 0;
            Integer target_update_count_sum = 0;
            String group_lot_status_now = "";

            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            if (load_lot_list != null && load_lot_list.size() > 0) {
                for (int i = 0; i < load_lot_list.size(); i++) {
                    JSONObject jbItem = load_lot_list.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    if (i == 0) target_group_lot_num = group_lot_num;
                    String lot_num = jbItem.getString("lot_num");
                    Integer finish_count = jbItem.getInteger("finish_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    Integer finish_ng_count = jbItem.getInteger("finish_ng_count");
                    target_plan_count += finish_ok_count;

                    //先查找数据
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        Document docItemBigData = iteratorBigData.next();
                        Integer target_update_count = 0;
                        if (docItemBigData.containsKey("target_update_count")) {
                            target_update_count = docItemBigData.getInteger("target_update_count");
                        }
                        target_update_count_sum += target_update_count;
                        finish_ok_count += target_update_count;
                        group_lot_status_now = docItemBigData.getString("group_lot_status");
                        iteratorBigData.close();
                    }

                    //更新
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    Update updateBigData = new Update();
                    updateBigData.set("target_lot_count", finish_ok_count);
                    updateBigData.set("target_update_count", finish_ok_count);
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }
            }

            Boolean isCancel = false;
            if (target_plan_count <= 0 && target_update_count_sum <= 0) isCancel = true;

            //判断放板数量是否为0
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && isCancel) {

                //判断是否写入点位CANCEL
                if (group_lot_status_now.equals("WORK")) {
                    String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                    String tagTaskCancelRequest = "";
                    if (aisMonitorModel.equals("AIS-PC")) {
                        tagTaskCancelRequest = "UnLoadAis/AisStatus/TaskCancelRequest";
                    } else if (aisMonitorModel.equals("AIS-SERVER")) {
                        tagTaskCancelRequest = "UnLoadAis_" + station_code + "/AisStatus/TaskCancelRequest";
                    } else {
                        errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagTaskCancelRequest, "1", true);
                    if (!errorMsg.equals("")) {
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }

                //再更新
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                Update updateBigData2 = new Update();
                updateBigData2.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }

            //判断是否当前任务正在进行中,若是进行中,则需要把任务数量更新到PLC
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && target_plan_count > 0) {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                String port_code = "";
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    port_code = docItemBigData.getString("port_code");
                    iteratorBigData.close();
                }
                if (port_code != null && !port_code.equals("")) {
                    String sqlPort = "select port_index " +
                            "from a_eap_fmod_station_port " +
                            "where station_id=" + station_id + " and port_code='" + port_code + "'";
                    List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPort, false, request, apiRoutePath);
                    if (lstPort != null && lstPort.size() > 0) {
                        String port_index = lstPort.get(0).get("port_index").toString();
                        String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                        String tagUpdateLotCountStatus = "";
                        String tagUpdateLotCount = "";
                        if (aisMonitorModel.equals("AIS-PC")) {
                            tagUpdateLotCountStatus = "UnLoadPlc/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "UnLoadPlc/AisReqControl" + port_index + "/UpdateLotCount";
                        } else if (aisMonitorModel.equals("AIS-SERVER")) {
                            tagUpdateLotCountStatus = "UnLoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "UnLoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCount";
                        } else {
                            errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        String tagOnlyKeyList = tagUpdateLotCount + "," + tagUpdateLotCountStatus;
                        String tagValueList = (target_plan_count + target_update_count_sum) + "&1";
                        errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
                        if (!errorMsg.equals("")) {
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                    }
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "放板机完工同步到收板机异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //收板机Panel校验  ----暂时先不做一车多批的判断,这个再想想
    @RequestMapping(value = "/EapCoreUnLoadDummyPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadDummyPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/dummy/EapCoreUnLoadDummyPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_count = 0;//计划首检数量
            Integer inspect_finish_count = 0;//实际完成首检数量
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";
            String dummyTaskFlag = "0";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            //1.先判断是否存在正常板件任务
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                apsPlanTable = "a_eap_aps_dummy_plan";
                apsPlanDTable = "a_eap_aps_dummy_plan_d";
                dummyTaskFlag = "1";
            }
            //2.如果没有当前工作中的正常板件任务，按dummy任务处理
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //1.判断是否存在PLAN状态下group_lot_num,若存在则进行更新为WORK状态
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

                //再查
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_count = docItemBigData.getInteger("inspect_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));

            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            if (plan_id.equals("")) {//当未找到任务时直接记录
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 99;
                }
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + plan_lot_count + "," + dummyTaskFlag;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若不为Dummy板则直接先存储
            if(dummyTaskFlag.equals("1")) {
                if (!dummy_flag.equals("Y")) {
                    panel_ng_code = 5;
                    result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                            panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + plan_lot_count + "," + dummyTaskFlag;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                    mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                    return transResult;
                }
            } else{
                if(dummy_flag.equals("Y")){
                    result=station_flow_id+","+lot_num+","+pallet_num+","+ panel_index+","+panel_barcode+","+
                            panel_ng_code+","+inspect_finish_count+","+tray_barcode+",WORK"+ "," + plan_lot_count + "," + dummyTaskFlag;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                    mongoTemplate.insert(mapBigDataRow,meStationFlowTable);
                    transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
                    return transResult;
                }
            }
            panel_ng_code = 99;//Dummy模式返回100代码

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            if (inspect_count > 0 && inspect_finish_count < inspect_count) {
                mapBigDataRow.put("inspect_flag", "Y");
                inspect_finish_count++;
            }

            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_count >= plan_lot_count) lot_status = "FINISH";
            String lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (inspect_count > 0 && inspect_finish_count <= inspect_count) {
                updateBigData.set("inspect_finish_count", inspect_finish_count);
            }
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + plan_lot_count + "," + dummyTaskFlag;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "DEMO异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更改任务完工状态以及查询完工明细 Dummy任务
    @RequestMapping(value = "/EapCoreUnLoadDummyPlanUpdateFinishStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadDummyPlanUpdateFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fastprint/dummy/EapCoreUnLoadDummyPlanUpdateFinishStatus";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_dummy_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();

            //查询是否一批多车模式
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //1.查询当前是否存在进行中的任务
            Integer plan_all_count = 0;
            Integer finish_all_count = 0;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                String pallet_num2 = docItemBigData.getString("pallet_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                Integer inspect_count = docItemBigData.getInteger("inspect_count");
                Integer inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("pallet_num", pallet_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("inspect_count", inspect_count);
                jbItem2.put("inspect_finish_count", inspect_finish_count);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
                plan_all_count += plan_lot_count2;
                finish_all_count += finish_ok_count2;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //判断是否需要更新
            Boolean isUpdTaskStatus = true;
            if (task_error_code_int < 4 && OneCarMultyLotFlag.equals("2")) {
                if (finish_all_count < plan_all_count) isUpdTaskStatus = false;
            }

            //更改完工任务状态
            if (isUpdTaskStatus) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "FINISH");
                updateBigData.set("lot_status", "FINISH");
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                updateBigData.set("task_error_code", task_error_code_int);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
