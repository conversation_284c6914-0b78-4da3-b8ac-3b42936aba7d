server:
  port: 9089 #9090
  servlet:
    context-path: /
  compression:
    enabled: true
    min-response-size: 1024
    mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

#定义变量
#MongoDB与SQL大数据文件存储路径
file-bigdata-save-path: '/u01/ais/file/bigdata/'

mybatis-plus:
  configuration:
    call-setters-on-nulls: true #集成mybatis-plus 返回map字段为空时，true返回

spring:
  aop:
    proxy-target-class: true
  data:
    mongodb:
      uri: ********************************************************
      database: ais_esb
  datasource:
    type: com.zaxxer.hikari.HikariDataSource
    driver-class-name: org.postgresql.Driver
    url: ****************************************************************************************************************************************
    username: postgres
    password: 123456
    #password: Ais@123456
    hikari:
      minimum-idle: 50
      maximum-pool-size: 400
      auto-commit: true
      idle-timeout: 30000
      pool-name: DateSourceHikariPD
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: GMT+8
#  profiles:
#    active: dev

limits:
  - url: /aisEsbApi/pmc/project/bq/PmcBqStationScreenEmergency
    method: POST
    limit-quantity: 100
    period: 1000
  - url: /aisEsbApi/pmc/project/bq/PmcBqStationScreenQueue
    method: POST
    limit-quantity: 100
    period: 1000
  - url: /aisEsbApi/pmc/project/bq/PmcBqStationScreenQuality
    method: POST
    limit-quantity: 100
    period: 1000
  - url: /aisEsbApi/pmc/project/bq/PmcBqAndonStationScreenData
    method: POST
    limit-quantity: 100
    period: 1000
