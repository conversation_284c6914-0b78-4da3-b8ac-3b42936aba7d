package com.api.eap.project.guangxin.event;

import com.alibaba.fastjson.JSONArray;
import org.springframework.context.ApplicationEvent;

public class BatchesFinishedEvent extends ApplicationEvent
{
    private final String equipmentType;
    private final boolean integrated;

    public BatchesFinishedEvent(Object source, String equipmentType, boolean integrated)
    {
        super(source);
        this.equipmentType = equipmentType;
        this.integrated = integrated;
    }

    @Override
    public JSONArray getSource()
    {
        return (JSONArray) super.getSource();
    }

    public String getEquipmentType()
    {
        return equipmentType;
    }

    public boolean isIntegrated()
    {
        return integrated;
    }
}
