package com.api.eap.project.xinai;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 芯爱LDI发送事件功能函数
 * 1.Panel2DCRead:基板2DC读取结果通知
 * 2.PanelTransferCommand:投入基板的2DC和投入番号的通知
 * 3.ForcedPanelLoading:基板强制投入的通知
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Service
@Slf4j
public class EapXinaiLdiSendFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapXinaiLdiSendSubFunc eapXinaiLdiSendSubFunc;

    //1.基板2DC读取结果通知
    public String Panel2DCRead(String panel_barcode,String panel_index) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapXinaiLdiSendSubFunc.Panel2DCRead(panel_barcode,panel_index);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
        String decision=jbResult.getString("decision");
        return decision;
    }

    //2.投入基板的2DC和投入番号的通知
    public void PanelTransferCommand(String panel_barcode,String panel_index) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapXinaiLdiSendSubFunc.PanelTransferCommand(panel_barcode,panel_index);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //3.基板强制投入的通知
    public void ForcedPanelLoading(String panel_barcode,String panel_index) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapXinaiLdiSendSubFunc.ForcedPanelLoading(panel_barcode,panel_index);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }
}
