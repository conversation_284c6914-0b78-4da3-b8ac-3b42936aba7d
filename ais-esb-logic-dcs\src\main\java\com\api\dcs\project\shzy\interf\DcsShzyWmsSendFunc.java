package com.api.dcs.project.shzy.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 中冶WMS发送流程功能函数
 * 1.通知执行分拣
 * 2.通知中控上料完成
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Service
@Slf4j
public class DcsShzyWmsSendFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private DcsShzyWmsSendSubFunc dcsShzyWmsSendSubFunc;
    @Resource
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Resource
    private CFuncUtilsRest cFuncUtilsRest;

    //1.通知执行分拣
    public void SendFjExecuteTask(String task_num, Integer position_x,Integer position_y,Integer position_z,
                                  String stock_code) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsShzyWmsSendSubFunc.SendFjExecuteTask(task_num, position_x,
                position_y,position_z,stock_code);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //2.通知中控上料完成
    public void SendZkFeedFinish(String task_num, String status,String statusMsg) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsShzyWmsSendSubFunc.SendZkFeedFinish(task_num, status,statusMsg);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }
}
