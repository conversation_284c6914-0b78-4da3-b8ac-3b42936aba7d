
package com.api.eap.project.dy.p1.wcf;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="PanelDataUploadReportResult" type="{http://tempuri.org/}PanelDataUploadReportResponseItem" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "panelDataUploadReportResult"
})
@XmlRootElement(name = "PanelDataUploadReportResponse")
public class PanelDataUploadReportResponse {

    @XmlElement(name = "PanelDataUploadReportResult")
    protected PanelDataUploadReportResponseItem panelDataUploadReportResult;

    /**
     * 获取panelDataUploadReportResult属性的值。
     * 
     * @return
     *     possible object is
     *     {@link PanelDataUploadReportResponseItem }
     *     
     */
    public PanelDataUploadReportResponseItem getPanelDataUploadReportResult() {
        return panelDataUploadReportResult;
    }

    /**
     * 设置panelDataUploadReportResult属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link PanelDataUploadReportResponseItem }
     *     
     */
    public void setPanelDataUploadReportResult(PanelDataUploadReportResponseItem value) {
        this.panelDataUploadReportResult = value;
    }

}
