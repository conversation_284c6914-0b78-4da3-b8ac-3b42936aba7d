package com.api.pack.core.station;

import com.api.base.Const;
import com.api.base.IMongoBasicService;
import org.springframework.data.domain.Example;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class StationFlowService extends IMongoBasicService<StationFlow, StationFlowRepository>
{
    public StationFlowService(StationFlowRepository repository)
    {
        super(repository);
    }

    public StationFlow findHeadByPlanIdAndLotNumAndSerialNum(String planId, String lotNum, String serialNum)
    {
        StationFlow query = new StationFlow();
        query.setPlanId(planId);
        query.setLotNum(lotNum);
        query.setSerialNum(serialNum);
        query.setHeadFlag(Const.FLAG_Y);
        return this.getRepository().findOne(Example.of(query)).orElse(null);
    }

    public StationFlow findOneByStationCodeAndPlanIdAndLotNumAndSerialNum(String stationCode, String planId, String lotNum, String serialNum)
    {
        StationFlow query = new StationFlow();
        query.setStationCode(stationCode);
        query.setPlanId(planId);
        query.setLotNum(lotNum);
        query.setSerialNum(serialNum);
        return this.getRepository().findOne(Example.of(query)).orElse(null);
    }
}
