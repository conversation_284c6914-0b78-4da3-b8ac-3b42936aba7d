package com.api.eap.project.zhcy;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 志圣-珠海超毅-APS计划管理控制器
 * 1. 查询APS计划列表
 * 2. 取消APS计划
 * 3. 删除APS计划
 * 4. 查询APS计划面板详情
 * </p>
 *
 * <AUTHOR>
 * @Date 2025-04-16
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/zhcy/aps-plan")
public class EapZhcyApsPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    private static final String A_EAP_APS_PLAN = "a_eap_aps_plan";

    /**
     * 查询APS计划列表
     * 支持按设备ID、计划ID、状态等条件查询
     * @param jsonParas 请求参数
     * @param request HTTP请求
     * @return APS计划列表JSON
     */
    @RequestMapping(value = "/queryApsPlans", method = {RequestMethod.POST})
    public String queryApsPlans(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String errorMsg = "";
        try {
            // 获取查询参数
            String eqpId = jsonParas.getString("eqpId");
            String jobId = jsonParas.getString("jobId");

            // 支持单个状态(String)或多个状态(JSONArray)查询
            String lotStatus = null;
            List<String> statusList = null;

            // 检查是否有statusList参数
            if (jsonParas.containsKey("statusList")) {
                JSONArray statusArray = jsonParas.getJSONArray("statusList");
                if (statusArray != null && !statusArray.isEmpty()) {
                    statusList = new ArrayList<>();
                    for (int i = 0; i < statusArray.size(); i++) {
                        statusList.add(statusArray.getString(i));
                    }
                }
            } else if (jsonParas.containsKey("lotStatus")) {
                // 兼容原有的单状态查询
                lotStatus = jsonParas.getString("lotStatus");
            }

            // 获取分页参数 - 修改为匹配前端参数名
            Integer currentPage = jsonParas.getInteger("currentPage");
            Integer pageSize = jsonParas.getInteger("pageSize");

            // 兼容原有参数名
            if (currentPage == null) {
                currentPage = jsonParas.getInteger("page");
            }
            if (pageSize == null) {
                pageSize = jsonParas.getInteger("limit");
            }

            // 设置默认值
            if (currentPage == null || currentPage < 1) currentPage = 1;
            if (pageSize == null || pageSize < 1) pageSize = 10;

            // 调用实现方法
            Map<String, Object> result = queryApsPlansImpl(eqpId, jobId, lotStatus, statusList, currentPage, pageSize);

            List<Map<String, Object>> planList = (List<Map<String, Object>>) result.get("taskList");
            long total = (long) result.get("total");

            // 返回结果
            return CFuncUtilsLayUiResut.GetStandJson(true, planList, "", "", (int)total);
        } catch (Exception ex) {
            errorMsg = "查询APS计划列表异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
    }

    /**
     * 查询APS计划列表实现
     * @param eqpId 设备ID
     * @param jobId 计划ID
     * @param lotStatus 计划状态(单个)
     * @param statusList 计划状态列表(多个)
     * @param page 页码
     * @param limit 每页记录数
     * @return APS计划列表和总数
     */
    private Map<String, Object> queryApsPlansImpl(String eqpId, String jobId, String lotStatus, List<String> statusList, int page, int limit) {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> planList = new ArrayList<>();

        try {
            // 构建查询条件
            Query query = new Query();
            if (StringUtils.hasText(eqpId)) {
                query.addCriteria(Criteria.where("eqp_id").is(eqpId));
            }
            if (StringUtils.hasText(jobId)) {
                query.addCriteria(Criteria.where("plan_id").is(jobId));
            }

            // 处理状态查询条件
            if (statusList != null && !statusList.isEmpty()) {
                // 使用in操作符查询多个状态
                query.addCriteria(Criteria.where("lot_status").in(statusList));
                log.info("根据多个状态查询APS计划: {}", statusList);
            } else if (StringUtils.hasText(lotStatus)) {
                // 兼容原有的单状态查询
                query.addCriteria(Criteria.where("lot_status").is(lotStatus));
            }

            // 默认只查询启用的计划
            query.addCriteria(Criteria.where("enable_flag").is("Y"));

            // 计算总数
            long total = mongoTemplate.count(query, A_EAP_APS_PLAN);

            // 添加分页
            query.skip((page - 1) * limit).limit(limit);

            // 自定义排序逻辑：WORK排第一、PLAN排第二、FINISH排第三、CANCEL排第四
            // 对于WORK和PLAN，按创建时间升序（早的在前）；对于FINISH和CANCEL，按创建时间降序（晚的在前）

            // 使用聚合管道实现自定义排序
            Document sortDoc = new Document();
            // 先根据状态优先级排序
            sortDoc.append("lot_status_order", 1);
            // 然后按调整后的创建时间排序
            sortDoc.append("create_time_sort_value", 1);

            // 添加状态排序字段
            Document addFieldsStatus = new Document("$addFields", new Document("lot_status_order",
                new Document("$switch", new Document("branches", Arrays.asList(
                    new Document("case", new Document("$eq", Arrays.asList("$lot_status", "WORK"))).append("then", 1),
                    new Document("case", new Document("$eq", Arrays.asList("$lot_status", "PLAN"))).append("then", 2),
                    new Document("case", new Document("$eq", Arrays.asList("$lot_status", "FINISH"))).append("then", 3),
                    new Document("case", new Document("$eq", Arrays.asList("$lot_status", "CANCEL"))).append("then", 4)
                )).append("default", 99))));

            // 添加创建时间排序字段（FINISH和CANCEL取负值以实现降序）
            Document addFieldsTime = new Document("$addFields", new Document("create_time_sort_value",
                new Document("$cond", Arrays.asList(
                    new Document("$in", Arrays.asList("$lot_status", Arrays.asList("FINISH", "CANCEL"))),
                    new Document("$multiply", Arrays.asList(new Document("$toLong", "$create_time"), -1)), // Convert Date to numeric and negate
                    new Document("$toLong", "$create_time") // Convert Date to numeric for WORK and PLAN
                ))));

            // 添加排序阶段
            Document sort = new Document("$sort", sortDoc);

            // 添加分页阶段
            Document skip = new Document("$skip", (page - 1) * limit);
            Document limitDoc = new Document("$limit", limit);

            // 添加投影阶段，排除 recipe_parameter_list 和 panel_list 字段
            Document project = new Document("$project", new Document()
                .append("recipe_parameter_list", 0) // 排除 recipe_parameter_list
                .append("panel_list", 0));          // 排除 panel_list

            // 构建聚合管道
            List<Document> pipeline = new ArrayList<>();

            // 添加查询条件到管道开始
            Document match = new Document("$match", query.getQueryObject());
            pipeline.add(match);

            // 添加其他阶段
            pipeline.add(addFieldsStatus); // 添加状态排序字段
            pipeline.add(addFieldsTime);   // 添加创建时间排序字段
            pipeline.add(sort);           // 排序
            pipeline.add(project);        // 投影
            pipeline.add(skip);           // 分页跳过
            pipeline.add(limitDoc);       // 分页限制

            // 执行聚合查询
            List<Document> documents = mongoTemplate.getCollection(A_EAP_APS_PLAN)
                    .aggregate(pipeline)
                    .into(new ArrayList<>());

            // 转换结果
            for (Document doc : documents) {
                Map<String, Object> planMap = new HashMap<>();
                planMap.putAll(doc);
                planList.add(planMap);
            }
            result.put("taskList", planList);
            result.put("total", total);
        } catch (Exception ex) {
            log.error("查询APS计划列表异常: {}", ex.getMessage(), ex);
            throw ex;
        }

        return result;
    }

    /**
     * 取消APS计划
     * 将计划状态更新为CANCEL
     * @param jsonParas 请求参数
     * @param request HTTP请求
     * @return 处理结果
     */
    @RequestMapping(value = "/cancelApsPlan", method = {RequestMethod.POST})
    public String cancelApsPlan(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String errorMsg = "";

        try {
            // 获取参数
            String jobId = jsonParas.getString("jobId");
            String eqpId = jsonParas.getString("eqpId");

            // 参数验证
            if (!StringUtils.hasText(jobId)) {
                return CFuncUtilsLayUiResut.GetErrorJson("计划ID不能为空");
            }

            // 构建查询条件
            Query query = new Query();
            query.addCriteria(Criteria.where("plan_id").is(jobId));
            if (StringUtils.hasText(eqpId)) {
                query.addCriteria(Criteria.where("eqp_id").is(eqpId));
            }
            query.addCriteria(Criteria.where("enable_flag").is("Y"));

            // 检查计划是否存在
            Document plan = mongoTemplate.findOne(query, Document.class, A_EAP_APS_PLAN);
            if (plan == null) {
                return CFuncUtilsLayUiResut.GetErrorJson("计划不存在或已被删除");
            }

            // 检查计划状态
            String lotStatus = plan.getString("lot_status");
            if ("WORK".equals(lotStatus)) {
                return CFuncUtilsLayUiResut.GetErrorJson("计划正在生产中，无法取消");
            }

            // 更新计划状态为CANCEL
            Update update = new Update();
            update.set("lot_status", "CANCEL");
            update.set("update_time", new Date());

            mongoTemplate.updateFirst(query, update, A_EAP_APS_PLAN);

            return CFuncUtilsLayUiResut.GetStandJson(true, null, "计划取消成功", "", 0);
        } catch (Exception ex) {
            errorMsg = "取消APS计划异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
    }

    /**
     * 删除APS计划
     * 将计划的enable_flag设置为N
     * @param jsonParas 请求参数
     * @param request HTTP请求
     * @return 处理结果
     */
    @RequestMapping(value = "/deleteApsPlan", method = {RequestMethod.POST})
    public String deleteApsPlan(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String errorMsg = "";

        try {
            // 获取参数
            String jobId = jsonParas.getString("jobId");
            String eqpId = jsonParas.getString("eqpId");

            // 参数验证
            if (!StringUtils.hasText(jobId)) {
                return CFuncUtilsLayUiResut.GetErrorJson("计划ID不能为空");
            }

            // 构建查询条件
            Query query = new Query();
            query.addCriteria(Criteria.where("plan_id").is(jobId));
            if (StringUtils.hasText(eqpId)) {
                query.addCriteria(Criteria.where("eqp_id").is(eqpId));
            }
            query.addCriteria(Criteria.where("enable_flag").is("Y"));

            // 检查计划是否存在
            Document plan = mongoTemplate.findOne(query, Document.class, A_EAP_APS_PLAN);
            if (plan == null) {
                return CFuncUtilsLayUiResut.GetErrorJson("计划不存在或已被删除");
            }

            // 检查计划状态
            String lotStatus = plan.getString("lot_status");
            if ("WORK".equals(lotStatus)) {
                return CFuncUtilsLayUiResut.GetErrorJson("计划正在生产中，无法删除");
            }

            // 更新计划状态为CANCEL并设置enable_flag为N
            Update update = new Update();
            update.set("lot_status", "CANCEL");
            update.set("enable_flag", "N");
            update.set("update_time", new Date());

            mongoTemplate.updateFirst(query, update, A_EAP_APS_PLAN);

            return CFuncUtilsLayUiResut.GetStandJson(true, null, "计划删除成功", "", 0);
        } catch (Exception ex) {
            errorMsg = "删除APS计划异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
    }

    /**
     * 查询APS计划面板详情
     * 根据计划ID查询面板列表
     * @param jsonParas 请求参数
     * @param request HTTP请求
     * @return 面板列表JSON
     */
    @RequestMapping(value = "/queryApsPlanPanelDetails", method = {RequestMethod.POST})
    public String queryApsPlanPanelDetails(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String errorMsg = "";

        try {
            // 获取参数
            String jobId = jsonParas.getString("jobId");

            // 参数验证
            if (!StringUtils.hasText(jobId)) {
                return CFuncUtilsLayUiResut.GetErrorJson("计划ID不能为空");
            }

            // 构建查询条件
            Query query = new Query();
            query.addCriteria(Criteria.where("plan_id").is(jobId));
            query.addCriteria(Criteria.where("enable_flag").is("Y"));

            // 检查计划是否存在
            Document plan = mongoTemplate.findOne(query, Document.class, A_EAP_APS_PLAN);
            if (plan == null) {
                return CFuncUtilsLayUiResut.GetErrorJson("计划不存在或已被删除");
            }

            // 获取PanelList
            Object panelListObj = plan.get("panel_list");
            JSONArray panelList = new JSONArray();

            if (panelListObj != null) {
                if (panelListObj instanceof List) {
                    List<?> panelListRaw = (List<?>) panelListObj;
                    for (Object panel : panelListRaw) {
                        if (panel instanceof Map) {
                            JSONObject panelJson = new JSONObject();
                            panelJson.putAll((Map<String, Object>) panel);
                            panelList.add(panelJson);
                        }
                    }
                }
            }

            // 构建返回结果
            Map<String, Object> result = new HashMap<>();
            result.put("jobId", jobId);
            result.put("eqpId", plan.getString("eqp_id"));
            result.put("pn", plan.getString("pn"));
            result.put("totalPanelCount", plan.getInteger("total_panel_count"));
            result.put("panelList", panelList);
            List<Map<String, Object>> res = new ArrayList<Map<String, Object>>();
            res.add(result);
            return CFuncUtilsLayUiResut.GetStandJson(true, res,"查询成功", "", panelList.size());
        } catch (Exception ex) {
            errorMsg = "查询APS计划面板详情异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
    }
}