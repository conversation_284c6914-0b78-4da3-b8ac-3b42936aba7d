package com.api.dcs.core.interf.fj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * DCS分拣流程接口(Sub)公共方法
 * 1.发送分拣任务
 * 2.通知工位开始分拣
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@Slf4j
public class DcsCoreFjSendSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.发送分拣任务
    public JSONObject FjSendTask(JSONArray task_list) throws Exception {
        JSONObject jbResult = new JSONObject();
        Integer code = 0;
        String errorMsg = "";
        String esbInterfCode = "FjSendTask";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            postParas.put("request_uuid", CFuncUtilsSystem.GetOnlySign(""));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("task_list", task_list);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);

            //4.判断数据
            if (jsonObjectBack == null || !jsonObjectBack.containsKey("code") || !jsonObjectBack.containsKey("msg")) {
                code = -1;
                errorMsg = "接受报文为空或者不包含code|msg字段";
                jbResult.put("code", code);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            code = jsonObjectBack.getInteger("code");
            String msg = jsonObjectBack.getString("msg");
            if (code != 0) {
                jbResult.put("code", code);
                jbResult.put("successFlag", false);
                jbResult.put("message", msg);
                return jbResult;
            }

            //成功
            jbResult.put("code", code);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("code", code);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.通知工位开始分拣
    public JSONObject FjSendTaskStart(String station_code, String task_num, String is_frame) throws Exception {
        JSONObject jbResult = new JSONObject();
        Integer code = 0;
        String errorMsg = "";
        String esbInterfCode = "FjSendTaskStart";
        String token = station_code;
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            postParas.put("request_uuid", CFuncUtilsSystem.GetOnlySign(""));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("station_code", station_code);
            postParas.put("task_no", task_num);
            postParas.put("is_frame", is_frame);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);

            //4.判断数据
            if (jsonObjectBack == null || !jsonObjectBack.containsKey("code") || !jsonObjectBack.containsKey("msg")) {
                code = -1;
                errorMsg = "接受报文为空或者不包含code|msg字段";
                jbResult.put("code", code);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            code = jsonObjectBack.getInteger("code");
            String msg = jsonObjectBack.getString("msg");
            if (code != 0) {
                jbResult.put("code", code);
                jbResult.put("successFlag", false);
                jbResult.put("message", msg);
                return jbResult;
            }

            //成功
            jbResult.put("code", code);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("code", code);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
