package com.api.pack.core.sort;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.util.LinkedList;
import java.util.List;

@Service
public class SortSplitRuleService extends ServiceImpl<SortSplitRuleMapper, SortSplitRule> implements IService<SortSplitRule>
{
    public SortSplitRule getById(Long id)
    {
        SortSplitRule rule = this.getBaseMapper().selectById(id);
        if (rule != null)
        {
            List<SortSplitRule.Item> items = new LinkedList<>();
            if (!ObjectUtils.isEmpty(rule.getContent()))
            {
                List<String> content = JSON.parseArray(rule.getContent(), String.class);
                for (String item : content)
                {
                    items.add(new SortSplitRule.Item(item));
                }
                rule.setItems(items);
            }
        }
        return rule;
    }
}
