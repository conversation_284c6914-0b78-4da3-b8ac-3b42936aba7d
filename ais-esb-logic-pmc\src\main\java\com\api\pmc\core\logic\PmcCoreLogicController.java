package com.api.pmc.core.logic;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 逻辑接口
 * 1.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/logic")
public class PmcCoreLogicController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    //1.逻辑信息
    @RequestMapping(value = "/PmcCoreLogicFuncSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreLogicFuncSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/logic/PmcCoreLogicFuncSel";
        String selectResult="";
        String errorMsg="";
        String userName="LOGIC";
        try{
            //获取逻辑信息
            String func_code=jsonParas.getString("func_code");
            String sqlLogic="select rlf.logic_func_id," +
                    "rlf.func_code," +
                    "rlf.func_des," +
                    "sf.function_path " +
                    "from rcs_logic_func rlf " +
                    "inner join sys_function sf on rlf.function_id=sf.function_id " +
                    "and sf.menu_type_code='LOGIC' " +
                    "and sf.enable_flag='Y' " +
                    "where rlf.enable_flag='Y' "+
                    "and rlf.func_code='"+func_code+"' "+
                    "order by rlf.logic_func_id";
            List<Map<String,Object>> itemListLogic=cFuncDbSqlExecute.ExecSelectSql(userName,sqlLogic,false,request,apiRoutePath);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListLogic,"","",0);
        }
        catch (Exception ex){
            errorMsg= "逻辑信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //2.逻辑属性信息
    @RequestMapping(value = "/PmcCoreLogicFuncAttrSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreLogicFuncAttrSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/logic/PmcCoreLogicFuncAttrSel";
        String selectResult="";
        String errorMsg="";
        String userName="LOGIC";
        try{
            //获取逻辑属性信息
            String logicFuncId=jsonParas.getString("logic_func_id");
            String sqlLogicAttr="select rlfg.logic_func_g_id,rlag.logic_attr_group_code," +
                    "rlfi.logic_func_i_id,rlfi.logic_func_item_id,rlai.logic_attr_item_code,rlfi.logic_func_i_value," +
                    "rlfi.attribute1,rlfi.attribute2,rlfi.attribute3,rlfi.attribute4,rlfi.attribute5 " +
                    "from rcs_logic_func_g rlfg " +
                    "inner join rcs_logic_func_i rlfi on rlfg.logic_func_g_id=rlfi.logic_func_g_id " +
                    "and rlfg.enable_flag='Y' and rlfi.enable_flag='Y' " +
                    "inner join rcs_logic_attr_group rlag on rlfg.logic_attr_group_id=rlag.logic_attr_group_id " +
                    "and rlag.enable_flag='Y' " +
                    "inner join rcs_logic_attr_item rlai on rlfi.logic_func_item_id=rlai.logic_attr_item_id " +
                    "and rlai.enable_flag='Y' and rlag.logic_attr_group_id=rlai.logic_attr_group_id " +
                    "where rlfg.logic_func_id="+logicFuncId+
                    " order by rlfg.logic_func_g_id,rlfi.logic_func_item_id";
            List<Map<String,Object>> itemListLogicAttr=cFuncDbSqlExecute.ExecSelectSql(userName,sqlLogicAttr,false,request,apiRoutePath);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListLogicAttr,"","",0);
        }
        catch (Exception ex){
            errorMsg= "逻辑属性信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //3.逻辑Tag信息
    @RequestMapping(value = "/PmcCoreLogicFuncTagSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreLogicFuncTagSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/logic/PmcCoreLogicFuncTagSel";
        String selectResult="";
        String errorMsg="";
        String userName="LOGIC";
        try{
            //获取逻辑Tag信息
            String tagIdList=jsonParas.getString("tag_id_list");
            String sqlLogicTag="select sc.client_id,sc.client_code,stg.tag_group_id,stg.tag_group_code,st.tag_id,st.TAG_CODE " +
                    "from scada_client sc inner join scada_tag_group stg on sc.client_id=stg.client_id " +
                    "and sc.enable_flag='Y' and stg.enable_flag='Y' " +
                    "inner join scada_tag st on stg.tag_group_id=st.tag_group_id " +
                    "and st.enable_flag='Y' " +
                    "where st.tag_id in ("+tagIdList+") order by sc.client_id,stg.tag_group_id,st.tag_id";
            List<Map<String,Object>> itemListLogicTag=cFuncDbSqlExecute.ExecSelectSql(userName,sqlLogicTag,false,request,apiRoutePath);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListLogicTag,"","",0);
        }
        catch (Exception ex){
            errorMsg= "逻辑Tag信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}

