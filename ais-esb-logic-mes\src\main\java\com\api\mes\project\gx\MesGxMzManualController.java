package com.api.mes.project.gx;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.google.common.primitives.Ints;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 处理人工配组工艺逻辑
 * 1.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@RestController
@RequestMapping("/mes/project/gx")
public class MesGxMzManualController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //到位后清空人工配组临时表
    @Transactional
    @RequestMapping(value = "/MesGxMzManualOpClear", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzManualOpClear(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzManualOpClear";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String sqlDelete = "delete from c_mes_me_mz_manual " + "where station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlDelete, true, request, apiRoutePath);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "清空人工配组临时表异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //针对人工配组电芯数据进行存储
    @RequestMapping(value = "/MesGxMzManualSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzManualSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzManualSave";
        String transResult = "";
        String errorMsg = "";
        String meStationRecipeTable = "c_mes_me_recipe_data";
        String mzQualityTable = "c_mes_me_mz_quality";
        try {
            String prod_line_code = jsonParas.getString("prod_line_code");
            String prod_line_id = jsonParas.getString("prod_line_id");
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            String sqlNgCount = "select count(1) " + "from c_mes_me_mz_manual " + "where station_code='" + station_code + "' and check_ng_flag='Y'";
            int ngCount = cFuncDbSqlResolve.GetSelectCount(sqlNgCount);
            if (ngCount > 0) {
                errorMsg = "当前人工配组未完成,存在异常电芯数据,请修正电芯数据后再进行下一步作业";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String sqlMzManual = "select dx_id," + "COALESCE(mz_barcode,'') mz_barcode," + "dx_index,mk_num,dx_barcode,dx_gear," + "COALESCE(dx_ori_batch,'') dx_ori_batch," + "COALESCE(dx_ori_capacity,0) dx_ori_capacity," + "COALESCE(dx_ori_ocv4_pressure,0) dx_ori_ocv4_pressure," + "COALESCE(dx_ori_ocr4_air,0) dx_ori_ocr4_air," + "COALESCE(dx_ori_k_value,0) dx_ori_k_value," + "COALESCE(dx_ori_dcir,0) dx_ori_dcir," + "COALESCE(dx_ori_thickness,0) dx_ori_thickness," + "COALESCE(dx_ori_ocv4_time,'') dx_ori_ocv4_time,col_num," + "COALESCE(mz_capacity_diff,0) mz_capacity_diff," + "COALESCE(mz_capacity_plus,0) mz_capacity_plus," + "COALESCE(mz_min_capacity,0) mz_min_capacity," + "COALESCE(mz_max_capacity,0) mz_max_capacity," + "COALESCE(make_order,'') make_order," + "COALESCE(small_model_type,'') small_model_type " + "from c_mes_me_mz_manual " + "where station_code='" + station_code + "' " + "order by dx_index";
            List<Map<String, Object>> itemListMzManual = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzManual, false, request, apiRoutePath);
            if (itemListMzManual == null || itemListMzManual.size() <= 0) {
                errorMsg = "未能查询到人工配组电芯数据,查询是否因为未清空模组标识导致";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String mz_quality_id = CFuncUtilsSystem.CreateUUID(true);
            String make_order = itemListMzManual.get(0).get("make_order").toString();
            String small_model_type = itemListMzManual.get(0).get("small_model_type").toString();
            String mz_barcode = itemListMzManual.get(0).get("mz_barcode").toString();
            String mz_status = "OK";
            int col_num = Integer.parseInt(itemListMzManual.get(0).get("col_num").toString());
            int ng_rack = 0;
            int ng_code = 0;
            String ng_msg = "";
            double mz_capacity_diff = Double.parseDouble(itemListMzManual.get(0).get("mz_capacity_diff").toString());
            double mz_capacity_plus = Double.parseDouble(itemListMzManual.get(0).get("mz_capacity_plus").toString());
            double mz_min_capacity = Double.parseDouble(itemListMzManual.get(0).get("mz_min_capacity").toString());
            double mz_max_capacity = Double.parseDouble(itemListMzManual.get(0).get("mz_max_capacity").toString());

            if (mz_barcode.equals("")) {
                errorMsg = "查询到人工配组生成模组码为空,查询是否因为未清空模组标识导致";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //根据订单查询电芯配方
            String sqlMoRecipe = "select e.dx_num,COALESCE(e.dx_direct,0) dx_direct " + "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " + "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " + "on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_mz d " + "on c.recipe_id=d.recipe_id inner join c_mes_fmod_recipe_mz_dx e " + "on d.mz_id=e.mz_id " + "where a.enable_flag='Y' and c.enable_flag='Y' and d.enable_flag='Y' and e.enable_flag='Y' " + "and c.recipe_type='MODELDX' and a.prod_line_id=" + prod_line_id + " " + "and a.make_order='" + make_order + "' " + "order by e.dx_num";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe, false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}未配置模组电芯配方";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            int[] dxDirectArray = new int[52];
            for (Map<String, Object> map : itemListMoRecipe) {
                int dx_num = Integer.parseInt(map.get("dx_num").toString());
                int dx_direct = Integer.parseInt(map.get("dx_direct").toString());
                dxDirectArray[dx_num - 1] = dx_direct;
            }
            String tag_value_direct = Ints.join(",", dxDirectArray);

            //1.先插入电芯采集数据到电芯质量表
            InsertDxQuality(station_code, make_order, small_model_type, itemListMzManual);
            //2.保存模组与电芯关系,但是不保存模组数据,模组数据最后保存，防止异常问题
            InsertMzDxRel(mz_quality_id, itemListMzManual);
            //3.保存配方数据
            long tag_id = 107004003;
            Map<String, Object> mapParasItem = new HashMap<>();
            mapParasItem.put("item_date", item_date);
            mapParasItem.put("item_date_val", item_date_val);
            mapParasItem.put("recipe_data_id", CFuncUtilsSystem.CreateUUID(true));
            mapParasItem.put("prod_line_code", prod_line_code);
            mapParasItem.put("station_code", station_code);
            mapParasItem.put("station_id", Long.parseLong(station_id));
            mapParasItem.put("serial_num", mz_barcode);
            mapParasItem.put("tag_id", tag_id);
            mapParasItem.put("tag_des", "PLC模组电芯方向(MES后台生成)");
            mapParasItem.put("tag_value", tag_value_direct);
            mapParasItem.put("enable_flag", "Y");
            mongoTemplate.insert(mapParasItem, meStationRecipeTable);

            //4.保存模组数据
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("mz_quality_id", mz_quality_id);
            mapBigDataRow.put("make_order", make_order);
            mapBigDataRow.put("small_model_type", small_model_type);
            mapBigDataRow.put("station_code", station_code);
            mapBigDataRow.put("mz_barcode", mz_barcode);
            mapBigDataRow.put("mz_status", mz_status);
            mapBigDataRow.put("col_num", col_num);
            mapBigDataRow.put("ng_rack", ng_rack);
            mapBigDataRow.put("ng_code", ng_code);
            mapBigDataRow.put("ng_msg", ng_msg);
            mapBigDataRow.put("mz_capacity_diff", mz_capacity_diff);
            mapBigDataRow.put("mz_capacity_plus", mz_capacity_plus);
            mapBigDataRow.put("mz_min_capacity", mz_min_capacity);
            mapBigDataRow.put("mz_max_capacity", mz_max_capacity);
            mapBigDataRow.put("up_flag", "N");
            mapBigDataRow.put("up_ng_code", 0);
            mapBigDataRow.put("up_ng_msg", "");
            mapBigDataRow.put("enable_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, mzQualityTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, make_order + "," + mz_barcode, "", 0);
        } catch (Exception ex) {
            errorMsg = "针对人工配组电芯数据进行存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //针对人工配组电芯数据进行存储[离线]
    @RequestMapping(value = "/MesGxMzManualSave2", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzManualSave2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzManualSave2";
        String transResult = "";
        String errorMsg = "";
        String meStationRecipeTable = "c_mes_me_recipe_data";
        String mzQualityTable = "c_mes_me_mz_quality";
        try {
            String prod_line_code = "";
            String prod_line_id = "";
            String station_code = "";
            String station_id = "10";
            String station_code2 = jsonParas.getString("station_code");
            //查询生产线信息
            String sqlProdLine = "select sfpl.prod_line_id,sfpl.prod_line_code,sfs.station_code " + "from sys_fmod_station sfs inner join sys_fmod_prod_line sfpl " + "on sfs.prod_line_id=sfpl.prod_line_id " + "where sfs.station_id=" + station_id + "";
            List<Map<String, Object>> itemListProdLine = cFuncDbSqlExecute.ExecSelectSql(station_code2, sqlProdLine, false, request, apiRoutePath);
            prod_line_code = itemListProdLine.get(0).get("prod_line_code").toString();
            prod_line_id = itemListProdLine.get(0).get("prod_line_id").toString();
            station_code = itemListProdLine.get(0).get("station_code").toString();
            //
            String sqlNgCount = "select count(1) " + "from c_mes_me_mz_manual " + "where station_code='" + station_code2 + "' and check_ng_flag='Y'";
            int ngCount = cFuncDbSqlResolve.GetSelectCount(sqlNgCount);
            if (ngCount > 0) {
                errorMsg = "当前人工配组未完成,存在异常电芯数据,请修正电芯数据后再进行下一步作业";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String sqlMzManual = "select dx_id," + "COALESCE(mz_barcode,'') mz_barcode," + "dx_index,mk_num,dx_barcode,dx_gear," + "COALESCE(dx_ori_batch,'') dx_ori_batch," + "COALESCE(dx_ori_capacity,0) dx_ori_capacity," + "COALESCE(dx_ori_ocv4_pressure,0) dx_ori_ocv4_pressure," + "COALESCE(dx_ori_ocr4_air,0) dx_ori_ocr4_air," + "COALESCE(dx_ori_k_value,0) dx_ori_k_value," + "COALESCE(dx_ori_dcir,0) dx_ori_dcir," + "COALESCE(dx_ori_thickness,0) dx_ori_thickness," + "COALESCE(dx_ori_ocv4_time,'') dx_ori_ocv4_time,col_num," + "COALESCE(mz_capacity_diff,0) mz_capacity_diff," + "COALESCE(mz_capacity_plus,0) mz_capacity_plus," + "COALESCE(mz_min_capacity,0) mz_min_capacity," + "COALESCE(mz_max_capacity,0) mz_max_capacity," + "COALESCE(make_order,'') make_order," + "COALESCE(small_model_type,'') small_model_type " + "from c_mes_me_mz_manual " + "where station_code='" + station_code2 + "' " + "order by dx_index";
            List<Map<String, Object>> itemListMzManual = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzManual, false, request, apiRoutePath);
            if (itemListMzManual == null || itemListMzManual.size() <= 0) {
                errorMsg = "未能查询到人工配组电芯数据,查询是否因为未清空模组标识导致";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String mz_quality_id = CFuncUtilsSystem.CreateUUID(true);
            String make_order = itemListMzManual.get(0).get("make_order").toString();
            String small_model_type = itemListMzManual.get(0).get("small_model_type").toString();
            String mz_barcode = itemListMzManual.get(0).get("mz_barcode").toString();
            String mz_status = "OK";
            int col_num = Integer.parseInt(itemListMzManual.get(0).get("col_num").toString());
            int ng_rack = 0;
            int ng_code = 0;
            String ng_msg = "";
            double mz_capacity_diff = Double.parseDouble(itemListMzManual.get(0).get("mz_capacity_diff").toString());
            double mz_capacity_plus = Double.parseDouble(itemListMzManual.get(0).get("mz_capacity_plus").toString());
            double mz_min_capacity = Double.parseDouble(itemListMzManual.get(0).get("mz_min_capacity").toString());
            double mz_max_capacity = Double.parseDouble(itemListMzManual.get(0).get("mz_max_capacity").toString());

            if (mz_barcode.equals("")) {
                errorMsg = "查询到人工配组生成模组码为空,查询是否因为未清空模组标识导致";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //根据订单查询电芯配方
            String sqlMoRecipe = "select e.dx_num,COALESCE(e.dx_direct,0) dx_direct " + "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " + "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " + "on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_mz d " + "on c.recipe_id=d.recipe_id inner join c_mes_fmod_recipe_mz_dx e " + "on d.mz_id=e.mz_id " + "where a.enable_flag='Y' and c.enable_flag='Y' and d.enable_flag='Y' and e.enable_flag='Y' " + "and c.recipe_type='MODELDX' and a.prod_line_id=" + prod_line_id + " " + "and a.make_order='" + make_order + "' " + "order by e.dx_num";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe, false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}未配置模组电芯配方";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            int[] dxDirectArray = new int[52];
            for (Map<String, Object> map : itemListMoRecipe) {
                int dx_num = Integer.parseInt(map.get("dx_num").toString());
                int dx_direct = Integer.parseInt(map.get("dx_direct").toString());
                dxDirectArray[dx_num - 1] = dx_direct;
            }
            String tag_value_direct = Ints.join(",", dxDirectArray);

            //1.先插入电芯采集数据到电芯质量表
            InsertDxQuality(station_code, make_order, small_model_type, itemListMzManual);
            //2.保存模组与电芯关系,但是不保存模组数据,模组数据最后保存，防止异常问题
            InsertMzDxRel(mz_quality_id, itemListMzManual);
            //3.保存配方数据
            long tag_id = 107004003;
            Map<String, Object> mapParasItem = new HashMap<>();
            mapParasItem.put("item_date", item_date);
            mapParasItem.put("item_date_val", item_date_val);
            mapParasItem.put("recipe_data_id", CFuncUtilsSystem.CreateUUID(true));
            mapParasItem.put("prod_line_code", prod_line_code);
            mapParasItem.put("station_code", station_code);
            mapParasItem.put("station_id", Long.parseLong(station_id));
            mapParasItem.put("serial_num", mz_barcode);
            mapParasItem.put("tag_id", tag_id);
            mapParasItem.put("tag_des", "PLC模组电芯方向(MES后台生成)");
            mapParasItem.put("tag_value", tag_value_direct);
            mapParasItem.put("enable_flag", "Y");
            mongoTemplate.insert(mapParasItem, meStationRecipeTable);

            //4.保存模组数据
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("mz_quality_id", mz_quality_id);
            mapBigDataRow.put("make_order", make_order);
            mapBigDataRow.put("small_model_type", small_model_type);
            mapBigDataRow.put("station_code", station_code);
            mapBigDataRow.put("mz_barcode", mz_barcode);
            mapBigDataRow.put("mz_status", mz_status);
            mapBigDataRow.put("col_num", col_num);
            mapBigDataRow.put("ng_rack", ng_rack);
            mapBigDataRow.put("ng_code", ng_code);
            mapBigDataRow.put("ng_msg", ng_msg);
            mapBigDataRow.put("mz_capacity_diff", mz_capacity_diff);
            mapBigDataRow.put("mz_capacity_plus", mz_capacity_plus);
            mapBigDataRow.put("mz_min_capacity", mz_min_capacity);
            mapBigDataRow.put("mz_max_capacity", mz_max_capacity);
            mapBigDataRow.put("up_flag", "N");
            mapBigDataRow.put("up_ng_code", 0);
            mapBigDataRow.put("up_ng_msg", "");
            mapBigDataRow.put("enable_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, mzQualityTable);

            //存储上线信息
            String url = "http://127.0.0.1:9090/aisEsbApi/mes/core/aps/MesCoreApsMoOnlineInsert";
            JSONObject jbParas = new JSONObject();
            jbParas.put("station_code", station_code);
            jbParas.put("make_order", make_order);
            jbParas.put("prod_line_code", prod_line_code);
            jbParas.put("serial_type", "MZ");
            jbParas.put("container_num", "OFFLINE");
            jbParas.put("print_barcode", "");
            jbParas.put("serial_num_list", mz_barcode);
            jbParas.put("repair_flag", "N");
            JSONObject jbResult = cFuncUtilsRest.PostJbBackJb(url, jbParas);
            if (jbResult == null) {
                errorMsg = "保存模组码{" + mz_barcode + "}线首失败";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            int backCode = jbResult.getInteger("code");
            String backMsg = jbResult.getString("msg");
            if (backCode != 0) {
                errorMsg = "保存模组码{" + mz_barcode + "}线首失败{" + backMsg + "}";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, make_order + "," + mz_barcode, "", 0);
        } catch (Exception ex) {
            errorMsg = "针对离线人工配组电芯数据进行存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //保存电芯质量数据
    private void InsertDxQuality(String station_code, String make_order, String small_model_type, List<Map<String, Object>> itemListMzManual) throws Exception {
        try {
            String dxQualityTable = "c_mes_me_dx_quality";
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            for (Map<String, Object> map : itemListMzManual) {
                double norMalValue = 0;
                String dx_quality_id = CFuncUtilsSystem.CreateUUID(true);
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("dx_quality_id", dx_quality_id);
                mapBigDataRow.put("dx_barcode", map.get("dx_barcode").toString());
                mapBigDataRow.put("dx_location_num", 1);
                mapBigDataRow.put("make_order", make_order);
                mapBigDataRow.put("small_model_type", small_model_type);
                mapBigDataRow.put("box_barcode", "");
                mapBigDataRow.put("station_code", station_code);
                mapBigDataRow.put("dx_gear", Integer.parseInt(map.get("dx_gear").toString()));
                mapBigDataRow.put("dx_ori_batch", map.get("dx_ori_batch").toString());
                mapBigDataRow.put("dx_ori_capacity", Double.parseDouble(map.get("dx_ori_capacity").toString()));
                mapBigDataRow.put("dx_ori_ocv4_pressure", Double.parseDouble(map.get("dx_ori_ocv4_pressure").toString()));
                mapBigDataRow.put("dx_ori_ocr4_air", Double.parseDouble(map.get("dx_ori_ocr4_air").toString()));
                mapBigDataRow.put("dx_ori_k_value", Double.parseDouble(map.get("dx_ori_k_value").toString()));
                mapBigDataRow.put("dx_ori_dcir", Double.parseDouble(map.get("dx_ori_dcir").toString()));
                mapBigDataRow.put("dx_ori_thickness", Double.parseDouble(map.get("dx_ori_thickness").toString()));
                mapBigDataRow.put("dx_ori_ocv4_time", map.get("dx_ori_ocv4_time").toString());
                mapBigDataRow.put("dx_pressure_kl_value", norMalValue);
                mapBigDataRow.put("dx_pressure_kl_time", CFuncUtilsSystem.GetNowDateTime(""));
                mapBigDataRow.put("dx_pressure_fd_value", norMalValue);
                mapBigDataRow.put("dx_dianzu_value", norMalValue);
                mapBigDataRow.put("dx_k2_value", norMalValue);
                mapBigDataRow.put("dx_status", "OK");
                mapBigDataRow.put("ng_rack", 0);
                mapBigDataRow.put("ng_code", 0);
                mapBigDataRow.put("ng_msg", "");
                mapBigDataRow.put("up_flag", "N");
                lstDocuments.add(mapBigDataRow);
            }
            mongoTemplate.insert(lstDocuments, dxQualityTable);
        } catch (Exception ex) {
            throw new Exception("保存电芯质量数据异常:" + ex.getMessage());
        }
    }

    //保存模组与电芯关系
    private void InsertMzDxRel(String mz_quality_id, List<Map<String, Object>> itemListMzManual) throws Exception {
        try {
            String mzDxRelTable = "c_mes_me_mz_dx_rel";
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            for (Map<String, Object> map : itemListMzManual) {
                Map<String, Object> mapDataItem = new HashMap<>();
                String mz_dx_rel_id = CFuncUtilsSystem.CreateUUID(true);
                String dx_barcode = map.get("dx_barcode").toString();
                int mk_num = Integer.parseInt(map.get("mk_num").toString());
                mapDataItem.put("item_date", item_date);
                mapDataItem.put("item_date_val", item_date_val);
                mapDataItem.put("mz_dx_rel_id", mz_dx_rel_id);
                mapDataItem.put("mz_quality_id", mz_quality_id);
                mapDataItem.put("dx_barcode", dx_barcode);
                mapDataItem.put("mk_num", mk_num);
                mapDataItem.put("mz_status", "OK");
                mapDataItem.put("dx_index", Integer.parseInt(map.get("dx_index").toString()));
                lstDocuments.add(mapDataItem);
            }
            mongoTemplate.insert(lstDocuments, mzDxRelTable);
        } catch (Exception ex) {
            throw new Exception("保存模组与电芯关系异常:" + ex.getMessage());
        }
    }

    //根据模组码查询模组配方信息
    @RequestMapping(value = "/MesGxMzManualRepairBarCodeSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxMzManualRepairBarCodeSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxMzManualRepairBarCodeSelect";
        String transResult = "";
        String errorMsg = "";
        String mzQualityTable = "c_mes_me_mz_quality";
        try {
            String prod_line_id = jsonParas.getString("prod_line_id");
            String station_code = jsonParas.getString("station_code");
            String mz_barcode = jsonParas.getString("mz_barcode");
            //1.先判断是否为模组码
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mz_barcode").is(mz_barcode));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(mzQualityTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            String make_order = "";
            String small_model_type = "";
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                make_order = docItemBigData.getString("make_order");
                small_model_type = docItemBigData.getString("small_model_type");
                iteratorBigData.close();
            } else {
                errorMsg = "未能根据电模组码{" + mz_barcode + "}查找到相关上线信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            //根据订单查找配方信息
            String sqlMzRecipe = "select " + "'" + mz_barcode + "' as mz_barcode," + "'" + small_model_type + "' as small_model_type," + "a.mz_num,b.recipe_name,d.make_order," + "COALESCE(a.dx_width_size,0) dx_width_size," + "COALESCE(a.dx_height_size,0) dx_height_size," + "COALESCE(a.dx_column,0) dx_column," + "COALESCE(a.dx_layer_num,0) dx_layer_num,a.dx_count," + "COALESCE(a.mz_p_count,0) mz_p_count," + "COALESCE(a.mz_s_count,0) mz_s_count," + "COALESCE(a.mz_tj_technology,0) mz_tj_technology," + "COALESCE(a.mz_type,'0') mz_type," + "COALESCE(a.dx_lk_layer_num,'0') dx_lk_layer_num," + "COALESCE(a.mz_end_width,'0') mz_end_width," + "COALESCE(a.mid_partition,'0') mid_partition," + "COALESCE(a.mid_partition_layer,'0') mid_partition_layer," + "COALESCE(a.mid_partition_breadth,'0') mid_partition_breadth " + "from c_mes_fmod_recipe_mz a inner join c_mes_fmod_recipe b " + "on a.recipe_id=b.recipe_id inner join c_mes_aps_plan_mo_recipe c " + "on b.recipe_id=c.recipe_id inner join c_mes_aps_plan_mo d " + "on c.mo_id=d.mo_id " + "where d.prod_line_id=" + prod_line_id + " " + "and d.make_order='" + make_order + "' " + "and b.recipe_type='MODELDX' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMzRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzRecipe, false, request, apiRoutePath);
            if (itemListMzRecipe == null || itemListMzRecipe.size() <= 0) {
                errorMsg = "未能根据模组码{" + mz_barcode + "},查询到的订单号{" + make_order + "}查找到订单对应的模组配方信息,请确认是否删除了配方";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMzRecipe, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据模组码查询模组配方信息异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
