package com.api.dcs.core.wms;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 库存公共方法
 * 1.根据库位号查询库位信息
 * 2.根据机型查询机型信息
 * 3.查找入库库位以及相关信息
 * 4.查找出库库位以及相关信息
 * 5.查找倒垛来源库位以及相关信息
 * 6.查找倒垛目的库位以及相关信息
 * 7.查找抛丸来源库位以及相关信息
 * 8.查找抛丸目标库位以及相关信息
 * 9.更新库存
 * 10.增加库位事件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Service
public class DcsStockCommonFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.根据库位号查询库位信息
    public Map<String, Object> GetStockInfo(String stock_code) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlStockSel="select " +
                "stock_id,ware_house,stock_group_code,stock_des,stock_flag,model_flag," +
                "COALESCE(model_id,0) model_id," +
                "location_x,location_y,COALESCE(location_z,0) location_z," +
                "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count," +
                "COALESCE(min_count,0) min_count,COALESCE(max_count,0) max_count," +
                "COALESCE(stock_status,'') stock_status," +
                "COALESCE(stock_region_code,'') stock_region_code,stock_order " +
                "from b_dcs_wms_fmod_stock " +
                "where stock_code='"+stock_code+"' and enable_flag='Y' " +
                "order by stock_id LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                false,null,"");
        if(itemListStock!=null && itemListStock.size()>0){
            mapItem=itemListStock.get(0);
        }
        return mapItem;
    }

    //2.根据机型查询机型信息
    public Map<String, Object> GetModelInfo(String model_id,String model_type) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlModelSel="select " +
                "model_id,material_code,material_des," +
                "COALESCE(material_draw,'') material_draw," +
                "model_type,m_length,m_width,m_height,m_weight," +
                "COALESCE(m_texture,'') m_texture," +
                "COALESCE(cut_texture,'') cut_texture,data_from," +
                "COALESCE(npa_startx,0) npa_startx,COALESCE(npa_starty,0) npa_starty " +
                "from b_dcs_fmod_model " +
                "where enable_flag='Y' ";
        if(model_id!=null && !model_id.equals("")){
            sqlModelSel+=" and model_id="+model_id+"";
        }
        else{
            if(model_type!=null && !model_type.equals("")){
                sqlModelSel+=" and model_type='"+model_type+"'";
            }
        }
        sqlModelSel+=" order by model_id LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListModel=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlModelSel,
                false,null,"");
        if(itemListModel!=null && itemListModel.size()>0){
            mapItem=itemListModel.get(0);
        }
        return mapItem;
    }

    //3.查找入库库位以及相关信息
    public Map<String, Object> GetKwStockIn(String ware_house,String model_id,Float m_height) throws Exception{
        Map<String, Object> mapItem=null;
        //1.先查找已有该型号,且库存量>0以及库存量<最大库存
        String sqlStockSel="select " +
                "stock_id,stock_code,location_x,location_y," +
                "COALESCE(location_z,0) location_z," +
                "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                "from b_dcs_wms_fmod_stock " +
                "where enable_flag='Y' and stock_status='NORMAL' " +
                "and stock_region_code='KW' " +
                "and stock_count>0 and stock_count<max_count " +
                "and ware_house='"+ware_house+"' and model_id="+model_id+" " +
                "and stock_id not in " +
                "(select distinct stock_id from b_dcs_wms_me_stock " +
                "where alarm_flag='N' group by stock_id having count(stock_id)>0) "+
                "order by stock_order LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                false,null,"");
        if(itemListStock==null || itemListStock.size()<=0){
            //2.固定型号(Y)+库存量=0
            sqlStockSel="select " +
                    "stock_id,stock_code,location_x,location_y," +
                    "COALESCE(location_z,0) location_z," +
                    "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                    "from b_dcs_wms_fmod_stock " +
                    "where enable_flag='Y' and stock_status='NORMAL' " +
                    "and stock_region_code='KW' " +
                    "and stock_count=0 and model_flag='Y' " +
                    "and ware_house='"+ware_house+"' and model_id="+model_id+" " +
                    "order by stock_order LIMIT 1 OFFSET 0";
            itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                    false,null,"");
            if(itemListStock==null || itemListStock.size()<=0){
                //3.是否固定型号(N)+库存量=0
                sqlStockSel="select " +
                        "stock_id,stock_code,location_x,location_y," +
                        "COALESCE(location_z,0) location_z," +
                        "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                        "from b_dcs_wms_fmod_stock " +
                        "where enable_flag='Y' and stock_status='NORMAL' " +
                        "and stock_region_code='KW' " +
                        "and stock_count=0 and model_flag='N' " +
                        "and ware_house='"+ware_house+"' " +
                        "order by stock_order LIMIT 1 OFFSET 0";
                itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                        false,null,"");
                if(itemListStock==null || itemListStock.size()<=0){
                    return mapItem;
                }
            }
        }
        //4.计算坐标
        mapItem=itemListStock.get(0);
        Integer location_z=Integer.parseInt(mapItem.get("location_z").toString());
        String z_math_way=mapItem.get("z_math_way").toString();//计算方法:PLUS(+), MINUS(-)
        Integer stock_count=Integer.parseInt(mapItem.get("stock_count").toString());
        if(z_math_way==null || z_math_way.equals("")) z_math_way="PLUS";
        int plus_height=(int)((int)(stock_count+1)*(float)m_height);
        if(z_math_way.equals("PLUS")){
            location_z=location_z+plus_height;
        }
        else{
            location_z=location_z-plus_height;
        }
        mapItem.put("location_z",location_z);
        return mapItem;
    }

    //4.查找出库库位以及相关信息
    public Map<String, Object> GetKwStockOut(String ware_house,String model_id,Float m_height,String is_auto_blast) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlStockSel="select " +
                "stock_id,stock_code,location_x,location_y," +
                "COALESCE(location_z,0) location_z," +
                "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                "from b_dcs_wms_fmod_stock " +
                "where enable_flag='Y' and stock_status='NORMAL' " +
                "and stock_region_code='KW' " +
                "and stock_count>0 " +
                "and ware_house='"+ware_house+"' and model_id="+model_id+" " +
                "order by stock_order LIMIT 1 OFFSET 0";
        if(is_auto_blast.equals("Y")){
            //需要已经完成抛丸的库位
            sqlStockSel="select " +
                    "stock_id,stock_code,location_x,location_y," +
                    "COALESCE(location_z,0) location_z," +
                    "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                    "from b_dcs_wms_fmod_stock " +
                    "where enable_flag='Y' and stock_status='NORMAL' " +
                    "and stock_region_code='KW' " +
                    "and stock_count>0 " +
                    "and ware_house='"+ware_house+"' and model_id="+model_id+" " +
                    "and stock_id not in " +
                    "(select distinct stock_id from b_dcs_wms_me_stock " +
                    "where alarm_flag='Y' group by stock_id having count(stock_id)>0) "+
                    "order by stock_order LIMIT 1 OFFSET 0";
        }
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                false,null,"");
        if(itemListStock!=null && itemListStock.size()>0){
            mapItem=itemListStock.get(0);
            Integer location_z=Integer.parseInt(mapItem.get("location_z").toString());
            String z_math_way=mapItem.get("z_math_way").toString();//计算方法:PLUS(+), MINUS(-)
            Integer stock_count=Integer.parseInt(mapItem.get("stock_count").toString());
            if(z_math_way==null || z_math_way.equals("")) z_math_way="PLUS";
            int plus_height=(int)((int)(stock_count)*(float)m_height);
            if(z_math_way.equals("PLUS")){
                location_z=location_z+plus_height;
            }
            else{
                location_z=location_z-plus_height;
            }
            mapItem.put("location_z",location_z);
        }
        return mapItem;
    }

    //5.查找倒垛来源库位以及相关信息
    public Map<String, Object> GetKwStockDdFrom(String stock_code,String model_id,Float m_height) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlStockSel="select " +
                "stock_id,stock_code,location_x,location_y," +
                "COALESCE(location_z,0) location_z," +
                "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                "from b_dcs_wms_fmod_stock " +
                "where enable_flag='Y' and stock_status='NORMAL' " +
                "and stock_region_code='KW' " +
                "and stock_count>0 " +
                "and stock_code='"+stock_code+"' and model_id="+model_id+" " +
                "order by stock_order LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                false,null,"");
        if(itemListStock==null || itemListStock.size()<=0){
            throw new Exception("倒垛来源库位{"+stock_code+"}失效或者不存在或者数量<=0");
        }
        mapItem=itemListStock.get(0);
        Integer location_z=Integer.parseInt(mapItem.get("location_z").toString());
        String z_math_way=mapItem.get("z_math_way").toString();//计算方法:PLUS(+), MINUS(-)
        Integer stock_count=Integer.parseInt(mapItem.get("stock_count").toString());
        if(z_math_way==null || z_math_way.equals("")) z_math_way="PLUS";
        int plus_height=(int)((int)(stock_count)*(float)m_height);
        if(z_math_way.equals("PLUS")){
            location_z=location_z+plus_height;
        }
        else{
            location_z=location_z-plus_height;
        }
        mapItem.put("location_z",location_z);
        return mapItem;
    }

    //6.查找倒垛目的库位以及相关信息
    public Map<String, Object> GetKwStockDdTo(String stock_code,String model_id,Float m_height) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlStockSel="select " +
                "stock_id,stock_code,location_x,location_y," +
                "COALESCE(location_z,0) location_z," +
                "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count," +
                "COALESCE(model_id,0) model_id,COALESCE(max_count,0) max_count " +
                "from b_dcs_wms_fmod_stock " +
                "where enable_flag='Y' and stock_status='NORMAL' " +
                "and stock_region_code='KW' " +
                "and stock_code='"+stock_code+"' " +
                "order by stock_order LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                false,null,"");
        if(itemListStock==null || itemListStock.size()<=0){
            throw new Exception("倒垛目标库位{"+stock_code+"}失效或者不存在");
        }
        mapItem=itemListStock.get(0);
        Integer location_z=Integer.parseInt(mapItem.get("location_z").toString());
        String z_math_way=mapItem.get("z_math_way").toString();//计算方法:PLUS(+), MINUS(-)
        Integer stock_count=Integer.parseInt(mapItem.get("stock_count").toString());
        if(z_math_way==null || z_math_way.equals("")) z_math_way="PLUS";
        String model_id_stock=mapItem.get("model_id").toString();
        Integer max_count=Integer.parseInt(mapItem.get("max_count").toString());
        if(!model_id_stock.equals("0") && !model_id_stock.equals(model_id)){
            throw new Exception("倒垛目标库位{"+stock_code+"}机型与倒垛来源机型不一致");
        }
        if(stock_count+1>max_count){
            throw new Exception("倒垛目标库位{"+stock_code+"}库存量超出最大数量{"+max_count+"}");
        }
        int plus_height=(int)((int)(stock_count+1)*(float)m_height);
        if(z_math_way.equals("PLUS")){
            location_z=location_z+plus_height;
        }
        else{
            location_z=location_z-plus_height;
        }
        mapItem.put("location_z",location_z);
        return mapItem;
    }

    //7.查找抛丸来源库位以及相关信息
    public Map<String, Object> GetKwStockPwFrom(String stock_code,String model_id,Float m_height) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlStockSel="select " +
                "stock_id,stock_code,location_x,location_y," +
                "COALESCE(location_z,0) location_z," +
                "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                "from b_dcs_wms_fmod_stock " +
                "where enable_flag='Y' and stock_status='NORMAL' " +
                "and stock_region_code='KW' " +
                "and stock_count>0 " +
                "and stock_code='"+stock_code+"' and model_id="+model_id+" " +
                "order by stock_order LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                false,null,"");
        if(itemListStock==null || itemListStock.size()<=0){
            throw new Exception("抛丸来源库位{"+stock_code+"}失效或者不存在或者数量<=0");
        }
        mapItem=itemListStock.get(0);
        Integer location_z=Integer.parseInt(mapItem.get("location_z").toString());
        String z_math_way=mapItem.get("z_math_way").toString();//计算方法:PLUS(+), MINUS(-)
        Integer stock_count=Integer.parseInt(mapItem.get("stock_count").toString());
        if(z_math_way==null || z_math_way.equals("")) z_math_way="PLUS";
        int plus_height=(int)((int)(stock_count)*(float)m_height);
        if(z_math_way.equals("PLUS")){
            location_z=location_z+plus_height;
        }
        else{
            location_z=location_z-plus_height;
        }
        mapItem.put("location_z",location_z);
        return mapItem;
    }

    //8.查找抛丸目标库位以及相关信息
    public Map<String, Object> GetKwStockPwTo(String ware_house,String model_id,Float m_height) throws Exception{
        Map<String, Object> mapItem=null;
        //1.先查找已有该型号,且库存量>0以及库存量<最大库存
        String sqlStockSel="select " +
                "stock_id,stock_code,location_x,location_y," +
                "COALESCE(location_z,0) location_z," +
                "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                "from b_dcs_wms_fmod_stock " +
                "where enable_flag='Y' and stock_status='NORMAL' " +
                "and stock_region_code='KW' " +
                "and stock_count>0 and stock_count<max_count " +
                "and ware_house='"+ware_house+"' and model_id="+model_id+" " +
                "and stock_id not in " +
                "(select distinct stock_id from b_dcs_wms_me_stock " +
                "where alarm_flag='Y' group by stock_id having count(stock_id)>0) " +
                "order by stock_order LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                false,null,"");
        if(itemListStock==null || itemListStock.size()<=0){
            //2.固定型号(Y)+库存量=0
            sqlStockSel="select " +
                    "stock_id,stock_code,location_x,location_y," +
                    "COALESCE(location_z,0) location_z," +
                    "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                    "from b_dcs_wms_fmod_stock " +
                    "where enable_flag='Y' and stock_status='NORMAL' " +
                    "and stock_region_code='KW' " +
                    "and stock_count=0 and model_flag='Y' " +
                    "and ware_house='"+ware_house+"' and model_id="+model_id+" " +
                    "order by stock_order LIMIT 1 OFFSET 0";
            itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                    false,null,"");
            if(itemListStock==null || itemListStock.size()<=0){
                //3.是否固定型号(N)+库存量=0
                sqlStockSel="select " +
                        "stock_id,stock_code,location_x,location_y," +
                        "COALESCE(location_z,0) location_z," +
                        "z_set_flag,COALESCE(z_math_way,'') z_math_way,stock_count " +
                        "from b_dcs_wms_fmod_stock " +
                        "where enable_flag='Y' and stock_status='NORMAL' " +
                        "and stock_region_code='KW' " +
                        "and stock_count=0 and model_flag='N' " +
                        "and ware_house='"+ware_house+"' " +
                        "order by stock_order LIMIT 1 OFFSET 0";
                itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                        false,null,"");
                if(itemListStock==null || itemListStock.size()<=0){
                    return mapItem;
                }
            }
        }
        //4.计算坐标
        mapItem=itemListStock.get(0);
        Integer location_z=Integer.parseInt(mapItem.get("location_z").toString());
        String z_math_way=mapItem.get("z_math_way").toString();//计算方法:PLUS(+), MINUS(-)
        Integer stock_count=Integer.parseInt(mapItem.get("stock_count").toString());
        if(z_math_way==null || z_math_way.equals("")) z_math_way="PLUS";
        int plus_height=(int)((int)(stock_count+1)*(float)m_height);
        if(z_math_way.equals("PLUS")){
            location_z=location_z+plus_height;
        }
        else{
            location_z=location_z-plus_height;
        }
        mapItem.put("location_z",location_z);
        return mapItem;
    }

    //9.更新库存
    public void UpdateKwStockCount(String userID,Boolean isAdd,String car_code,Map<String, Object> mapParas) throws Exception{
        //1.获取参数
        String stock_code=mapParas.get("stock_code").toString();
        String model_id=mapParas.get("model_id").toString();

        //2.根据库位查询
        String sqlStockSel="select " +
                "stock_id,stock_count,model_flag " +
                "from b_dcs_wms_fmod_stock " +
                "where stock_code='"+stock_code+"' " +
                "order by stock_id LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql(userID,sqlStockSel,
                false,null,"");
        if(itemListStock==null || itemListStock.size()<=0){
            throw new Exception("未能根据库位{"+stock_code+"}查找到库位基础信息");
        }
        String stock_id=itemListStock.get(0).get("stock_id").toString();
        String model_flag=itemListStock.get(0).get("model_flag").toString();
        Integer stock_count=Integer.parseInt(itemListStock.get(0).get("stock_count").toString());
        if(stock_count<0) stock_count=0;
        if(isAdd) stock_count++;
        else stock_count--;
        if(stock_count<0) stock_count=0;

        //3.库存增加或者减少
        String sqlStockUpd="";
        if(isAdd){
            if(stock_count>1){
                sqlStockUpd="update b_dcs_wms_fmod_stock set " +
                        "stock_count="+stock_count+" " +
                        "where stock_id="+stock_id;
            }
            else{
                if(!model_flag.equals("Y")){
                    sqlStockUpd="update b_dcs_wms_fmod_stock set " +
                            "stock_count="+stock_count+"," +
                            "model_id="+model_id+" " +
                            "where stock_id="+stock_id;
                }
                else{
                    sqlStockUpd="update b_dcs_wms_fmod_stock set " +
                            "stock_count="+stock_count+" " +
                            "where stock_id="+stock_id;
                }
            }
        }
        else{
            if(stock_count>0){
                sqlStockUpd="update b_dcs_wms_fmod_stock set " +
                        "stock_count="+stock_count+" " +
                        "where stock_id="+stock_id;
            }
            else{
                if(!model_flag.equals("Y")){
                    sqlStockUpd="update b_dcs_wms_fmod_stock set " +
                            "stock_count="+stock_count+"," +
                            "model_id=0 " +
                            "where stock_id="+stock_id;
                }
                else{
                    sqlStockUpd="update b_dcs_wms_fmod_stock set " +
                            "stock_count="+stock_count+" " +
                            "where stock_id="+stock_id;
                }
            }
        }
        cFuncDbSqlExecute.ExecUpdateSql(userID,sqlStockUpd,false,null,"");

        //4.修改明细
        String sqlMeStockUpd="";
        if(isAdd){
            String nowDateTime= CFuncUtilsSystem.GetNowDateTime("");
            Long stock_d_id=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_me_stock_id_seq",true);
            String task_from=mapParas.get("task_from").toString();
            String task_num=mapParas.get("task_num").toString();
            String serial_num=mapParas.get("serial_num").toString();
            String lot_num=mapParas.get("lot_num").toString();
            String location_z=mapParas.get("location_z").toString();
            String task_way=mapParas.get("task_way").toString();
            String task_type=mapParas.get("task_type").toString();
            String xz_location_y=mapParas.get("xz_location_y").toString();
            String xz_finish_flag=mapParas.get("xz_finish_flag").toString();
            String alarm_flag=mapParas.get("alarm_flag").toString();
            if(alarm_flag==null || alarm_flag.equals("")) alarm_flag="Y";
            if(xz_location_y==null || xz_location_y.equals("")) xz_location_y="0";
            if(xz_finish_flag==null || xz_finish_flag.equals("")) xz_finish_flag="N";
            Integer stock_index=stock_count;
            sqlMeStockUpd="insert into b_dcs_wms_me_stock " +
                    "(created_by,creation_date,stock_d_id,stock_id,task_from," +
                    "task_num,serial_num,lot_num,stock_index,location_z," +
                    "lock_flag,task_way,task_type,xz_location_y,xz_finish_flag," +
                    "stock_d_time,alarm_flag,car_code) values " +
                    "('"+userID+"','"+nowDateTime+"',"+stock_d_id+","+stock_id+",'"+task_from+"'," +
                    "'"+task_num+"','"+serial_num+"','"+lot_num+"',"+stock_index+","+location_z+"," +
                    "'N','"+task_way+"','"+task_type+"',"+xz_location_y+",'"+xz_finish_flag+"'," +
                    "'"+nowDateTime+"','"+alarm_flag+"','"+car_code+"')";
        }
        else{
            sqlMeStockUpd="delete from b_dcs_wms_me_stock " +
                    "where stock_d_id in " +
                    "(select stock_d_id from b_dcs_wms_me_stock " +
                    "where stock_id="+stock_id+" " +
                    "order by stock_d_id desc LIMIT 1 OFFSET 0)";
        }
        cFuncDbSqlExecute.ExecUpdateSql(userID,sqlMeStockUpd,false,null,"");
    }

    //10.增加库位事件
    public void StockEventIns(String userID,String car_code,String stock_code,String stock_way,
                              Map<String, Object> mapParas) throws Exception{
        String meStockEventTable="b_dcs_wms_me_stock_event";
        //1.根据库位查询
        String sqlStockSel="select " +
                "stock_id,stock_count " +
                "from b_dcs_wms_fmod_stock " +
                "where stock_code='"+stock_code+"' " +
                "order by stock_id LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql(userID,sqlStockSel,
                false,null,"");
        if(itemListStock==null || itemListStock.size()<=0){
            throw new Exception("未能根据库位{"+stock_code+"}查找到库位基础信息");
        }
        String stock_id=itemListStock.get(0).get("stock_id").toString();
        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
        String stock_event_id=CFuncUtilsSystem.CreateUUID(true);
        Map<String, Object> mapBigDataRow=new HashMap<>();
        mapBigDataRow.put("item_date",item_date);
        mapBigDataRow.put("item_date_val",item_date_val);
        mapBigDataRow.put("stock_event_id",stock_event_id);
        mapBigDataRow.put("stock_id",Long.parseLong(stock_id));
        mapBigDataRow.put("car_code",car_code);
        mapBigDataRow.put("stock_code",stock_code);
        mapBigDataRow.put("stock_way",stock_way);
        mapBigDataRow.put("task_way",mapParas.get("task_way").toString());
        mapBigDataRow.put("task_type",mapParas.get("task_type").toString());
        mapBigDataRow.put("task_from",mapParas.get("task_from").toString());
        mapBigDataRow.put("task_num",mapParas.get("task_num").toString());
        mapBigDataRow.put("serial_num",mapParas.get("serial_num").toString());
        mapBigDataRow.put("lot_num",mapParas.get("lot_num").toString());
        mapBigDataRow.put("model_type",mapParas.get("model_type").toString());
        mapBigDataRow.put("material_code",mapParas.get("material_code").toString());
        mapBigDataRow.put("material_des",mapParas.get("material_des").toString());
        mapBigDataRow.put("material_draw",mapParas.get("material_draw").toString());
        mapBigDataRow.put("m_length",Double.parseDouble(mapParas.get("m_length").toString()));
        mapBigDataRow.put("m_width",Double.parseDouble(mapParas.get("m_width").toString()));
        mapBigDataRow.put("m_height",Double.parseDouble(mapParas.get("m_height").toString()));
        mapBigDataRow.put("m_weight",Double.parseDouble(mapParas.get("m_weight").toString()));
        mapBigDataRow.put("m_texture",mapParas.get("m_texture").toString());
        mapBigDataRow.put("from_stock_code",mapParas.get("from_stock_code").toString());
        mapBigDataRow.put("from_location_x",mapParas.get("from_location_x").toString());
        mapBigDataRow.put("from_location_y",mapParas.get("from_location_y").toString());
        mapBigDataRow.put("from_location_z",mapParas.get("from_location_z").toString());
        mapBigDataRow.put("to_stock_code",mapParas.get("to_stock_code").toString());
        mapBigDataRow.put("to_location_x",mapParas.get("to_location_x").toString());
        mapBigDataRow.put("to_location_y",mapParas.get("to_location_y").toString());
        mapBigDataRow.put("to_location_z",mapParas.get("to_location_z").toString());
        mapBigDataRow.put("event_status",mapParas.get("event_status").toString());
        mapBigDataRow.put("alarm_flag",mapParas.get("alarm_flag").toString());
        mapBigDataRow.put("up_flag","N");
        mapBigDataRow.put("up_code","");
        mapBigDataRow.put("up_msg","");
        mongoTemplate.insert(mapBigDataRow,meStockEventTable);
    }
}
