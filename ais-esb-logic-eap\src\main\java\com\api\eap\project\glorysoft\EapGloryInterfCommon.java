package com.api.eap.project.glorysoft;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <p>
 * 哥瑞利EAP接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-10
 */
@Service
public class EapGloryInterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;

    //创建报文Header
    public JSONObject CreateHeader(String station_id,String func_name) throws Exception{
        JSONObject jbHeader=new JSONObject();
        String meUserTable="a_eap_me_station_user";
        String user_name="-1";
        String transactionID= CFuncUtilsSystem.GetOnlySign("");
        Query queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
        queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
        queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
        MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
        if(iteratorBigData.hasNext()){
            Document docItemBigData = iteratorBigData.next();
            user_name=docItemBigData.getString("user_name");
            iteratorBigData.close();
        }
        jbHeader.put("MessageName",func_name);
        jbHeader.put("TransactionID",transactionID);
        jbHeader.put("UserID",user_name);
        return jbHeader;
    }

    //创建报文Result
    public JSONObject CreateResult(Integer code,String messageChs,String messageEng) throws Exception{
        JSONObject jbResult=new JSONObject();
        jbResult.put("Code",code);
        jbResult.put("MessageCH",messageChs);
        jbResult.put("MessageEN",messageEng);
        return jbResult;
    }

    //创建发送参数报文
    public JSONObject CreateSendParas(String station_id,String station_code,String func_name,JSONObject jbBody,
                                      Integer code,String messageChs,String messageEng) throws Exception{
        JSONObject jbSendParas=new JSONObject();
        JSONObject jbHeader=CreateHeader(station_id,func_name);
        JSONObject jbResult=CreateResult(code,messageChs,messageEng);
        JSONObject jbBody2=jbBody;
        if(jbBody2==null) jbBody2=new JSONObject();
        jbBody2.put("EquipmentID",station_code);
        jbSendParas.put("Header",jbHeader);
        jbSendParas.put("Body",jbBody2);
        jbSendParas.put("Result",jbResult);
        return jbSendParas;
    }

    //执行接口
    public JSONObject PostJbBackJb(String url,String station_code,JSONObject jsonParas) throws Exception{
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("EqpId",station_code);
        HttpEntity<JSONObject> formEntity = new HttpEntity<>(jsonParas, headers);
        JSONObject jsonResult= restTemplate.postForObject(url , formEntity, JSONObject.class);
        return jsonResult;
    }
}
