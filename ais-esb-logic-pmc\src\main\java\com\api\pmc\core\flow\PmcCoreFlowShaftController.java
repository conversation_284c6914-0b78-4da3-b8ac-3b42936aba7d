package com.api.pmc.core.flow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 采集拧紧枪质量数据流程
 * 1.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/flow")
public class PmcCoreFlowShaftController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;

    //储存拧紧数据
    @RequestMapping(value = "/PmcCoreFlowShaftQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreFlowShaftQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowShaftQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_shaft";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_code = jsonParas.getString("station_code");//工位号
            String vin=jsonParas.getString("vin");
            String parameter_set_id=jsonParas.getString("parameter_set_id");
            JSONArray jaData=jsonParas.getJSONArray("shaft_data");
            if(jaData!=null && jaData.size()>0){
                String work_center_code="";
                String prod_line_code="";
                String station_des="";
                //1.先根据工位号查询工位信息
                String sqlStation="select COALESCE(b.work_center_code,'') work_center_code," +
                        "b.prod_line_code,a.station_des " +
                        "from sys_fmod_station a inner join sys_fmod_prod_line b " +
                        "on a.prod_line_id=b.prod_line_id " +
                        "where a.station_code='"+station_code+"'";
                List<Map<String, Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation,
                        false,request,apiRoutePath);
                if(itemListStation!=null && itemListStation.size()>0){
                    work_center_code=itemListStation.get(0).get("work_center_code").toString();
                    prod_line_code=itemListStation.get(0).get("prod_line_code").toString();
                    station_des=itemListStation.get(0).get("station_des").toString();
                }
                //2.先根据工位号查询设备
                String device_code="";//设备编号
                String device_des="";//设备描述
                String sqlDevice="select COALESCE(device_code,'') device_code," +
                        "COALESCE(device_des,'') device_des " +
                        "from d_pmc_fmod_station_device " +
                        "where device_section_code='"+station_code+"'";
                List<Map<String, Object>> itemListDevice=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlDevice,
                        false,request,apiRoutePath);
                if(itemListDevice!=null && itemListDevice.size()>0){
                    device_code=itemListDevice.get(0).get("device_code").toString();
                    device_des=itemListDevice.get(0).get("device_des").toString();
                }
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    String shaft_index=jbItem.getString("shaft_index");
                    String shaft_status="NG";
                    String torque_status=jbItem.getString("torque_status");
                    String torque_value=jbItem.getString("torque_value");
                    String torque_target=jbItem.getString("torque_target");
                    String torque_min_limit=jbItem.getString("torque_min_limit");
                    String torque_max_limit=jbItem.getString("torque_max_limit");
                    String angle_status=jbItem.getString("angle_status");
                    String angle_value=jbItem.getString("angle_value");
                    String angle_target=jbItem.getString("angle_target");
                    String angle_min=jbItem.getString("angle_min");
                    String angle_max=jbItem.getString("angle_max");
                    String shaft_time=jbItem.getString("shaft_time");
                    if(torque_status.equals("OK") && angle_status.equals("OK")) shaft_status="OK";

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("work_center_code",work_center_code);
                    mapDataItem.put("prod_line_code",prod_line_code);
                    mapDataItem.put("station_code",station_code);
                    mapDataItem.put("station_des",station_des);
                    mapDataItem.put("device_code",device_code);
                    mapDataItem.put("device_des",device_des);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("parameter_set_id",parameter_set_id);
                    mapDataItem.put("shaft_index",shaft_index);
                    mapDataItem.put("shaft_status",shaft_status);
                    mapDataItem.put("torque_status",torque_status);
                    mapDataItem.put("torque_value",torque_value);
                    mapDataItem.put("torque_target",torque_target);
                    mapDataItem.put("torque_min_limit",torque_min_limit);
                    mapDataItem.put("torque_max_limit",torque_max_limit);
                    mapDataItem.put("angle_status",angle_status);
                    mapDataItem.put("angle_value",angle_value);
                    mapDataItem.put("angle_target",angle_target);
                    mapDataItem.put("angle_min",angle_min);
                    mapDataItem.put("angle_max",angle_max);
                    mapDataItem.put("shaft_time",shaft_time);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存拧紧数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }
}
