package com.api.dcs.project.shzy.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.wms.DcsCarCommonFunc;
import com.api.dcs.core.wms.DcsStockCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 不成套库位转移路线创建
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-11
 */
@Service
@Slf4j
public class DcsShzyCarMainUnSuitInFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsCarCommonFunc dcsCarCommonFunc;
    @Autowired
    private DcsStockCommonFunc dcsStockCommonFunc;
    @Autowired
    private DcsShzyWmsMapStockFunc dcsShzyWmsMapStockFunc;
    @Autowired
    private DcsShzyCarCommonFunc dcsShzyCarCommonFunc;

    //创建任务与路线规划
    public Map<String, Object> CreateTaskRoute(String userID, HttpServletRequest request, String apiRoutePath,
                                               String ware_house, String car_code) throws Exception{
        String errorMsg = "";
        String task_type="TKW_KW";
        Map<String, Object> mapResult=new HashMap<>();
        mapResult.put("passFlag",false);//是否允许越过此调度
        mapResult.put("errorMsg",errorMsg);//当前调度错误信息
        String wmsTaskTable = "b_dcs_wms_car_task";

        //1.判断天车是否存在
        Map<String, Object> mapCar= dcsCarCommonFunc.GetCarInfo(car_code);
        if(mapCar==null){
            throw new Exception("天车编码{"+car_code+"}不存在天车基础数据中");
        }

        //2.判断A是否存在有临时库位
        Map<String, Object> mapStockFrom= dcsShzyWmsMapStockFunc.GetTempKwStock(ware_house,"A");
        if(mapStockFrom==null){
            errorMsg="任务类型为{"+task_type+"}的临时倒垛任务,未能找到可以出库的库位和库存";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String from_stock_id=mapStockFrom.get("stock_id").toString();
        String from_stock_code=mapStockFrom.get("stock_code").toString();
        Integer from_location_x=Integer.parseInt(mapStockFrom.get("position_x").toString());
        Integer from_location_y=Integer.parseInt(mapStockFrom.get("position_y").toString());
        Integer from_location_z=Integer.parseInt(mapStockFrom.get("location_z").toString());
        Integer from_stock_count=Integer.parseInt(mapStockFrom.get("stock_count").toString());
        Integer m_length=Integer.parseInt(mapStockFrom.get("m_length").toString());
        Integer m_width=Integer.parseInt(mapStockFrom.get("m_width").toString());
        Integer m_height=Integer.parseInt(mapStockFrom.get("m_thickness").toString());
        String project_code=mapStockFrom.get("lot_num").toString();
        String material_code=mapStockFrom.get("material_code").toString();
        String serial_num=mapStockFrom.get("serial_num").toString();
        String kit_task_num=mapStockFrom.get("kit_task_num").toString();
        String kit_structure_no=mapStockFrom.get("kit_structure_no").toString();
        String kit_material_type=mapStockFrom.get("kit_material_type").toString();
        String kit_flag=mapStockFrom.get("kit_flag").toString();
        double m_weight=(((double)(int)m_length)*((double)(int)m_width)*((double)(int)m_height)*7.85d)/1000000d;
        m_weight=Double.parseDouble(String.format("%.2f", m_weight));

        //3.判断B区域库位
        String to_stock_group_code="B";
        Map<String, Object> mapStockTo= dcsShzyWmsMapStockFunc.GetKwStockIn(ware_house,to_stock_group_code,
                project_code,material_code,"N",m_length,m_width,m_height,request,apiRoutePath);
        if(mapStockTo==null){
            errorMsg="未能根据库区{"+to_stock_group_code+"}查询到存储库位存储位置";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        Long to_stock_id=Long.parseLong(mapStockTo.get("stock_id").toString());
        String to_stock_code=mapStockTo.get("stock_code").toString();
        String to_stock_d_id=mapStockTo.get("stock_d_id").toString();
        Integer to_stock_count=Integer.parseInt(mapStockTo.get("stock_count").toString());
        Integer to_location_x=Integer.parseInt(mapStockTo.get("position_x").toString());
        Integer to_location_y=Integer.parseInt(mapStockTo.get("position_y").toString());
        Integer to_location_z=Integer.parseInt(mapStockTo.get("location_z").toString());
        Integer to_start_cell_row=Integer.parseInt(mapStockTo.get("start_cell_row").toString());
        Integer to_end_cell_row=Integer.parseInt(mapStockTo.get("end_cell_row").toString());
        Integer to_start_cell_col=Integer.parseInt(mapStockTo.get("start_cell_col").toString());
        Integer to_end_cell_col=Integer.parseInt(mapStockTo.get("end_cell_col").toString());

        //4.创建天车任务(做记录使用)
        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
        Map<String, Object> mapBigDataRow = new HashMap<>();
        String task_id = CFuncUtilsSystem.CreateUUID(true);
        String taskNum=CFuncUtilsSystem.GetOnlySign("TKW");
        String task_from="WMS";
        String task_way="AUTO";
        String need_check_gd_flag="N";
        String need_check_model_flag="N";
        String need_tell_start_flag="N";
        String cq_if_tags_list="";
        String cq_if_tags_value="";
        String fz_if_tags_list="";
        String fz_if_tags_value="";
        JSONObject jbAttr=new JSONObject();
        jbAttr.put("kit_task_num",kit_task_num);
        jbAttr.put("kit_structure_no",kit_structure_no);
        jbAttr.put("kit_material_type",kit_material_type);
        jbAttr.put("kit_flag",kit_flag);
        mapBigDataRow.put("item_date", item_date);
        mapBigDataRow.put("item_date_val", item_date_val);
        mapBigDataRow.put("task_id", task_id);
        mapBigDataRow.put("task_from", task_from);
        mapBigDataRow.put("task_way", task_way);
        mapBigDataRow.put("task_type", task_type);
        mapBigDataRow.put("task_num", taskNum);
        mapBigDataRow.put("serial_num", serial_num);
        mapBigDataRow.put("lot_num", project_code);
        mapBigDataRow.put("model_type", material_code);
        mapBigDataRow.put("from_stock_code", from_stock_code);
        mapBigDataRow.put("to_stock_code", to_stock_code);
        mapBigDataRow.put("need_check_gd_flag", need_check_gd_flag);
        mapBigDataRow.put("need_check_model_flag", need_check_model_flag);
        mapBigDataRow.put("need_tell_start_flag", need_tell_start_flag);
        mapBigDataRow.put("tell_start_date", "");
        mapBigDataRow.put("tell_cancel_date", "");
        mapBigDataRow.put("tell_stop_date", "");
        mapBigDataRow.put("tell_start_by", "");
        mapBigDataRow.put("tell_cancel_by", "");
        mapBigDataRow.put("tell_stop_by", "");
        mapBigDataRow.put("task_status", "PLAN");
        mapBigDataRow.put("lock_flag", "N");
        mapBigDataRow.put("enable_flag", "Y");
        mapBigDataRow.put("attribute1", "");
        mapBigDataRow.put("attribute2", "");
        mapBigDataRow.put("attribute3", jbAttr.toString());
        mapBigDataRow.put("cq_if_tags_list", cq_if_tags_list);
        mapBigDataRow.put("cq_if_tags_value", cq_if_tags_value);
        mapBigDataRow.put("fz_if_tags_list", fz_if_tags_list);
        mapBigDataRow.put("fz_if_tags_value", fz_if_tags_value);
        mapBigDataRow.put("check_gd_tag", "");
        mapBigDataRow.put("check_gd_value", "");
        mapBigDataRow.put("check_model_tag", "");

        //5.锁定库位
        //5.1 来源库位
        String sqlStockFromLock="update b_dcs_wms_map_me_stock set " +
                "lock_flag='Y' " +
                "where stock_id="+from_stock_id;
        cFuncDbSqlExecute.ExecUpdateSql(userID,sqlStockFromLock,false,request,apiRoutePath);
        //5.2 目标库位
        if(to_stock_id>0L){
            String sqlStockToLock="update b_dcs_wms_map_me_stock set " +
                    "lock_flag='Y' " +
                    "where stock_id="+to_stock_id;
            cFuncDbSqlExecute.ExecUpdateSql(userID,sqlStockToLock,false,request,apiRoutePath);
        }
        else{
            dcsShzyWmsMapStockFunc.LockMapStockCell("WMS",taskNum,to_stock_group_code,
                    to_start_cell_row,to_end_cell_row, to_start_cell_col,to_end_cell_col,"Y");
        }

        //6.插入调度任务
        Long car_task_id= dcsShzyCarCommonFunc.LockCarTaskIns(userID,request,apiRoutePath,"N",car_code,ware_house,
                task_id,0L,taskNum,task_from,task_way,task_type,serial_num,project_code,
                material_code,0L,from_stock_code,to_stock_code,
                (double)m_length,(double)m_width,(double)m_height,m_weight);

        Integer para_f=0;//旋转角度(0度,180度)
        Integer para_r=1;//R任务类型(1叉取、2放置、3避让、4寻中、5抛丸)
        Integer para_a=1;//A位置代码(1库位、2上盖板焊接位、3服盖板焊接位、4下盖板焊接位)
        Integer para_h=0;//半高标志位,1为半高位
        //6.1 插取路线
        Long car_route_id= dcsCarCommonFunc.LockCarRouteIns(userID,request,apiRoutePath,car_task_id,ware_house,car_code,
                "CHA_QU",1,from_location_x,from_location_y,from_location_z,
                0L,"","","N",
                "N",cq_if_tags_list,cq_if_tags_value,from_stock_count,
                "","","",
                para_f,para_r,para_a,para_h);

        para_r=2;//R任务类型(1叉取、2放置、3避让、4寻中、5抛丸)
        para_a=1;//A位置代码(1库位、2上盖板焊接位、3服盖板焊接位、4下盖板焊接位)
        //6.2 放置路线
        car_route_id= dcsCarCommonFunc.LockCarRouteIns(userID,request,apiRoutePath,car_task_id,ware_house,car_code,
                "FANG_ZHI",2,to_location_x,to_location_y,to_location_z,
                0L,"","",need_check_gd_flag,
                need_check_model_flag,fz_if_tags_list,fz_if_tags_value,to_stock_count,
                "","","",
                para_f,para_r,para_a,para_h);

        //7.插入天车任务
        mongoTemplate.insert(mapBigDataRow, wmsTaskTable);
        return mapResult;
    }
}
