package com.api.mes.project.sh;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.mes.core.recipe.MesCoreRecipeBarCodeController;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/mes/project/sh")
public class MesSHCoreApsOnlineController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MesCoreRecipeBarCodeController mesCoreRecipeBarCodeController;
    @Autowired
    private MongoTemplate mongoTemplate;
    //返修策略查询下发
    @RequestMapping(value = "/MesRepairIssuedSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesRepairIssuedSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/sh/MesRepairIssuedSel";
        String selectResult = "";
        String errorMsg = "";
        String serial_num = "";
        String exact_barcode = "";
        String repair_work_code2 = "";
        String material_sub = "OK";
        try {
            String station_code = jsonParas.getString("station_code");
            String prod_line_code = jsonParas.getString("prod_line_code");
            String  sqlMaterial = "select COALESCE(exact_barcode,'') exact_barcode " + "from c_mes_me_cr_pdure_bom " + "where main_material_flag='Y' " + "and verify_flag='Y' " + "and prod_line_code='" + prod_line_code + "' " + "and station_code='" + station_code + "' " + "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMaterial = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMaterial, true, request, apiRoutePath);
            exact_barcode = itemListMaterial.get(0).get("exact_barcode").toString();
            String  sqlMaterial2 = "select exact_barcode,repair_work_code " + "from c_mes_me_repair_work " + "where  repair_work_id=(select max(repair_work_id) from c_mes_me_repair_work where exact_barcode= '" + exact_barcode +  "') " +   "LIMIT 1 OFFSET 0";
            itemListMaterial = cFuncDbSqlExecute.ExecSelectSql(station_code,sqlMaterial2,true,request,apiRoutePath);
            if(itemListMaterial == null || itemListMaterial.size()<=0){
                errorMsg = "返修策略查询异常,未查询到当前物料绑定的返修策略.";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
            String repair_work_code = itemListMaterial.get(0).get("repair_work_code").toString();
            if(station_code.equals("RAL01-OP040-A") || station_code.equals("RAL01-OP010-2-A") || station_code.equals("RAL01-OP010-1") || station_code.equals("RAL01-OP030")){
                repair_work_code2 = repair_work_code.substring(0,18);
                station_code = "RAL01-OP010-2-A";
            } else if (station_code.equals("RAL01-OP040-B")) {
                repair_work_code2 = repair_work_code.substring(18,34);
                station_code = "RAL01-OP010-2-A";
            } else if (station_code.equals("RAL01-OP040-C") || station_code.equals("RAL01-OP030-4-A")) {
                repair_work_code2 = repair_work_code.substring(36,51);
                station_code = "RAL01-OP030-1";
            }
            Query query = new Query();
            Integer batchSize = 1000;
            query.addCriteria(Criteria.where("exact_barcode").regex(itemListMaterial.get(0).get("exact_barcode").toString()));
            query.addCriteria(Criteria.where("station_code").regex(station_code));
            MongoCursor<Map> iterator = mongoTemplate.getCollection("c_mes_me_station_material").find(query.getQueryObject(), Map.class).sort(query.getSortObject()).noCursorTimeout(true).batchSize(batchSize).iterator();
            while (iterator.hasNext()) {
                Map map = iterator.next();
                map.put("id", map.get("_id").toString());
                map.get(map);
                serial_num = map.get("serial_num").toString();
            }
            Query query2 = new Query();
            query2.addCriteria(Criteria.where("serial_num").regex(itemListMaterial.get(0).get("exact_barcode").toString()));
            query2.addCriteria(Criteria.where("station_code").regex(station_code));
            MongoCursor<Map> iterator2 = mongoTemplate.getCollection("c_mes_me_station_material").find(query2.getQueryObject(), Map.class).sort(query2.getSortObject()).noCursorTimeout(true).batchSize(batchSize).iterator();
            while (iterator2.hasNext()) {
                Map map = iterator2.next();
                map.put("id", map.get("_id").toString());
                map.get(map);
                serial_num = map.get("serial_num").toString();
            }
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            Map<String, Object> mapDataItem = new HashMap<>();
            mapDataItem.put("serial_num", serial_num);//
            mapDataItem.put("repair_work_code", repair_work_code2);//

            //校验扫码物料和绑定物料是否一致
            String  sqlsub = "select material_code,exact_barcode " + "from c_mes_me_cr_pdure_bom " + "where main_material_flag!='Y' " + "and verify_flag='Y' " + "and prod_line_code='" + prod_line_code + "' " + "and station_code='" + station_code + "' " + "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListsub = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlsub, true, request, apiRoutePath);
            if(itemListsub != null && itemListsub.size() > 0){
                exact_barcode = itemListsub.get(0).get("exact_barcode").toString();
                Query querysub = new Query();
                querysub.addCriteria(Criteria.where("exact_barcode").regex(exact_barcode));
                querysub.addCriteria(Criteria.where("serial_num").regex(serial_num));
                MongoCursor<Map> iteratorsub = mongoTemplate.getCollection("c_mes_me_station_material").find(querysub.getQueryObject(), Map.class).sort(querysub.getSortObject()).noCursorTimeout(true).batchSize(batchSize).iterator();
                while (iteratorsub.hasNext()){
                    Map map1 = iteratorsub.next();
                    material_sub="1";
                }
                if(!material_sub.equals("1")){
                    material_sub = "NG";
                }
            }
            mapDataItem.put("material_sub", material_sub);//
            lstDocuments.add(mapDataItem);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, lstDocuments, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "返修策略查询异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //双环测试线转线信息下发
    @RequestMapping(value = "/MesRepairIssuedSel2", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesRepairIssuedSel2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/sh/MesRepairIssuedSel2";
        String selectResult = "";
        String errorMsg = "";
        String serial_num = "";
        String exact_barcode = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String prod_line_code = jsonParas.getString("prod_line_code");
            String  sqlMaterial = "select COALESCE(exact_barcode,'') exact_barcode " + "from c_mes_me_cr_pdure_bom " + "where main_material_flag='Y' " + "and verify_flag='Y' " + "and prod_line_code='" + prod_line_code + "' " + "and station_code='" + station_code + "' " + "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMaterial = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMaterial, true, request, apiRoutePath);
            exact_barcode = itemListMaterial.get(0).get("exact_barcode").toString();
            if(station_code.equals("RAL01-OP040-A") || station_code.equals("RAL01-OP010-2-A") || station_code.equals("RAL01-OP010-1") || station_code.equals("RAL01-OP030")){
                station_code = "RAL01-OP010-2-A";
            } else if (station_code.equals("RAL01-OP040-B")) {
                station_code = "RAL01-OP010-2-A";
            } else if (station_code.equals("RAL01-OP040-C") || station_code.equals("RAL01-OP030-4-A")) {
                station_code = "RAL01-OP030-1";
            }

            Query query = new Query();
            Integer batchSize = 1000;
            query.addCriteria(Criteria.where("exact_barcode").regex(exact_barcode));
            query.addCriteria(Criteria.where("station_code").regex(station_code));
            MongoCursor<Map> iterator = mongoTemplate.getCollection("c_mes_me_station_material").find(query.getQueryObject(), Map.class).sort(query.getSortObject()).noCursorTimeout(true).batchSize(batchSize).iterator();
            while (iterator.hasNext()) {
                Map map = iterator.next();
                map.put("id", map.get("_id").toString());
                map.get(map);
                serial_num = map.get("serial_num").toString();
            }
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            Map<String, Object> mapDataItem = new HashMap<>();
            mapDataItem.put("serial_num", serial_num);//
            lstDocuments.add(mapDataItem);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, lstDocuments, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "测试转线查询异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
