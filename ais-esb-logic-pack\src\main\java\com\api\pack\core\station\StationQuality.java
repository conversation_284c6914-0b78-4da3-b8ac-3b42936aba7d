package com.api.pack.core.station;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.api.base.IMongoBasic;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <p>
 * StationQuality: 站点质量
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-09
 */
@ApiModel(value = "StationQuality", description = "站点质量")
@Document("a_pack_me_station_quality")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class StationQuality extends IMongoBasic
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点流程ID")
    @JsonProperty("station_flow_id")
    @JSONField(name = "station_flow_id")
    @Field("station_flow_id")
    private String stationFlowId;

    @ApiModelProperty(value = "站点编码")
    @JsonProperty("station_code")
    @JSONField(name = "station_code")
    @Field("station_code")
    private String stationCode;

    @ApiModelProperty(value = "站点描述")
    @JsonProperty("station_desc")
    @JSONField(name = "station_desc")
    @Field("station_desc")
    private String stationDesc;

    @ApiModelProperty(value = "批次号")
    @JsonProperty("lot_num")
    @JSONField(name = "lot_num")
    @Field("lot_num")
    private String lotNum;

    @ApiModelProperty(value = "内部标签追溯码")
    @JsonProperty("serial_num")
    @JSONField(name = "serial_num")
    @Field("serial_num")
    private String serialNum;

    @ApiModelProperty(value = "采集对象")
    @JsonProperty("quality_for")
    @JSONField(name = "quality_for")
    @Field("quality_for")
    private String qualityFor;

    @ApiModelProperty(value = "采集项目编码")
    @JsonProperty("tag_code")
    @JSONField(name = "tag_code")
    @Field("tag_code")
    private String tagCode;

    @ApiModelProperty(value = "采集项目名称")
    @JsonProperty("tag_des")
    @JSONField(name = "tag_des")
    @Field("tag_des")
    private String tagDes;

    @ApiModelProperty(value = "采集项目单位")
    @JsonProperty("tag_uom")
    @JSONField(name = "tag_uom")
    @Field("tag_uom")
    private String tagUom;

    @ApiModelProperty(value = "标准值")
    @JsonProperty("theory_value")
    @JSONField(name = "theory_value")
    @Field("theory_value")
    private String theoryValue;

    @ApiModelProperty(value = "下限值")
    @JsonProperty("down_limit")
    @JSONField(name = "down_limit")
    @Field("down_limit")
    private String downLimit;

    @ApiModelProperty(value = "上限值")
    @JsonProperty("upper_limit")
    @JSONField(name = "upper_limit")
    @Field("upper_limit")
    private String upperLimit;

    @ApiModelProperty(value = "采集值")
    @JsonProperty("tag_value")
    @JSONField(name = "tag_value")
    @Field("tag_value")
    private String tagValue;

    @ApiModelProperty(value = "采集信息说明")
    @JsonProperty("tag_msg")
    @JSONField(name = "tag_msg")
    @Field("tag_msg")
    private String tagMsg;

    @ApiModelProperty(value = "子合格标志(来自设备)")
    @JsonProperty("quality_d_sign")
    @JSONField(name = "quality_d_sign")
    @Field("quality_d_sign")
    private String qualityDSign;

    @ApiModelProperty(value = "追溯时间")
    @JsonProperty("trace_d_time")
    @JSONField(name = "trace_d_time")
    @Field("trace_d_time")
    private Long traceDTime;

    public StationQuality(JSONObject flowInfo, JSONObject taskInfo)
    {
        this.stationCode = flowInfo.getString("code");
        this.stationDesc = flowInfo.getString("desc");
        this.stationFlowId = taskInfo.getString("station_flow_id");
        this.lotNum = taskInfo.getString("lot_num");
        this.serialNum = taskInfo.getString("barcode");
        if (this.serialNum == null || this.serialNum.isEmpty())
        {
            this.serialNum = taskInfo.getString("serial_num");
        }
        this.traceDTime = System.currentTimeMillis();
    }

    public StationQuality(JSONObject flowInfo, JSONObject taskInfo, JSONObject quality)
    {
        this(flowInfo, taskInfo);
        this.qualityFor = quality.getString("quality_for");
        this.tagCode = quality.getString("tag_code");
        this.tagDes = quality.getString("tag_des");
        this.tagUom = quality.getString("tag_uom");
        this.tagValue = quality.getString("tag_value");
        this.tagMsg = quality.getString("tag_msg");
        this.qualityDSign = quality.getString("quality_d_sign");
        if (this.qualityDSign == null || this.qualityDSign.isEmpty())
        {
            this.qualityDSign = Const.FLAG_Y;
        }
    }

    public StationQuality(JSONObject flowInfo, JSONObject taskInfo, String tagKey, String tagValue)
    {
        this(flowInfo, taskInfo);
        this.qualityFor = "PLC";
        this.tagCode = tagKey;
        this.tagValue = tagValue;
        this.qualityDSign = Const.FLAG_Y;
        if (tagKey.endsWith(StationConst.QUALITY_TAG_KEY_SUFFIX))
        {
            this.qualityDSign = StationConst.QUALITY_TAG_VALUE_QUALIFIED.equals(tagValue) ? Const.FLAG_Y : Const.FLAG_N;
        }
    }

    public boolean isUnqualified()
    {
        return !Const.FLAG_Y.equals(this.qualityDSign);
    }
}
