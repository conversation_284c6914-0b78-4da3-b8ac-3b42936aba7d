package com.api.base;

import org.springframework.data.domain.*;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.repository.CrudRepository;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.List;
import java.util.Optional;

public abstract class IMongoBasicService<IModel extends Serializable, IRepository extends MongoRepository<IModel, String>> implements CrudRepository<IModel, String>
{
    private final IRepository repository;

    public IMongoBasicService(IRepository repository)
    {
        this.repository = repository;
    }

    public IRepository getRepository()
    {
        return this.repository;
    }

    @Transactional(readOnly = true)
    @Override
    public long count()
    {
        return this.repository.count();
    }

    @Transactional(readOnly = true)
    public long count(Example<IModel> example)
    {
        return this.repository.count(example);
    }

    @Transactional(readOnly = true)
    @Override
    public boolean existsById(String id)
    {
        return this.repository.existsById(id);
    }

    @Transactional(readOnly = true)
    @Override
    public Optional<IModel> findById(String id)
    {
        return this.repository.findById(id);
    }

    @Transactional(readOnly = true)
    @Override
    public List<IModel> findAll()
    {
        return this.repository.findAll();
    }

    @Transactional(readOnly = true)
    public List<IModel> findAll(Example<IModel> example)
    {
        return this.repository.findAll(example);
    }

    @Transactional(readOnly = true)
    public List<IModel> findAll(Example<IModel> example, Sort sort)
    {
        return this.repository.findAll(example, sort);
    }

    @Transactional(readOnly = true)
    @Override
    public Iterable<IModel> findAllById(Iterable<String> ids)
    {
        return this.repository.findAllById(ids);
    }

    @Transactional(readOnly = true)
    public Page<IModel> findAll(int page, int size)
    {
        return this.repository.findAll(PageRequest.of(page, size));
    }

    @Transactional(readOnly = true)
    public Page<IModel> findAll(int page, int size, Sort sort)
    {
        return this.repository.findAll(PageRequest.of(page, size, sort));
    }

    @Transactional(readOnly = true)
    public Page<IModel> findAll(Example<IModel> example, Pageable pageable)
    {
        return this.repository.findAll(example, pageable);
    }

    @Transactional(readOnly = true)
    public Optional<IModel> findOne(Example<IModel> example)
    {
        return this.repository.findOne(example);
    }

    @Override
    public <S extends IModel> S save(S entity)
    {
        if (entity instanceof IMongoBasic)
        {
            ((IMongoBasic) entity).completeBeforeSave();
        }
        return this.repository.save(entity);
    }

    @Override
    public <S extends IModel> Iterable<S> saveAll(Iterable<S> entities)
    {
        for (S entity : entities)
        {
            if (entity instanceof IMongoBasic)
            {
                ((IMongoBasic) entity).completeBeforeSave();
            }
        }
        return this.repository.saveAll(entities);
    }

    @Override
    public void deleteById(String id)
    {
        this.repository.deleteById(id);
    }

    @Override
    public void delete(IModel entity)
    {
        this.repository.delete(entity);
    }

    @Override
    public void deleteAll(Iterable<? extends IModel> entities)
    {
        this.repository.deleteAll(entities);
    }

    @Override
    public void deleteAll()
    {
        this.repository.deleteAll();
    }
}
