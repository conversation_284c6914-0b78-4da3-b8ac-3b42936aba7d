package com.api.pack.core.sort;

import com.api.pack.core.board.BoardConst;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 分选异常类
 * </p>
 *
 * @since 2024-05-23
 */
@EqualsAndHashCode(callSuper = true)
@Data
//@AllArgsConstructor
@NoArgsConstructor
public class SortException extends RuntimeException
{
    protected String message;
    protected String status;
    protected int code;
    protected String sortCode;

    public SortException(String message)
    {
        this(message, BoardConst.NG, BoardConst.CODE_NG);
    }

    public SortException(String message, int code)
    {
        this(message, BoardConst.NG, code >= 0 ? BoardConst.CODE_NG : code);
    }

    public SortException(String message, String sortCode)
    {
        this(message, BoardConst.NG, BoardConst.CODE_NG, sortCode);
    }

    public SortException(String message, String status, int code)
    {
        super(message);
        this.message = message;
        this.status = status;
        this.code = code;
    }

    public SortException(String message, String status, int code, String sortCode)
    {
        super(message);
        this.message = message;
        this.status = status;
        this.code = code;
        this.sortCode = sortCode;
    }
}
