package com.api.base;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

public enum Tool
{
    INSTANCE;

    public Object getPropertyValue(Object target, String property)
    {
        if (target == null)
        {
            return null;
        }
        PropertyDescriptor targetProperty = BeanUtils.getPropertyDescriptor(target.getClass(), property);
        if (targetProperty != null)
        {
            Method readMethod = targetProperty.getReadMethod();
            if (readMethod != null)
            {
                try
                {
                    if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers()))
                    {
                        readMethod.setAccessible(true);
                    }
                    return readMethod.invoke(target);
                }
                catch (Throwable ex)
                {
                    throw new FatalBeanException("Could not read property '" + property + "' from target", ex);
                }
            }
        }
        return null;
    }
}
