pack.ccdDataError=CCD board data is empty
pack.pcsCountError=Front and back PCS board count is error
pack.formatError={0} message format error
pack.unconformity={0} information does not match {1} information
pack.unconformityWithValues={0} information {1} and {2} information {3} do not match
pack.noDetected={0} not detected {1} information
pack.notMatch={0} {1} information detection value {2} does not match the set value {3}
pack.notMatchAll={0} {1} information does not match the set
pack.notConfigured=Not configured for "{0}" detection information
pack.compareRules.FrontAndBackSort=Front to back
pack.compareRules.FrontAndBackSort.notMatch={0} {1} information front detection value {2} does not match the back detection value {3}
pack.compareRules.XoutNPSort=X Number/Position
pack.compareRules.XoutSort.notMatch={0} front X count {1} does not match the back X count {2}
pack.compareRules.XoutSort.notMatchForFront={0} front X count does not match the back X count
pack.compareRules.XoutSort.notMatchForBack={0} back X count does not match the front X count
pack.compareRules.XoutSort.markIsOk={0} X is drawn, but optical point detection is OK
pack.compareRules.XoutSort.markIsOK={0} X is drawn, but optical point detection is OK
pack.compareRules.XoutSort.markIsNG={0} X is not drawn, and optical point detection is NG
pack.compareRules.XoutSort.notMatchOfPcs={0} front PCS count {1} does not match the back PCS count {2}
pack.compareRules.XoutSort.notMatchForActual={0} message X count {1} does not match the actual value {2}
pack.compareRules.XoutSort.notMatchForRule={0} X count {1}X does not match the rule set value {2}
pack.compareRules.XoutSort.ruleIsInvalid=XOut rule is invalid
pack.compareRules.LevelSort.notMatch={0} front level "{1}" does not match the back level "{2}"
pack.compareRules.LevelSort.greaterThanStand={0} QR code level "{1}" is greater than the set level "{2}"
pack.compareRules.BoardDirectionSort.notMatch={0} front board direction "{1}" does not match the back board direction "{2}"
pack.compareRules.BarLengthSort.notMatch={0} front barcode length {1} does not match the back barcode length {2}
pack.compareRules.BarLengthSort.notMatchForRule={0} barcode length {1} does not match the rule set value {2}
pack.compareRules.BarCaseSort.notMatchForRule={0} barcode {1} case does not match the rule {2}
pack.compareRules.MultiCodeSort.repeated={0} barcode {1} repeated
pack.compareRules.DirMarkSort.statusIsNG={0} optical point status NG
pack.compareRules.ETPassSort.notMatch={0} {1}information detection value {2} does not match the "ETPass" information value