package com.api.pmc.project.bq;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * <p>
 * ANDON指标分析
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@RestController
@Slf4j
@RequestMapping("/pmc/project/bq")
public class PmcBqAndonIndexAnalysisController {
    @Autowired
    private CFuncDbSqlMapper cFuncDbSqlMapper;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    /**
     * Andon工位屏数据
     *
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/PmcBqAndonStationScreenData", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqAndonStationScreenData(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        String prod_line_code = jsonParas.getString("prod_line_code");
        String line_section_code = jsonParas.getString("line_section_code");//安灯事件需要根据分段区域编码来区分（内饰有区分）
        String fastcode_group_code = jsonParas.getString("fastcode_group_code");
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startOfDay = now.with(LocalTime.MIN);
            LocalDateTime endOfDay = now.with(LocalTime.MAX);

            //JSONObject orderObj = cFuncUtilsRest.PostJbBackJb("http://127.0.0.1:9091/pmc/project/bq/PmcBqStationScreenOrderNum", new JSONObject());
            JSONObject orderObj = cFuncUtilsRest.PostJbBackJb("http://10.140.4.15:9090/aisEsbOra/pmc/project/bq/PmcBqStationScreenOrderNum", new JSONObject());
            // 创建日期时间格式化器
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

            String sqlAndon = "select COALESCE(happen_date,'') happen_date," +
                    "COALESCE(reset_date,'') reset_date,line_section_code from d_pmc_me_andon_event " +
                    "where (andon_type='99' AND line_section_code='" + line_section_code + "' and COALESCE(happen_date,'')>='" + formatter.format(startOfDay) + "' " +
                    "and COALESCE(happen_date,'')<='" + formatter.format(endOfDay) + "') " +
                    "OR (andon_type='99' AND line_section_code='" + line_section_code + "' and COALESCE(reset_date,'')>='" + formatter.format(startOfDay) + "' " +
                    " and COALESCE(reset_date,'')<='" + formatter.format(endOfDay) + "') " +
                    "OR (andon_type='99' AND line_section_code='" + line_section_code + "' and reset_flag='N') ";
            List<Map<String, Object>> andonData = cFuncDbSqlMapper.ExecSelectSql(sqlAndon);

            int orderNum = 0; // 今日计划
            long minutes = 0; // 实际工作时间(分钟)
            long stopMinutes = 0; // 实际停线时间(分钟) 根据设备停线点位记录的停线时间-休息时间、吃饭时间
            if (orderObj != null && "0".equals(orderObj.getString("code"))) {
                orderNum = orderObj.getIntValue("orderNum");
                JSONArray dateList = orderObj.getJSONArray("dateList");
                minutes = calculateWorkTime(dateList, now, formatter);
                stopMinutes = calculateStopTime(dateList, andonData, formatter);
            }

            int upLineNum = 0;  // 上线数
            int downLineNum = 0; // 下线数
            List<Map<String, Object>> stationList = cFuncDbSqlMapper.ExecSelectSql("select sf.fastcode_code from sys_fastcode sf left join sys_fastcode_group sfg on sf.fastcode_group_id = sfg.fastcode_group_id\n" +
                    " where sfg.fastcode_group_code = '" + fastcode_group_code + "'" +
                    " order by sf.fastcode_order");
            if (!CollectionUtils.isEmpty(stationList) && stationList.size() == 2) {
                upLineNum = cFuncDbSqlResolve.GetSelectCount("SELECT COUNT(DISTINCT make_order) FROM d_pmc_me_station_flow WHERE station_code = '" + stationList.get(0).get("fastcode_code") + "'" +
                        " and creation_date >= '" + formatter.format(startOfDay) + "' and creation_date <= '" + formatter.format(endOfDay) + "' and staff_id!='MES' and make_order!=''");
                downLineNum = cFuncDbSqlResolve.GetSelectCount("SELECT COUNT(DISTINCT make_order) FROM d_pmc_me_station_flow WHERE make_order <>'' and station_code = '" + stationList.get(1).get("fastcode_code") + "'" +
                        " and creation_date >= '" + formatter.format(startOfDay) + "' and creation_date <= '" + formatter.format(endOfDay) + "'");
            }

            double jphNum = 0.0; // JPH节拍
            double deviceMobility = 0.0; // 设备可动率
            if (minutes > 0l) {
                float value = (float) downLineNum * 60 / minutes;
                jphNum = new BigDecimal(value).setScale(1, RoundingMode.HALF_UP).doubleValue();

                if (minutes > stopMinutes) {
                    float device_mobility_value = (float) (minutes - stopMinutes) * 100 / minutes;
                    deviceMobility = new BigDecimal(device_mobility_value).setScale(1, RoundingMode.HALF_UP).doubleValue();
                }
            }

            double finishRate = 0.0;
            if (orderNum > 0) {
                float value = (float) downLineNum * 100 / orderNum;
                finishRate = new BigDecimal(value).setScale(1, RoundingMode.HALF_UP).doubleValue();
            }

            //获取大屏
            String sql = "select COALESCE(today_plan,'') today_plan," +
                    "COALESCE(current_offline,'') current_offline," +
                    "COALESCE(finish_rate,'') finish_rate," +
                    "COALESCE(jph_actual,'') jph_actual," +
                    "COALESCE(device_mobility,'') device_mobility," +
                    "COALESCE(today_stop,'') today_stop," +
                    "COALESCE(above_show,'') above_show, " +
                    "COALESCE(below_show,'') below_show, " +
                    "COALESCE(background_image,'') background_image " +
                    "from d_pmc_fmod_screen_set_content " +
                    "where enable_flag='Y' " +
                    "and prod_line_code='" + prod_line_code + "' ";
            List<Map<String, Object>> contentList = cFuncDbSqlMapper.ExecSelectSql(sql);

            JSONObject result = new JSONObject();
            result.put("code", 0);
            result.put("today_plan", orderNum);
            result.put("current_online", upLineNum);
            result.put("current_offline", downLineNum);
            result.put("jph_actual", jphNum);
            result.put("finish_rate", finishRate);
            result.put("today_stop", stopMinutes);
            result.put("device_mobility", deviceMobility);
            result.put("contentList", contentList);
            selectResult = result.toString();
        } catch (Exception ex) {
            errorMsg = "Andon工位屏数据查询异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 计算工作时长（分钟）
     *
     * @param dateList
     * @param now
     * @param formatter
     * @return
     */
    public long calculateWorkTime(JSONArray dateList, LocalDateTime now, DateTimeFormatter formatter) {
        if (CollectionUtils.isEmpty(dateList)) {
            return 0l;
        }
        String work_start_time_str = dateList.getJSONObject(0).getString("START_TIME");
        String work_end_time_str = dateList.getJSONObject(dateList.size() - 1).getString("END_TIME");
        LocalDateTime work_start_time = LocalDateTime.parse(work_start_time_str, formatter);
        LocalDateTime work_end_time = LocalDateTime.parse(work_end_time_str, formatter);
        LocalDateTime startOfDay = now.with(LocalTime.MIN);
        LocalDateTime endOfDay = now.with(LocalTime.MAX);
        // 当前时间不在工作时间范围
        if (now.isBefore(work_start_time) && now.isAfter(work_end_time)) {
            return 0l;
        }

        long minutes = 0l;
        //1.获取所有时间点 key是时间点，value中1标识工作开始，0标识工作结束
        Map<LocalDateTime, Integer> map = new HashMap<>();
        for (int i = 0; i < dateList.size(); i++) {
            JSONObject obj = dateList.getJSONObject(i);
            String start_time_str = obj.getString("START_TIME");
            String end_time_str = obj.getString("END_TIME");
            String rest_start_time_str = obj.getString("REST_START_TIME");
            String rest_end_time_str = obj.getString("REST_END_TIME");
            LocalDateTime start_time = LocalDateTime.parse(start_time_str, formatter);
            LocalDateTime end_time = LocalDateTime.parse(end_time_str, formatter);
            LocalDateTime rest_start_time = LocalDateTime.parse(rest_start_time_str, formatter);
            LocalDateTime rest_end_time = LocalDateTime.parse(rest_end_time_str, formatter);
            //休息开始时间=工作结束时间，休息结束时间=工作开始时间
            if (!map.containsKey(start_time))
                map.put(start_time, 1);
            if (!map.containsKey(end_time))
                map.put(end_time, 0);
            if (!map.containsKey(rest_start_time))
                map.put(rest_start_time, 0);
            if (!map.containsKey(rest_end_time))
                map.put(rest_end_time, 1);
        }
        TreeMap<LocalDateTime, Integer> timeList = new TreeMap<>(map);
        //2.获取工作日历中所有的工作时间
        JSONArray workTimeList = new JSONArray();
        JSONObject preObj = null;
        for (LocalDateTime key : timeList.keySet()) {
            if (timeList.get(key) == 1) {
                JSONObject newObj = new JSONObject();
                newObj.put("WORK_START_TIME", key.format(formatter));
                workTimeList.add(newObj);
                preObj = newObj;
            } else if (timeList.get(key) == 0 && preObj != null) {
                preObj.put("WORK_END_TIME", key.format(formatter));
            }
        }
        //3.计算当前工作时间
        for (int i = 0; i < workTimeList.size(); i++) {
            JSONObject obj = workTimeList.getJSONObject(i);
            String start_time_str = obj.getString("WORK_START_TIME");
            String end_time_str = obj.getString("WORK_END_TIME");
            LocalDateTime start_time = LocalDateTime.parse(start_time_str, formatter);//工作开始时间
            LocalDateTime end_time = LocalDateTime.parse(end_time_str, formatter);//工作结束时间
            if (now.isAfter(end_time) || now.isEqual(end_time)) {
                minutes += ChronoUnit.MINUTES.between(start_time, end_time);
                continue;
            } else if (now.isAfter(start_time) && now.isBefore(end_time)) {
                minutes += ChronoUnit.MINUTES.between(start_time, now);
                continue;
            }
        }
        return minutes;
    }

    /**
     * 计算停线时长（分钟）
     *
     * @param dateList
     * @param andonData
     * @param formatter
     * @return
     */
    public long calculateStopTime(JSONArray dateList, List<Map<String, Object>> andonData, DateTimeFormatter formatter) {
        if (CollectionUtils.isEmpty(dateList)) {
            return 0l;
        }
        String work_start_time_str = dateList.getJSONObject(0).getString("START_TIME");
        String work_end_time_str = dateList.getJSONObject(dateList.size() - 1).getString("END_TIME");
        LocalDateTime work_start_time = LocalDateTime.parse(work_start_time_str, formatter);
        LocalDateTime work_end_time = LocalDateTime.parse(work_end_time_str, formatter);

        long stopMinutes = 0l;
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startOfDay = now.with(LocalTime.MIN);
        LocalDateTime endOfDay = now.with(LocalTime.MAX);

        //1.获取所有时间点 key是时间点，value中1标识工作开始，0标识工作结束
        Map<LocalDateTime, Integer> map = new HashMap<>();
        for (int i = 0; i < dateList.size(); i++) {
            JSONObject obj = dateList.getJSONObject(i);
            String start_time_str = obj.getString("START_TIME");
            String end_time_str = obj.getString("END_TIME");
            String rest_start_time_str = obj.getString("REST_START_TIME");
            String rest_end_time_str = obj.getString("REST_END_TIME");
            LocalDateTime start_time = LocalDateTime.parse(start_time_str, formatter);
            LocalDateTime end_time = LocalDateTime.parse(end_time_str, formatter);
            LocalDateTime rest_start_time = LocalDateTime.parse(rest_start_time_str, formatter);
            LocalDateTime rest_end_time = LocalDateTime.parse(rest_end_time_str, formatter);
            //休息开始时间=工作结束时间，休息结束时间=工作开始时间
            if (!map.containsKey(start_time))
                map.put(start_time, 1);
            if (!map.containsKey(end_time))
                map.put(end_time, 0);
            if (!map.containsKey(rest_start_time))
                map.put(rest_start_time, 0);
            if (!map.containsKey(rest_end_time))
                map.put(rest_end_time, 1);
        }
        TreeMap<LocalDateTime, Integer> timeList = new TreeMap<>(map);
        //2.获取工作日历中所有的所有的休息时间
        JSONArray restTimeList = new JSONArray();
        JSONObject newObj1 = new JSONObject();
        newObj1.put("REST_START_TIME", startOfDay.format(formatter));
        restTimeList.add(newObj1);
        JSONObject preObj = newObj1;

        for (LocalDateTime key : timeList.keySet()) {
            if (timeList.get(key) == 1 && preObj != null) {
                preObj.put("REST_END_TIME", key.format(formatter));
            } else if (timeList.get(key) == 0) {
                JSONObject newObj2 = new JSONObject();
                newObj2.put("REST_START_TIME", key.format(formatter));
                restTimeList.add(newObj2);
                preObj = newObj2;
            }
        }
        preObj.put("REST_END_TIME", endOfDay.format(formatter));
        //根据停线数据计算停线时间
        for (int i = 0; i < andonData.size(); i++) {
            String happen_date_str = andonData.get(i).get("happen_date").toString();
            String reset_date_str = andonData.get(i).get("reset_date").toString();
            LocalDateTime happen_date = LocalDateTime.parse(happen_date_str, formatter);//停线开始时间
            LocalDateTime reset_date = now;
            if (reset_date_str != null && !reset_date_str.equals(""))
                reset_date = LocalDateTime.parse(reset_date_str, formatter);//停线结束时间

            //如果停线开始时间早于工作开始时间，停线开始时间按工作开始时间算
            if (happen_date.isBefore(work_start_time))
                happen_date = work_start_time;
            //1.获取当前停线持续时间（分钟）
            long stopTime = ChronoUnit.MINUTES.between(happen_date, reset_date);
            //2.工作时间之外的停线时间不做计算
            if (reset_date.isBefore(work_start_time) || happen_date.isAfter(work_end_time))
                continue;
            //3.获取在此次停线中的休息时间（分钟）
            long restTime = 0;
            for (int m = 0; m < restTimeList.size(); m++) {
                JSONObject obj = restTimeList.getJSONObject(m);
                String rest_start_time_str = obj.getString("REST_START_TIME");
                String rest_end_time_str = obj.getString("REST_END_TIME");
                LocalDateTime rest_start_time = LocalDateTime.parse(rest_start_time_str, formatter);//休息开始时间
                LocalDateTime rest_end_time = LocalDateTime.parse(rest_end_time_str, formatter);//休息结束时间
                //3.1  休息开始时间<停线开始时间<休息结束时间<停线结束时间  休息时间=休息结束时间-停线开始时间
                if (rest_start_time.isBefore(happen_date) && happen_date.isBefore(rest_end_time) && rest_end_time.isBefore(reset_date)) {
                    restTime += ChronoUnit.MINUTES.between(happen_date, rest_end_time);
                }
                //3.2  停线开始时间<休息开始时间<休息结束时间<停线结束时间  休息时间=休息结束时间-休息开始时间
                else if (happen_date.isBefore(rest_start_time) && rest_start_time.isBefore(rest_end_time) && rest_end_time.isBefore(reset_date)) {
                    restTime += ChronoUnit.MINUTES.between(rest_start_time, rest_end_time);
                }
                //3.3  停线开始时间<休息开始时间<停线结束时间<休息结束时间  休息时间=停线结束时间-休息开始时间
                else if (happen_date.isBefore(rest_start_time) && rest_start_time.isBefore(reset_date) && reset_date.isBefore(rest_end_time)) {
                    restTime += ChronoUnit.MINUTES.between(rest_start_time, reset_date);
                }
                //3.4  休息开始时间<停线开始时间<停线结束时间<休息结束时间  休息时间=休息结束时间-休息开始时间
                else if (rest_start_time.isBefore(happen_date) && happen_date.isBefore(reset_date) && reset_date.isBefore(rest_end_time)) {
                    restTime += ChronoUnit.MINUTES.between(rest_start_time, rest_end_time);
                }
            }
            if (stopTime > restTime) {
                stopMinutes += (stopTime - restTime);
            }
        }
        return stopMinutes;
    }
}

