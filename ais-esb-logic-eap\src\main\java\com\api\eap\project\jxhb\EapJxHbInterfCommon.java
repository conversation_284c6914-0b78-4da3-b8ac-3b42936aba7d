package com.api.eap.project.jxhb;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <p>
 * (江西红森)EAP接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@Service
@Slf4j
public class EapJxHbInterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.创建返回报文
    public String CreateResponseHead01(Boolean isSuccess, String msg) throws Exception{
        Integer code=200;
        if(!isSuccess) code=500;
        JSONObject response_head=new JSONObject();
        response_head.put("success",isSuccess);
        response_head.put("message",msg);
        response_head.put("code",code);
        response_head.put("timestamp",CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss"));
        return response_head.toString();
    }

    //获取返回信息
    public void CheckResponseData(JSONObject jsonResult) throws Exception{
        String errorMsg="";
        try{
            Integer rtn_code=jsonResult.getInteger("Return_Code");
            String rtn_msg=jsonResult.getString("Return_Msg");
            if(rtn_code!=0){
                errorMsg=rtn_code+"@"+rtn_msg;
                throw new Exception(errorMsg);
            }
        }
        catch (Exception ex){
            throw ex;
        }
    }
}
