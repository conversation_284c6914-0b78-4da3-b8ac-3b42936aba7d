package com.api.eap.core.interf.ais1;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * EAP事件接口(Sub)公共方法
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class Eap1CoreSendEventSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private Eap1CoreInterfCommon eap1CoreInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.[接口]通用接口(EAP发布)
    public JSONObject EapCoreCommonEvent(String esbInterfCode, JSONObject postParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        String esb_interf_des = "";
        boolean successFlag = false;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            requestParas = postParas.toString();
            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url," +
                    "COALESCE(esb_interf_des,'') esb_interf_des " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            esb_interf_des = itemList.get(0).get("esb_interf_des").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            if (jsonObjectBack == null || !jsonObjectBack.containsKey("code")) {
                errorMsg = "接受报文为空或者不包含code";
            }
            int code = jsonObjectBack.getInteger("code");
            String msg = jsonObjectBack.getString("msg");
            if (code != 0) {
                errorMsg = esb_interf_des + "接口返回失败：" + msg;
                successFlag = false;
            } else {
                errorMsg = esb_interf_des + "成功";
                successFlag = true;
            }
        } catch (Exception ex) {
            errorMsg = esb_interf_des + "发生未知异常:" + ex.getMessage();
            successFlag = false;
        }
        jbResult = new JSONObject();
        jbResult.put("isSaveFlag", true);
        jbResult.put("esbInterfCode", esbInterfCode);
        jbResult.put("token", token);
        jbResult.put("requestParas", requestParas);
        jbResult.put("responseParas", responseParas);
        jbResult.put("successFlag", successFlag);
        jbResult.put("message", errorMsg);
        return jbResult;
    }

}
