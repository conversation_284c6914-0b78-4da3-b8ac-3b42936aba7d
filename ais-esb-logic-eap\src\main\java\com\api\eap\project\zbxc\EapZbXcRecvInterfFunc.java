package com.api.eap.project.zbxc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.jcraft.jsch.ChannelExec;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 淄博芯材EAP接受接口方法内容
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-10
 */
@Service
@Slf4j
public class EapZbXcRecvInterfFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private EapZbXcInterfCommon eapZbXcInterfCommon;

    //EAP询问机台是否在线
    public JSONObject aisAreYouThereReply(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "AreYouThereReply";
        String esbInterfCode = "AisAreYouThereReply";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("Header");
            jbRecvBody = jsonParas.getJSONObject("Body");
            jbRecvResult = jsonParas.getJSONObject("Result");
            jbRecvResult.put("Code", 1);
            jbRecvResult.put("MessageCH", "");
            jbRecvResult.put("MessageEN", "");
            String message_name = jbRecvHeader.getString("MessageName");
            String userId = jbRecvHeader.getString("UserID");
            String station_code = jbRecvBody.getString("EquipmentID");

            //返回初始化
            jbResponse = new JSONObject();
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否存在
            String sqlStation = "select " + "station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_code='" + station_code + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + station_code + "}不存在AIS投收板机系统中";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "EAP询问机台是否在线成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP收到时间信号返回(修改时间)
    public JSONObject aisDateTimeCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "DateTimeCommand";
        String esbInterfCode = "AisDateTimeCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("Header");
            jbRecvBody = jsonParas.getJSONObject("Body");
            jbRecvResult = jsonParas.getJSONObject("Result");
            jbRecvResult.put("Code", 1);
            jbRecvResult.put("MessageCH", "");
            jbRecvResult.put("MessageEN", "");
            String message_name = jbRecvHeader.getString("MessageName");
            String userId = jbRecvHeader.getString("UserID");
            String station_code = jbRecvBody.getString("EquipmentID");
            String eapDateTime = jbRecvBody.getString("DateTime");

            //返回初始化
            jbResponse = new JSONObject();
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否存在
            String sqlStation = "select " + "station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_code='" + station_code + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + station_code + "}不存在AIS投收板机系统中";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if (eapDateTime == null || eapDateTime.equals("")) {
                errorMsg = "EAP修改时间不能为空";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            log.debug("EAPDateTime: {}",eapDateTime);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
            Date parse = simpleDateFormat.parse(eapDateTime);
            //修改PC时间
            SimpleDateFormat df = new SimpleDateFormat("MM-dd-yyyy HH:mm:ss");
            eapDateTime = df.format(parse);
            log.debug("EAPDateTime 转换: {}",eapDateTime);
            String[] lst = eapDateTime.split(" ", -1);
            String dataStr_ = lst[0];
            String timeStr_ = lst[1];
//            File file = new File("commands.bat");
//            if (file.exists()) {
//                file.delete();
//            }
//            String sshConnectionInfo = cFuncDbSqlResolve.GetParameterValue("SSH_CONNECTION_INFO");
//            JSONObject jsonObject = JSONObject.parseObject(sshConnectionInfo);
//            String username = jsonObject.getString("username");
//            String password = jsonObject.getString("password");
//            String ip = jsonObject.getString("ip");
//            Integer port = jsonObject.getInteger("port");
//
//            JSch jsch = new JSch();
//            Session session = jsch.getSession(username, ip, port); // 指定用户名、主机名和端口号
//            session.setPassword(password); // 输入服务器密码
//            session.setConfig("StrictHostKeyChecking", "no");
//            session.connect();
            String cmd = " cmd /c date " + dataStr_;
            log.debug("cmd date :{}",cmd);
            Runtime.getRuntime().exec(cmd);
            cmd = " cmd /c time " + timeStr_;
            log.debug("cmd time :{}",cmd);
            Runtime.getRuntime().exec(cmd);
//            log.debug("指令：{}",command);
//            ChannelExec channelExec = (ChannelExec) session.openChannel("exec");
//            channelExec.setCommand(command);
//            channelExec.setErrStream(System.err);
//            channelExec.setOutputStream(System.out);
//            channelExec.connect();
//             等待命令执行完毕
//            while (!channelExec.isClosed()) {
//                log.debug("等待命令结束");
//                Thread.sleep(100);
//            }
//            channelExec.disconnect();
//            session.disconnect();
//            try (PrintWriter out = new PrintWriter(new FileWriter(file))) {
//                out.println("@echo off"); // 关闭命令回显
//                out.println("cls"); // 关闭命令回显
//                out.println("runas /user:管理员用户名 ipconfig /all");
//                out.println("set newdate=" + dataStr_);
//                out.println("set newtime=" + timeStr_);
//                out.println("date %newdate%");
//                out.println("sleep 2s ");
//                out.println("time %newtime%");
//                out.println("echo 命令运行完毕");
//                out.close();
//                String command = "cmd /c " + file.getAbsolutePath();
//                Runtime.getRuntime().exec(command);
//            } catch (IOException e) {
//                log.error("校时失败");
//            }
//            String command = " cmd /c date " + dataStr_+" & cmd /c time " + timeStr_;
//            ProcessBuilder pb = new ProcessBuilder("cmd.exe", "/c", command);
//            Process p = pb.start();
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "EAP心跳接受与更新时间成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP调用此接口下发信息给设备
    public JSONObject aisCIMMessageCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CIMMessageCommand";
        String esbInterfCode = "AisCIMMessageCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        String cimTableName = "a_eap_me_station_hmi_show";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("Header");
            jbRecvBody = jsonParas.getJSONObject("Body");
            jbRecvResult = jsonParas.getJSONObject("Result");
            jbRecvResult.put("Code", 1);
            jbRecvResult.put("MessageCH", "");
            jbRecvResult.put("MessageEN", "");
            String message_name = jbRecvHeader.getString("MessageName");
            String userId = jbRecvHeader.getString("UserID");
            String station_code = jbRecvBody.getString("EquipmentID");
            String cim_message = jbRecvBody.getString("Message");

            //返回初始化
            jbResponse = new JSONObject();
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否存在
            String sqlStation = "select " + "station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_code='" + station_code + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + station_code + "}不存在AIS投收板机系统中";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if (cim_message == null || cim_message.equals("")) {
                errorMsg = "CIM消息不能为空";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //插入CIM消息
            long station_id = Long.parseLong(itemList.get(0).get("station_id").toString());
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("hmi_show_id", CFuncUtilsSystem.CreateUUID(true));
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("screen_control", "1");
            mapBigDataRow.put("screen_code", "EAP");
            mapBigDataRow.put("cim_from", "EAP");
            mapBigDataRow.put("cim_msg", cim_message);
            mapBigDataRow.put("finish_flag", "N");
            mongoTemplate.insert(mapBigDataRow, cimTableName);

            //成功
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受CIM消息成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP给收放板机下发载具允许置载或不允许置载指令
    public JSONObject aisLoadControlCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "LoadControlCommand";
        String esbInterfCode = "AisLoadControlCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("Header");
            jbRecvBody = jsonParas.getJSONObject("Body");
            jbRecvResult = jsonParas.getJSONObject("Result");
            jbRecvResult.put("Code", 1);
            jbRecvResult.put("MessageCH", "");
            jbRecvResult.put("MessageEN", "");
            String message_name = jbRecvHeader.getString("MessageName");
            String userId = jbRecvHeader.getString("UserID");
            String station_code = jbRecvBody.getString("EquipmentID");
            String port_code = jbRecvBody.getString("PortID");
            String pallet_num = jbRecvBody.getString("CarrierID");
            String control_status = jbRecvBody.getString("LoadControl");//OK允许,NG不允许

            //返回初始化
            jbResponse = new JSONObject();
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否存在
            String sqlStation = "select " + "station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_code='" + station_code + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + station_code + "}不存在AIS投收板机系统中";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //写入到SCADA


            //成功
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受EAP载具命令成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP给收放板机下发开始、取消等事件报告
    public JSONObject aisCarrierControlCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CarrierControlCommand";
        String esbInterfCode = "AisCarrierControlCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("Header");
            jbRecvBody = jsonParas.getJSONObject("Body");
            jbRecvResult = jsonParas.getJSONObject("Result");
            jbRecvResult.put("Code", 1);
            jbRecvResult.put("MessageCH", "");
            jbRecvResult.put("MessageEN", "");
            String message_name = jbRecvHeader.getString("MessageName");
            String userId = jbRecvHeader.getString("UserID");
            String station_code = jbRecvBody.getString("EquipmentID");
            String port_code = jbRecvBody.getString("PortID");
            String pallet_num = jbRecvBody.getString("CarrierID");
            String lot_num = jbRecvBody.getString("LotID");
            String carrier_status = jbRecvBody.getString("CarrierControl");//1(开始)、2(取消)、3(继续)、4(强制取消)、5(完成)、6(Hold)

            //返回初始化
            jbResponse = new JSONObject();
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否存在
            String sqlStation = "select " + "station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_code='" + station_code + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + station_code + "}不存在AIS投收板机系统中";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //写入到SCADA
            String station_attr = itemList.get(0).get("station_attr").toString();
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String allowStartRequestTag = "";
            String clientCode = "";
            if (station_attr.equals("Load")) clientCode = "LoadEap";
            else if (station_attr.equals("UnLoad")) clientCode = "UnLoadEap";
            else {
                errorMsg = "设备编号{" + station_code + "}AIS未设置Load与UnLoad属性";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if (aisMonitorModel.equals("AIS-PC")) {
                allowStartRequestTag = clientCode + "/EapStatus/AllowStartRequest";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                allowStartRequestTag = clientCode + "_" + station_code + "/EapStatus/AllowStartRequest";
            } else {
                errorMsg = "设备编号{" + station_code + "}AIS未设置AIS_MONITOR_MODE系统参数值为AIS-PC|AIS-SERVER";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String writeValue = carrier_status;
            errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, allowStartRequestTag, writeValue, true);
            if (!errorMsg.equals("")) {
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //成功
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受EAP任务开始取消等命令成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //EAP调用此接口下发Lot信息
    public JSONObject aisCarrierLotInfoDownloadCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CarrierLotInfoDownloadCommand";
        String esbInterfCode = "AisCarrierLotInfoDownloadCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("Header");
            jbRecvBody = jsonParas.getJSONObject("Body");
            jbRecvResult = jsonParas.getJSONObject("Result");
            jbRecvResult.put("Code", 1);
            jbRecvResult.put("MessageCH", "");
            jbRecvResult.put("MessageEN", "");
            String message_name = jbRecvHeader.getString("MessageName");
            String userId = jbRecvHeader.getString("UserID");
            String station_code = jbRecvBody.getString("EquipmentID");
            String port_code = jbRecvBody.getString("PortID");
            String lot_num = jbRecvBody.getString("LotID");
            String pallet_num = jbRecvBody.getString("CarrierID");
            String material_code = jbRecvBody.getString("ProductNo");
            String lot_version = jbRecvBody.getString("RecipeID");
            JSONArray lot_attr_list = jbRecvBody.getJSONArray("RecipeList");//Name和Value
            String item_info = jbRecvBody.getString("RecipeList");
            String first_model = jbRecvBody.getString("FirstMode");//0不做首件,1停机首件,2不停机首件,3下料首件
            String first_count = jbRecvBody.getString("FirstQTY");
            String plan_count = jbRecvBody.getString("PanelQTY");
            JSONArray pnlList = jbRecvBody.getJSONArray("PanelList");//PanelID
            if (port_code == null) port_code = "";
            if (lot_num == null) lot_num = "";
            if (pallet_num == null) pallet_num = "";
            if (material_code == null) material_code = "";
            if (lot_version == null) lot_version = "";
            if (first_model == null) first_model = "";
            if (first_count == null || first_count.equals("")) first_count = "0";
            if (plan_count == null || plan_count.equals("")) plan_count = "0";
            Integer plan_count_int = Integer.parseInt(plan_count);
            Integer inspect_count = 0;
            if (!first_model.equals("0") && Integer.parseInt(first_count) > 0)
                inspect_count = Integer.parseInt(first_count);

            //返回初始化
            jbResponse = new JSONObject();
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否存在
            String sqlStation = "select " + "station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_code='" + station_code + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + station_code + "}不存在AIS投收板机系统中";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //查询端口号是否正确
            String sqlPortCount = "select count(1) " + "from sys_fmod_station a inner join a_eap_fmod_station_port b " + "on a.station_id=b.station_id " + "where station_code='" + station_code + "' and b.port_code='" + port_code + "'";
            Integer count = cFuncDbSqlResolve.GetSelectCount(sqlPortCount);
            if (count <= 0) {
                errorMsg = "AIS系统未配置工位{" + station_code + "}以及端口{" + port_code + "}匹配关系信息,检查AIS或者EAP参数";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if (lot_num.equals("") || plan_count_int <= 0) {
                errorMsg = "下发批次号或者批次数量不能为空或者0";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if (pnlList != null && pnlList.size() > 0 && pnlList.size() != plan_count_int) {
                errorMsg = "有Panel模式时,下发的PNL数量{" + pnlList.size() + "},不等于下发计划数量{" + plan_count_int + "}";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            String panel_list = "";
            if (pnlList != null && pnlList.size() > 0) {
                for (int i = 0; i < pnlList.size(); i++) {
                    JSONObject jbItem = pnlList.getJSONObject(i);
                    String panel_barcode = jbItem.getString("PanelID");
                    if (i == 0) panel_list = panel_barcode;
                    else panel_list += "," + panel_barcode;
                }
            }

            //暂时不知道长宽高属性参数值
            //合成标准
            JSONArray plan_list = new JSONArray();
            String group_lot_num = lot_num;
            JSONObject jbItem = new JSONObject();
            jbItem.put("task_from", "EAP");
            jbItem.put("group_lot_num", group_lot_num);
            jbItem.put("lot_num", lot_num);
            jbItem.put("pallet_num", pallet_num);
            jbItem.put("lot_index", 1);
            jbItem.put("plan_lot_count", plan_count_int);
            jbItem.put("target_lot_count", plan_count_int);
            jbItem.put("port_code", port_code);
            jbItem.put("material_code", material_code);
            jbItem.put("panel_length", "0");
            jbItem.put("panel_width", "0");
            jbItem.put("panel_tickness", "0");
            jbItem.put("inspect_count", inspect_count);
            jbItem.put("item_info", item_info);
            jbItem.put("panel_list", panel_list);
            plan_list.add(jbItem);

            JSONObject postParas = new JSONObject();
            postParas.put("station_code", station_code);
            postParas.put("plan_list", plan_list);
            String sharePlanUrl = "http://127.0.0.1:9090/aisEsbApi/eap/core/share/EapCoreSharePlanSave";
            JSONObject jbPlanResult = cFuncUtilsRest.PostJbBackJb(sharePlanUrl, postParas);
            if (jbPlanResult.getInteger("code") != 0) {
                errorMsg = jbPlanResult.getString("error");
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //成功
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受EAP生产任务下发成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //Panel信息请求信号返回
    public JSONObject aisPanelInformationRequestReply(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "PanelInformationRequestReply";
        String esbInterfCode = "AisPanelInformationRequestReply";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jbRecvHeader = null;
        JSONObject jbRecvBody = null;
        JSONObject jbRecvResult = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            jbRecvHeader = jsonParas.getJSONObject("Header");
            jbRecvBody = jsonParas.getJSONObject("Body");
            jbRecvResult = jsonParas.getJSONObject("Result");
            jbRecvResult.put("Code", 1);
            jbRecvResult.put("MessageCH", "");
            jbRecvResult.put("MessageEN", "");
            String message_name = jbRecvHeader.getString("MessageName");
            String userId = jbRecvHeader.getString("UserID");
            String station_code = jbRecvBody.getString("EquipmentID");
            String pallet_num = jbRecvBody.getString("CarrierID");
            String lot_num = jbRecvBody.getString("LotID");
            String panel_barcode = jbRecvBody.getString("PanelID");
            String panel_status = jbRecvBody.getString("Result");//1OK,2NG

            //返回初始化
            jbResponse = new JSONObject();
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);

            //判断方法名是否符合
            if (!message_name.equals(funcName)) {
                errorMsg = "方法名{" + message_name + "}不为{" + funcName + "},AIS系统拒绝执行";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否存在
            String sqlStation = "select " + "station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_code='" + station_code + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "设备编号{" + station_code + "}不存在AIS投收板机系统中";
                jbRecvResult.put("Code", 0);
                jbRecvResult.put("MessageCH", errorMsg);
                jbResponse.put("Result", jbRecvResult);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //写入到SCADA


            //成功
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受EAP板件判断结果成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            if (jbRecvResult == null) jbRecvResult = new JSONObject();
            jbRecvResult.put("Code", 0);
            jbRecvResult.put("MessageCH", errorMsg);
            jbRecvResult.put("MessageEN", "");
            jbResponse.put("Header", jbRecvHeader);
            jbResponse.put("Body", jbRecvBody);
            jbResponse.put("Result", jbRecvResult);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
