package com.api.eap.core.share;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 生产计划共用对外API接口
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/share")
public class EapCoreSharePlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //取消端口任务
    @RequestMapping(value = "/EapCoreSharePlanPortCancel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreSharePlanPortCancel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreSharePlanPortCancel";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String[] group_lot_status_list = new String[]{"PLAN", "WORK"};
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String station_attr2=jsonParas.getString("station_attr2");

            //1.查询工位信息,并且针对一车多批不做处理
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr, "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if (OneCarMultyLotFlag.equals("2")) {
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }
            if(station_attr2!=null && !"".equals(station_attr2)) station_attr=station_attr2;
            if(station_attr.equals("UnLoad")){
                group_lot_status_list = new String[]{"WORK"};
            }
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            queryBigData.addCriteria(Criteria.where("lot_status").in(group_lot_status_list));
            Update updateBigData = new Update();
            updateBigData.set("lot_status", "CANCEL");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            //将lot_status
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            updateBigData = new Update();
            updateBigData.set("group_lot_status", "CANCEL");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            //删除AOI-Tray队列信息
            mongoTemplate.dropCollection("a_eap_me_aoi_tray_queue");
            //删除镭射数据
            if(station_attr.equals("Load")){
                mongoTemplate.dropCollection("a_eap_me_laser");
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "取消端口任务异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    /**
     * 修改放板机任务，在放板机对应多个收板机时，同步任务后下游功能无法找到任务同步对应的收板机
     *
     * @param jsonParas
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/EapCoreShareTaskUpdate", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreShareTaskUpdate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreShareTaskUpdate";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            if (!jsonParas.containsKey("target_host") || StringUtils.isEmpty(jsonParas.getString("target_host")) || !jsonParas.containsKey("plan_list") || CollectionUtils.isEmpty(jsonParas.getJSONArray("plan_list"))) {
                log.error("修改放板机任务失败，收板机ip与任务信息异常");
            }
            String targetHost = jsonParas.getString("target_host");
            JSONArray plan_list = jsonParas.getJSONArray("plan_list");
            JSONObject jsonObject = plan_list.getJSONObject(0);
            String lotNum = jsonObject.getString("lot_num");
            String groupLotNum = jsonObject.getString("group_lot_num");
            Query query = new Query();
            query.addCriteria(Criteria.where("lot_num").is(lotNum));
            query.addCriteria(Criteria.where("group_lot_num").is(groupLotNum));
            query.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            Update update = Update.update("target_host", targetHost);
            UpdateResult updateResult = mongoTemplate.updateFirst(query, update, apsPlanTable);
            long matchedCount = updateResult.getMatchedCount();
            Assert.isTrue(updateResult.getMatchedCount() != 0, "更新失败");
            return CFuncUtilsLayUiResut.GetStandJson(true, null, "更新成功", null, matchedCount);
        } catch (Exception e) {
            e.printStackTrace();
            return CFuncUtilsLayUiResut.GetErrorJson("更新放板机任务的收板机Host失败");
        }

    }

    //保存任务信息到数据库
    @RequestMapping(value = "/EapCoreSharePlanSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreSharePlanSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreSharePlanSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            String station_attr = lstStation.get(0).get("station_attr").toString();
            if (station_attr != null && station_attr.equals("UnLoad")) {
                //收板机,需要判断是否为离线模式,若为离线模式则不执行任务接受
                String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagOnOffLine = "";
                if (aisMonitorModel.equals("AIS-PC")) {
                    tagOnOffLine = "UnLoadPlc/PlcConfig/OnOffLine";
                } else if (aisMonitorModel.equals("AIS-SERVER")) {
                    tagOnOffLine = "UnLoadPlc_" + station_code + "/PlcConfig/OnOffLine";
                } else {
                    errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                log.error("读取收板机离线在线点位：{}", tagOnOffLine);
                JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, tagOnOffLine);
                if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                    JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                    String tagOnOffLineValue = jbItem.getString("tag_value");
                    if (tagOnOffLineValue.equals("")) {
                        errorMsg = "查询收板机在线与离线状态时收板机PLC断网";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    if (tagOnOffLineValue.equals("0")) {//离线
                        transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "当前模式为离线模式", 0);
                        return transResult;
                    }
                } else {
                    errorMsg = "未查询到收板机在线与离线状态";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }

            JSONArray plan_list = jsonParas.getJSONArray("plan_list");
            if (plan_list == null || plan_list.size() <= 0) {
                errorMsg = "保存任务信息不能为空集合";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //若是收板机,判断是否存在PLAN或者WORK的任务,若存在则不执行再次插入
            if (station_attr.equals("UnLoad")) {
                String[] group_lot_status2 = new String[]{"PLAN", "WORK"};
                JSONObject jbItem = plan_list.getJSONObject(0);
                String group_lot_num_unload = jbItem.getString("group_lot_num") == null ? "" : jbItem.getString("group_lot_num");
                Query queryBigDataUnLoad = new Query();
                queryBigDataUnLoad.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigDataUnLoad.addCriteria(Criteria.where("group_lot_num").is(group_lot_num_unload));
                queryBigDataUnLoad.addCriteria(Criteria.where("group_lot_status").in(group_lot_status2));
                long allCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigDataUnLoad.getQueryObject());
                if (allCount > 0) {
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "存在PLAN或者WORK的任务", 0);
                    return transResult;
                }
                //若是相同批次不同步(2025-4-22 by Zhoujun)
                String UnLoad_AsynTaskByLot=cFuncDbSqlResolve.GetParameterValue("UnLoad_AsynTaskByLot");
                if("Y".equals(UnLoad_AsynTaskByLot)){
                    String first_lot_num=plan_list.getJSONObject(0).getString("lot_num");
                    if(first_lot_num==null) first_lot_num="";
                    if(!"".equals(first_lot_num)){
                        String[] group_lot_status3 = new String[]{"WAIT","PLAN", "WORK"};
                        queryBigDataUnLoad = new Query();
                        queryBigDataUnLoad.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                        queryBigDataUnLoad.addCriteria(Criteria.where("lot_num").is(first_lot_num));
                        queryBigDataUnLoad.addCriteria(Criteria.where("group_lot_status").in(group_lot_status3));
                        allCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigDataUnLoad.getQueryObject());
                        if(allCount > 0){
                            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "存在PLAN或者WORK的任务", 0);
                            return transResult;
                        }
                    }
                }
            }

            //若是收板机且当前模式为收板机上游决定生产顺序,则不能进行设定lot_group_status的PLAN
            String final_lot_group_status = "PLAN";
            if (station_attr.equals("UnLoad")) {
                String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagTaskOrderByUpDevice = "";
                String clientCode = "";
                if (aisMonitorModel.equals("AIS-PC")) {
                    tagTaskOrderByUpDevice = "UnLoadAis/AisConfig/TaskOrderByUpDevice";
                    clientCode = "UnLoadAis";
                } else if (aisMonitorModel.equals("AIS-SERVER")) {
                    tagTaskOrderByUpDevice = "UnLoadAis_" + station_code + "/AisConfig/TaskOrderByUpDevice";
                    clientCode = "UnLoadAis_" + station_code;
                }
                //1.先判断是否存在tag点
                String sqlOnlyCountTag = "select count(1) " + "from scada_tag st inner join scada_tag_group stg " + "on st.tag_group_id=stg.tag_group_id inner join scada_client sc " + "on stg.client_id=sc.client_id " + "where sc.enable_flag='Y' and stg.enable_flag='Y' and st.enable_flag='Y' " + "and sc.client_code='" + clientCode + "' and stg.tag_group_code='AisConfig' " + "and st.tag_code='TaskOrderByUpDevice'";
                Integer OnlyCountTag = cFuncDbSqlResolve.GetSelectCount(sqlOnlyCountTag);
                if (OnlyCountTag > 0) {
                    JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, tagTaskOrderByUpDevice);
                    if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                        JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                        String tagTaskOrderByUpDeviceValue = jbItem.getString("tag_value");
                        if (tagTaskOrderByUpDeviceValue.equals("")) {
                            errorMsg = "查询收板机工单任务生产顺序来源于上游设备状态时收板机PLC断网";
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        if (tagTaskOrderByUpDeviceValue.equals("1")) {
                            final_lot_group_status = "WAIT";
                        }
                    } else {
                        errorMsg = "未查询到收板机工单任务生产顺序来源于上游设备状态";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
            }

            //查询当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstPlanDocuments = new ArrayList<>();
            List<Map<String, Object>> lstPlanBDocuments = new ArrayList<>();
            String group_id = CFuncUtilsSystem.CreateUUID(true);
            for (int i = 0; i < plan_list.size(); i++) {
                JSONObject jbItem = plan_list.getJSONObject(i);
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String plan_id = CFuncUtilsSystem.CreateUUID(true);
                String port_code = jbItem.getString("port_code") == null ? "" : jbItem.getString("port_code");
                if (station_attr.equals("UnLoad")) port_code = "";
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("plan_id", plan_id);
                mapBigDataRow.put("station_id", Long.parseLong(station_id));
                mapBigDataRow.put("task_from", jbItem.getString("task_from") == null ? "EAP" : jbItem.getString("task_from"));
                mapBigDataRow.put("group_lot_num", jbItem.getString("group_lot_num") == null ? "" : jbItem.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", jbItem.getString("lot_num") == null ? "" : jbItem.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", jbItem.getString("lot_short_num") == null ? "" : jbItem.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", jbItem.getInteger("lot_index") == null ? 1 : jbItem.getInteger("lot_index"));
                mapBigDataRow.put("plan_lot_count", jbItem.getInteger("plan_lot_count") == null ? 0 : jbItem.getInteger("plan_lot_count"));
                mapBigDataRow.put("target_lot_count", jbItem.getInteger("target_lot_count") == null ? 0 : jbItem.getInteger("target_lot_count"));
                mapBigDataRow.put("port_code", port_code);
                mapBigDataRow.put("material_code", jbItem.getString("material_code") == null ? "" : jbItem.getString("material_code"));
                mapBigDataRow.put("pallet_num", jbItem.getString("pallet_num") == null ? "" : jbItem.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", jbItem.getString("pallet_type") == null ? "" : jbItem.getString("pallet_type"));
                mapBigDataRow.put("lot_level", jbItem.getString("lot_level") == null ? "" : jbItem.getString("lot_level"));
                mapBigDataRow.put("panel_length", jbItem.getDouble("panel_length") == null ? 0d : jbItem.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", jbItem.getDouble("panel_width") == null ? 0d : jbItem.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", jbItem.getDouble("panel_tickness") == null ? 0d : jbItem.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_model", jbItem.getInteger("panel_model") == null ? -1 : jbItem.getInteger("panel_model"));
                mapBigDataRow.put("inspect_count", jbItem.getInteger("inspect_count") == null ? 0 : jbItem.getInteger("inspect_count"));
                mapBigDataRow.put("inspect_finish_count", 0);
                mapBigDataRow.put("pdb_count", jbItem.getInteger("pdb_count") == null ? 0 : jbItem.getInteger("pdb_count"));
                mapBigDataRow.put("pdb_rule", jbItem.getInteger("pdb_rule") == null ? 0 : jbItem.getInteger("pdb_rule"));
                mapBigDataRow.put("fp_count", jbItem.getInteger("fp_count") == null ? 0 : jbItem.getInteger("fp_count"));
                mapBigDataRow.put("group_lot_status", final_lot_group_status);
                mapBigDataRow.put("lot_status", "PLAN");
                mapBigDataRow.put("finish_count", 0);
                mapBigDataRow.put("finish_ok_count", 0);
                mapBigDataRow.put("finish_ng_count", 0);
                mapBigDataRow.put("task_error_code", 0);
                mapBigDataRow.put("item_info", jbItem.getString("item_info") == null ? "" : jbItem.getString("item_info"));
                mapBigDataRow.put("task_start_time", "");
                mapBigDataRow.put("task_end_time", "");
                mapBigDataRow.put("task_cost_time", (long) 0);
                mapBigDataRow.put("attribute1", jbItem.getString("attribute1") == null ? "" : jbItem.getString("attribute1"));
                mapBigDataRow.put("attribute2", jbItem.getString("attribute2") == null ? "" : jbItem.getString("attribute2"));
                mapBigDataRow.put("attribute3", jbItem.getString("attribute3") == null ? "" : jbItem.getString("attribute3"));
                mapBigDataRow.put("face_code", jbItem.getInteger("face_code") == null ? 0 : jbItem.getInteger("face_code"));
                mapBigDataRow.put("pallet_use_count", jbItem.getInteger("pallet_use_count") == null ? 0 : jbItem.getInteger("pallet_use_count"));
                mapBigDataRow.put("user_name", user_name);
                mapBigDataRow.put("group_id", group_id);
                mapBigDataRow.put("offline_flag", "N");
                mapBigDataRow.put("target_update_count", 0);

                lstPlanDocuments.add(mapBigDataRow);
                String panel_list = jbItem.getString("panel_list") == null ? "" : jbItem.getString("panel_list");
                String[] panelList = panel_list.split(",", -1);
                if (!panel_list.equals("")) {
                    if (panelList != null && panelList.length > 0) {
                        for (int j = 0; j < panelList.length; j++) {
                            String panel_barcode = panelList[j];
                            String plan_d_id = CFuncUtilsSystem.CreateUUID(true);
                            Map<String, Object> mapBigDataRowB = new HashMap<>();
                            mapBigDataRowB.put("item_date", item_date);
                            mapBigDataRowB.put("item_date_val", item_date_val);
                            mapBigDataRowB.put("plan_d_id", plan_d_id);
                            mapBigDataRowB.put("plan_id", plan_id);
                            mapBigDataRowB.put("panel_barcode", panel_barcode);
                            lstPlanBDocuments.add(mapBigDataRowB);
                        }
                    }
                }
            }
            log.info("同步任务信息：{}", lstPlanDocuments);
            if (lstPlanBDocuments.size() > 0) mongoTemplate.insert(lstPlanBDocuments, apsPlanBTable);
            mongoTemplate.insert(lstPlanDocuments, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "插入成功", 0);
        } catch (Exception ex) {
            errorMsg = "保存任务信息到数据库异常" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //取消工位所有任务
    @RequestMapping(value = "/EapCoreSharePlanCancel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreSharePlanCancel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreSharePlanCancel";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String[] group_lot_status_list = new String[]{"WAIT","PLAN", "WORK"};
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));

            if(StringUtils.hasText(port_code)) {
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            }


            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "CANCEL");
            updateBigData.set("lot_status", "CANCEL");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            //删除AOI-Tray队列信息
            mongoTemplate.dropCollection("a_eap_me_aoi_tray_queue");
            mongoTemplate.dropCollection("a_eap_me_pallet_queue");
            mongoTemplate.dropCollection("a_eap_me_laser");

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "取消端口任务异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //查询未完成的任务清单
    @RequestMapping(value = "/EapCoreShareUnFinishTaskSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreShareUnFinishTaskSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreShareUnFinishTaskSel";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            String[] group_lot_status_list = new String[]{"PLAN", "WORK"};
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "查询未完成的任务清单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //根据GroupID查询任务信息
    @RequestMapping(value = "/EapCoreShareTaskSelByGroupID", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreShareTaskSelByGroupID(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreShareTaskSelByGroupID";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_id= jsonParas.getString("group_id");
            long station_id_long = Long.parseLong(station_id);
            List<Map<String, Object>> itemList = new ArrayList<>();
            if (!group_id.equals("")) {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    String plan_id=docItemBigData.getString("plan_id");
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    //增加panel_list
                    String panel_list="";
                    Integer panel_index=0;
                    Query queryBigDataD = new Query();
                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanBTable).find(queryBigDataD.getQueryObject()).
                            sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(20).iterator();
                    while (iteratorBigDataD.hasNext()){
                        Document docItemBigDataD = iteratorBigDataD.next();
                        String panel_barcode=docItemBigDataD.getString("panel_barcode");
                        if(panel_index==0) panel_list=panel_barcode;
                        else panel_list+=","+panel_barcode;
                        panel_index++;
                    }
                    if (iteratorBigDataD.hasNext()) iteratorBigDataD.close();
                    mapBigDataRow.put("panel_list", panel_list);
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据GroupID查询任务信息异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //根据母批号查询任务数据
    @RequestMapping(value = "/EapCoreShareTaskSelByGroupLot", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreShareTaskSelByGroupLot(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/share/EapCoreShareTaskSelByGroupLot";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            long station_id_long = Long.parseLong(station_id);
            List<Map<String, Object>> itemList = new ArrayList<>();
            if (!group_lot_num.equals("")) {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                //再查询
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    String plan_id=docItemBigData.getString("plan_id");
                    mapBigDataRow.put("group_id", docItemBigData.getString("group_id"));
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    if(docItemBigData.containsKey("face_code_unload")){
                        Integer face_code_unload=docItemBigData.getInteger("face_code_unload");
                        mapBigDataRow.put("face_code_unload", face_code_unload);
                    }
                    //增加panel_list
                    String panel_list="";
                    Integer panel_index=0;
                    Query queryBigDataD = new Query();
                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanBTable).find(queryBigDataD.getQueryObject()).
                            sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(20).iterator();
                    while (iteratorBigDataD.hasNext()){
                        Document docItemBigDataD = iteratorBigDataD.next();
                        String panel_barcode=docItemBigDataD.getString("panel_barcode");
                        if(panel_index==0) panel_list=panel_barcode;
                        else panel_list+=","+panel_barcode;
                        panel_index++;
                    }
                    if (iteratorBigDataD.hasNext()) iteratorBigDataD.close();
                    mapBigDataRow.put("panel_list", panel_list);
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据母批号查询任务数据异常:" + ex;
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
