package com.api.pack.core.station;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.api.base.IMongoBasic;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <p>
 * StationFlow: 站点流程
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@ApiModel(value = "StationFlow", description = "站点流程")
@Document("a_pack_me_station_flow")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class StationFlow extends IMongoBasic
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点编码")
    @JsonProperty("station_code")
    @JSONField(name = "station_code")
    @Field("station_code")
    private String stationCode;

    @ApiModelProperty(value = "站点描述")
    @JsonProperty("station_desc")
    @JSONField(name = "station_desc")
    @Field("station_desc")
    private String stationDesc;

    @ApiModelProperty(value = "批次号")
    @JsonProperty("lot_num")
    @JSONField(name = "lot_num")
    @Field("lot_num")
    private String lotNum;

    @ApiModelProperty(value = "内部标签追溯码")
    @JsonProperty("serial_num")
    @JSONField(name = "serial_num")
    @Field("serial_num")
    private String serialNum;

    @ApiModelProperty(value = "合格标志")
    @JsonProperty("quality_sign")
    @JSONField(name = "quality_sign")
    @Field("quality_sign")
    private String qualitySign;

    @ApiModelProperty(value = "配方数据")
    @JsonProperty("recipe_paras")
    @JSONField(name = "recipe_paras")
    @Field("recipe_paras")
    private String recipeParas;

    @ApiModelProperty(value = "到达时间")
    @JsonProperty("arrive_date")
    @JSONField(name = "arrive_date")
    @Field("arrive_date")
    private Long arriveDate;

    @ApiModelProperty(value = "离开时间")
    @JsonProperty("leave_date")
    @JSONField(name = "leave_date")
    @Field("leave_date")
    private Long leaveDate;

    @ApiModelProperty(value = "消耗时间")
    @JsonProperty("cost_time")
    @JSONField(name = "cost_time")
    @Field("cost_time")
    private Long costTime;

    @ApiModelProperty(value = "线首标志")
    @JsonProperty("head_flag")
    @JSONField(name = "head_flag")
    @Field("head_flag")
    private String headFlag;

    @ApiModelProperty(value = "计划ID")
    @JsonProperty("plan_id")
    @JSONField(name = "plan_id")
    @Field("plan_id")
    private String planId;

    public StationFlow(JSONObject data)
    {
        JSONObject flowInfo = data.getJSONObject("flow_info");
        if (flowInfo == null)
        {
            throw new RuntimeException("参数错误，缺少flow_info");
        }
        this.stationCode = flowInfo.getString("code");
        this.stationDesc = flowInfo.getString("desc");
        JSONObject taskInfo = data.getJSONObject("task_info");
        if (taskInfo == null)
        {
            throw new RuntimeException("参数错误，缺少task_info");
        }
        this.lotNum = taskInfo.getString("lot_num");
        this.serialNum = taskInfo.getString("barcode");
        if (this.serialNum == null || this.serialNum.isEmpty())
        {
            this.serialNum = taskInfo.getString("serial_num");
        }
        this.recipeParas = taskInfo.getString("recipe_paras");
        this.arriveDate = taskInfo.getLong("flow_start_time");
        this.leaveDate = System.currentTimeMillis();
        this.costTime = this.leaveDate - this.arriveDate;
        Boolean head = taskInfo.getBoolean("head");
        this.headFlag = head != null && head ? Const.FLAG_Y : Const.FLAG_N;
        this.planId = taskInfo.getString("plan_id");
    }

    public static StationFlow byId(String id, StationFlowService service)
    {
        return service.findById(id).orElse(null);
    }

    public static StationFlow byStationCodePlanIdAndLotNumAndSerialNum(String stationCode, String planId, String lotNum, String serialNum, StationFlowService service)
    {
        return service.findOneByStationCodeAndPlanIdAndLotNumAndSerialNum(stationCode, planId, lotNum, serialNum);
    }

    public static StationFlow headOfPlanIdAndLotNumAndSerialNum(String planId, String lotNum, String serialNum, StationFlowService service)
    {
        return service.findHeadByPlanIdAndLotNumAndSerialNum(planId, lotNum, serialNum);
    }

    @Transient
    public boolean isHead()
    {
        return Const.FLAG_Y.equals(this.headFlag);
    }

    @Transient
    public boolean checkIsTail()
    {
        boolean isTail = false;
        if (this.isHead())
        {
            // 标签ID&高度&尾包标识&数量
            String headRecipeParas = this.getRecipeParas();
            if (headRecipeParas != null && headRecipeParas.contains("&"))
            {
                String[] headParas = headRecipeParas.split("&");
                if (headParas.length == 4)
                {
                    isTail = "1".equals(headParas[2]);
                }
            }
        }
        return isTail;
    }
}
