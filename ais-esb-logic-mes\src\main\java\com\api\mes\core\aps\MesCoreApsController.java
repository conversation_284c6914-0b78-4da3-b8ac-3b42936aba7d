package com.api.mes.core.aps;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.Cache;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@RestController
@Slf4j
@RequestMapping("/mes/core/aps")
public class MesCoreApsController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Qualifier("jetCache")
    @Resource
    private Cache cache;

    //根据条码或者上线工位获取到订单
    @RequestMapping(value = "/MesCoreApsMoOnlineSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsMoOnlineSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsMoOnlineSelect";
        String selectResult = "";
        String errorMsg = "";
        String meOnlineFlowTable = "c_mes_me_online_flow";
        String meStationFlowTable = "c_mes_me_station_flow";
        List<Map<String, Object>> itemListMo = null;
        String result = "0";
        try {
            String station_code = jsonParas.getString("station_code");
            String online_station_code = jsonParas.getString("online_station_code");//传递上线工位
            String prod_line_code = jsonParas.getString("prod_line_code");
            String serial_num = jsonParas.getString("serial_num");
            String make_order = jsonParas.getString("make_order");
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
//            if ("TSGX".equals(projectCode) && "OP3030".equals(station_code)) {
//                cache.put("OP3030-" + serial_num, CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
//            } else if ("TSGX".equals(projectCode) && "OP3050".equals(station_code)) {
//                Object o = cache.get("OP3030-" + serial_num);
//                result = o == null ? "" : o.toString();
//            }
            if ("TSGX".equals(projectCode) && "OP3050".equals(station_code)) {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
                queryBigData.addCriteria(Criteria.where("station_code").is("OP3030"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document doc = iteratorBigData.next();
                    result = doc.getString("arrive_date");
                    iteratorBigData.close();
                }
            }
            if (make_order != null && !make_order.equals("")) {
                String sqlMo = "select b.make_order," + "COALESCE(b.product_batch,'') product_batch,b.small_model_type,c.model_code," + "COALESCE(b.mo_custom_code,'') mo_custom_code," + "COALESCE(b.mo_custom_des,'') mo_custom_des," + "COALESCE(b.mo_status,'') mo_status," + "'N' as repair_flag " + "from c_mes_aps_plan_mo b " + "inner join c_mes_fmod_small_model c " + "on b.prod_line_id=c.prod_line_id and b.small_model_type=c.small_model_type " + "inner join sys_fmod_prod_line d " + "on c.prod_line_id=d.prod_line_id " + "where b.make_order='" + make_order + "' and d.prod_line_code='" + prod_line_code + "' " + "LIMIT 1 OFFSET 0";
                itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, true, request, apiRoutePath);
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
                return selectResult;
            }
            if (serial_num == null || serial_num.equals("")) {
                String sqlMo = "select b.make_order," + "COALESCE(b.product_batch,'') product_batch,b.small_model_type,c.model_code," + "COALESCE(b.mo_custom_code,'') mo_custom_code," + "COALESCE(b.mo_custom_des,'') mo_custom_des," + "COALESCE(b.mo_status,'') mo_status," + "'N' as repair_flag,b.mo_plan_count " + "from c_mes_aps_station_mo a inner join c_mes_aps_plan_mo b " + "on a.mo_id=b.mo_id " + "inner join c_mes_fmod_small_model c  " + "on b.prod_line_id=c.prod_line_id and b.small_model_type=c.small_model_type " + "where a.station_code='" + online_station_code + "' LIMIT 1 OFFSET 0";
                itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, true, request, apiRoutePath);
                if (itemListMo != null && itemListMo.size() > 0) {
                    String delete01 = "delete from c_mes_me_station_mis where station_code='" + station_code + "'";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, delete01, false, request, apiRoutePath);
                    String make_order2 = itemListMo.get(0).get("make_order").toString();
                    String product_batch2 = itemListMo.get(0).get("product_batch").toString();
                    String small_model_type2 = itemListMo.get(0).get("small_model_type").toString();
                    Integer model_code2 = Integer.parseInt(itemListMo.get(0).get("model_code").toString());
                    Integer mo_plan_count2 = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
                    Query queryBigData2 = new Query();
                    queryBigData2.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                    queryBigData2.addCriteria(Criteria.where("station_code").is(station_code));
                    queryBigData2.addCriteria(Criteria.where("make_order").is(make_order));
                    queryBigData2.addCriteria(Criteria.where("enable_flag").is("Y"));
                    long moFlowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData2.getQueryObject());
                    Integer mo_finish_count2 = (int) moFlowCount;
                    String sqlInsertMis = "insert into c_mes_me_station_mis " + "(user_name,arrive_date,station_code,serial_num," + "make_order,product_batch," + "small_model_type,model_code,mo_plan_count,mo_finish_count," + "serial_status,online_time,serial_stay_time,ideal_beats,actual_beats," + "shift_code,shift_des) values " + "('" + station_code + "','" + CFuncUtilsSystem.GetNowDateTime("") + "','" + station_code + "',''," + "'" + make_order2 + "','" + product_batch2 + "'," + "'" + small_model_type2 + "'," + model_code2 + "," + mo_plan_count2 + "," + mo_finish_count2 + "," + "'正常件','" + CFuncUtilsSystem.GetNowDateTime("") + "',0,0,0," + "'','')";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertMis, false, request, apiRoutePath);
                }
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
                return selectResult;
            }
            //从过站读取
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meOnlineFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            itemListMo = new ArrayList<>();
            log.debug("查询条码是否存在上线记录：{}", iteratorBigData.hasNext());
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> map = new HashMap<>();
                make_order = docItemBigData.getString("make_order");
                map.put("make_order", make_order);
                map.put("product_batch", docItemBigData.getString("product_batch"));
                map.put("small_model_type", docItemBigData.getString("small_model_type"));
                map.put("model_code", docItemBigData.getInteger("model_code"));
                map.put("mo_custom_code", docItemBigData.getString("mo_custom_code"));
                map.put("mo_custom_des", docItemBigData.getString("mo_custom_des"));
                map.put("print_barcode", docItemBigData.getString("print_barcode"));
                String repair_flag = "N";
                if (docItemBigData.containsKey("repair_flag")) {
                    repair_flag = docItemBigData.getString("repair_flag");
                }
                map.put("repair_flag", repair_flag);
                itemListMo.add(map);
                log.debug("上线记录信息：{}", itemListMo);
                //唐山国轩项目
                if (projectCode != null && projectCode.equals("TSGX")) {
                    String delete = "delete from c_mes_aps_station_mo where station_code='" + station_code + "'";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, delete, false, request, apiRoutePath);
                    String sqlMo = "select mo_id from c_mes_aps_plan_mo where make_order='" + make_order + "'";
                    List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, true, request, apiRoutePath);
                    if (itemList != null && itemList.size() > 0) {
                        String mo_id = itemList.get(0).get("mo_id").toString();
                        String sqlStation = "select station_id from sys_fmod_station where station_code='" + station_code + "'";
                        List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, true, request, apiRoutePath);
                        if (itemListStation != null && itemListStation.size() > 0) {
                            String station_id = itemListStation.get(0).get("station_id").toString();
                            String sqlInsert01 = "insert into c_mes_aps_station_mo " + "(station_id,make_order,station_code,mo_id) values " + "(" + station_id + ",'" + make_order + "','" + station_code + "'," + mo_id + ")";
                            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsert01, false, request, apiRoutePath);
                        }
                    }
                }
                break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "根据条码或者上线工位获取到订单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //根据条码或者上线工位获取到订单
    @RequestMapping(value = "/MesCoreApsMoSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsMoSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsMoSelect";
        String selectResult = "";
        String errorMsg = "";
        String meDxQualityTable = "c_mes_me_dx_quality";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");
            String prod_line_code = jsonParas.getString("prod_line_code");
            String serial_num = jsonParas.getString("serial_num");
            Query query = new Query();
            query.addCriteria(Criteria.where("dx_barcode").is(serial_num));
            query.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            String make_order = "";
            List<Document> documents = mongoTemplate.find(query, Document.class, meDxQualityTable);
            if (!CollectionUtils.isEmpty(documents)) {
                Document document = documents.stream().findFirst().get();
                make_order = document.getString("make_order");
            }
            if (!StringUtils.isEmpty(make_order)) {
                String sqlMo = "select b.make_order," + "COALESCE(b.product_batch,'') product_batch,b.small_model_type,c.model_code," + "COALESCE(c.main_material_des,'') main_material_des," + "COALESCE(b.mo_custom_code,'') mo_custom_code," + "COALESCE(b.mo_custom_des,'') mo_custom_des," + "'N' as repair_flag " + "from c_mes_aps_plan_mo b " + "inner join c_mes_fmod_small_model c " + "on b.prod_line_id=c.prod_line_id and b.small_model_type=c.small_model_type " + "inner join sys_fmod_prod_line d " + "on c.prod_line_id=d.prod_line_id " + "where b.make_order='" + make_order + "' and d.prod_line_code='" + prod_line_code + "' " + "LIMIT 1 OFFSET 0";
                itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, true, request, apiRoutePath);
                log.error("itemListMo:{}", itemListMo);
                return CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
            }
            return CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据条码或者上线工位获取到订单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //根据条码或者上线工位获取到订单 含主物料条码
    @RequestMapping(value = "/MesCoreApsMoOnlineSelect02", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsMoOnlineSelect02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsMoOnlineSelect02";
        String selectResult = "";
        String errorMsg = "";
        String meOnlineFlowTable = "c_mes_me_online_flow";
        String meStationFlowTable = "c_mes_me_station_flow";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");
            String online_station_code = jsonParas.getString("online_station_code");//传递上线工位
            String prod_line_code = jsonParas.getString("prod_line_code");
            String serial_num = jsonParas.getString("serial_num");
            String make_order = jsonParas.getString("make_order");
            if (make_order != null && !make_order.equals("")) {
                String sqlMo = "select b.make_order," + "COALESCE(b.product_batch,'') product_batch,b.small_model_type,c.model_code," + "COALESCE(c.main_material_des,'') main_material_des," + "COALESCE(b.mo_custom_code,'') mo_custom_code," + "COALESCE(b.mo_custom_des,'') mo_custom_des," + "'N' as repair_flag " + "from c_mes_aps_plan_mo b " + "inner join c_mes_fmod_small_model c " + "on b.prod_line_id=c.prod_line_id and b.small_model_type=c.small_model_type " + "inner join sys_fmod_prod_line d " + "on c.prod_line_id=d.prod_line_id " + "where b.make_order='" + make_order + "' and d.prod_line_code='" + prod_line_code + "' " + "LIMIT 1 OFFSET 0";
                itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, true, request, apiRoutePath);
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
                return selectResult;
            }
            if (serial_num == null || serial_num.equals("")) {
                String sqlMo = "select b.make_order," + "COALESCE(b.product_batch,'') product_batch,b.small_model_type,c.model_code," + "COALESCE(c.main_material_des,'') main_material_des," + "COALESCE(b.mo_custom_code,'') mo_custom_code," + "COALESCE(b.mo_custom_des,'') mo_custom_des," + "'N' as repair_flag,b.mo_plan_count " + "from c_mes_aps_station_mo a inner join c_mes_aps_plan_mo b " + "on a.mo_id=b.mo_id " + "inner join c_mes_fmod_small_model c  " + "on b.prod_line_id=c.prod_line_id and b.small_model_type=c.small_model_type " + "where a.station_code='" + online_station_code + "' LIMIT 1 OFFSET 0";
                itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, true, request, apiRoutePath);
                if (itemListMo != null && itemListMo.size() > 0) {
                    String delete01 = "delete from c_mes_me_station_mis where station_code='" + station_code + "'";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, delete01, false, request, apiRoutePath);
                    String make_order2 = itemListMo.get(0).get("make_order").toString();
                    String product_batch2 = itemListMo.get(0).get("product_batch").toString();
                    String small_model_type2 = itemListMo.get(0).get("small_model_type").toString();
                    Integer model_code2 = Integer.parseInt(itemListMo.get(0).get("model_code").toString());
                    Integer mo_plan_count2 = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
                    Query queryBigData2 = new Query();
                    queryBigData2.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                    queryBigData2.addCriteria(Criteria.where("station_code").is(station_code));
                    queryBigData2.addCriteria(Criteria.where("make_order").is(make_order));
                    queryBigData2.addCriteria(Criteria.where("enable_flag").is("Y"));
                    long moFlowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData2.getQueryObject());
                    Integer mo_finish_count2 = (int) moFlowCount;
                    String sqlInsertMis = "insert into c_mes_me_station_mis " + "(user_name,arrive_date,station_code,serial_num," + "make_order,product_batch," + "small_model_type,model_code,mo_plan_count,mo_finish_count," + "serial_status,online_time,serial_stay_time,ideal_beats,actual_beats," + "shift_code,shift_des) values " + "('" + station_code + "','" + CFuncUtilsSystem.GetNowDateTime("") + "','" + station_code + "',''," + "'" + make_order2 + "','" + product_batch2 + "'," + "'" + small_model_type2 + "'," + model_code2 + "," + mo_plan_count2 + "," + mo_finish_count2 + "," + "'正常件','" + CFuncUtilsSystem.GetNowDateTime("") + "',0,0,0," + "'','')";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertMis, false, request, apiRoutePath);
                }
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
                return selectResult;
            }
            //从过站读取
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meOnlineFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            itemListMo = new ArrayList<>();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> map = new HashMap<>();
                String small_model_type = docItemBigData.getString("small_model_type");
                String main_material_des = "";
                String sql = "select COALESCE(main_material_des,'') main_material_des from c_mes_fmod_small_model " + "where small_model_type='" + small_model_type + "'";
                List<Map<String, Object>> model = cFuncDbSqlExecute.ExecSelectSql(station_code, sql, true, request, apiRoutePath);
                if (model != null && model.size() > 0) {
                    main_material_des = model.get(0).get("main_material_des").toString();
                }
                map.put("make_order", docItemBigData.getString("make_order"));
                map.put("product_batch", docItemBigData.getString("product_batch"));
                map.put("small_model_type", small_model_type);
                map.put("main_material_des", main_material_des);
                map.put("model_code", docItemBigData.getInteger("model_code"));
                map.put("mo_custom_code", docItemBigData.getString("mo_custom_code"));
                map.put("mo_custom_des", docItemBigData.getString("mo_custom_des"));
                map.put("print_barcode", docItemBigData.getString("print_barcode"));
                String repair_flag = "N";
                if (docItemBigData.containsKey("repair_flag")) {
                    repair_flag = docItemBigData.getString("repair_flag");
                }
                map.put("repair_flag", repair_flag);
                itemListMo.add(map);
                break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (itemListMo == null || itemListMo.size() == 0) {
                String sqlMo = "select b.make_order," + "COALESCE(b.product_batch,'') product_batch,b.small_model_type,c.model_code," + "COALESCE(b.mo_custom_code,'') mo_custom_code," + "COALESCE(b.mo_custom_des,'') mo_custom_des," + "'N' as repair_flag,b.mo_plan_count " + "from c_mes_aps_station_mo a inner join c_mes_aps_plan_mo b " + "on a.mo_id=b.mo_id " + "inner join c_mes_fmod_small_model c  " + "on b.prod_line_id=c.prod_line_id and b.small_model_type=c.small_model_type " + "where a.station_code='" + online_station_code + "' LIMIT 1 OFFSET 0";
                itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, true, request, apiRoutePath);
                if (itemListMo != null && itemListMo.size() > 0) {
                    String delete01 = "delete from c_mes_me_station_mis where station_code='" + station_code + "'";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, delete01, false, request, apiRoutePath);
                    String make_order2 = itemListMo.get(0).get("make_order").toString();
                    String product_batch2 = itemListMo.get(0).get("product_batch").toString();
                    String small_model_type2 = itemListMo.get(0).get("small_model_type").toString();
                    Integer model_code2 = Integer.parseInt(itemListMo.get(0).get("model_code").toString());
                    Integer mo_plan_count2 = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
                    Query queryBigData2 = new Query();
                    queryBigData2.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                    queryBigData2.addCriteria(Criteria.where("station_code").is(station_code));
                    queryBigData2.addCriteria(Criteria.where("make_order").is(make_order));
                    queryBigData2.addCriteria(Criteria.where("enable_flag").is("Y"));
                    long moFlowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData2.getQueryObject());
                    Integer mo_finish_count2 = (int) moFlowCount;
                    String sqlInsertMis = "insert into c_mes_me_station_mis " + "(user_name,arrive_date,station_code,serial_num," + "make_order,product_batch," + "small_model_type,model_code,mo_plan_count,mo_finish_count," + "serial_status,online_time,serial_stay_time,ideal_beats,actual_beats," + "shift_code,shift_des) values " + "('" + station_code + "','" + CFuncUtilsSystem.GetNowDateTime("") + "','" + station_code + "',''," + "'" + make_order2 + "','" + product_batch2 + "'," + "'" + small_model_type2 + "'," + model_code2 + "," + mo_plan_count2 + "," + mo_finish_count2 + "," + "'正常件','" + CFuncUtilsSystem.GetNowDateTime("") + "',0,0,0," + "'','')";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertMis, false, request, apiRoutePath);
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据条码或者上线工位获取到订单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //根据条码或者上线工位获取到订单
    @RequestMapping(value = "/MesCoreApsMoOnlineSelect03", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsMoOnlineSelect03(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsMoOnlineSelect3";
        String selectResult = "";
        String errorMsg = "";
        String meOnlineFlowTable = "c_mes_me_online_flow";
        try {
            String prod_line_code = jsonParas.getString("prod_line_code");
            String serial_num = jsonParas.getString("serial_num");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            List<String> serial_nums = Arrays.asList(serial_num.split(","));
            int size = serial_nums.stream().distinct().collect(Collectors.toList()).size();
            Assert.isTrue(serial_nums.size() == size, "重码校验失败，当前模组码中存在重复模组码");
            queryBigData.addCriteria(Criteria.where("serial_num").in(serial_nums));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            List<Map<String, Object>> itemListMo = mongoTemplate.find(queryBigData, Document.class, meOnlineFlowTable).stream().map(m -> {
                Map<String, Object> map = new HashMap<>();
                map.putAll(m);
                String repair_flag = "N";
                if (m.containsKey("repair_flag")) {
                    repair_flag = m.getString("repair_flag");
                }
                map.put("repair_flag", repair_flag);
                return map;
            }).collect(Collectors.toList());
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, JSONArray.toJSONString(itemListMo), "", 0);
        } catch (Exception ex) {
            errorMsg = "根据条码或者上线工位获取到订单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //上线信息存储
    @RequestMapping(value = "/MesCoreApsMoOnlineInsert", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsMoOnlineInsert(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsMoOnlineInsert";
        String transResult = "";
        String errorMsg = "";
        String meOnlineFlowTable = "c_mes_me_online_flow";
        try {
            String station_code = jsonParas.getString("station_code");
            String make_order = jsonParas.getString("make_order");
            String prod_line_code = jsonParas.getString("prod_line_code");
            String serial_type = jsonParas.getString("serial_type");
            String container_num = jsonParas.getString("container_num");
            String print_barcode = jsonParas.getString("print_barcode");
            String serial_num_list = jsonParas.getString("serial_num_list");
            String repair_flag = jsonParas.getString("repair_flag");
            if (serial_type == null) serial_type = "";
            if (container_num == null) container_num = "";
            if (print_barcode == null) print_barcode = "";
            if (repair_flag == null) repair_flag = "";
            if (!repair_flag.equals("Y")) repair_flag = "N";
            String[] serialNumList = serial_num_list.split(",", -1);
            if (serialNumList == null || serialNumList.length <= 0) {
                errorMsg = "工位号{" + station_code + "},保存上线信息时不能传递条码集合{" + serial_num_list + "}为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String sqlMo = "select COALESCE(a.product_batch,'') product_batch," + "COALESCE(a.mo_custom_code,'') mo_custom_code," + "COALESCE(a.mo_custom_des,'') mo_custom_des," + "COALESCE(a.small_model_type,'') small_model_type," + "COALESCE(c.model_code,0) model_code " + "from c_mes_aps_plan_mo a inner join sys_fmod_prod_line b " + "on a.prod_line_id=b.prod_line_id inner join c_mes_fmod_small_model c " + "on a.small_model_type=c.small_model_type and b.prod_line_id=c.prod_line_id " + "where b.prod_line_code='" + prod_line_code + "' and a.make_order='" + make_order + "' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, false, request, apiRoutePath);
            if (itemListMo == null || itemListMo.size() <= 0) {
                errorMsg = "工位号{" + station_code + "},根据订单号{" + make_order + "},未查询到订单信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String product_batch = itemListMo.get(0).get("product_batch").toString();
            String mo_custom_code = itemListMo.get(0).get("mo_custom_code").toString();
            String mo_custom_des = itemListMo.get(0).get("mo_custom_des").toString();
            String small_model_type = itemListMo.get(0).get("small_model_type").toString();
            int model_code = Integer.parseInt(itemListMo.get(0).get("model_code").toString());
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            //1.先禁用
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("serial_num").in(serialNumList));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            Update updateBigData = new Update();
            updateBigData.set("enable_flag", "N");
            mongoTemplate.updateMulti(queryBigData, updateBigData, meOnlineFlowTable);
            //再插入
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            for (String serial_num : serialNumList) {
                Map<String, Object> mapDataItem = new HashMap<>();
                String online_flow_id = CFuncUtilsSystem.CreateUUID(true);
                mapDataItem.put("item_date", item_date);
                mapDataItem.put("item_date_val", item_date_val);
                mapDataItem.put("online_flow_id", online_flow_id);
                mapDataItem.put("prod_line_code", prod_line_code);
                mapDataItem.put("station_code", station_code);
                mapDataItem.put("serial_type", serial_type);
                mapDataItem.put("serial_num", serial_num);
                mapDataItem.put("container_num", container_num);
                mapDataItem.put("staff_id", station_code);
                mapDataItem.put("make_order", make_order);
                mapDataItem.put("product_batch", product_batch);
                mapDataItem.put("mo_custom_code", mo_custom_code);
                mapDataItem.put("mo_custom_des", mo_custom_des);
                mapDataItem.put("small_model_type", small_model_type);
                mapDataItem.put("model_code", model_code);
                mapDataItem.put("print_barcode", print_barcode);
                mapDataItem.put("repair_flag", repair_flag);
                mapDataItem.put("enable_flag", "Y");
                lstDocuments.add(mapDataItem);
            }
            mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, meOnlineFlowTable).insert(lstDocuments).execute();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "上线信息存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更新自动上线数量
    @Transactional
    @RequestMapping(value = "/MesCoreApsMoUpdateOnLineCount", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsMoUpdateOnLineCount(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsMoUpdateOnLineCount";
        String transResult = "";
        String errorMsg = "";
        try {
            String prod_line_id = jsonParas.getString("prod_line_id");
            String station_code = jsonParas.getString("station_code");
            String make_order = jsonParas.getString("make_order");
            //订单类型：WAIT_PUBLISH(待发布与验证)、LINE_UP(排队)、CARRY_ON(进行)、COMPLETE(完成)、HAND_UP(挂起)、SHUT_DOWN(关闭)
            String mo_id = "";
            Integer mo_plan_count = 0;//计划数量
            Integer mo_online_count = 0;//实际上线数量
            String mo_status = "";
            String sqlMo = "select mo_id," + "mo_plan_count," + "mo_online_count," + "mo_status " + "from c_mes_aps_plan_mo " + "where prod_line_id=" + prod_line_id + " " + "and make_order='" + make_order + "' " + "and mo_plan_count>mo_online_count " + "and enable_flag='Y' " + "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, false, request, apiRoutePath);
            if (itemListMo != null && itemListMo.size() > 0) {
                mo_id = itemListMo.get(0).get("mo_id").toString();
                mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
                mo_online_count = Integer.parseInt(itemListMo.get(0).get("mo_online_count").toString());
                mo_status = itemListMo.get(0).get("mo_status").toString();
                /*
                if(mo_online_count+1>=mo_plan_count){
                    mo_status="COMPLETE";
                }*/
                if (station_code.equals("OP3010")) {
                    //查询是否选择了订单
                    String sqlStationMo = "select mo_id " + "from c_mes_aps_station_mo " + "where make_order='" + make_order + "' " + "and station_code='" + station_code + "' ";
                    List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, request, apiRoutePath);
                    if (itemListStationMo != null && itemListStationMo.size() > 0) {
                        mo_status = "LINE_UP";
                    }
                }
                String sqlUpdate = "update c_mes_aps_plan_mo set " + "mo_online_count=mo_online_count+1," + "mo_status='" + mo_status + "' " + "where mo_id=" + mo_id;
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdate, false, request, apiRoutePath);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, mo_id, "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "更新自动上线数量异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //校验前工位质量
    @RequestMapping(value = "/MesCoreApsPreStationQulityCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsPreStationQulityCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsPreStationQulityCheck";
        String selectResult = "";
        String errorMsg = "";
        String mesMeStationFlow = "c_mes_me_station_flow";
        try {
            String station_code = jsonParas.getString("station_code");
            String serial_num = jsonParas.getString("serial_num");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(mesMeStationFlow).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).limit(1).iterator();
            List<Map<String, Object>> itemList = new ArrayList<>();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> map = new HashMap<>();
                map.put("make_order", docItemBigData.getString("make_order"));
                map.put("product_batch", docItemBigData.getString("product_batch"));
                map.put("small_model_type", docItemBigData.getString("small_model_type"));
                map.put("model_code", docItemBigData.getInteger("model_code"));
                map.put("repair_flag", docItemBigData.getString("repair_flag"));
                map.put("quality_sign", docItemBigData.getString("quality_sign"));
                itemList.add(map);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据条码或者上线工位获取到订单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
