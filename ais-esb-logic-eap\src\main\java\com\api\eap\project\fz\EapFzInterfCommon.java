package com.api.eap.project.fz;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.UUID;

/**
 * <p>
 * 方正EAP接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Service
public class EapFzInterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;

    //创建报文Header
    public JSONObject CreateHeader(String func_name) throws Exception {
        String nowDate = CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss");
        JSONObject jbHeader = new JSONObject();
        jbHeader.put("timestamp", nowDate);
        jbHeader.put("messagename", func_name);
        jbHeader.put("from", "EQP");
        jbHeader.put("to", "EAP");
        jbHeader.put("token", UUID.randomUUID().toString().replace("-", ""));
        return jbHeader;
    }

    //创建报文Result
    public JSONObject CreateResult(boolean success, JSONObject result, String message) throws Exception {
        JSONObject jbResult = new JSONObject();
        jbResult.put("success", success);
        jbResult.put("result", result);
        JSONObject error = new JSONObject();
        error.put("error", message);
        error.put("error", "");
        jbResult.put("error", error);
        return jbResult;
    }

    //创建发送参数报文
    public JSONObject CreateSendParas(String func_name, JSONObject jbBody) throws Exception {
        JSONObject jbSendParas = new JSONObject();
        JSONObject jbHeader = CreateHeader(func_name);
        JSONObject jbBody2 = jbBody;
        if (jbBody2 == null) jbBody2 = new JSONObject();
        jbSendParas.put("header", jbHeader);
        jbSendParas.put("body", jbBody2);
        return jbSendParas;
    }

    //执行接口
    public JSONObject PostJbBackJb(String url, JSONObject jsonParas) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<JSONObject> formEntity = new HttpEntity<>(jsonParas, headers);
        JSONObject jsonResult = restTemplate.postForObject(url, formEntity, JSONObject.class);
        return jsonResult;
    }
}
