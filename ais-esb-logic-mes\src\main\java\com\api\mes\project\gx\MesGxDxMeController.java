package com.api.mes.project.gx;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.mes.core.interf.MesCoreInterfOriginalDxController;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 处理电芯工艺逻辑
 * 1.根据当前电芯判断产线
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@Slf4j
@RestController
@RequestMapping("/mes/project/gx")
public class MesGxDxMeController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MesCoreInterfOriginalDxController mesCoreInterfOriginalDxController;

    //到位后清空上线临时表
    @RequestMapping(value = "/MesGxDxOnlineClear", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxDxOnlineClear(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxDxOnlineClear";
        String transResult = "";
        String errorMsg = "";
        String dxOriginalTable = "c_mes_me_dx_online";
        try {
            mongoTemplate.dropCollection(dxOriginalTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "到位后清空上线临时表异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //根据电芯条码选择产线
    @RequestMapping(value = "/MesGxDxLineChoice", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxDxLineChoice(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxDxLineChoice";
        String selectResult = "";
        String errorMsg = "";
        String dxOriginalTable = "c_mes_me_dx_original";
        try {
            String station_code = jsonParas.getString("station_code");
            String online_station_code = jsonParas.getString("online_station_code");//传递上线工位
            String prod_line_id = jsonParas.getString("prod_line_id");
            String dx_barcode = jsonParas.getString("dx_barcode");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").is(dx_barcode));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(dxOriginalTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            int dx_gear = -1;//档位
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                dx_gear = docItemBigData.getInteger("dx_gear");
                break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (dx_gear <= 0) {
                errorMsg = "根据当前电芯条码{" + dx_barcode + "}未找到电芯档位基础数据";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            //查找订单信息
            String sqlMo = "select a.mo_id,a.make_order,a.small_model_type " + "from c_mes_aps_plan_mo a inner join c_mes_aps_station_mo b " + "on a.make_order=b.make_order inner join sys_fmod_prod_line c " + "on a.prod_line_id=c.prod_line_id inner join sys_fmod_station d " + "on c.prod_line_id=d.prod_line_id and b.station_id=d.station_id " + "where c.prod_line_id=" + prod_line_id + " and d.station_code='" + online_station_code + "' " + "and a.enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, true, request, apiRoutePath);
            if (itemListMo == null || itemListMo.size() <= 0) {
                errorMsg = "根据上线工位{" + online_station_code + "},未查找到当前上线订单";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String mo_id = itemListMo.get(0).get("mo_id").toString();
            String make_order = itemListMo.get(0).get("make_order").toString();
            String small_model_type = itemListMo.get(0).get("small_model_type").toString();
            //查询是否具备模组配方
            String sqlRecipe = "select b.recipe_id,b.recipe_name,b.recipe_version " + "from c_mes_aps_plan_mo_recipe a inner join c_mes_fmod_recipe b on a.recipe_id=b.recipe_id " + "where a.mo_id=" + mo_id + " and b.recipe_type='MODELDX' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlRecipe, true, request, apiRoutePath);
            if (itemListRecipe == null || itemListRecipe.size() <= 0) {
                errorMsg = "根据当前选择订单{" + make_order + "}未找到模组配方版本";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String recipe_id = itemListRecipe.get(0).get("recipe_id").toString();
            String recipe_name = itemListRecipe.get(0).get("recipe_name").toString();
            String recipe_version = itemListRecipe.get(0).get("recipe_version").toString();
            recipe_name = recipe_name + recipe_version;
            String sqlMzRecipe = "select " + "'" + make_order + "' as make_order," + "'" + small_model_type + "' as small_model_type," + "'" + recipe_name + "' as recipe_name," + "COALESCE(b.line_num,0) line_num " + "from c_mes_fmod_recipe_mz a inner join c_mes_fmod_recipe_mz_dx b on a.mz_id=b.mz_id " + "where a.recipe_id=" + recipe_id + " and a.enable_flag='Y' and b.enable_flag='Y' and b.dx_gear=" + dx_gear + " " + "order by b.dx_num LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMzRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzRecipe, true, request, apiRoutePath);
            if (itemListMzRecipe == null || itemListMzRecipe.size() <= 0) {
                errorMsg = "根据当前选择订单{" + make_order + "},电芯档位{" + dx_gear + "},未找到模组配方基础数据";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMzRecipe, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "查询电芯选择产线数据异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //电芯扫描合格标志验证
    @RequestMapping(value = "/MesGxDxScanCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxDxScanCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxDxScanCheck";
        String tranResult = "";
        String errorMsg = "";
        String dx_value_list = "";
        String now_step = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String online_station_code = jsonParas.getString("online_station_code");//传递上线工位
            String prod_line_id = jsonParas.getString("prod_line_id");
            String dx_barcode1 = jsonParas.getString("dx_barcode1");
            String dx_barcode2 = jsonParas.getString("dx_barcode2");
            String dx_barcode3 = jsonParas.getString("dx_barcode3");
            String dx_barcode4 = jsonParas.getString("dx_barcode4");
            String select_line_num = jsonParas.getString("select_line_num");//当前料框为选择的那条产线
            dx_value_list = jsonParas.getString("dx_value_list");//四组电芯测量值(电阻、开路电压、负路电压)
            String[] dxValueList = dx_value_list.split(",", -1);
            List<String> lstDxBarCode = new ArrayList<>();
            lstDxBarCode.add(dx_barcode1);
            lstDxBarCode.add(dx_barcode2);
            lstDxBarCode.add(dx_barcode3);
            lstDxBarCode.add(dx_barcode4);

            now_step = "解析电芯OCV数据";
            if (dxValueList == null || dxValueList.length < 12) {
                errorMsg = "OCV测试数据不符合12个数组要求{" + dx_value_list + "}";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            double dx_barcode1_dz = Double.parseDouble(dxValueList[0]);
            double dx_barcode1_kldy = Double.parseDouble(dxValueList[1]);
            double dx_barcode1_fldy = Double.parseDouble(dxValueList[2]);
            double dx_barcode2_dz = Double.parseDouble(dxValueList[3]);
            double dx_barcode2_kldy = Double.parseDouble(dxValueList[4]);
            double dx_barcode2_fldy = Double.parseDouble(dxValueList[5]);
            double dx_barcode3_dz = Double.parseDouble(dxValueList[6]);
            double dx_barcode3_kldy = Double.parseDouble(dxValueList[7]);
            double dx_barcode3_fldy = Double.parseDouble(dxValueList[8]);
            double dx_barcode4_dz = Double.parseDouble(dxValueList[9]);
            double dx_barcode4_kldy = Double.parseDouble(dxValueList[10]);
            double dx_barcode4_fldy = Double.parseDouble(dxValueList[11]);
            //获取订单，然后做判断
            //1.查询当前工位是否选择了订单
            String sqlStationMo = "select mo_id,make_order " + "from c_mes_aps_station_mo " + "where station_code='" + online_station_code + "'";
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, request, apiRoutePath);
            if (itemListStationMo == null || itemListStationMo.size() <= 0) {
                errorMsg = "上线工位未选择订单";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            now_step = "查询上线工位选择订单";
            String mo_id = itemListStationMo.get(0).get("mo_id").toString();
            String make_order = itemListStationMo.get(0).get("make_order").toString();
            String sqlMoRecipe = "select c.recipe_id,d.mz_id,a.small_model_type " + "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " + "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " + "on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_mz d " + "on c.recipe_id=d.recipe_id " + "where a.enable_flag='Y' and c.enable_flag='Y' and d.enable_flag='Y' " + "and c.recipe_type='MODELDX' and a.mo_id=" + mo_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe, false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}不可用或者未配置模组电芯配方";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            now_step = "查询模组配方";
            String mz_id = itemListMoRecipe.get(0).get("mz_id").toString();
            String small_model_type = itemListMoRecipe.get(0).get("small_model_type").toString();
            //1.根据模组配方ID查找LIMIT项目
            String sqlMzLimitRecipe = "select a.mz_limit_code,a.down_limit,a.upper_limit,a.ng_rack_code," + "COALESCE(b.mz_limit_id,0) mz_limit_id," + "COALESCE(b.down_limit,0) down_limit2," + "COALESCE(b.upper_limit,0) upper_limit2," + "COALESCE(b.ng_rack_code,0) ng_rack_code2 " + "from c_mes_fmod_recipe_mz_limit_must a left join c_mes_fmod_recipe_mz_limit b " + "on a.mz_limit_code=b.mz_limit_code and b.mz_id=" + mz_id + " and b.enable_flag='Y' " + "order by a.mz_limit_index";
            List<Map<String, Object>> itemListMzLimitRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzLimitRecipe, false, request, apiRoutePath);
            now_step = "查询模组LIMIT约束项目";
            //2.根据模组配方ID查找条码约束项目
            String sqlMzNgBarRecipe = "select mz_dxbar_ng_id,dxbar_ng_name,start_index,end_index," + "value_list,ng_way,ng_rack_num " + "from c_mes_fmod_recipe_mz_dxbar_ng " + "where mz_id=" + mz_id + " and enable_flag='Y'";
            List<Map<String, Object>> itemListMzNgBarRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzNgBarRecipe, false, request, apiRoutePath);
            now_step = "查询条码约束条件";
            //3.判断当前为那个档位
            String sqlGear = "select dx_gear " + "from c_mes_fmod_recipe_mz_dx " + "where mz_id=" + mz_id + " and line_num=" + select_line_num + " " + "and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListGear = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlGear, false, request, apiRoutePath);
            if (itemListGear == null || itemListGear.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "},未能根据产线{" + select_line_num + "},找到对应的档位";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            now_step = "根据产线找到档位";
            int need_dx_gear = Integer.parseInt(itemListGear.get(0).get("dx_gear").toString());
            int ng_rack_num_noread = 1;//未读取到条码放入曹号
            int ng_rack_num_nodata = 5;//电芯原始数据未存在放入曹号
            int ng_rack_num_chongma = 5;//电芯已做放入曹号
            int ng_rack_num_gear = 5;//档位NG放入曹号
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    int ng_rack_code = Integer.parseInt(map.get("ng_rack_code").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    int ng_rack_code2 = Integer.parseInt(map.get("ng_rack_code2").toString());
                    if (mz_limit_id > 0) ng_rack_code = ng_rack_code2;
                    if (ng_rack_code <= 0) ng_rack_code = 5;
                    if (mz_limit_code.equals("dx_barcode_noread")) ng_rack_num_noread = ng_rack_code;
                    if (mz_limit_code.equals("dx_nodata")) ng_rack_num_nodata = ng_rack_code;
                    if (mz_limit_code.equals("dx_chongma")) ng_rack_num_chongma = ng_rack_code;
                    if (mz_limit_code.equals("dx_gear")) ng_rack_num_gear = ng_rack_code;
                }
            }
            now_step = "进入单个电芯判断";
            int result1 = GetDxBarCodeNgCode(dx_barcode1, need_dx_gear, ng_rack_num_noread, ng_rack_num_nodata, ng_rack_num_chongma, ng_rack_num_gear, dx_barcode1_dz, dx_barcode1_kldy, dx_barcode1_fldy, itemListMzLimitRecipe, itemListMzNgBarRecipe, make_order, small_model_type, station_code, 1, lstDxBarCode);
            int result2 = GetDxBarCodeNgCode(dx_barcode2, need_dx_gear, ng_rack_num_noread, ng_rack_num_nodata, ng_rack_num_chongma, ng_rack_num_gear, dx_barcode2_dz, dx_barcode2_kldy, dx_barcode2_fldy, itemListMzLimitRecipe, itemListMzNgBarRecipe, make_order, small_model_type, station_code, 2, lstDxBarCode);
            int result3 = GetDxBarCodeNgCode(dx_barcode3, need_dx_gear, ng_rack_num_noread, ng_rack_num_nodata, ng_rack_num_chongma, ng_rack_num_gear, dx_barcode3_dz, dx_barcode3_kldy, dx_barcode3_fldy, itemListMzLimitRecipe, itemListMzNgBarRecipe, make_order, small_model_type, station_code, 3, lstDxBarCode);
            int result4 = GetDxBarCodeNgCode(dx_barcode4, need_dx_gear, ng_rack_num_noread, ng_rack_num_nodata, ng_rack_num_chongma, ng_rack_num_gear, dx_barcode4_dz, dx_barcode4_kldy, dx_barcode4_fldy, itemListMzLimitRecipe, itemListMzNgBarRecipe, make_order, small_model_type, station_code, 4, lstDxBarCode);

            String result = String.valueOf(result1) + "," + String.valueOf(result2) + "," + String.valueOf(result3) + "," + String.valueOf(result4);
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "电芯扫描合格标志验证异常,OCV测试数据{" + dx_value_list + "},当前步骤{" + now_step + "}:" + ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //电芯扫描合格标志验证（唐山/金寨国轩，单个电芯检测）
    @RequestMapping(value = "/MesGxDxScanCheck2", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxDxScanCheck2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxDxScanCheck2";
        String tranResult = "";
        String errorMsg = "";
        String dx_value_list = "";
        String now_step = "";
        try {
            //是否是测试阶段，测试阶段不需要判定，直接给合格
            String testModeFlag = cFuncDbSqlResolve.GetParameterValue("TestModeFlag");
            String station_code = jsonParas.getString("station_code");
            String online_station_code = jsonParas.getString("online_station_code");//传递上线工位
            String prod_line_id = jsonParas.getString("prod_line_id");
            String dx_barcode = jsonParas.getString("dx_barcode");
            String select_line_num = jsonParas.getString("select_line_num");//当前料框为选择的那条产线
            dx_value_list = jsonParas.getString("dx_value_list");//电芯测量值(电阻、开路电压、负路电压)
            String[] dxValueList = dx_value_list.split(",", -1);
            List<String> lstDxBarCode = new ArrayList<>();
            lstDxBarCode.add(dx_barcode);
            double dx_barcode_dz = 0;
            double dx_barcode_kldy = 0;
            double dx_barcode_fldy = 0;
            if (!testModeFlag.equals("Y")) {
                now_step = "解析电芯OCV数据";
                if (dxValueList == null || dxValueList.length < 3) {
                    errorMsg = "OCV测试数据不符合3个数组要求{" + dx_value_list + "}";
                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return tranResult;
                }
                dx_barcode_dz = Double.parseDouble(dxValueList[1]);
                dx_barcode_kldy = Double.parseDouble(dxValueList[0]);
                dx_barcode_fldy = Double.parseDouble(dxValueList[2]);
            }
            //获取订单，然后做判断
            //1.查询当前工位是否选择了订单
            String sqlStationMo = "select mo_id,make_order " + "from c_mes_aps_station_mo " + "where station_code='" + online_station_code + "'";
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, request, apiRoutePath);
            if (itemListStationMo == null || itemListStationMo.size() <= 0) {
                errorMsg = "上线工位未选择订单";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            now_step = "查询上线工位选择订单";
            String mo_id = itemListStationMo.get(0).get("mo_id").toString();
            String make_order = itemListStationMo.get(0).get("make_order").toString();
            String sqlMoRecipe = "select c.recipe_id,d.mz_id,a.small_model_type " + "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_mz d on c.recipe_id=d.recipe_id where a.enable_flag='Y' and c.enable_flag='Y' and d.enable_flag='Y' and c.recipe_type='MODELDX' and a.mo_id=" + mo_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe, false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}不可用或者未配置模组电芯配方";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            now_step = "查询模组配方";
            String mz_id = itemListMoRecipe.get(0).get("mz_id").toString();
            String small_model_type = itemListMoRecipe.get(0).get("small_model_type").toString();
            //1.根据模组配方ID查找LIMIT项目
            String sqlMzLimitRecipe = "select a.mz_limit_code,a.down_limit,a.upper_limit,a.ng_rack_code," + "COALESCE(b.mz_limit_id,0) mz_limit_id," + "COALESCE(b.down_limit,0) down_limit2," + "COALESCE(b.upper_limit,0) upper_limit2," + "COALESCE(b.ng_rack_code,0) ng_rack_code2 " + "from c_mes_fmod_recipe_mz_limit_must a left join c_mes_fmod_recipe_mz_limit b " + "on a.mz_limit_code=b.mz_limit_code and b.mz_id=" + mz_id + " and b.enable_flag='Y' " + "order by a.mz_limit_index";
            List<Map<String, Object>> itemListMzLimitRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzLimitRecipe, false, request, apiRoutePath);
            now_step = "查询模组LIMIT约束项目";
            //2.根据模组配方ID查找条码约束项目
            String sqlMzNgBarRecipe = "select mz_dxbar_ng_id,dxbar_ng_name,start_index,end_index," + "value_list,ng_way,ng_rack_num " + "from c_mes_fmod_recipe_mz_dxbar_ng " + "where mz_id=" + mz_id + " and enable_flag='Y'";
            List<Map<String, Object>> itemListMzNgBarRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzNgBarRecipe, false, request, apiRoutePath);
            now_step = "查询条码约束条件";
            //3.判断当前为那个档位
            String sqlGear = "select dx_gear " + "from c_mes_fmod_recipe_mz_dx " + "where mz_id=" + mz_id + "  " + "and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListGear = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlGear, false, request, apiRoutePath);
            if (itemListGear == null || itemListGear.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "},未能找到对应的档位";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            now_step = "根据产线找到档位";
            int need_dx_gear = Integer.parseInt(itemListGear.get(0).get("dx_gear").toString());
            int ng_rack_num_noread = 1;//未读取到条码放入曹号
            int ng_rack_num_nodata = 5;//电芯原始数据未存在放入曹号
            int ng_rack_num_chongma = 5;//电芯已做放入曹号
            int ng_rack_num_gear = 5;//档位NG放入曹号
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    int ng_rack_code = Integer.parseInt(map.get("ng_rack_code").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    int ng_rack_code2 = Integer.parseInt(map.get("ng_rack_code2").toString());
                    if (mz_limit_id > 0) ng_rack_code = ng_rack_code2;
                    if (ng_rack_code <= 0) ng_rack_code = 5;
                    if (mz_limit_code.equals("dx_barcode_noread")) ng_rack_num_noread = ng_rack_code;
                    if (mz_limit_code.equals("dx_nodata")) ng_rack_num_nodata = ng_rack_code;
                    if (mz_limit_code.equals("dx_chongma")) ng_rack_num_chongma = ng_rack_code;
                    if (mz_limit_code.equals("dx_gear")) ng_rack_num_gear = ng_rack_code;
                }
            }
            now_step = "进入单个电芯判断";
            int result = GetDxBarCodeNgCode(dx_barcode, need_dx_gear, ng_rack_num_noread, ng_rack_num_nodata, ng_rack_num_chongma, ng_rack_num_gear, dx_barcode_dz, dx_barcode_kldy, dx_barcode_fldy, itemListMzLimitRecipe, itemListMzNgBarRecipe, make_order, small_model_type, station_code, 1, lstDxBarCode);

            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, String.valueOf(result), "", 0);
        } catch (Exception ex) {
            errorMsg = "电芯扫描合格标志验证异常,OCV测试数据{" + dx_value_list + "},当前步骤{" + now_step + "}:" + ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //获取电芯条码放入曹号或者是OK
    private int GetDxBarCodeNgCode(String dx_barcode, int need_dx_gear, int ng_rack_num_noread, int ng_rack_num_nodata, int ng_rack_num_chongma, int ng_rack_num_gear, double test_dz, double test_kldy, double test_fldy, List<Map<String, Object>> itemListMzLimitRecipe, List<Map<String, Object>> itemListMzNgBarRecipe, String make_order, String small_model_type, String station_code, int position, List<String> lstDxBarCode) throws Exception {
        int result_code = 5;//1代表OK,>1代表NG
        String dxOriginalTable = "c_mes_me_dx_original";
        String mzDxRelTable = "c_mes_me_mz_dx_rel";
        try {
            int dx_gear = 0;
            String dx_ori_batch = "";
            double dx_ori_capacity = 0;
            double dx_ori_ocv4_pressure = 0;
            double dx_ori_ocr4_air = 0;
            double dx_ori_k_value = 0;
            double dx_ori_dcir = 0;
            double dx_ori_thickness = 0;
            String dx_ori_ocv4_time = "";
            double dx_pressure_kl_value = test_kldy;
            double dx_pressure_fd_value = test_fldy;
            double dx_dianzu_value = test_dz;
            double dx_k2_value = 0;
            double dx_diff_pressure = 0d;
            String dx_status = "NG";
            int ng_code = -1;
            int ng_rack = 5;
            String ng_msg = "";
            //是否是测试阶段，测试阶段不需要判定，直接给合格
            String testModeFlag = cFuncDbSqlResolve.GetParameterValue("TestModeFlag");
            if (testModeFlag.equals("Y")) {
                result_code = 1;
                ng_rack = 0;
                ng_code = 0;
                ng_msg = "";
                dx_status = "OK";
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                return result_code;
            }
            //1.先判断扫描条码是否为NG
            if (dx_barcode.equals("NG000")) {
                result_code = ng_rack_num_noread + 1;
                ng_rack = ng_rack_num_noread;
                ng_code = -1;
                ng_msg = "读取电芯条码{" + dx_barcode + "},未能读取成功";
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                return result_code;
            }
            //2.判断电芯原始数据
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").is(dx_barcode));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(dxOriginalTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                dx_gear = (docItemBigData.get("dx_gear") instanceof Double) ? docItemBigData.getDouble("dx_gear").intValue() : docItemBigData.getInteger("dx_gear");
                dx_ori_batch = docItemBigData.getString("dx_ori_batch");
                dx_ori_capacity = docItemBigData.getDouble("dx_ori_capacity");
                dx_ori_ocv4_pressure = docItemBigData.getDouble("dx_ori_ocv4_pressure");
                dx_ori_ocr4_air = docItemBigData.getDouble("dx_ori_ocr4_air");
                dx_ori_k_value = docItemBigData.getDouble("dx_ori_k_value");
                dx_ori_dcir = docItemBigData.getDouble("dx_ori_dcir");
                dx_ori_thickness = docItemBigData.getDouble("dx_ori_thickness");
                dx_ori_ocv4_time = docItemBigData.getString("dx_ori_ocv4_time");
                break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (dx_gear < 0) {
                result_code = ng_rack_num_nodata + 1;
                ng_rack = ng_rack_num_nodata;
                ng_code = -2;
                ng_msg = "未能根据电芯码{" + dx_barcode + "},查找到电芯原始数据,需要导入电芯数据";
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                return result_code;
            }
            //3.判断档位是否一致
            if (dx_gear != need_dx_gear) {
                result_code = ng_rack_num_gear + 1;
                ng_rack = ng_rack_num_gear;
                ng_code = -3;
                ng_msg = "当前电芯条码{" + dx_barcode + "},对应的档位为{" + dx_gear + "},与当前料框对应的档位{" + need_dx_gear + "}不符";
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                return result_code;
            }
            //4.判断是否重码
            //4.1 判断当组内是否有重码
            int indexDx = 0;
            for (int i = 0; i < lstDxBarCode.size(); i++) {
                if (lstDxBarCode.get(i).equals(dx_barcode)) indexDx++;
            }
            if (indexDx > 1) {
                result_code = ng_rack_num_chongma + 1;
                ng_rack = ng_rack_num_chongma;
                ng_code = -4;
                ng_msg = "当前电芯条码{" + dx_barcode + "},在一次性R1机械手条码集合中存在重码,检查相机拍摄角度";
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                return result_code;
            }
            //4.2.判断数据是否重码
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").is(dx_barcode));
            queryBigData.addCriteria(Criteria.where("mz_status").is("OK"));
            iteratorBigData = mongoTemplate.getCollection(mzDxRelTable).find(queryBigData.getQueryObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                iteratorBigData.close();
                result_code = ng_rack_num_chongma + 1;
                ng_rack = ng_rack_num_chongma;
                ng_code = -4;
                ng_msg = "当前电芯条码{" + dx_barcode + "},已与模组绑定,属于重码,若需上线,需先进行解绑";
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                return result_code;
            }
            try {
                dx_diff_pressure = new BigDecimal(String.valueOf((dx_ori_ocv4_pressure - dx_pressure_kl_value))).setScale(4, RoundingMode.DOWN).doubleValue();
            } catch (Exception e) {
                log.error("diff_pressure 格式化异常：{}--{}", dx_ori_ocv4_pressure, dx_pressure_kl_value);
                dx_diff_pressure = 0d;
            }
            //5.计算K2值
            if (dx_ori_ocv4_time != null && !dx_ori_ocv4_time.equals("")) {
                try {
                    SimpleDateFormat dfK2 = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                    dx_ori_ocv4_time = dx_ori_ocv4_time.replace("/", "-");
                    long arriveTime = dfK2.parse(dx_ori_ocv4_time).getTime();
                    long leaveTime = dfK2.parse(CFuncUtilsSystem.GetNowDateTime("")).getTime();
                    float diffTime = (float) (leaveTime - arriveTime) / (float) (1000 * 60 * 60 * 24);//获取天数差
                    //金寨新增压差值
                    dx_k2_value = (dx_ori_ocv4_pressure - dx_pressure_kl_value) / diffTime;
                    DecimalFormat decimalFormat = new DecimalFormat("0.000");
                    decimalFormat.setRoundingMode(RoundingMode.HALF_UP);
                    dx_k2_value = Double.parseDouble(decimalFormat.format(dx_k2_value));
                } catch (Exception err) {
                    dx_k2_value = -99;
                }
            } else {
                dx_k2_value = -99;
            }
            //6.依次做对配方设置做判断
            //6.1 ☆☆☆依次对电芯逻辑进行判断
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    double down_limit = Double.parseDouble(map.get("down_limit").toString());
                    double upper_limit = Double.parseDouble(map.get("upper_limit").toString());
                    int ng_rack_code = Integer.parseInt(map.get("ng_rack_code").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    double down_limit2 = Double.parseDouble(map.get("down_limit2").toString());
                    double upper_limit2 = Double.parseDouble(map.get("upper_limit2").toString());
                    int ng_rack_code2 = Integer.parseInt(map.get("ng_rack_code2").toString());
                    if (mz_limit_id > 0) ng_rack_code = ng_rack_code2;
                    if (ng_rack_code <= 0) ng_rack_code = 5;
                    if (mz_limit_id > 0) {
                        down_limit = down_limit2;
                        upper_limit = upper_limit2;
                    }
                    //电芯原始高档位开路电压范围判定(偶数档位)
                    if (mz_limit_code.equals("dx_ori_high_kl_pressure") && mz_limit_id > 0) {
                        if (dx_gear % 2 == 0) {
                            if (dx_ori_ocv4_pressure < down_limit || dx_ori_ocv4_pressure > upper_limit) {
                                result_code = ng_rack_code + 1;
                                ng_rack = ng_rack_code;
                                ng_code = -5;
                                ng_msg = "当前电芯条码{" + dx_barcode + "},原始OCV4高档位开路电压值{" + dx_ori_ocv4_pressure + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                                return result_code;
                            }
                        }
                    }
                    //电芯原始低档位开路电压范围判定(奇数档位)mV
                    if (mz_limit_code.equals("dx_ori_low_kl_pressure") && mz_limit_id > 0) {
                        if (dx_gear % 2 != 0) {
                            if (dx_ori_ocv4_pressure < down_limit || dx_ori_ocv4_pressure > upper_limit) {
                                result_code = ng_rack_code + 1;
                                ng_rack = ng_rack_code;
                                ng_code = -6;
                                ng_msg = "当前电芯条码{" + dx_barcode + "},原始OCV4低档位开路电压值{" + dx_ori_ocv4_pressure + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                                return result_code;
                            }
                        }
                    }
                    //电芯原始内阻范围判定mΩ
                    if (mz_limit_code.equals("dx_ori_neizu") && mz_limit_id > 0) {
                        if (dx_ori_ocr4_air < down_limit || dx_ori_ocr4_air > upper_limit) {
                            result_code = ng_rack_code + 1;
                            ng_rack = ng_rack_code;
                            ng_code = -7;
                            ng_msg = "当前电芯条码{" + dx_barcode + "},原始OCV4内阻值{" + dx_ori_ocr4_air + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                            return result_code;
                        }
                    }
                    //电芯原始K值范围判定
                    if (mz_limit_code.equals("dx_ori_kvalue") && mz_limit_id > 0) {
                        if (dx_ori_k_value < down_limit || dx_ori_k_value > upper_limit) {
                            result_code = ng_rack_code + 1;
                            ng_rack = ng_rack_code;
                            ng_code = -8;
                            ng_msg = "当前电芯条码{" + dx_barcode + "},原始K值{" + dx_ori_k_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                            return result_code;
                        }
                    }
                    //电芯原始高档位容量范围判定(偶数档位)
                    if (mz_limit_code.equals("dx_ori_high_container") && mz_limit_id > 0) {
                        if (dx_gear % 2 == 0) {
                            if (dx_ori_capacity < down_limit || dx_ori_capacity > upper_limit) {
                                result_code = ng_rack_code + 1;
                                ng_rack = ng_rack_code;
                                ng_code = -9;
                                ng_msg = "当前电芯条码{" + dx_barcode + "},原始高档位电容值{" + dx_ori_capacity + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                                return result_code;
                            }
                        }
                    }
                    //电芯原始低档位容量范围判定(奇数档位)
                    if (mz_limit_code.equals("dx_ori_low_container") && mz_limit_id > 0) {
                        if (dx_gear % 2 != 0) {
                            if (dx_ori_capacity < down_limit || dx_ori_capacity > upper_limit) {
                                result_code = ng_rack_code + 1;
                                ng_rack = ng_rack_code;
                                ng_code = -10;
                                ng_msg = "当前电芯条码{" + dx_barcode + "},原始低档位电容值{" + dx_ori_capacity + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                                return result_code;
                            }
                        }
                    }
                    //电芯原始DCRI范围判定
                    if (mz_limit_code.equals("dx_ori_dcir") && mz_limit_id > 0) {
                        if (dx_ori_dcir < down_limit || dx_ori_dcir > upper_limit) {
                            result_code = ng_rack_code + 1;
                            ng_rack = ng_rack_code;
                            ng_code = -11;
                            ng_msg = "当前电芯条码{" + dx_barcode + "},原始DCIR值{" + dx_ori_dcir + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                            return result_code;
                        }
                    }
                    //电芯原始厚度范围判定
                    if (mz_limit_code.equals("dx_ori_thickness") && mz_limit_id > 0) {
                        if (dx_ori_thickness < down_limit || dx_ori_thickness > upper_limit) {
                            result_code = ng_rack_code + 1;
                            ng_rack = ng_rack_code;
                            ng_code = -12;
                            ng_msg = "当前电芯条码{" + dx_barcode + "},原始厚度值{" + dx_ori_thickness + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                            return result_code;
                        }
                    }
                    //电芯测试结果开路电压范围判定mV
                    if (mz_limit_code.equals("dx_test_kl_pressure") && mz_limit_id > 0) {
                        if (dx_pressure_kl_value < down_limit || dx_pressure_kl_value > upper_limit) {
                            result_code = ng_rack_code + 1;
                            ng_rack = ng_rack_code;
                            ng_code = -13;
                            ng_msg = "当前电芯条码{" + dx_barcode + "},OCV测试开路电压值{" + dx_pressure_kl_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                            return result_code;
                        }
                    }
                    //电芯测试结果电阻范围判定mΩ
                    if (mz_limit_code.equals("dx_test_dianzu") && mz_limit_id > 0) {
                        if (dx_dianzu_value < down_limit || dx_dianzu_value > upper_limit) {
                            result_code = ng_rack_code + 1;
                            ng_rack = ng_rack_code;
                            ng_code = -14;
                            ng_msg = "当前电芯条码{" + dx_barcode + "},OCV测试电阻值{" + dx_dianzu_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                            return result_code;
                        }
                    }
                    //电芯测试结果电阻范围判定mΩ
                    if (mz_limit_code.equals("dx_test_fl_pressure") && mz_limit_id > 0) {
                        if (dx_pressure_fd_value < down_limit || dx_pressure_fd_value > upper_limit) {
                            result_code = ng_rack_code + 1;
                            ng_rack = ng_rack_code;
                            ng_code = -15;
                            ng_msg = "当前电芯条码{" + dx_barcode + "},OCV测试负路电压值{" + dx_pressure_fd_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                            return result_code;
                        }
                    }
                    //电芯计算结果K值范围判断
                    if (mz_limit_code.equals("dx_test_kvalue") && mz_limit_id > 0) {
                        if (dx_k2_value < down_limit || dx_k2_value > upper_limit) {
                            result_code = ng_rack_code + 1;
                            ng_rack = ng_rack_code;
                            ng_code = -16;
                            ng_msg = "当前电芯条码{" + dx_barcode + "},计算K2值{" + dx_k2_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                            return result_code;
                        }
                    }
                    //电芯压差
                    if (mz_limit_code.equals("dx_diff_pressure") && mz_limit_id > 0) {
                        if (dx_diff_pressure < down_limit || dx_diff_pressure > upper_limit) {
                            result_code = ng_rack_code + 1;
                            ng_rack = ng_rack_code;
                            ng_code = -16;
                            ng_msg = "当前电芯条码{" + dx_barcode + "},计算压差{" + dx_diff_pressure + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                            return result_code;
                        }
                    }
                    //电芯批次数量范围判断--这个不做开发,待定义模式;应该是需要对订单进行更新处理,然后再做判断
                    if (mz_limit_code.equals("dx_batch_count") && mz_limit_id > 0) {

                    }
                }
            }
            //6.2 ☆☆☆依次对条码规则进行判断
            if (itemListMzNgBarRecipe != null && itemListMzNgBarRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzNgBarRecipe) {
                    String dxbar_ng_name = map.get("dxbar_ng_name").toString();
                    int start_index = Integer.parseInt(map.get("start_index").toString());
                    int end_index = Integer.parseInt(map.get("end_index").toString());
                    String value_list = map.get("value_list").toString();
                    String ng_way = map.get("ng_way").toString();//NG_IN、NG_NOTIN
                    int ng_rack_code = Integer.parseInt(map.get("ng_rack_num").toString());
                    if (end_index <= start_index || end_index > dx_barcode.length()) {
                        throw new Exception("条码规则进行判断时,项目{" + dxbar_ng_name + "}设置起始位置{" + start_index + "}与结束位置{" + end_index + "}超出范围");
                    }
                    String subStr = dx_barcode.substring(start_index, end_index);
                    String[] valueList = value_list.split(",", -1);
                    if (ng_way.equals("NG_IN")) {
                        if (valueList != null && valueList.length > 0) {
                            for (int i = 0; i < valueList.length; i++) {
                                String itemValue = valueList[i];
                                if (itemValue.equals(subStr)) {
                                    result_code = ng_rack_code + 1;
                                    ng_rack = ng_rack_code;
                                    ng_code = -17;
                                    ng_msg = "当前电芯条码{" + dx_barcode + "},判断{" + dxbar_ng_name + "},截取值{" + subStr + "},在NG集合中{" + value_list + "}";
                                    InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                                    return result_code;
                                }
                            }
                        }
                    } else {
                        if (valueList != null && valueList.length > 0) {
                            Boolean isIn = false;
                            for (int i = 0; i < valueList.length; i++) {
                                String itemValue = valueList[i];
                                if (itemValue.equals(subStr)) {
                                    isIn = true;
                                    break;
                                }
                            }
                            if (!isIn) {
                                result_code = ng_rack_code + 1;
                                ng_rack = ng_rack_code;
                                ng_code = -18;
                                ng_msg = "当前电芯条码{" + dx_barcode + "},判断{" + dxbar_ng_name + "},截取值{" + subStr + "},不在集合中{" + value_list + "}";
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
                                return result_code;
                            }
                        }
                    }
                }
            }

            //终于判断成功
            result_code = 1;
            ng_rack = 0;
            ng_code = 0;
            ng_msg = "";
            dx_status = "OK";
            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, dx_status, ng_rack, ng_code, ng_msg, station_code, position, dx_diff_pressure);
        } catch (Exception ex) {
            throw ex;
        }
        return result_code;
    }

    //插入电芯合格质量数据
    private void InsertDxQuality(String dx_barcode, String make_order, String small_model_type, int dx_gear, String dx_ori_batch, double dx_ori_capacity, double dx_ori_ocv4_pressure, double dx_ori_ocr4_air, double dx_ori_k_value, double dx_ori_dcir, double dx_ori_thickness, String dx_ori_ocv4_time, double dx_pressure_kl_value, double dx_pressure_fd_value, double dx_dianzu_value, double dx_k2_value, String dx_status, int ng_rack, int ng_code, String ng_msg, String station_code, int position, double dx_diff_pressure) throws Exception {
        try {
            String dxQualityTable = "c_mes_me_dx_quality";
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String dx_quality_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("dx_quality_id", dx_quality_id);
            mapBigDataRow.put("dx_barcode", dx_barcode);
            mapBigDataRow.put("dx_location_num", position);
            mapBigDataRow.put("make_order", make_order);
            mapBigDataRow.put("small_model_type", small_model_type);
            mapBigDataRow.put("box_barcode", "");
            mapBigDataRow.put("station_code", station_code);
            mapBigDataRow.put("dx_gear", dx_gear);
            mapBigDataRow.put("dx_ori_batch", dx_ori_batch);
            mapBigDataRow.put("dx_ori_capacity", dx_ori_capacity);
            mapBigDataRow.put("dx_ori_ocv4_pressure", dx_ori_ocv4_pressure);
            mapBigDataRow.put("dx_diff_pressure", dx_diff_pressure);
            mapBigDataRow.put("dx_ori_ocr4_air", dx_ori_ocr4_air);
            mapBigDataRow.put("dx_ori_k_value", dx_ori_k_value);
            mapBigDataRow.put("dx_ori_dcir", dx_ori_dcir);
            mapBigDataRow.put("dx_ori_thickness", dx_ori_thickness);
            mapBigDataRow.put("dx_ori_ocv4_time", dx_ori_ocv4_time);
            mapBigDataRow.put("dx_pressure_kl_value", dx_pressure_kl_value);
            mapBigDataRow.put("dx_pressure_kl_time", CFuncUtilsSystem.GetNowDateTime(""));
            mapBigDataRow.put("dx_pressure_fd_value", dx_pressure_fd_value);
            mapBigDataRow.put("dx_dianzu_value", dx_dianzu_value);
            mapBigDataRow.put("dx_k2_value", dx_k2_value);
            mapBigDataRow.put("dx_status", dx_status);
            mapBigDataRow.put("ng_rack", ng_rack);
            mapBigDataRow.put("ng_code", ng_code);
            mapBigDataRow.put("ng_msg", ng_msg);
            mongoTemplate.insert(mapBigDataRow, dxQualityTable);
        } catch (Exception ex) {
            throw ex;
        }
    }

    //插入上线电芯数据到DB
    private void MesGxDxOnlineInsert(String dx_barcode, int dx_gear, String dx_ori_batch, double dx_ori_capacity, double dx_ori_ocv4_pressure, double dx_ori_ocr4_air, double dx_ori_k_value, double dx_ori_dcir, double dx_ori_thickness, String dx_ori_ocv4_time, int dx_ng_code, String dx_ng_msg, String dx_ori_technology) throws Exception {
        String dxOnlineTable = "c_mes_me_dx_online";
        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
        String dx_id = CFuncUtilsSystem.CreateUUID(true);
        Map<String, Object> mapBigDataRow = new HashMap<>();
        mapBigDataRow.put("item_date", item_date);
        mapBigDataRow.put("item_date_val", item_date_val);
        mapBigDataRow.put("dx_id", dx_id);
        mapBigDataRow.put("dx_barcode", dx_barcode);
        mapBigDataRow.put("dx_gear", dx_gear);
        mapBigDataRow.put("dx_ori_batch", dx_ori_batch);
        mapBigDataRow.put("dx_ori_capacity", dx_ori_capacity);
        mapBigDataRow.put("dx_ori_ocv4_pressure", dx_ori_ocv4_pressure);
        mapBigDataRow.put("dx_ori_ocr4_air", dx_ori_ocr4_air);
        mapBigDataRow.put("dx_ori_k_value", dx_ori_k_value);
        mapBigDataRow.put("dx_ori_dcir", dx_ori_dcir);
        mapBigDataRow.put("dx_ori_thickness", dx_ori_thickness);
        mapBigDataRow.put("dx_ori_ocv4_time", dx_ori_ocv4_time);
        mapBigDataRow.put("dx_ng_code", dx_ng_code);
        mapBigDataRow.put("dx_ng_msg", dx_ng_msg);
        String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
        if ("JZGX".equals(projectCode)) {
            mapBigDataRow.put("dx_ori_technology", dx_ori_technology);
        }
        mongoTemplate.insert(mapBigDataRow, dxOnlineTable);
    }

    //电芯上线信息查询
    @RequestMapping(value = "/MesGxDxOnlineSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxDxOnlineSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxDxOnlineSelect";
        String selectResult = "";
        String errorMsg = "";
        String dxOnlineTable = "c_mes_me_dx_online";
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Query queryBigData = new Query();
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(dxOnlineTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1000).iterator();
            List<Map<String, Object>> itemList = new ArrayList<>();
            int item_index = 1;
            while (iteratorBigData.hasNext()) {
                Map<String, Object> mapItem = new HashMap<>();
                Document docItemBigData = iteratorBigData.next();
                String item_date = df.format(docItemBigData.getDate("item_date"));
                mapItem.put("item_index", item_index);
                mapItem.put("item_date", item_date);
                mapItem.put("dx_barcode", docItemBigData.getString("dx_barcode"));
                mapItem.put("dx_gear", docItemBigData.getInteger("dx_gear"));
                mapItem.put("dx_ori_batch", docItemBigData.getString("dx_ori_batch"));
                mapItem.put("dx_ori_capacity", docItemBigData.getDouble("dx_ori_capacity"));
                mapItem.put("dx_ori_ocv4_pressure", docItemBigData.getDouble("dx_ori_ocv4_pressure"));
                mapItem.put("dx_ori_ocr4_air", docItemBigData.getDouble("dx_ori_ocr4_air"));
                mapItem.put("dx_ori_k_value", docItemBigData.getDouble("dx_ori_k_value"));
                mapItem.put("dx_ori_dcir", docItemBigData.getDouble("dx_ori_dcir"));
                mapItem.put("dx_ori_thickness", docItemBigData.getDouble("dx_ori_thickness"));
                mapItem.put("dx_ori_ocv4_time", docItemBigData.getString("dx_ori_ocv4_time"));
                mapItem.put("dx_ng_code", docItemBigData.getInteger("dx_ng_code"));
                mapItem.put("dx_ng_msg", docItemBigData.getString("dx_ng_msg"));
                itemList.add(mapItem);
                item_index++;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "电芯上线明细查询发生未知异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //电芯上线校验 （唐山/金寨国轩，单个电芯检测）
    @RequestMapping(value = "/MesGxDxOnlineCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxDxOnlineCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxDxOnlineCheck";
        String tranResult = "";
        String errorMsg = "";
        String dx_result = "";
        try {
            String prod_line_id = jsonParas.getString("prod_line_id");
            String station_code = jsonParas.getString("station_code");
            String dx_barcode_list = jsonParas.getString("dx_barcode_list");
            String hasOnline = jsonParas.getString("hasOnline");
            //1.查询当前工位是否选择了订单
            String sqlStationMo = "select mo_id,make_order " + "from c_mes_aps_station_mo " + "where station_code='" + station_code + "'";
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, request, apiRoutePath);
            if (itemListStationMo == null || itemListStationMo.size() <= 0) {
                errorMsg = "请先选择订单";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String mo_id = itemListStationMo.get(0).get("mo_id").toString();
            String make_order = itemListStationMo.get(0).get("make_order").toString();
            String sqlMoRecipe = "select c.recipe_id,d.mz_id,a.small_model_type  " + "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " + "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " + "on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_mz d " + "on c.recipe_id=d.recipe_id " + "where a.enable_flag='Y' and c.enable_flag='Y' and d.enable_flag='Y' " + "and c.recipe_type='MODELDX' and a.mo_id=" + mo_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe, false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}不可用或者未配置模组电芯配方";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String mz_id = itemListMoRecipe.get(0).get("mz_id").toString();
            String small_model_type = itemListMoRecipe.get(0).get("small_model_type").toString();
            //1.根据模组配方ID查找LIMIT项目
            String sqlMzLimitRecipe = "select a.mz_limit_code,a.down_limit,a.upper_limit,a.ng_rack_code," + "COALESCE(b.mz_limit_id,0) mz_limit_id," + "COALESCE(b.down_limit,0) down_limit2," + "COALESCE(b.upper_limit,0) upper_limit2," + "COALESCE(b.ng_rack_code,0) ng_rack_code2 " + "from c_mes_fmod_recipe_mz_limit_must a left join c_mes_fmod_recipe_mz_limit b " + "on a.mz_limit_code=b.mz_limit_code and b.mz_id=" + mz_id + " and b.enable_flag='Y' " + "order by a.mz_limit_index";
            List<Map<String, Object>> itemListMzLimitRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzLimitRecipe, false, request, apiRoutePath);
            //2.根据模组配方ID查找条码约束项目
            String sqlMzNgBarRecipe = "select mz_dxbar_ng_id,dxbar_ng_name,start_index,end_index," + "value_list,ng_way,ng_rack_num " + "from c_mes_fmod_recipe_mz_dxbar_ng " + "where mz_id=" + mz_id + " and enable_flag='Y'";
            List<Map<String, Object>> itemListMzNgBarRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzNgBarRecipe, false, request, apiRoutePath);
            String[] dx_barcode_array = dx_barcode_list.split(",");
            if (dx_barcode_array != null && dx_barcode_array.length > 0) {
                for (String dx_barcode : dx_barcode_array) {
                    if (hasOnline.equals("N")) {
                        //added by chenru 2024-05-18
                        //查询是否从工厂MES获取电芯原始数据，模式不需要，唐山/金寨国轩需要的话，配置1
                        String getDxOriginalDataFromMes = cFuncDbSqlResolve.GetParameterValue("GetDxOriginalDataFromMes");
                        if (getDxOriginalDataFromMes.equals("1")) {
                            JSONObject jbParas = new JSONObject();
                            jbParas.put("prod_line_id", prod_line_id);
                            jbParas.put("dx_barcode", dx_barcode);
                            String result = mesCoreInterfOriginalDxController.MesInterfCoreOriginalDxSelect(jbParas, request);
                            JSONObject jbResult = JSONObject.parseObject(result);
                            if (jbResult == null) {
                                errorMsg = "根据当前电芯码{" + dx_barcode + "},向上游系统查询电芯基础数据失败";
                                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                return tranResult;
                            }
                            String backCode = jbResult.getString("code");
                            String backMsg = jbResult.getString("errorMsg");
                            String dx_data = jbResult.getString("data");
                            if (!backCode.equals("200")) {
                                errorMsg = "根据当前电芯码{" + dx_barcode + "},向上游系统查询电芯基础数据失败:" + backMsg;
                                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                return tranResult;
                            } else {
                                jbParas = new JSONObject();
                                jbParas.put("dx_barcode", dx_barcode);
                                jbParas.put("dx_data", dx_data);

                                result = mesCoreInterfOriginalDxController.MesCoreOriginalDxSave(jbParas, request);
                                jbResult = JSONObject.parseObject(result);
                                int code = jbResult.getInteger("code");
                                backMsg = jbResult.getString("msg");
                                if (code != 0) {
                                    errorMsg = "保存当前电芯码{" + dx_barcode + "}基础数据失败:" + backMsg;
                                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                    return tranResult;
                                }
                            }
                        }
                    }
                    int dx_result_i = MesGxDxOnlineCheck(station_code, small_model_type, dx_barcode, make_order, mz_id, itemListMzLimitRecipe, itemListMzNgBarRecipe);
                    if (dx_result.equals("")) {
                        dx_result = String.valueOf(dx_result_i);
                    } else {
                        dx_result = dx_result + "," + String.valueOf(dx_result_i);
                    }
                }
            }
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, dx_result, "", 0);
        } catch (Exception ex) {
            errorMsg = "电芯上线校验发生未知异常" + ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //电芯上线校验 （唐山/金寨国轩，单个电芯检测）
    @RequestMapping(value = "/MesGxDxOnlineCheck2", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxDxOnlineCheck2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxDxOnlineCheck";
        String tranResult = "";
        String errorMsg = "";
        String dx_result = "";
        try {
            String prod_line_id = cFuncDbSqlResolve.GetParameterValue("MesTenantID");
            String station_code = jsonParas.getString("station_code");
            String dx_barcode_list = jsonParas.getString("dx_barcode_list");
            String hasOnline = jsonParas.getString("hasOnline");
            //1.查询当前工位是否选择了订单
            String sqlStationMo = "select mo_id,make_order " + "from c_mes_aps_station_mo " + "where station_code='" + station_code + "'";
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, request, apiRoutePath);
            if (itemListStationMo == null || itemListStationMo.size() <= 0) {
                errorMsg = "请先选择订单";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String mo_id = itemListStationMo.get(0).get("mo_id").toString();
            String make_order = itemListStationMo.get(0).get("make_order").toString();
            String sqlMoRecipe = "select c.recipe_id,d.mz_id,a.small_model_type  " + "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " + "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " + "on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_mz d " + "on c.recipe_id=d.recipe_id " + "where a.enable_flag='Y' and c.enable_flag='Y' and d.enable_flag='Y' " + "and c.recipe_type='MODELDX' and a.mo_id=" + mo_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe, false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "当前选择订单{" + make_order + "}不可用或者未配置模组电芯配方";
                tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String mz_id = itemListMoRecipe.get(0).get("mz_id").toString();
            String small_model_type = itemListMoRecipe.get(0).get("small_model_type").toString();
            //1.根据模组配方ID查找LIMIT项目
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");

            String sqlMzLimitRecipe = "select a.mz_limit_code,a.down_limit,a.upper_limit,a.ng_rack_code," + "COALESCE(b.mz_limit_id,0) mz_limit_id," + "COALESCE(b.down_limit,0) down_limit2," + "COALESCE(b.upper_limit,0) upper_limit2," + "COALESCE(b.ng_rack_code,0) ng_rack_code2 " + "from c_mes_fmod_recipe_mz_limit_must a left join c_mes_fmod_recipe_mz_limit b " + "on a.mz_limit_code=b.mz_limit_code and b.mz_id=" + mz_id + " and b.enable_flag='Y' " + "order by a.mz_limit_index";
            if ("JZGX".equals(projectCode)) {
                sqlMzLimitRecipe = "select a.mz_limit_code,a.down_limit,a.upper_limit,a.ng_rack_code," +
                        "COALESCE(b.mz_limit_id,0) mz_limit_id," + "COALESCE(b.down_limit,0) down_limit2," +
                        "COALESCE(b.upper_limit,0) upper_limit2," + "COALESCE(b.ng_rack_code,0) ng_rack_code2,COALESCE(b.rfr012,'') rfr012 " +
                        "from c_mes_fmod_recipe_mz_limit_must a join c_mes_fmod_recipe_mz_limit b " +
                        "on a.mz_limit_code=b.mz_limit_code and b.mz_id=" + mz_id + " and b.enable_flag='Y' " +
                        "order by a.mz_limit_index";
            }
            List<Map<String, Object>> itemListMzLimitRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzLimitRecipe, false, request, apiRoutePath);
            //2.根据模组配方ID查找条码约束项目
            String sqlMzNgBarRecipe = "select mz_dxbar_ng_id,dxbar_ng_name,start_index,end_index," + "value_list,ng_way,ng_rack_num " + "from c_mes_fmod_recipe_mz_dxbar_ng " + "where mz_id=" + mz_id + " and enable_flag='Y'";
            List<Map<String, Object>> itemListMzNgBarRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMzNgBarRecipe, false, request, apiRoutePath);
            String[] dx_barcode_array = dx_barcode_list.split(",");
            if (dx_barcode_array != null && dx_barcode_array.length > 0) {
                for (String dx_barcode : dx_barcode_array) {
                    if (hasOnline.equals("N")) {
                        //added by chenru 2024-05-18
                        //查询是否从工厂MES获取电芯原始数据，模式不需要，唐山/金寨国轩需要的话，配置1
                        String getDxOriginalDataFromMes = cFuncDbSqlResolve.GetParameterValue("GetDxOriginalDataFromMes");
                        if (getDxOriginalDataFromMes.equals("1")) {
                            JSONObject jbParas = new JSONObject();
                            jbParas.put("prod_line_id", prod_line_id);
                            jbParas.put("dx_barcode", dx_barcode);
                            String result = mesCoreInterfOriginalDxController.MesInterfCoreOriginalDxSelect(jbParas, request);
//                            String result = "{\"tracelD\":\"1839561336071843840\",\"code\":\"200\",\"errorMsg\":\"are no errors\",\"data\":{\"RFR012\":\"分容预测\",\"RFR005\":\"330\",\"RBJ015\":\"71.448\",\"RCOV4005\":\"0.8500\",\"RCOV4004\":\"0.1880\",\"RCOV4007\":\"2024-09-27 08:23:16\",\"RCOV4002\":\"3241.8072\"}}";
                            JSONObject jbResult = JSONObject.parseObject(result);
                            if (jbResult == null) {
                                errorMsg = "根据当前电芯码{" + dx_barcode + "},向上游系统查询电芯基础数据失败";
                                InsertDxQuality(dx_barcode, make_order, small_model_type, 0, "NULL", 0d, 0d, 0d, 0d, 0d, 0d, "NULL", 0d, 0d, 0d, 0d, "NG", 0, 10, errorMsg, station_code, 0, 0d);
                                dx_result += StringUtils.isEmpty(dx_result) ? "10" : ",10";
                                continue;
                            }
                            String backCode = jbResult.getString("code");
                            String backMsg = jbResult.getString("errorMsg");
                            String dx_data = jbResult.getString("data");
                            if (!backCode.equals("200")) {
                                errorMsg = "根据当前电芯码{" + dx_barcode + "},向上游系统查询电芯基础数据失败:" + backMsg;
                                InsertDxQuality(dx_barcode, make_order, small_model_type, 0, "NULL", 0d, 0d, 0d, 0d, 0d, 0d, "NULL", 0d, 0d, 0d, 0d, "NG", 0, 10, errorMsg, station_code, 0, 0d);
                                dx_result += StringUtils.isEmpty(dx_result) ? "10" : ",10";
                                continue;
                            } else {
                                jbParas = new JSONObject();
                                jbParas.put("dx_barcode", dx_barcode);
                                jbParas.put("dx_data", dx_data);

                                result = mesCoreInterfOriginalDxController.MesCoreOriginalDxSave(jbParas, request);
                                jbResult = JSONObject.parseObject(result);
                                int code = jbResult.getInteger("code");
                                backMsg = jbResult.getString("msg");
                                if (code != 0) {
                                    errorMsg = "保存当前电芯码{" + dx_barcode + "}基础数据失败:" + backMsg;
                                    tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                    return tranResult;
                                }
                            }
                        }
                    }
                    int dx_result_i = MesGxDxOnlineCheck2(station_code, small_model_type, dx_barcode, make_order, mz_id, itemListMzLimitRecipe, itemListMzNgBarRecipe);
                    if (dx_result.equals("")) {
                        dx_result = String.valueOf(dx_result_i);
                    } else {
                        dx_result = dx_result + "," + String.valueOf(dx_result_i);
                    }
                }
            }
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, dx_result, "", 0);
        } catch (Exception ex) {
            errorMsg = "电芯上线校验发生未知异常" + ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    private int MesGxDxOnlineCheck(String station_code, String small_model_type, String dx_barcode, String make_order, String mz_id, List<Map<String, Object>> itemListMzLimitRecipe, List<Map<String, Object>> itemListMzNgBarRecipe) {
        String dxOnlineTable = "c_mes_me_dx_online";
        String dxOriginalTable = "c_mes_me_dx_original";
        String mzDxRelTable = "c_mes_me_mz_dx_rel";
        int dx_ng_code = 0;
        String dx_ng_msg = "";
        int dx_result = 1;//1：合格，2：扫码NG，3：电压NG，4：内阻NG，5：K值NG，6：挡位NG，7：批次NG，8：电容NG,9：重码，10：缺少原始数据，11：DCRI范围NG，12：厚度NG，13：条码规则NG，14:其他
        try {
            //是否是测试阶段，测试阶段不需要判定，直接给合格
            String testModeFlag = cFuncDbSqlResolve.GetParameterValue("TestModeFlag");
            //1.先查询之前是否做了扫描动作,若做了扫描动作,则获取之前存在的档位
            int dx_gear_exist = 0;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_ng_code").is(0));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(dxOnlineTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                dx_gear_exist = docItemBigData.getInteger("dx_gear");
                break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.判断当前扫描电芯是否在电芯原始表
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").is(dx_barcode));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(dxOriginalTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            int dx_gear = 0;
            String dx_ori_batch = "";//批次号
            double dx_ori_capacity = 0f;//容量
            double dx_ori_ocv4_pressure = 0f;//电压
            double dx_ori_ocr4_air = 0f;//内阻
            double dx_ori_k_value = 0f;//K值
            double dx_ori_dcir = 0f;//Dcir内阻
            double dx_ori_thickness = 0f;//厚度
            double dx_diff_pressure = 0d;
            String dx_ori_ocv4_time = "";//ocv4时间
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                dx_gear = docItemBigData.getInteger("dx_gear");
                dx_ori_batch = docItemBigData.getString("dx_ori_batch");
                dx_ori_capacity = docItemBigData.getDouble("dx_ori_capacity");
                dx_ori_ocv4_pressure = docItemBigData.getDouble("dx_ori_ocv4_pressure");
                dx_ori_ocr4_air = docItemBigData.getDouble("dx_ori_ocr4_air");
                dx_ori_k_value = docItemBigData.getDouble("dx_ori_k_value");
                dx_ori_dcir = docItemBigData.getDouble("dx_ori_dcir");
                dx_ori_thickness = docItemBigData.getDouble("dx_ori_thickness");
                dx_ori_ocv4_time = docItemBigData.getString("dx_ori_ocv4_time");
                break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            //查询电芯ocv质量数据，并进行ocv测试
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").is(dx_barcode));
            queryBigData.addCriteria(Criteria.where("dx_status").is("OK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection("c_mes_me_dx_quality").find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                dx_ng_code = -15;
                dx_ng_msg = "未能根据电芯码{" + dx_barcode + "},查找到电芯原始数据,请先导入电芯数据";
                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);
                //插入质量数据
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                dx_result = 10;
                return dx_result;
            }
            Document next = iteratorBigData.next();
            //TODO
            Double dx_pressure_kl_value = next.getDouble("dx_pressure_kl_value");
            Double dx_pressure_fd_value = next.getDouble("dx_pressure_fd_value");
            Double dx_dianzu_value = next.getDouble("dx_dianzu_value");
            Double dx_k2_value = next.getDouble("dx_k2_value");
            if (testModeFlag.equals("Y")) {
                //执行插入,代表验证成功
                dx_ng_code = 0;
                dx_ng_msg = "";
                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                //插入质量数据
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "OK", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                dx_result = 1;
                return dx_result;
            }
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
            if ((!"TSGX".equals(projectCode)) && dx_gear < 0) {
                dx_ng_code = -1;
                dx_ng_msg = "未能根据电芯码{" + dx_barcode + "},查找到电芯原始数据,请先导入电芯数据";
                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                //插入质量数据
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                dx_result = 10;
                return dx_result;
            }
            //电芯档位NG槽位设定(必须)
            if (dx_gear_exist > 0) {
                if (dx_gear_exist != dx_gear) {
                    dx_ng_code = -2;
                    dx_ng_msg = "当前电芯条码{" + dx_barcode + "},对应的档位为{" + dx_gear + "},与当前料框对应的档位{" + dx_gear_exist + "}不符";
                    MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                    //插入质量数据
                    InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                    dx_result = 6;
                    return dx_result;
                }
            } else {//判断当前电芯档位是否在配方中
                String sqlGearCount = "select count(1) from c_mes_fmod_recipe_mz_dx " + "where mz_id=" + mz_id + " and dx_gear=" + dx_gear + "";
                Integer gearCount = cFuncDbSqlResolve.GetSelectCount(sqlGearCount);
                if (gearCount <= 0) {
                    dx_ng_code = -3;
                    dx_ng_msg = "当前电芯条码{" + dx_barcode + "},对应的档位为{" + dx_gear + "},不符合当前订单{" + make_order + "}对应的档位要求";
                    MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                    //插入质量数据
                    InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                    dx_result = 6;
                    return dx_result;
                }
            }
            //判断是否重码(是否已经做了模组与电芯的绑定)
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").is(dx_barcode));
            queryBigData.addCriteria(Criteria.where("mz_status").is("OK"));
            iteratorBigData = mongoTemplate.getCollection(mzDxRelTable).find(queryBigData.getQueryObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                iteratorBigData.close();
                dx_ng_code = -4;
                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},已与模组绑定,属于重码,若需上线,需先进行解绑";
                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                //插入质量数据
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                dx_result = 9;
                return dx_result;
            }
            //☆☆☆依次对电芯逻辑进行判断
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    double down_limit = Double.parseDouble(map.get("down_limit").toString());
                    double upper_limit = Double.parseDouble(map.get("upper_limit").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    double down_limit2 = Double.parseDouble(map.get("down_limit2").toString());
                    double upper_limit2 = Double.parseDouble(map.get("upper_limit2").toString());
                    if (mz_limit_id > 0) {
                        down_limit = down_limit2;
                        upper_limit = upper_limit2;
                    }
                    //电芯原始高档位开路电压范围判定(偶数档位)
                    if (mz_limit_code.equals("dx_ori_high_kl_pressure") && mz_limit_id > 0) {
                        if (dx_gear % 2 == 0) {
                            if (dx_ori_ocv4_pressure < down_limit || dx_ori_ocv4_pressure > upper_limit) {
                                dx_ng_code = -5;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始OCV4高档位开路电压值{" + dx_ori_ocv4_pressure + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 3;
                                return dx_result;
                            }
                        }
                    }
                    //电芯原始低档位开路电压范围判定(奇数档位)mV
                    if (mz_limit_code.equals("dx_ori_low_kl_pressure") && mz_limit_id > 0) {
                        if (dx_gear % 2 != 0) {
                            if (dx_ori_ocv4_pressure < down_limit || dx_ori_ocv4_pressure > upper_limit) {
                                dx_ng_code = -6;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始OCV4低档位开路电压值{" + dx_ori_ocv4_pressure + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 3;
                                return dx_result;
                            }
                        }
                    }
                    //电芯原始内阻范围判定mΩ
                    if (mz_limit_code.equals("dx_ori_neizu") && mz_limit_id > 0) {
                        if (dx_ori_ocr4_air < down_limit || dx_ori_ocr4_air > upper_limit) {
                            dx_ng_code = -7;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始OCV4内阻值{" + dx_ori_ocr4_air + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                            //插入质量数据
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 4;
                            return dx_result;
                        }
                    }
                    //电芯原始K值范围判定
                    if (mz_limit_code.equals("dx_ori_kvalue") && mz_limit_id > 0) {
                        if (dx_ori_k_value < down_limit || dx_ori_k_value > upper_limit) {
                            dx_ng_code = -8;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始K值{" + dx_ori_k_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                            //插入质量数据
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 5;
                            return dx_result;
                        }
                    }
                    //电芯原始高档位容量范围判定(偶数档位)
                    if (mz_limit_code.equals("dx_ori_high_container") && mz_limit_id > 0) {
                        if (dx_gear % 2 == 0) {
                            if (dx_ori_capacity < down_limit || dx_ori_capacity > upper_limit) {
                                dx_ng_code = -9;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始高档位电容值{" + dx_ori_capacity + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 6;
                                return dx_result;
                            }
                        }
                    }
                    //电芯原始低档位容量范围判定(奇数档位)
                    if (mz_limit_code.equals("dx_ori_low_container") && mz_limit_id > 0) {
                        if (dx_gear % 2 != 0) {
                            if (dx_ori_capacity < down_limit || dx_ori_capacity > upper_limit) {
                                dx_ng_code = -9;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始低档位电容值{" + dx_ori_capacity + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 6;
                                return dx_result;
                            }
                        }
                    }
                    //电芯原始DCRI范围判定
                    if (mz_limit_code.equals("dx_ori_dcir") && mz_limit_id > 0) {
                        if (dx_ori_dcir < down_limit || dx_ori_dcir > upper_limit) {
                            dx_ng_code = -10;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始DCIR值{" + dx_ori_dcir + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                            //插入质量数据
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 11;
                            return dx_result;
                        }
                    }
                    //电芯原始厚度范围判定
                    if (mz_limit_code.equals("dx_ori_thickness") && mz_limit_id > 0) {
                        if (dx_ori_thickness < down_limit || dx_ori_thickness > upper_limit) {
                            dx_ng_code = -11;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始厚度值{" + dx_ori_thickness + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                            //插入质量数据
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 12;
                            return dx_result;
                        }
                    }
                    //电芯测试结果开路电压范围判定mV
                    if (mz_limit_code.equals("dx_test_kl_pressure") && mz_limit_id > 0) {
                        if (dx_pressure_kl_value < down_limit || dx_pressure_kl_value > upper_limit) {
                            dx_ng_code = -15;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},OCV测试开路电压值{" + dx_pressure_kl_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 16;
                            return dx_result;
                        }
                    }
                    //电芯测试结果电阻范围判定mΩ
                    if (mz_limit_code.equals("dx_test_fl_pressure") && mz_limit_id > 0) {
                        if (dx_pressure_fd_value < down_limit || dx_pressure_fd_value > upper_limit) {
                            dx_ng_code = -15;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},OCV测试负路电压值{" + dx_pressure_fd_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 16;
                            return dx_result;
                        }
                    }
                    //电芯测试结果电阻范围判定mΩ
                    if (mz_limit_code.equals("dx_test_dianzu") && mz_limit_id > 0) {
                        if (dx_dianzu_value < down_limit || dx_dianzu_value > upper_limit) {
                            dx_ng_code = -14;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},OCV测试电阻值{" + dx_dianzu_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 16;
                            return dx_result;
                        }
                    }
                }
            }
            //☆☆☆依次对条码规则进行判断
            if (itemListMzNgBarRecipe != null && itemListMzNgBarRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzNgBarRecipe) {
                    String dxbar_ng_name = map.get("dxbar_ng_name").toString();
                    int start_index = Integer.parseInt(map.get("start_index").toString());
                    int end_index = Integer.parseInt(map.get("end_index").toString());
                    String value_list = map.get("value_list").toString();
                    String ng_way = map.get("ng_way").toString();//NG_IN、NG_NOTIN
                    String subStr = dx_barcode.substring(start_index, end_index);
                    String[] valueList = value_list.split(",", -1);
                    if (ng_way.equals("NG_IN")) {
                        if (valueList != null && valueList.length > 0) {
                            for (int i = 0; i < valueList.length; i++) {
                                String itemValue = valueList[i];
                                if (itemValue.equals(subStr)) {
                                    dx_ng_code = -12;
                                    dx_ng_msg = "当前电芯条码{" + dx_barcode + "},判断{" + dxbar_ng_name + "},截取值{" + subStr + "},在NG集合中{" + value_list + "}";
                                    MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                                    //插入质量数据
                                    InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                    dx_result = 13;
                                    return dx_result;
                                }
                            }
                        }
                    } else {
                        if (valueList != null && valueList.length > 0) {
                            Boolean isIn = false;
                            for (int i = 0; i < valueList.length; i++) {
                                String itemValue = valueList[i];
                                if (itemValue.equals(subStr)) {
                                    isIn = true;
                                    break;
                                }
                            }
                            if (!isIn) {
                                dx_ng_code = -13;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},判断{" + dxbar_ng_name + "},截取值{" + subStr + "},不在集合中{" + value_list + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 13;
                                return dx_result;
                            }
                        }
                    }
                }
            }
            if (dx_ng_code == 0) {
                //执行插入,代表验证成功
                dx_ng_code = 0;
                dx_ng_msg = "";
                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, null);

                //插入质量数据
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_pressure_kl_value, dx_pressure_fd_value, dx_dianzu_value, dx_k2_value, "OK", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                dx_result = 1;
                return dx_result;
            } else {
                dx_result = 14;
                return dx_result;
            }
        } catch (Exception ex) {
            dx_result = 14;
            return dx_result;
        }
    }

    private int MesGxDxOnlineCheck2(String station_code, String small_model_type, String dx_barcode, String make_order, String mz_id, List<Map<String, Object>> itemListMzLimitRecipe, List<Map<String, Object>> itemListMzNgBarRecipe) {
        String dxOnlineTable = "c_mes_me_dx_online";
        String dxOriginalTable = "c_mes_me_dx_original";
        String mzDxRelTable = "c_mes_me_mz_dx_rel";
        int dx_ng_code = 0;
        String dx_ng_msg = "";
        int dx_result = 1;//1：合格，2：扫码NG，3：电压NG，4：内阻NG，5：K值NG，6：挡位NG，7：批次NG，8：电容NG,9：重码，10：缺少原始数据，11：DCRI范围NG，12：厚度NG，13：条码规则NG，14:其他
        try {
            //是否是测试阶段，测试阶段不需要判定，直接给合格
            String testModeFlag = cFuncDbSqlResolve.GetParameterValue("TestModeFlag");
            //1.先查询之前是否做了扫描动作,若做了扫描动作,则获取之前存在的档位
            int dx_gear_exist = -1;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_ng_code").is(0));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(dxOnlineTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                dx_gear_exist = docItemBigData.getInteger("dx_gear");
                break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            //2.判断当前扫描电芯是否在电芯原始表
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").is(dx_barcode));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(dxOriginalTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            int dx_gear = -1;
            String dx_ori_batch = "";//批次号
            double dx_ori_capacity = 0d;//容量
            double dx_ori_ocv4_pressure = 0d;//电压
            double dx_ori_ocr4_air = 0d;//内阻
            double dx_ori_k_value = 0d;//K值
            double dx_ori_dcir = 0d;//Dcir内阻
            double dx_ori_thickness = 0d;//厚度
            double dx_diff_pressure = 0d;
            String dx_ori_ocv4_time = "";//ocv4时间
            String dx_ori_technology = ""; //分容预测工艺
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                dx_gear = (docItemBigData.get("dx_gear") instanceof Double) ? docItemBigData.getDouble("dx_gear").intValue() : docItemBigData.getInteger("dx_gear");
                dx_ori_batch = docItemBigData.getString("dx_ori_batch");
                dx_ori_capacity = Double.parseDouble(docItemBigData.get("dx_ori_capacity").toString());
                dx_ori_ocv4_pressure = Double.parseDouble(docItemBigData.get("dx_ori_ocv4_pressure").toString());
                dx_ori_ocr4_air = Double.parseDouble(docItemBigData.get("dx_ori_ocr4_air").toString());
                dx_ori_k_value = Double.parseDouble(docItemBigData.get("dx_ori_k_value").toString());
                dx_ori_dcir = Double.parseDouble(docItemBigData.get("dx_ori_dcir").toString());
                dx_ori_thickness = Double.parseDouble(docItemBigData.get("dx_ori_thickness").toString());
                dx_ori_ocv4_time = docItemBigData.getString("dx_ori_ocv4_time");
                dx_ori_technology = docItemBigData.getString("dx_ori_technology");
                break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (testModeFlag.equals("Y")) {
                //执行插入,代表验证成功
                dx_ng_code = 0;
                dx_ng_msg = "";
                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                //插入质量数据
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "OK", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                dx_result = 1;
                return dx_result;
            }
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");

            if ((!"TSGX".equals(projectCode)) && dx_gear < 0) {
                dx_ng_code = -1;
                dx_ng_msg = "未能根据电芯码{" + dx_barcode + "},查找到电芯原始数据,请先导入电芯数据";
                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                //插入质量数据
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                dx_result = 10;
                return dx_result;
            }
            //电芯档位NG槽位设定(必须)
            if (dx_gear_exist > 0) {
                if (dx_gear_exist != dx_gear) {
                    dx_ng_code = -2;
                    dx_ng_msg = "当前电芯条码{" + dx_barcode + "},对应的档位为{" + dx_gear + "},与当前料框对应的档位{" + dx_gear_exist + "}不符";
                    MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                    //插入质量数据
                    InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                    dx_result = 6;
                    return dx_result;
                }
            } else {//判断当前电芯档位是否在配方中
                String sqlGearCount = "select count(1) from c_mes_fmod_recipe_mz_dx " + "where mz_id=" + mz_id + " and dx_gear=" + dx_gear + "";
                Integer gearCount = cFuncDbSqlResolve.GetSelectCount(sqlGearCount);
                if (gearCount <= 0) {
                    dx_ng_code = -3;
                    dx_ng_msg = "当前电芯条码{" + dx_barcode + "},对应的档位为{" + dx_gear + "},不符合当前订单{" + make_order + "}对应的档位要求";
                    MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                    //插入质量数据
                    InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                    dx_result = 6;
                    return dx_result;
                }
            }
            //判断是否重码(是否已经做了模组与电芯的绑定)
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dx_barcode").is(dx_barcode));
            queryBigData.addCriteria(Criteria.where("mz_status").is("OK"));
            iteratorBigData = mongoTemplate.getCollection(mzDxRelTable).find(queryBigData.getQueryObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                iteratorBigData.close();
                dx_ng_code = -4;
                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},已与模组绑定,属于重码,若需上线,需先进行解绑";
                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                //插入质量数据
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                dx_result = 9;
                return dx_result;
            }
            //☆☆☆依次对电芯逻辑进行判断
            if (itemListMzLimitRecipe != null && itemListMzLimitRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzLimitRecipe) {
                    String mz_limit_code = map.get("mz_limit_code").toString();
                    String RFR012 = map.get("rfr012") == null ? "" : map.get("rfr012").toString();
                    double down_limit = Double.parseDouble(map.get("down_limit").toString());
                    double upper_limit = Double.parseDouble(map.get("upper_limit").toString());
                    int mz_limit_id = Integer.parseInt(map.get("mz_limit_id").toString());
                    double down_limit2 = Double.parseDouble(map.get("down_limit2").toString());
                    double upper_limit2 = Double.parseDouble(map.get("upper_limit2").toString());
                    if (mz_limit_id > 0) {
                        down_limit = down_limit2;
                        upper_limit = upper_limit2;
                    }
                    //电芯原始高档位开路电压范围判定(偶数档位)
                    if (mz_limit_code.equals("dx_ori_high_kl_pressure") && mz_limit_id > 0) {
                        if (dx_gear % 2 == 0) {
                            if (dx_ori_ocv4_pressure < down_limit || dx_ori_ocv4_pressure > upper_limit) {
                                dx_ng_code = -5;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始OCV4高档位开路电压值{" + dx_ori_ocv4_pressure + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 3;
                                return dx_result;
                            }
                        }
                    }
                    //电芯原始低档位开路电压范围判定(奇数档位)mV
                    if (mz_limit_code.equals("dx_ori_low_kl_pressure") && mz_limit_id > 0) {
                        if (dx_gear % 2 != 0) {
                            if (dx_ori_ocv4_pressure < down_limit || dx_ori_ocv4_pressure > upper_limit) {
                                dx_ng_code = -6;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始OCV4低档位开路电压值{" + dx_ori_ocv4_pressure + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 3;
                                return dx_result;
                            }
                        }
                    }
                    //电芯原始内阻范围判定mΩ
                    if (mz_limit_code.equals("dx_ori_neizu") && mz_limit_id > 0) {
                        if (dx_ori_ocr4_air < down_limit || dx_ori_ocr4_air > upper_limit) {
                            dx_ng_code = -7;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始OCV4内阻值{" + dx_ori_ocr4_air + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                            //插入质量数据
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 4;
                            return dx_result;
                        }
                    }
                    //电芯原始K值范围判定
                    if (mz_limit_code.equals("dx_ori_kvalue") && mz_limit_id > 0) {
                        if (dx_ori_k_value < down_limit || dx_ori_k_value > upper_limit) {
                            dx_ng_code = -8;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始K值{" + dx_ori_k_value + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                            //插入质量数据
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 5;
                            return dx_result;
                        }
                    }
                    //电芯原始高档位容量范围判定(偶数档位)
                    if (mz_limit_code.equals("dx_ori_high_container") && mz_limit_id > 0) {
                        if (dx_gear % 2 == 0) {
                            if (dx_ori_capacity < down_limit || dx_ori_capacity > upper_limit) {
                                dx_ng_code = -9;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始高档位电容值{" + dx_ori_capacity + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 6;
                                return dx_result;
                            }
                        }
                    }
                    //检验 分容预测工艺是否正确 金寨国轩特有
                    if("JZGX".equals(projectCode) && mz_limit_id > 0){
                        //判断是否为空 并且去掉两端空格，并且不为空字符串
                        if(RFR012!= null && !RFR012.trim().isEmpty()){
                            //分割成数组
                            String[] technologies = RFR012.split(",");
                            //判断数组中是否包含他们给的工艺，包含exactMatch为true，不包含false
                            boolean exactMatch = Arrays.asList(technologies).contains(dx_ori_technology);
                            if(!exactMatch){
                                dx_ng_code = -9;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},当前工艺{" + dx_ori_technology + "},不在模组规则中工艺{"+ RFR012+ "}范围内";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 6;
                                return dx_result;
                            }
                        }
                    }
                    //电芯原始低档位容量范围判定(奇数档位)
                    if (mz_limit_code.equals("dx_ori_low_container") && mz_limit_id > 0) {
                        if (dx_gear % 2 != 0) {
                            if (dx_ori_capacity < down_limit || dx_ori_capacity > upper_limit) {
                                dx_ng_code = -9;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始低档位电容值{" + dx_ori_capacity + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 6;
                                return dx_result;
                            }
                        }
                    }
                    //电芯原始DCRI范围判定
                    if (mz_limit_code.equals("dx_ori_dcir") && mz_limit_id > 0) {
                        if (dx_ori_dcir < down_limit || dx_ori_dcir > upper_limit) {
                            dx_ng_code = -10;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始DCIR值{" + dx_ori_dcir + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                            //插入质量数据
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 11;
                            return dx_result;
                        }
                    }
                    //电芯原始厚度范围判定
                    if (mz_limit_code.equals("dx_ori_thickness") && mz_limit_id > 0) {
                        if (dx_ori_thickness < down_limit || dx_ori_thickness > upper_limit) {
                            dx_ng_code = -11;
                            dx_ng_msg = "当前电芯条码{" + dx_barcode + "},原始厚度值{" + dx_ori_thickness + "},不在合格范围{" + down_limit + "-" + upper_limit + "}";
                            MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                            //插入质量数据
                            InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                            dx_result = 12;
                            return dx_result;
                        }
                    }
                }
            }
            //☆☆☆依次对条码规则进行判断
            if (itemListMzNgBarRecipe != null && itemListMzNgBarRecipe.size() > 0) {
                for (Map<String, Object> map : itemListMzNgBarRecipe) {
                    String dxbar_ng_name = map.get("dxbar_ng_name").toString();
                    int start_index = Integer.parseInt(map.get("start_index").toString());
                    int end_index = Integer.parseInt(map.get("end_index").toString());
                    String value_list = map.get("value_list").toString();
                    String ng_way = map.get("ng_way").toString();//NG_IN、NG_NOTIN
                    String subStr = dx_barcode.substring(start_index, end_index);
                    String[] valueList = value_list.split(",", -1);
                    if (ng_way.equals("NG_IN")) {
                        if (valueList != null && valueList.length > 0) {
                            for (int i = 0; i < valueList.length; i++) {
                                String itemValue = valueList[i];
                                if (itemValue.equals(subStr)) {
                                    dx_ng_code = -12;
                                    dx_ng_msg = "当前电芯条码{" + dx_barcode + "},判断{" + dxbar_ng_name + "},截取值{" + subStr + "},在NG集合中{" + value_list + "}";
                                    MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                                    //插入质量数据
                                    InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                    dx_result = 13;
                                    return dx_result;
                                }
                            }
                        }
                    } else {
                        if (valueList != null && valueList.length > 0) {
                            Boolean isIn = false;
                            for (int i = 0; i < valueList.length; i++) {
                                String itemValue = valueList[i];
                                if (itemValue.equals(subStr)) {
                                    isIn = true;
                                    break;
                                }
                            }
                            if (!isIn) {
                                dx_ng_code = -13;
                                dx_ng_msg = "当前电芯条码{" + dx_barcode + "},判断{" + dxbar_ng_name + "},截取值{" + subStr + "},不在集合中{" + value_list + "}";
                                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                                //插入质量数据
                                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "NG", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                                dx_result = 13;
                                return dx_result;
                            }
                        }
                    }
                }
            }
            if (dx_ng_code == 0) {
                //执行插入,代表验证成功
                dx_ng_code = 0;
                dx_ng_msg = "";
                MesGxDxOnlineInsert(dx_barcode, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, dx_ng_code, dx_ng_msg, dx_ori_technology);

                //插入质量数据
                InsertDxQuality(dx_barcode, make_order, small_model_type, dx_gear, dx_ori_batch, dx_ori_capacity, dx_ori_ocv4_pressure, dx_ori_ocr4_air, dx_ori_k_value, dx_ori_dcir, dx_ori_thickness, dx_ori_ocv4_time, 0, 0, 0, 0, "OK", 0, dx_ng_code, dx_ng_msg, station_code, 0, dx_diff_pressure);
                dx_result = 1;
                return dx_result;
            } else {
                log.error("error dx_ng_code :{}", dx_ng_code);
                dx_result = 14;
                return dx_result;
            }
        } catch (Exception ex) {
            log.error("error:{}", ex.getMessage());
            dx_result = 14;
            return dx_result;
        }
    }
}
