package com.api.eap.project.xinai;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 芯爱LDI事件接口(Sub)公共方法
 * 1.Panel2DCRead:基板2DC读取结果通知
 * 2.PanelTransferCommand:投入基板的2DC和投入番号的通知
 * 3.ForcedPanelLoading:基板强制投入的通知
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Service
@Slf4j
public class EapXinaiLdiSendSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.基板2DC读取结果通知
    public JSONObject Panel2DCRead(String panel_barcode, String panel_index){
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcCode="Panel2DCRead";
        String esbInterfCode="LdiPanel2DCRead";
        String token="";
        String requestParas = "";
        String responseParas = "";
        try{
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //发送信息
            JSONObject postParas = new JSONObject();
            //头
            JSONObject jbHeader=new JSONObject();
            jbHeader.put("MessageName",funcCode);
            jbHeader.put("TransactionID", CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmssSSSSSS"));
            //内容
            JSONObject jbBody=new JSONObject();
            jbBody.put("PanelID",panel_barcode);
            //结果
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("Code",1);
            jsonResult.put("Message","");
            jsonResult.put("Decision",1);
            //组合
            postParas.put("Header",jbHeader);
            postParas.put("Body",jbBody);
            postParas.put("Result",jsonResult);
            requestParas=postParas.toString();

            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //执行接口
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("Message");
            Integer decision=jbBackResult.getInteger("Decision");
            if(code!=1){
                errorMsg="调用接口返回失败："+msg;
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("decision", decision);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        }
        catch (Exception ex){
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.投入基板的2DC和投入番号的通知
    public JSONObject PanelTransferCommand(String panel_barcode, String panel_index){
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcCode="PanelTransferCommand";
        String esbInterfCode="LdiPanelTransferCommand";
        String token="";
        String requestParas = "";
        String responseParas = "";
        try{
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //发送信息
            JSONObject postParas = new JSONObject();
            //头
            JSONObject jbHeader=new JSONObject();
            jbHeader.put("MessageName",funcCode);
            jbHeader.put("TransactionID", CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmssSSSSSS"));
            //内容
            JSONObject jbBody=new JSONObject();
            jbBody.put("PanelID",panel_barcode);
            jbBody.put("CarryNo",Integer.parseInt(panel_index));
            //结果
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("Code",1);
            jsonResult.put("Message","");
            //组合
            postParas.put("Header",jbHeader);
            postParas.put("Body",jbBody);
            postParas.put("Result",jsonResult);
            requestParas=postParas.toString();

            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //执行接口
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("Message");
            if(code!=1){
                errorMsg="调用接口返回失败："+msg;
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        }
        catch (Exception ex){
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.基板强制投入的通知
    public JSONObject ForcedPanelLoading(String panel_barcode, String panel_index){
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcCode="ForcedPanelLoading";
        String esbInterfCode="LdiForcedPanelLoading";
        String token="";
        String requestParas = "";
        String responseParas = "";
        try{
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //发送信息
            JSONObject postParas = new JSONObject();
            //头
            JSONObject jbHeader=new JSONObject();
            jbHeader.put("MessageName",funcCode);
            jbHeader.put("TransactionID", CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmssSSSSSS"));
            //内容
            JSONObject jbBody=new JSONObject();
            jbBody.put("CarryNo",Integer.parseInt(panel_index));
            //结果
            JSONObject jsonResult=new JSONObject();
            jsonResult.put("Code",1);
            jsonResult.put("Message","");
            //组合
            postParas.put("Header",jbHeader);
            postParas.put("Body",jbBody);
            postParas.put("Result",jsonResult);
            requestParas=postParas.toString();

            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //执行接口
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("Message");
            if(code!=1){
                errorMsg="调用接口返回失败："+msg;
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        }
        catch (Exception ex){
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
