package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定颖EAP发送事件数据定义接口
 * 1.UserVerify:用户登入登出验证
 * 2.UserLoginRequest:人员登入登出事件
 * 3.EQPInfoVerify:检测与EAP通讯并且返回EAP时间由应用进行更新本地时间
 * 4.AlarmReport:上报报警信息到EAP
 * 5.RealStatusReport:设备状态上报到EAP
 * 6.StatusChangeReport:只要报警与状态发生任何变化都需要进行上报到EAP
 * 7.ParamVerify:查询工单信息
 * 8.UtilityReport:上报三率
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/dy/interf/send")
public class EapDySendEventController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDySendEventFunc eapDySendEventFunc;

    //3.检测与EAP通讯并且返回EAP时间由应用进行更新本地时间
    @RequestMapping(value = "/EQPInfoVerify", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyEQPInfoVerify(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/send/EQPInfoVerify";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_attr=jsonParas.getString("station_attr");
            if(station_code==null || station_code.equals("")){
                //1.查询工位信息
                String sqlStation="select station_id," +
                        "station_code,COALESCE(station_attr,'') station_attr " +
                        "from sys_fmod_station " +
                        "where station_attr='"+station_attr+"'";
                List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(userId,sqlStation,false,request,apiRoutePath);
                station_code=itemListStation.get(0).get("station_code").toString();
            }
            eapDySendEventFunc.EQPInfoVerify(station_code);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "上报报警信息到EAP异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.上报报警信息到EAP
    @RequestMapping(value = "/AlarmReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyAlarmReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/send/AlarmReport";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        try{
            String station_id=jsonParas.getString("station_id");
            String station_code=jsonParas.getString("station_code");
            String station_attr=jsonParas.getString("station_attr");
            String alarm_id=jsonParas.getString("alarm_id");
            String alarm_desc=jsonParas.getString("alarm_desc");
            String warn_flag=jsonParas.getString("warn_flag");
            String reset_flag=jsonParas.getString("reset_flag");
            String local_flag=jsonParas.getString("local_flag");
            String offline_flag=jsonParas.getString("offline_flag");
            String status_code=jsonParas.getString("status_code");
            if(station_id==null || station_id.equals("")){
                //1.查询工位信息
                String sqlStation="select station_id," +
                        "station_code,COALESCE(station_attr,'') station_attr " +
                        "from sys_fmod_station " +
                        "where station_attr='"+station_attr+"'";
                List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(userId,sqlStation,false,request,apiRoutePath);
                station_code=itemListStation.get(0).get("station_code").toString();
                station_id=itemListStation.get(0).get("station_id").toString();
            }

            eapDySendEventFunc.AlarmReport(Long.parseLong(station_id),station_code,reset_flag,warn_flag,
                    alarm_id,alarm_desc,local_flag,status_code,offline_flag);

            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "上报报警信息到EAP异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.设备状态上报到EAP
    @RequestMapping(value = "/RealStatusReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyRealStatusReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/send/RealStatusReport";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        try{
            String station_id=jsonParas.getString("station_id");
            String station_code=jsonParas.getString("station_code");
            String station_attr=jsonParas.getString("station_attr");
            String alarm_id=jsonParas.getString("alarm_id");
            String warn_flag=jsonParas.getString("warn_flag");
            String reset_flag=jsonParas.getString("reset_flag");
            String local_flag=jsonParas.getString("local_flag");
            String auto_flag=jsonParas.getString("auto_flag");
            String stop_flag=jsonParas.getString("stop_flag");
            String repair_flag=jsonParas.getString("repair_flag");
            String hold_flag=jsonParas.getString("hold_flag");
            String status_code=jsonParas.getString("status_code");//设备状态：Idle, Run, Stop, Initial and Down
            String short_bettery_flag=jsonParas.getString("short_bettery_flag");
            String power_on_time=jsonParas.getString("power_on_time");
            String idle_delay_time=jsonParas.getString("idle_delay_time");
            String current_lot_count=jsonParas.getString("current_lot_count");
            String history_lot_count=jsonParas.getString("history_lot_count");
            String red_light_flag=jsonParas.getString("red_light_flag");
            String yellow_light_flag=jsonParas.getString("yellow_light_flag");
            String green_light_flag=jsonParas.getString("green_light_flag");
            String blue_light_flag=jsonParas.getString("blue_light_flag");
            String user_name=jsonParas.getString("user_name");
            String lot_num=jsonParas.getString("lot_num");
            String eap_offline_flag=jsonParas.getString("eap_offline_flag");
            String plc_offline_flag=jsonParas.getString("plc_offline_flag");
            String prod_mode=jsonParas.getString("prod_mode");
            String alarm_desc=jsonParas.getString("alarm_desc");
            String offline_flag=jsonParas.getString("offline_flag");
            String prod_mix_mode=jsonParas.getString("prod_mix_mode");
            if(prod_mix_mode==null) prod_mix_mode="N/A";
            //工位信息
            if(station_id==null || station_id.equals("")){
                //1.查询工位信息
                String sqlStation="select station_id," +
                        "station_code,COALESCE(station_attr,'') station_attr " +
                        "from sys_fmod_station " +
                        "where station_attr='"+station_attr+"'";
                List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(userId,sqlStation,false,request,apiRoutePath);
                station_code=itemListStation.get(0).get("station_code").toString();
                station_id=itemListStation.get(0).get("station_id").toString();
            }

            eapDySendEventFunc.RealStatusReport(station_id,station_code,local_flag,auto_flag, stop_flag, repair_flag, hold_flag,
                    status_code, reset_flag, warn_flag, alarm_id, short_bettery_flag, power_on_time, idle_delay_time,
                    current_lot_count, history_lot_count, red_light_flag, yellow_light_flag, green_light_flag, blue_light_flag,
                    user_name, lot_num, eap_offline_flag, plc_offline_flag, prod_mode,alarm_desc,offline_flag,station_attr,prod_mix_mode);

            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "设备状态上报到EAP异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //6.只要报警与状态发生任何变化都需要进行上报到EAP
    @RequestMapping(value = "/StatusChangeReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyStatusChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/send/StatusChangeReport";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        try{
            String station_id=jsonParas.getString("station_id");
            String station_code=jsonParas.getString("station_code");
            String station_attr=jsonParas.getString("station_attr");
            String alarm_id=jsonParas.getString("alarm_id");
            String local_flag=jsonParas.getString("local_flag");
            String auto_flag=jsonParas.getString("auto_flag");
            String status_code=jsonParas.getString("status_code");//设备状态：Idle, Run, Stop, Initial and Down
            String alarm_desc=jsonParas.getString("alarm_desc");
            String offline_flag=jsonParas.getString("offline_flag");
            String alarm_level=jsonParas.getString("alarm_level");
            if(alarm_level==null) alarm_level="";
            //工位信息
            if(station_id==null || station_id.equals("")){
                //1.查询工位信息
                String sqlStation="select station_id," +
                        "station_code,COALESCE(station_attr,'') station_attr " +
                        "from sys_fmod_station " +
                        "where station_attr='"+station_attr+"'";
                List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(userId,sqlStation,false,request,apiRoutePath);
                station_code=itemListStation.get(0).get("station_code").toString();
                station_id=itemListStation.get(0).get("station_id").toString();
            }

            eapDySendEventFunc.StatusChangeReport(Long.parseLong(station_id), station_code,auto_flag, status_code,
                    alarm_id,local_flag,alarm_desc,alarm_level,offline_flag);

            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "设备状态与报警变化上报到EAP异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //CimMode模式变化后事件上报
    @RequestMapping(value = "/CimModeChangeReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCimModeChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/send/CimModeChangeReport";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        String meUserTable="a_eap_me_station_user";
        try{
            String station_id=jsonParas.getString("station_id");
            String station_code=jsonParas.getString("station_code");
            String station_attr=jsonParas.getString("station_attr");
            String auto_value=jsonParas.getString("auto_value");//1自动,0脱机
            String model_value=jsonParas.getString("model_value");//0本地,1远程
            String cim_model="";
            if(auto_value.equals("0")){
                cim_model="Local";
            }
            else{
                if(model_value.equals("0")) cim_model="Semi-Auto";
                else cim_model="Remote";
            }
            //查询当前用户信息
            String user_name="";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                user_name=docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            String cim_pwd= eapDySendEventFunc.CimModeChangeReport(Long.parseLong(station_id),station_code,cim_model,user_name,station_attr);
            if(cim_pwd!=null && !cim_pwd.equals("")){
                if(cim_model.equals("Semi-Auto") || cim_model.equals("Remote")){
                    String sqlUpdParas="update sys_parameter set " +
                            "parameter_val='"+cim_pwd+"' " +
                            "where parameter_code='Hmi_CheckPwd'";
                    cFuncDbSqlExecute.ExecUpdateSql(userId,sqlUpdParas,false,request,apiRoutePath);
                }
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "CimMode模式变化后事件上报异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //修改EAP的IP与端口号
    @RequestMapping(value = "/EapChangeIpAndPort", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyChangeIpAndPort(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dy/interf/send/EapChangeIpAndPort";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        try{
            String ip_port=jsonParas.getString("ip_port");
            String[] ipParas=ip_port.split(":",-1);
            if(ipParas==null || ipParas.length<2){
                errorMsg="参数错误";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            //查询
            String sqlInterfSel="select " +
                    "esb_interf_id,COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where data_to_sys='EAP'";
            List<Map<String,Object>> itemListInterf=cFuncDbSqlExecute.ExecSelectSql(userId,
                    sqlInterfSel,false,request,apiRoutePath);
            if(itemListInterf!=null && itemListInterf.size()>0){
                for(Map<String,Object> mapItem : itemListInterf){
                    String esb_interf_id=mapItem.get("esb_interf_id").toString();
                    String esb_prod_intef_url=mapItem.get("esb_prod_intef_url").toString();
                    if(esb_prod_intef_url!=null && !esb_prod_intef_url.equals("")){
                        String[] lstTemp=esb_prod_intef_url.split("/");
                        String firstPara=lstTemp[2];
                        String replacePara=ip_port;
                        esb_prod_intef_url=esb_prod_intef_url.replace(firstPara,replacePara);
                        String sqlUpd="update sys_core_esb_interf set " +
                                "esb_prod_intef_url='"+esb_prod_intef_url+"' " +
                                "where esb_interf_id="+esb_interf_id;
                        cFuncDbSqlExecute.ExecUpdateSql(userId,
                                sqlUpd,false,request,apiRoutePath);
                    }
                }
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "修改EAP的IP与端口号异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
