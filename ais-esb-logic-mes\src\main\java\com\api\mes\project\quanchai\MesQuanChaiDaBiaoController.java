package com.api.mes.project.quanchai;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@RestController
@Slf4j
@RequestMapping("/mes/project/quanchai")
public class MesQuanChaiDaBiaoController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.根据机型获取小配方
    @RequestMapping(value = "/MesQcInterfSmallRecipeSelect", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesQcInterfSmallRecipeSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/project/quanchai/MesQcInterfSmallRecipeSelect";
        String selectResult="";
        String errorMsg="";
        try{
            String small_model_type=jsonParas.getString("small_model_type");
            String sql="select station_recipe_3040,station_recipe_3050a,station_recipe_3050b,station_recipe_3080,station_recipe_3090,station_recipe_3110,station_recipe_3130,station_recipe_3145a,station_recipe_3160,station_recipe_3190a," +
                    "station_recipe_3270,station_recipe_3290a,station_recipe_3290b,station_recipe_3310,station_recipe_3330,station_recipe_4040,station_recipe_4120,station_recipe_4140,station_recipe_4160,station_recipe_4180,station_recipe_4400,station_recipe_4430,station_recipe1,station_recipe2,station_recipe3 from c_mes_me_small_recipe " +
                    "where small_model_type='"+small_model_type+"'";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(small_model_type,sql,true,request,apiRoutePath);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "根据机型获取小配方异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
