package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 四轮定位系统接口
 * 1.四轮定位回传结果
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfSldwController {
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.四轮定位回传结果
    @RequestMapping(value = "/PmcCoreSldwResultUpload", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreSldwResultUpload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String selectResult="";
        String errorMsg="";
        String sldwFillTable="d_pmc_me_station_quality_sldw";
        try{
            log.info("四轮定位回传结果:"+jsonParas.toString());

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            //获取参数
            String request_uuid=jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time=jsonParas.getString("request_time");//请求时间
            String request_attr=jsonParas.getString("request_attr");//预留请求属性
            String data_from_sys=jsonParas.getString("data_from_sys");//来源系统
            //新增
            String vin="";//vin号
            String device_code="";//设备编号	工厂在MEDS系统里维护的设备编号
            String car_model="";	//车型	检测的车型名称
            String test_time="";	//检测时间	本次检测完成的时间
            String propulsive_angle="";	//推进角	测量数据
            String propulsion_angle_determination="";	//推进角判定	"0：不合格1：合格 2：无标准值"
            String front_left_wheel_camber="";		//左前轮外倾角	测量数据
            String determination_of_left_front_wheel_camber="";	//左前轮外倾角判定	"0：不合格1：合格 3：无标准值"
            String front_right_wheel_camber="";	//右前轮外倾角	测量数据
            String determination_of_right_front_wheel_camber="";	//右前轮外倾角判定	"0：不合格1：合格 4：无标准值"
            String front_left_caster="";	//左前轮后倾角	测量数据
            String determination_of_left_front_caster="";	//左前轮后倾角判定	"0：不合格1：合格 5：无标准值"
            String right_front_caster="";	//右前轮后倾角	测量数据
            String right_front_caster_angle_determination="";	//右前轮后倾角判定	"0：不合格1：合格 6：无标准值"
            String left_front_wheel_single_toe_in="";	//左前轮单前束	测量数据
            String judgment_of_left_front_wheel_single_toe_in="";	//左前轮单前束判定	"0：不合格1：合格 7：无标准值"
            String right_front_wheel_single_toe_in="";	//右前轮单前束	测量数据
            String right_front_wheel_single_toe_in_determination="";	//右前轮单前束判定	"0：不合格1：合格 8：无标准值"
            String front_wheel_total_toe_in="";	//前轮总前束	测量数据
            String judgment_of_front_wheel_total_toe_in="";	//前轮总前束判定	"0：不合格1：合格 9：无标准值"
            String rear_left_wheel_camber="";	//左后轮外倾角	测量数据
            String determination_of_left_rear_wheel_camber="";	//左后轮外倾角判定	"0：不合格1：合格 10：无标准值"
            String rear_right_wheel_camber="";	//右后轮外倾角	测量数据
            String determination_of_right_rear_wheel_camber="";	//右后轮外倾角判定	"0：不合格1：合格 11：无标准值"
            String single_toe_in_of_left_rear_wheel="";	//左后轮单前束	测量数据
            String judgment_of_left_rear_wheel_single_toe_in="";	//左后轮单前束判定	"0：不合格1：合格 12：无标准值"
            String right_rear_wheel_single_toe_in="";	//右后轮单前束	测量数据
            String right_rear_wheel_single_toe_in_determination="";	//右后轮单前束判定	"0：不合格1：合格 13：无标准值"
            String rear_wheel_total_toe_in="";	//后轮总前束	测量数据
            String judgment_of_total_toe_in_of_rear_wheel="";	//Int	后轮总前束判定	"0：不合格1：合格 14：无标准值"
            String nowDateTime= CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            JSONArray jzArray = jsonParas.getJSONArray("list");
            if(jzArray != null && jzArray.size()>0) {
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for (int i = 0; i < jzArray.size(); i++) {
                    JSONObject jzObject = jzArray.getJSONObject(i);
                    vin = jzObject.getString("vin");
                    device_code=jzObject.getString("device_code");
                    car_model=jzObject.getString("car_model");
                    test_time=jzObject.getString("test_time");
                    propulsive_angle=jzObject.getString("propulsive_angle");
                    propulsion_angle_determination=jzObject.getString("propulsion_angle_determination");
                    front_left_wheel_camber=jzObject.getString("front_left_wheel_camber");
                    determination_of_left_front_wheel_camber=jzObject.getString("determination_of_left_front_wheel_camber");
                    front_right_wheel_camber=jzObject.getString("front_right_wheel_camber");
                    determination_of_right_front_wheel_camber=jzObject.getString("determination_of_right_front_wheel_camber");
                    front_left_caster=jzObject.getString("front_left_caster");
                    determination_of_left_front_caster=jzObject.getString("determination_of_left_front_caster");
                    right_front_caster=jzObject.getString("right_front_caster");
                    right_front_caster_angle_determination=jzObject.getString("right_front_caster_angle_determination");
                    left_front_wheel_single_toe_in=jzObject.getString("left_front_wheel_single_toe_in");
                    judgment_of_left_front_wheel_single_toe_in=jzObject.getString("judgment_of_left_front_wheel_single_toe_in");
                    right_front_wheel_single_toe_in = jzObject.getString("right_front_wheel_single_toe_in");
                    right_front_wheel_single_toe_in_determination = jzObject.getString("right_front_wheel_single_toe_in_determination");
                    front_wheel_total_toe_in = jzObject.getString("front_wheel_total_toe_in");
                    judgment_of_front_wheel_total_toe_in = jzObject.getString("judgment_of_front_wheel_total_toe_in");
                    rear_left_wheel_camber = jzObject.getString("rear_left_wheel_camber");
                    determination_of_left_rear_wheel_camber = jzObject.getString("determination_of_left_rear_wheel_camber");
                    rear_right_wheel_camber = jzObject.getString("rear_right_wheel_camber");
                    determination_of_right_rear_wheel_camber = jzObject.getString("determination_of_right_rear_wheel_camber");
                    single_toe_in_of_left_rear_wheel = jzObject.getString("single_toe_in_of_left_rear_wheel");
                    judgment_of_left_rear_wheel_single_toe_in = jzObject.getString("judgment_of_left_rear_wheel_single_toe_in");
                    right_rear_wheel_single_toe_in = jzObject.getString("right_rear_wheel_single_toe_in");
                    right_rear_wheel_single_toe_in_determination = jzObject.getString("right_rear_wheel_single_toe_in_determination");
                    rear_wheel_total_toe_in = jzObject.getString("rear_wheel_total_toe_in");
                    judgment_of_total_toe_in_of_rear_wheel = jzObject.getString("judgment_of_total_toe_in_of_rear_wheel");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);

                    mapDataItem.put("vin",vin);
                    mapDataItem.put("device_code",device_code);
                    mapDataItem.put("car_model",car_model);
                    mapDataItem.put("test_time",test_time);
                    mapDataItem.put("propulsive_angle",propulsive_angle);
                    mapDataItem.put("propulsion_angle_determination",propulsion_angle_determination);
                    mapDataItem.put("front_left_wheel_camber",front_left_wheel_camber);
                    mapDataItem.put("determination_of_left_front_wheel_camber",determination_of_left_front_wheel_camber);
                    mapDataItem.put("front_right_wheel_camber",front_right_wheel_camber);
                    mapDataItem.put("determination_of_right_front_wheel_camber",determination_of_right_front_wheel_camber);
                    mapDataItem.put("front_left_caster",front_left_caster);
                    mapDataItem.put("determination_of_left_front_caster",determination_of_left_front_caster);
                    mapDataItem.put("right_front_caster",right_front_caster);
                    mapDataItem.put("right_front_caster_angle_determination",right_front_caster_angle_determination);
                    mapDataItem.put("left_front_wheel_single_toe_in",left_front_wheel_single_toe_in);
                    mapDataItem.put("judgment_of_left_front_wheel_single_toe_in",judgment_of_left_front_wheel_single_toe_in);
                    mapDataItem.put("right_front_wheel_single_toe_in",right_front_wheel_single_toe_in);
                    mapDataItem.put("right_front_wheel_single_toe_in_determination",right_front_wheel_single_toe_in_determination);
                    mapDataItem.put("front_wheel_total_toe_in",front_wheel_total_toe_in);
                    mapDataItem.put("judgment_of_front_wheel_total_toe_in",judgment_of_front_wheel_total_toe_in);
                    mapDataItem.put("rear_left_wheel_camber",rear_left_wheel_camber);
                    mapDataItem.put("determination_of_left_rear_wheel_camber",determination_of_left_rear_wheel_camber);
                    mapDataItem.put("rear_right_wheel_camber",rear_right_wheel_camber);
                    mapDataItem.put("determination_of_right_rear_wheel_camber",determination_of_right_rear_wheel_camber);
                    mapDataItem.put("single_toe_in_of_left_rear_wheel",single_toe_in_of_left_rear_wheel);
                    mapDataItem.put("judgment_of_left_rear_wheel_single_toe_in",judgment_of_left_rear_wheel_single_toe_in);
                    mapDataItem.put("right_rear_wheel_single_toe_in",right_rear_wheel_single_toe_in);
                    mapDataItem.put("right_rear_wheel_single_toe_in_determination",right_rear_wheel_single_toe_in_determination);
                    mapDataItem.put("rear_wheel_total_toe_in",rear_wheel_total_toe_in);
                    mapDataItem.put("judgment_of_total_toe_in_of_rear_wheel",judgment_of_total_toe_in_of_rear_wheel);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                    /*
                    String sqlDxStationIns="insert into d_pmc_me_station_quality_sldw " +
                            "(created_by,creation_date," +
                            "vin,device_code,car_model,test_time,propulsive_angle,propulsion_angle_determination," +
                            "front_left_wheel_camber,determination_of_left_front_wheel_camber,front_right_wheel_camber," +
                            "determination_of_right_front_wheel_camber,front_left_caster,determination_of_left_front_caster," +
                            "right_front_caster,right_front_caster_angle_determination,left_front_wheel_single_toe_in," +
                            "judgment_of_left_front_wheel_single_toe_in,right_front_wheel_single_toe_in,right_front_wheel_single_toe_in_determination," +
                            "front_wheel_total_toe_in,judgment_of_front_wheel_total_toe_in,rear_left_wheel_camber," +
                            "determination_of_left_rear_wheel_camber,rear_right_wheel_camber,determination_of_right_rear_wheel_camber," +
                            "single_toe_in_of_left_rear_wheel,judgment_of_left_rear_wheel_single_toe_in,right_rear_wheel_single_toe_in," +
                            "right_rear_wheel_single_toe_in_determination,rear_wheel_total_toe_in,judgment_of_total_toe_in_of_rear_wheel) values " +
                            "('"+data_from_sys+"','"+nowDateTime+"','"+
                            vin+"','"+device_code+"','" +car_model+"','" +test_time+"','" +propulsive_angle+"','" +propulsion_angle_determination+"','" +
                            front_left_wheel_camber+"','" +determination_of_left_front_wheel_camber+"','" +front_right_wheel_camber+"','" +
                            determination_of_right_front_wheel_camber+"','" +front_left_caster+"','" +determination_of_left_front_caster+"','" +
                            right_front_caster+"','" +right_front_caster_angle_determination+"','" +left_front_wheel_single_toe_in+"','" +
                            judgment_of_left_front_wheel_single_toe_in+"','" +right_front_wheel_single_toe_in+"','" +right_front_wheel_single_toe_in_determination+"','" +
                            front_wheel_total_toe_in+"','" +judgment_of_front_wheel_total_toe_in+"','" +rear_left_wheel_camber+"','" +
                            determination_of_left_rear_wheel_camber+"','" +rear_right_wheel_camber+"','" +determination_of_right_rear_wheel_camber+"','" +
                            single_toe_in_of_left_rear_wheel+"','" +judgment_of_left_rear_wheel_single_toe_in+"','" +right_rear_wheel_single_toe_in+"','" +
                            right_rear_wheel_single_toe_in_determination+"','" +rear_wheel_total_toe_in+"','" +judgment_of_total_toe_in_of_rear_wheel+"')";
                    cFuncDbSqlExecute.ExecUpdateSql(data_from_sys,sqlDxStationIns,false,request,apiRoutePath);*/
                }
                mongoTemplate.insert(lstDocuments,sldwFillTable);
            }
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "四轮定位回传结果异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
