package com.api.eap.core.unload;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 收扳机Strip工序特殊逻辑
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-8-31
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/unload")
public class EapCoreUnLoadStripController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //保存任务信息到数据库[收板机切条]
    @RequestMapping(value = "/EapCoreUnLoadStripCutPlanSave", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapCoreUnLoadStripCutPlanSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/core/unload/EapCoreUnLoadStripCutPlanSave";
        String transResult="";
        String errorMsg="";
        String meUserTable="a_eap_me_station_user";
        String apsPlanTable="a_eap_aps_plan";
        String apsPlanBTable="a_eap_aps_plan_d";
        try{
            String station_code=jsonParas.getString("station_code");
            String sqlStation="select station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station where station_code='"+station_code+"'";
            List<Map<String, Object>> lstStation=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlStation,false,request,apiRoutePath);
            if(lstStation==null || lstStation.size()<=0){
                errorMsg="未能根据工位号{"+station_code+"}查找到工位配置信息";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id=lstStation.get(0).get("station_id").toString();
            String station_attr=lstStation.get(0).get("station_attr").toString();
            if(station_attr!=null && station_attr.equals("UnLoad")){
                //收板机,需要判断是否为离线模式,若为离线模式则不执行任务接受
                String aisMonitorModel=cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagOnOffLine="";
                if(aisMonitorModel.equals("AIS-PC")){
                    tagOnOffLine="UnLoadPlc/PlcConfig/OnOffLine";
                }
                else if(aisMonitorModel.equals("AIS-SERVER")){
                    tagOnOffLine="UnLoadPlc_"+station_code+"/PlcConfig/OnOffLine";
                }
                else{
                    errorMsg="未配置收板机系统参数AIS_MONITOR_MODE";
                    transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,tagOnOffLine);
                if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                    JSONObject jbItem=jsonArrayTag.getJSONObject(0);
                    String tagOnOffLineValue=jbItem.getString("tag_value");
                    if(tagOnOffLineValue.equals("")){
                        errorMsg="查询收板机在线与离线状态时收板机PLC断网";
                        transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    if(tagOnOffLineValue.equals("0")){//离线
                        transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
                        return transResult;
                    }
                }
                else{
                    errorMsg="未查询到收板机在线与离线状态";
                    transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }

            JSONArray plan_list=jsonParas.getJSONArray("plan_list");
            if(plan_list==null || plan_list.size()<=0){
                errorMsg="保存任务信息不能为空集合";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //若是收板机,判断是否存在PLAN或者WORK的任务,若存在则不执行再次插入
            if(station_attr.equals("UnLoad")){
                String[] group_lot_status2=new String[]{"PLAN","WORK"};
                JSONObject jbItem=plan_list.getJSONObject(0);
                String group_lot_num_unload=jbItem.getString("group_lot_num")==null ? "":jbItem.getString("group_lot_num");
                Query queryBigDataUnLoad = new Query();
                queryBigDataUnLoad.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigDataUnLoad.addCriteria(Criteria.where("group_lot_num").is(group_lot_num_unload));
                queryBigDataUnLoad.addCriteria(Criteria.where("group_lot_status").in(group_lot_status2));
                long allCount =  mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigDataUnLoad.getQueryObject());
                if(allCount>0){
                    transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
                    return transResult;
                }
            }

            //若是收板机且当前模式为收板机上游决定生产顺序,则不能进行设定lot_group_status的PLAN
            String final_lot_group_status="PLAN";
            if(station_attr.equals("UnLoad")){
                String aisMonitorModel=cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagTaskOrderByUpDevice="";
                String clientCode="";
                if(aisMonitorModel.equals("AIS-PC")){
                    tagTaskOrderByUpDevice="UnLoadAis/AisConfig/TaskOrderByUpDevice";
                    clientCode="UnLoadAis";
                }
                else if(aisMonitorModel.equals("AIS-SERVER")){
                    tagTaskOrderByUpDevice="UnLoadAis_"+station_code+"/AisConfig/TaskOrderByUpDevice";
                    clientCode="UnLoadAis_"+station_code;
                }
                //1.先判断是否存在tag点
                String sqlOnlyCountTag="select count(1) " +
                        "from scada_tag st inner join scada_tag_group stg " +
                        "on st.tag_group_id=stg.tag_group_id inner join scada_client sc " +
                        "on stg.client_id=sc.client_id " +
                        "where sc.enable_flag='Y' and stg.enable_flag='Y' and st.enable_flag='Y' " +
                        "and sc.client_code='"+clientCode+"' and stg.tag_group_code='AisConfig' " +
                        "and st.tag_code='TaskOrderByUpDevice'";
                Integer OnlyCountTag=cFuncDbSqlResolve.GetSelectCount(sqlOnlyCountTag);
                if(OnlyCountTag>0){
                    JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,tagTaskOrderByUpDevice);
                    if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                        JSONObject jbItem=jsonArrayTag.getJSONObject(0);
                        String tagTaskOrderByUpDeviceValue=jbItem.getString("tag_value");
                        if(tagTaskOrderByUpDeviceValue.equals("")){
                            errorMsg="查询收板机工单任务生产顺序来源于上游设备状态时收板机PLC断网";
                            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        if(tagTaskOrderByUpDeviceValue.equals("1")){
                            final_lot_group_status="WAIT";
                        }
                    }
                    else{
                        errorMsg="未查询到收板机工单任务生产顺序来源于上游设备状态";
                        transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
            }

            //查询当前登入者
            String user_name="";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                user_name=docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstPlanDocuments=new ArrayList<>();
            List<Map<String, Object>> lstPlanBDocuments=new ArrayList<>();
            String group_id=CFuncUtilsSystem.CreateUUID(true);
            for(int i=0;i<plan_list.size();i++){
                JSONObject jbItem=plan_list.getJSONObject(i);
                Map<String, Object> mapBigDataRow=new HashMap<>();
                String plan_id=CFuncUtilsSystem.CreateUUID(true);
                String port_code=jbItem.getString("port_code")==null ? "":jbItem.getString("port_code");
                if(station_attr.equals("UnLoad")) port_code="";
                mapBigDataRow.put("item_date",item_date);
                mapBigDataRow.put("item_date_val",item_date_val);
                mapBigDataRow.put("plan_id",plan_id);
                mapBigDataRow.put("station_id",Long.parseLong(station_id));
                mapBigDataRow.put("task_from",jbItem.getString("task_from")==null ? "EAP":jbItem.getString("task_from"));
                mapBigDataRow.put("group_lot_num",jbItem.getString("group_lot_num")==null ? "":jbItem.getString("group_lot_num"));
                mapBigDataRow.put("lot_num",jbItem.getString("lot_num")==null ? "":jbItem.getString("lot_num"));
                mapBigDataRow.put("lot_short_num",jbItem.getString("lot_short_num")==null ? "":jbItem.getString("lot_short_num"));
                mapBigDataRow.put("lot_index",jbItem.getInteger("lot_index")==null ? 1:jbItem.getInteger("lot_index"));
                mapBigDataRow.put("plan_lot_count",jbItem.getInteger("fp_count")==null ? 0:jbItem.getInteger("fp_count"));
                mapBigDataRow.put("target_lot_count",jbItem.getInteger("fp_count")==null ? 0:jbItem.getInteger("fp_count"));
                mapBigDataRow.put("port_code",port_code);
                mapBigDataRow.put("material_code",jbItem.getString("material_code")==null ? "":jbItem.getString("material_code"));
                mapBigDataRow.put("pallet_num",jbItem.getString("pallet_num")==null ? "":jbItem.getString("pallet_num"));
                mapBigDataRow.put("pallet_type",jbItem.getString("pallet_type")==null ? "":jbItem.getString("pallet_type"));
                mapBigDataRow.put("lot_level",jbItem.getString("lot_level")==null ? "":jbItem.getString("lot_level"));
                mapBigDataRow.put("panel_length",jbItem.getDouble("panel_length")==null ? 0d:jbItem.getDouble("panel_length"));
                mapBigDataRow.put("panel_width",jbItem.getDouble("panel_width")==null ? 0d:jbItem.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness",jbItem.getDouble("panel_tickness")==null ? 0d:jbItem.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_model",jbItem.getInteger("panel_model")==null ? -1:jbItem.getInteger("panel_model"));
                mapBigDataRow.put("inspect_count",jbItem.getInteger("inspect_count")==null ? 0:jbItem.getInteger("inspect_count"));
                mapBigDataRow.put("inspect_finish_count",0);
                mapBigDataRow.put("pdb_count",jbItem.getInteger("pdb_count")==null ? 0:jbItem.getInteger("pdb_count"));
                mapBigDataRow.put("pdb_rule",jbItem.getInteger("pdb_rule")==null ? 0:jbItem.getInteger("pdb_rule"));
                mapBigDataRow.put("fp_count",jbItem.getInteger("fp_count")==null ? 0:jbItem.getInteger("fp_count"));
                mapBigDataRow.put("group_lot_status",final_lot_group_status);
                mapBigDataRow.put("lot_status","PLAN");
                mapBigDataRow.put("finish_count",0);
                mapBigDataRow.put("finish_ok_count",0);
                mapBigDataRow.put("finish_ng_count",0);
                mapBigDataRow.put("task_error_code",0);
                mapBigDataRow.put("item_info",jbItem.getString("item_info")==null ? "":jbItem.getString("item_info"));
                mapBigDataRow.put("task_start_time","");
                mapBigDataRow.put("task_end_time","");
                mapBigDataRow.put("task_cost_time",(long)0);
                mapBigDataRow.put("attribute1",jbItem.getString("attribute1")==null ? "":jbItem.getString("attribute1"));
                mapBigDataRow.put("attribute2",jbItem.getString("attribute2")==null ? "":jbItem.getString("attribute2"));
                mapBigDataRow.put("attribute3",jbItem.getString("attribute3")==null ? "":jbItem.getString("attribute3"));
                mapBigDataRow.put("face_code",jbItem.getInteger("face_code")==null ? 0:jbItem.getInteger("face_code"));
                mapBigDataRow.put("pallet_use_count",jbItem.getInteger("pallet_use_count")==null ? 0:jbItem.getInteger("pallet_use_count"));
                mapBigDataRow.put("user_name",user_name);
                mapBigDataRow.put("group_id",group_id);
                mapBigDataRow.put("offline_flag","N");
                mapBigDataRow.put("target_update_count",0);

                lstPlanDocuments.add(mapBigDataRow);
                String panel_list=jbItem.getString("panel_list")==null ? "":jbItem.getString("panel_list");
                String[] panelList=panel_list.split(",",-1);
                if(!panel_list.equals("")){
                    if(panelList!=null && panelList.length>0){
                        for(int j=0;j<panelList.length;j++){
                            String panel_barcode=panelList[j];
                            String plan_d_id=CFuncUtilsSystem.CreateUUID(true);
                            Map<String, Object> mapBigDataRowB=new HashMap<>();
                            mapBigDataRowB.put("item_date",item_date);
                            mapBigDataRowB.put("item_date_val",item_date_val);
                            mapBigDataRowB.put("plan_d_id",plan_d_id);
                            mapBigDataRowB.put("plan_id",plan_id);
                            mapBigDataRowB.put("panel_barcode",panel_barcode);
                            lstPlanBDocuments.add(mapBigDataRowB);
                        }
                    }
                }
            }
            if(lstPlanBDocuments.size()>0) mongoTemplate.insert(lstPlanBDocuments,apsPlanBTable);
            mongoTemplate.insert(lstPlanDocuments,apsPlanTable);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "保存任务信息到数据库异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //放板机完工同步到收板机[Strip切条]
    @RequestMapping(value = "/EapCoreUnLoadStripCutPlanLoadFinish", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapCoreUnLoadStripCutPlanLoadFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/core/unload/EapCoreUnLoadStripCutPlanLoadFinish";
        String transResult="";
        String errorMsg="";
        String apsPlanTable="a_eap_aps_plan";
        try{
            String station_code=jsonParas.getString("station_code");//收板机工位
            String sqlStation="select station_id " +
                    "from sys_fmod_station where station_code='"+station_code+"'";
            List<Map<String, Object>> lstStation=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlStation,false,request,apiRoutePath);
            if(lstStation==null || lstStation.size()<=0){
                errorMsg="未能根据工位号{"+station_code+"}查找到工位配置信息";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id=lstStation.get(0).get("station_id").toString();
            JSONArray load_panel_list=jsonParas.getJSONArray("load_panel_list");
            JSONArray load_lot_list=jsonParas.getJSONArray("load_lot_list");
            String target_group_lot_num="";
            Integer target_plan_count=0;
            Integer target_update_count_sum=0;
            String group_lot_status_now="";

            String[] group_lot_status=new String[]{"WAIT","PLAN","WORK"};
            if(load_lot_list!=null && load_lot_list.size()>0){
                for(int i=0;i<load_lot_list.size();i++){
                    JSONObject jbItem=load_lot_list.getJSONObject(i);
                    String group_lot_num=jbItem.getString("group_lot_num");
                    if(i==0) target_group_lot_num=group_lot_num;
                    String lot_num=jbItem.getString("lot_num");
                    Integer plan_lot_count=jbItem.getInteger("plan_lot_count");
                    Integer finish_count=jbItem.getInteger("finish_count");
                    Integer finish_ok_count=jbItem.getInteger("finish_ok_count");
                    Integer finish_ng_count=jbItem.getInteger("finish_ng_count");
                    Integer fp_count=jbItem.getInteger("fp_count");
                    //每片Panel分条数量
                    Integer oncePanelCutCount=0;
                    if(plan_lot_count>0 && fp_count>0){
                        oncePanelCutCount=fp_count/plan_lot_count;
                        finish_ok_count=finish_ok_count*oncePanelCutCount;
                    }
                    target_plan_count+=finish_ok_count;

                    //先查找数据
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if(iteratorBigData.hasNext()){
                        Document docItemBigData = iteratorBigData.next();
                        Integer target_update_count=0;
                        if(docItemBigData.containsKey("target_update_count")){
                            target_update_count=docItemBigData.getInteger("target_update_count");
                        }
                        target_update_count_sum+=target_update_count;
                        finish_ok_count+=target_update_count;
                        group_lot_status_now=docItemBigData.getString("group_lot_status");
                        iteratorBigData.close();
                    }

                    //更新
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    Update updateBigData = new Update();
                    updateBigData.set("target_lot_count",finish_ok_count);
                    updateBigData.set("target_update_count",finish_ok_count);
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }
            }

            Boolean isCancel=false;
            if(target_plan_count<=0 && target_update_count_sum<=0) isCancel=true;

            //判断放板数量是否为0
            if(target_group_lot_num!=null && !target_group_lot_num.equals("") && isCancel){

                //判断是否写入点位CANCEL
                if(group_lot_status_now.equals("WORK")){
                    String aisMonitorModel=cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                    String tagTaskCancelRequest="";
                    if(aisMonitorModel.equals("AIS-PC")){
                        tagTaskCancelRequest="UnLoadAis/AisStatus/TaskCancelRequest";
                    }
                    else if(aisMonitorModel.equals("AIS-SERVER")){
                        tagTaskCancelRequest="UnLoadAis_"+station_code+"/AisStatus/TaskCancelRequest";
                    }
                    else{
                        errorMsg="未配置收板机系统参数AIS_MONITOR_MODE";
                        transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    errorMsg= cFuncUtilsCellScada.WriteTagByStation(station_code,station_code,tagTaskCancelRequest,"1",true);
                    if(!errorMsg.equals("")){
                        transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }

                //再更新
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                Update updateBigData2 = new Update();
                updateBigData2.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
                return transResult;
            }

            //判断是否当前任务正在进行中,若是进行中,则需要把任务数量更新到PLC
            if(target_group_lot_num!=null && !target_group_lot_num.equals("") && target_plan_count>0){
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                String port_code="";
                if(iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    port_code=docItemBigData.getString("port_code");
                    iteratorBigData.close();
                }
                if(port_code!=null && !port_code.equals("")){
                    String sqlPort="select port_index " +
                            "from a_eap_fmod_station_port " +
                            "where station_id="+station_id+" and port_code='"+port_code+"'";
                    List<Map<String, Object>> lstPort=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlPort,false,request,apiRoutePath);
                    if(lstPort!=null && lstPort.size()>0){
                        String port_index=lstPort.get(0).get("port_index").toString();
                        String aisMonitorModel=cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                        String tagUpdateLotCountStatus="";
                        String tagUpdateLotCount="";
                        if(aisMonitorModel.equals("AIS-PC")){
                            tagUpdateLotCountStatus="UnLoadPlc/AisReqControl"+port_index+"/UpdateLotCountStatus";
                            tagUpdateLotCount="UnLoadPlc/AisReqControl"+port_index+"/UpdateLotCount";
                        }
                        else if(aisMonitorModel.equals("AIS-SERVER")){
                            tagUpdateLotCountStatus="UnLoadPlc_"+station_code+"/AisReqControl"+port_index+"/UpdateLotCountStatus";
                            tagUpdateLotCount="UnLoadPlc_"+station_code+"/AisReqControl"+port_index+"/UpdateLotCount";
                        }
                        else{
                            errorMsg="未配置收板机系统参数AIS_MONITOR_MODE";
                            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        String tagOnlyKeyList=tagUpdateLotCount+","+tagUpdateLotCountStatus;
                        String tagValueList=(target_plan_count+target_update_count_sum)+"&1";
                        errorMsg= cFuncUtilsCellScada.WriteTagByStation(station_code,station_code,tagOnlyKeyList,tagValueList,true);
                        if(!errorMsg.equals("")){
                            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                    }
                }
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "放板机完工同步到收板机异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
