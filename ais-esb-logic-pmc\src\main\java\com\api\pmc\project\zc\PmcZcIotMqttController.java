package com.api.pmc.project.zc;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 中车IOT接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-04-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/project/zc")
public class PmcZcIotMqttController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlMapper cFuncDbSqlMapper;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
}
