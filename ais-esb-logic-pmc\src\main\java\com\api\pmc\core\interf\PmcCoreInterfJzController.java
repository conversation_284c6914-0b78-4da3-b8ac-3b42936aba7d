package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 加注接口
 * 1.加注数据回传
 * 2.加注设备报警信息回传
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfJzController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.加注数据回传
    @RequestMapping(value = "/PmcCoreJzReportStation", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreJzReportStation(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreJzReportStation";
        String selectResult = "";
        String errorMsg = "";
        String jzFillTable = "d_pmc_me_station_quality_fill";
        try {
            log.info("加注数据回传:" + jsonParas.toString());

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            //获取参数
            String request_uuid = jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time = jsonParas.getString("request_time");//请求时间
            String request_attr = jsonParas.getString("request_attr");//预留请求属性
            String data_from_sys = jsonParas.getString("data_from_sys");//来源系统，待定义
            //1、新增
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            String vin = "";//VIN号
            String shebeibh = "";//设备编号
            String shebeibhms = "";//设备描述
            String dh = "";//图号
            String shedingjzl = "";//设定加注量
            String shijijzl = "";//实际加注量
            String shedingyl = "";//设定压力
            String shijiyl = "";//实际压力
            String shedingczkz = "";//设定粗抽真空值
            String shijiczkz = "";//实际粗抽真空值
            String shedingxzkz = "";//设定细抽真空值
            String shijixzkz = "";//实际细抽真空值
            String shedingzkjl = "";//设定真空检漏
            String shijizkjl = "";//实际真空检漏
            String shedingzyjl = "";//设定正压检漏
            String shijizyjl = "";//实际正压检漏
            String zuoyesj = "";//作业时间
            String jiazhujp = "";//加注节拍
            String gongyicsly = "";//工艺参数来源
            String panding = "";//加注结果判定(0未判定1合格2不合格)
            JSONArray jzArray = jsonParas.getJSONArray("list");
            if (jzArray != null && jzArray.size() > 0) {
                List<Map<String, Object>> lstDocuments = new ArrayList<>();
                for (int i = 0; i < jzArray.size(); i++) {
                    JSONObject jzObject = jzArray.getJSONObject(i);
                    vin = jzObject.getString("vin");//VIN号
                    shebeibh = jzObject.getString("shebeibh");//设备编号
                    dh = jzObject.getString("dh");//图号
                    shedingjzl = jzObject.getString("shedingjzl");//设定加注量
                    shijijzl = jzObject.getString("shijijzl");//实际加注量
                    shedingyl = jzObject.getString("shedingyl");//设定压力
                    shijiyl = jzObject.getString("shijiyl");//实际压力
                    shedingczkz = jzObject.getString("shedingczkz");//设定粗抽真空值
                    shijiczkz = jzObject.getString("shijiczkz");//实际粗抽真空值
                    shedingxzkz = jzObject.getString("shedingxzkz");//设定细抽真空值
                    shijixzkz = jzObject.getString("shijixzkz");//实际细抽真空值
                    shedingzkjl = jzObject.getString("shedingzkjl");//设定真空检漏
                    shijizkjl = jzObject.getString("shijizkjl");//实际真空检漏
                    shedingzyjl = jzObject.getString("shedingzyjl");//设定正压检漏
                    shijizyjl = jzObject.getString("shijizyjl");//实际正压检漏
                    zuoyesj = jzObject.getString("zuoyesj");//作业时间
                    jiazhujp = jzObject.getString("jiazhujp");//加注节拍
                    gongyicsly = jzObject.getString("gongyicsly");//工艺参数来源
                    panding = jzObject.getString("panding");//加注结果判定,1合格，2不合格

                    //2、先根据设备号查询设备描述
                    String sqlDevice = "select COALESCE(device_des,'') device_des " +
                            "from d_pmc_fmod_station_device " +
                            "where enable_flag='Y' " +
                            "and device_code='" + shebeibh + "'";
                    List<Map<String, Object>> itemListDevice = cFuncDbSqlExecute.ExecSelectSql(shebeibh, sqlDevice,
                            false, request, apiRoutePath);
                    if (itemListDevice != null && itemListDevice.size() > 0) {
                        shebeibhms = itemListDevice.get(0).get("device_des").toString();
                    }
                    //3、新增 Mongodb
                    Map<String, Object> mapDataItem = new HashMap<>();
                    String quality_trace_id = CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date", item_date);
                    mapDataItem.put("item_date_val", item_date_val);
                    mapDataItem.put("quality_trace_id", quality_trace_id);

                    mapDataItem.put("vin", vin);
                    mapDataItem.put("shebeibh", shebeibh);
                    mapDataItem.put("shebeibhms", shebeibhms);
                    mapDataItem.put("dh", dh);
                    mapDataItem.put("shedingjzl", shedingjzl);
                    mapDataItem.put("shijijzl", shijijzl);
                    mapDataItem.put("shedingyl", shedingyl);
                    mapDataItem.put("shijiyl", shijiyl);
                    mapDataItem.put("shedingczkz", shedingczkz);
                    mapDataItem.put("shijiczkz", shijiczkz);
                    mapDataItem.put("shedingxzkz", shedingxzkz);
                    mapDataItem.put("shijixzkz", shijixzkz);
                    mapDataItem.put("shedingzkjl", shedingzkjl);
                    mapDataItem.put("shijizkjl", shijizkjl);
                    mapDataItem.put("shedingzyjl", shedingzyjl);
                    mapDataItem.put("shijizyjl", shijizyjl);
                    mapDataItem.put("zuoyesj", zuoyesj);
                    mapDataItem.put("jiazhujp", jiazhujp);
                    mapDataItem.put("gongyicsly", gongyicsly);
                    mapDataItem.put("panding", panding);
                    mapDataItem.put("up_flag", "N");
                    mapDataItem.put("up_code", -1);
                    mapDataItem.put("up_msg", "");
                    lstDocuments.add(mapDataItem);
                    /*
                    String insertSql="insert into d_pmc_me_station_quality_fill " +
                            "(created_by,creation_date," +
                            "vin,shebeibh,dh," +
                            "shedingjzl,shijijzl,shedingyl,shijiyl," +
                            "shedingczkz,shijiczkz,shedingxzkz,shijixzkz," +
                            "shedingzkjl,shijizkjl,shedingzyjl,shijizyjl," +
                            "zuoyesj,jiazhujp,gongyicsly,panding " +
                            ") values (" + "'"+data_from_sys+"','"+nowDateTime+"','"+
                            vin+"','"+shebeibh+"','"+dh+"',"+
                            shedingjzl+","+shijijzl+","+shedingyl+","+shijiyl+","+
                            shedingczkz+","+shijiczkz+","+shedingxzkz+","+shijixzkz+","+
                            shedingzkjl+","+shijizkjl+","+shedingzyjl+","+shijizyjl+",'"+
                            zuoyesj+"',"+jiazhujp+","+gongyicsly+","+panding +")";
                    cFuncDbSqlExecute.ExecUpdateSql(userName,insertSql,true,request,apiRoutePath);*/
                }
                mongoTemplate.insert(lstDocuments, jzFillTable);
            }

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "加注数据回传异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.加注设备报警信息回传
    @RequestMapping(value = "/PmcCoreJzAlarm", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreJzAlarm(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreJzAlarm";
        String selectResult = "";
        String errorMsg = "";
        try {
            log.info("加注设备报警信息回传:" + jsonParas.toString());

            String request_uuid = jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time = jsonParas.getString("request_time");//请求时间
            String request_attr = jsonParas.getString("request_attr");//预留请求属性
            String data_from_sys = jsonParas.getString("data_from_sys");//来源系统，待定义

            String vin = jsonParas.getString("vin");//VIN号
            String shebeibh = jsonParas.getString("shebeibh");//设备编号
            String baojingnr = jsonParas.getString("baojingnr");//报警内容
            String baojingqssj = jsonParas.getString("baojingqssj");//报警发生时间

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "加注设备报警信息回传异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //3.加注设备状态保存
    @RequestMapping(value = "/PmcCoreJzDeviceStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreJzDeviceStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreJzDeviceStatus";
        String selectResult = "";
        String errorMsg = "";
        String jzFillDeviceStatusTable = "d_pmc_me_fill_device_status";
        try {
            log.info("加注设备状态数据回传:" + jsonParas.toString());

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            //获取参数
            String request_uuid = jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time = jsonParas.getString("request_time");//请求时间
            String request_attr = jsonParas.getString("request_attr");//预留请求属性
            String data_from_sys = jsonParas.getString("data_from_sys");//来源系统，待定义
            //1、新增
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            String shebeibh = jsonParas.getString("shebeibh");//设备编号
            int device_status = jsonParas.getInteger("device_status");//设备状态(1在线2离线)
            //2、新增 Mongodb
            Map<String, Object> mapDataItem = new HashMap<>();
            String fill_device_id = CFuncUtilsSystem.CreateUUID(true);
            mapDataItem.put("item_date", item_date);
            mapDataItem.put("item_date_val", item_date_val);
            mapDataItem.put("fill_device_id", fill_device_id);

            mapDataItem.put("shebeibh", shebeibh);
            mapDataItem.put("device_status", device_status);
            mongoTemplate.insert(mapDataItem, jzFillDeviceStatusTable);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "加注设备状态数据回传" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //4.中控接收上位加注参数
    @RequestMapping(value = "/PmcCoreJzStationOilFilling", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreJzStationOilFilling(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreJzStationOilFilling";
        String selectResult = "";
        String errorMsg = "";
        String oilFillingTable = "d_pmc_me_station_oil_filling";
        try {
            log.info("获取上位加注数据:" + jsonParas.toString());

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            //获取参数
            String request_uuid = jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time = jsonParas.getString("request_time");//请求时间
            String request_attr = jsonParas.getString("request_attr");//预留请求属性
            //1、新增
            JSONArray jzArray = jsonParas.getJSONArray("list");//加注数据列表
            if (jzArray != null && jzArray.size() > 0) {
                List<Map<String, Object>> lstDocuments = new ArrayList<>();
                for (int i = 0; i < jzArray.size(); i++) {
                    JSONObject jzObject = jzArray.getJSONObject(i);
                    String final_vacuum_time = jzObject.getString("final_vacuum_time");//最终真空时间
                    String fine_vacuum_time = jzObject.getString("fine_vacuum_time");//精抽真空时间
                    String filling_pressure_deviation = jzObject.getString("filling_pressure_deviation");//加注压力上偏差
                    String program_name = jzObject.getString("program_name");//程序名称
                    String blow_back_suction_time = jzObject.getString("blow_back_suction_time");//吹气回吸时间
                    String facility_code = jzObject.getString("facility_code");//加注设备编码
                    String coarse_vacuum_time = jzObject.getString("coarse_vacuum_time");//粗抽真空时间
                    String program_code = jzObject.getString("program_code");//程序编号
                    String fine_vacuum = jzObject.getString("fine_vacuum");//精抽真空
                    String filling_pressure_under_deviation = jzObject.getString("filling_pressure_under_deviation");//加注压力下偏差
                    String positive_leak_sensing_time = jzObject.getString("positive_leak_sensing_time");//正压检漏时间
                    String filling_time = jzObject.getString("filling_time");//加注时间
                    String vacuum_leak_sensing = jzObject.getString("vacuum_leak_sensing");//真空检漏
                    String facility_name = jzObject.getString("facility_name");//设备名称
                    String filling_quantity_under_deviation = jzObject.getString("filling_quantity_under_deviation");//加注量下偏差
                    String positive_leak_sensing = jzObject.getString("positive_leak_sensing");//正压检漏值
                    String coarse_vacuum = jzObject.getString("coarse_vacuum");//粗抽真空
                    String suction_pressure_back_time1 = jzObject.getString("suction_pressure_back_time1");//泄压回吸1时间
                    String part_code1 = jzObject.getString("part_code1");//零件编号1
                    String filling_pressure = jzObject.getString("filling_pressure");//加注压力
                    String suction_pressure_back_time2 = jzObject.getString("suction_pressure_back_time2");//泄压回吸2时间
                    String vacuum_leak_sensing_time = jzObject.getString("vacuum_leak_sensing_time");//真空检漏时间
                    String filling_quantity_deviation = jzObject.getString("filling_quantity_deviation");//真空检漏
                    String filling_way = jzObject.getString("filling_way");//加注方式
                    String part_name1 = jzObject.getString("part_name1");//加注量下偏差
                    String filling_quantity = jzObject.getString("filling_quantity");//加注量
                    String order_prod = jzObject.getString("order_prod");//订单号
                    String final_vacuum = jzObject.getString("final_vacuum");//最终真空

                    Map<String, Object> mapDataItem = new HashMap<>();
                    String oil_filling_id = CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date", item_date);
                    mapDataItem.put("item_date_val", item_date_val);
                    mapDataItem.put("oil_filling_id", oil_filling_id);

                    mapDataItem.put("final_vacuum_time", final_vacuum_time);
                    mapDataItem.put("fine_vacuum_time", fine_vacuum_time);
                    mapDataItem.put("filling_pressure_deviation", filling_pressure_deviation);
                    mapDataItem.put("program_name", program_name);
                    mapDataItem.put("blow_back_suction_time", blow_back_suction_time);
                    mapDataItem.put("facility_code", facility_code);
                    mapDataItem.put("coarse_vacuum_time", coarse_vacuum_time);
                    mapDataItem.put("program_code", program_code);
                    mapDataItem.put("fine_vacuum", fine_vacuum);
                    mapDataItem.put("filling_pressure_under_deviation", filling_pressure_under_deviation);
                    mapDataItem.put("positive_leak_sensing_time", positive_leak_sensing_time);
                    mapDataItem.put("filling_time", filling_time);
                    mapDataItem.put("vacuum_leak_sensing", vacuum_leak_sensing);
                    mapDataItem.put("facility_name", facility_name);
                    mapDataItem.put("filling_quantity_under_deviation", filling_quantity_under_deviation);
                    mapDataItem.put("positive_leak_sensing", positive_leak_sensing);
                    mapDataItem.put("coarse_vacuum", coarse_vacuum);
                    mapDataItem.put("suction_pressure_back_time1", suction_pressure_back_time1);
                    mapDataItem.put("part_code1", part_code1);
                    mapDataItem.put("filling_pressure", filling_pressure);
                    mapDataItem.put("suction_pressure_back_time2", suction_pressure_back_time2);
                    mapDataItem.put("vacuum_leak_sensing_time", vacuum_leak_sensing_time);
                    mapDataItem.put("filling_quantity_deviation", filling_quantity_deviation);
                    mapDataItem.put("filling_way", filling_way);
                    mapDataItem.put("part_name1", part_name1);
                    mapDataItem.put("filling_quantity", filling_quantity);
                    mapDataItem.put("order_prod", order_prod);
                    mapDataItem.put("final_vacuum", final_vacuum);
                    mapDataItem.put("vin", "");
                    mapDataItem.put("up_flag", "N");
                    mapDataItem.put("up_code", -1);
                    mapDataItem.put("up_msg", "");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments, oilFillingTable);
            }

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "加注数据回传异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
