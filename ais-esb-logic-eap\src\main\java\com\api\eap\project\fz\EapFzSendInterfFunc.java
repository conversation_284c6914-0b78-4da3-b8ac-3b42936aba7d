package com.api.eap.project.fz;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.EapCoreSharePlanController;
import com.api.eap.project.glorysoft.EapGloryInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Array;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 方正EAP发送接口方法内容
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@Service
@Slf4j
public class EapFzSendInterfFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapFzInterfCommon eapFzInterfCommon;
    @Autowired
    private EapCoreSharePlanController eapCoreSharePlanController;

    //[EAP]设备初始化建立通讯请求上报
    public JSONObject eapAreYouThereRequest(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = null;
        String errorMsg = "";
        String funcName = "AreYouThere";
        String esbInterfCode = "EapAreYouThere";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            String station_code = jsonParas.getString("station_code");
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //2.获取EAP的IP地址
            String eap_ip = "127.0.0.1";
            String sqlEapIp = "select " +
                    "COALESCE(center_host_1,'') center_host_1," +
                    "COALESCE(center_host_2,'') center_host_2 " +
                    "from sys_core_center LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListEapIp = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlEapIp, false, null, "");
            if (itemListEapIp != null && itemListEapIp.size() > 0) {
                String center_host_1 = itemListEapIp.get(0).get("center_host_1").toString();
                String center_host_2 = itemListEapIp.get(0).get("center_host_2").toString();
                if (center_host_2 == null || center_host_2.equals("")) {
                    eap_ip = center_host_1;
                } else {
                    eap_ip = center_host_2;
                }
            }
            JSONObject jbBody = new JSONObject();
            jbBody.put("equip_id", station_code);
            jbBody.put("server_ip", eap_ip);
            JSONObject postParas = eapFzInterfCommon.CreateSendParas(funcName, jbBody);
            requestParas = postParas.toString();
            JSONObject jsonObjectBack = eapFzInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            boolean success = jsonObjectBack.getBoolean("success");
            String msg = jsonObjectBack.getJSONObject("error").getString("message");
            if (!success) {
                errorMsg = msg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送一次通讯心跳成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //[EAP]设备基本信息上报
    public JSONObject eapEquipmentInformationReport(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = null;
        String errorMsg = "";
        String funcName = "EquipmentInformation";
        String esbInterfCode = "EapEquipmentInformation";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            String sysModel = jsonParas.getString("sysModel");
            String pn_name = jsonParas.getString("pn_name");
            String lot_num = jsonParas.getString("lot_num");
            String order_id = jsonParas.getString("order_id");
            String recipe_name = jsonParas.getString("recipe_name");
            String line_Cleaning = jsonParas.getString("line_Cleaning");
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            JSONObject jbBody = new JSONObject();
            jbBody.put("operation_mode", sysModel);
            jbBody.put("pn_name", pn_name);
            jbBody.put("lot", lot_num);
            jbBody.put("order_id", order_id);
            jbBody.put("recipe_name", recipe_name);
            jbBody.put("line_Cleaning", line_Cleaning);
            JSONObject postParas = eapFzInterfCommon.CreateSendParas(funcName, jbBody);
            requestParas = postParas.toString();
            JSONObject jsonObjectBack = eapFzInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            boolean success = jsonObjectBack.getBoolean("success");
            String msg = jsonObjectBack.getJSONObject("error").getString("message");
            if (!success) {
                errorMsg = msg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送设备基本信息成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //[EAP]设备发生状态改变时调用
    public JSONObject eapEquipmentStatusReportChangeReport(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = null;
        String errorMsg = "";
        String funcName = "EquipmentStatus";
        String esbInterfCode = "EapEquipmentStatus";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            String device_status = jsonParas.getString("device_status");//DOWN(停机-0)、IDLE(待机-1)、RUN(运行-2)、PM(保养-3)
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody = new JSONObject();
            jbBody.put("equipment_status", device_status);
            JSONObject postParas = eapFzInterfCommon.CreateSendParas(funcName, jbBody);
            requestParas = postParas.toString();
            JSONObject jsonObjectBack = eapFzInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            boolean success = jsonObjectBack.getBoolean("success");
            String msg = jsonObjectBack.getJSONObject("error").getString("message");
            if (!success) {
                errorMsg = msg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送一次状态改变成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //[EAP]设备发生报警时调用
    public JSONObject eapAlarmWarningDataReport(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = null;
        String errorMsg = "";
        String funcName = "AlarmWarningDataReport";
        String esbInterfCode = "EapAlarmWarningDataReport";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            String alarm_level = jsonParas.getString("alarm_level");//警报型态(K: 客户关注报警,S: 报警[Alarm] ,M: 警示,L: 警报[Warning],N: 忽略报警)
            String alarm_value = jsonParas.getString("alarm_value");//报警型态:(S: 发生[Alarm/Warning set],R: 解除[Alarm/Warning reset])
            String alarm_code = jsonParas.getString("alarm_code");//警报代码
            String alarm_text = jsonParas.getString("alarm_text");//警报叙述
            String alarm_time = jsonParas.getString("alarm_time");//警报时间
            String alarm_type = "";
            if (alarm_value.equals("1")) alarm_type = "S";
            else alarm_type = "R";
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody = new JSONObject();
            JSONObject trace_data_list = new JSONObject();
            JSONArray trace_data = new JSONArray();
            JSONObject trace_data_item = new JSONObject();
            trace_data_item.put("alarm_type", alarm_level);
            trace_data_item.put("alarm_type", alarm_type);
            trace_data_item.put("alarm_code", alarm_code);
            trace_data_item.put("alarm_text", alarm_text);
            trace_data_item.put("alarm_time", alarm_time);
            trace_data.add(trace_data_item);
            trace_data_list.put("trace_data", trace_data);
            jbBody.put("trace_data_list", trace_data_list);
            JSONObject postParas = eapFzInterfCommon.CreateSendParas(funcName, jbBody);
            requestParas = postParas.toString();
            JSONObject jsonObjectBack = eapFzInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            boolean success = jsonObjectBack.getBoolean("success");
            String msg = jsonObjectBack.getJSONObject("error").getString("message");
            if (!success) {
                errorMsg = msg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送一次设备报警成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //[EAP]制程资料报告
    public JSONObject eapProcessDataReport(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = null;
        String errorMsg = "";
        String funcName = "ProcessDataReport";
        String esbInterfCode = "EapProcessDataReport";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            String report_type = jsonParas.getString("report_type");//报告类别 (0: 单片[by Piece], 1: 批次[by Lot]) 。
            String judge = jsonParas.getString("judge");//判等Product Judge (0: NG, 1:OK，2:开路，3:短路)
            String pn_name = jsonParas.getString("pn_name");
            String panel_list = jsonParas.getString("panel_list");
            String proc_data_list = jsonParas.getString("proc_data_list");
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody = new JSONObject();
            jbBody.put("report_type", report_type);
            jbBody.put("judge", judge);
            jbBody.put("pn_name", pn_name);
            jbBody.put("panel_list", panel_list);
            jbBody.put("proc_data_list", proc_data_list);
            JSONObject postParas = eapFzInterfCommon.CreateSendParas(funcName, jbBody);
            requestParas = postParas.toString();
            JSONObject jsonObjectBack = eapFzInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            boolean success = jsonObjectBack.getBoolean("success");
            String msg = jsonObjectBack.getJSONObject("error").getString("message");
            if (!success) {
                errorMsg = msg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送一次取片事件成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //[EAP]员工上下岗报告
    public JSONObject eapUserLoginLogoutReport(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = null;
        String errorMsg = "";
        String funcName = "UserLoginLogoutReport";
        String esbInterfCode = "EapUserLoginLogoutReport";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            String work_type = jsonParas.getString("work_type");
            String user_no = jsonParas.getString("user_no");
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody = new JSONObject();
            jbBody.put("work_type", work_type);
            jbBody.put("user_no", user_no);
            JSONObject postParas = eapFzInterfCommon.CreateSendParas(funcName, jbBody);
            requestParas = postParas.toString();
            JSONObject jsonObjectBack = eapFzInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            boolean success = jsonObjectBack.getBoolean("success");
            String msg = jsonObjectBack.getJSONObject("error").getString("message");
            if (!success) {
                errorMsg = msg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送一次放片事件成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //[EAP]档案路径和参数信息请求
    public JSONObject eapFilePathAndParameterRequest(JSONObject jsonParas, HttpServletRequest request) throws Exception {
        JSONObject jbResult = null;
        String errorMsg = "";
        String funcName = "FilePathAndParameterRequest";
        String esbInterfCode = "EapFilePathAndParameterRequest";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            String lot_num = jsonParas.getString("lot_num");

            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody = new JSONObject();
            jbBody.put("equip_id", station_code);
            jbBody.put("LotID", lot_num);
            JSONObject postParas = eapFzInterfCommon.CreateSendParas(funcName, jbBody);
            requestParas = postParas.toString();
            JSONObject jsonObjectBack = eapFzInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            boolean success = jsonObjectBack.getBoolean("success");
            String msg = jsonObjectBack.getJSONObject("error").getString("message");
            if (!success) {
                errorMsg = msg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //2.数据解析
            JSONObject result = jsonObjectBack.getJSONObject("result");
            if (result != null) {
                String lot_id = result.getString("lot_id");
                String pn_name = result.getString("pn_name");
                String port_id = result.getString("port_id");
                String panel_count = result.getString("panel_count");
                JSONObject panel_list = result.getJSONObject("panel_list");
                JSONArray panel_id = panel_list.getJSONArray("panel_id");
                String panel_id_list = "";
                for (int i = 0; i < panel_id.size(); i++) {
                    if (i == 0) panel_id_list = panel_id.getString(i);
                    else panel_id_list += "," + panel_id.getString(i);
                }
                String panel_width = "";
                String panel_length = "";
                JSONObject parameter_list = result.getJSONObject("parameter_list");
                if (parameter_list != null) {
                    JSONArray parameter = parameter_list.getJSONArray("parameter");
                    if (parameter != null && parameter.size() > 0) {
                        for (int i = 0; i < parameter.size(); i++) {
                            String parameter_name = parameter.getJSONObject(i).getString("parameter_name");
                            String parameter_value = parameter.getJSONObject(i).getString("parameter_value");
                            if (parameter_name.equals("PanelWidth"))
                                panel_width = parameter_value;
                            if (parameter_name.equals("BotYScale"))
                                panel_length = parameter_value;
                        }
                    }
                }
                //2.1 数据校验

                //2.3 插入数据到数据库
                //2.3.1.获取配方参数
                result.remove("lot_id");
                result.remove("pn_name");
                result.remove("port_id");
                result.remove("panel_count");
                result.remove("panel_list");
                result.remove("parameter_list");
                String item_info=result.toString();

                JSONArray plan_list = new JSONArray();
                JSONObject jbItem = new JSONObject();
                jbItem.put("task_from", "EAP");
                jbItem.put("group_lot_num", lot_id);
                jbItem.put("lot_num", lot_id);
                jbItem.put("lot_index", 1);
                jbItem.put("plan_lot_count", Integer.parseInt(panel_count));
                jbItem.put("target_lot_count", Integer.parseInt(panel_count));
                jbItem.put("port_code", port_id);
                jbItem.put("panel_length", Double.parseDouble(panel_length));
                jbItem.put("panel_width", Double.parseDouble(panel_width));
                jbItem.put("material_code", pn_name);
                jbItem.put("panel_list", panel_id_list);
                jbItem.put("item_info", item_info);
                plan_list.add(jbItem);

                JSONObject paras = new JSONObject();
                paras.put("station_code", station_code);
                paras.put("plan_list", plan_list);
                JSONObject jbPlanResult = JSONObject.parseObject(eapCoreSharePlanController.EapCoreSharePlanSave(paras, request));

                if (jbPlanResult.getInteger("code") != 0) {
                    errorMsg = "保存批次号{"+lot_num+"}任务信息失败："+jbPlanResult.getString("error");
                    throw new Exception(errorMsg);
                }
            }

            //成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送档案路径和参数信息请求成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //[EAP]配方参数变更上报
    public JSONObject eapRecipeParameterRequest(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = null;
        String errorMsg = "";
        String funcName = "RecipeParameterRequest";
        String esbInterfCode = "EapRecipeParameterRequest";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            String recipe_name = jsonParas.getString("recipe_name");
            String lot_num = jsonParas.getString("lot_num");
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody = new JSONObject();
            jbBody.put("eqp_id", station_code);
            jbBody.put("recipe_name", recipe_name);
            jbBody.put("lot_id", lot_num);
            JSONObject postParas = eapFzInterfCommon.CreateSendParas(funcName, jbBody);
            requestParas = postParas.toString();
            JSONObject jsonObjectBack = eapFzInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            boolean success = jsonObjectBack.getBoolean("success");
            String msg = jsonObjectBack.getJSONObject("error").getString("message");
            if (!success) {
                errorMsg = msg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送配方参数变更上报成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

}
