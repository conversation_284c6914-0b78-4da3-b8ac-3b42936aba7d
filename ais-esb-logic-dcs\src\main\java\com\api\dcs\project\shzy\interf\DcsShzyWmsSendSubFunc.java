package com.api.dcs.project.shzy.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 中冶WMS流程接口(Sub)公共方法
 * 1.通知执行分拣
 * 2.通知中控上料完成
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@Service
@Slf4j
public class DcsShzyWmsSendSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsShzyWmsInterfCommon dcsShzyWmsInterfCommon;

    //1.通知执行分拣
    public JSONObject SendFjExecuteTask(String task_num, Integer position_x,Integer position_y,Integer position_z,
                                        String stock_code) throws Exception {
        JSONObject jbResult = new JSONObject();
        Integer code = 0;
        String errorMsg = "";
        String esbInterfCode = "SendFjExecuteTask";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            postParas.put("id", CFuncUtilsSystem.CreateUUID(true));
            postParas.put("timestamp", System.currentTimeMillis());
            JSONObject jbData=new JSONObject();
            jbData.put("partTaskNo",task_num);
            jbData.put("placePosX",position_x);
            jbData.put("placePosY",position_y);
            jbData.put("placePosZ",position_z);
            jbData.put("warehouseLocation",stock_code);
            postParas.put("data",jbData);
            requestParas = postParas.toString();
            jbResult.put("requestParas", requestParas);

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = dcsShzyWmsInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            jbResult.put("responseParas", responseParas);

            //4.判断数据
            if (jsonObjectBack == null || !jsonObjectBack.containsKey("code") || !jsonObjectBack.containsKey("message")) {
                errorMsg = "接受报文为空或者不包含code|message字段";
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            code = jsonObjectBack.getInteger("code");
            String msg = jsonObjectBack.getString("message");
            if (code != 200) {
                jbResult.put("successFlag", false);
                jbResult.put("message", msg);
                return jbResult;
            }

            //成功
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex;
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.通知中控上料完成
    public JSONObject SendZkFeedFinish(String task_num,String status,String statusMsg) throws Exception {
        JSONObject jbResult = new JSONObject();
        Integer code = 0;
        String errorMsg = "";
        String esbInterfCode = "SendZkFeedFinish";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            postParas.put("feedTaskNo",task_num);
            postParas.put("status",status);
            postParas.put("desc",statusMsg);
            requestParas = postParas.toString();
            jbResult.put("requestParas", requestParas);

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = dcsShzyWmsInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            jbResult.put("responseParas", responseParas);

            //4.判断数据
            if (jsonObjectBack == null || !jsonObjectBack.containsKey("code") || !jsonObjectBack.containsKey("message")) {
                errorMsg = "接受报文为空或者不包含code|message字段";
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            code = jsonObjectBack.getInteger("code");
            String msg = jsonObjectBack.getString("message");
            if (code != 200) {
                jbResult.put("successFlag", false);
                jbResult.put("message", msg);
                return jbResult;
            }

            //成功
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex;
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
