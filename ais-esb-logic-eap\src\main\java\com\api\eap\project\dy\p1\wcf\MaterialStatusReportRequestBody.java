
package com.api.eap.project.dy.p1.wcf;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>materialStatusReportRequestBody complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="materialStatusReportRequestBody"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="report_dt" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="keep_reason" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="mtrl_type" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="mtrl_status_id" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="mtrl_lot_id" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="mtrl_infos" type="{http://tempuri.org/}mtrlInfos" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "materialStatusReportRequestBody", propOrder = {
    "reportDt",
    "keepReason",
    "mtrlType",
    "mtrlStatusId",
    "mtrlLotId",
    "mtrlInfos"
})
public class MaterialStatusReportRequestBody {

    @XmlElement(name = "report_dt", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar reportDt;
    @XmlElement(name = "keep_reason")
    protected String keepReason;
    @XmlElement(name = "mtrl_type")
    protected String mtrlType;
    @XmlElement(name = "mtrl_status_id")
    protected String mtrlStatusId;
    @XmlElement(name = "mtrl_lot_id")
    protected String mtrlLotId;
    @XmlElement(name = "mtrl_infos")
    protected MtrlInfos mtrlInfos;

    /**
     * 获取reportDt属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getReportDt() {
        return reportDt;
    }

    /**
     * 设置reportDt属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setReportDt(XMLGregorianCalendar value) {
        this.reportDt = value;
    }

    /**
     * 获取keepReason属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKeepReason() {
        return keepReason;
    }

    /**
     * 设置keepReason属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKeepReason(String value) {
        this.keepReason = value;
    }

    /**
     * 获取mtrlType属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMtrlType() {
        return mtrlType;
    }

    /**
     * 设置mtrlType属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMtrlType(String value) {
        this.mtrlType = value;
    }

    /**
     * 获取mtrlStatusId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMtrlStatusId() {
        return mtrlStatusId;
    }

    /**
     * 设置mtrlStatusId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMtrlStatusId(String value) {
        this.mtrlStatusId = value;
    }

    /**
     * 获取mtrlLotId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMtrlLotId() {
        return mtrlLotId;
    }

    /**
     * 设置mtrlLotId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMtrlLotId(String value) {
        this.mtrlLotId = value;
    }

    /**
     * 获取mtrlInfos属性的值。
     * 
     * @return
     *     possible object is
     *     {@link MtrlInfos }
     *     
     */
    public MtrlInfos getMtrlInfos() {
        return mtrlInfos;
    }

    /**
     * 设置mtrlInfos属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link MtrlInfos }
     *     
     */
    public void setMtrlInfos(MtrlInfos value) {
        this.mtrlInfos = value;
    }

}
