package com.api.mes.core.recipe;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 1.根据订单查询工位配方
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-25
 */
@RestController
@Slf4j
@RequestMapping("/mes/core/recipe")
public class MesCoreRecipeParasController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //根据工序查询对应的
    @RequestMapping(value = "/MesCoreRecipeParasSelect", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeParasSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeParasSelect";
        String selectResult="";
        String errorMsg="";
        String station_code="";
        String serial_num="";
        String recipeDataTable="c_mes_me_recipe_data";
        try{
            long l1=System.currentTimeMillis();
            station_code = jsonParas.getString("station_code");//工位号
            String proceduce_id= jsonParas.getString("proceduce_id");//工序ID
            serial_num=jsonParas.getString("serial_num");//工件编号
            String sqlSelectCraftParas="select tag_id,tag_des,COALESCE(from_station_id,-1) from_station_id," +
                    "COALESCE(from_tag_id,-1) from_tag_id,from_tag_des,COALESCE(default_value,'0') default_value " +
                    "from c_mes_fmod_recipe_craft_paras " +
                    "where proceduce_id="+proceduce_id+" and enable_flag='Y'";
            List<Map<String, Object>> itemListCraftParas=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlSelectCraftParas,
                    false,request,apiRoutePath);
            if(itemListCraftParas==null || itemListCraftParas.size()<=0){
                selectResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
                return selectResult;
            }
            String sqlDistinct="select distinct b.station_code,b.station_id " +
                    "from c_mes_fmod_recipe_craft_paras a inner join sys_fmod_station b " +
                    "on a.from_station_id=b.station_id " +
                    "where a.proceduce_id="+proceduce_id+" and a.enable_flag='Y'";
            List<Map<String, Object>> itemListSt=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlDistinct,
                    false,request,apiRoutePath);
            Map<String, String> mapHistory=new HashMap<>();//存储数据库工件历史数据
            List<String> noOkStationCode=new ArrayList<>();//排除工位集合
            if(itemListSt!=null && itemListSt.size()>0){
                for(int i=0;i<itemListSt.size();i++){
                    String from_station_id2=itemListSt.get(i).get("station_id").toString();
                    List<Map<String, Object>> itemListD=new ArrayList<>();
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(from_station_id2)));
                    queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
                    queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                    queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(recipeDataTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1000).iterator();
                    while (iteratorBigData.hasNext()) {
                        Map<String, Object> mapBigData = new HashMap<>();
                        Document docItemBigData = iteratorBigData.next();
                        long tag_id_BigData=docItemBigData.getLong("tag_id");
                        String tag_value_BigData=docItemBigData.getString("tag_value");
                        if(tag_value_BigData==null || tag_value_BigData.equals("")) tag_value_BigData="-1";
                        mapBigData.put("tag_id",tag_id_BigData);
                        mapBigData.put("tag_value",tag_value_BigData);
                        itemListD.add(mapBigData);
                    }
                    if(iteratorBigData.hasNext()) iteratorBigData.close();
                    if(itemListD!=null && itemListD.size()>0){
                        for(int j=0;j<itemListD.size();j++){
                            String tag_id=itemListD.get(j).get("tag_id").toString();
                            String tag_value=itemListD.get(j).get("tag_value").toString();
                            if(!tag_id.equals("-1")){
                                if(!mapHistory.containsKey(tag_id)) mapHistory.put(tag_id,tag_value);
                            }
                        }
                    }
                    else{
                        noOkStationCode.add(from_station_id2);
                    }
                }
            }

            //组合数据
            List<Map<String, Object>> itemList=new ArrayList<>();
            for(int i=0;i<itemListCraftParas.size();i++){
                String tag_id=itemListCraftParas.get(i).get("tag_id").toString();
                String tag_des=itemListCraftParas.get(i).get("tag_des").toString();
                String from_station_id=itemListCraftParas.get(i).get("from_station_id").toString();
                String from_tag_id=itemListCraftParas.get(i).get("from_tag_id").toString();
                String default_value=itemListCraftParas.get(i).get("default_value").toString();
                Map<String, Object> mapItem;
                if(!noOkStationCode.contains(from_station_id)){
                    if(from_station_id.equals("-1") || from_tag_id.equals("-1")){//不来自工位TAG，使用默认value
                        mapItem=new HashMap<>();
                        mapItem.put("tag_id",tag_id);
                        mapItem.put("tag_des",tag_des);
                        mapItem.put("write_value",default_value);
                        itemList.add(mapItem);
                        continue;
                    }
                    if(mapHistory.containsKey(from_tag_id)){
                        mapItem=new HashMap<>();
                        mapItem.put("tag_id",tag_id);
                        mapItem.put("tag_des",tag_des);
                        mapItem.put("write_value",mapHistory.get(from_tag_id).toString());
                        itemList.add(mapItem);
                        continue;
                    }
                    //如果没有采集，用默认值
                    mapItem=new HashMap<>();
                    mapItem.put("tag_id",tag_id);
                    mapItem.put("tag_des",tag_des);
                    mapItem.put("write_value",default_value);
                    itemList.add(mapItem);
                }
            }
            long l2=System.currentTimeMillis();
            log.info("工位号{"+station_code+"},工件编号{"+serial_num+"},查询工艺参数配方耗时{"+(l2-l1)+"}");
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "工位号{"+station_code+"},工件编号{"+serial_num+"},查询工艺参数配方异常:"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
