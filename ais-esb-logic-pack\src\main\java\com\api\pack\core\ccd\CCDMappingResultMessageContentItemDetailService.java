package com.api.pack.core.ccd;

import com.api.base.IMongoBasicService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class CCDMappingResultMessageContentItemDetailService extends IMongoBasicService<CCDMappingResultMessageContentItemDetail, CCDMappingResultMessageContentItemDetailRepository>
{
    public CCDMappingResultMessageContentItemDetailService(CCDMappingResultMessageContentItemDetailRepository repository)
    {
        super(repository);
    }
}
