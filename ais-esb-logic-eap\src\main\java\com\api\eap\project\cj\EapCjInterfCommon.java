package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 定颖EAP接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapCjInterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private OpCommonFunc opCommonFunc;
    //是否为DEV环境
    private static Boolean isDev=false;

    //返回request_head
    public JSONObject CreateRequestHeader(String function_name, String station_code) throws Exception{
        JSONObject request_head=new JSONObject();
        request_head.put("function_name",function_name);
        request_head.put("eqp_id",station_code);
        request_head.put("time_stamp",CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss.SSS"));
        request_head.put("trx_id",CFuncUtilsSystem.GetOnlySign(""));
        return request_head;
    }

    //解析返回数据，并针对错误直接返回错误,同时返回response_body
    public JSONObject GetResponseBody(JSONObject jsonResult) throws Exception{
        JSONObject response_body=null;
        String errorMsg="";
        try{
            if(!jsonResult.containsKey("response_head")){
                errorMsg="EAP返回数据格式不包含response_head";
                throw new Exception(errorMsg);
            }
            JSONObject response_head=jsonResult.getJSONObject("response_head");
            String result=response_head.getString("result");
            String rtn_code=response_head.getString("rtn_code");
            String rtn_msg=response_head.getString("rtn_msg");
            if(!result.equals("OK")){
                errorMsg=rtn_code+"@"+rtn_msg;
                throw new Exception(errorMsg);
            }
            if(jsonResult.containsKey("response_body")){
                response_body=jsonResult.getJSONObject("response_body");
            }
        }
        catch (Exception ex){
            throw ex;
        }
        return response_body;
    }

    //返回标准返回头
    public JSONObject CreateResponseHead(String function_name,String trx_id,
                                         String result_status,Integer code,String message) throws Exception{
        JSONObject response_head=new JSONObject();
        response_head.put("function_name",function_name);
        response_head.put("trx_id",trx_id);
        response_head.put("result",result_status);
        response_head.put("rtn_code",String.valueOf(code));
        response_head.put("rtn_msg",message);
        return response_head;
    }

    //接受EAP信息后返回数据到EAP格式1
    public String CreateResponseHead01(String function_name,String trx_id,
                                         String result_status,Integer code,String message) throws Exception{
        JSONObject response_head=CreateResponseHead(function_name,trx_id,result_status,code,message);
        JSONObject jbSend=new JSONObject();
        jbSend.put("response_head",response_head);
        String tranResult=jbSend.toString();
        return tranResult;
    }

    //登入或者登出
    public void CheckInOrOut(Boolean checkIn,Long station_id,String station_code,String station_attr,
                             String user_id,String dept_id,String shift_id,String nick_name) throws Exception{
        String meStationUserTable="a_eap_me_station_user";
        String user_code="";
        if(checkIn) user_code=user_id;
        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
        Map<String, Object> mapBigDataRow=new HashMap<>();

        //3.1 先执行自动登出
        String nowDateTime=CFuncUtilsSystem.GetNowDateTime("");
        Query query = new Query();
        query.addCriteria(Criteria.where("station_id").is(station_id));
        query.addCriteria(Criteria.where("checkout_flag").is("N"));
        Update updateBigData = new Update();
        updateBigData.set("checkout_date", nowDateTime);
        updateBigData.set("checkout_flag", "Y");
        mongoTemplate.updateMulti(query, updateBigData, meStationUserTable);

        //3.2 写入到点位
        opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                "Ais","AisStatus","WebUserId","EAP",user_code,false);

        //4.若是登入,则记录登入
        if(checkIn){
            String check_user_id=CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("check_user_id", check_user_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("user_name", user_code);
            mapBigDataRow.put("dept_id", dept_id);
            mapBigDataRow.put("shift_id", shift_id);
            mapBigDataRow.put("nick_name", nick_name);
            mapBigDataRow.put("checkin_date", nowDateTime);
            mapBigDataRow.put("checkout_date", "");
            mapBigDataRow.put("checkout_flag", "N");
            mongoTemplate.insert(mapBigDataRow, meStationUserTable);
        }
    }

    //当定颖为DEV测试环境时,用这个方法
    public JSONObject PostJbBackJb(String url,JSONObject jsonParas) throws Exception{
        if(isDev){
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
            headers.add("Accept", MediaType.APPLICATION_JSON.toString());
            HttpEntity<String> formEntity = new HttpEntity<String>(jsonParas.toString(), headers);
            String jsonResult = restTemplate.postForObject(url , formEntity, String.class);
            jsonResult=jsonResult.replaceAll("\\\\","");
            jsonResult=jsonResult.substring(1,jsonResult.length()-1);
            JSONObject jsonResult2= JSONObject.parseObject(jsonResult);
            return jsonResult2;
        }
        else{
            return cFuncUtilsRest.PostJbBackJb(url,jsonParas);
        }
    }

    public  boolean isLocal() throws Exception {
        String onOffLine = opCommonFunc.ReadCellOneRedisValue("P5AMP001", "Load",
                "Plc", "PlcConfig", "OnOffLine");
        return "0".equals(onOffLine);
    }
    public  boolean isLocal(String station_code,String station_attr) throws Exception {
        String onOffLine = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                "Plc", "PlcConfig", "OnOffLine");
        return "0".equals(onOffLine);
    }
    //校验当前DY版本是否为正确版本
    public Boolean CheckDyVersion(Integer compatVersion) throws Exception{
        Boolean isOk=false;
        try{
            String dyVersion=cFuncDbSqlResolve.GetParameterValue("Dy_Version");
            if(dyVersion!=null && !dyVersion.equals("")){
                Integer dyVersionInt=0;
                dyVersionInt=Integer.parseInt(dyVersion);
                if(dyVersionInt>=compatVersion) isOk=true;
            }
        }
        catch (Exception ex){isOk=false;}
        return isOk;
    }
    public JSONObject GetParamsMapping(){
        String dy_paramsMapping = cFuncDbSqlResolve.GetParameterValue("Dy_ParamsMapping");
        return StringUtils.isEmpty(dy_paramsMapping)?null:JSONObject.parseObject(dy_paramsMapping);
    }
    //校验当前是否是office测试
    public Boolean DyOfficeTest() throws Exception{
        Boolean isOk=false;
        try{
            String dyOfficeTest=cFuncDbSqlResolve.GetParameterValue("Office_Test");
            if(dyOfficeTest!=null && dyOfficeTest.equals("Y")){
                isOk=true;
            }
        }
        catch (Exception ex){isOk=false;}
        return isOk;
    }
}
