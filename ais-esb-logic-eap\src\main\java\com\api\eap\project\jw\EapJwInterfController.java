package com.api.eap.project.jw;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 广合接口处理
 * 1.被动接生产任务
 * 2.上报完板信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-11-07
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/jw/interf")
public class EapJwInterfController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.被动接生产任务
    @RequestMapping(value = "/EapJwRecvTask", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapJwRecvTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="/eap/project/jw/interf/EapJwRecvTask";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapJwRecvTask(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //处理任务接受
    private JSONObject eapJwRecvTask(JSONObject jsonParas,HttpServletRequest request,String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String esbInterfCode="EapJwRecvTask";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            String funcCode=jsonParas.getString("FuncCode");//接口分类代码  FBPortRecvNewLotTask
            String stationCode = jsonParas.getString("DeviceCode");//工位号
            String lotNum=jsonParas.getString("LotNum");//Lot任务号
            String portCode=jsonParas.getString("PortCode");//端口编号
            String materialCode=jsonParas.getString("MaterialCode");//料号
            String version=jsonParas.getString("Version");//版本
            String level=jsonParas.getString("Level");//层数
            String planCount=jsonParas.getString("PlanCount");//计划数量
            String palletType=jsonParas.getString("PalletType");//载具类型(P1载具1类型,P2载具2类型)
            if(palletType==null || palletType.equals("")) palletType="0";
            String panelLength=jsonParas.getString("PanelLength");//板长
            String panelWidth=jsonParas.getString("PanelWidth");//板宽
            String paneThickNess=jsonParas.getString("PaneThickNess");//板厚
            String matchingSeq=jsonParas.getString("MatchingSeq");//匹配序列
            String attribute1="FFFFFFFF";//预留属性1,用于存储 匹配序列,FFFFFFFF表示没有匹配序列
            if(matchingSeq!=null && !matchingSeq.equals("")) attribute1=matchingSeq;
            String attribute2=jsonParas.getString("Attribute2");
            if(attribute2==null) attribute2="";
            String attribute3=jsonParas.getString("Attribute3");
            if(attribute3==null) attribute3="";
            String taskPdbFlag =jsonParas.getString("PdbFlag");// "N";//是否放陪镀板
            if(taskPdbFlag==null) taskPdbFlag="";
            String taskPdbCount = jsonParas.getString("PdbCount");//"0";//陪镀板数量
            if(taskPdbCount==null || taskPdbCount.equals("")) taskPdbCount="0";
            if(!taskPdbFlag.equals("Y")) taskPdbCount="0";

            String PanelIdList = jsonParas.getString("PanelIdList");
            String panel_list="";
            if (PanelIdList != null && !PanelIdList.equals("")){
                JSONArray panelIdListArray = jsonParas.getJSONArray("PanelIdList");
                for (int i = 0; i < panelIdListArray.size(); i++){
                    JSONObject jsonItem = panelIdListArray.getJSONObject(i);
                    String panelBarCode = jsonItem.getString("PanelId");
                    if(i==0) panel_list=panelBarCode;
                    else panel_list+=","+panelBarCode;
                }
            }

            //查询当前工位
            String sqlStationCount="select count(1) " +
                    "from sys_fmod_station where station_code='"+stationCode+"'";
            Integer count=cFuncDbSqlResolve.GetSelectCount(sqlStationCount);
            if(count<=0){
                errorMsg="AIS系统未配置工位{"+stationCode+"}信息";
                responseParas= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //查询端口号是否正确
            String sqlPortCount="select count(1) " +
                    "from sys_fmod_station a inner join a_eap_fmod_station_port b " +
                    "on a.station_id=b.station_id " +
                    "where station_code='"+stationCode+"' and b.port_code='"+portCode+"'";
            count=cFuncDbSqlResolve.GetSelectCount(sqlPortCount);
            if(count<=0){
                errorMsg="AIS系统未配置工位{"+stationCode+"}以及端口{"+portCode+"}匹配关系信息";
                responseParas= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //合成标准
            JSONArray plan_list=new JSONArray();
            String group_lot_num=lotNum;
            JSONObject jbItem=new JSONObject();
            jbItem.put("task_from","EAP");
            jbItem.put("group_lot_num",group_lot_num);
            jbItem.put("lot_num",lotNum);
            jbItem.put("lot_index",1);
            jbItem.put("plan_lot_count",planCount);
            jbItem.put("target_lot_count",planCount);
            jbItem.put("port_code",portCode);
            jbItem.put("material_code",materialCode);
            jbItem.put("lot_level",level);
            jbItem.put("pallet_type",palletType);
            jbItem.put("panel_length",panelLength);
            jbItem.put("panel_width",panelWidth);
            jbItem.put("panel_tickness",paneThickNess);
            jbItem.put("attribute1",attribute1);
            jbItem.put("attribute2",attribute2);
            jbItem.put("attribute3",attribute3);
            jbItem.put("pdb_count",taskPdbCount);
            jbItem.put("pdb_rule",palletType);
            jbItem.put("panel_list",panel_list);
            plan_list.add(jbItem);

            JSONObject postParas=new JSONObject();
            postParas.put("station_code",stationCode);
            postParas.put("plan_list",plan_list);
            String sharePlanUrl="http://127.0.0.1:9090/aisEsbApi/eap/core/share/EapCoreSharePlanSave";
            JSONObject jbPlanResult= cFuncUtilsRest.PostJbBackJb(sharePlanUrl,postParas);
            if(jbPlanResult.getInteger("code")!=0){
                errorMsg=jbPlanResult.getString("error");
                responseParas= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            responseParas=CFuncUtilsLayUiResut.GetStandJson(true,null,lotNum,"",0);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受任务成功");
        }
        catch (Exception ex){
            errorMsg="AIS接受任务发生未知异常:"+ex.getMessage();
            responseParas= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
