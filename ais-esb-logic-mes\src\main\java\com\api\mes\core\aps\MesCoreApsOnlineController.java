package com.api.mes.core.aps;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.mes.core.recipe.MesCoreRecipeBarCodeController;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@RestController
@Slf4j
@RequestMapping("/mes/core/aps")
public class MesCoreApsOnlineController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MesCoreRecipeBarCodeController mesCoreRecipeBarCodeController;

    //分线主物料条码查询
    @RequestMapping(value = "/MesCoreApsMaterialSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsMaterialSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsMaterialSel";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String prod_line_code = jsonParas.getString("prod_line_code");
            String  sqlMaterial = "select COALESCE(exact_barcode,'') exact_barcode " + "from c_mes_me_cr_pdure_bom " + "where main_material_flag='Y' " + "and verify_flag='Y' " + "and prod_line_code='" + prod_line_code + "' " + "and station_code='" + station_code + "' " + "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMaterial = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMaterial, true, request, apiRoutePath);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMaterial, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "分线主物料条码查询异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //生成总成编号
    @RequestMapping(value = "/MesCoreApsZcBarCodeCreate", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsZcBarCodeCreate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsZcBarCodeCreate";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String make_order = jsonParas.getString("make_order");
            String sqlMakeOrder = "select c.mo_id,c.make_order,c.small_model_type," + "COALESCE(c.product_batch,'') product_batch,d.model_code," + "COALESCE(c.mo_custom_code,'') mo_custom_code," + "COALESCE(b.plan_lock_count,0) plan_lock_count," + "COALESCE(c.mo_online_count,0) mo_online_count " + "from sys_fmod_station a inner join sys_fmod_prod_line b " + "on a.prod_line_id=b.prod_line_id inner join c_mes_aps_plan_mo c " + "on b.prod_line_id=c.prod_line_id inner join c_mes_fmod_small_model d " + "on c.prod_line_id=d.prod_line_id and c.small_model_type=d.small_model_type " + "where a.station_code='" + station_code + "' " + "and c.make_order='" + make_order + "' " +
                    //"and c.mo_status='CARRY_ON' " +
                    "and c.enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMakeOrder = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMakeOrder, false, request, apiRoutePath);
            //查询打印数量
            SimpleDateFormat df = new SimpleDateFormat("yyyyMM");
            Date nowTime = new Date();
            String product_num = df.format(nowTime);
            product_num = product_num.substring(2);
            String sqlOnlineCount = "select count(1) from c_a_online_temp " + "where station_code='" + station_code + "' and product_num='" + product_num + "'";
            Integer onlineCount = cFuncDbSqlResolve.GetSelectCount(sqlOnlineCount);
            onlineCount = onlineCount + 1;
            if (itemListMakeOrder == null || itemListMakeOrder.size() <= 0) {
                errorMsg = "工位{" + station_code + "}上线未设定订单信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                log.error(errorMsg);
                return transResult;
            }
            String mo_id = itemListMakeOrder.get(0).get("mo_id").toString();
            //String make_order=itemListMakeOrder.get(0).get("make_order").toString();//订单号
            String small_model_type = itemListMakeOrder.get(0).get("small_model_type").toString();//产品型号
            String product_batch = itemListMakeOrder.get(0).get("product_batch").toString();//订单批号(打刻相关)
            String mo_custom_code = itemListMakeOrder.get(0).get("mo_custom_code").toString();//订单来源客户代码(手工输入条码)
            String model_code = itemListMakeOrder.get(0).get("model_code").toString();
            Integer plan_lock_count = Integer.parseInt(itemListMakeOrder.get(0).get("plan_lock_count").toString());
            onlineCount = onlineCount + plan_lock_count;
            String serial_num = "Q" + product_num + "4" + String.format("%04d", onlineCount) + "Z";
            if (mo_custom_code != null && !mo_custom_code.equals("")) {
                if (mo_custom_code.length() >= 11) {
                    String mo_online_count = itemListMakeOrder.get(0).get("mo_online_count").toString();//实际上线数量
                    if (mo_online_count == null) mo_online_count = "0";
                    Integer mo_online_count_int = Integer.parseInt(mo_online_count);
                    String header = mo_custom_code.substring(0, mo_custom_code.length() - 5);
                    String increasStr = mo_custom_code.substring(mo_custom_code.length() - 5, mo_custom_code.length() - 1);
                    Integer increasNum = Integer.parseInt(increasStr);
                    increasNum += mo_online_count_int;
                    increasStr = String.format("%04d", increasNum);
                    serial_num = header + increasStr + mo_custom_code.substring(mo_custom_code.length() - 1);
                } else {
                    serial_num = mo_custom_code;
                }
            }
            String dk_num = product_batch + serial_num;
            //插入上线信息
            long onlineId = cFuncDbSqlResolve.GetIncreaseID("c_a_online_temp_id_seq", false);
            String sqlInsertOnline = "insert into c_a_online_temp " + "(creation_date,online_id,make_order,small_model_type,station_code," + "serial_num,dk_num,product_num,model_code) values " + "('" + CFuncUtilsSystem.GetNowDateTime("") + "'," + onlineId + ",'" + make_order + "','" + small_model_type + "'," + "'" + station_code + "','" + serial_num + "','" + dk_num + "','" + product_num + "'," + model_code + ")";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertOnline, false, request, apiRoutePath);
            /*
            //更新上线数量
            String sqlUpdateOnline="update c_mes_aps_plan_mo set " +
                    "mo_status='CARRY_ON', " +
                    "mo_online_count=mo_online_count+1 " +
                    "where mo_id="+mo_id+"";
            cFuncDbSqlExecute.ExecUpdateSql(station_code,sqlUpdateOnline,false,request,apiRoutePath);*/
            String result = make_order + "," + small_model_type + "," + model_code + "," + serial_num + "," + dk_num;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "生成总成编号失败" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //生成总成编号（比亚迪条形码）和工件编号（比亚迪打刻码，后续工位扫描唯一码） 生成规则：如 BYD DHT31-2146010E K2300001
    @RequestMapping(value = "/MesCoreApsZcBarCodeCreate02", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsZcBarCodeCreate02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsZcBarCodeCreate02";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String barCode = jsonParas.getString("barCode");
            String make_order = jsonParas.getString("make_order");
            String sqlMakeOrder = "select c.mo_id,c.make_order,c.small_model_type," + "COALESCE(c.product_batch,'') product_batch,d.model_code," + "COALESCE(c.mo_custom_code,'') mo_custom_code," + "COALESCE(b.plan_lock_count,0) plan_lock_count," + "COALESCE(c.mo_online_count,0) mo_online_count " + "from sys_fmod_station a inner join sys_fmod_prod_line b " + "on a.prod_line_id=b.prod_line_id inner join c_mes_aps_plan_mo c " + "on b.prod_line_id=c.prod_line_id inner join c_mes_fmod_small_model d " + "on c.prod_line_id=d.prod_line_id and c.small_model_type=d.small_model_type " + "where a.station_code='" + station_code + "' " + "and c.make_order='" + make_order + "' " +
                    //"and c.mo_status='CARRY_ON' " +
                    "and c.enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMakeOrder = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMakeOrder, false, request, apiRoutePath);
            if (itemListMakeOrder == null || itemListMakeOrder.size() <= 0) {
                errorMsg = "工位{" + station_code + "}上线未设定订单信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                log.error(errorMsg);
                return transResult;
            }
            String mo_id = itemListMakeOrder.get(0).get("mo_id").toString();
            //String make_order=itemListMakeOrder.get(0).get("make_order").toString();//订单号
            String small_model_type = itemListMakeOrder.get(0).get("small_model_type").toString();//产品型号
            String product_batch = itemListMakeOrder.get(0).get("product_batch").toString();//订单批号(打刻相关)
            String mo_custom_code = itemListMakeOrder.get(0).get("mo_custom_code").toString();//订单来源客户代码(手工输入条码)
            String model_code = itemListMakeOrder.get(0).get("model_code").toString();
            Integer plan_lock_count = Integer.parseInt(itemListMakeOrder.get(0).get("plan_lock_count").toString());
            //根据product_num查询打印数量
            SimpleDateFormat df = new SimpleDateFormat("yyyy");
            Date nowTime = new Date();
            String product_num = df.format(nowTime);
            String serialNumberPrefix = product_num.substring(2);
            product_num = product_batch + serialNumberPrefix;

            String sqlOnlineCount = "select count(1) from c_a_online_temp " + "where station_code='" + station_code + "' and product_num='" + product_num + "'";
            Integer onlineCount = cFuncDbSqlResolve.GetSelectCount(sqlOnlineCount);
            onlineCount = onlineCount + 1;
            onlineCount = onlineCount + plan_lock_count;
            String dk_num = product_num + String.format("%05d", onlineCount);
            String serial_num = barCode;//工件编号
            String resp = mesCoreRecipeBarCodeController.MesCoreRecipeBarCodeCreate(jsonParas, request);
            JSONObject obj = JSONObject.parseObject(resp);
            String zc_barcode = "";//总成编号=打印条形码
            if (obj != null && obj.containsKey("result")) zc_barcode = obj.getString("result");
            //插入上线信息
            long onlineId = cFuncDbSqlResolve.GetIncreaseID("c_a_online_temp_id_seq", false);
            String sqlInsertOnline = "insert into c_a_online_temp " + "(creation_date,online_id,make_order,small_model_type,station_code," + "serial_num,dk_num,product_num,model_code) values " + "('" + CFuncUtilsSystem.GetNowDateTime("") + "'," + onlineId + ",'" + make_order + "','" + small_model_type + "'," + "'" + station_code + "','" + serial_num + "','" + dk_num + "','" + product_num + "'," + model_code + ")";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertOnline, false, request, apiRoutePath);
            //更新上线数量
             /*String sqlUpdateOnline="update c_mes_aps_plan_mo set " +
                    "mo_status='CARRY_ON', " +
                    "mo_online_count=mo_online_count+1 " +
                    "where mo_id="+mo_id+"";
            cFuncDbSqlExecute.ExecUpdateSql(station_code,sqlUpdateOnline,false,request,apiRoutePath);*/
            String result = make_order + "," + small_model_type + "," + model_code + "," + serial_num + "," + zc_barcode + "," + product_batch + "," + serialNumberPrefix + String.format("%05d", onlineCount);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "生成总成编号失败" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //生成比亚迪打刻码
    @RequestMapping(value = "/MesCoreApsDkNumCreate", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsDkNumCreate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsDkNumCreate";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String barCode = jsonParas.getString("barCode");
            String sqlMakeOrder = "select c.mo_id,c.make_order,c.small_model_type,COALESCE(c.product_batch,'') product_batch," + "d.model_code,COALESCE(c.mo_custom_code,'') mo_custom_code,COALESCE(b.plan_lock_count,0) plan_lock_count," + "COALESCE(c.mo_online_count,0) mo_online_count from sys_fmod_station a inner join sys_fmod_prod_line b " + "on a.prod_line_id=b.prod_line_id inner join c_mes_aps_plan_mo c on b.prod_line_id=c.prod_line_id " + "inner join c_mes_fmod_small_model d on c.prod_line_id=d.prod_line_id " + "and c.small_model_type=d.small_model_type where a.station_code='" + station_code + "' " + "and c.enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMakeOrder = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMakeOrder, false, request, apiRoutePath);
            if (itemListMakeOrder == null || itemListMakeOrder.size() <= 0) {
                errorMsg = "工位{" + station_code + "}上线未设定订单信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                log.error(errorMsg);
                return transResult;
            }
            String mo_id = itemListMakeOrder.get(0).get("mo_id").toString();
            String make_order = itemListMakeOrder.get(0).get("make_order").toString();//订单号
            String small_model_type = itemListMakeOrder.get(0).get("small_model_type").toString();//产品型号
            String product_batch = itemListMakeOrder.get(0).get("product_batch").toString();//订单批号(打刻相关)
            String mo_custom_code = itemListMakeOrder.get(0).get("mo_custom_code").toString();//订单来源客户代码(手工输入条码)
            String model_code = itemListMakeOrder.get(0).get("model_code").toString();
            Integer plan_lock_count = Integer.parseInt(itemListMakeOrder.get(0).get("plan_lock_count").toString());
            //根据product_num查询打印数量
            SimpleDateFormat df = new SimpleDateFormat("yyyy");
            Date nowTime = new Date();
            String product_num = df.format(nowTime);
            String serialNumberPrefix = product_num.substring(2);
            product_num = product_batch + serialNumberPrefix;

            String sqlOnlineCount = "select count(1) from c_a_online_temp " + "where station_code='" + station_code + "' and product_num='" + product_num + "'";
            Integer onlineCount = cFuncDbSqlResolve.GetSelectCount(sqlOnlineCount);
            onlineCount = onlineCount + 1;
            onlineCount = onlineCount + plan_lock_count;
            String dk_num = product_num + String.format("%05d", onlineCount);
            String serial_num = barCode;//工件编号
            String zc_barcode = "";//总成编号=打印条形码
            //插入上线信息
            long onlineId = cFuncDbSqlResolve.GetIncreaseID("c_a_online_temp_id_seq", false);
            String sqlInsertOnline = "insert into c_a_online_temp " + "(creation_date,online_id,make_order,small_model_type,station_code," + "serial_num,dk_num,product_num,model_code) values " + "('" + CFuncUtilsSystem.GetNowDateTime("") + "'," + onlineId + ",'" + make_order + "','" + small_model_type + "'," + "'" + station_code + "','" + serial_num + "','" + dk_num + "','" + product_num + "'," + model_code + ")";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertOnline, false, request, apiRoutePath);

            String result = make_order + "," + small_model_type + "," + model_code + "," + serial_num + "," + zc_barcode + "," + product_batch + "," + serialNumberPrefix + String.format("%05d", onlineCount);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "生成总成编号失败" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //生成双环项目箱体码
    @RequestMapping(value = "/MesCoreApsShPackCodeCreate", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreApsShPackCodeCreate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/MesCoreApsShPackCodeCreate";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String make_order = jsonParas.getString("make_order");
            String sqlMakeOrder = "select c.mo_id,c.make_order,c.small_model_type," +
                    "COALESCE(c.product_batch,'') product_batch,d.model_code," +
                    "COALESCE(c.mo_custom_code,'') mo_custom_code," +
                    "COALESCE(b.plan_lock_count,0) plan_lock_count," +
                    "COALESCE(c.mo_online_count,0) mo_online_count " +
                    "from sys_fmod_station a inner join sys_fmod_prod_line b " +
                    "on a.prod_line_id=b.prod_line_id inner join c_mes_aps_plan_mo c " +
                    "on b.prod_line_id=c.prod_line_id inner join c_mes_fmod_small_model d " +
                    "on c.prod_line_id=d.prod_line_id and c.small_model_type=d.small_model_type " +
                    "where a.station_code='" + station_code + "' " + "and c.make_order='" + make_order + "' " +
                    "and c.enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMakeOrder = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMakeOrder, false, request, apiRoutePath);
            if (itemListMakeOrder == null || itemListMakeOrder.size() <= 0) {
                errorMsg = "工位{" + station_code + "}上线未设定订单信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                log.error(errorMsg);
                return transResult;
            }
            String mo_id = itemListMakeOrder.get(0).get("mo_id").toString();
            //String make_order=itemListMakeOrder.get(0).get("make_order").toString();//订单号
            String small_model_type = itemListMakeOrder.get(0).get("small_model_type").toString();//产品型号
            String product_batch = itemListMakeOrder.get(0).get("product_batch").toString();//订单批号(打刻相关)
            String mo_custom_code = itemListMakeOrder.get(0).get("mo_custom_code").toString();//订单来源客户代码(手工输入条码)
            String model_code = itemListMakeOrder.get(0).get("model_code").toString();
            //根据product_num查询打印数量
            SimpleDateFormat df = new SimpleDateFormat("yyMMdd");
            Date nowTime = new Date();
            String product_num = df.format(nowTime);
            String sqlOnlineCount = "select count(1) from c_a_online_temp " + "where station_code='" + station_code + "' and product_num='" + product_num + "'";
            Integer onlineCount = cFuncDbSqlResolve.GetSelectCount(sqlOnlineCount);
            onlineCount = onlineCount + 1;
            String pack_code = mo_custom_code + "-" + product_num + String.format("%05d", onlineCount);
            //插入上线信息
            long onlineId = cFuncDbSqlResolve.GetIncreaseID("c_a_online_temp_id_seq", false);
            String sqlInsertOnline = "insert into c_a_online_temp " +
                    "(creation_date,online_id,make_order,small_model_type,station_code," +
                    "serial_num,dk_num,product_num,model_code) values " +
                    "('" + CFuncUtilsSystem.GetNowDateTime("") + "'," + onlineId + ",'" +
                    make_order + "','" + small_model_type + "'," + "'" + station_code + "','" +
                    pack_code + "','" + pack_code + "','" + product_num + "'," + model_code + ")";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertOnline, false, request, apiRoutePath);
            String result = pack_code;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "生成总成编号失败" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
