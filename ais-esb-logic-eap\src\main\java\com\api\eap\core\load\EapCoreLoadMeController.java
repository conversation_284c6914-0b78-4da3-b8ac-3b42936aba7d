package com.api.eap.core.load;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 放板机工艺制造处理逻辑
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/load")
public class EapCoreLoadMeController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //判断是否需要切批弹窗
    @RequestMapping(value = "/EapCoreLoadMeChangeRecipeDlg", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadMeChangeRecipeDlg(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadMeChangeRecipeDlg";
        String transResult = "";
        String errorMsg = "";
        String allow_dlg = "Y";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, allow_dlg, "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否需要切批弹窗异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //离线板件信息存储
    @RequestMapping(value = "/EapCoreLoadMeOffPanelSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadMeOffPanelSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadMeOffPanelSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String user_name = "";
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");
            String dummy_flag = jsonParas.getString("dummy_flag");
            String manual_judge_code = jsonParas.getString("manual_judge_code");
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";

            long station_id_long = Long.parseLong(station_id);
            String port_code = port_index;
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) {
                port_code = lstPort.get(0).get("port_code").toString();
            }
            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //存储
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", "");
            mapBigDataRow.put("task_from", "AIS");
            mapBigDataRow.put("group_lot_num", "");
            mapBigDataRow.put("lot_num", "");
            mapBigDataRow.put("lot_short_num", "");
            mapBigDataRow.put("lot_index", 0);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", "");
            mapBigDataRow.put("pallet_num", "");
            mapBigDataRow.put("pallet_type", "");
            mapBigDataRow.put("lot_level", "");
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", 0d);
            mapBigDataRow.put("panel_width", 0d);
            mapBigDataRow.put("panel_tickness", 0d);
            mapBigDataRow.put("panel_index", 0);
            mapBigDataRow.put("panel_status", "OK");
            mapBigDataRow.put("panel_ng_code", 0);
            mapBigDataRow.put("panel_ng_msg", "");
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", "Y");
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, station_flow_id, "", 0);
        } catch (Exception ex) {
            errorMsg = "离线板件信息存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //离线板件信息存储
    //山东清河，根据任务编码类型找到离线任务Dummy开头
    @RequestMapping(value = "/EapCoreLoadMeOffPanelSave2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadMeOffPanelSave2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadMeOffPanelSave2";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String user_name = "";
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");
            String dummy_flag = jsonParas.getString("dummy_flag");
            String manual_judge_code = jsonParas.getString("manual_judge_code");
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";

            long station_id_long = Long.parseLong(station_id);
            String port_code = port_index;
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) {
                port_code = lstPort.get(0).get("port_code").toString();
            }
            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //1.获取Dummy任务
            Document docPlan = null;
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.addCriteria(Criteria.where("lot_num").regex("Dummy"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                docPlan = iteratorBigData.next();
                iteratorBigData.close();
            }
            Integer finish_count = 0;
            if (docPlan != null) {
                finish_count = docPlan.getInteger("finish_count");
                finish_count++;
                Update updateBigData = new Update();
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_count);
                if (finish_count == 1) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                    updateBigData.set("lot_status", "WORK");
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }
            //存储
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", docPlan == null ? "" : docPlan.getString("plan_id"));
            mapBigDataRow.put("task_from", "AIS");
            mapBigDataRow.put("group_lot_num", docPlan == null ? "" : docPlan.getString("group_lot_num"));
            mapBigDataRow.put("lot_num", docPlan == null ? "" : docPlan.getString("lot_num"));
            mapBigDataRow.put("lot_short_num", docPlan == null ? "" : docPlan.getString("lot_short_num"));
            mapBigDataRow.put("lot_index", docPlan == null ? 0 : docPlan.getInteger("lot_index"));
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", "");
            mapBigDataRow.put("pallet_num", "");
            mapBigDataRow.put("pallet_type", "");
            mapBigDataRow.put("lot_level", "");
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", 0d);
            mapBigDataRow.put("panel_width", 0d);
            mapBigDataRow.put("panel_tickness", 0d);
            mapBigDataRow.put("panel_index", 0);
            mapBigDataRow.put("panel_status", "OK");
            mapBigDataRow.put("panel_ng_code", 0);
            mapBigDataRow.put("panel_ng_msg", "");
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", "Y");
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, station_flow_id, "", 0);
        } catch (Exception ex) {
            errorMsg = "离线板件信息存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //离线板件信息存储
    //无锡健鼎 小板件
    @RequestMapping(value = "/EapCoreLoadMeOffPanelSave3", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadMeOffPanelSave3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadMeOffPanelSave3";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String user_name = "";
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String port_index = jsonParas.getString("port_index");
            if (port_index == null || port_index.equals("")) port_index = "0";
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");
            String dummy_flag = jsonParas.getString("dummy_flag");
            String manual_judge_code = jsonParas.getString("manual_judge_code");
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_count = 0;//计划首检数量
            Integer inspect_finish_count = 0;//实际完成首检数量
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";


            String port_code = port_index;
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) {
                port_code = lstPort.get(0).get("port_code").toString();
            }
            //查询该工单下全部的plan_id
            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            Map<String, Object> planData = new HashMap<String, Object>();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明没有WORK状态下的任务，需要按时间顺序将第一条任务改成WORK状态,然后再查询
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_count = docItemBigData.getInteger("inspect_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            panel_index = finish_count + 1;
            String lot_status = "WORK";
            String lot_status2 = lot_status;
            String panel_status="OK";

            String Panel_Count = cFuncDbSqlResolve.GetParameterValue("Panel_Count");
            if (Panel_Count == null || Panel_Count.equals("")) Panel_Count = "4";
            int panel_barcode_count = 0;
            String[] panel_barcode_list = panel_barcode.split(",");
            for (int i = 0; i < panel_barcode_list.length; i++) {
                String panel = panel_barcode_list[i];
                if (!panel.equals("") && !panel.equals("FFFFFFFF") && !panel.equals("NoRead")) {
                    panel_barcode_count++;
                }
            }
            int panel_ng_code = 0;
            if (!String.valueOf(panel_barcode_count).equals(Panel_Count)) {
                panel_ng_code = 1;
                panel_status="NG";
            }

            //1.获取当前用户信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //存储
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> itemList = new ArrayList<>();
            for (int i = 0; i < panel_barcode_list.length; i++) {
                String panel_barcode_i = panel_barcode_list[i];
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("station_flow_id", station_flow_id);
                mapBigDataRow.put("station_id", station_id_long);
                mapBigDataRow.put("plan_id", "");
                mapBigDataRow.put("task_from", "AIS");
                mapBigDataRow.put("group_lot_num", "");
                mapBigDataRow.put("lot_num", "");
                mapBigDataRow.put("lot_short_num", "");
                mapBigDataRow.put("lot_index", 0);
                mapBigDataRow.put("port_code", port_code);
                mapBigDataRow.put("material_code", "");
                mapBigDataRow.put("pallet_num", "");
                mapBigDataRow.put("pallet_type", "");
                mapBigDataRow.put("lot_level", "");
                mapBigDataRow.put("fp_index", 0);
                mapBigDataRow.put("panel_barcode", panel_barcode_i);
                mapBigDataRow.put("panel_length", 0d);
                mapBigDataRow.put("panel_width", 0d);
                mapBigDataRow.put("panel_tickness", 0d);
                mapBigDataRow.put("panel_index", 0);
                mapBigDataRow.put("panel_status", panel_status);
                mapBigDataRow.put("panel_ng_code", panel_ng_code);
                mapBigDataRow.put("panel_ng_msg", "");
                mapBigDataRow.put("inspect_flag", "N");
                mapBigDataRow.put("dummy_flag", dummy_flag);
                mapBigDataRow.put("manual_judge_code", manual_judge_code);
                mapBigDataRow.put("panel_flag", "Y");
                mapBigDataRow.put("user_name", user_name);
                mapBigDataRow.put("eap_flag", "N");
                mapBigDataRow.put("tray_barcode", tray_barcode);
                mapBigDataRow.put("face_code", 0);
                mapBigDataRow.put("offline_flag", "N");
                itemList.add(mapBigDataRow);
            }
            mongoTemplate.insert(itemList, meStationFlowTable);
            String result = "," + lot_num + "," + pallet_num + "," + panel_index + ",," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "离线板件信息存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    // 四合离线板件信息存储
    @RequestMapping(value = "/EapCoreLoadMeOffPanelSave4", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadMeOffPanelSave4(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadMeOffPanelSave4";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String user_name = "";
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");
            String dummy_flag = jsonParas.getString("dummy_flag");
            String manual_judge_code = jsonParas.getString("manual_judge_code");
            boolean  panel_barcode_flag = jsonParas.getBoolean("panel_barcode_flag");
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            String panel_status = "NG"; //默认NG
            int panel_ng_code = 1; //默认混批
            String panel_ng_msg = "混批"; //默认混批
            if(panel_barcode_flag){
                panel_status = "OK";
                panel_ng_code = 0;
                panel_ng_msg = "";
            }
            long station_id_long = Long.parseLong(station_id);
            String port_code = port_index;
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) {
                port_code = lstPort.get(0).get("port_code").toString();
            }
            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //存储
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", "");
            mapBigDataRow.put("task_from", "AIS");
            mapBigDataRow.put("group_lot_num", "");
            mapBigDataRow.put("lot_num", "");
            mapBigDataRow.put("lot_short_num", "");
            mapBigDataRow.put("lot_index", 0);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", "");
            mapBigDataRow.put("pallet_num", "");
            mapBigDataRow.put("pallet_type", "");
            mapBigDataRow.put("lot_level", "");
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", 0d);
            mapBigDataRow.put("panel_width", 0d);
            mapBigDataRow.put("panel_tickness", 0d);
            mapBigDataRow.put("panel_index", 0);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", "Y");
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, station_flow_id, "", 0);
        } catch (Exception ex) {
            errorMsg = "离线板件信息存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //保存AOI Tray队列信息
    @RequestMapping(value = "/EapCoreLoadMeSaveAoiTrayQueue", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadMeSaveAoiTrayQueue(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadMeSaveAoiTrayQueue";
        String transResult = "";
        String errorMsg = "";
        String meTrayQueueTable = "a_eap_me_aoi_tray_queue";
        try {
            String station_id = jsonParas.getString("station_id");
            String face_code = jsonParas.getString("face_code");//面次,1:A面；2:B面
            String tray_barcode = jsonParas.getString("tray_barcode");
            long station_id_long = Long.parseLong(station_id);
            //存储
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String tray_queue_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("tray_queue_id", tray_queue_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mongoTemplate.insert(mapBigDataRow, meTrayQueueTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, tray_queue_id, "", 0);
        } catch (Exception ex) {
            errorMsg = "保存AOI-Tray队列信息异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //获取一个AOI队列信息
    @RequestMapping(value = "/EapCoreLoadMeGetAoiTrayQueue", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadMeGetAoiTrayQueue(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadMeGetAoiTrayQueue";
        String transResult = "";
        String errorMsg = "";
        String meTrayQueueTable = "a_eap_me_aoi_tray_queue";
        String tray_barcode = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String face_code = jsonParas.getString("face_code");//面次,1:A面；2:B面
            //获取第一片
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("face_code").is(face_code));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meTrayQueueTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            String tray_queue_id = "";
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                tray_queue_id = docItemBigData.getString("tray_queue_id");
                tray_barcode = docItemBigData.getString("tray_barcode");
                iteratorBigData.close();
            }
            if (!tray_queue_id.equals("")) {
                //删除
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("tray_queue_id").is(tray_queue_id));
                mongoTemplate.remove(queryBigData, meTrayQueueTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, tray_barcode, "", 0);
        } catch (Exception ex) {
            errorMsg = "获取AOI-Tray队列信息异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
