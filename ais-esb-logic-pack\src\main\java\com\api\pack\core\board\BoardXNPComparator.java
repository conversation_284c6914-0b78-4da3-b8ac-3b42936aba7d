package com.api.pack.core.board;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.util.ObjectUtils;

import java.util.*;

/**
 * <p>
 * 板件X数/位比较器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@Slf4j
public class BoardXNPComparator extends BoardDefaultComparator implements BoardComparator
{
    public BoardCompareResult compare(SETBoard board, BoardCompareRule rule, MessageSource messageSource)
    {
        try
        {
            // [{"key":"1","num":0,"pos":""},{"key":"2","num":-1,"pos":"-"},{"key":"11","num":-1,"pos":"1"},{"key":"12","num":-1,"pos":"2"},{"key":"13","num":-1,"pos":"3"},{"key":"14","num":-1,"pos":"4"}]
            if (rule.getTargetValue() instanceof String && !ObjectUtils.isEmpty(rule.getTargetValue()))
            {
                JSONArray ruleValue = JSON.parseArray((String) rule.getTargetValue());
                List<PCSBoard> pcsBoards = board.getChildrenBoards();
                List<String> xoutPositions = new LinkedList<>();
                for (PCSBoard pcsBoard : pcsBoards)
                {
                    if (pcsBoard.isXout())
                    {
                        // SET板件检测光学点值是否为NG
                        BoardCompareResult setBoardCheckOpticalPointValueResult = pcsBoard.checkValue(this, BoardConst.BOARD_PROPERTY_MARK, BoardConst.NG, messageSource, Boolean.TRUE);
                        boolean isNG = setBoardCheckOpticalPointValueResult != null && setBoardCheckOpticalPointValueResult.isOK();
                        if (isNG) // 光学点检测为NG
                        {
                            xoutPositions.add(String.valueOf(pcsBoard.getBoardIndex()));
                        }
                        else // 光学点检测不为NG
                        {
                            String err = messageSource.getMessage(BoardConst.BOARD_COMPARE_MESSAGES_PACK_NOT_MATCH_ALL, new Object[]{pcsBoard.getBoardName(), rule.getName()}, Locale.getDefault());
                            return new BoardCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
                        }
                    }
                }
                xoutPositions.sort(java.util.Comparator.comparingInt(Integer::parseInt));
                int xoutNum = xoutPositions.size();
                String xoutPos = String.join(",", xoutPositions);
                int ngPos = 2;
                for (int i = 0; i < ruleValue.size(); i++)
                {
                    JSONObject entry = ruleValue.getJSONObject(i);
                    int k = entry.getInteger("key");
                    int n = entry.getInteger("num");
                    String p = entry.getString("pos");
                    // NG位赋值
                    if (n == -1 && "-".equals(p))
                    {
                        ngPos = k;
                    }
                    // X数判定
                    if (n >= 0 && xoutNum == n && ObjectUtils.isEmpty(p))
                    {
                        return new BoardCompareResult(BoardConst.STATUS_OK, BoardConst.CODE_OK, xoutPos, k);
                    }
                    // X位判定
                    List<String> xoutPositionsS = Arrays.asList(xoutPos.split(","));
                    xoutPositionsS.sort(java.util.Comparator.comparingInt(Integer::parseInt));
                    xoutPos = String.join(",", xoutPositionsS);
                    if (n == -1 && !ObjectUtils.isEmpty(p) && p.equals(xoutPos))
                    {
                        return new BoardCompareResult(BoardConst.STATUS_OK, BoardConst.CODE_OK, xoutPos, k);
                    }
                }
                if (!xoutPositions.isEmpty())
                {
                    return new BoardCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, getPackNotConfiguredMessage(rule.getName(), messageSource), ngPos);
                }
            }
            else
            {
                return new BoardCompareResult(BoardConst.STATUS_OK, BoardConst.CODE_OK, BoardConst.BLANK);
            }
        }
        catch (Exception ex)
        {
            log.error(ex.getMessage(), ex);
            return new BoardCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, ex.getMessage());
        }
        return null;
    }
}
