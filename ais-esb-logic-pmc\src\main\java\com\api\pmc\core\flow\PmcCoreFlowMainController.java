package com.api.pmc.core.flow;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 主流程
 * 1.根据过站ID修改离开时间
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/flow")
public class PmcCoreFlowMainController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private PmcCoreAviBase pmcCoreAviBase;
    @Autowired
    private PmcCoreStationCrosBase pmcCoreStationCrosBase;

    //1.根据过站ID修改离开时间
    @Transactional
    @RequestMapping(value = "/PmcCoreFlowMainUpd", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowMainUpd(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowMainUpd";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String station_flow_id = jsonParas.getString("station_flow_id");//过站ID
            String show_only_flag = jsonParas.getString("show_only_flag");//是否工位独立显示(Y过站离开是否清空实时工件信息)
            String leave_date = jsonParas.getString("leave_date");//离开时间
            String avi_station = jsonParas.getString("avi_station");//AVI推算工位集
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //1、获取过站信息
            String arrive_date = "";//到达时间
            String sqlStationFlow = "select COALESCE(arrive_date,'') arrive_date " +
                    "from d_pmc_me_station_flow " +
                    "where station_flow_id=" + station_flow_id;
            List<Map<String, Object>> itemListStationFlow = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationFlow, false, request, apiRoutePath);
            if (itemListStationFlow != null && itemListStationFlow.size() > 0) {
                arrive_date = itemListStationFlow.get(0).get("arrive_date").toString();
                //2、计算消费时间
                String cost_time = "0";
                if (arrive_date != null && !arrive_date.equals("")) {
                    SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    long arriveTime = df.parse(arrive_date).getTime();
                    long leaveTime = df.parse(leave_date).getTime();
                    long diffTime = (leaveTime - arriveTime) / 1000;
                    cost_time = String.valueOf(diffTime);
                }
                //3、修改过站离开时间
                String updStation = "update d_pmc_me_station_flow set " +
                        "last_updated_by='" + station_code + "'," +
                        "last_update_date='" + nowDateTime + "'," +
                        "leave_date='" + leave_date + "', " +
                        "cost_time=" + cost_time +
                        " where station_flow_id=" + station_flow_id;
                cFuncDbSqlExecute.ExecUpdateSql(station_code, updStation, true, request, apiRoutePath);
            }
            //4、AVI推算
            //推算循环
            String station_status = "";//工位状态(1正常、2缓存区、3空板过点、4拉入、5拉出)
            String empty_ban_flag = "";//是否为空板
            String serial_num = "";//工件编号或RFID(DMS+行 不满20位前面补0)
            String pallet_num = "";//托盘号
            String staff_id = "";//操作者(默认工位号)
            String make_order = "";//订单号
            String dms = "";//DMS号
            String item_project = "";//行项目
            String vin = "";//vin号
            String small_model_type = "";//型号
            String main_material_code = "";//物料编号
            String material_color = "";//颜色
            String material_size = "";//尺寸
            String shaft_proc_num = "";//拧紧程序号
            String quality_sign = "";//总合格标志
            String set_sign = "";//拉入或者拉出过站点（SET_IN、SET_OUT、NORMAL）
            //过站状态
            String check_status = "";//过站校验状态(OK/NG)
            String check_code = "";//过站校验代码
            String check_msg = "";//过站校验描述
            String engine_num = "";//发动机
            String driver_way = "";//驱动形式
            String status_way = "";//状态计算方式(1过点实际/2推算/3采集)
            if (avi_station != null && !avi_station.equals("")) {
                String[] lst = avi_station.split(",", -1);
                if (lst != null && lst.length > 0) {
                    Integer lstLen = lst.length - 1;//数组数量
                    String pre_station_code = "";//前工位
                    String cur_station_code = "";//当前工位
                    for (int i = lstLen; i > 0; i--) {
                        pre_station_code = lst[i - 1];
                        cur_station_code = lst[i];
                        //2、获取前工位信息
                        String sqlStationStatus = "select COALESCE(station_status,'') station_status," +
                                "COALESCE(station_des,'') station_des," +
                                "COALESCE(empty_ban_flag,'') empty_ban_flag," +
                                "COALESCE(serial_num,'') serial_num," +
                                "COALESCE(pallet_num,'') pallet_num," +
                                "COALESCE(staff_id,'') staff_id," +
                                "COALESCE(make_order,'') make_order," +
                                "COALESCE(dms,'') dms," +
                                "COALESCE(item_project,'') item_project," +
                                "COALESCE(vin,'') vin," +
                                "COALESCE(small_model_type,'') small_model_type," +
                                "COALESCE(main_material_code,'') main_material_code," +
                                "COALESCE(material_color,'') material_color," +
                                "COALESCE(material_size,'') material_size," +
                                "COALESCE(shaft_proc_num,'') shaft_proc_num," +
                                "COALESCE(quality_sign,'') quality_sign," +
                                "COALESCE(set_sign,'') set_sign," +
                                "COALESCE(check_status,'') check_status," +
                                "COALESCE(check_code,0) check_code," +
                                "COALESCE(check_msg,'') check_msg," +
                                "COALESCE(engine_num,'') engine_num," +
                                "COALESCE(driver_way,'') driver_way," +
                                "COALESCE(status_way,'') status_way " +
                                "from d_pmc_me_station_status " +
                                "where station_code='" + pre_station_code + "' " +
                                "and work_center_code='" + work_center_code + "' ";
                        List<Map<String, Object>> itemListStationStatus = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationStatus, false, request, apiRoutePath);
                        if (itemListStationStatus != null && itemListStationStatus.size() > 0) {
                            station_status = itemListStationStatus.get(0).get("station_status").toString();
                            empty_ban_flag = itemListStationStatus.get(0).get("empty_ban_flag").toString();
                            serial_num = itemListStationStatus.get(0).get("serial_num").toString();
                            pallet_num = itemListStationStatus.get(0).get("pallet_num").toString();
                            staff_id = itemListStationStatus.get(0).get("staff_id").toString();
                            make_order = itemListStationStatus.get(0).get("make_order").toString();
                            dms = itemListStationStatus.get(0).get("dms").toString();
                            item_project = itemListStationStatus.get(0).get("item_project").toString();
                            vin = itemListStationStatus.get(0).get("vin").toString();
                            small_model_type = itemListStationStatus.get(0).get("small_model_type").toString();
                            main_material_code = itemListStationStatus.get(0).get("main_material_code").toString();
                            material_color = itemListStationStatus.get(0).get("material_color").toString();
                            material_size = itemListStationStatus.get(0).get("material_size").toString();
                            shaft_proc_num = itemListStationStatus.get(0).get("shaft_proc_num").toString();
                            quality_sign = itemListStationStatus.get(0).get("quality_sign").toString();
                            set_sign = itemListStationStatus.get(0).get("set_sign").toString();
                            check_status = itemListStationStatus.get(0).get("check_status").toString();
                            check_code = itemListStationStatus.get(0).get("check_code").toString();
                            check_msg = itemListStationStatus.get(0).get("check_msg").toString();
                            engine_num = itemListStationStatus.get(0).get("engine_num").toString();
                            driver_way = itemListStationStatus.get(0).get("driver_way").toString();
                            status_way = itemListStationStatus.get(0).get("status_way").toString();
                            String station_des = itemListStationStatus.get(0).get("station_des").toString();
                            //3、修改订单信息
                            pmcCoreAviBase.AviIntefTask(request, apiRoutePath,
                                    work_center_code, cur_station_code,
                                    station_status, empty_ban_flag, serial_num, pallet_num, staff_id,
                                    make_order, dms, item_project, vin, small_model_type,
                                    main_material_code, material_color, material_size, shaft_proc_num,
                                    quality_sign, set_sign, check_status, check_code, check_msg, engine_num, driver_way, status_way);
                            //4、插入过站信息
                            try {
                                String sqlStation = "select COALESCE(a.attribute2,'') flow_taglist, " +
                                        "COALESCE(a.attribute5,'') up_interf_flag " +
                                        "from sys_fmod_station a " +
                                        "where a.enable_flag='Y' " +
                                        "and a.station_code='" + cur_station_code + "' " +
                                        "LIMIT 1 OFFSET 0";
                                List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, null, apiRoutePath);
                                if (itemListStation == null || itemListStation.size() <= 0) {
                                    errorMsg = "工位号{" + cur_station_code + "},系统中不存在";
                                    selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                    return selectResult;
                                }
                                String flow_taglist = itemListStation.get(0).get("flow_taglist").toString();
                                String up_interf_flag = itemListStation.get(0).get("up_interf_flag").toString();
                                pmcCoreStationCrosBase.StationCrosIntefTask(request, apiRoutePath, staff_id, "4",
                                        cur_station_code, prod_line_code, work_center_code,
                                        make_order, dms, item_project, serial_num, pallet_num,
                                        vin, main_material_code, small_model_type, material_color,
                                        material_size, shaft_proc_num, engine_num, driver_way,
                                        station_status, arrive_date,
                                        check_status, check_code, check_msg,
                                        station_des, "", "",
                                        "", up_interf_flag, flow_taglist);
                            } catch (Exception ex) {
                            }
                        }
                    }
                }
            }
            //5、过站显示(清空 当前订单)
            if (show_only_flag.equals("Y")) {
                station_status = "0";
                empty_ban_flag = "N";
                serial_num = "";
                pallet_num = "";
                staff_id = "";
                make_order = "";
                dms = "";
                item_project = "";
                vin = "";
                small_model_type = "";
                main_material_code = "";
                material_color = "";
                material_size = "";
                shaft_proc_num = "";
                quality_sign = "";
                set_sign = "";
                check_status = "";
                check_code = "-1";
                check_msg = "";
                engine_num = "";
                driver_way = "";
                status_way = "";
                //3、修改订单信息
                pmcCoreAviBase.AviIntefTask(request, apiRoutePath,
                        work_center_code, station_code,
                        station_status, empty_ban_flag, serial_num, pallet_num, staff_id,
                        make_order, dms, item_project, vin, small_model_type,
                        main_material_code, material_color, material_size, shaft_proc_num,
                        quality_sign, set_sign, check_status, check_code, check_msg, engine_num, driver_way, status_way);
            }
            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("station_flow_id", station_flow_id);
            itemListMo.add(map);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "根据过站ID修改离开时间异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }


}
