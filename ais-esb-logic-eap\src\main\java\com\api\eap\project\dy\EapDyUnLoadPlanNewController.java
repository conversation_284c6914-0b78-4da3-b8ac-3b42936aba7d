package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 定颖收扳机生产逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-26
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/dy/unload")
public class EapDyUnLoadPlanNewController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private EapDySendFlowFunc eapDySendFlowFunc;

    //收板机Panel校验
    @RequestMapping(value = "/EapDyUnLoadPlanPanelCheckAndSaveNew", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyUnLoadPlanPanelCheckAndSaveNew(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/unload/EapDyUnLoadPlanPanelCheckAndSaveNew";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断
            String group_lot_num=jsonParas.getString("group_lot_num");//当前端口作业母批号
            String group_lot_num2=jsonParas.getString("group_lot_num2");//另外一个端口作业母批号
            String DyUnLoadZcjFlag=cFuncDbSqlResolve.GetParameterValue("Dy_UnLoadZCJ");//暂存是否在收扳机

            //针对条码进行校验
            Integer face_code2_int=0;
            String[] lstPanelBarCode=panel_barcode.split(",",-1);
            if(lstPanelBarCode!=null && lstPanelBarCode.length==2){
                panel_barcode=lstPanelBarCode[0];
                String face_code2=lstPanelBarCode[1];
                if(face_code2.equals("Z")) face_code2_int=1;
                else if(face_code2.equals("F")) face_code2_int=2;
            }
            //若出现多个集合,则直接判断为NoRead
            if(lstPanelBarCode!=null && lstPanelBarCode.length>2){
                panel_barcode="NoRead";
            }

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer eap_face_code=0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";
            String virtu_pallet_num="";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                target_lot_count = docItemBigData.getInteger("target_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                eap_face_code=docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                virtu_pallet_num=pallet_num;
                if(docItemBigData.containsKey("virtu_pallet_num")) virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                iteratorBigData.close();
            }
            face_code=face_code2_int;
            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");
            mapBigDataRow.put("virtu_pallet_num",virtu_pallet_num);

            String SbDoubleFlag = cFuncDbSqlResolve.GetParameterValue("SB_DOUBLE_FLAG");
            String isUseNextLot = "N";//是否使用下一个任务

            //3.当未找到任务时直接记录
            if (plan_id.equals("")) {
                panel_ng_code=8;
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 11;
                }
                panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                mapBigDataRow.put("panel_ng_code", panel_ng_code);
                mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
                mapBigDataRow.put("offline_flag", "Y");
                if(!"Y".equals(DyUnLoadZcjFlag)){
                    mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                }
                port_index="0";
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num + "," +
                        port_index+ "," +face_code+ ","+panel_status;
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //4.查询List
            List<String> lstPlanId = new ArrayList<>();
            List<String> lot_short_num_list=new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData2.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
                String lot_short_num_1=docItemBigData.getString("lot_short_num");
                if(!lot_short_num_1.equals("") && !lot_short_num_list.contains(lot_short_num_1)) lot_short_num_list.add(lot_short_num_1);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //5.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                panel_ng_code = 11;
                panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                mapBigDataRow.put("panel_ng_code", panel_ng_code);
                mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
                port_index="0";
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num + "," +
                        port_index+ "," +face_code+ ","+panel_status;
                if(!"Y".equals(DyUnLoadZcjFlag)){
                    mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                }
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //6.是否为AIS判断则需要判断混板逻辑
            //6.1 有panel模式
            if (panel_model_value.equals("1")) {
                if (ng_manual_pass_value.equals("1")) {
                    panel_ng_code=4;
                } else {
                    if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                        if(panel_barcode.equals("NoRead")) panel_ng_code=2;
                        else panel_ng_code=7;
                    } else {
                        panel_ng_code = CheckPanelByItemInfo(station_id,plan_id,lstPlanId, panel_barcode, lot_short_num, item_info,
                                strip_flag, ng_auto_pass_value,face_code,eap_face_code,lot_short_num_list);
                        if (panel_ng_code==1 && SbDoubleFlag.equals("Y")) {
                            if(group_lot_num2!=null && !group_lot_num2.equals("")){
                                //先查询lst
                                lstPlanId=new ArrayList<>();
                                lot_short_num_list=new ArrayList<>();
                                Query queryBigData3 = new Query();
                                queryBigData3.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigData3.addCriteria(Criteria.where("group_lot_num").is(group_lot_num2));
                                MongoCursor<Document> iteratorBigData3 = mongoTemplate.getCollection(apsPlanTable).find(queryBigData3.getQueryObject()).
                                        sort(queryBigData3.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                                while (iteratorBigData3.hasNext()) {
                                    Document docItemBigData3 = iteratorBigData3.next();
                                    lstPlanId.add(docItemBigData3.getString("plan_id"));
                                    String lot_short_num_1=docItemBigData3.getString("lot_short_num");
                                    if(!lot_short_num_1.equals("") && !lot_short_num_list.contains(lot_short_num_1)) lot_short_num_list.add(lot_short_num_1);
                                }
                                if (iteratorBigData3.hasNext()) iteratorBigData3.close();
                                //判断
                                lot_status_list = new String[]{"PLAN", "WORK"};
                                queryBigData = new Query();
                                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num2));
                                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                if (iteratorBigData.hasNext()) {
                                    Document docItemBigData = iteratorBigData.next();
                                    String select_plan_id2 = docItemBigData.get("plan_id").toString();
                                    String select_group_lot_num2 = docItemBigData.get("group_lot_num").toString();
                                    String select_lot_short_num2 = docItemBigData.get("lot_short_num").toString();
                                    String select_item_info2 = docItemBigData.get("item_info").toString();
                                    Integer select_finish_count2 = docItemBigData.getInteger("finish_count");
                                    Integer select_finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                                    Integer select_finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                                    Integer eap_face_code2=docItemBigData.getInteger("face_code");
                                    Integer panel_ng_code2 = CheckPanelByItemInfo(station_id,select_plan_id2,lstPlanId, panel_barcode,
                                            select_lot_short_num2, select_item_info2, strip_flag, ng_auto_pass_value,
                                            face_code,eap_face_code2,lot_short_num_list);
                                    if (panel_ng_code2 != 1) {
                                        panel_ng_code = panel_ng_code2;
                                        plan_id = select_plan_id2;
                                        group_lot_num = select_group_lot_num2;
                                        lot_short_num = select_lot_short_num2;
                                        item_info = select_item_info2;
                                        finish_count = select_finish_count2;
                                        finish_ok_count = select_finish_ok_count2;
                                        finish_ng_count = select_finish_ng_count2;
                                        panel_index = finish_count + 1;
                                        //其他数据
                                        plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                                        task_from=docItemBigData.getString("task_from");
                                        lot_num = docItemBigData.getString("lot_num");
                                        lot_index = docItemBigData.getInteger("lot_index");
                                        material_code = docItemBigData.getString("material_code");
                                        pallet_num = docItemBigData.getString("pallet_num");
                                        pallet_type = docItemBigData.getString("pallet_type");
                                        lot_level = docItemBigData.getString("lot_level");
                                        panel_length = docItemBigData.getDouble("panel_length");
                                        panel_width = docItemBigData.getDouble("panel_width");
                                        panel_tickness = docItemBigData.getDouble("panel_tickness");
                                        virtu_pallet_num=pallet_num;
                                        if(docItemBigData.containsKey("virtu_pallet_num")) virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                                        isUseNextLot = "Y";
                                    }
                                    iteratorBigData.close();
                                }
                            }
                        }
                    }
                }
            } else {
                panel_barcode = "";
                mapBigDataRow.put("panel_barcode", panel_barcode);
            }
            //判断最终结果
            if(panel_ng_code>0){
                if(panel_ng_code==4) panel_status = "NG_PASS";
                else{
                    if(ng_auto_pass_value.equals("1")) panel_status = "NG_PASS";
                    else panel_status = "NG";
                }
            }
            else panel_status = "OK";
            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);

            if (isUseNextLot.equals("Y")) {
                if (port_code.equals("01")) port_code = "02";
                else port_code = "01";
                if (port_index.equals("1")) port_index = "2";
                else port_index = "1";
            }
            else port_index="0";
            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("panel_index", panel_index);
            //其他数据
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("virtu_pallet_num", virtu_pallet_num);

            //判断是否可以插入到数据库
            Boolean isCanUpdIns=true;
            if("Y".equals(DyUnLoadZcjFlag)){
                if(!panel_status.equals("OK") && !panel_status.equals("NG_PASS")){
                    isCanUpdIns=false;
                }
            }

            //插入到过站数据
            if(isCanUpdIns){
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            }

            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            Update updateBigData = new Update();
            String lot_status = "WORK";
            String lot_status2 = "WORK";

            if (isUseNextLot.equals("N")) {
                if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
                lot_status2 = lot_status;
                if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
                updateBigData.set("lot_status", lot_status);
                if (finish_count == 1) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                if (lot_status.equals("FINISH")) {
                    updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
            }
            else{
                if(finish_ok_count >= plan_lot_count){
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                updateBigData.set("lot_status", lot_status);
                if (finish_count == 1) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
            }

            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);

            //更新数据
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            if(isCanUpdIns){
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //更新EAP状态
            if(finish_count==1){
                String writeTags="UnLoadEap/EapStatus/CurrentLotNum,UnLoadEap/EapStatus/CurrentLotCount";
                String tagValues=lot_num+"&"+plan_lot_count;
                cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, writeTags, tagValues, false);
            }

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num + "," +
                    port_index+ "," +face_code+ "," +panel_status;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Exception:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //条码校验
    private Integer CheckPanelByItemInfo(String station_id,String plan_id,List<String>lstPlanId,String panel_barcode,
                                        String lot_short_num, String item_info, String strip_flag, String ng_auto_pass_value,
                                        Integer face_code,Integer eap_face_code,List<String> lot_short_num_list2) throws Exception {
        Integer panel_ng_code = 0;
        String apsPlanDTable = "a_eap_aps_plan_d";
        String mePanelQueueTable="a_eap_me_panel_queue";
        try {
            //判断是否为Strip
            if (strip_flag.equals("Y")) {
                if (item_info != null && !item_info.equals("")) {
                    JSONArray jaStripList = JSONArray.parseArray(item_info);
                    if (jaStripList != null && jaStripList.size() > 0) {
                        panel_ng_code = 1;
                        for (int m = 0; m < jaStripList.size(); m++) {
                            JSONObject jbStripItem = jaStripList.getJSONObject(m);
                            String strip_barcode = jbStripItem.getString("strip_barcode");
                            String strip_level = jbStripItem.getString("strip_level");
                            String strip_status = jbStripItem.getString("strip_status");
                            if (strip_barcode.equals(panel_barcode)) {
                                if (strip_status.equals("OK")) {
                                    panel_ng_code = 0;
                                }
                                break;
                            }
                        }
                    }
                }
            } else {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                if (pnListCount > 0) {
                    Query queryBigDataD = new Query();
                    queryBigDataD.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                    long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                    if (okCount <= 0) {
                        panel_ng_code = 1;
                    }
                }
                if(panel_ng_code==0) {
                    //需要判断简码LIST信息,新增板件判断逻辑,240315 by ZhouJun
                    List<String> lot_short_num_list = lot_short_num_list2;
                    if (item_info != null && !item_info.equals("")) {
                        JSONArray item_info_list = JSONArray.parseArray(item_info);
                        if (item_info_list != null && item_info_list.size() > 0) {
                            int lotShortNumListCount = 0;
                            for (int i = 0; i < item_info_list.size(); i++) {
                                JSONObject jbItem = item_info_list.getJSONObject(i);
                                String item_id = jbItem.getString("item_id");
                                String item_value = jbItem.getString("item_value");
                                if (item_id.equals("S026") && item_value != null && !item_value.equals("") && !item_value.contains("NA")) {
                                    try {
                                        lotShortNumListCount = Integer.parseInt(item_value);
                                    } catch (Exception intError) {
                                        lotShortNumListCount = 0;
                                    }
                                    break;
                                }
                            }
                            if (lotShortNumListCount > 0) {
                                List<String> lotShortColList = new ArrayList<>();
                                for (int i = 1; i <= lotShortNumListCount; i++) {
                                    String SName = "S" + String.format("%03d", 26 + i);
                                    lotShortColList.add(SName);
                                }
                                for (int i = 0; i < item_info_list.size(); i++) {
                                    JSONObject jbItem = item_info_list.getJSONObject(i);
                                    String item_id = jbItem.getString("item_id");
                                    String item_value = jbItem.getString("item_value");
                                    if (lotShortColList.contains(item_id) &&
                                            item_value != null && !item_value.equals("")) {
                                        if (!lot_short_num_list.contains(item_value))
                                            lot_short_num_list.add(item_value);
                                    }
                                }
                            }
                        }
                    }
                    String lot_short_num2=lot_short_num;
                    if(eapDyInterfCommon.CheckDyVersion(3)){
                        if(lot_short_num2.length()>=12){
                            lot_short_num2=lot_short_num2.substring(0,12);
                        }
                    }
                    if (!lot_short_num2.equals("") && !lot_short_num_list.contains(lot_short_num2))
                        lot_short_num_list.add(lot_short_num2);
                    //新增逻辑,简码判断
                    if (lot_short_num_list.size() > 0 && pnListCount<=0) {
                        panel_ng_code = 1;
                        for (int i = 0; i < lot_short_num_list.size(); i++) {
                            String lot_short_num_item = lot_short_num_list.get(i);
                            if (panel_barcode.startsWith(lot_short_num_item)) {
                                panel_ng_code = 0;
                                break;
                            }
                        }
                    }

                    //判断AOI下发数据是否OK
                    String SbPanelFromUp=cFuncDbSqlResolve.GetParameterValue("SbPanelFromUp");
                    String up_panel_status="OK";
                    if(SbPanelFromUp.equals("Y") && panel_ng_code==0){
                        up_panel_status="NG";
                        queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                        queryBigData.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                        queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                        MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(mePanelQueueTable).find(queryBigData.getQueryObject()).
                                sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                        if (iteratorBigData.hasNext()){
                            Document docItemBigData = iteratorBigData.next();
                            up_panel_status=docItemBigData.getString("panel_status");
                            iteratorBigData.close();
                        }
                    }
                    if(up_panel_status.equals("NG")){
                        panel_ng_code=9;
                    }
                    //判断面次信息
                    if(eap_face_code>0 && panel_ng_code==0){
                        if(face_code!=eap_face_code){
                            String eap_face_code_str="正面";
                            if(eap_face_code!=1) eap_face_code_str="反面";
                            String face_code_str="正面";
                            if(face_code!=1) face_code_str="反面";
                            panel_ng_code=6;
                            //增加弹窗报错
                            opCommonFunc.SaveCimMessage(Long.parseLong(station_id),"1","-6","AIS",
                                    "检测到pnl号{"+panel_barcode+"}识别面次为{"+face_code_str+"}与工单要求面次{"+eap_face_code_str+"}不一致,判定为NG",5);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            panel_ng_code=99;
            log.error(ex.getMessage());
        }
        return panel_ng_code;
    }

    //收板机Panel校验(小板)
    @RequestMapping(value = "/EapDyUnLoadPlanSmallPanelCheckAndSaveNew", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyUnLoadPlanSmallPanelCheckAndSaveNew(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/unload/EapDyUnLoadPlanSmallPanelCheckAndSaveNew";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断
            String group_lot_num=jsonParas.getString("group_lot_num");//当前端口作业母批号
            String group_lot_num2=jsonParas.getString("group_lot_num2");//另外一个端口作业母批号

            //针对条码进行校验
            Integer face_code2_int=0;

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";
            Integer old_finish_count=0;
            Integer old_finish_ok_count=0;

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                target_lot_count = docItemBigData.getInteger("target_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                old_finish_count=finish_count;
                old_finish_ok_count=finish_ok_count;
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }
            face_code=face_code2_int;

            //循环遍历
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstDocuments=new ArrayList<>();
            String station_flow_id_list="";
            String panel_barcode_list="";
            String[] lstPanelBarCode=panel_barcode.split("@",-1);
            for(String panelBarCodeItem : lstPanelBarCode){
                if(panelBarCodeItem!=null && !panelBarCodeItem.equals("")){
                    panel_index=finish_count+1;
                    finish_count++;
                    if(panel_status.equals("NG"))  finish_ng_count++;
                    else finish_ok_count++;
                    String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
                    if(station_flow_id_list.equals("")){
                        station_flow_id_list=station_flow_id;
                        panel_barcode_list=panelBarCodeItem;
                    }
                    else{
                        station_flow_id_list+="@"+station_flow_id;
                        panel_barcode_list+="@"+panelBarCodeItem;
                    }
                    String offline_flag="N";
                    if (plan_id.equals("")) offline_flag="Y";
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("item_date", item_date);
                    mapBigDataRow.put("item_date_val", item_date_val);
                    mapBigDataRow.put("station_flow_id", station_flow_id);
                    mapBigDataRow.put("station_id", station_id_long);
                    mapBigDataRow.put("plan_id", plan_id);
                    mapBigDataRow.put("task_from", task_from);
                    mapBigDataRow.put("group_lot_num", group_lot_num);
                    mapBigDataRow.put("lot_num", lot_num);
                    mapBigDataRow.put("lot_short_num", lot_short_num);
                    mapBigDataRow.put("lot_index", lot_index);
                    mapBigDataRow.put("port_code", port_code);
                    mapBigDataRow.put("material_code", material_code);
                    mapBigDataRow.put("pallet_num", pallet_num);
                    mapBigDataRow.put("pallet_type", pallet_type);
                    mapBigDataRow.put("lot_level", lot_level);
                    mapBigDataRow.put("fp_index", fp_index);
                    mapBigDataRow.put("panel_barcode", panel_barcode);
                    mapBigDataRow.put("panel_length", panel_length);
                    mapBigDataRow.put("panel_width", panel_width);
                    mapBigDataRow.put("panel_tickness", panel_tickness);
                    mapBigDataRow.put("panel_index", panel_index);
                    mapBigDataRow.put("panel_status", panel_status);
                    mapBigDataRow.put("panel_ng_code", panel_ng_code);
                    mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
                    mapBigDataRow.put("inspect_flag", "N");
                    mapBigDataRow.put("dummy_flag", dummy_flag);
                    mapBigDataRow.put("manual_judge_code", manual_judge_code);
                    mapBigDataRow.put("panel_flag", panel_flag);
                    mapBigDataRow.put("user_name", user_name);
                    mapBigDataRow.put("eap_flag", eap_flag);
                    mapBigDataRow.put("tray_barcode", tray_barcode);
                    mapBigDataRow.put("face_code", face_code);
                    mapBigDataRow.put("offline_flag", offline_flag);
                    lstDocuments.add(mapBigDataRow);
                }
            }
            //插入到过站数据
            mongoTemplate.insert(lstDocuments,meStationFlowTable);
            port_index="0";
            //更新完工数量
            Update updateBigData = new Update();
            String lot_status = "WORK";
            String lot_status2 = "WORK";
            if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
            lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (old_finish_count<=0) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            //更新数据
            if (!plan_id.equals("")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }
            //更新EAP状态
            if(old_finish_count<=0){
                String station_code="";//工位号
                String sqlStation="select station_code " +
                        "from sys_fmod_station " +
                        "where station_id="+station_id+"";
                List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStation,false,request,apiRoutePath);
                if(itemListStation!=null && itemListStation.size()>0){
                    station_code=itemListStation.get(0).get("station_code").toString();
                }
                String writeTags="UnLoadEap/EapStatus/CurrentLotNum,UnLoadEap/EapStatus/CurrentLotCount";
                String tagValues=lot_num+"&"+plan_lot_count;
                cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, writeTags, tagValues, false);
            }

            //返回数据
            result = station_flow_id_list + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode_list + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num + "," +
                    port_index+ "," +face_code;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "收板机小板Panel校验异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //API接口提供给小板视觉系统(KLW)
    @RequestMapping(value = "/EapDyUnLoadSmallPanelCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyUnLoadSmallPanelCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/unload/EapDyUnLoadSmallPanelCheck";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String meLaserTable="a_eap_me_laser";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        Integer back_result_code=0;
        try{
            String panel_barcode=jsonParas.getString("panel_barcode");
            String yk_result=jsonParas.getString("yk_result");
            if(panel_barcode==null) panel_barcode="";
            if(yk_result==null) yk_result="";
            //1.查询当前所属工位
            String sqlStation = "select " +
                    "station_id,station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_attr='UnLoad' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                    false, null, "");
            if(itemListStation==null || itemListStation.size()<=0){
                errorMsg="未配置收扳机工位信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_code=itemListStation.get(0).get("station_code").toString();
            String station_attr=itemListStation.get(0).get("station_attr").toString();
            //2.查询所需必备参数
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OnOffLineTag="";
            String SysModelTag="";
            String PlcWorkPortIndexTag="";
            String WorkGroupId1Tag="";
            String WorkGroupId2Tag="";
            String NgPanelPassFlagTag="";
            String PanelModelTag="";
            String OneCarMultyLotFlagTag="";
            String StationFlowInfoTag="";
            //值
            String port_code = "";
            String onoff_value="";
            String sys_model="";
            String port_index="";
            String group_lot_num="";
            String group_lot_num1="";
            String group_lot_num2="";
            String ng_auto_pass_value="";
            String panel_model_value="";
            String onecar_multybatch_value="";
            if (aisMonitorModel.equals("AIS-PC")) {
                OnOffLineTag=station_attr+"Plc/PlcConfig/OnOffLine";
                SysModelTag=station_attr+"Plc/PlcConfig/SysModel";
                PlcWorkPortIndexTag=station_attr+"Plc/PlcStatus/PlcWorkPortIndex";
                WorkGroupId1Tag=station_attr+"Ais/AisStatus/WorkGroupId1";
                WorkGroupId2Tag=station_attr+"Ais/AisStatus/WorkGroupId2";
                NgPanelPassFlagTag = station_attr+"Ais/AisConfig/NgPanelPassFlag";
                PanelModelTag=station_attr+"Plc/PlcConfig/PanelModel";
                OneCarMultyLotFlagTag=station_attr+"Ais/AisConfig/OneCarMultyLotFlag";
                StationFlowInfoTag = station_attr+"Ais/AisStatus/StationFlowInfo";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OnOffLineTag=station_attr+"Plc_"+station_code+"/PlcConfig/OnOffLine";
                SysModelTag=station_attr+"Plc_"+station_code+"/PlcConfig/SysModel";
                PlcWorkPortIndexTag=station_attr+"Plc_"+station_code+"/PlcStatus/PlcWorkPortIndex";
                WorkGroupId1Tag=station_attr+"Ais_"+station_code+"/AisStatus/WorkGroupId1";
                WorkGroupId2Tag=station_attr+"Ais_"+station_code+"/AisStatus/WorkGroupId2";
                NgPanelPassFlagTag = station_attr+"Ais_"+station_code+"/AisConfig/NgPanelPassFlag";
                PanelModelTag=station_attr+"Plc_"+station_code+"/PlcConfig/PanelModel";
                OneCarMultyLotFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/OneCarMultyLotFlag";
                StationFlowInfoTag = station_attr+"Ais_"+station_code+"/AisStatus/StationFlowInfo";
            }
            else{
                errorMsg="AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String sumReadTags=OnOffLineTag+","+SysModelTag+","+PlcWorkPortIndexTag+","+
                    WorkGroupId1Tag+","+WorkGroupId2Tag+","+NgPanelPassFlagTag+","+PanelModelTag+","+
                    OneCarMultyLotFlagTag;
            JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,sumReadTags);
            if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                for(int i=0;i<jsonArrayTag.size();i++){
                    JSONObject jbItem=jsonArrayTag.getJSONObject(i);
                    String tag_key=jbItem.getString("tag_key");
                    String tag_value=jbItem.getString("tag_value");
                    if(tag_key.equals(OnOffLineTag)) onoff_value=tag_value;
                    if(tag_key.equals(SysModelTag)) sys_model=tag_value;
                    if(tag_key.equals(PlcWorkPortIndexTag)) port_index=tag_value;
                    if(tag_key.equals(WorkGroupId1Tag)) group_lot_num1=tag_value;
                    if(tag_key.equals(WorkGroupId2Tag)) group_lot_num2=tag_value;
                    if(tag_key.equals(NgPanelPassFlagTag)) ng_auto_pass_value=tag_value;
                    if(tag_key.equals(PanelModelTag)) panel_model_value=tag_value;
                    if(tag_key.equals(OneCarMultyLotFlagTag)) onecar_multybatch_value=tag_value;
                }
            }
            if(port_index==null || port_index.equals("") || port_index.equals("0")) port_index="1";
            port_code="01";
            if("2".equals(port_index)){
                group_lot_num=group_lot_num2;
                port_code="02";
            }
            else group_lot_num=group_lot_num1;
            //相关参数
            String panel_flag="N";
            Integer panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name="";
            String tray_barcode="";
            Integer face_code2_int=1;//默认正面

            //3.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //4.获取当前计划中或者执行中的子任务
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer eap_face_code=0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";
            String virtu_pallet_num="";
            String[] lot_status_list = new String[]{"PLAN", "WORK"};

            //必须是在线状态下才查询工单
            if("1".equals(onoff_value)){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    plan_id = docItemBigData.getString("plan_id");
                    task_from = docItemBigData.getString("task_from");
                    lot_num = docItemBigData.getString("lot_num");
                    lot_short_num = docItemBigData.getString("lot_short_num");
                    lot_index = docItemBigData.getInteger("lot_index");
                    port_code = docItemBigData.getString("port_code");
                    material_code = docItemBigData.getString("material_code");
                    pallet_num = docItemBigData.getString("pallet_num");
                    pallet_type = docItemBigData.getString("pallet_type");
                    lot_level = docItemBigData.getString("lot_level");
                    panel_length = docItemBigData.getDouble("panel_length");
                    panel_width = docItemBigData.getDouble("panel_width");
                    panel_tickness = docItemBigData.getDouble("panel_tickness");
                    plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                    target_lot_count = docItemBigData.getInteger("target_lot_count");
                    finish_count = docItemBigData.getInteger("finish_count");
                    finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                    finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                    face_code = docItemBigData.getInteger("face_code");
                    eap_face_code=docItemBigData.getInteger("face_code");
                    pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                    inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                    item_info = docItemBigData.getString("item_info");
                    old_plan_status = docItemBigData.getString("lot_status");
                    virtu_pallet_num=pallet_num;
                    if(docItemBigData.containsKey("virtu_pallet_num")) virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                    iteratorBigData.close();
                }
            }

            face_code=face_code2_int;
            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", "N");
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");
            mapBigDataRow.put("virtu_pallet_num",virtu_pallet_num);

            JSONArray jaResult=new JSONArray();
            JSONObject jbStationInfo=new JSONObject();
            jbStationInfo.put("station_id",station_id);
            jbStationInfo.put("station_code",station_code);
            jbStationInfo.put("panel_model",panel_model_value);
            jbStationInfo.put("port_code",port_code);
            jbStationInfo.put("group_lot_num",group_lot_num);
            jbStationInfo.put("pallet_num",pallet_num);
            jbStationInfo.put("panel_barcode",panel_barcode);
            jbStationInfo.put("small_panel_flag","Y");
            //5.当未找到任务时直接记录
            if (plan_id.equals("")) {
                if("1".equals(onoff_value)){
                    panel_ng_code=8;
                    panel_status="NG";
                    back_result_code=panel_ng_code;
                    if("1".equals(ng_auto_pass_value)){
                        panel_status="NG_PASS";
                        back_result_code=0;
                    }
                }
                else{
                    if(!"OK".equals(yk_result)){
                        panel_ng_code=9;
                        panel_status="NG";
                        back_result_code=9;
                    }
                }
                panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                mapBigDataRow.put("panel_ng_code", panel_ng_code);
                mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                //发送到Event后台处理
                port_index="0";
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num + "," +
                        port_index+ "," +face_code+ ","+panel_status+","+yk_result;
                JSONObject jbResult=new JSONObject();
                jbResult.put("result_item",result);
                jaResult.add(jbResult);
                jbStationInfo.put("panel_check_result",jaResult.toString());
                cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, StationFlowInfoTag,
                        jbStationInfo.toString(), false);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, String.valueOf(back_result_code), "", 0);
                return transResult;
            }

            //有工单情况下的判断
            if(!"OK".equals(yk_result)){
                panel_ng_code=9;
            }
            else{
                if(!"1".equals(panel_model_value)){
                    panel_barcode = "";
                    mapBigDataRow.put("panel_barcode", panel_barcode);
                }
                else{
                    if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                        if(panel_barcode.equals("NoRead")) panel_ng_code=2;
                        else panel_ng_code=7;
                    }
                    else{
                        Query queryBigDataLaser = new Query();
                        queryBigDataLaser.addCriteria(Criteria.where("station_id").is(station_id));
                        queryBigDataLaser.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                        long laserAllCount =  mongoTemplate.getCollection(meLaserTable).countDocuments(queryBigDataLaser.getQueryObject());
                        if(laserAllCount>0){
                            queryBigDataLaser = new Query();
                            queryBigDataLaser.addCriteria(Criteria.where("station_id").is(station_id));
                            queryBigDataLaser.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                            queryBigDataLaser.addCriteria(Criteria.where("laser_barcode").is(panel_barcode));
                            long laserCount =  mongoTemplate.getCollection(meLaserTable).countDocuments(queryBigDataLaser.getQueryObject());
                            if(laserCount<=0){
                                panel_ng_code=1;
                            }
                        }
                    }
                }
            }

            //判断最终结果
            if(panel_ng_code>0){
                if(panel_ng_code==4) panel_status = "NG_PASS";
                else{
                    if(ng_auto_pass_value.equals("1") && panel_ng_code!=9) panel_status = "NG_PASS";
                    else panel_status = "NG";
                }
            }
            else panel_status = "OK";
            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
            if("NG".equals(panel_status)){
                back_result_code=panel_ng_code;
            }
            port_index="0";//不需要双工位
            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("panel_index", panel_index);
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            Update updateBigData = new Update();
            String lot_status = "WORK";
            String lot_status2 = "WORK";
            if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
            lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            updateBigData.set("lot_status", lot_status);
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);

            //更新数据
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //更新EAP状态
            if(finish_count==1){
                String writeTags="UnLoadEap/EapStatus/CurrentLotNum,UnLoadEap/EapStatus/CurrentLotCount";
                String tagValues=lot_num+"&"+plan_lot_count;
                cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, writeTags, tagValues, false);
            }

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num + "," +
                    port_index+ "," +face_code+ "," +panel_status+","+yk_result;
            JSONObject jbResult=new JSONObject();
            jbResult.put("result_item",result);
            jaResult.add(jbResult);
            jbStationInfo.put("panel_check_result",jaResult.toString());
            cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, StationFlowInfoTag,
                    jbStationInfo.toString(), false);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, String.valueOf(back_result_code), "", 0);
        } catch (Exception ex) {
            errorMsg = "Exception:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
