package com.api.pack.core.ccd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@EqualsAndHashCode(callSuper = true)
//@AllArgsConstructor
@NoArgsConstructor
public class CCDLblBarcodeResultMessage extends CCDMessage<List<CCDLblBarcodeResultMessageContentItem>>
{
    public static CCDLblBarcodeResultMessage fromJSON(String msg)
    {
        return JSON.parseObject(msg, CCDLblBarcodeResultMessage.class);
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public Map<Integer, String> getCCDLblBarcodeResultMessageContentItemMapping()
    {
        if (this.getContent() == null)
        {
            return new HashMap<>();
        }
        return this.getContent().stream().collect(Collectors.toMap(CCDLblBarcodeResultMessageContentItem::getBarCodeIndex, CCDLblBarcodeResultMessageContentItem::getBarCodeValue));
    }
}
