package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 合件接口
 * 1.HJ-MES合件AGV状态上传
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfHjController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //1.合件AGV状态上传
    @RequestMapping(value = "/PmcCoreHjReportStation", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreHjReportStation(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/interf/PmcCoreHjReportStation";
        String selectResult="";
        String errorMsg="";
        String userName="-1";
        try{
            log.info("合件AGV状态上传:"+jsonParas.toString());

            String request_uuid=jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time=jsonParas.getString("request_time");//请求时间
            String request_attr=jsonParas.getString("request_attr");//预留请求属性
            String data_from_sys=jsonParas.getString("data_from_sys");//来源系统，待定义
            //循环
            String agv_num="";//AGV编号
            String agv_numstate="";//AGV状态
            String agv_errorid="";//AGV错误代码
            String agv_linenum="";//AGV路线号
            String agv_speed="";//AGV速度等级
            String agv_marknum="";//AGV地标号
            JSONArray jzArray = jsonParas.getJSONArray("list");
            if(jzArray != null && jzArray.size()>0) {
                for (int i = 0; i < jzArray.size(); i++) {
                    JSONObject jzObject = jzArray.getJSONObject(i);
                    agv_num=jzObject.getString("agv_num");//AGV编号
                    agv_numstate=jzObject.getString("agv_numstate");//AGV状态
                    agv_errorid=jzObject.getString("agv_errorid");//AGV错误代码
                    agv_linenum=jzObject.getString("agv_linenum");//AGV路线号
                    agv_speed=jzObject.getString("agv_speed");//AGV速度等级
                    agv_marknum=jzObject.getString("agv_marknum");//AGV地标号

                    /*
                    String insertSql="insert into d_pmc_me_station_quality_fill (vin,shebeibh,dh," +
                            "shedingjzl,shijijzl,shedingyl,shijiyl," +
                            "shedingczkz,shijiczkz,shedingxzkz,shijixzkz," +
                            "shedingzkjl,shijizkjl,shedingzyjl,shijizyjl," +
                            "zuoyesj,jiazhujp,gongyicsly,panding " +
                            ") values (" + "'"+vin+"','"+shebeibh+"','"+dh+"',"+
                            shedingjzl+","+shijijzl+","+shedingyl+","+shijiyl+","+
                            shedingczkz+","+shijiczkz+","+shedingxzkz+","+shijixzkz+","+
                            shedingzkjl+","+shijizkjl+","+shedingzyjl+","+shijizyjl+",'"+
                            zuoyesj+"',"+jiazhujp+","+gongyicsly+","+panding +")";
                    cFuncDbSqlExecute.ExecUpdateSql(userName,insertSql,true,request,apiRoutePath);
                     */
                }
            }

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "合件AGV状态上传异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
