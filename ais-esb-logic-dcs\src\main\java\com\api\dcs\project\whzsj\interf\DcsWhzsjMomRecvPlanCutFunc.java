package com.api.dcs.project.whzsj.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 切割计划功能函数
 * 1.新增切割计划
 * 2.判断是否存在并新增切割计划
 * 3.判断是否存在并新增切割计划明细
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
@Slf4j
public class DcsWhzsjMomRecvPlanCutFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.新增切割计划
    public JSONObject receiveMomPlanCut(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        Integer code = 0;
        String userID="-1";
        String response_time = CFuncUtilsSystem.GetNowDateTime("");
        String request_uuid = "";
        try {
            //1.解析 JSON
            request_uuid = jsonParas.getString("reqNo");//请求编号
            String scli_plan_no = jsonParas.getString("scliPlanNo");//切割计划单号
            String project_code = jsonParas.getString("projectCode");//项目号
            String block_code =jsonParas.getString("blockCode");//分段号
            String lot_num = jsonParas.getString("batchCode");//批次号
            String start_time = jsonParas.getString("startTime");//备料开始时间
            String end_time = jsonParas.getString("endTime");//备料结束时间
            String plan_from = jsonParas.getString("reqSystem");//计划来源：AIS、MOM
            String plan_status = "PLAN";//计划状态(PLAN、WORK、FINISH、CANCEL)
            //2.判断 切割计划是否存在
            Long plan_cut_id=PlanCutExistsIns(userID,scli_plan_no,project_code,block_code,lot_num,
                    start_time,end_time,plan_from,plan_status,response_time);
            //3.解析明细
            JSONArray dataList = jsonParas.getJSONArray("data");
            if(dataList!=null && dataList.size()>0){
                String serial_num = "";//唯一码
                String scli_plan_order = "";//切割顺序：同一切割计划单内从1依次递增，1代表切割任务中的第一张
                String material_code = "";//钢材编码
                String material_des = "";//钢材名称
                String m_texture = "";//材质
                String ship_class = "";//船级社
                String m_length = "";//长
                String m_width = "";//宽
                String m_height = "";//高
                String m_weight = "";//重量
                for (int i = 0; i < dataList.size(); i++) {
                    JSONObject jsonObject = dataList.getJSONObject(i);
                    serial_num = jsonObject.getString("uniqNo");//唯一码
                    scli_plan_order = jsonObject.getString("scliPlanOrder");//切割顺序
                    material_code = jsonObject.getString("materialsCode");//钢材编码
                    material_des = jsonObject.getString("materialsName");//钢材名称
                    m_texture = jsonObject.getString("material");//材质
                    ship_class = jsonObject.getString("shipClass");//船级社
                    m_length = jsonObject.getString("length");//长
                    m_width = jsonObject.getString("width");//宽
                    m_height = jsonObject.getString("height");//高
                    m_weight = jsonObject.getString("weight");//重量
                    //4.新增 切割计划明细
                    PlanCutDExistsIns(userID,plan_cut_id,serial_num,scli_plan_order,
                            material_code,material_des,m_texture,ship_class,
                            m_length,m_width,m_height,m_weight,response_time);
                }
            }
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("scli_plan_no", scli_plan_no);
            jbResult.put("code", code);
            jbResult.put("msg", "成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("scli_plan_no", "");
            jbResult.put("code", code);
            jbResult.put("msg", errorMsg);
        }
        return jbResult;
    }

    //2.判断是否存在并新增切割计划
    public Long PlanCutExistsIns(String userID, String scli_plan_no,String project_code,String block_code,
                                  String lot_num, String start_time,String end_time,
                                 String plan_from,String plan_status,String response_time) throws Exception{
        //1.检索是否存在
        Long plan_cut_id=0l;
        String sqlPlanCutSel="select plan_cut_id " +
                "from b_dcs_wms_plan_cut " +
                "where scli_plan_no='"+scli_plan_no+"' ";
        List<Map<String, Object>> itemPlanCutList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlPlanCutSel,
                false,null,"");
        if(itemPlanCutList!=null && itemPlanCutList.size()>0){
            plan_cut_id=Long.parseLong(itemPlanCutList.get(0).get("plan_cut_id").toString());
        }
        else {
            //2.不存在，新增
            plan_cut_id=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_plan_cut_id_seq",true);
            String sqlPlanCutIns="insert into b_dcs_wms_plan_cut " +
                    "(created_by,creation_date,plan_cut_id," +
                    "scli_plan_no,project_code,block_code,lot_num," +
                    "start_time,end_time," +
                    "plan_from,plan_status,enable_flag) values" +
                    "('"+userID+"','"+response_time+"',"+plan_cut_id+",'"+
                    scli_plan_no+"','"+project_code+"','"+block_code+"','"+lot_num+"','"+
                    start_time+"','"+end_time+"','"+
                    plan_from+"','"+plan_status+"','Y')";
            cFuncDbSqlExecute.ExecUpdateSql("AIS",sqlPlanCutIns,false,
                    null,"");
        }
        return plan_cut_id;
    }

    //3.判断是否存在并新增切割计划明细
    public Long PlanCutDExistsIns(String userID, Long plan_cut_id,String serial_num,
                                   String scli_plan_order,String material_code,String material_des,
                                   String m_texture,String ship_class,String m_length,String m_width,
                                   String m_height,String m_weight,String response_time) throws Exception{
        //1.检索是否存在
        Long plan_cut_d_id=0l;
        String sqlPlanCutDSel="select plan_cut_d_id " +
                "from b_dcs_wms_plan_cut_d " +
                "where plan_cut_id="+plan_cut_id+" "+
                "and serial_num='"+serial_num+"' ";
        List<Map<String, Object>> itemPlanCutDList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlPlanCutDSel,
                false,null,"");
        if(itemPlanCutDList!=null && itemPlanCutDList.size()>0){
            plan_cut_d_id=Long.parseLong(itemPlanCutDList.get(0).get("plan_cut_d_id").toString());
        }
        else {
            //2.不存在，新增
            plan_cut_d_id=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_plan_cut_d_id_seq",true);
            String sqlCarTaskIns="insert into b_dcs_wms_plan_cut_d " +
                    "(created_by,creation_date,plan_cut_d_id,plan_cut_id," +
                    "serial_num,scli_plan_order," +
                    "material_code,material_des,m_texture,ship_class," +
                    "m_length,m_width,m_height,m_weight,lock_flag) values" +
                    "('"+userID+"','"+response_time+"',"+plan_cut_d_id+","+plan_cut_id+",'"+
                    serial_num+"',"+scli_plan_order+",'"+
                    material_code+"','"+material_des+"','"+m_texture+"','"+ship_class+"',"+
                    m_length+","+m_width+","+m_height+","+m_weight+",'N')";
            cFuncDbSqlExecute.ExecUpdateSql("AIS",sqlCarTaskIns,
                    false,null,"");
        }
        return plan_cut_d_id;
    }

}
