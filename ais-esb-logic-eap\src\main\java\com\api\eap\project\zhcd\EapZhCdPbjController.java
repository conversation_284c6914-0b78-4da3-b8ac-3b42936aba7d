package com.api.eap.project.zhcd;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * （珠海崇达）配板机处理逻辑
 * 1.配扳机扫描验证
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/zhcd/pbj")
public class EapZhCdPbjController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private PlanCommonFunc planCommonFunc;

    //1.放板机Panel校验
    @RequestMapping(value = "/EapZhCdPbjPanelCheck", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapZhCdPbjPanelCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/zhcd/pbj/EapZhCdPbjPanelCheck";
        String transResult="";
        String errorMsg="";
        String apsPlanTable="a_eap_pbj_aps_plan";
        String apsPlanDTable="a_eap_pbj_aps_plan_d";
        String meArrayTable="a_eap_pbj_me_array";
        String result="";
        try{
            Integer port_index=jsonParas.getInteger("port_index");//作业端口(1左侧配板机，2右侧配板机)
            Integer level_index=jsonParas.getInteger("level_index");//层级排序
            String panel_barcode=jsonParas.getString("panel_barcode");//Top层条码
            Integer ng_auto_pass=jsonParas.getInteger("ng_auto_pass");//NG自动越过标识,1为启用
            if(level_index==null) level_index=0;
            if(panel_barcode==null) panel_barcode="";
            if(ng_auto_pass==null) ng_auto_pass=0;

            //1.查询当前Work任务
            Map<String, Object> mapLotInfo= null;
            Map<String, Object> mapBigDataRow=new HashMap<>();
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String me_array_id=CFuncUtilsSystem.CreateUUID(true);
            String plan_id="";
            String lot_num="";
            Integer lot_level=0;
            Integer task_index=0;
            String batch_num="";
            String product_no="";
            String top_level="";
            String bot_level="";
            Integer set_ng_code=0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:其他异常
            Boolean isLeftTask=true;

            Query queryBigData= new Query();
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"lot_index"));
            MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(),Map.class).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                mapLotInfo = iteratorBigData.next();
                iteratorBigData.close();
            }
            if(mapLotInfo==null) set_ng_code=8;
            else{
                plan_id=mapLotInfo.get("plan_id").toString();
                lot_num=mapLotInfo.get("lot_num").toString();
                lot_level=Integer.parseInt(mapLotInfo.get("lot_level").toString());
                String left_on_flag=mapLotInfo.get("left_on_flag").toString();
                String right_on_flag=mapLotInfo.get("right_on_flag").toString();
                //判断PLC传递是否符合任务
                Boolean isPlcDataOk=true;
                if(left_on_flag.equals("N") && port_index==1){
                    isPlcDataOk=false;
                    set_ng_code=9;
                }
                if(isPlcDataOk && right_on_flag.equals("N") && port_index==2 && lot_level<=6){
                    isPlcDataOk=false;
                    set_ng_code=9;
                }
                if(isPlcDataOk && (level_index<=0 || level_index>lot_level)){
                    isPlcDataOk=false;
                    set_ng_code=10;
                }
                if(isPlcDataOk && lot_level>6 && port_index==1){
                    isPlcDataOk=false;
                    set_ng_code=11;
                }
                if(isPlcDataOk && port_index==1 && level_index>6){
                    isPlcDataOk=false;
                    set_ng_code=12;
                }
                if(isPlcDataOk && lot_level>6 && port_index==2 && level_index<=6){
                    isPlcDataOk=false;
                    set_ng_code=13;
                }
                if(isPlcDataOk){
                    Integer finish_ok_count=0;
                    Integer now_level_index=0;
                    Integer left_finish_ok_count=Integer.parseInt(mapLotInfo.get("left_finish_ok_count").toString());
                    Integer right_finish_ok_count=Integer.parseInt(mapLotInfo.get("right_finish_ok_count").toString());
                    Integer left_level_index=Integer.parseInt(mapLotInfo.get("left_level_index").toString());
                    Integer right_level_index=Integer.parseInt(mapLotInfo.get("right_level_index").toString());
                    if(lot_level>6 && port_index==2){
                        finish_ok_count=left_finish_ok_count;
                        now_level_index=left_level_index;
                    }
                    else if(lot_level<=6 && port_index==2){
                        isLeftTask=false;
                        finish_ok_count=right_finish_ok_count;
                        now_level_index=right_level_index;
                    }
                    else{
                        finish_ok_count=left_finish_ok_count;
                        now_level_index=left_level_index;
                    }
                    finish_ok_count++;
                    now_level_index++;
                    task_index=finish_ok_count;
                    //查询当前扫描应该属于什么信息
                    queryBigData= new Query();
                    queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                    queryBigData.addCriteria(Criteria.where("level_index").is(now_level_index));
                    iteratorBigData = mongoTemplate.getCollection(apsPlanDTable).find(queryBigData.getQueryObject(),Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if(iteratorBigData.hasNext()){
                        Map<String, Object> mapPanelInfo= iteratorBigData.next();
                        batch_num=mapPanelInfo.get("batch_num").toString();
                        product_no=mapPanelInfo.get("product_no").toString();
                        top_level=mapPanelInfo.get("top_level").toString();
                        bot_level=mapPanelInfo.get("bot_level").toString();
                        iteratorBigData.close();
                    }
                    if(level_index!=now_level_index){
                        set_ng_code=1;
                    }
                    else{
                        //进入正式的条码判断
                        if(panel_barcode.equals("") || panel_barcode.equals("NoRead")){
                            if(ng_auto_pass==1) set_ng_code=4;
                            else{
                                if(panel_barcode.equals("")) set_ng_code=7;
                                else set_ng_code=2;
                            }
                        }
                        else{
                            String resolve_top_level="";
                            if(panel_barcode.length()>=14){
                                resolve_top_level=panel_barcode.substring(12,14);
                            }
                            if(!resolve_top_level.equals(top_level)) set_ng_code=1;
                        }
                    }
                }
            }

            //结论
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("me_array_id",me_array_id);
            mapBigDataRow.put("plan_id",plan_id);
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("lot_level",lot_level);
            mapBigDataRow.put("port_index",port_index);
            mapBigDataRow.put("task_index",task_index);
            mapBigDataRow.put("panel_top_barcode",panel_barcode);
            mapBigDataRow.put("panel_bot_barcode","");
            mapBigDataRow.put("level_index",level_index);
            mapBigDataRow.put("batch_num",batch_num);
            mapBigDataRow.put("product_no",product_no);
            mapBigDataRow.put("top_level",top_level);
            mapBigDataRow.put("bot_level",bot_level);
            mapBigDataRow.put("set_ng_code",set_ng_code);
            mapBigDataRow.put("set_ng_msg",planCommonFunc.getPanelNgMsg(set_ng_code));

            //修改状态
            if((set_ng_code==0 || set_ng_code==4) && mapLotInfo!=null){
                queryBigData= new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                String task_start_time=mapLotInfo.get("task_start_time").toString();
                if(task_start_time==null || task_start_time.equals("")) task_start_time=CFuncUtilsSystem.GetNowDateTime("");
                Integer plan_lot_count=Integer.parseInt(mapLotInfo.get("plan_lot_count").toString());
                Integer left_finish_ok_count=Integer.parseInt(mapLotInfo.get("left_finish_ok_count").toString());
                Integer right_finish_ok_count=Integer.parseInt(mapLotInfo.get("right_finish_ok_count").toString());
                Integer left_level_index=Integer.parseInt(mapLotInfo.get("left_level_index").toString());
                Integer right_level_index=Integer.parseInt(mapLotInfo.get("right_level_index").toString());
                Update updateBigData = new Update();
                if(level_index<lot_level){
                    if(isLeftTask){
                        updateBigData.set("left_level_index", left_level_index+1);
                    }
                    else{
                        updateBigData.set("right_level_index", right_level_index+1);
                    }
                }
                else{
                    updateBigData.set("finish_ok_count", left_finish_ok_count+right_finish_ok_count+1);
                    if(isLeftTask){
                        updateBigData.set("left_finish_ok_count", left_finish_ok_count+1);
                        updateBigData.set("left_level_index", 0);
                    }
                    else{
                        updateBigData.set("right_finish_ok_count", right_finish_ok_count+1);
                        updateBigData.set("right_level_index", 0);
                    }
                    if((left_finish_ok_count+right_finish_ok_count+1)>=plan_lot_count){
                        updateBigData.set("lot_status", "FINISH");
                        String task_end_time=CFuncUtilsSystem.GetNowDateTime("");
                        long task_cost_time=CFuncUtilsSystem.GetDiffMsTimes(task_start_time,task_end_time);
                        updateBigData.set("task_cost_time", task_cost_time);
                    }
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //插入数据
            mongoTemplate.insert(mapBigDataRow,meArrayTable);

            //返回数据
            JSONObject jbResult=new JSONObject();
            jbResult.put("me_array_id",me_array_id);
            jbResult.put("lot_num",lot_num);
            jbResult.put("lot_level",lot_level);
            jbResult.put("task_index",task_index);
            jbResult.put("panel_barcode",panel_barcode);
            jbResult.put("level_index",level_index);
            jbResult.put("panel_ng_code",set_ng_code);
            jbResult.put("top_level",top_level);
            jbResult.put("bot_level",bot_level);
            result=jbResult.toString();
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
        }
        catch (Exception ex){
            errorMsg= "异常:"+ex;
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
