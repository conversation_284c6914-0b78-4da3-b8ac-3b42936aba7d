package com.api.dcs.project.fjrm.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * WMS接受流程对外接口
 * 1.W10A1 废料调运计划(暂时不用)
 * 2.W10A2 删除调运计划(暂时不用)
 *
 * 3.W10D1 废料库存信息请求(暂时不用)
 *
 * 4.W10B1 物料请求总计划
 * 5.W10B2 物料请求总计划确认
 *
 * 6.W10C1 化学成分报文
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/fjrm/interf/wms/recv")
public class DcsFjrmWmsRecvController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private DcsFjrmWmsRecvFunc dcsFjrmWmsRecvFunc;

    //1.W10A1 废料调运计划(暂时不用)
    @RequestMapping(value = "/DcsFjrmWmsRecvW10a1", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsFjrmWmsRecvW10a1(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/fjrm/interf/wms/recv/DcsFjrmWmsRecvW10a1";
        //记录日志
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsFjrmWmsRecvFunc.RecvW10a1Task(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //4.W10B1 物料请求总计划
    @RequestMapping(value = "/DcsFjrmWmsRecvW10b1", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsFjrmWmsRecvW10b1(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/fjrm/interf/wms/recv/DcsFjrmWmsRecvW10b1";
        //记录日志
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsFjrmWmsRecvFunc.RecvW10b1Task(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //5.W10B2 物料请求总计划确认
    @RequestMapping(value = "/DcsFjrmWmsRecvW10b2", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsFjrmWmsRecvW10b2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/fjrm/interf/wms/recv/DcsFjrmWmsRecvW10b2";
        //记录日志
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsFjrmWmsRecvFunc.RecvW10b2Task(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //6.W10C1 化学成分报文
    @RequestMapping(value = "/DcsFjrmWmsRecvW10c1", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsFjrmWmsRecvW10c1(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/fjrm/interf/wms/recv/DcsFjrmWmsRecvW10c1";
        //记录日志
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsFjrmWmsRecvFunc.RecvW10c1Task(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

}
