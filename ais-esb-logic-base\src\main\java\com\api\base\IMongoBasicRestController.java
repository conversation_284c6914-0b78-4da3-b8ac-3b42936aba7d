package com.api.base;

import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.data.domain.*;
import org.springframework.http.ResponseEntity;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

public abstract class IMongoBasicRestController<VO extends Serializable, Service extends IMongoBasicService<VO, ?>> implements IController<VO>
{
    protected final Service service;

    public IMongoBasicRestController(Service service)
    {
        this.service = service;
    }

    @ApiOperation("删除指定资源（指定IDs）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功（当前接口无此状态返回）"), // 200
            @ApiResponse(code = 204, message = "删除完成"), // 204
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @DeleteMapping
    public ResponseEntity<?> delete(@RequestParam List<String> ids)
    {
        Iterable<VO> models = this.service.findAllById(ids);
        if (!ObjectUtils.isEmpty(models))
        {
            this.service.deleteAll(models);
        }
        return this.noContent();
    }

    @ApiOperation("删除指定资源（指定ID）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "成功（当前接口无此状态返回）"), // 200
            @ApiResponse(code = 204, message = "删除完成"), // 204
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteById(@PathVariable String id)
    {
        this.service.deleteById(id);
        return this.noContent();
    }

    @ApiOperation("获取资源列表（可选过滤条件）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"), // 200
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @GetMapping
    public ResponseEntity<?> get(@ApiParam(hidden = true) @RequestParam(required = false) Map<String, Object> parameters)
    {
        PageRequest pageRequest = this.generatePageRequestByParameters(parameters);
        if (pageRequest != null)
        {
            return this.getPage(parameters, pageRequest);
        }
        else
        {
            Sort sort = this.generateSortByParameters(parameters);
            return this.getList(parameters, sort);
        }
    }

    @ApiOperation("获取单个资源（指定ID）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "查询成功"), // 200
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @GetMapping("/{id}")
    public ResponseEntity<?> getById(@PathVariable(required = false) String id)
    {
        VO o = this.service.findById(id).orElse(null);
        return o != null ? this.ok(o) : this.notFound();
    }

    @ApiOperation("修改指定资源（指定ID）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "修改完成"), // 200
            @ApiResponse(code = 201, message = "修改完成"), // 201
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @PatchMapping({"", "/{id}"})
    public ResponseEntity<?> patch(@PathVariable(required = false) String id, @RequestBody VO o)
    {
        this.service.save(o);
        return this.ok(o);
    }

    @ApiOperation("更替指定资源（指定ID）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "修改完成"), // 200
            @ApiResponse(code = 201, message = "修改完成"), // 201
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @PutMapping({"", "/{id}"})
    public ResponseEntity<?> put(@PathVariable(required = false) String id, @RequestBody VO o)
    {
        if (!ObjectUtils.isEmpty(id))
        {
            if (!this.service.existsById(id))
            {
                return this.notFound();
            }
            else
            {
                this.service.deleteById(id);
            }
        }
        this.service.save(o);
        return this.ok(o);
    }

    @ApiOperation("新增资源")
    @ApiResponses({
            @ApiResponse(code = 200, message = "新增完成"), // 200
            @ApiResponse(code = 201, message = "新增完成"), // 201
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @PostMapping
    public ResponseEntity<?> post(@RequestBody VO o)
    {
        o = this.service.save(o);
        return this.ok(o);
    }

    public ResponseEntity<?> getList(Map<String, Object> parameters, Sort sort)
    {
        // 根据泛型获取实际类型
        VO vo = this.newInstanceOfVO();
        // 覆盖实体类相关属性值
        this.overlay(parameters, vo);
        return this.ok(this.service.findAll(Example.of(vo), sort));
    }

    public ResponseEntity<?> getPage(Map<String, Object> parameters, Pageable pageable)
    {
        // 根据泛型获取实际类型
        VO vo = this.newInstanceOfVO();
        // 覆盖实体类相关属性值
        this.overlay(parameters, vo);
        Page<VO> page = this.service.findAll(Example.of(vo), pageable);
        return this.ok(page.getContent(), page.getTotalElements());
    }

    public Sort generateSortByParameters(Map<String, Object> parameters)
    {
        parameters.remove(Const.KEY_USER_NAME);
        Boolean sorted = Optional.ofNullable(parameters.get(Const.KEY_SORTED)).map(it -> {
            try
            {
                parameters.remove(Const.KEY_SORTED);
                return Boolean.parseBoolean(it.toString());
            }
            catch (NumberFormatException e)
            {
                return null;
            }
        }).orElse(null);
        if (sorted != null && !sorted)
        {
            return null;
        }
        return Optional.ofNullable(parameters.get(Const.KEY_SORT)).map(it -> {
            try
            {
                parameters.remove(Const.KEY_SORT);
                String sortString = it.toString();
                List<Sort.Order> orders = Arrays.stream(sortString.split(",")).map(oit -> {
                    String[] order = oit.split(" ");
                    String property = order[0];
                    Sort.Direction direction = null;
                    if (order.length == 2)
                    {
                        direction = Sort.Direction.fromString(order[1]);
                    }
                    return direction != null && direction.isDescending() ? Sort.Order.desc(property) : Sort.Order.asc(property);
                }).collect(Collectors.toList());
                return Sort.by(orders);
            }
            catch (NumberFormatException e)
            {
                return null;
            }
        }).orElse(null);
    }

    public PageRequest generatePageRequestByParameters(Map<String, Object> parameters)
    {
        parameters.remove(Const.KEY_USER_NAME);
        Integer page = Optional.ofNullable(parameters.get(Const.KEY_PAGE)).map(it -> {
            try
            {
                parameters.remove(Const.KEY_PAGE);
                return Integer.parseInt(it.toString());
            }
            catch (NumberFormatException e)
            {
                return null;
            }
        }).orElse(null);
        // page 从 0 开始
        if (page != null)
        {
            page = page > 0 ? page - 1 : 0;
        }
        Integer size = Optional.ofNullable(parameters.get(Const.KEY_SIZE)).map(it -> {
            try
            {
                parameters.remove(Const.KEY_SIZE);
                return Integer.parseInt(it.toString());
            }
            catch (NumberFormatException e)
            {
                return null;
            }
        }).orElse(null);
        // 分页查询
        if (page != null && size != null)
        {
            Sort sort = this.generateSortByParameters(parameters);
            if (sort != null)
            {
                return PageRequest.of(page, size, sort);
            }
            return PageRequest.of(page, size);
        }
        return null;
    }
}
