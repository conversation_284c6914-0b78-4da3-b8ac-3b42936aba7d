package com.api.eap.core4.load;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * AIS4.0放板机生产计划处理逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2024-7-29
 */
@RestController
@Slf4j
@RequestMapping("/eap/core4/load")
public class EapCore4LoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //选择子批计划任务
    @RequestMapping(value = "/EapCore4LoadSubPlanLotSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4LoadSubPlanLotSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/load/EapCore4LoadSubPlanLotSel";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            long station_id_long = Long.parseLong(station_id);

            String group_id = "";
            Integer left_lot_count = 0;//剩余PLAN状态下LOT工单数量
            Integer pallet_all_count = 0;//载具总数量
            String[] group_lot_status_list = new String[]{"PLAN", "WORK"};
            List<Map<String, Object>> itemList = null;
            Map<String, Object> mapItem = null;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                mapItem = iteratorBigData.next();
                group_id = mapItem.get("group_id").toString();
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                left_lot_count = (int) mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                List<Map> lotList = mongoTemplate.find(queryBigData, Map.class, apsPlanTable);
                for (Map map : lotList) {
                    pallet_all_count += Integer.parseInt(map.get("plan_lot_count").toString());
                }
                mapItem.put("left_lot_count", left_lot_count);
                mapItem.put("pallet_all_count", pallet_all_count);
                itemList = new ArrayList<>();
                itemList.add(mapItem);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "选择子批计划任务异常:" + ex;
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //子批完成修改状态以及拿取最后板件信息
    @RequestMapping(value = "/EapCore4LoadSubLotFinishUpd", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4LoadSubLotFinishUpd(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/load/EapCore4LoadSubLotFinishUpd";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String plan_id = jsonParas.getString("plan_id");

            JSONObject jbLotInfo = null;
            Integer task_error_code = 0;
            //1.查询任务数据
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String group_id = docItemBigData.getString("group_id");
                String group_lot_num = docItemBigData.getString("group_lot_num");
                String lot_num = docItemBigData.getString("lot_num");
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                if (plan_lot_count == finish_ok_count) task_error_code = 1;
                else if (plan_lot_count > finish_ok_count) task_error_code = 2;
                else if (plan_lot_count < finish_ok_count) task_error_code = 3;
                jbLotInfo = new JSONObject();
                jbLotInfo.put("group_id", group_id);
                jbLotInfo.put("group_lot_num", group_lot_num);
                jbLotInfo.put("plan_id", plan_id);
                jbLotInfo.put("lot_num", lot_num);
                jbLotInfo.put("plan_lot_count", plan_lot_count);
                jbLotInfo.put("finish_ok_count", finish_ok_count);
                iteratorBigData.close();
            }
            if (jbLotInfo != null) {
                String[] panel_status = new String[]{"OK", "NG_PASS"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                queryBigData.addCriteria(Criteria.where("panel_status").in(panel_status));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    String panel_barcode = docItemBigData.getString("panel_barcode");
                    String panel_status2 = docItemBigData.getString("panel_status");
                    String inspect_flag = docItemBigData.getString("inspect_flag");
                    String dummy_flag = docItemBigData.getString("dummy_flag");
                    jbLotInfo.put("last_panel_barcode", panel_barcode);
                    jbLotInfo.put("last_panel_status", panel_status2);
                    jbLotInfo.put("last_inspect_flag", inspect_flag);
                    jbLotInfo.put("last_dummy_flag", dummy_flag);
                    iteratorBigData.close();
                }
                //修改状态
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                Update updateBigData = new Update();
                updateBigData.set("lot_status", "FINISH");
                updateBigData.set("group_lot_status", "FINISH");
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                updateBigData.set("task_error_code", task_error_code);
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                result = jbLotInfo.toString();
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "子批完成修改状态异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //退载具更新状态
    @RequestMapping(value = "/EapCore4LoadGroupFinishUpd", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4LoadGroupFinishUpd(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/load/EapCore4LoadGroupFinishUpd";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String group_id = jsonParas.getString("group_id");
            Integer task_error_code = jsonParas.getInteger("task_error_code");

            if (group_id != null && !group_id.equals("")) {
                //更新主状态
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "FINISH");
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                //更新为完成状态
                String[] lot_status = new String[]{"WAIT", "PLAN", "WORK"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status));
                updateBigData = new Update();
                updateBigData.set("lot_status", "FINISH");
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                updateBigData.set("task_error_code", task_error_code);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "退载具更新状态异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //查询放板机母批最后一个子批信息 --退载具时候判断
    @RequestMapping(value = "/EapCore4LoadLastLotInGroupSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4LoadLastLotInGroupSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/load/EapCore4LoadLastLotInGroupSelect";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String group_id = jsonParas.getString("group_id");
            String group_lot_num = "";
            String lot_num = "";

            if (group_id != null && !group_id.equals("")) {
                //获取任务
                String[] lot_status = new String[]{"WORK", "FINISH"};
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                if (iteratorBigData.hasNext()) {
                    Map<String, Object> mapLotInfo = iteratorBigData.next();
                    group_lot_num = mapLotInfo.get("group_lot_num").toString();
                    lot_num = mapLotInfo.get("lot_num").toString();
                    iteratorBigData.close();
                }
            }
            String result = group_lot_num + "," + lot_num;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "退载具更新状态异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //查询放板机母批最后一个子批信息 --完批时候判断，判断是否还有PLAN状态的任务，有就不是最后一个，没有当前就是最后一个
    @RequestMapping(value = "/EapCore4LoadLastLotInGroupSelect2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4LoadLastLotInGroupSelect2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/load/EapCore4LoadLastLotInGroupSelect2";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String group_id = jsonParas.getString("group_id");
            String plan_id = jsonParas.getString("plan_id");
            String group_lot_num = "";
            String lot_num = "";

            if (group_id != null && !group_id.equals("")) {
                //获取母批中是否还有PLAN状态的任务，如果没有，当前任务就是最后子批
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                if (!iteratorBigData.hasNext()) {
                    Query queryBigData2 = new Query();
                    queryBigData2.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData2.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Map> iteratorBigData2 = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject(), Map.class).
                            sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                    if (iteratorBigData2.hasNext()) {
                        Map<String, Object> mapLotInfo = iteratorBigData2.next();
                        group_lot_num = mapLotInfo.get("group_lot_num").toString();
                        lot_num = mapLotInfo.get("lot_num").toString();
                        iteratorBigData2.close();
                    }
                }
            }
            String result = group_lot_num + "," + lot_num;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "退载具更新状态异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
