package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 定颖放板机定制生产计划处理逻辑
 * 1.放板机Panel校验
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/cj/master")
public class EapCjOutPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //收板机Panel校验
    @RequestMapping(value = "/EapDyMainOutPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyUnLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/main/EapDyMainOutPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                target_lot_count = docItemBigData.getInteger("target_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            String SbDoubleFlag = cFuncDbSqlResolve.GetParameterValue("SB_DOUBLE_FLAG");
            String isUseNextLot = "N";//是否使用下一个任务

            if (plan_id.equals("")) {//当未找到任务时直接记录
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 99;
                }
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num + "," + port_index;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                panel_ng_code = 99;//Dummy模式返回100代码
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num + "," + port_index;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_ng_code = 4;
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_ng_code = 4;
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            if (ng_auto_pass_value.equals("1")) {
                                panel_ng_code = 4;
                            } else {
                                panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            }
                        } else {
                            panel_ng_code = CheckPanelByItemInfo(plan_id, panel_barcode, lot_short_num, item_info, strip_flag, ng_auto_pass_value);
                            if (panel_ng_code != 0 && SbDoubleFlag.equals("Y")) {
                                //当前PORT任务与板件不匹配,则执行下一个任务
                                String[] group_lot_status_list = new String[]{"WAIT", "PLAN"};
                                lot_status_list = new String[]{"WAIT","PLAN", "WORK"};
                                queryBigData = new Query();
                                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                                queryBigData.addCriteria(Criteria.where("group_lot_num").ne(group_lot_num));
                                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                                queryBigData.limit(1);
                                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                if (iteratorBigData.hasNext()) {
                                    Document docItemBigData = iteratorBigData.next();
                                    String select_plan_id2 = docItemBigData.get("plan_id").toString();
                                    String select_group_lot_num2 = docItemBigData.get("group_lot_num").toString();
                                    String select_lot_short_num2 = docItemBigData.get("lot_short_num").toString();
                                    String select_item_info2 = docItemBigData.get("item_info").toString();
                                    Integer select_finish_count2 = docItemBigData.getInteger("finish_count");
                                    Integer select_finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                                    Integer select_finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                                    Integer panel_ng_code2 = CheckPanelByItemInfo(select_plan_id2, panel_barcode, select_lot_short_num2, select_item_info2, strip_flag, ng_auto_pass_value);
                                    if (panel_ng_code2 == 0) {
                                        panel_ng_code = panel_ng_code2;
                                        plan_id = select_plan_id2;
                                        group_lot_num = select_group_lot_num2;
                                        lot_short_num = select_lot_short_num2;
                                        item_info = select_item_info2;
                                        finish_count = select_finish_count2;
                                        finish_ok_count = select_finish_ok_count2;
                                        finish_ng_count = select_finish_ng_count2;
                                        panel_index = finish_count + 1;
                                        //其他数据
                                        plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                                        task_from=docItemBigData.getString("task_from");
                                        lot_num = docItemBigData.getString("lot_num");
                                        lot_index = docItemBigData.getInteger("lot_index");
                                        material_code = docItemBigData.getString("material_code");
                                        pallet_num = docItemBigData.getString("pallet_num");
                                        pallet_type = docItemBigData.getString("pallet_type");
                                        lot_level = docItemBigData.getString("lot_level");
                                        panel_length = docItemBigData.getDouble("panel_length");
                                        panel_width = docItemBigData.getDouble("panel_width");
                                        panel_tickness = docItemBigData.getDouble("panel_tickness");
                                        isUseNextLot = "Y";
                                    }
                                    iteratorBigData.close();
                                }
                            }
                        }
                    }
                } else {
                    //如果是无Panel模式,则需要进行组合
                    if (!lot_short_num.equals("") && lot_short_num.length() >= 10) {
                        String noPanelLotShortNum = lot_short_num.substring(0, 10);
                        panel_barcode = noPanelLotShortNum + String.format("%03d", panel_index);
                        mapBigDataRow.put("panel_barcode", panel_barcode);
                    } else {
                        panel_barcode = String.format("%03d", panel_index);
                        mapBigDataRow.put("panel_barcode", panel_barcode);
                    }
                }
            }


            if (panel_ng_code == 0) panel_status = "OK";
            else if (panel_ng_code == 4) panel_status = "NG_PASS";
            else panel_status = "NG";
            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);

            if (isUseNextLot.equals("Y")) {
                if (port_code.equals("01")) port_code = "02";
                else port_code = "01";
                if (port_index.equals("1")) port_index = "2";
                else port_index = "1";
            }
            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("panel_index", panel_index);
            //其他数据
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);

            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            Update updateBigData = new Update();
            String lot_status = "WORK";
            String lot_status2 = "WORK";

            if (isUseNextLot.equals("N")) {
                if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
                lot_status2 = lot_status;
                if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
                updateBigData.set("lot_status", lot_status);
                if (finish_count == 1) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                if (lot_status.equals("FINISH")) {
                    updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
            }
            else{
                if(finish_ok_count >= plan_lot_count){
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                updateBigData.set("lot_status", lot_status);
                if (finish_count == 1) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
            }

            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);

            //更新数据
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num + "," + port_index;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "收板机Panel校验异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //收板机Panel提前验证,不做存储
    @RequestMapping(value = "/EapDyUnLoadPlanPanelPreCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyUnLoadPlanPanelPreCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/unload/EapDyUnLoadPlanPanelPreCheck";
        String transResult = "";
        String result = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            long station_id_long = Long.parseLong(station_id);
            String lot_num = "";
            //返回信息:panel_flag+","+lot_num+","+panel_ng_code+","+panel_status+","+panel_ng_msg

            //1.若是无panel模式则无需进行扫描
            if (!panel_model_value.equals("1")) {
                result = panel_flag + "," + lot_num + "," + panel_ng_code + "," + panel_status + "," + panel_ng_msg;
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //2.有Panel直接判断简码
            if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                //需要进行NG累加
                String ngAllCount = opCommonFunc.ReadCellOneRedisValue(station_code, "UnLoad",
                        "Eap", "EapStatus", "PanelNgCount");
                if (ngAllCount == null || ngAllCount.equals("")) {
                    errorMsg = "读取标签{PanelNgCount}失败";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                Integer ngAllCountInt = Integer.parseInt(ngAllCount) + 1;
                errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, "UnLoad",
                        "Eap", "EapStatus", "PanelNgCount", "AIS",
                        String.valueOf(ngAllCountInt), false);
                if (!errorMsg.equals("")) {
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                //
                if (ng_auto_pass_value.equals("1")) {
                    panel_ng_code = 0;
                    panel_status = "NG_PASS";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(4);
                    result = panel_flag + "," + lot_num + "," + panel_ng_code + "," + panel_status + "," + panel_ng_msg;
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                    return transResult;
                }
                panel_status = "NG";
                panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                result = panel_flag + "," + lot_num + "," + panel_ng_code + "," + panel_status + "," + panel_ng_msg;
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            String[] group_lot_status_list = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            Integer lotCount=0;
            while (iteratorBigData.hasNext()) {
                lotCount++;
                Document docItemBigData = iteratorBigData.next();
                String lot_num_ori = docItemBigData.getString("lot_num");
                String lot_short_num_ori = docItemBigData.getString("lot_short_num");
                String item_info = docItemBigData.getString("item_info");

                //需要判断简码LIST信息,新增板件判断逻辑,240315 by ZhouJun
                List<String> lot_short_num_list = new ArrayList<>();
                if (item_info != null && !item_info.equals("")) {
                    JSONArray item_info_list = JSONArray.parseArray(item_info);
                    if (item_info_list != null && item_info_list.size() > 0) {
                        int lotShortNumListCount = 0;
                        for (int i = 0; i < item_info_list.size(); i++) {
                            JSONObject jbItem = item_info_list.getJSONObject(i);
                            String item_id = jbItem.getString("item_id");
                            String item_value = jbItem.getString("item_value");
                            if (item_id.equals("S026") && item_value != null && !item_value.equals("") && !item_value.contains("NA")) {
                                try {
                                    lotShortNumListCount = Integer.parseInt(item_value);
                                } catch (Exception intError) {
                                    lotShortNumListCount = 0;
                                }
                                break;
                            }
                        }
                        if (lotShortNumListCount > 0) {
                            List<String> lotShortColList = new ArrayList<>();
                            for (int i = 1; i <= lotShortNumListCount; i++) {
                                String SName = "S" + String.format("%03d", 26 + i);
                                lotShortColList.add(SName);
                            }
                            for (int i = 0; i < item_info_list.size(); i++) {
                                JSONObject jbItem = item_info_list.getJSONObject(i);
                                String item_id = jbItem.getString("item_id");
                                String item_value = jbItem.getString("item_value");
                                if (lotShortColList.contains(item_id) &&
                                        item_value != null && !item_value.equals("")) {
                                    if (!lot_short_num_list.contains(item_value)) lot_short_num_list.add(item_value);
                                }
                            }
                        }
                    }
                }
                if (!lot_short_num_ori.equals("") && !lot_short_num_list.contains(lot_short_num_ori))
                    lot_short_num_list.add(lot_short_num_ori);
                if (lot_short_num_list.size() > 0) {
                    for (int i = 0; i < lot_short_num_list.size(); i++) {
                        String lot_short_num_item = lot_short_num_list.get(i);
                        if (panel_barcode.startsWith(lot_short_num_item)) {
                            lot_num = lot_num_ori;
                            break;
                        }
                    }
                }
                if (!lot_num.equals("")) {
                    break;
                }
                //超出比对数量也跳出循环
                if(lotCount>=10){
                    break;
                }
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (lot_num.equals("")) {
                if (ng_auto_pass_value.equals("1")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 0;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(4);
                } else {
                    panel_status = "NG";
                    panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }

            //记录OK数量,这里代表着不会到收扳机
            if (panel_ng_code >= 0) {
                String okAllCount = opCommonFunc.ReadCellOneRedisValue(station_code, "UnLoad",
                        "Eap", "EapStatus", "PanelOkCount");
                if (okAllCount == null || okAllCount.equals("")) {
                    errorMsg = "读取标签{PanelOkCount}失败";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                Integer okAllCountInt = Integer.parseInt(okAllCount) + 1;
                errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, "UnLoad",
                        "Eap", "EapStatus", "PanelOkCount", "AIS",
                        String.valueOf(okAllCountInt), false);
                if (!errorMsg.equals("")) {
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }
            result = panel_flag + "," + lot_num + "," + panel_ng_code + "," + panel_status + "," + panel_ng_msg;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "收板机Panel提前验证异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    public Integer CheckPanelByItemInfo(String plan_id, String panel_barcode, String lot_short_num, String item_info, String strip_flag, String ng_auto_pass_value) throws Exception {
        Integer panel_ng_code = 0;
        String panel_ng_msg = "";
        String panel_status = "OK";
        String apsPlanDTable = "a_eap_aps_plan_d";
        try {
            //判断是否为Strip
            if (strip_flag.equals("Y")) {
                if (item_info != null && !item_info.equals("")) {
                    JSONArray jaStripList = JSONArray.parseArray(item_info);
                    if (jaStripList != null && jaStripList.size() > 0) {
                        panel_status = "NG";
                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        for (int m = 0; m < jaStripList.size(); m++) {
                            JSONObject jbStripItem = jaStripList.getJSONObject(m);
                            String strip_barcode = jbStripItem.getString("strip_barcode");
                            String strip_level = jbStripItem.getString("strip_level");
                            String strip_status = jbStripItem.getString("strip_status");
                            if (strip_barcode.equals(panel_barcode)) {
                                if (strip_status.equals("OK")) {
                                    panel_status = "OK";
                                    panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                }
                                break;
                            }
                        }
                    }
                }
            } else {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                if (pnListCount > 0) {
                    Query queryBigDataD = new Query();
                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                    long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                    if (okCount <= 0) {
                        if (ng_auto_pass_value.equals("1")) {
                            panel_status = "NG_PASS";
                            panel_ng_code = 4;
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            panel_status = "NG";
                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        }
                    }
                } else {
                    //需要判断简码LIST信息,新增板件判断逻辑,240315 by ZhouJun
                    List<String> lot_short_num_list = new ArrayList<>();
                    if (item_info != null && !item_info.equals("")) {
                        JSONArray item_info_list = JSONArray.parseArray(item_info);
                        if (item_info_list != null && item_info_list.size() > 0) {
                            int lotShortNumListCount = 0;
                            for (int i = 0; i < item_info_list.size(); i++) {
                                JSONObject jbItem = item_info_list.getJSONObject(i);
                                String item_id = jbItem.getString("item_id");
                                String item_value = jbItem.getString("item_value");
                                if (item_id.equals("S026") && item_value != null && !item_value.equals("") && !item_value.contains("NA")) {
                                    try {
                                        lotShortNumListCount = Integer.parseInt(item_value);
                                    } catch (Exception intError) {
                                        lotShortNumListCount = 0;
                                    }
                                    break;
                                }
                            }
                            if (lotShortNumListCount > 0) {
                                List<String> lotShortColList = new ArrayList<>();
                                for (int i = 1; i <= lotShortNumListCount; i++) {
                                    String SName = "S" + String.format("%03d", 26 + i);
                                    lotShortColList.add(SName);
                                }
                                for (int i = 0; i < item_info_list.size(); i++) {
                                    JSONObject jbItem = item_info_list.getJSONObject(i);
                                    String item_id = jbItem.getString("item_id");
                                    String item_value = jbItem.getString("item_value");
                                    if (lotShortColList.contains(item_id) &&
                                            item_value != null && !item_value.equals("")) {
                                        if (!lot_short_num_list.contains(item_value))
                                            lot_short_num_list.add(item_value);
                                    }
                                }
                            }
                        }
                    }
                    if (!lot_short_num.equals("") && !lot_short_num_list.contains(lot_short_num))
                        lot_short_num_list.add(lot_short_num);
                    //新增逻辑,简码判断
                    if (lot_short_num_list.size() > 0) {
                        //默认混批
                        panel_status = "NG";
                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);

                        for (int i = 0; i < lot_short_num_list.size(); i++) {
                            String lot_short_num_item = lot_short_num_list.get(i);
                            if (panel_barcode.startsWith(lot_short_num_item)) {
                                panel_status = "OK";
                                panel_ng_code = 0;
                                panel_ng_msg = "";
                                break;
                            }
                        }
                        if (panel_ng_code > 0 && ng_auto_pass_value.equals("1")) {
                            panel_status = "NG_PASS";
                            panel_ng_code = 4;
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
        return panel_ng_code;
    }
}
