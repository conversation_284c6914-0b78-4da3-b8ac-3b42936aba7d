package com.api.eap.project.thailand.guanghe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * (泰国广合)EAP流程接口(Sub)公共方法
 * 1.[接口]请求叫料
 * 2.[接口]准备生产请求
 * 3.[接口]开始生产请求
 * 4.[接口]板件读码报告
 * 5.[接口]批次完工报告
 * 6.[接口]请求卸料报告
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class EapTlGhSendFlowSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapTlGhInterfCommon eapTlGhInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //1.[接口]请求叫料
    public JSONObject LoadRequestReport(String station_code, String port_code, String request_mode) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "LoadRequestReport";
        String esbInterfCode = "LoadRequestReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);
            postParas.put("portid", port_code);
            postParas.put("requestmode", request_mode);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.[接口]准备生产请求
    public JSONObject LotReadyReport(String station_code, String port_code,
                                     String lot_num, String pallet_num, String plan_lot_num,
                                     String recipe_id, String vehicleno_eq) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "LotReadyReport";
        String esbInterfCode = "LotReadyReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);
            postParas.put("portid", port_code);
            postParas.put("lotno", lot_num);
            postParas.put("number", plan_lot_num);
            postParas.put("recipeid", recipe_id);
            postParas.put("carrierid", pallet_num);
            postParas.put("lotfalg", vehicleno_eq);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.[接口]开始生产请求
    public JSONObject LotStartReport(String station_code, String port_code,
                                     String lot_num, String pallet_num, String plan_lot_num,
                                     String recipe_id, String vehicleno_eq, String requestmode) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "LotStartReport";
        String esbInterfCode = "LotStartReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);
            postParas.put("portid", port_code);
            postParas.put("lotno", lot_num);
            postParas.put("number", plan_lot_num);
            postParas.put("recipeid", recipe_id);
            postParas.put("carrierid", pallet_num);
            postParas.put("lotfalg", vehicleno_eq);
            postParas.put("requestmode", requestmode);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.[接口]板件读码报告
    public JSONObject PanelReadReport(String station_code, String port_code,
                                      String lot_num, String panel_barcode, String inspect_flag,
                                      String mid_flag, String first_flag, String lot_type,
                                      String recipe_id, String panel_result) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "PanelReadReport";
        String esbInterfCode = "PanelReadReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);
            postParas.put("lotno", lot_num);
            postParas.put("pnlid", panel_barcode);
            postParas.put("firstflag", inspect_flag);//首件标识
            postParas.put("Insp", mid_flag);
            postParas.put("endtype", first_flag);
            postParas.put("lottype", lot_type);
            postParas.put("recipeid", recipe_id);
            postParas.put("result", panel_result);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //5.[接口]批次完工报告
    public JSONObject LotEndReport(String station_code, String port_code,
                                   String lot_num, String pallet_num, String firstmode, Integer finish_count,
                                   String recipe_id, String vehicleno_eq, JSONArray finish_result,
                                   String requestmode, String last_lot_flag,String heatnumber) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "LotEndReport";
        String esbInterfCode = "LotEndReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            if (last_lot_flag == null) last_lot_flag = "N";
            String endtype = "0";
            if (firstmode.equals("2")) {
                endtype = "1";
            } else if (!last_lot_flag.equals("Y")) {
                endtype = "2";
            }
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);
            postParas.put("portid", port_code);
            postParas.put("lotno", lot_num);
            postParas.put("number", finish_count);
            postParas.put("recipeid", recipe_id);
            postParas.put("carrierid", pallet_num);
            postParas.put("vehiclenoEQ", vehicleno_eq);
            postParas.put("pnlids", finish_result);
            postParas.put("endtype", endtype);
            postParas.put("requestmode", requestmode);
            postParas.put("heartnumder", heatnumber);
            requestParas = postParas.toString();

            String PbjUnloadFlag = cFuncDbSqlResolve.GetParameterValue("PbjUnloadFlag");
            if (PbjUnloadFlag.equals("Y")) {
                esbInterfCode = "LotEndReportPbj";
            }
            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //6.[接口]请求卸料报告
    public JSONObject UnLoadRequestReport(String station_code, String port_code, String pre_call, String unload_type,
                                          JSONArray arrlist, String requestmode) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "UnLoadRequestReport";
        String esbInterfCode = "UnLoadRequestReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);
            postParas.put("portid", port_code);
            postParas.put("unloadtype", unload_type);
            postParas.put("precall", pre_call);
            postParas.put("requestmode", requestmode);
            postParas.put("arrlist", arrlist);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //7.[接口]设备读板件ID请求批次信息
    public JSONObject PanelInfoRequestReport(String station_code, String panel_barcode) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "PanelInfoRequestReport";
        String esbInterfCode = "PanelInfoRequestReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);
            postParas.put("pnlid", panel_barcode);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //8.[接口]读载具ID报告事件
    public JSONObject CarriertLotInfoRequest(String station_code, String pallet_num, String port_code) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CarriertLotInfoRequest";
        String esbInterfCode = "CarriertLotInfoRequest";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, null, "");
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                throw new Exception(errorMsg);
            }
            String station_attr = lstStation.get(0).get("station_attr").toString();
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);
            postParas.put("carrierid", pallet_num);
            postParas.put("requestmode", station_attr.toLowerCase());
            postParas.put("portno", port_code);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //8.[接口]设备空闲端口数上报--配套机
    public JSONObject PortQtyReport(String station_code, JSONArray port_list) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "PortQtyReport";
        String esbInterfCode = "PortQtyReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            if (port_list == null)
                port_list = new JSONArray();
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);
            postParas.put("portqty", port_list.size());
            postParas.put("portlist", port_list);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject jsonResult = JSONObject.parseObject(jsonObjectBack);
            Integer rtn_code = jsonResult.getInteger("code");

            //4.更新端口上报状态
            String client_code = "";
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            if (aisMonitorModel.equals("AIS-PC")) {
                client_code = "LoadAis";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                client_code = "LoadAis_" + station_code;
            } else {
                errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //端口上报状态(0:初始状态;1:上报成功不再上报;2:上报失败继续上报)
            String tag_key = client_code + "/AisRcsStatus/PortQtyReportStatus";
            String tag_value = "";
            if (rtn_code == 200) {
                tag_value = "1";
            } else {
                tag_value = "2";
            }
            errorMsg = cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, tag_key, tag_value, true);
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(jsonResult);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //9.[接口]配套外层批完工报告--配套机
    public JSONObject LayerLotEndReport(String station_code, String port_code, String recipe_id,
                                        String heatnumber, String layerlot, String setsqty,
                                        String vehicleno_eq, JSONArray ink_lot_list) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "LayerLotEndReport";
        String esbInterfCode = "LayerLotEndReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            if (ink_lot_list == null)
                ink_lot_list = new JSONArray();
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("eqno", station_code);//设备编号
            postParas.put("portid", port_code);//端口号
            postParas.put("recipeid", recipe_id);//配方ID
            postParas.put("wcflag", "Y");//外层批完批标识
            postParas.put("heatnumber", heatnumber);//炉次
            postParas.put("layerlot", layerlot);//外层批次
            postParas.put("setsqty", setsqty);//配套套数
            postParas.put("vehiclenoEQ", vehicleno_eq);//分批标识
            postParas.put("lotlist", ink_lot_list);//内层批次信息
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = eapTlGhInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = eapTlGhInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
