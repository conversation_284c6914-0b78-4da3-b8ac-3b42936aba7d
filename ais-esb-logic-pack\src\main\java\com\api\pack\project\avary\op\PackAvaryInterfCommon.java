package com.api.pack.project.avary.op;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 鹏鼎接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Service
@Slf4j
public class PackAvaryInterfCommon {
    public static String GetStandJson(Boolean isOK, List<Map<String, Object>> itemList, String result, String errorMsg, long pageAllCount) {
        String returnResult = "";

        try {
            int code = 1;
            if (!isOK) {
                code = -1;
            }
            JSONObject json = new JSONObject();
            json.put("ReturnCode", Integer.valueOf(code));
            json.put("ReturnMsg", errorMsg);
            returnResult = json.toString();
        } catch (Exception var12) {
            returnResult = GetErrorJson(var12.getMessage());
        }

        return returnResult;
    }

    public static String GetErrorJson(String errorMsg) {
        String returnResult = "";
        JSONObject json = new JSONObject();
        json.put("ReturnCode", -1);
        json.put("ReturnMsg", errorMsg);
        returnResult = json.toString();
        return returnResult;
    }

}
