package com.api.dcs.project.xcmg;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 徐工定制化上料处理逻辑
 * 1.手工上料工位:查询当前选择机型，同时生成任务ID
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/xcmg/sl")
public class EapXcmgSlController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //上料工位任务查询
    @RequestMapping(value = "/DcsXcmgSlManualOnTaskSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsXcmgSlManualOnTaskSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/xcmg/sl/DcsXcmgSlManualOnTaskSel";
        String selectResult="";
        String errorMsg="";
        try{
            //1.创建任务号
            String taskNum="IN"+CFuncUtilsSystem.GetNowDateTime("yyMMddHHmmss");
            //2.查询当前选择的型号
            String sqlModelSel="select " +
                    "'"+taskNum+"' as task_num," +
                    "material_code,model_type,m_length,m_width,m_height,m_weight," +
                    "COALESCE(m_texture,'') m_texture " +
                    "from b_dcs_fmod_model " +
                    "where enable_flag='Y' and selected_flag='Y' " +
                    "order by model_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlModelSel,
                    false,request,apiRoutePath);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
