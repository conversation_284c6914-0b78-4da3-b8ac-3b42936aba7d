package com.api.eap.project.thailand.hs;

import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 泰国沪士EAP发送事件数据定义接口
 * 1.通用事件上报
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/hs/interf/send")
public class EapTlHsSendEventController {
    @Autowired
    private EapTlHsSendEventFunc eapTlHsSendEventFunc;

    //1.通用事件上报
    @RequestMapping(value = "/EventReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapTlHsComomEventReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/hs/interf/send/EventReport";
        String transResult="";
        String errorMsg="";
        try{
            String esbInterfCode=jsonParas.getString("esbInterfCode");
            JSONObject postParas=jsonParas.getJSONObject("postParas");
            eapTlHsSendEventFunc.ComomEventFunc(esbInterfCode,postParas);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "Exception:"+ex;
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
