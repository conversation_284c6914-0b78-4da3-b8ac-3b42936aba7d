package com.api.eap.core.agv;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 针对海康威视AGV标准接口
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
public class HikAgvController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //AGV请求搬入
    @RequestMapping(value = "/AisCenterAgv/agvCallback", method = {RequestMethod.POST, RequestMethod.GET})
    public String AisCenterAgv(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/core/agv/AisCenterAgv/agvCallback";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        String method = jsonParas.getString("method");
        String esbInterfCode02 = "";
        if (method.equals("arriveMachine")) esbInterfCode02 = "AgvAskIn";
        if (method.equals("end")) esbInterfCode02 = "AgvTellOut";
        if (method.equals("inFinish")) esbInterfCode02 = "AgvInFinish";
        JSONObject jbResult = agvSafeCheckEvent(jsonParas, request, apiRoutePath, esbInterfCode02);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //AGV请求搬入
    @RequestMapping(value = "/AisCenterAgvAskIn", method = {RequestMethod.POST, RequestMethod.GET})
    public String AisCenterAgvAskIn(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/core/agv/AisCenterAgvAskIn";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = agvSafeCheckEvent(jsonParas, request, apiRoutePath, "AgvAskIn");
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //AGV请求搬出
    @RequestMapping(value = "/AisCenterAgvTellOut", method = {RequestMethod.POST, RequestMethod.GET})
    public String AisCenterAgvTellOut(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/core/agv/AisCenterAgvTellOut";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = agvSafeCheckEvent(jsonParas, request, apiRoutePath, "AgvTellOut");
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        return responseParas;
    }

    //回调AGV接口,通知继续任务
    @RequestMapping(value = "/CoreAgvCallBack", method = {RequestMethod.POST, RequestMethod.GET})
    public String CoreAgvCallBack(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/core/agv/CoreAgvCallBack";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = agvContinueTask(jsonParas, request);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //处理AGV请求搬入搬出接口
    private JSONObject agvSafeCheckEvent(JSONObject jsonParas, HttpServletRequest request,
                                         String apiRoutePath, String esbInterfCode) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbBack = new JSONObject();
        String reqCode = CFuncUtilsSystem.CreateUUID(false);
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            //获取AGV传入参数
            String robotCode = jsonParas.getString("robotCode");//AGV编码
            reqCode = jsonParas.getString("reqCode");//唯一UUID
            String currentPositionCode = jsonParas.getString("currentPositionCode");//当前位置
            String taskCode = jsonParas.getString("taskCode");//任务号
            String method = jsonParas.getString("method");//任务类型(IN/OUT)
            String palletType = jsonParas.getString("palletType");//载具类型
            if (robotCode == null) robotCode = "";
            if (reqCode == null) reqCode = CFuncUtilsSystem.CreateUUID(false);
            if (taskCode == null) taskCode = "";
            if (method == null) method = "";
            if (palletType == null) palletType = "";
            String taskStatus = "";

            //1.判断AGV数据是否正确
            //无锡健鼎项目：putbox：搬入请求（上料请求）
            //applyputout：进入完成（到达机台内)
            //end：结束(离开完成）
            //getbox:卸除请求（下料请求）
            //applygetout:进入完成（到达机台内）
            //end：结束（下料完成）
            if (!method.equals("IN") && !method.equals("OUT") &&
                    !method.equals("arriveMachine") && !method.equals("end") && !method.equals("inFinish") &&
                    !method.equals("start") && !method.equals("cancel") && !method.equals("getbox") &&
                    !method.equals("applygetout") && !method.equals("putbox") && !method.equals("applyputout")) {
                errorMsg = "AGV请求method只能为IN或者OUT或者arriveMachine或者end或者inFinish或者start或者cancel或者getbox或者applygetout或者putbox或者applyputout";
                jbBack = new JSONObject();
                jbBack.put("code", -1);
                jbBack.put("message", errorMsg);
                jbBack.put("reqCode", reqCode);
                jbBack.put("data", "");
                responseParas = jbBack.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if (method.equals("IN") || method.equals("arriveMachine") || method.equals("getbox") || method.equals("putbox"))
                taskStatus = "ASK_IN";
            else if (method.equals("OUT") || method.equals("end")) taskStatus = "TELL_OUT";
            else taskStatus = "IN_FINISH";


            //2.根据AGV地码查找工位与端口信息
            String sqlStationPort = "select b.station_code,a.port_code,a.port_index,COALESCE(b.station_attr,'') station_attr " +
                    "from a_eap_fmod_station_port a inner join sys_fmod_station b " +
                    "on a.station_id=b.station_id " +
                    "where a.agv_position='" + currentPositionCode + "' and a.enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> lstStationPort = cFuncDbSqlExecute.ExecSelectSql("AGV", sqlStationPort,
                    false, request, apiRoutePath);
            if (lstStationPort == null || lstStationPort.size() <= 0) {
                errorMsg = "未能根据AGV地码{" + currentPositionCode + "}在AIS查找地码配置工位端口信息";
                jbBack = new JSONObject();
                jbBack.put("code", -2);
                jbBack.put("message", errorMsg);
                jbBack.put("reqCode", reqCode);
                jbBack.put("data", "");
                responseParas = jbBack.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String station_code = lstStationPort.get(0).get("station_code").toString();
            String port_index = lstStationPort.get(0).get("port_index").toString();
            String station_attr = lstStationPort.get(0).get("station_attr").toString();
            if (!station_attr.equals("Load") && !station_attr.equals("UnLoad")) {
                errorMsg = "AIS未配置工位{" + station_code + "}属性为Load或者UnLoad";
                jbBack = new JSONObject();
                jbBack.put("code", -3);
                jbBack.put("message", errorMsg);
                jbBack.put("reqCode", reqCode);
                jbBack.put("data", "");
                responseParas = jbBack.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }


            //获取当前模式
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String tagAgvNum = "";
            String tagAgvTaskNum = "";
            String tagAgvPalletType = "";
            String tagAgvReqCode = "";
            String tagAgvTaskStatus = "";
            String tagAgvStatus = ""; //modified by chenru 20240419 呼叫AGV，AGV响应后先缓存起来，流程图接收到后马上复位
            String tagAisReqControl = ""; //modified by chenru 20241225 无锡健鼎 ，agv触发后写入plc请求
            String tagAGVRequestMode = ""; //modified by chenru 20241225 无锡健鼎 ，工位AGV请求模式
            String tagClientGroup = "";
            if (aisMonitorModel.equals("AIS-PC")) {
                if (station_attr.equals("Load")) {
                    tagClientGroup = "LoadAgv/AgvStatus" + port_index;
                    tagAisReqControl = "LoadPlc/AisReqControl" + port_index + "/AskIn";
                    tagAGVRequestMode = "LoadPlc/PlcStatus/AGVRequestMode" + port_index;
                } else {
                    tagClientGroup = "UnLoadAgv/AgvStatus" + port_index;
                    tagAisReqControl = "UnLoadPlc/AisReqControl" + port_index + "/AskPalletOut";
                    tagAGVRequestMode = "UnLoadPlc/PlcStatus/AGVRequestMode" + port_index;
                }
            } else {
                if (station_attr.equals("Load")) {
                    tagClientGroup = "LoadAgv_" + station_code + "/AgvStatus" + port_index;
                    tagAisReqControl = "LoadPlc_" + station_code + "/AisReqControl" + port_index + "/AskIn";
                    tagAGVRequestMode = "LoadPlc_" + station_code + "/PlcStatus/AGVRequestMode" + port_index;
                } else {
                    tagClientGroup = "UnLoadAgv_" + station_code + "/AgvStatus" + port_index;
                    tagAisReqControl = "UnLoadPlc_" + station_code + "/AisReqControl" + port_index + "/AskPalletOut";
                    tagAGVRequestMode = "UnLoadPlc_" + station_code + "/PlcStatus/AGVRequestMode" + port_index;
                }
            }
            tagAgvNum = tagClientGroup + "/AgvNum";
            tagAgvTaskNum = tagClientGroup + "/AgvTaskNum";
            tagAgvPalletType = tagClientGroup + "/AgvPalletType";
            tagAgvReqCode = tagClientGroup + "/AgvReqCode";
            tagAgvTaskStatus = tagClientGroup + "/AgvTaskStatus";
            tagAgvStatus = tagClientGroup + "/AgvStatus";
            String tagList = tagAgvNum + "," + tagAgvTaskNum + "," + tagAgvPalletType + "," + tagAgvReqCode + "," + tagAgvTaskStatus;
            ;
            String tagWriteValue = robotCode + "&" + taskCode + "&" + palletType + "&" + reqCode + "&" + taskStatus;
            if (taskStatus.equals("ASK_IN")) {
                //如果是ASK_IN，写入缓存信息
                String cache = "1," + robotCode + "," + taskCode + "," + palletType + "," + reqCode + "," + taskStatus;

                //projectCode != SDQH 时，写入TagList时包含tagAgvStatus
                //查询系统参数 ProjectCode，判断是否为山东清河项目，如果是山东清河项目，则去掉  tagAgvStatus  modified by 朱一鸣 2024607
                String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
                if (projectCode.equals("WXJD")) {
                    String tagAGVRequestValue = "";
                    if (method.equals("putbox")) tagAGVRequestValue = "1";
                    if (method.equals("getbox")) tagAGVRequestValue = "2";
                    //无锡健鼎需要给plc写请求进入信号
                    tagList = tagAgvNum + "," + tagAgvTaskNum + "," + tagAgvPalletType + "," +
                            tagAgvReqCode + "," + tagAgvTaskStatus + "," + tagAisReqControl + "," + tagAGVRequestMode;
                    tagWriteValue = robotCode + "&" + taskCode + "&" + palletType + "&" + reqCode + "&" + taskStatus + "&1&" +  tagAGVRequestValue;
                } else if (projectCode.equals("SDQH")) {
                    //山东清河项目不用写入 tagAgvStatus
                    tagList = tagAgvNum + "," + tagAgvTaskNum + "," + tagAgvPalletType + "," + tagAgvReqCode + "," + tagAgvTaskStatus;
                    tagWriteValue = robotCode + "&" + taskCode + "&" + palletType + "&" + reqCode + "&" + taskStatus;
                } else {
                    tagList = tagAgvNum + "," + tagAgvTaskNum + "," + tagAgvPalletType + "," + tagAgvReqCode + "," + tagAgvTaskStatus + "," + tagAgvStatus;
                    tagWriteValue = robotCode + "&" + taskCode + "&" + palletType + "&" + reqCode + "&" + taskStatus + "&" + cache;
                }
            }
            errorMsg = cFuncUtilsCellScada.WriteTagByStation("AGV", station_code, tagList, tagWriteValue, true);
            if (!errorMsg.equals("")) {
                jbBack = new JSONObject();
                jbBack.put("code", -4);
                jbBack.put("message", errorMsg);
                jbBack.put("reqCode", reqCode);
                jbBack.put("data", "");
                responseParas = jbBack.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            jbBack = new JSONObject();
            jbBack.put("code", 0);
            jbBack.put("message", "调用接口成功");
            jbBack.put("reqCode", reqCode);
            jbBack.put("data", "");
            responseParas = jbBack.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "");
        } catch (Exception ex) {
            errorMsg = "AIS处理AGV请求搬入搬出接口未知异常:" + ex.getMessage();
            jbBack = new JSONObject();
            jbBack.put("code", -99);
            jbBack.put("message", errorMsg);
            jbBack.put("reqCode", reqCode);
            jbBack.put("data", "");
            responseParas = jbBack.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //AGV任务继续
    private JSONObject agvContinueTask(JSONObject jsonParas, HttpServletRequest request) throws Exception {
        JSONObject jbResult = new JSONObject();
        String esbInterfCode = "AgvCallBack";
        String errorMsg = "";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //参数
            String req_code = jsonParas.getString("req_code");//唯一编码
            String task_num = jsonParas.getString("task_num");//任务号
            String method = jsonParas.getString("method");//任务类型

            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jsonSend = new JSONObject();
            jsonSend.put("reqCode", req_code);
            jsonSend.put("taskCode", task_num);
            if (method != null && !method.equals("")) {
                jsonSend.put("method", method);
            }
            requestParas = jsonSend.toString();
            JSONObject jsonObjectEap = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, jsonSend);
            responseParas = jsonObjectEap.toString();
            Integer backCode = jsonObjectEap.getInteger("code");
            String backMsg = jsonObjectEap.getString("message");
            if (backCode != 0) {
                errorMsg = "通知AGV继续任务接口返回失败：" + backMsg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //调用成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "调用成功");
        } catch (Exception ex) {
            errorMsg = "AIS通知AGV任务继续接口发生未知异常:" + ex.getMessage();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
