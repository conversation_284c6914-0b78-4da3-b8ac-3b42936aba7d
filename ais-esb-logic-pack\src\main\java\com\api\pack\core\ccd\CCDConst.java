package com.api.pack.core.ccd;

import com.api.base.Const;

public interface CCDConst
{
    String BLANK = Const.BLANK;
    String OK = "OK";
    String NG = "NG";
    String NO_READ = "NoRead";

    String NONE = "None";
    String SINGLE = "Single";
    String DOUBLE = "Double";

    String SET = "Set";
    String PCS = "Pcs";
    String CHAR = "Char";
    String FRONT = "Front";
    String BACK = "Back";
    String CONTENT = "Content";
    String SET_FRONT_CONTENT = SET + FRONT + CONTENT;
    String SET_BACK_CONTENT = SET + BACK + CONTENT;
    String NUM = "Num";
    String MARK = "DirMarkChkRtl";
    String HAS_DIR_MARK = "HasDirMark";
    String DIRECTION = "Direction";
    String MSG_LIST = "MsgList";
    String IS_XOUT = "IsXout";
    String QRC = "QRC";
    String QRC_LEVEL = "QRCLevel";
    String PART_NUMBER = "PartNumber";
    String DATE_CODE = "DateCode";
    String LOT_NUM = "LotNum";
    String TRANSMIT_ID = "TransmitID"; // CCD线扫流水号
    String XOUT_QTY = "XoutQty";
    String LAYOUT_QTY = "LayoutQty";
    String CUSTOMER_QRC1 = "CustomerQRC1"; // 客户QRC1
    String CUSTOMER_QRC2 = "CustomerQRC2"; // 客户QRC2
    String REPAIR_LABEL = "RepairLabel"; // 修复标签
    String FLOW_ERROR = "FlowError"; // 流程错误
    String FLOW_ERROR_MSG = "FlowErrorMsg"; // 流程错误信息
    String PCS_MSG_LIST = "PcsMsgList"; // PCS信息列表
}
