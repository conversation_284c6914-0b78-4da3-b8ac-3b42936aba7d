package com.api.pack.core.op;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.base.Const;
import com.api.base.EventPublisher;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.pack.core.pile.Pile;
import com.api.pack.core.pile.PileRemoveEvent;
import com.api.pack.core.pile.PileService;
import com.api.pack.core.station.StationFlow;
import com.api.pack.core.station.StationFlowService;
import com.api.pack.core.station.StationQuality;
import com.api.pack.core.station.StationQualityService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 站点
 * 1.记录站点流程数据
 * 2.记录站点质量数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@RestController
@RequestMapping("/pack/core/op")
@AllArgsConstructor
@Slf4j
public class PackCoreOpStationController
{
    private final CFuncUtilsCellScada cFuncUtilsCellScada;
    private final StationFlowService stationFlowService;
    private final StationQualityService stationQualityService;
    private final PileService pileService;
    private final PackCoreService packCoreService;

    @RequestMapping(value = "/RecordStationFlow", produces = "application/json;charset=utf-8", method = {
            RequestMethod.POST, RequestMethod.GET
    })
    public String RecordStationFlow(@RequestBody(required = false) JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/core/op/RecordStationFlow";
        String tranResult;
        try
        {
            String stationCode = jsonParas.getString("station_code");
            JSONObject data = jsonParas.getJSONObject("data");
            if (data == null)
            {
                throw new RuntimeException("参数错误");
            }
            StationFlow record = new StationFlow(data);
            String stationFlowCode = record.getStationCode();
            String planId = record.getPlanId();
            String lotNum = record.getLotNum();
            String serialNum = record.getSerialNum();
            StationFlow flow = StationFlow.byStationCodePlanIdAndLotNumAndSerialNum(stationFlowCode, planId, lotNum, serialNum, this.stationFlowService);
            if (flow != null)
            {
                record.setId(flow.getId());
            }
            if (record.isHead())
            {
                Pile pile = Pile.byPileBarcodeAndPlanId(serialNum, planId, this.pileService);
                EventPublisher.publish(new PileRemoveEvent(pile));
                if (Pile.isEmptyByPlanId(planId, this.pileService))
                {
                    this.packCoreService.stop(stationCode);
                }
            }
            this.stationFlowService.save(record);
            JSONObject result = new JSONObject();
            result.put("station_flow_id", record.getId());
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result.toJSONString(), apiRoutePath, 1);
        }
        catch (Exception ex)
        {
            String errorMsg = "RecordStationFlow error: " + ex.getMessage();
            log.error(errorMsg, ex);
            tranResult = CFuncUtilsLayUiResut.GetStandJson(false, null, null, errorMsg, 0);
        }
        return tranResult;
    }

    @RequestMapping(value = "/RecordStationQuality", produces = "application/json;charset=utf-8", method = {
            RequestMethod.POST, RequestMethod.GET
    })
    public String RecordStationQuality(@RequestBody(required = false) JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/core/op/RecordStationQuality";
        String tranResult;
        try
        {
            String stationCode = jsonParas.getString("station_code");
            JSONObject data = jsonParas.getJSONObject("data");
            if (data == null)
            {
                throw new RuntimeException("参数错误");
            }
            JSONObject flowInfo = data.getJSONObject("flow_info");
            if (flowInfo == null)
            {
                throw new RuntimeException("参数错误，缺少flow_info");
            }
            JSONObject taskInfo = data.getJSONObject("task_info");
            if (taskInfo == null)
            {
                throw new RuntimeException("参数错误，缺少task_info");
            }
            String stationFlowId = taskInfo.getString("station_flow_id");
            String qualitySign = taskInfo.getString("quality_sign");
            StationFlow stationFlow = StationFlow.byId(stationFlowId, this.stationFlowService);
            boolean qualified = true;
            List<StationQuality> records = new ArrayList<>();
            JSONArray qualities = taskInfo.getJSONArray("qualities");
            if (qualities != null && !qualities.isEmpty())
            {
                for (int i = 0; i < qualities.size(); i++)
                {
                    JSONObject quality = qualities.getJSONObject(i);
                    StationQuality record = new StationQuality(flowInfo, taskInfo, quality);
                    records.add(record);
                    if (record.isUnqualified())
                    {
                        qualified = false;
                    }
                }
            }
            if (stationFlow != null)
            {
                String sfStationCode = stationFlow.getStationCode();
                JSONObject tagMap = taskInfo.getJSONObject("tag_map");
                if (tagMap != null && !tagMap.isEmpty())
                {
                    String qualityTagKeys = tagMap.getString(sfStationCode);
                    JSONArray qualityTags = this.readTags(stationCode, qualityTagKeys);
                    if (qualityTags != null && !qualityTags.isEmpty())
                    {
                        for (int i = 0; i < qualityTags.size(); i++)
                        {
                            JSONObject qualityTag = qualityTags.getJSONObject(i);
                            String tagKey = qualityTag.getString("tag_key");
                            String tagValue = qualityTag.getString("tag_value");
                            StationQuality record = new StationQuality(flowInfo, taskInfo, tagKey, tagValue);
                            records.add(record);
                            if (record.isUnqualified())
                            {
                                qualified = false;
                            }
                        }
                    }
                }
                stationFlow.setQualitySign(qualitySign);
                if (qualitySign == null)
                {
                    stationFlow.setQualitySign(qualified ? Const.FLAG_Y : Const.FLAG_N);
                }
                this.stationFlowService.save(stationFlow);
            }
            if (!records.isEmpty())
            {
                this.stationQualityService.saveAll(records);
            }
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "保存成功", apiRoutePath, 1);
        }
        catch (Exception ex)
        {
            String errorMsg = "RecordStationQuality error: " + ex.getMessage();
            log.error(errorMsg, ex);
            tranResult = CFuncUtilsLayUiResut.GetStandJson(false, null, null, errorMsg, 0);
        }
        return tranResult;
    }

    private JSONArray readTags(String stationCode, String tagKeys) throws Exception
    {
        return cFuncUtilsCellScada.ReadTagByStation(stationCode, tagKeys);
    }

    private String readOneTagValue(String stationCode, String tagKey) throws Exception
    {
        JSONArray jsonArray = readTags(stationCode, tagKey);
        if (jsonArray != null && !jsonArray.isEmpty())
        {
            return jsonArray.getJSONObject(0).getString("tag_value");
        }
        throw new Exception("未读取到标签{" + tagKey + "}对应值");
    }
}
