package com.api.mes.project.byd;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Map;

/**
 * <p>
 * 比亚迪定制化条码生成
 * 1.打刻码
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@Service
@Slf4j
public class MesBydBarCodeCreateFunc {

    //生成比亚迪打刻码
    //生成规则：如 BYD DHT31-2146010E K2300001
    public String CreateDkBarCode(Integer increaNum, Map<String, Object> mapRule) throws Exception {
        String barCode = "";
        String attr_field1 = mapRule.get("attr_field1").toString();//BYD DHT31-2146010E --5种机型对应(举例)
        String attr_field2 = mapRule.get("attr_field2").toString();//K --工厂代码(举例)
        String attr_field3 = mapRule.get("attr_field3").toString();
        String attr_field4 = mapRule.get("attr_field4").toString();
        String attr_field5 = mapRule.get("attr_field5").toString();
        String attr_field6 = mapRule.get("attr_field6").toString();
        try {
            //23--年
            //获取当前年份
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            String yearHeader = String.valueOf(year).substring(2);

            barCode = attr_field1 + attr_field2 + yearHeader + String.format("%05d", increaNum);
        } catch (Exception ex) {
            throw ex;
        }
        return barCode;
    }

    //生成比亚迪打印码
    //生成规则：如 0104116P0M819F0001
    public String CreatePrintBarCode(Integer increaNum, Map<String, Object> mapRule) throws Exception {
        String barCode = "";
        String attr_field1 = mapRule.get("attr_field1").toString();//01041 --供应商代码(举例)
        String attr_field2 = mapRule.get("attr_field2").toString();//16P0  --零部件代号(举例)
        String attr_field3 = mapRule.get("attr_field3").toString();//F --生产序列号前标识
        String attr_field4 = mapRule.get("attr_field4").toString();
        String attr_field5 = mapRule.get("attr_field5").toString();
        String attr_field6 = mapRule.get("attr_field6").toString();
        try {
            //M--年
            //8--月  0~9,A,B 0代表10月
            //4--日  01~31
            //获取当前年份
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;
            int day = cal.get(Calendar.DATE);
            String yearHeader = "";
            String monthHeader = "";
            String dayHeader = "";
            switch (year) {
                case 2023:
                    yearHeader = "P";
                    break;
                case 2024:
                    yearHeader = "R";
                    break;
                case 2025:
                    yearHeader = "S";
                    break;
                case 2026:
                    yearHeader = "T";
                    break;
                case 2027:
                    yearHeader = "V";
                    break;
                case 2028:
                    yearHeader = "W";
                    break;
                case 2029:
                    yearHeader = "X";
                    break;
                case 2030:
                    yearHeader = "Y";
                    break;
                case 2031:
                    yearHeader = "1";
                    break;
                case 2032:
                    yearHeader = "2";
                    break;
                case 2033:
                    yearHeader = "3";
                    break;
                case 2034:
                    yearHeader = "4";
                    break;
                case 2035:
                    yearHeader = "5";
                    break;
                case 2036:
                    yearHeader = "6";
                    break;
                case 2037:
                    yearHeader = "7";
                    break;
                case 2038:
                    yearHeader = "8";
                    break;
                case 2039:
                    yearHeader = "9";
                    break;
                case 2040:
                    yearHeader = "0";
                    break;
            }
            //月
            if (month <= 9) monthHeader = String.valueOf(month);
            else if (month == 10) monthHeader = "0";
            else if (month == 11) monthHeader = "A";
            else if (month == 12) monthHeader = "B";
            //天
            dayHeader = String.format("%02d", day);
            barCode = attr_field1 + attr_field2 + yearHeader + monthHeader + dayHeader + attr_field3 + String.format("%04d", increaNum);
        } catch (Exception ex) {
            throw ex;
        }
        return barCode;
    }
}
