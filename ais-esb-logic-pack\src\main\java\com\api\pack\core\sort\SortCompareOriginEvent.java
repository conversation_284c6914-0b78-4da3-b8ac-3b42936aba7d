package com.api.pack.core.sort;


import lombok.EqualsAndHashCode;
import org.springframework.context.ApplicationEvent;
import org.springframework.util.ObjectUtils;

@EqualsAndHashCode(callSuper = true)
public class SortCompareOriginEvent extends ApplicationEvent
{
    public SortCompareOriginEvent(Object source)
    {
        super(source);
    }

    @Override
    public SortCompareOrigin getSource()
    {
        return (SortCompareOrigin) super.getSource();
    }

    public boolean isSet()
    {
        return this.getSource().getSrc().getIndex() == null // 事件源的来源数据的索引为空
                && this.getSource().getDst().getIndex() == null; // 事件源的目标数据的索引为空
    }

    public boolean check()
    {
        return this.getSource() != null // 事件源不为空
                && this.getSource().getSrc() != null // 事件源的源数据不为空
                && this.getSource().getDst() != null // 事件源的目标数据不为空
                && this.getSource().getPlan() != null // 事件源的计划不为空
                && this.getSource().getRule() != null // 事件源的规则不为空
                && !ObjectUtils.isEmpty(this.getSource().getCode()); // 事件源的编码不为空
    }

    public boolean checkXOut()
    {
        return this.check() // 事件检测
                && this.getSource().getSrc().getCategory() != null // 事件源的来源数据的类别不为空
                && this.getSource().getDst().getCategory() != null // 事件源的目标数据的类别不为空
                && SortConst.X_OUT.equals(this.getSource().getCode()); // 事件源的编码为XoutSort
    }

    public boolean checkLevel()
    {
        return this.check() // 事件检测
                && this.getSource().getCode().endsWith(SortConst.LEVEL); // 事件源的编码以LevelSort结尾
    }

    public boolean checkDirection()
    {
        return this.check() // 事件检测
                && SortConst.BOARD_DIRECTION.equals(this.getSource().getCode()); // 事件源的编码为BoardDirectionSort
    }

    public boolean checkBarcodeLength()
    {
        return this.check() // 事件检测
                && this.isSet() // SET
                && SortConst.BAR_LENGTH.equals(this.getSource().getCode()); // 事件源的编码为BarLengthSort
    }

    public boolean checkBarcodeCase()
    {
        return this.check() // 事件检测
                && this.isSet() // SET
                && SortConst.BAR_CASE.equals(this.getSource().getCode()); // 事件源的编码为BarCaseSort
    }

    public boolean checkRepeatedCode()
    {
        return this.check() // 事件检测
                && this.isSet() // SET
                && SortConst.MULTI_CODE.equals(this.getSource().getCode()); // 事件源的编码为MultiCodeSort
    }

    public boolean checkETPass()
    {
        return this.check() // 事件检测
                && this.isSet() // SET
                && SortConst.ET_PASS.equals(this.getSource().getCode()); // 事件源的编码为ETPassSort
    }
}
