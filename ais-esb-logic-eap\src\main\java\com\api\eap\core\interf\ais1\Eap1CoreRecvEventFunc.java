package com.api.eap.core.interf.ais1;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * EAP接受事件功能函数
 * 1.EapCoreInterfControlStop:接受EAP下发控制启动与停止
 * 2.EapCoreInterfRecvHmiShow:接受EAP下发控制HMI文本显示
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class Eap1CoreRecvEventFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private Eap1CoreInterfCommon eap1CoreInterfCommon;

    //1.接受EAP下发控制启动与停止
    public JSONObject EapCoreInterfControlStop(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "EapCoreInterfControlStop";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 0;
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            request_uuid = jsonParas.getString("request_uuid");
            String station_code = jsonParas.getString("station_code");
            String control_value = jsonParas.getString("control_value");

            //1.判断工位是否存在
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + station_code + "' " +
                    "and enable_flag='Y'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                code = -1;
                errorMsg = "传递的设备编号{" + station_code + "}不存在AIS设备基础数据,AIS系统拒绝执行";
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String station_attr = itemListStation.get(0).get("station_attr").toString();

            //2.判断控制指令是否正确
            if (!control_value.equals("RUN") && !control_value.equals("STOP")) {
                code = -2;
                errorMsg = "EAP远程控制指令只能为RUN或者STOP,AIS系统拒绝执行";
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //3.写入到PLC
            String control_value_final = "1";//1停止放板；2继续放板
            if (control_value.equals("RUN")) control_value_final = "2";
            errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr, "Plc",
                    "PlcStatus", "AisStopWorkRequst", "EAP", control_value_final, true);
            if (!errorMsg.equals("")) {
                code = -3;
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, "");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.接受EAP远程控制HMI文本显示
    public JSONObject EapCoreInterfRecvHmiShow(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "EapCoreInterfRecvHmiShow";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 0;
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            request_uuid = jsonParas.getString("request_uuid");
            String station_code = jsonParas.getString("station_code");
            Integer hmi_msg_code = jsonParas.getInteger("hmi_msg_code");
            String hmi_msg = jsonParas.getString("hmi_msg");
            String hmi_dlg_flag = jsonParas.getString("hmi_dlg_flag");

            //1.判断工位是否存在
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + station_code + "' " +
                    "and enable_flag='Y'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                code = -2;
                errorMsg = "传递的设备编号{" + station_code + "}不存在AIS设备基础数据,AIS系统拒绝执行";
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String station_attr = itemListStation.get(0).get("station_attr").toString();

            //3.写入到PLC
            String hmi_dlg_code = "1";//1文本、2弹窗
            if (hmi_dlg_flag.equals("Y")) hmi_dlg_code = "2";
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String AisHmiShowRequstTag = "";
            String AisHmiShowMsgCodeTag = "";
            String AisHmiShowTypeTag = "";
            if (aisMonitorModel.equals("AIS-PC")) {
                AisHmiShowRequstTag = station_attr + "Plc" + "/PlcStatus/AisHmiShowRequst";
                AisHmiShowMsgCodeTag = station_attr + "Plc" + "/PlcStatus/AisHmiShowMsgCode";
                AisHmiShowTypeTag = station_attr + "Plc" + "/PlcStatus/AisHmiShowType";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                AisHmiShowRequstTag = station_attr + "Plc_" + station_code + "/PlcStatus/AisHmiShowRequst";
                AisHmiShowMsgCodeTag = station_attr + "Plc_" + station_code + "/PlcStatus/AisHmiShowMsgCode";
                AisHmiShowTypeTag = station_attr + "Plc_" + station_code + "/PlcStatus/AisHmiShowType";
            }
            String writeTags = AisHmiShowMsgCodeTag + "," + AisHmiShowTypeTag + "," + AisHmiShowRequstTag;
            String writeValues = hmi_msg_code + "&" + hmi_dlg_code + "&1";
            errorMsg = cFuncUtilsCellScada.WriteTagByStation("EAP", station_code, writeTags, writeValues, true);
            if (!errorMsg.equals("")) {
                code = -3;
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, "");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.AIS任务取消(AIS发布)
    public JSONObject EapCoreInterfCancelTask(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "EapCoreInterfCancelTask";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 0;
        String apsPlanTable = "a_eap_aps_plan";
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            request_uuid = jsonParas.getString("request_uuid");
            String station_code = jsonParas.getString("station_code");
            String lot_num = jsonParas.getString("lot_num");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String cancel_msg = jsonParas.getString("cancel_msg");

            //1.判断工位是否存在
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + station_code + "' " +
                    "and enable_flag='Y'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                code = -1;
                errorMsg = "传递的设备编号{" + station_code + "}不存在AIS设备基础数据,AIS系统拒绝执行";
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            long station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr = itemListStation.get(0).get("station_attr").toString();

            //2.判断任务是否存在
            String[] lot_num_list = lot_num.split(",");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").in(lot_num_list));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            Update updateBigData = new Update();
            updateBigData.set("lot_status", "CANCEL");
            updateBigData.set("group_lot_status", "CANCEL");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("attribute1", cancel_msg);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, "");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "任务取消成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.AIS通讯状态测试(AIS发布)
    public JSONObject EapCoreInterfConnTest(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "EapCoreInterfConnTest";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 0;
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            request_uuid = jsonParas.getString("request_uuid");
            String station_code = jsonParas.getString("station_code");

            //1.判断工位是否存在
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + station_code + "' " +
                    "and enable_flag='Y'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                code = -1;
                errorMsg = "传递的设备编号{" + station_code + "}不存在AIS设备基础数据,AIS系统拒绝执行";
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, "");
            JSONObject response = JSONObject.parseObject(responseParas);
            response.put("server_time", CFuncUtilsSystem.GetNowDateTime(""));
            responseParas = response.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "AIS通讯状态测试成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid, code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

}
