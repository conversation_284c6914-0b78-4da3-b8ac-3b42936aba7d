package com.api.dcs.project.fjrm.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.project.shzy.interf.DcsShzyWmsSendSubFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <p>
 * WMS发送流程功能函数
 * 1.通知MES库存变化
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
@Slf4j
public class DcsFjrmWmsSendFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private DcsFjrmWmsSendSubFunc dcsFjrmWmsSendSubFunc;
    @Resource
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Resource
    private CFuncUtilsRest cFuncUtilsRest;

    //1.通知MES库存变化
    public void SendMseStockChange(String task_num, String status,String statusMsg) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsFjrmWmsSendSubFunc.SendMseStockChange(task_num, status,statusMsg);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }
}
