package com.api.pack.core.ccd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.util.LinkedList;
import java.util.List;

@ApiModel(value = "CCDMesMessageMappingResult", description = "CCD MES 发送json下mappResult下面的内容字段")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CCDMappingResultMessage extends CCDMessage<CCDMappingResultMessageContentItem>
{
    @ApiModelProperty(value = "结果代码")
    @JsonProperty("Code")
    @JSONField(name = "Code")
    private String Code;

    @ApiModelProperty(value = "执行成功与否")
    @JsonProperty("Succ")
    @JSONField(name = "Succ")
    private String succ;

    @ApiModelProperty(value = "错误讯息")
    @JsonProperty("Message")
    @JSONField(name = "Message")
    private String message;

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public List<CCDMappingResultMessageContentItemDetail> getResults()
    {
        if (!ObjectUtils.isEmpty(this.getContent()))
        {
            this.getContent().getResults().forEach(item -> item.setEnableFlag(Const.FLAG_Y));
            return this.getContent().getResults();
        }
        return new LinkedList<>();
    }

    public static CCDMappingResultMessage fromJSON(String msg)
    {
        return JSON.parseObject(msg, CCDMappingResultMessage.class);
    }
}
