package com.api.pack.core.sort;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SortCompareVO implements Serializable
{
    @ApiModelProperty(value = "工位ID")
    @JsonProperty("station_id")
    @JSONField(name = "station_id")
    private String stationId;

    @ApiModelProperty(value = "工位代码")
    @JsonProperty("station_code")
    @JSONField(name = "station_code")
    private String stationCode;

    @ApiModelProperty(value = "CCD报文")
    @JsonProperty("ccd_data")
    @JSONField(name = "ccd_data")
    private String ccdData;
}
