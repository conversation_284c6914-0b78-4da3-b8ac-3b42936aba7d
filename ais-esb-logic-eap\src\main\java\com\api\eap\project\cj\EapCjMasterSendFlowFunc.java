package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 定颖EAP发送流程功能函数
 * 1.PortStatusChangeReport:[接口]端口状态发生变化推送到EAP
 * 2.CarrierIDReport:[接口]载具扫描上报验证
 * 3.CarrierStatusReport:[接口]开始/结束/取消/终止投板（收板）时上报
 * 4.CCDDataReport:[接口]CCD读码上报
 * 5.FetchOutReport:[接口]每放一片板需要上报（针对放板机）
 * 6.StoreInReport:[接口]每收一片板需要上报（针对收板机）
 * 7.JobCountReport:[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
 * 8.PanelDataUploadReport:[接口]每片Panel收放台車事件上報
 * 9.WIPTrackingReport:[接口]每一lot完板上报
 * 10.CarrierDataUploadReport:[接口]最后全部任务完板后上报
 * 11.JobDataCreateModifyReport:[接口]人工操作界面拿走一片板子(手工录入)
 * 12.JobRemoveRecoveryReport:[接口]人工操作界面放回板子(手工录入)
 * 13.EachPositionReport:[接口]暂存位置信息动态上报
 * 14.EachPanelDataDownLoad:[上下游接口]当片信息(上游传递给下游)
 * 15.BcOffLineLotDownLoad:[上下游接口]BC离线工单信息下发
 * 16.BcOffLineLoginInDownLoad:[上下游接口]BC离线同步登入
 * 17.LotFinishDownLoad:[上下游接口]传递到下游设备结批信息
 * 18.ProductionModeDownLoad:[上下游接口]首件状态下发
 * 19.HeartBeatCheckDownLoad:[上下游接口]检测下游心跳状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapCjMasterSendFlowFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapCjMasterSendFlowSubFunc eapCjMasterSendFlowSubFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.[接口]端口状态发生变化推送到EAP
    public void PortStatusChangeReport(String station_code, String port_code, String port_type,
                                       String port_status, String allow_sb_emptypallet_flag,
                                       String pallet_num, String pallet_all_count, String user_name,
                                       String port_ng_flag, String pallet_reload_flag) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.PortStatusChangeReport(station_code, port_code, port_type,
                port_status, allow_sb_emptypallet_flag, pallet_num, pallet_all_count, user_name, port_ng_flag, pallet_reload_flag);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        //写入点位改变端口状态
        String tagCode = "PortStatus" + Integer.parseInt(port_code);
        opCommonFunc.WriteCellOneTagValue(station_code, port_type, "Eap", "EapStatus", tagCode,
                "AIS", port_status, false);
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //2.[接口]载具扫描上报验证
    public JSONObject CarrierIDReport(String station_code, String pallet_num, String port_code, String port_type,
                                      int model, String production_mode, String manual_scan_flag,
                                      JSONArray lot_list, String first_scan_flag, String read_type) throws Exception {
        JSONObject response_body = null;
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.CarrierIDReport(station_code, pallet_num, port_code, port_type,
                model, production_mode, manual_scan_flag, lot_list, first_scan_flag, read_type);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String existCarryID = "";
        if (jbResult.containsKey("existCarryID")) existCarryID = jbResult.getString("existCarryID");
        if (jbResult.containsKey("responseBody")) response_body = jbResult.getJSONObject("responseBody");
        if (!existCarryID.equals("Y")) response_body = null;
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
        return response_body;
    }

    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //3.[接口]开始/结束/取消/终止投板（收板）时上报
    public void CarrierStatusReport(String station_code, String pallet_num, String task_status,
                                    JSONArray lot_list, String port_mode,
                                    String port_type, String port_code) throws Exception {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapCjMasterSendFlowSubFunc.CarrierStatusReport(station_code, pallet_num, task_status, lot_list, port_mode);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            if(!successFlag){
                throw new Exception("请求错误");
            }
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
        //写入点位改变端口状态
        String tagCode = "CarryStatus" + Integer.parseInt(port_code);
        opCommonFunc.WriteCellOneTagValue(station_code, port_type, "Eap", "EapStatus", tagCode,
                "AIS", task_status, false);
    }

    //4.[接口]CCD读码上报
    @Async
    public void CCDDataReport(String station_code, String panel_id, String ccd_no) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.CCDDataReport(station_code, panel_id, ccd_no);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //5.[接口]每放一片板需要上报（针对放板机）
    @Async
    public void FetchOutReport(String station_code, String panel_id, String port_code, String slot_no, String lot_num) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.FetchOutReport(station_code, panel_id, port_code, slot_no, lot_num);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //6.[接口]每收一片板需要上报（针对收板机）
    @Async
    public void StoreInReport(String station_code, String panel_id, String port_code, String slot_no, String lot_num) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.StoreInReport(station_code, panel_id, port_code, slot_no, lot_num);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //7.[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
    @Async
    public void JobCountReport(String station_code,String station_attr, int task_left_count, String port_no) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.JobCountReport(station_code,station_attr, task_left_count, port_no);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //8.[接口]每片Panel收放台車事件上報
    @Async
    public void PanelDataUploadReport(Long station_id, String station_code, String panel_id, String slot_no,
                                      JSONArray item_attr_list, String offline_flag, String local_flag,
                                      String ccd_no, String lot_num, String prod_id, String prod_version,
                                      String process_code, JSONObject attr_else,
                                      String holding_time, String over_times, Integer hold_result) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.PanelDataUploadReport(station_id, station_code, panel_id, slot_no,
                item_attr_list, offline_flag, local_flag, ccd_no, lot_num, prod_id, prod_version, process_code, attr_else,
                holding_time, over_times, hold_result);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //9.[接口]每一lot完板上报
    @Async
    public void WIPTrackingReport(Long station_id, String station_code, String user_name, String dept_id, String shift_id,
                                  String start_date, String end_date, String lot_num, int task_count, int lot_count,
                                  int lot_finish_count, String material_code, String lot_short_num, String lot_version,
                                  String prod_mode, String attribute1, String attribute2,
                                  JSONArray item_attr_list, String offline_flag, String local_flag, JSONObject attr_else) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.WIPTrackingReport(station_id, station_code, user_name, dept_id, shift_id,
                start_date, end_date, lot_num, task_count, lot_count, lot_finish_count, material_code, lot_short_num, lot_version,
                prod_mode, attribute1, attribute2, item_attr_list, offline_flag, local_flag, attr_else);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //10.[接口]最后全部任务完板后上报
    @Async
    public void CarrierDataUploadReport(Long station_id, String station_code, String pallet_num, String model,
                                        String splitseq, String lastSplit,
                                        JSONArray lot_list, String offline_flag, String local_flag) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.CarrierDataUploadReport(station_id, station_code, pallet_num, model,
                splitseq, lastSplit, lot_list, offline_flag, local_flag);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //11.[接口]人工操作界面拿走一片板子(手工录入)
    public void JobDataCreateModifyReport(String station_code, String panel_id, String port_code,
                                          String slot_no, String port_type, String user_name) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.JobDataCreateModifyReport(station_code, panel_id, port_code, slot_no, port_type, user_name);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //12.[接口]人工操作界面放回板子(手工录入)
    public void JobRemoveRecoveryReport(String station_code, String panel_id, String port_code,
                                        String slot_no, String port_type, String user_name, Boolean isRemove) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.JobRemoveRecoveryReport(station_code, panel_id, port_code, slot_no, port_type, user_name, isRemove);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //13.[接口]暂存位置信息动态上报
    public void EachPositionReport(String station_code, JSONArray panel_list) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.EachPositionReport(station_code, panel_list);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //14.[上下游接口]当片信息(上游传递给下游)
    @Async
    public void EachPanelDataDownLoad(String station_code, String lot_id, String panel_id, String panel_status) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.EachPanelDataDownLoad(station_code, lot_id, panel_id, panel_status);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //15.[上下游接口]BC离线工单信息下发
    public void BcOffLineLotDownLoad(String station_code, String lot_id, String prod_id, int pnl_count,
                                     String lot_short_id, JSONArray item_list) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.BcOffLineLotDownLoad(station_code, lot_id, prod_id, pnl_count, lot_short_id, item_list);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //16.[上下游接口]BC离线同步登入
    public void BcOffLineLoginInDownLoad(String station_code, String user_id, String dept_id,
                                         String shift_id, String nick_name) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.BcOffLineLoginInDownLoad(station_code, user_id, dept_id, shift_id, nick_name);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //17.[上下游接口]传递到下游设备结批信息
    @Async
    public void LotFinishDownLoad(String station_code, String lot_id, int lot_count, String lot_short_id) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.LotFinishDownLoad(station_code, lot_id, lot_count, lot_short_id);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //18.[上下游接口]首件状态下发
    public void ProductionModeDownLoad(String station_code, int inspectCount) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.ProductionModeDownLoad(station_code, inspectCount);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //19.[上下游接口]检测下游心跳状态
    public void HeartBeatCheckDownLoad(String station_code, int force_end_a, int force_end_b) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.HeartBeatCheckDownLoad(station_code, force_end_a, force_end_b);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    public void ReceiveReport(String station_code,String station_attr, String lot_num, String panel_barcode, String panel_index) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.ReceiveReport(station_code, station_attr,lot_num, panel_index, panel_barcode);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    public void SendOutReport(String station_code,String station_attr, String lot_num, String panel_barcode, String panel_index) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapCjMasterSendFlowSubFunc.SendOutReport(station_code, station_attr,lot_num, panel_barcode, panel_index);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

}
