package com.api.pack.core.fmod;

import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.IMybatisBasic;
import com.api.system.FastCodeGroup;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 出货地
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("a_pack_fmod_recipe_print_label")
public class FmodRecipeShipAddress implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("创建者")
    @CreatedBy
    @TableField("created_by")
    protected String createdBy;

    @ApiModelProperty("创建时间")
    @CreatedDate
    @TableField("creation_date")
    protected Date creationDate;

    @ApiModelProperty("最后更新者")
    @LastModifiedBy
    @TableField("last_updated_by")
    protected String lastUpdatedBy;

    @ApiModelProperty("最后更新时间")
    @LastModifiedDate
    @TableField("last_update_date")
    protected Date lastUpdateDate;

    @ApiModelProperty(value = "ID")
    @JsonProperty("shipaddress_id")
    @JSONField(name = "shipaddress_id")
    @TableId(value = "shipaddress_id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "名称")
    @JsonProperty("shipaddress_name")
    @JSONField(name = "shipaddress_name")
    @TableField(value = "shipaddress_name")
    private String name;

    @ApiModelProperty(value = "出货地明细")
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @TableField(exist = false)
    private List<FmodRecipeShipAddressDetail> details;

    public static FmodRecipeShipAddress byName(String name, FmodRecipeShipAddressService service)
    {
        return service.getByName(name);
    }

    public Map<Integer, FmodRecipeShipAddressDetail> getDetailsMapping(FastCodeGroup mesField)
    {
        Map<Integer, FmodRecipeShipAddressDetail> detailsMapping = new HashMap<>();
        Map<String, String> mesFieldMapping = mesField.getMapping();
        for (FmodRecipeShipAddressDetail detail : this.getDetails())
        {
            detail.setMesFieldValue(mesFieldMapping.get(detail.getMesField()));
            detailsMapping.put(detail.getIndex(), detail);
        }
        return detailsMapping;
    }
}
