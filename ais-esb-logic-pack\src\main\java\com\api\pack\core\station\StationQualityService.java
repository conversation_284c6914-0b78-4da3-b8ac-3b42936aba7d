package com.api.pack.core.station;

import com.api.base.IMongoBasicService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class StationQualityService extends IMongoBasicService<StationQuality, StationQualityRepository>
{
    public StationQualityService(StationQualityRepository repository)
    {
        super(repository);
    }
}
