package com.api.config;

import com.api.core.mongo.CoreMongoLogIndexFunc;
import com.api.dcs.core.mongo.DcsCoreMongoIndexFunc;
import com.api.eap.core.mongo.EapCoreMongoIndexFunc;
import com.api.mes.core.mongo.MesCoreMongoIndexFunc;
import com.api.pack.core.mongo.PackCoreMongoIndexFunc;
import com.api.pmc.core.mongo.PmcCoreMongoIndexFunc;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * AISCore大数据索引创建初始化
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */

@Configuration
public class CoreMongoIndexConfig {
    @Autowired
    private CoreMongoLogIndexFunc coreMongoLogIndexFunc;
    @Autowired
    private MesCoreMongoIndexFunc mesCoreMongoIndexFunc;
    @Autowired
    private EapCoreMongoIndexFunc eapCoreMongoIndexFunc;
    @Autowired
    private DcsCoreMongoIndexFunc dcsCoreMongoIndexFunc;
    @Autowired
    private PmcCoreMongoIndexFunc pmcCoreMongoIndexFunc;
    @Autowired
    private PackCoreMongoIndexFunc packCoreMongoIndexFunc;

    @Bean
    public void CoreMongoIndexInit() {
        coreMongoLogIndexFunc.CreateApiLogIndex();//创建ApiLog索引
        coreMongoLogIndexFunc.CreateSqlMmLogIndex();//创建SqlMmLog索引
        coreMongoLogIndexFunc.CreateJobLogIndex();//创建JobLog索引
        mesCoreMongoIndexFunc.CreateMesIndex();//创建MES索引
        eapCoreMongoIndexFunc.CreateEapIndex();//创建EAP索引
        dcsCoreMongoIndexFunc.CreateDcsIndex();//创建DCS索引
        pmcCoreMongoIndexFunc.CreatePmcIndex();//创建PMC索引
        packCoreMongoIndexFunc.CreatePackIndex();//创建PACK索引
    }
}
