package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pmc.core.flow.PmcCoreOnlineBase;
import com.api.pmc.core.flow.PmcCoreStationCrosBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 焊装订单接口
 * 1.焊装生产订单上传
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfHaMoController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private PmcCoreStationCrosBase pmcCoreStationCrosBase;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private PmcCoreOnlineBase pmcCoreOnlineBase;

    //1.焊装生产订单上传
    @Transactional
    @RequestMapping(value = "/PmcCoreHaDkMoUpload", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreHaDkMoUpload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreHaDkMoUpload";
        String selectResult = "";
        String errorMsg = "";
        try {
            log.info("焊装生产订单上传:" + jsonParas.toString());

            //1、参数
            String work_center_code = jsonParas.getString("work_center_code");//车间代号
            String prod_line_code = "UB";//产线
            String station_code = "HA-UB-000X";//工位
            String make_order = jsonParas.getString("order_prod");//订单号
            String serial_num = jsonParas.getString("serial_number");//工件编号(RFID号)
            String dms = jsonParas.getString("dms");//dms号
            String item_project = jsonParas.getString("item_project");//行项目
            String vin = jsonParas.getString("vin");//VIN码
            String small_model_type = jsonParas.getString("small_model_type");//车型
            String main_material_code = jsonParas.getString("main_material_code");//物料编码
            String material_color = jsonParas.getString("material_color");//颜色编码
            String material_size = jsonParas.getString("material_size");//尺寸
            String shaft_proc_num = "";//拧紧程序号
            String pallet_num = "";//托盘号
            String engine_num = jsonParas.getString("engine_num");//发动机号
            String driver_way = jsonParas.getString("driver_way");//驱动形式
            String publish_number = jsonParas.getString("publish_number");//发布顺序号
            String white_car_adjust = jsonParas.getString("white_car_adjust_assembly");//白车身调整总成
            String right_front_door = jsonParas.getString("right_front_door_assembly");//右前车门焊接总成
            String left_front_door = jsonParas.getString("left_front_door_assembly");//左前车门焊接总成
            String vpp_s = jsonParas.getString("vpp_s");//平台(描述)
            String bbl_s = jsonParas.getString("bbl_s");//驾驶室长度(描述)
            String bbr_s = jsonParas.getString("bbr_s");//驾驶室顶高度(描述)
            String bbf_s = jsonParas.getString("bbf_s");//驾驶室地板高度(描述)
            String bok_s = jsonParas.getString("bok_s");//工具箱(描述)
            String y26 = jsonParas.getString("y26");//变速箱型式(描述)
            String bed_s = jsonParas.getString("bed_s");//车门装饰板(描述)
            String bor_s = jsonParas.getString("bor_s");//天窗形式(特征值)
            //2024-01-31
            String ebom_white_car_code = jsonParas.getString("ebom_white_car_code");//天窗形式(特征值)

            //根据白车身调整总成查询上线工位和工艺路线
            ArrayList<String> arrayList = new ArrayList<>();
            String sqlRoute = "SELECT COALESCE(route_station,'') route_station " +
                    "from d_pmc_fmod_ha_car_route where car_type like '%" + white_car_adjust + "%' " +
                    "and enable_flag='Y'";
            List<Map<String, Object>> itemListRoute = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlRoute, false, null, apiRoutePath);
            if (itemListRoute != null && itemListRoute.size() > 0) {
                for (int i = 0; i < itemListRoute.size(); i++) {
                    String route_station = itemListRoute.get(i).get("route_station").toString();
                    String[] routeStationArray = route_station.split(",");
                    if (routeStationArray != null && routeStationArray.length > 0) {
                        for (String routeStation : routeStationArray) {
                            if (!arrayList.contains(routeStation))
                                arrayList.add(routeStation);
                        }
                    }
                }
            } else {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                errorMsg = "焊装生产订单上传异常:未找到白车身号{" + white_car_adjust + "}对应有效的工艺路线";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //订单信息
            String sqlOnline = "select flow_online_id " +
                    "from d_pmc_me_flow_online " +
                    "where make_order='" + make_order + "' " +
                    "and work_center_code='" + work_center_code + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
            if (itemListStation != null && itemListStation.size() > 0) {
                String flow_online_id = itemListStation.get(0).get("flow_online_id").toString();
                //2、线首修改
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                String updOnline = "update d_pmc_me_flow_online set " +
                        "last_updated_by='" + station_code + "'," +
                        "last_update_date='" + nowDateTime + "'," +
                        "serial_num='" + serial_num + "'," +
                        "dms='" + dms + "'," +
                        "item_project='" + item_project + "'," +
                        "vin='" + vin + "'," +
                        "small_model_type='" + small_model_type + "'," +
                        "main_material_code='" + main_material_code + "'," +
                        "material_color='" + material_color + "'," +
                        "material_size='" + material_size + "'," +
                        "shaft_proc_num='" + shaft_proc_num + "'," +
                        "pallet_num='" + pallet_num + "'," +
                        "engine_num='" + engine_num + "'," +
                        "driver_way='" + driver_way + "'," +
                        "publish_number='" + publish_number + "'," +
                        "white_car_adjust='" + white_car_adjust + "'," +
                        "right_front_door='" + right_front_door + "'," +
                        "left_front_door='" + left_front_door + "'," +
                        "vpp_s='" + vpp_s + "'," +
                        "bbl_s='" + bbl_s + "'," +
                        "bbr_s='" + bbr_s + "'," +
                        "bbf_s='" + bbf_s + "'," +
                        "bok_s='" + bok_s + "'," +
                        "y26='" + y26 + "'," +
                        "bed_s='" + bed_s + "'," +
                        "bor_s='" + bor_s + "', " +
                        "ebom_white_car_code='" + ebom_white_car_code + "' " +
                        "where flow_online_id=" + flow_online_id;
                cFuncDbSqlExecute.ExecUpdateSql(station_code, updOnline, true, null, apiRoutePath);
            } else {
                //3、线首保存 焊装定制
                this.HaOnlineIntefTask(request, apiRoutePath,
                        work_center_code, prod_line_code, station_code,
                        serial_num, pallet_num,
                        make_order, dms, item_project, vin, small_model_type,
                        main_material_code, material_color, material_size, shaft_proc_num,
                        engine_num, driver_way, publish_number, "Y", "Y",
                        white_car_adjust, right_front_door, left_front_door, vpp_s,
                        bbl_s, bbr_s, bbf_s, bok_s, y26, bed_s, bor_s,ebom_white_car_code, arrayList);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "焊装生产订单上传异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //1.线首保存 焊装定制,优先根据配置的工艺路线来生成，没有配置的话按原来的方法生成全部
    public void HaOnlineIntefTask(HttpServletRequest request, String apiRoutePath,
                                  String work_center_code, String prod_line_code, String station_code,
                                  String serial_num, String pallet_num,
                                  String make_order, String dms, String item_project, String vin, String small_model_type,
                                  String main_material_code, String material_color, String material_size, String shaft_proc_num,
                                  String engine_num, String driver_way, String publish_number, String online_flag, String dx_flag,
                                  String white_car_adjust, String right_front_door, String left_front_door, String vpp_s,
                                  String bbl_s, String bbr_s, String bbf_s, String bok_s, String y26, String bed_s, String bor_s,
                                  String ebom_white_car_code,ArrayList<String> routeStationArray) throws Exception {
        try {
            //默认值
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //工位状态：1正常 才认为是正常首件上线
            //1、新增线首
            long flowOnlineId = cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_flow_online_id_seq", true);
            String mo_work_order = String.valueOf(flowOnlineId);//生产订单顺序,针对订单进行排序
            String sqlOnlineIns = "insert into d_pmc_me_flow_online " +
                    "(created_by,creation_date,flow_online_id," +
                    "work_center_code,prod_line_code,station_code,make_order," +
                    "serial_num,dms,item_project,vin,small_model_type," +
                    "main_material_code,material_color,material_size,shaft_proc_num," +
                    "staff_id,pallet_num,engine_num,driver_way,publish_number," +
                    "repair_flag,enable_flag," +
                    "white_car_adjust,right_front_door,left_front_door,vpp_s," +
                    "bbl_s,bbr_s,bbf_s,bok_s,y26,bed_s,bor_s,ebom_white_car_code) values " +
                    "('admin','" + nowDateTime + "'," + flowOnlineId + ",'" +
                    work_center_code + "','" + prod_line_code + "','" + station_code + "','" + make_order + "','" +
                    serial_num + "','" + dms + "','" + item_project + "','" + vin + "','" + small_model_type + "','" +
                    main_material_code + "','" + material_color + "','" + material_size + "','" + shaft_proc_num + "'," +
                    "'AIS','" + pallet_num + "','" + engine_num + "','" + driver_way + "','" + publish_number + "','N','Y','" +
                    white_car_adjust + "','" + right_front_door + "','" + left_front_door + "','" + vpp_s + "','" +
                    bbl_s + "','" + bbr_s + "','" + bbf_s + "','" + bok_s + "','" + y26 + "','" + bed_s + "','" + bor_s + "','" + ebom_white_car_code + "')";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlOnlineIns, false, request, apiRoutePath);
            //2、生成工位生产订单
            if (online_flag.equals("Y") &&
                    dx_flag.equals("Y")) {//上线、定序工位
                if (routeStationArray != null && routeStationArray.size()>0) {
                        for (String routeStation : routeStationArray) {
                            long stationMoId = cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_station_mo_id_seq", true);
                            String sqlDxStationIns = "insert into d_pmc_me_station_mo " +
                                    "(created_by,creation_date,station_mo_id," +
                                    "prod_line_code,station_code,make_order," +
                                    "dms,item_project,mo_work_order," +
                                    "work_status,set_sign) values " +
                                    "('admin','" + nowDateTime + "'," + stationMoId + ",'" +
                                    prod_line_code + "','" + routeStation + "','" + make_order + "','" +
                                    dms + "','" + item_project + "'," + mo_work_order + "," +
                                    "'PLAN','NONE')";
                            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlDxStationIns, false, request, apiRoutePath);
                        }
                } else {
                    String sqlDxStation = "select COALESCE(station_code,'N') station_code " +
                            "from sys_fmod_station a," +
                            "     sys_fmod_prod_line b " +
                            "where a.prod_line_id=b.prod_line_id " +
                            "and a.enable_flag='Y' " +
                            "and b.enable_flag='Y' " +
                            "and a.station_attr='Y' " +
                            "and a.station_code<>'" + station_code + "' " +
                            "and b.work_center_code='" + work_center_code + "' " +
                            "order by a.prod_line_id,a.station_order ";
                    List<Map<String, Object>> itemListDxStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlDxStation, false, null, apiRoutePath);
                    if (itemListDxStation != null && itemListDxStation.size() > 0) {
                        for (Map<String, Object> mapDxStation : itemListDxStation) {
                            long stationMoId = cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_station_mo_id_seq", true);
                            String stationCodeDx = mapDxStation.get("station_code").toString();
                            String sqlDxStationIns = "insert into d_pmc_me_station_mo " +
                                    "(created_by,creation_date,station_mo_id," +
                                    "prod_line_code,station_code,make_order," +
                                    "dms,item_project,mo_work_order," +
                                    "work_status,set_sign) values " +
                                    "('admin','" + nowDateTime + "'," + stationMoId + ",'" +
                                    prod_line_code + "','" + stationCodeDx + "','" + make_order + "','" +
                                    dms + "','" + item_project + "'," + mo_work_order + "," +
                                    "'PLAN','NONE')";
                            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlDxStationIns, false, request, apiRoutePath);
                        }
                    }
                }
            }
        } catch (Exception ex) {
            throw new Exception("线首保存失败:" + ex.getMessage());
        }
    }

    //2.焊装生产订单上传
    @Transactional
    @RequestMapping(value = "/PmcCoreHaDkMoUpload_bak", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreHaDkMoUpload_bak(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreHaDkMoUpload";
        String selectResult = "";
        String errorMsg = "";
        try {
            log.info("焊装生产订单上传:" + jsonParas.toString());

            //1、参数
            String work_center_code = jsonParas.getString("work_center_code");//车间代号
            String prod_line_code = "UB";//产线
            String station_code = "HA-UB-000X";//工位
            String make_order = jsonParas.getString("order_prod");//订单号
            String serial_num = jsonParas.getString("serial_number");//工件编号(RFID号)
            String dms = jsonParas.getString("dms");//dms号
            String item_project = jsonParas.getString("item_project");//行项目
            String vin = jsonParas.getString("vin");//VIN码
            String small_model_type = jsonParas.getString("small_model_type");//车型
            String main_material_code = jsonParas.getString("main_material_code");//物料编码
            String material_color = jsonParas.getString("material_color");//颜色编码
            String material_size = jsonParas.getString("material_size");//尺寸
            String shaft_proc_num = "";//拧紧程序号
            String pallet_num = "";//托盘号
            String engine_num = jsonParas.getString("engine_num");//发动机号
            String driver_way = jsonParas.getString("driver_way");//驱动形式
            String publish_number = jsonParas.getString("publish_number");//发布顺序号
            String white_car_adjust = jsonParas.getString("white_car_adjust_assembly");//白车身调整总成
            String right_front_door = jsonParas.getString("right_front_door_assembly");//右前车门焊接总成
            String left_front_door = jsonParas.getString("left_front_door_assembly");//左前车门焊接总成
            String vpp_s = jsonParas.getString("vpp_s");//平台(描述)
            String bbl_s = jsonParas.getString("bbl_s");//驾驶室长度(描述)
            String bbr_s = jsonParas.getString("bbr_s");//驾驶室顶高度(描述)
            String bbf_s = jsonParas.getString("bbf_s");//驾驶室地板高度(描述)
            String bok_s = jsonParas.getString("bok_s");//工具箱(描述)
            String y26 = jsonParas.getString("y26");//变速箱型式(描述)
            String bed_s = jsonParas.getString("bed_s");//车门装饰板(描述)
            String bor_s = jsonParas.getString("bor_s");//天窗形式(特征值)
            //订单信息
            String sqlOnline = "select flow_online_id " +
                    "from d_pmc_me_flow_online " +
                    "where make_order='" + make_order + "' " +
                    "and work_center_code='" + work_center_code + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
            if (itemListStation != null && itemListStation.size() > 0) {
                String flow_online_id = itemListStation.get(0).get("flow_online_id").toString();
                //2、线首修改
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                String updOnline = "update d_pmc_me_flow_online set " +
                        "last_updated_by='" + station_code + "'," +
                        "last_update_date='" + nowDateTime + "'," +
                        "serial_num='" + serial_num + "'," +
                        "dms='" + dms + "'," +
                        "item_project='" + item_project + "'," +
                        "vin='" + vin + "'," +
                        "small_model_type='" + small_model_type + "'," +
                        "main_material_code='" + main_material_code + "'," +
                        "material_color='" + material_color + "'," +
                        "material_size='" + material_size + "'," +
                        "shaft_proc_num='" + shaft_proc_num + "'," +
                        "pallet_num='" + pallet_num + "'," +
                        "engine_num='" + engine_num + "'," +
                        "driver_way='" + driver_way + "'," +
                        "publish_number='" + publish_number + "'," +
                        "white_car_adjust='" + white_car_adjust + "'," +
                        "right_front_door='" + right_front_door + "'," +
                        "left_front_door='" + left_front_door + "'," +
                        "vpp_s='" + vpp_s + "'," +
                        "bbl_s='" + bbl_s + "'," +
                        "bbr_s='" + bbr_s + "'," +
                        "bbf_s='" + bbf_s + "'," +
                        "bok_s='" + bok_s + "'," +
                        "y26='" + y26 + "'," +
                        "bed_s='" + bed_s + "'," +
                        "bor_s='" + bor_s + "' " +
                        "where flow_online_id=" + flow_online_id;
                cFuncDbSqlExecute.ExecUpdateSql(station_code, updOnline, true, null, apiRoutePath);
            } else {
                //3、线首保存
                pmcCoreOnlineBase.OnlineIntefTask(request, apiRoutePath,
                        work_center_code, prod_line_code, station_code,
                        serial_num, pallet_num,
                        make_order, dms, item_project, vin, small_model_type,
                        main_material_code, material_color, material_size, shaft_proc_num,
                        engine_num, driver_way, publish_number, "Y", "Y",
                        white_car_adjust, right_front_door, left_front_door, vpp_s,
                        bbl_s, bbr_s, bbf_s, bok_s, y26, bed_s, bor_s);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "焊装生产订单上传异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
