package com.api.pack.core.recipe;

import com.alibaba.fastjson.JSONObject;
import com.api.base.Const;
import com.api.common.db.CFuncDbSqlResolve;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.swagger.annotations.ApiModelProperty;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class RecipeService extends ServiceImpl<RecipeMapper, Recipe> implements IService<Recipe>
{
    public void saveByEAPData(JSONObject data, CFuncDbSqlResolve cFuncDbSqlResolve)
    {
        String modelType = data.getString("model_type"); // 料号
        String[] ss = modelType.split("-");
        String version = "01";
        if (ss.length >= 2)
        {
            version = ss[1];
        }
        QueryWrapper<Recipe> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("model_type", modelType);
        queryWrapper.eq("model_version", version);
        queryWrapper.eq("enable_flag", "Y");
        List<Recipe> recipes = this.list(queryWrapper);
        if (recipes != null && !recipes.isEmpty())
        {
            for (Recipe recipe : recipes)
            {
                recipe.setMLength(data.getDoubleValue("m_length"));
                recipe.setMWidth(data.getDoubleValue("m_width"));
                recipe.setMThickness(data.getDoubleValue("m_tickness"));
            }
            this.updateBatchById(recipes);
        }
        else
        {
            try
            {
                long id = cFuncDbSqlResolve.GetIncreaseID("a_pack_fmod_recipe_id_seq", false);
                data.putIfAbsent("recipe_id", id);
            }
            catch (Exception ex)
            {
                throw new RuntimeException(ex);
            }
            this.save(new Recipe(data));
        }
    }
}