package com.api.eap.project.wxsn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 生产计划共用对外API接口
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/wxsn/share")
public class EapWxsnSharePlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //取消端口任务
    @RequestMapping(value = "/EapWxsnSharePlanPortCancel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnSharePlanPortCancel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/share/EapWxsnSharePlanPortCancel";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String[] group_lot_status_list = new String[]{"PLAN", "WORK"};
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "CANCEL");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "取消端口任务异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //保存任务信息到数据库
    @RequestMapping(value = "/EapWxsnSharePlanSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnSharePlanSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/share/EapWxsnSharePlanSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            String station_attr = lstStation.get(0).get("station_attr").toString();
            if (station_attr != null && station_attr.equals("UnLoad")) {
                //收板机,需要判断是否为离线模式,若为离线模式则不执行任务接受
                String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagOnOffLine = "";
                if (aisMonitorModel.equals("AIS-PC")) {
                    tagOnOffLine = "UnLoadPlc/PlcConfig/OnOffLine";
                } else if (aisMonitorModel.equals("AIS-SERVER")) {
                    tagOnOffLine = "UnLoadPlc_" + station_code + "/PlcConfig/OnOffLine";
                } else {
                    errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, tagOnOffLine);
                if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                    JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                    String tagOnOffLineValue = jbItem.getString("tag_value");
                    if (tagOnOffLineValue.equals("")) {
                        errorMsg = "查询收板机在线与离线状态时收板机PLC断网";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    if (tagOnOffLineValue.equals("0")) {//离线
                        transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                        return transResult;
                    }
                } else {
                    errorMsg = "未查询到收板机在线与离线状态";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }

            JSONArray plan_list = jsonParas.getJSONArray("plan_list");
            if (plan_list == null || plan_list.size() <= 0) {
                errorMsg = "保存任务信息不能为空集合";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //若是收板机,判断是否存在PLAN或者WORK的任务,若存在则不执行再次插入
            if (station_attr.equals("UnLoad")) {
                String[] group_lot_status2 = new String[]{"PLAN", "WORK"};
                JSONObject jbItem = plan_list.getJSONObject(0);
                String group_lot_num_unload = jbItem.getString("group_lot_num") == null ? "" : jbItem.getString("group_lot_num");
                Query queryBigDataUnLoad = new Query();
                queryBigDataUnLoad.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigDataUnLoad.addCriteria(Criteria.where("group_lot_num").is(group_lot_num_unload));
                queryBigDataUnLoad.addCriteria(Criteria.where("group_lot_status").in(group_lot_status2));
                long allCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigDataUnLoad.getQueryObject());
                if (allCount > 0) {
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                    return transResult;
                }
            }

            //若是收板机且当前模式为收板机上游决定生产顺序,则不能进行设定lot_group_status的PLAN
            String final_lot_group_status = "PLAN";
            if (station_attr.equals("UnLoad")) {
                String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String tagTaskOrderByUpDevice = "";
                String clientCode = "";
                if (aisMonitorModel.equals("AIS-PC")) {
                    tagTaskOrderByUpDevice = "UnLoadAis/AisConfig/TaskOrderByUpDevice";
                    clientCode = "UnLoadAis";
                } else if (aisMonitorModel.equals("AIS-SERVER")) {
                    tagTaskOrderByUpDevice = "UnLoadAis_" + station_code + "/AisConfig/TaskOrderByUpDevice";
                    clientCode = "UnLoadAis_" + station_code;
                }
                //1.先判断是否存在tag点
                String sqlOnlyCountTag = "select count(1) " +
                        "from scada_tag st inner join scada_tag_group stg " +
                        "on st.tag_group_id=stg.tag_group_id inner join scada_client sc " +
                        "on stg.client_id=sc.client_id " +
                        "where sc.enable_flag='Y' and stg.enable_flag='Y' and st.enable_flag='Y' " +
                        "and sc.client_code='" + clientCode + "' and stg.tag_group_code='AisConfig' " +
                        "and st.tag_code='TaskOrderByUpDevice'";
                Integer OnlyCountTag = cFuncDbSqlResolve.GetSelectCount(sqlOnlyCountTag);
                if (OnlyCountTag > 0) {
                    JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, tagTaskOrderByUpDevice);
                    if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                        JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                        String tagTaskOrderByUpDeviceValue = jbItem.getString("tag_value");
                        if (tagTaskOrderByUpDeviceValue.equals("")) {
                            errorMsg = "查询收板机工单任务生产顺序来源于上游设备状态时收板机PLC断网";
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        if (tagTaskOrderByUpDeviceValue.equals("1")) {
                            final_lot_group_status = "WAIT";
                        }
                    } else {
                        errorMsg = "未查询到收板机工单任务生产顺序来源于上游设备状态";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
            }

            //查询当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstPlanDocuments = new ArrayList<>();
            List<Map<String, Object>> lstPlanBDocuments = new ArrayList<>();
            String group_id = CFuncUtilsSystem.CreateUUID(true);
            for (int i = 0; i < plan_list.size(); i++) {
                JSONObject jbItem = plan_list.getJSONObject(i);
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String plan_id = CFuncUtilsSystem.CreateUUID(true);
                String port_code = jbItem.getString("port_code") == null ? "" : jbItem.getString("port_code");
                if (station_attr.equals("UnLoad")) port_code = "";
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("plan_id", plan_id);
                mapBigDataRow.put("station_id", Long.parseLong(station_id));
                mapBigDataRow.put("task_from", jbItem.getString("task_from") == null ? "EAP" : jbItem.getString("task_from"));
                mapBigDataRow.put("group_lot_num", jbItem.getString("group_lot_num") == null ? "" : jbItem.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", jbItem.getString("lot_num") == null ? "" : jbItem.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", jbItem.getString("lot_short_num") == null ? "" : jbItem.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", jbItem.getInteger("lot_index") == null ? 1 : jbItem.getInteger("lot_index"));
                mapBigDataRow.put("plan_lot_count", jbItem.getInteger("plan_lot_count") == null ? 0 : jbItem.getInteger("plan_lot_count"));
                mapBigDataRow.put("target_lot_count", jbItem.getInteger("target_lot_count") == null ? 0 : jbItem.getInteger("target_lot_count"));
                mapBigDataRow.put("port_code", port_code);
                mapBigDataRow.put("material_code", jbItem.getString("material_code") == null ? "" : jbItem.getString("material_code"));
                mapBigDataRow.put("pallet_num", jbItem.getString("pallet_num") == null ? "" : jbItem.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", jbItem.getString("pallet_type") == null ? "" : jbItem.getString("pallet_type"));
                mapBigDataRow.put("lot_level", jbItem.getString("lot_level") == null ? "" : jbItem.getString("lot_level"));
                mapBigDataRow.put("panel_length", jbItem.getDouble("panel_length") == null ? 0d : jbItem.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", jbItem.getDouble("panel_width") == null ? 0d : jbItem.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", jbItem.getDouble("panel_tickness") == null ? 0d : jbItem.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_model", jbItem.getInteger("panel_model") == null ? -1 : jbItem.getInteger("panel_model"));
                mapBigDataRow.put("inspect_count", jbItem.getInteger("inspect_count") == null ? 0 : jbItem.getInteger("inspect_count"));
                mapBigDataRow.put("work_mode", jbItem.getString("work_mode") == null ? 0 : jbItem.getString("work_mode"));
                mapBigDataRow.put("inspect_finish_count", 0);
                mapBigDataRow.put("pdb_count", jbItem.getInteger("pdb_count") == null ? 0 : jbItem.getInteger("pdb_count"));
                mapBigDataRow.put("pdb_rule", jbItem.getInteger("pdb_rule") == null ? 0 : jbItem.getInteger("pdb_rule"));
                mapBigDataRow.put("fp_count", jbItem.getInteger("fp_count") == null ? 0 : jbItem.getInteger("fp_count"));
                mapBigDataRow.put("group_lot_status", final_lot_group_status);
                mapBigDataRow.put("lot_status", "PLAN");
                mapBigDataRow.put("finish_count", 0);
                mapBigDataRow.put("finish_ok_count", 0);
                mapBigDataRow.put("finish_ng_count", 0);
                mapBigDataRow.put("task_error_code", 0);
                mapBigDataRow.put("item_info", jbItem.getString("item_info") == null ? "" : jbItem.getString("item_info"));
                mapBigDataRow.put("task_start_time", "");
                mapBigDataRow.put("task_end_time", "");
                mapBigDataRow.put("task_cost_time", (long) 0);
                mapBigDataRow.put("attribute1", jbItem.getString("attribute1") == null ? "" : jbItem.getString("attribute1"));
                mapBigDataRow.put("attribute2", jbItem.getString("attribute2") == null ? "" : jbItem.getString("attribute2"));
                mapBigDataRow.put("attribute3", jbItem.getString("attribute3") == null ? "" : jbItem.getString("attribute3"));
                mapBigDataRow.put("face_code", jbItem.getInteger("face_code") == null ? 0 : jbItem.getInteger("face_code"));
                mapBigDataRow.put("pallet_use_count", jbItem.getInteger("pallet_use_count") == null ? 0 : jbItem.getInteger("pallet_use_count"));
                mapBigDataRow.put("user_name", user_name);
                mapBigDataRow.put("group_id", group_id);
                mapBigDataRow.put("offline_flag", "N");
                mapBigDataRow.put("target_update_count", 0);
                mapBigDataRow.put("other_attribute", jbItem.getString("other_attribute") == null ? "" : jbItem.getString("other_attribute"));

                lstPlanDocuments.add(mapBigDataRow);
                String panel_list = jbItem.getString("panel_list") == null ? "" : jbItem.getString("panel_list");
                String[] panelList = panel_list.split(",", -1);
                if (panelList != null && panelList.length > 0) {
                    for (int j = 0; j < panelList.length; j++) {
                        String panel_barcode = panelList[j];
                        String plan_d_id = CFuncUtilsSystem.CreateUUID(true);
                        Map<String, Object> mapBigDataRowB = new HashMap<>();
                        mapBigDataRowB.put("item_date", item_date);
                        mapBigDataRowB.put("item_date_val", item_date_val);
                        mapBigDataRowB.put("plan_d_id", plan_d_id);
                        mapBigDataRowB.put("plan_id", plan_id);
                        mapBigDataRowB.put("panel_barcode", panel_barcode);
                        lstPlanBDocuments.add(mapBigDataRowB);
                    }
                }
            }
            if (lstPlanBDocuments.size() > 0) mongoTemplate.insert(lstPlanBDocuments, apsPlanBTable);
            mongoTemplate.insert(lstPlanDocuments, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "保存任务信息到数据库异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //保存离线任务信息到数据库
    @RequestMapping(value = "/EapWxsnShareOffLinePlanSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnShareOffLinePlanSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/share/EapWxsnShareOffLinePlanSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");
            String lot_num = jsonParas.getString("lot_num");
            String face_code = jsonParas.getString("face_code");
            String sqlStation = "select station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();

            //查询当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //1.先将当前端口正在进行中的任务结束掉
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("task_error_code", 1);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            //2.插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstPlanDocuments = new ArrayList<>();
            String group_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            String plan_id = CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("station_id", Long.parseLong(station_id));
            mapBigDataRow.put("task_from", "AIS");
            mapBigDataRow.put("group_lot_num", lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_num);
            mapBigDataRow.put("lot_index", 1);
            mapBigDataRow.put("plan_lot_count", 0);
            mapBigDataRow.put("target_lot_count", 0);
            mapBigDataRow.put("port_code", "");
            mapBigDataRow.put("material_code", "");
            mapBigDataRow.put("pallet_num", "");
            mapBigDataRow.put("pallet_type", "");
            mapBigDataRow.put("lot_level", "");
            mapBigDataRow.put("panel_length", 0d);
            mapBigDataRow.put("panel_width", 0d);
            mapBigDataRow.put("panel_tickness", 0d);
            mapBigDataRow.put("panel_model", -1);
            mapBigDataRow.put("inspect_count", 0);
            mapBigDataRow.put("inspect_finish_count", 0);
            mapBigDataRow.put("pdb_count", 0);
            mapBigDataRow.put("pdb_rule", 0);
            mapBigDataRow.put("fp_count", 0);
            mapBigDataRow.put("group_lot_status", "WORK");
            mapBigDataRow.put("lot_status", "WORK");
            mapBigDataRow.put("finish_count", 0);
            mapBigDataRow.put("finish_ok_count", 0);
            mapBigDataRow.put("finish_ng_count", 0);
            mapBigDataRow.put("task_error_code", 0);
            mapBigDataRow.put("item_info", "");
            mapBigDataRow.put("task_start_time", "");
            mapBigDataRow.put("task_end_time", "");
            mapBigDataRow.put("task_cost_time", (long) 0);
            mapBigDataRow.put("attribute1", "");
            mapBigDataRow.put("attribute2", "");
            mapBigDataRow.put("attribute3", "");
            mapBigDataRow.put("face_code", face_code.equals("A") ? 1 : 2);
            mapBigDataRow.put("pallet_use_count", 0);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("group_id", group_id);
            mapBigDataRow.put("offline_flag", "Y");
            mapBigDataRow.put("target_update_count", 0);
            mapBigDataRow.put("other_attribute", "");

            lstPlanDocuments.add(mapBigDataRow);
            mongoTemplate.insert(lstPlanDocuments, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "保存离线任务信息到数据库异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //完成离线任务
    @RequestMapping(value = "/EapWxsnShareOffLinePlanFinish", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnShareOffLinePlanFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/share/EapWxsnShareOffLinePlanFinish";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();

            //查询当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //1.先将当前端口正在进行中的任务结束掉
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("task_error_code", 1);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "保存离线任务信息到数据库异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //获取当前执行的离线任务
    @RequestMapping(value = "/GetWorkOffLinePlan", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetWorkOffLinePlan(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/share/GetWorkOffLinePlan";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                itemList.add(mapBigDataRow);
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在放板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
