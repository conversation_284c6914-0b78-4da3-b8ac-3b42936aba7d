package com.api.eap.core.mcs;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 针对Mcs标准接口
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-02
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/mcs")
public class McsController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //设备上料（出库）
    @RequestMapping(value = "/McsCoreLoadItem", method = {RequestMethod.POST, RequestMethod.GET})
    public String McsCoreLoadItem(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/core/mcs/McsCoreLoadItem";
        String esbInterfCode = "LoadItem";
        String pallet_num = jsonParas.getString("pallet_num");
        String station_code = jsonParas.getString("station_code");
        String port_code = jsonParas.getString("port_code");
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jsonSend = new JSONObject();
        jsonSend.put("CarrierNo", pallet_num);
        jsonSend.put("DeviceCode", station_code);
        jsonSend.put("PortCode", port_code);

        JSONObject jbResult = mcsInterfEvent(jsonSend, esbInterfCode, request);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");

        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        String transResult = "";
        if (successFlag) {
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } else {
            transResult = CFuncUtilsLayUiResut.GetStandJson(false, null, "1", "", 0);
        }
        return transResult;
    }

    //设备下料（入库）
    @RequestMapping(value = "/McsCoreUnloadItem", method = {RequestMethod.POST, RequestMethod.GET})
    public String McsCoreUnloadItem(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/core/mcs/McsCoreUnloadItem";
        String esbInterfCode = "UnloadItem";
        String lot_num = jsonParas.getString("lot_num");
        String pallet_num = jsonParas.getString("pallet_num");
        String pallet_type = jsonParas.getString("pallet_type");
        String station_code = jsonParas.getString("station_code");
        String port_code = jsonParas.getString("port_code");
        String material_code = jsonParas.getString("material_code");
        String material_name = jsonParas.getString("material_name");
        String current_process = jsonParas.getString("current_process");
        String current_process_name = jsonParas.getString("current_process_name");
        String next_process = jsonParas.getString("next_process");
        String next_process_name = jsonParas.getString("next_process_name");
        Integer plan_lot_count = jsonParas.getInteger("plan_lot_count");
        String prod_mode = jsonParas.getString("prod_mode");
        BigDecimal hold_time = new BigDecimal(jsonParas.getString("hold_time"));

        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jsonSend = new JSONObject();
        jsonSend.put("LotNo", lot_num);
        jsonSend.put("CarrierNo", pallet_num);
        jsonSend.put("CarrierType", pallet_type);
        jsonSend.put("DeviceCode", station_code);
        jsonSend.put("PortCode", port_code);
        jsonSend.put("MaterialNo", material_code);
        jsonSend.put("MaterialName", material_name);
        jsonSend.put("CurrentProcess", current_process);
        jsonSend.put("CurrentProcessName", current_process_name);
        jsonSend.put("NextProcess", next_process);
        jsonSend.put("NextProcessName", next_process_name);
        jsonSend.put("LotQty", plan_lot_count);
        jsonSend.put("ProdMode", prod_mode);
        jsonSend.put("HoldTime", hold_time);
        jsonSend.put("Memo", "");

        JSONObject jbResult = mcsInterfEvent(jsonSend, esbInterfCode, request);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");

        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        String transResult = "";
        if (successFlag) {
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } else {
            transResult = CFuncUtilsLayUiResut.GetStandJson(false, null, "1", "", 0);
        }
        return transResult;
    }

    //设备上空载具（空载具出库）
    @RequestMapping(value = "/McsCoreLoadCarrier", method = {RequestMethod.POST, RequestMethod.GET})
    public String McsCoreLoadCarrier(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/core/mcs/McsCoreLoadCarrier";
        String esbInterfCode = "LoadCarrier";
        String pallet_num = jsonParas.getString("pallet_num");
        String pallet_type = jsonParas.getString("pallet_type");
        String station_code = jsonParas.getString("station_code");
        String port_code = jsonParas.getString("port_code");
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jsonSend = new JSONObject();
        jsonSend.put("CarrierNo", pallet_num);
        jsonSend.put("DeviceCode", station_code);
        jsonSend.put("PortCode", port_code);
        jsonSend.put("CarrierType", pallet_type);

        JSONObject jbResult = mcsInterfEvent(jsonSend, esbInterfCode, request);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");

        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        String transResult = "";
        if (successFlag) {
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } else {
            transResult = CFuncUtilsLayUiResut.GetStandJson(false, null, "1", "", 0);
        }
        return transResult;
    }

    //设备下空载具（空载具入库）
    @RequestMapping(value = "/McsCoreUnloadCarrier", method = {RequestMethod.POST, RequestMethod.GET})
    public String McsCoreUnloadCarrier(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/core/mcs/McsCoreUnloadCarrier";
        String esbInterfCode = "UnloadCarrier";
        String pallet_num = jsonParas.getString("pallet_num");
        String pallet_type = jsonParas.getString("pallet_type");
        String station_code = jsonParas.getString("station_code");
        String port_code = jsonParas.getString("port_code");
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jsonSend = new JSONObject();
        jsonSend.put("CarrierNo", pallet_num);
        jsonSend.put("DeviceCode", station_code);
        jsonSend.put("PortCode", port_code);
        jsonSend.put("CarrierType", pallet_type);

        JSONObject jbResult = mcsInterfEvent(jsonSend, esbInterfCode, request);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");

        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }

        String transResult = "";
        if (successFlag) {
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } else {
            transResult = CFuncUtilsLayUiResut.GetStandJson(false, null, "1", "", 0);
        }
        return transResult;
    }

    //调用MCS接口
    private JSONObject mcsInterfEvent(JSONObject jsonParas, String esbInterfCode, HttpServletRequest request) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jsonObjectEap = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, jsonParas);
            responseParas = jsonObjectEap.toString();
            String backCode = jsonObjectEap.getString("ReturnCode");
            String backMsg = jsonObjectEap.getString("ReturnMsg");
            if (!backCode.equals("0")) {
                errorMsg = "调用MCS接口{" + esbInterfCode + "}返回失败：" + backMsg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //调用成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "调用成功");
        } catch (Exception ex) {
            errorMsg = "调用MCS接口{" + esbInterfCode + "}返回发生未知异常:" + ex.getMessage();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

}
