package com.api.eap.project.jxhb;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.thailand.guanghe.EapTlGhInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * (江西红板)EAP事件接口(Sub)公共方法
 * 1.嫁动率上报
 * 2.生产参数实时值上报
 * 3.报警上传
 * 4.设备状态上报
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class EapJxHbSendEventSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapJxHbInterfCommon eapJxHbInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.嫁动率上报
    public JSONObject ActivationReport(Long station_id,String station_code,String station_des,String station_num,
                                       String shift_name,String activation) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "ActivationReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            postParas.put("EQ_CODE",station_code);
            postParas.put("EQ_NAME",station_des);
            postParas.put("EQ_NO",station_num);
            postParas.put("Shift_No",shift_name);
            postParas.put("Activation",activation);
            postParas.put("Upload_Time",CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("Div_Code","载板");
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            eapJxHbInterfCommon.CheckResponseData(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", new JSONObject());
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            errorMsg = "发生异常:" + ex;
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.生产参数实时值上报
    public JSONObject ProductParasReport(Long station_id,String station_code,String station_des,String station_num,
                                       String recipe_code,String panel_width,String panel_thick,
                                       String device_status,String total_output,String unit_output,
                                       String plan_count,String finish_count,
                                       String putboard_time,String putboard_signal,
                                         String device_mode) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "ProductParasReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            postParas.put("EQ_CODE",station_code);
            postParas.put("EQ_NAME",station_des);
            postParas.put("EQ_NO",station_num);
            postParas.put("Recipe_Name",recipe_code);
            postParas.put("Board_Width",panel_width);
            postParas.put("Plate_Thick",panel_thick);
            postParas.put("Device_Status",device_status);
            postParas.put("Total_Output",total_output);
            postParas.put("Unit_Output",unit_output);
            postParas.put("Expect_CompletBoard_Num",plan_count);
            postParas.put("CompletBoard_Num",finish_count);
            postParas.put("PutBoard_Time",putboard_time);
            postParas.put("PutBoard_Signal",putboard_signal);
            postParas.put("Device_Mode",device_mode);
            postParas.put("Upload_Time",CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("Div_Code","载板");
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            eapJxHbInterfCommon.CheckResponseData(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", new JSONObject());
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            errorMsg = "发生异常:" + ex;
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.报警上传
    public JSONObject AlarmReport(Long station_id,String station_code,String station_des,String station_num,
                                         String alarm_code,String alarm_des,String alarm_value) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "AlarmReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            postParas.put("EQ_CODE",station_code);
            postParas.put("EQ_NAME",station_des);
            postParas.put("EQ_NO",station_num);
            postParas.put("ALARMCODE",alarm_code);
            postParas.put("ALARMDES",alarm_des);
            postParas.put("ALARMVALUE",alarm_value);
            postParas.put("Upload_Time",CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("Div_Code","载板");
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            eapJxHbInterfCommon.CheckResponseData(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", new JSONObject());
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            errorMsg = "发生异常:" + ex;
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.设备状态上报
    public JSONObject DeviceStatusReport(Long station_id,String station_code,String station_des,String station_num,
                                  String device_status) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "DeviceStatusReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            postParas.put("EQ_CODE",station_code);
            postParas.put("EQ_NAME",station_des);
            postParas.put("EQ_NO",station_num);
            postParas.put("Device_Status",device_status);
            postParas.put("Upload_Time",CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("Div_Code","载板");
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            eapJxHbInterfCommon.CheckResponseData(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", new JSONObject());
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            errorMsg = "发生异常:" + ex;
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
