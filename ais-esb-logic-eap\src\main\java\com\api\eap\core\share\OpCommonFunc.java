package com.api.eap.core.share;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工位与端口共用方法
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@Service
@Slf4j
public class OpCommonFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //读取投收板机单个单元REDIS值
    public String ReadCellOneRedisValue(String station_code, String station_attr,
                                        String clientCodeSub, String groupCode, String tagCode) throws Exception {
        String tagValue = "";
        String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
        String readTag = "";
        if (aisMonitorModel.equals("AIS-PC")) {
            readTag = station_attr + clientCodeSub + "/" + groupCode + "/" + tagCode;
        } else if (aisMonitorModel.equals("AIS-SERVER")) {
            readTag = station_attr + clientCodeSub + "_" + station_code + "/" + groupCode + "/" + tagCode;
        }
        if (!readTag.equals("")) {
            JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, readTag);
            if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                tagValue = jbItem.getString("tag_value");
            } else {
                throw new Exception("未读取到标签{" + readTag + "}对应值");
            }
        }
        return tagValue;
    }

    public String ReadCellOneRedisValueByTag(String station_code, String readTag) throws Exception {
        String tagValue = "";
        if (!readTag.equals("")) {
            JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, readTag);
            if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                tagValue = jbItem.getString("tag_value");
            } else {
                throw new Exception("未读取到标签{" + readTag + "}对应值");
            }
        }
        return tagValue;
    }

    //写入单个标签值到单元
    public String WriteCellOneTagValue(String station_code, String station_attr,
                                       String clientCodeSub, String groupCode, String tagCode,
                                       String userId, String tagValue, Boolean isAysn) throws Exception {
        String errorMsg = "";
        String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
        String writeTag = "";
        if (aisMonitorModel.equals("AIS-PC")) {
            writeTag = station_attr + clientCodeSub + "/" + groupCode + "/" + tagCode;
        } else if (aisMonitorModel.equals("AIS-SERVER")) {
            writeTag = station_attr + clientCodeSub + "_" + station_code + "/" + groupCode + "/" + tagCode;
        }
        if (!writeTag.equals("")) {
            errorMsg = cFuncUtilsCellScada.WriteTagByStation(userId, station_code, writeTag, tagValue, isAysn);
        }
        return errorMsg;
    }

    //离线存储要发送的报文信息
    public void SaveUnFinishInterfData(Long station_id, String function_name, JSONObject jbSend) throws Exception {
        String errorMsg = "";
        String send_message = "";
        String meInterfOfflineTable = "a_eap_me_interf_offline";
        try {
            send_message = jbSend.toString();
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String interf_offline_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("interf_offline_id", interf_offline_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("function_name", function_name);
            mapBigDataRow.put("send_msg", send_message);
            mapBigDataRow.put("up_count", 0);
            mapBigDataRow.put("up_msg", "");
            mapBigDataRow.put("up_flag", "N");
            mongoTemplate.insert(mapBigDataRow, meInterfOfflineTable);
        } catch (Exception ex) {
            errorMsg = "存储未发送成功的EAP接口事件异常,方法名:" + function_name + ",消息:" + send_message + "";
            log.error(errorMsg, ex);
        }
    }

    /**
     * <p>
     * 保存CIM消息
     * 1.screen_control(0:弹窗5秒消失,1:弹窗)
     * </p>
     */
    public void SaveCimMessage(Long station_id, String screen_control,
                               String screen_code, String cim_from, String cim_msg,
                               Integer interval_second_time) throws Exception {
        String errorMsg = "";
        String cimTableName = "a_eap_me_station_hmi_show";
        try {
            long station_id2 = station_id;
            //判断若没有工位信息,则取第一个工位
            if (station_id <= 0) {
                String sqlStation = "select station_id " +
                        "from sys_fmod_station " +
                        "where enable_flag='Y' " +
                        "order by station_id LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                        false, null, "");
                if (itemListStation != null && itemListStation.size() > 0) {
                    station_id2 = Long.parseLong(itemListStation.get(0).get("station_id").toString());
                }
            }

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("hmi_show_id", CFuncUtilsSystem.CreateUUID(true));
            mapBigDataRow.put("station_id", station_id2);
            mapBigDataRow.put("screen_control", screen_control);
            mapBigDataRow.put("screen_code", screen_code);
            mapBigDataRow.put("cim_from", cim_from);
            mapBigDataRow.put("cim_msg", cim_msg);
            mapBigDataRow.put("interval_second_time", interval_second_time);
            mapBigDataRow.put("finish_flag", "N");
            mongoTemplate.insert(mapBigDataRow, cimTableName);
        } catch (Exception ex) {
            errorMsg = "保存CIM消息{" + cim_msg + "}异常";
            log.error(errorMsg, ex);
        }
    }

    //校验AIS版本
    public Boolean CheckAisVersion(String aisVersion) throws Exception {
        Boolean isOk = false;
        try {
            String aisVersionSet = cFuncDbSqlResolve.GetParameterValue("AIS_Version");
            if (aisVersionSet != null && !aisVersionSet.equals("")) {
                if (aisVersionSet.equals(aisVersion)) isOk = true;
            }
        } catch (Exception ex) {
            isOk = false;
        }
        return isOk;
    }

    //根据端口序号查询端口信息
    public String GetPortInfoByPortIndex(Long station_id, Integer port_index,
                                         String port_code, String port_sign) throws Exception {
        try {
            String sqlPort = "select port_code,port_sign " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " " +
                    "and port_index=" + port_index + " " +
                    "order by station_port_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListPort = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlPort,
                    false, null, "");
            if (itemListPort != null && itemListPort.size() > 0) {
                port_code = itemListPort.get(0).get("port_code").toString();
                port_sign = itemListPort.get(0).get("port_sign").toString();
            }
            return port_code;
        } catch (Exception ex) {
            port_code = "";
            port_sign = "";
            return port_code;
        }
    }
}
