package com.api.pack.project.kinsus.op;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.base.Const;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.pack.core.ccd.CCDValidationResult;
import com.api.pack.core.ccd.CCDValidationResultService;
import com.api.pack.core.pile.Pile;
import com.api.pack.core.pile.PileService;
import com.api.pack.core.plan.Plan;
import com.api.pack.core.plan.PlanService;
import com.api.pack.core.recipe.MeRecipe;
import com.api.pack.core.recipe.MeRecipeService;
import com.api.pack.core.station.StationFlow;
import com.api.pack.core.station.StationFlowRecipe;
import com.api.pack.core.station.StationFlowRecipeService;
import com.api.pack.core.station.StationFlowService;
import com.api.pack.project.kinsus.KinsusConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 包装机接口 -- 台湾景硕
 * 1.获取当前计划
 * 2.获取配方信息（根据barcode）
 * 3.CCD校验
 * 4.移除工件（根据barcode）
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-06
 */
@RestController
@Slf4j
@RequestMapping("/pack/project/kinsus/op")
public class PackKinsusOpController
{
    @Autowired
    private MeRecipeService recipeService;
    @Autowired
    private PlanService planService;
    @Autowired
    private PileService pileService;
    @Autowired
    private StationFlowService stationFlowService;
    @Autowired
    private StationFlowRecipeService stationFlowRecipeService;
    @Autowired
    private CCDValidationResultService ccdValidationResultService;

    //1.获取当前计划
    @RequestMapping(value = "/GetCurrentPlan", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetCurrentPlan(@RequestBody(required = false) JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/kinsus/op/GetCurrentPlan";
        String tranResult = "";
        try
        {
            JSONObject result = new JSONObject();
            Plan plan = Plan.current(this.planService);
            // 若未查询到任务标签队列或者配方存在异常，则置PcStatus_ReadyRsp>1（2:资料错误,3:附属设备作业失败,4:上工序NG,5:工件Hold,6:其他错误）。
            if (plan == null)
            {
                result.put("code", 6);
                result.put("msg", "未查询到任务");
            }
            else
            {
                MeRecipe recipe = MeRecipe.byModelTypeAndModelVersion(plan.getPartNumber(), plan.getPartVersion(), recipeService);
                if (recipe == null)
                {
                    result.put("code", 2);
                    result.put("msg", "资料错误，未查询到配方");
                    tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result.toJSONString(), apiRoutePath, 0);
                    return tranResult;
                }
                if (plan.getBundles() == null || plan.getBundles().isEmpty())
                {
                    List<Pile> piles = pileService.findAllByPlanIdOrderByPileIndexAsc(plan.getPlanId());
                    if (piles == null || piles.isEmpty())
                    {
                        result.put("code", 2);
                        result.put("msg", "资料错误，未查询到包装记录");
                        tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result.toJSONString(), apiRoutePath, 0);
                        return tranResult;
                    }
                    boolean hasRemainder = plan.getSetBoardUnitNumber() % recipe.getTrayVolume() > 0;
                    boolean hasTailRemainder = plan.getTailCount() % recipe.getTrayVolume() > 0;
                    //【（对应标签ID的Unit數）除以（TRAY盘最大unit容积）有余数就加一】乘以（一层TRAY盘高度）加（包装加间隔TRAY盘高度）
                    int height = (plan.getSetBoardUnitNumber() / recipe.getTrayVolume() + (hasRemainder ? 1
                                                                                                        : 0)) * recipe.getTrayHeight() + recipe.getOverlayHeight();
                    // 尾包配方
                    int tailHeight = (plan.getTailCount() / recipe.getTrayVolume() + (hasTailRemainder ? 1
                                                                                                       : 0)) * recipe.getTrayHeight() + recipe.getOverlayHeight();
                    Map<String, Boolean> pileMap = new HashMap<>();
                    List<Plan.Bundle> bundles = new LinkedList<>();
                    for (Pile pile : piles)
                    {
                        if (pileMap.containsKey(pile.getPileBarcode()))
                        {
                            result.put("code", 2);
                            result.put("msg", "资料错误，存在重复的包装ID");
                            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result.toJSONString(), apiRoutePath, 0);
                            return tranResult;
                        }
                        Plan.Bundle bundle = new Plan.Bundle();
                        bundle.setIndex(pile.getPileIndex());
                        bundle.setBarcode(pile.getPileBarcode());
                        bundle.setQty(pile.getArrayCount());
                        bundle.setLast(pile.isTail());
                        bundles.add(bundle);
                        pileMap.put(pile.getPileBarcode(), true);
                    }
                    plan.setBundles(bundles);
                    plan.setSetBoardThickness(bundles.get(0).isLast() ? tailHeight : height);
                }
                tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, JSON.toJSONString(plan), apiRoutePath, 0);
            }
        }
        catch (Exception ex)
        {
            log.error("GetCurrentPlan error: {}", ex.getMessage(), ex);
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(ex.getMessage());
        }
        return tranResult;
    }

    //2.获取配方信息（根据barcode）
    @RequestMapping(value = "/GetRecipeInfoByBarcode", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetRecipeInfoByPileBarcode(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/kinsus/op/GetRecipeInfoByBarcode";
        String tranResult = "";
        try
        {
            JSONObject result = new JSONObject();
            Plan currentPlan = Plan.current(this.planService);
            if (currentPlan == null)
            {
                result.put("code", 2);
                result.put("msg", "资料错误");
            }
            else
            {
                // OP02-FLOW
                String stationCode = jsonParas.getString("station_code");
                String pileBarcode = jsonParas.getString("barcode");
                Pile pile = Pile.byPileBarcodeAndPlanId(pileBarcode, currentPlan.getPlanId(), pileService);
                if (pile == null)
                {
                    result.put("code", 2);
                    result.put("msg", "资料错误，未查询到包装记录");
                }
                else
                {
                    String recipeParas = null;
                    String planId = pile.getPlanId();
                    String lotNum = pile.getLotNum();
                    String serialNum = pile.getPileBarcode();
                    StationFlow headStationFlow = StationFlow.headOfPlanIdAndLotNumAndSerialNum(planId, lotNum, serialNum, this.stationFlowService);
                    StationFlowRecipe recipe = StationFlowRecipe.byStationCodeAndLotNumAndPlanId(stationCode, lotNum, planId, stationFlowRecipeService);
                    if (recipe != null)
                    {
                        recipeParas =
                                headStationFlow != null && headStationFlow.checkIsTail() && recipe.getTailRecipeParas() != null
                                ? recipe.getTailRecipeParas() : recipe.getRecipeParas();
                    }
                    if (recipeParas == null && headStationFlow != null)
                    {
                        recipeParas = headStationFlow.getRecipeParas();
                    }
                    if (recipeParas == null)
                    {
                        result.put("code", 2);
                        result.put("msg", "资料错误，未查询到站点配方参数");
                    }
                    else
                    {
                        result.put("code", 0);
                        result.put("recipe_paras", recipeParas);
                        if (KinsusConst.STATION_FLOW_CODE_03.equals(stationCode)) // 站点3-瓦楞板喷码
                        {
                            MeRecipe meRecipe = MeRecipe.byModelTypeAndModelVersion(currentPlan.getPartNumber(), currentPlan.getPartVersion(), recipeService);
                            if (meRecipe == null)
                            {
                                result.put("code", 2);
                                result.put("msg", "资料错误，未查询到配方");
                            }
                            else
                            {
                                Integer count = pile.isTail() ? currentPlan.getTailCount()
                                                              : currentPlan.getSetBoardUnitNumber();
                                // 喷墨模板
                                result.put("inkjet_tpl", meRecipe.getInkjetTpl());
                                // 喷墨打印文本：料號#批號,顆數
                                result.put("pm_print_text", currentPlan.getPartNumber() + "#" + currentPlan.getLotNum() + "," + count);
                            }
                        }
                    }
                }
            }
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result.toJSONString(), apiRoutePath, 0);
        }
        catch (Exception ex)
        {
            log.error("GetRecipeInfo error: {}", ex.getMessage(), ex);
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(ex.getMessage());
        }
        return tranResult;
    }

    //3.CCD校验
    @RequestMapping(value = "/CCDVerify", method = {RequestMethod.POST, RequestMethod.GET})
    public String CCDVerify(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/kinsus/op/CCDVerify";
        JSONObject flowInfo = null;
        JSONObject taskInfo = null;
        JSONObject ccdData = null;
        String validationResult = "1"; // 1是OK，2为NG
        String validationMsg = "检测通过";
        String tranResult = "";
        try
        {
            JSONObject data = jsonParas.getJSONObject("data");
            Assert.notNull(data, "参数错误");
            flowInfo = data.getJSONObject("flow_info");
            Assert.notNull(flowInfo, "参数错误，未查询到流程信息");
            taskInfo = data.getJSONObject("task_info");
            Assert.notNull(taskInfo, "参数错误，未查询到任务信息");
            String stationFlowCode = flowInfo.getString("code");
            Assert.notNull(stationFlowCode, "参数错误，未查询到流程站点信息");
            ccdData = data.getJSONObject("ccd_data");
            Assert.notNull(ccdData, "参数错误，未查询到CCD数据");
            // CCD校验逻辑
            switch (stationFlowCode)
            {
                case KinsusConst.STATION_FLOW_CODE_03: // 站点3-喷墨检测
                {
                    //{
                    //    "From": "AutoPackingCCD",
                    //    "Header": "SendLblResult",
                    //    "DateTime": "241204114502",
                    //    "Content": [{
                    //        "LblName": "PACKAGE_ID",
                    //        "LblValue": "@NULL@"
                    //    }, {
                    //        "LblName": "2D_Code",
                    //        "LblValue": "@NULL@"
                    //    }]
                    //}
                    // PACKAGE_ID == 2D_Code == 料號#批號,顆數
                    String pmPrintText = taskInfo.getString("pm_print_text");
                    Assert.notNull(pmPrintText, "参数错误，未查询到喷墨打印文本");
                    JSONArray content = ccdData.getJSONArray("Content");
                    Assert.notNull(content, "参数错误，未查询到CCD数据内容");
                    for (int i = 0; i < content.size(); i++)
                    {
                        JSONObject lbl = content.getJSONObject(i);
                        Assert.notNull(lbl, "参数错误，未查询到CCD数据标签");
                        String lblName = lbl.getString("LblName");
                        Assert.notNull(lblName, "参数错误，未查询到CCD数据标签名称");
                        String lblValue = lbl.getString("LblValue");
                        Assert.notNull(lblValue, "参数错误，未查询到CCD数据标签值");
                        if ("@NULL@".equals(lblValue))
                        {
                            validationResult = "2";
                            validationMsg = "喷墨CCD校验失败，" + lblName + "为@NULL@";
                            break;
                        }
                        else if (!pmPrintText.equals(lblValue))
                        {
                            validationResult = "2";
                            validationMsg = String.format("喷墨CCD校验失败，记录[%s]与CCD反馈[%s]的喷墨打印文本不匹配", pmPrintText, lblValue);
                            break;
                        }
                    }
                    break;
                }
                case KinsusConst.STATION_FLOW_CODE_07: // 站点7-辅材检测
                {
                    //{
                    // 	"From": "AutoPackingCCD",
                    // 	"Header": "SendTestResult",
                    // 	"DateTime": "241204114655",
                    // 	"Content": {
                    // 		"TestResult": "0",
                    // 		"TestDetails": [{
                    // 			"TestItemCode": "SDK",
                    // 			"TestItemDes": "湿度卡:OK",
                    // 			"TestItemResult": "OK",
                    // 			"TestItemMsg": "1"
                    // 		}, {
                    // 			"TestItemCode": "WLB",
                    // 			"TestItemDes": "瓦楞板:NG",
                    // 			"TestItemResult": "NG",
                    // 			"TestItemMsg": "0"
                    // 		}, {
                    // 			"TestItemCode": "CSD",
                    // 			"TestItemDes": "长束带:OK",
                    // 			"TestItemResult": "OK",
                    // 			"TestItemMsg": "1"
                    // 		}, {
                    // 			"TestItemCode": "DSD",
                    // 			"TestItemDes": "短束带:NG",
                    // 			"TestItemResult": "NG",
                    // 			"TestItemMsg": "0"
                    // 		}]
                    // 	}
                    //}
                    // 校验内容：只要判断每一个TestItemMsg状态是否未1就行，全为1则显示校验成功则OK，存在不为1的部分全部进行显示出来对应的校验错误部分。
                    JSONObject content = ccdData.getJSONObject("Content");
                    Assert.notNull(content, "参数错误，未查询到CCD数据内容");
                    JSONArray testDetails = content.getJSONArray("TestDetails");
                    Assert.notNull(testDetails, "参数错误，未查询到CCD数据测试项");
                    for (int i = 0; i < testDetails.size(); i++)
                    {
                        JSONObject testItem = testDetails.getJSONObject(i);
                        Assert.notNull(testItem, "参数错误，未查询到CCD数据测试项");
                        String testItemResult = testItem.getString("TestItemResult");
                        Assert.notNull(testItemResult, "参数错误，未查询到CCD数据测试项结果");
                        if (!Const.OK.equals(testItemResult))
                        {
                            validationResult = "2";
                            validationMsg = "辅材检测失败，" + testItem.getString("TestItemDes");
                            break;
                        }
                    }
                    break;
                }
                case KinsusConst.STATION_FLOW_CODE_11: // 站点11-标签检测
                {
                    //{
                    //    "From": "AutoPackingCCD",
                    //    "Header": "SendLblResult",
                    //    "DateTime": "241204160133",
                    //    "Content": [{
                    //        "LblName": "PACKAGE_ID",
                    //        "LblValue": "@NULL@"
                    //    }, {
                    //        "LblName": "Lot",
                    //        "LblValue": "@NULL@"
                    //    }, {
                    //        "LblName": " PartNumber",
                    //        "LblValue": "@NULL@"
                    //    }]
                    //}
                    // 标签检测内容为1. 标签ID PACKAGE_ID 2. 料号信息 PartNumber 3. 批号信息 Lot
                    String pileBarcode = taskInfo.getString("serial_num");
                    Assert.notNull(pileBarcode, "参数错误，未查询到包装ID");
                    Plan currentPlan = Plan.current(planService);
                    Assert.notNull(currentPlan, "未查询到当前进行中的任务");
                    JSONArray content = ccdData.getJSONArray("Content");
                    Assert.notNull(content, "参数错误，未查询到CCD数据内容");
                    for (int i = 0; i < content.size(); i++)
                    {
                        JSONObject lbl = content.getJSONObject(i);
                        Assert.notNull(lbl, "参数错误，未查询到CCD数据标签");
                        String lblName = lbl.getString("LblName");
                        Assert.notNull(lblName, "参数错误，未查询到CCD数据标签名称");
                        String lblValue = lbl.getString("LblValue");
                        Assert.notNull(lblValue, "参数错误，未查询到CCD数据标签值");
                        if ("@NULL@".equals(lblValue))
                        {
                            validationResult = "2";
                            validationMsg = "标签CCD校验失败，" + lblName + "为@NULL@";
                            break;
                        }
                        if ("PACKAGE_ID".equals(lblName) && !pileBarcode.equals(lblValue))
                        {
                            validationResult = "2";
                            validationMsg = String.format("标签CCD校验失败，记录[%s]与CCD反馈[%s]的包装ID不匹配", pileBarcode, lblValue);
                            break;
                        }
                        if ("Lot".equals(lblName) && !currentPlan.getLotNum().equals(lblValue))
                        {
                            validationResult = "2";
                            validationMsg = String.format("标签CCD校验失败，记录[%s]与CCD反馈[%s]的批号不匹配", currentPlan.getLotNum(), lblValue);
                            break;
                        }
                        if ("PartNumber".equals(lblName) && !currentPlan.getPartNumber().equals(lblValue))
                        {
                            validationResult = "2";
                            validationMsg = String.format("标签CCD校验失败，记录[%s]与CCD反馈[%s]的料号不匹配", currentPlan.getPartNumber(), lblValue);
                            break;
                        }
                    }
                    break;
                }
            }
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, validationResult, apiRoutePath, 0);
        }
        catch (Exception ex)
        {
            log.error("CCDVerify error: {}", ex.getMessage(), ex);
            validationResult = "-1";
            validationMsg = ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(ex.getMessage());
        }
        if (flowInfo != null && taskInfo != null)
        {
            this.ccdValidationResultService.save(CCDValidationResult.of(flowInfo, taskInfo, ccdData, validationResult, validationMsg));
        }
        return tranResult;
    }

    //4.移除工件（根据barcode）
    @RequestMapping(value = "/RemoveBagByBarcode", method = {RequestMethod.POST, RequestMethod.GET})
    public String RemoveBagByBarcode(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/kinsus/op/RemoveBagByBarcode";
        String tranResult = "";
        try
        {
            String pileBarcode = jsonParas.getString("barcode");
            Plan currentPlan = Plan.current(planService);
            Assert.notNull(currentPlan, "未查询到当前进行中的任务");
            Pile pile = Pile.byPileBarcodeAndPlanId(pileBarcode, currentPlan.getPlanId(), pileService);
            Assert.notNull(pile, "资料错误，未查询到包装记录");
            pile.setEnableFlag(Const.FLAG_N);
            pile.setRetryFlag(Const.FLAG_Y);
            pileService.save(pile);
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        }
        catch (Exception ex)
        {
            log.error("RemovePileByBarcode error: {}", ex.getMessage(), ex);
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(ex.getMessage());
        }
        return tranResult;
    }
}
