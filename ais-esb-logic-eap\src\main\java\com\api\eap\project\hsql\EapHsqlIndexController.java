package com.api.eap.project.hsql;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;

import lombok.extern.slf4j.Slf4j;

/**
 * 黄石群立 设备控制。供首页使用。
 * <AUTHOR>
 * @date 2025-05-21
 */
@RestController
@RequestMapping("/eap/project/hsql/index")
@Slf4j
public class EapHsqlIndexController {

    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    
    @Autowired
    private RestTemplate restTemplate;
    
    @Autowired
    private CFuncLogInterf cFuncLogInterf;

    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    /**
     * 生產前批级過賬检查、 供web管理界面使用。
     * 根据工號、工单批号、機台編號检查生产前批级过账信息
     * @param empID 工号
     * @param lotNum 工单批号
     * @param machineID 机台编号
     * @param request HTTP请求对象
     * @return 返回料号、数量、投板面次、配方名称等信息（JSON格式）
     */
    @RequestMapping(value = "/StartProduction", method = {RequestMethod.POST, RequestMethod.GET})
    public String startProduction(@RequestBody(required = false) JSONObject jsonParas,HttpServletRequest request) {
        String apiRoutePath = "eap/project/hsql/index/StartProduction";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        String selectResult = "";
        String errorMsg = "";
        try {
            String empID = jsonParas.getString("EmpID");
            String lotNum = jsonParas.getString("LotNum");
            String station_code=jsonParas.getString("StationCode");
            // 参数验证
            if (empID == null || empID.isEmpty()) {
                errorMsg = "工号(EmpID)不能为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
            if (lotNum == null || lotNum.isEmpty()) {
                errorMsg = "工单批号(LotNum)不能为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
            //判断工位是否正确
            String sqlStation = "select  station_id,station_code station_code  from sys_fmod_station  where enable_flag='Y' order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String machineID = itemListStation.get(0).get("station_code").toString();
            if (machineID == null || machineID.isEmpty()) {
                errorMsg = "机台编号(MachineID)不能为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
            log.info("StartProduction API called with params: EmpID={}, LotNum={}, MachineID={}", empID, lotNum, machineID);
            // 查询接口配置
            String esbInterfCode = "StartProduction";
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url,"
                    + "COALESCE(esb_dev_intef_url,'') esb_dev_intef_url," +
                    "COALESCE(esb_test_result,'') esb_test_result from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";

            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }

            String esbProdIntefUrl = itemList.get(0).get("esb_prod_intef_url").toString();
            String esbDevIntefUrl  = itemList.get(0).get("esb_dev_intef_url").toString();
            String esbTestResult = itemList.get(0).get("esb_test_result").toString();
            if (esbProdIntefUrl == null || esbProdIntefUrl.isEmpty()) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
            // 构建请求URL
            String url = esbProdIntefUrl + "?EmpID=" + empID + "&LotNum=" + lotNum + "&MachineID=" + machineID;
            // 发送请求
            JSONObject responseJson = null;
            boolean success = true;
            String errMsg = "";
            try {
                if (esbDevIntefUrl != null && esbDevIntefUrl.startsWith("Y")) {
                    // 使用测试数据
                    log.info("使用测试数据进行StartProduction请求");
                    responseJson = JSONObject.parseObject(esbTestResult);
                } else {
                    // 发送实际请求
                    log.info("发送实际StartProduction请求: {}", url);
                    responseJson = restTemplate.getForObject(url, JSONObject.class);
                }
                //如果返回正常的数据。直接查询配方数据。组合
                // 配方下发逻辑 - 使用公共方法
                try {
                	String client_code_ais =  getClientCode(station_code, "Ais", request, apiRoutePath);
                    Map<String, String> customParams = new HashMap<>();
                    customParams.put("LotID", lotNum != null ? lotNum : "");
                    // 用接口返回的数据替换配方中的值
                    if (responseJson != null && responseJson.containsKey("Data")) {
                        JSONObject data = responseJson.getJSONObject("Data");
                        //写入任务面次的类型。
                        cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, client_code_ais+"/AisStatus/TaskType", data.getString("MC"), false);
                    }
                } catch (Exception recipeEx) {
                    log.error("配方下发失败: " + recipeEx.getMessage(), recipeEx);
                }
            } catch (Exception e) {
                errMsg = "StartProduction请求发生异常: " + e.getMessage();
                log.error(errMsg, e);
                return CFuncUtilsLayUiResut.GetErrorJson(errMsg);
            } finally {
                // 记录接口日志
                String endDate = CFuncUtilsSystem.GetNowDateTime("");
                String paraStr = "EmpID=" + empID + "&LotNum=" + lotNum + "&MachineID=" + machineID;
                String resStr = success && responseJson != null ? responseJson.toString() : "";
                // 判断是否使用生产环境
                boolean prodFlag = esbDevIntefUrl == null || !esbDevIntefUrl.startsWith("Y");
                cFuncLogInterf.Insert(
                    startDate,
                    endDate,
                    esbInterfCode,
                    prodFlag,
                    "",
                    paraStr,
                    resStr,
                    success,
                    errMsg,
                    request
                );
            }
            // 返回结果
            List<Map<String, Object>> resultList = new ArrayList<>();
            if (responseJson != null && responseJson.getBoolean("Success") && responseJson.containsKey("Data")) {
                JSONObject data = responseJson.getJSONObject("Data");
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("PartNum", data.getString("PartNum"));
                resultMap.put("Qnty", data.getInteger("Qnty"));
                resultMap.put("MC", data.getString("MC"));
                resultMap.put("RecipeName", data.getString("RecipeName"));
                //直接返回到js。页面去下发配方。
                resultList.add(resultMap);
            }else {
            	if(responseJson != null && !responseJson.getBoolean("Success")) {
            		//调用接口失败。
            		errMsg="MES接口调用失败,失败原因："+responseJson.getString("Message");
                	return CFuncUtilsLayUiResut.GetErrorJson(errMsg);	
            	}
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, resultList, "", "", resultList.size());
        } catch (Exception ex) {
            errorMsg = "生產前批级過賬检查发生异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }

        return selectResult;
    }

    /**
     * 手動放行、 供web管理界面使用。
     * 根据工号、当前作业工单批号、读码信息、读码面次、维护NG原因、机台编号进行手动放行
     * @param empID 工号
     * @param lotNum 工单批号
     * @param readInfo 读码信息
     * @param readMC 读码面次
     * @param ngReason 维护NG原因
     * @param machineID 机台编号
     * @param request HTTP请求对象
     * @return 返回处理结果（JSON格式）
     */
    @RequestMapping(value = "/NGReasonSaved", method = {RequestMethod.POST, RequestMethod.GET})
    public String ngReasonSaved(
            @RequestBody(required = false) JSONObject jsonParas,
            HttpServletRequest request) {
        String apiRoutePath = "eap/project/hsql/index/NGReasonSaved";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        String selectResult = "";
        String errorMsg = "";
        try {
        	//工号
            String empID = jsonParas.getString("EmpID");
            //当前作业工单批号
            String lotNum = jsonParas.getString("LotNum");
            //、读码信息（含片号）、读码面次、维护NG原因、机台编号、
            String readInfo = jsonParas.getString("ReadInfo");
            //读码面次、维护NG原因、机台编号、
            String readMC = jsonParas.getString("ReadMC");
            //维护NG原因
            String ngReason = jsonParas.getString("NGReason");
            String station_code = jsonParas.getString("StationCode");
            String machineID = station_code;
            //判断工位是否正确
//            String sqlStation = "select  station_id,station_code station_code  from sys_fmod_station  where enable_flag='Y' order by station_id LIMIT 1 OFFSET 0";
//            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
//            String machineID = itemListStation.get(0).get("station_code").toString();
//            if (machineID == null || machineID.isEmpty()) {
//                errorMsg = "机台编号(MachineID)不能为空";
//                log.error(errorMsg);
//                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
//            }
            // 参数验证
            if (empID == null || empID.isEmpty()) {
                errorMsg = "工号(EmpID)不能为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }

            if (lotNum == null || lotNum.isEmpty()) {
                errorMsg = "工单批号(LotNum)不能为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }

            if (readInfo == null || readInfo.isEmpty()) {
                errorMsg = "读码信息(ReadInfo)不能为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }

            if (readMC == null || readMC.isEmpty()) {
                errorMsg = "读码面次(ReadMC)不能为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }

            if (ngReason == null || ngReason.isEmpty()) {
                errorMsg = "维护NG原因(NGReason)不能为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
            if (machineID == null || machineID.isEmpty()) {
                errorMsg = "机台编号(MachineID)不能为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }

            log.info("NGReasonSaved API called with params: EmpID={}, LotNum={}, ReadInfo={}, ReadMC={}, NGReason={}, MachineID={}",empID, lotNum, readInfo, readMC, ngReason, machineID);

            // 查询接口配置
            String esbInterfCode = "NGReasonSaved";
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url,COALESCE(esb_dev_intef_url,'') esb_dev_intef_url,COALESCE(esb_test_result,'') esb_test_result from sys_core_esb_interf where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
            String esbProdIntefUrl = itemList.get(0).get("esb_prod_intef_url").toString();
            String esbDevIntefUrl = itemList.get(0).get("esb_dev_intef_url").toString();
            String esbTestResult = itemList.get(0).get("esb_test_result").toString();
            if (esbProdIntefUrl == null || esbProdIntefUrl.isEmpty()) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                log.error(errorMsg);
                return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
            // 构建请求URL
            String url = esbProdIntefUrl + "?EmpID=" + empID + "&LotNum=" + lotNum + "&ReadInfo=" + readInfo + "&ReadMC=" + readMC + "&NGReason=" + ngReason + "&MachineID=" + machineID;
            // 发送请求
            JSONObject responseJson = null;
            boolean success = true;
            String errMsg = "";
			String client_code =  getClientCode(station_code, "Plc", request, apiRoutePath);
			String client_code_ais =  getClientCode(station_code, "Ais", request, apiRoutePath);
            try {
                if (esbDevIntefUrl != null && esbDevIntefUrl.startsWith("Y")) {
                    // 使用测试数据
                    log.info("使用测试数据进行NGReasonSaved请求");
                    responseJson = JSONObject.parseObject(esbTestResult);
                } else {
                    // 发送实际请求
                    log.info("发送实际NGReasonSaved请求: {}", url);
                    responseJson = restTemplate.getForObject(url, JSONObject.class);
                }
                //返回值：{"Success":true,"Messages":"放行"}
                if (responseJson != null && responseJson.containsKey("Success") && responseJson.getBooleanValue("Success")) {
                    // 调用提取的方法写入放行
                	setCcdStartRsp(station_code, client_code,CcdStartRsp_OK);
                	//清空PmAis/AisStatus/E2DCheckFaillResult 的标签。便于页面定时获取标签值。
                	cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, client_code_ais+"/AisStatus/E2DCheckFaillResult", "", false);
                    success = true;
                }else{
                	success = false;
                }
            } catch (Exception e) {
                errMsg = "NGReasonSaved请求发生异常: " + e.getMessage();
                log.error(errMsg, e);
                return CFuncUtilsLayUiResut.GetErrorJson(errMsg);
            } finally {
                // 记录接口日志
                String endDate = CFuncUtilsSystem.GetNowDateTime("");
                String paraStr = "EmpID=" + empID + "&LotNum=" + lotNum + "&ReadInfo=" + readInfo + "&ReadMC=" + readMC +"&NGReason=" + ngReason + "&MachineID=" + machineID;
                String resStr = success && responseJson != null ? responseJson.toString() : "";
                // 判断是否使用生产环境
                boolean prodFlag = esbDevIntefUrl == null || !esbDevIntefUrl.startsWith("Y");
                cFuncLogInterf.Insert(
                    startDate,
                    endDate,
                    esbInterfCode,
                    prodFlag,
                    "",
                    paraStr,
                    resStr,
                    success,
                    errMsg,
                    request
                );
            }
            // 返回结果
            List<Map<String, Object>> resultList = new ArrayList<>();
            Map<String, Object> resultMap = new HashMap<>();
            if (responseJson != null) {
                resultMap.put("Success", responseJson.getBoolean("Success"));
                resultMap.put("Messages", responseJson.getString("Messages"));
            } else {
                resultMap.put("Success", false);
                resultMap.put("Messages", "调用接口失败，未获取到响应");
            }
            resultList.add(resultMap);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, resultList, "", "", resultList.size());
        } catch (Exception ex) {
            errorMsg = "手動放行发生异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 生產中片级檢查
     * 根据当前作业工单批号、剩余数量、机台编号进行生产中片级检查
     * @param lotNum 工单批号
     * @param remainingPieces 剩余数量
     * @param machineId 机台编号
     * @param request HTTP请求对象
     * @return 返回检查结果（JSON格式）
     */
    @RequestMapping(value = "/E2DCheckEachOne", method = {RequestMethod.POST, RequestMethod.GET})
    public String e2dCheckEachOne(@RequestBody(required = false) JSONObject jsonParas,HttpServletRequest request) {
        String apiRoutePath = "eap/project/hsql/index/E2DCheckEachOne";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        try {
//            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String panel_check_result = jsonParas.getString("panel_check_result");//用逗号分割读码的内容和片号
            //获取料号
//            E2DCheckEachOne(生產中片级檢查)
//            传入参数：
//            当前作业工单批号、读码信息（含片号）、读码面次、剩余数量、机台编号、
//            返回内容：
//            Ok、放行 或者 NG 报警信息
//            ULR:
//            http://10.144.65.102/HC1API/api/Post/E2DCheckEachOne/?LotNum=355AB003-02-00
//            ReadInfo=255Fb00701100 04 87&ReadMC=s&RemainingPieces=5&MachineId=PLGVCPH005
//            返回值：{"Success":true,"Messages":"放行"}
            //从panel_check_result 中解析出读码信息和读码面次数据。调用接口
            String readInfo = "Readerror";
            String readMC = "Readerror";
			// 获取PLC客户端代码
            String client_code =  getClientCode(station_code, "Plc", request, apiRoutePath);
            String client_code_ais =  getClientCode(station_code, "Ais", request, apiRoutePath);
            // 检查panel_check_result是否为空
            if (panel_check_result == null || panel_check_result.isEmpty() || panel_check_result.equals("NoRead")) {
	            //写入弹窗信息
                writeE2DCheckFailResult(readInfo, "0", readInfo, readMC,station_code, client_code_ais,client_code,CcdStartRsp_NG, "面板检查结果数据为空,无法解析读码信息");
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Messages", "面板检查结果数据为空,无法解析读码信息");
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            String MC_S = this.cFuncDbSqlResolve.GetParameterValue("MC_S");
            if (StringUtils.isEmpty(MC_S)) {
              MC_S = "140-230"; 
            }
            String MC_C = this.cFuncDbSqlResolve.GetParameterValue("MC_C");
            if (StringUtils.isEmpty(MC_C)) {
              MC_C = "231-280"; 
            }
            try {
              readInfo = StringUtils.split(panel_check_result, ",")[0];
              readMC = StringUtils.split(panel_check_result, ",")[1];
              if ("Readerror".equals(readInfo) || "Readerror".equals(readMC)) {
            	// 获取AIS客户端代码用于写入失败结果
	              String errorMsg = "读码错误: ";
	              if ("Readerror".equals(readInfo)) {
	                  errorMsg += "读码信息错误";
	              }
	              if ("Readerror".equals(readMC)) {
	                  if ("Readerror".equals(readInfo)) {
	                      errorMsg += "，读码面次错误";
	                  } else {
	                      errorMsg += "读码面次错误";
	                  }
	              }
                writeE2DCheckFailResult(readInfo, "0", readInfo, readMC, station_code, client_code_ais, client_code, CcdStartRsp_NG, errorMsg);
                JSONObject jSONObject = new JSONObject();
                jSONObject.put("Success", Boolean.valueOf(false));
                jSONObject.put("Messages", errorMsg);
                return EapHsqlInterfCommon.convertToEventFormat(jSONObject).toJSONString();
              } 
              int readMCInt = Integer.parseInt(readMC);
              if (readMCInt >= Integer.parseInt(StringUtils.split(MC_S, "-")[0]) && readMCInt <= Integer.parseInt(StringUtils.split(MC_S, "-")[1])) {
                readMC = "S";
              } else if (readMCInt >= Integer.parseInt(StringUtils.split(MC_C, "-")[0]) && readMCInt <= Integer.parseInt(StringUtils.split(MC_C, "-")[1])) {
                readMC = "C";
              } else {
        	   String  str = "面板角度为："+readMC+"，不在S面:"+MC_S+"范围内,也不在C面："+MC_C+"范围内";
                writeE2DCheckFailResult("", "0", readInfo, readMC, station_code, client_code_ais, client_code, CcdStartRsp_NG, str);
                JSONObject jSONObject = new JSONObject();
                jSONObject.put("Success", Boolean.valueOf(false));
                jSONObject.put("Messages", "檢查面板检查结果失败：" + str);
                return EapHsqlInterfCommon.convertToEventFormat(jSONObject).toJSONString();
              } 
            } catch (Exception e) {
              writeE2DCheckFailResult("", "0", readInfo, readMC, station_code, client_code_ais, client_code, CcdStartRsp_NG, "解析面板检查结果失败: " + e.getMessage());
              JSONObject jSONObject = new JSONObject();
              jSONObject.put("Success", Boolean.valueOf(false));
              jSONObject.put("Messages", "檢查面板检查结果失败: " + e.getMessage());
              return EapHsqlInterfCommon.convertToEventFormat(jSONObject).toJSONString();
            } 
            // 检查readInfo和readMC是否为"Readerror"
            if ("Readerror".equals(readInfo) || "Readerror".equals(readMC)) {
                // 获取AIS客户端代码用于写入失败结果
                String errorMsg = "读码错误: ";
                if ("Readerror".equals(readInfo)) {
                    errorMsg += "读码信息错误";
                }
                if ("Readerror".equals(readMC)) {
                    if ("Readerror".equals(readInfo)) {
                        errorMsg += "，读码面次错误";
                    } else {
                        errorMsg += "读码面次错误";
                    }
                }
                writeE2DCheckFailResult(readInfo, "0", readInfo, readMC,station_code, client_code_ais,client_code,CcdStartRsp_NG, errorMsg);
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Messages", errorMsg);
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            //参数验证
            String machineId = station_code;
            // scada读取lotNum批次信息。
            String lotNumTag=client_code+"/PlcCraft/LotID";
//            PartNo	[PlcCraft]料号
//            PanelID	[PlcCraft]板号
            //出板的数量
            String panelOutNoTag =client_code+"/PlcStatus/PanelOutNo";
            String taskCountTag =client_code+"/PcStatus/TaskCount";
            String tagList=lotNumTag+","+panelOutNoTag+","+taskCountTag;
            //获取工单号
            String lotNum="";
            //获取剩余数量
            int remainingPieces =0;
            String panelOutNo= "";
            String taskCount= "";
            log.info("tagList:"+JSONObject.toJSONString(tagList));
            JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, tagList);
            if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
            	log.info("jsonArrayTag:"+JSONObject.toJSONString(jsonArrayTag));
                for (int i = 0; i < jsonArrayTag.size(); i++) {
                    JSONObject jbItem = jsonArrayTag.getJSONObject(i);
                    String tag_key = jbItem.getString("tag_key");
                    String tag_value = jbItem.getString("tag_value");
                    if(tag_key.equals(lotNumTag)) lotNum=tag_value;
                    if(tag_key.equals(panelOutNoTag)) panelOutNo=tag_value;
                    if(tag_key.equals(taskCountTag)) taskCount=tag_value;
                }
            }
            //判断面次是否一致。
            //写入任务面次的类型。
            String mcTag = client_code_ais + "/AisStatus/TaskType";
            String mcValue="";
            JSONArray mcjsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, mcTag);
            if (mcjsonArrayTag != null && mcjsonArrayTag.size() > 0) {
            	log.info("mcjsonArrayTag:"+JSONObject.toJSONString(mcjsonArrayTag));
                for (int i = 0; i < mcjsonArrayTag.size(); i++) {
                    JSONObject jbItem = mcjsonArrayTag.getJSONObject(i);
                    String tag_key = jbItem.getString("tag_key");
                    mcValue = jbItem.getString("tag_value");
                }
            }
            //如果是不相同的面次。则报错
            if(!org.apache.commons.codec.binary.StringUtils.equals(mcValue,readMC)) {
            	JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                String msg = "实际面次和工单面次要求不一致，工单面次要求："+mcValue+",摄像头读码出来的实际面次："+readMC;
                errorResult.put("Msg", msg);
                log.error(msg);
                writeE2DCheckFailResult(lotNum, String.valueOf(remainingPieces), readInfo, readMC,station_code, client_code_ais, client_code,CcdStartRsp_Mixture, "面板号：" + readInfo + "接口校验不通过,放板错误"+msg);
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            if(!StringUtils.isEmpty(panelOutNo)&& !StringUtils.isEmpty(taskCount)){
            	//计算剩余数量
          		remainingPieces =Integer.parseInt(taskCount)-Integer.parseInt(panelOutNo);
            }
            if (lotNum == null || lotNum.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "工单批号(LotNum)不能为空");
                log.error("工单批号(LotNum)不能为空");
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }

            if (machineId == null || machineId.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "机台编号(MachineId)不能为空");
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            if (readInfo == null || readInfo.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "读取的信息(ReadInfo)不能为空");
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            if (readMC == null || readMC.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "机台编号(ReadMC)不能为空");
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            log.info("E2DCheckEachOne API called with params:readInfo={},readMC={}, LotNum={}, RemainingPieces={}, MachineId={}",readInfo,readMC,lotNum, remainingPieces, machineId);
            // 查询接口配置
            String esbInterfCode = "E2DCheckEachOne";
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url,"
                    + "COALESCE(esb_dev_intef_url,'') esb_dev_intef_url," +
                    "COALESCE(esb_test_result,'') esb_test_result from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, request, apiRoutePath);
            if (itemList == null || itemList.size() <= 0) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据");
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            String esbProdIntefUrl = itemList.get(0).get("esb_prod_intef_url").toString();
            String esbDevIntefUrl = itemList.get(0).get("esb_dev_intef_url").toString();
            String esbTestResult = itemList.get(0).get("esb_test_result").toString();
            if (esbProdIntefUrl == null || esbProdIntefUrl.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空");
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            // 构建请求URL
            String url = esbProdIntefUrl + "?LotNum=" + lotNum + "&readInfo="+readInfo+"&readMC="+readMC+"&RemainingPieces=" + remainingPieces + "&MachineId=" + machineId;
            // 发送请求
            JSONObject responseJson = null;
            boolean success = true;
            String errMsg = "";
            JSONObject errorResult = null;
            try {
                if (esbDevIntefUrl != null && esbDevIntefUrl.startsWith("Y")) {
                    // 使用测试数据
                    log.info("使用测试数据进行E2DCheckEachOne请求");
                    responseJson = JSONObject.parseObject(esbTestResult);
                } else {
                    // 发送实际请求
                    log.info("发送实际E2DCheckEachOne请求: {}", url);
                    responseJson = restTemplate.getForObject(url, JSONObject.class);
                }
                //返回值：{"Success":true,"Messages":"放行"}
                if (responseJson == null || !responseJson.containsKey("Success") || !responseJson.getBooleanValue("Success")) {
                    // 调用提取的方法写入失败结果
                    writeE2DCheckFailResult(lotNum, String.valueOf(remainingPieces), readInfo, readMC,station_code, client_code_ais, client_code,CcdStartRsp_Mixture, "面板号：" + readInfo + "接口校验不通过,放板错误,"+responseJson.getString("Messages"));
                    success = false;
                }else {
                	//放行。
                	setCcdStartRsp(station_code,client_code,CcdStartRsp_OK);
                	log.info(JSONObject.toJSONString(responseJson));
                }
            } catch (Exception e) {
                errMsg = "E2DCheckEachOne请求发生异常: " + e.getMessage();
                log.error(errMsg, e);
                success = false;
                // 构建错误响应
                errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", errMsg);
                // 添加Content字段，用于EapHsqlInterfCommon.convertToEventFormat方法
                JSONObject content = new JSONObject();
                content.put("Error", errMsg);
                errorResult.put("Content", content);
                writeE2DCheckFailResult(lotNum, String.valueOf(remainingPieces), readInfo, readMC,station_code, client_code_ais, client_code,CcdStartRsp_NG, "面板号：" + readInfo + "接口校验异常:"+errMsg);
                return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
            } finally {
                // 记录接口日志
                String endDate = CFuncUtilsSystem.GetNowDateTime("");
                String paraStr = "LotNum=" + lotNum + "&RemainingPieces=" + remainingPieces + "&MachineId=" + machineId;
                String resStr = success && responseJson != null ? responseJson.toString() : "";
                // 判断是否使用生产环境
                boolean prodFlag = esbDevIntefUrl == null || !esbDevIntefUrl.startsWith("Y");
                cFuncLogInterf.Insert(
                    startDate,
                    endDate,
                    esbInterfCode,
                    prodFlag,
                    "",
                    paraStr,
                    resStr,
                    success,
                    errMsg,
                    request
                );
            }
            // 构建返回结果
            JSONObject result = new JSONObject();
            if (responseJson != null) {
                result.put("Success", responseJson.getBoolean("Success"));
                result.put("Msg", responseJson.getString("Messages"));
            } else {
                result.put("Success", false);
                result.put("Msg", "调用接口失败，未获取到响应");
            }
            // 添加Content字段，用于EapZhcyInterfCommon.convertToEventFormat方法
            JSONObject content = new JSONObject();
            if (responseJson != null) {
                content.put("Result", responseJson.getString("Messages"));
            } else {
                content.put("Result", "调用接口失败，未获取到响应");
            }
            result.put("Content", content);
            return EapHsqlInterfCommon.convertToEventFormat(result).toJSONString();
        } catch (Exception ex) {
            String errorMsg = "生產中片级檢查发生异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            JSONObject errorResult = new JSONObject();
            errorResult.put("Success", false);
            errorResult.put("Msg", errorMsg);
            JSONObject content = new JSONObject();
            content.put("Error", errorMsg);
            errorResult.put("Content", content);
            return EapHsqlInterfCommon.convertToEventFormat(errorResult).toJSONString();
        }
    }
    
    /***
     * 通过角度获取面次数据。
     * @param jiaodu
     * @return
     */
    public String  getMcInfo(int jiaodu){
        String MC_S = this.cFuncDbSqlResolve.GetParameterValue("MC_S");
        if (StringUtils.isEmpty(MC_S)) {
          MC_S = "140-230"; 
        }
        String MC_C = this.cFuncDbSqlResolve.GetParameterValue("MC_C");
        if (StringUtils.isEmpty(MC_C)) {
          MC_C = "231-280"; 
        }
        int readMCInt =jiaodu;
        String readMC = "";
        if (readMCInt >= Integer.parseInt(StringUtils.split(MC_S, "-")[0]) && readMCInt <= Integer.parseInt(StringUtils.split(MC_S, "-")[1])) {
        	readMC = "S";
        } else if (readMCInt >= Integer.parseInt(StringUtils.split(MC_C, "-")[0]) && readMCInt <= Integer.parseInt(StringUtils.split(MC_C, "-")[1])) {
        	readMC = "C";
        }
        return readMC;
    }

    /**
     * 获取设备的PLC客户端代码
     * 根据设备ID查询对应的PLC客户端代码
     * @param station_code 设备ID
     * @param clientType 客户端类型，例如 "Plc" 或 "Ais"
     * @param request HTTP请求对象，可为null
     * @param apiRoutePath API路径，可为null
     * @return 客户端代码，如果未找到则返回空字符串
     * @throws Exception 当查询失败时抛出异常
     */
    public String getClientCode(String station_code, String clientType, HttpServletRequest request, String apiRoutePath) throws Exception {
        if (station_code == null || station_code.isEmpty()) {
            throw new Exception("设备ID不能为空");
        }
        if (clientType == null || clientType.isEmpty()) {
            // 默认使用PLC客户端
            clientType = "Plc";
        }
        String sqlClientCode = "SELECT client_code from scada_client where station_code='" + station_code + "' and client_code like '%" + clientType + "' and enable_flag='Y'";
        List<Map<String, Object>> lstsqlClientCode = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlClientCode, false, request, apiRoutePath);
        if (lstsqlClientCode == null || lstsqlClientCode.isEmpty()) {
            throw new Exception("未找到设备的" + clientType + "客户端配置: " + station_code);
        }
        return lstsqlClientCode.get(0).get("client_code").toString();
    }

    /**
     * 通用配方下发方法
     * 根据站点代码和配方信息下发配方到设备
     *
     * @param stationCode 站点代码
     * @param recipeName 配方名称（可选，与recipeId二选一）
     * @param recipeVersion 配方版本（可选）
     * @param recipeId 配方ID（可选，与recipeName二选一）
     * @param customParams 自定义参数Map，用于替换配方中的特定值
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @return 下发结果，true表示成功，false表示失败
     */
    public boolean deployRecipeToDevice(String stationCode, String recipeName, String recipeVersion,
                                      Long recipeId, Map<String, String> customParams,
                                      HttpServletRequest request, String apiRoutePath) {
        try {
            if (stationCode == null || stationCode.isEmpty()) {
                log.error("配方下发失败：站点代码不能为空");
                return false;
            }

            // 构建SQL查询条件
            StringBuilder whereCondition = new StringBuilder();
            if (recipeId != null) {
                whereCondition.append(" AND recipe.recipe_id = ").append(recipeId);
            } else if (recipeName != null && !recipeName.isEmpty()) {
                whereCondition.append(" AND recipe.recipe_name = '").append(recipeName).append("'");
                if (recipeVersion != null && !recipeVersion.isEmpty()) {
                    whereCondition.append(" AND recipe.recipe_version = '").append(recipeVersion).append("'");
                }
            } else {
                log.error("配方下发失败：必须提供配方名称或配方ID");
                return false;
            }

            // 执行SQL查询获取配方数据
            String recipeSql = "SELECT " +
                    "concat(client.client_code, '/', gro.tag_group_code, '/', d.parameter_code) AS tag, " +
                    "d.parameter_val, " +
                    "d.parameter_code " +
                    "FROM " +
                    "( " +
                    "    SELECT recipe_id, recipe_name, recipe_version " +
                    "    FROM a_eap_fmod_recipe recipe" +
                    "    WHERE 1=1 " + whereCondition.toString() + " " +
                    "    AND enable_flag = 'Y' " +
                    ") AS recipe " +
                    "LEFT JOIN a_eap_fmod_recipe_detail AS d " +
                    "    ON recipe.recipe_id = d.recipe_id " +
                    "LEFT JOIN scada_tag AS tag " +
                    "    ON d.tag_id = tag.tag_id " +
                    "LEFT JOIN scada_tag_group AS gro " +
                    "    ON tag.tag_group_id = gro.tag_group_id " +
                    "LEFT JOIN scada_client AS client " +
                    "    ON gro.client_id = client.client_id " +
                    "WHERE d.enable_flag = 'Y' " +
                    "AND client.station_code = '" + stationCode + "'";

            List<Map<String, Object>> recipeList = cFuncDbSqlExecute.ExecSelectSql("EAP", recipeSql, false, request, apiRoutePath);

            if (recipeList == null || recipeList.isEmpty()) {
                log.warn("配方下发失败：未找到配方数据，stationCode={}, recipeName={}, recipeVersion={}, recipeId={}",
                        stationCode, recipeName, recipeVersion, recipeId);
                return false;
            }

            // 将查询结果转换为Map
            Map<String, String> recipeMap = new LinkedHashMap<>();
            for (Map<String, Object> row : recipeList) {
                String tag = row.get("tag") != null ? row.get("tag").toString() : "";
                String parameterVal = row.get("parameter_val") != null ? row.get("parameter_val").toString() : "null";
                String parameterCode = row.get("parameter_code") != null ? row.get("parameter_code").toString() : "";

                if (!tag.isEmpty()) {
                    recipeMap.put(tag, parameterVal);
                }
            }

            // 应用自定义参数替换
            if (customParams != null && !customParams.isEmpty()) {
                for (Map.Entry<String, String> entry : recipeMap.entrySet()) {
                    String key = entry.getKey();

                    // 根据参数代码结尾匹配自定义参数
                    for (Map.Entry<String, String> customParam : customParams.entrySet()) {
                        if (key.endsWith(customParam.getKey())) {
                            recipeMap.put(key, customParam.getValue() != null ? customParam.getValue() : "");
                            break;
                        }
                    }
                }
            } 
            log.info("配方下发Map: {}", JSONObject.toJSONString(recipeMap));
            // 将Map转换为两个字符串
            StringBuilder keysBuilder = new StringBuilder();
            StringBuilder valuesBuilder = new StringBuilder();

            boolean first = true;
            for (Map.Entry<String, String> entry : recipeMap.entrySet()) {
                if (!first) {
                    keysBuilder.append(",");
                    valuesBuilder.append("&");
                }
                keysBuilder.append(entry.getKey());
                valuesBuilder.append(entry.getValue());
                first = false;
            }

            String tagsString = keysBuilder.toString();
            String valuesString = valuesBuilder.toString();

            log.info("配方下发 - StationCode: {}, RecipeName: {}, RecipeVersion: {}, RecipeId: {}",
                    stationCode, recipeName, recipeVersion, recipeId);
            log.info("配方下发 - Tags: {}", tagsString);
            log.info("配方下发 - Values: {}", valuesString);

            // 调用SCADA方法下发配方
            cFuncUtilsCellScada.WriteTagByStation("EAP", stationCode, tagsString, valuesString, false);

            log.info("配方下发成功: StationCode={}, RecipeName={}, RecipeVersion={}, RecipeId={}",
                    stationCode, recipeName, recipeVersion, recipeId);
            return true;

        } catch (Exception e) {
            log.error("配方下发失败: StationCode={}, RecipeName={}, RecipeVersion={}, RecipeId={}, Error={}",
                    stationCode, recipeName, recipeVersion, recipeId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 简化版配方下发方法 - 通过配方名称
     *
     * @param stationCode 站点代码
     * @param recipeName 配方名称
     * @param customParams 自定义参数Map
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @return 下发结果，true表示成功，false表示失败
     */
    public boolean deployRecipeByName(String stationCode, String recipeName, Map<String, String> customParams,
                                    HttpServletRequest request, String apiRoutePath) {
        return deployRecipeToDevice(stationCode, recipeName, null, null, customParams, request, apiRoutePath);
    }

    /**
     * 简化版配方下发方法 - 通过配方ID
     *
     * @param stationCode 站点代码
     * @param recipeId 配方ID
     * @param customParams 自定义参数Map
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @return 下发结果，true表示成功，false表示失败
     */
    public boolean deployRecipeById(String stationCode, Long recipeId, Map<String, String> customParams,
                                  HttpServletRequest request, String apiRoutePath) {
        return deployRecipeToDevice(stationCode, null, null, recipeId, customParams, request, apiRoutePath);
    }

    /**
     * 写入E2D检查失败结果标签
     * 当面板检查失败时，向SCADA系统写入失败信息
     * @param lotNum 工单批号
     * @param remainingPieces 剩余数量
     * @param readInfo 读码信息
     * @param readMC 读码面次
     * @param station_code 工位代码
     * @param client_code_ais AIS客户端代码
     * @param errorMsg 错误消息
     */
    private void writeE2DCheckFailResult(String lotNum, String remainingPieces, String readInfo,String readMC, String station_code, String client_code_ais,String client_code,String ccdCode, String errorMsg) {
        try {
        	//设置摄像头的读取格式。拦截板子。
        	setCcdStartRsp(station_code,client_code,ccdCode);
            // 把失败的信息弹窗
            String faillTag = client_code_ais + "/AisStatus/E2DCheckFaillResult";
            String faillMsg = "";
            // 构建失败信息Map
            Map<String, String> failMap = new HashMap<>();
            failMap.put("LotNum", lotNum);
            failMap.put("RemainingPieces", remainingPieces + "");
            failMap.put("ReadInfo", readInfo);
            failMap.put("ReadMC", readMC);
            failMap.put("Time", CFuncUtilsSystem.GetNowDateTime(""));
            failMap.put("Msg", errorMsg);
            failMap.put("CcdCode", ccdCode);
            faillMsg = JSONObject.toJSONString(failMap);
            cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, faillTag, faillMsg, false);
            log.info("写入E2D检查失败结果: LotNum={}, ReadInfo={}, ReadMC={}, ErrorMsg={}",lotNum, readInfo, readMC, errorMsg);
        } catch (Exception e) {
            log.error("写入E2D检查失败结果时发生异常: " + e.getMessage(), e);
        }
    }


    /**
     * 写入允许继续生产。
     * @param lotNum 工单批号
     * @param remainingPieces 剩余数量
     * @param readInfo 读码信息
     * @param readMC 读码面次
     * @param station_code 工位代码
     * @param client_code_ais AIS客户端代码
     * @param ccdCode //[PcStatus]读头扫码OK/NG	 1 OK  2读码超时或NG  3基板混料 4系统无数据下发
     * @param errorMsg 错误消息
     */
    private void setCcdStartRsp(String station_code, String client_code,String ccdCode) {
        try {
        	//[PcStatus]读头扫码OK/NG	 1 OK  2读码超时或NG  3基板混料 4系统无数据下发
            String ccdStartRspTag = client_code + "/PcStatus/CcdStartRsp";
            cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, ccdStartRspTag, ccdCode, false);
        } catch (Exception e) {
            log.error("写入AllowWork结果时发生异常: " + e.getMessage(), e);
        }
    }

    /**
     * ok
     */
    public static String CcdStartRsp_OK="1";
    /**
     * 2读码超时或NG
     */
    public static String CcdStartRsp_NG="2";
    /**
     * 基板混料
     */
    public static String CcdStartRsp_Mixture="3";
    /**
     * 系统无数据下发
     */
    public static String CcdStartRsp_NotSend="4";

}