package com.api.eap.project.xinai;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsRest;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <p>
 * 芯爱LDI接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Service
@Slf4j
public class EapXinaiLdiInterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //创建LDI返回报文
    public String CreateResponse(String function_name,String trx_id,
                                 JSONObject body,Integer code,String message) throws Exception{
        JSONObject jbSend=new JSONObject();
        //头
        JSONObject jbHeader=new JSONObject();
        jbHeader.put("MessageName",function_name);
        jbHeader.put("TransactionID",trx_id);
        //内容
        JSONObject jbBody=body;
        if(jbBody==null) jbBody=new JSONObject();
        //结果
        JSONObject jbResult=new JSONObject();
        jbResult.put("Code",code);
        jbResult.put("Message",message);
        //返回
        jbSend.put("Header",jbHeader);
        jbSend.put("Body",jbBody);
        jbSend.put("Result",jbResult);
        String tranResult=jbSend.toString();
        return tranResult;
    }
}
