package com.api.eap.project.zhcy;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import javax.servlet.http.HttpServletRequest;

import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

/**
 * 珠海超毅项目的业务逻辑处理类，负责设备任务管理及PLC交互。
 * <AUTHOR>
 */
@Service
@Slf4j
public class EapZhcySendBusinessFunc {

    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private EapZhcyInterfCommon eapZhcyInterfCommon;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private  EapZhcySendInterfFunc eapZhcySendInterfFunc;
    // 常量定义
    private static final String A_EAP_APS_PLAN = "a_eap_aps_plan"; // MongoDB集合名称
    private static final int MAX_RETRIES = 30; // PLC响应最大重试次数
    private static final int RETRY_INTERVAL_MS = 1000; // 重试间隔（毫秒）
    private static final int TASK_SWITCH_MAX_RETRIES = 60; // 任务切换最大重试次数
    private static final String PLC_CLIENT_TYPE = "Plc"; // PLC客户端类型
    private static final String DATE_TIME_FORMAT = "yyyy/MM/dd HH:mm:ss"; // 日期时间格式

    /**
     * PLC标签配置类，用于集中管理PLC标签路径。
     */
    @Data
    private static class PlcTagConfig {
    	/**
    	 *配方更新标志 (D6012)
    	 */
        private final String recipeUpdateTag;
        /**
         * 配方下发请求 (D6104)
         */
        private final String recipeDownRequestTag;
        /**
         * 配方选择完成标志 (D6011)
         */
        private final String recipeSelFinishTag;
//        /**
//         * 设备启动标志 (D6101)
//         */
//        private final String appStartTag;
        /**
         * 允许生产标志 (D6109)
         */
        private final String allowWorkTag;
        /**
         * 物料更换警报 (D6119)
         */
        private final String materialChangeAlertTag;
        /**
         * 任务切换标志 (D6117)
         */
        private final String changeTaskFlagTag;
        /**
         * 批次结束标志 (D6014)
         */
        private final String lotEndTag;
        /**
         * 物料确认标志
         */
        private final String confirmPartNoFlagTag;
        /**
         * 重新上机标志 (D6118)
         */
        private final String reloadMachineFlagTag;
        /**
         * 蜂鸣器 (D6102)
         */
        private final String buzzerTag;

        PlcTagConfig(String clientCode) {
            this.recipeUpdateTag = clientCode + "/PlcStatus/RecipeUpd";
            this.recipeDownRequestTag = clientCode + "/PcStatus/RecipeDownReq";
            this.recipeSelFinishTag = clientCode + "/PlcStatus/RecipeSelFinish";
//            this.appStartTag = clientCode + "/PcStatus/AppStart";
            this.allowWorkTag = clientCode + "/PcStatus/AllowWork";
            this.materialChangeAlertTag = clientCode + "/PcStatus/MaterialChangeAlert";
            this.changeTaskFlagTag = clientCode + "/PcStatus/ChangeTaskFlag";
            this.lotEndTag = clientCode + "/PlcStatus/LotEnd";
            this.confirmPartNoFlagTag = clientCode + "/PcStatus/ConfirmPartNoFlag";
            this.reloadMachineFlagTag = clientCode + "/PcStatus/ReLoadMachineFlag";
            this.buzzerTag = clientCode + "/PcStatus/Buzzer";
        }
    }

    /**
     * 处理扫描干膜物料标签并上机的流程：
     * 1. 验证二维码
     * 2. 检查是否存在WORK状态任务
     * 3. 查询最早的PLAN状态任务
     * 4. 调用EQP-EAP-025接口校验物料
     * 5. 下发配方到PLC并更新任务状态为WORK
     * 6. 启动设备生产
     * @param jsonParas 请求参数，包含QRCode、RequestId等
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @return 处理结果JSON对象
     */
    public JSONObject processScanAndLoad(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) {
    	String eqpId = jsonParas.getString("stationCode");
        log.info("开始处理扫描二维码并上机: EqpId={}", eqpId);
        String requestId = jsonParas.getString("RequestId");
        JSONObject result = createDefaultResult();
        try {
            // 验证二维码
            String qrCode = jsonParas.getString("qrCode");
            if (!StringUtils.hasText(qrCode)) {
                return failResult("二维码为空", requestId, "ScanAndLoad", jsonParas.toString());
            }
            log.info("扫描二维码: EqpId={}, QRCode={}", eqpId, qrCode);

            // 查询最早的PLAN状态任务
            Optional<Document> planTask = findEarliestPlanTask(eqpId);
            if (!planTask.isPresent()) {
                return failResult("未找到PLAN状态任务", requestId, "ScanAndLoad", jsonParas.toString());
            }
            Document planDoc = planTask.get();
            String planId = planDoc.getString("plan_id");
            String pn = planDoc.getString("pn");
            Integer totalPanelCount = planDoc.getInteger("total_panel_count");
            // recipe_parameter_list 是一个列表，不能直接使用 getString 方法
            Object recipeDataObj = planDoc.getString("recipe_parameter_list");
            String recipeData = recipeDataObj != null ? recipeDataObj.toString() : "";
            log.info("找到PLAN状态任务: planId={}, pn={}", planId, pn);
            // 校验物料
            if (!validateMaterial(eqpId, qrCode, pn, request, apiRoutePath)) {
                return failResult("物料校验失败，干膜物料与任务不匹配", requestId, "ScanAndLoad", jsonParas.toString());
            }
            // 检查是否存在WORK状态任务
            if (hasWorkTask(eqpId)) {
                return createWorkTaskExistsResponse(requestId);
            }
            // 获取PLC客户端代码和标签配置
            String clientCode = getClientCode(eqpId, PLC_CLIENT_TYPE, request, apiRoutePath);
            PlcTagConfig plcTags = new PlcTagConfig(clientCode);

            // 检查是否允许下发配方
            if (!isRecipeUpdateAllowed(eqpId, plcTags)) {
                return failResult("设备当前不允许下发配方", requestId, "ScanAndLoad", jsonParas.toString());
            }
            // 下发配方到PLC
            if (!downloadRecipeToPlc(eqpId, recipeData, clientCode)) {
                return failResult("配方下发失败", requestId, "ScanAndLoad", jsonParas.toString());
            }
            // 请求配方下发并等待PLC响应： 最长等待30s
            if (!requestAndWaitForPlcResponse(eqpId, plcTags)) {
                return failResult("PLC未响应配方下发请求", requestId, "ScanAndLoad", jsonParas.toString());
            }
            // 设置 允许设备生产 /PcStatus/AllowWork = 1
            allowProduction(eqpId, plcTags);

            // 设置重新上机标志为2（上机成功）
            String reloadResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, plcTags.getReloadMachineFlagTag(), "2", true);
            if (!reloadResult.isEmpty()) {
                log.warn("设置重新上机标志D6118=2失败: {}", reloadResult);
                // 继续执行，不返回错误
            } else {
                log.info("已设置重新上机标志D6118=2: EqpId={}", eqpId);
            }

            // 更新任务状态为WORK
            updateTaskStatus(planId, "WORK");
            // 上报任务开始
            reportJobDataProcess(eqpId, planId, totalPanelCount,0, "Start", request, apiRoutePath);
            return createSuccessResult(planId, pn, qrCode, requestId);
        } catch (Exception ex) {
            log.error("扫描并上机异常: {}", ex.getMessage(), ex);
            return failResult("扫描并上机异常: " + ex.getMessage(), requestId, "ScanAndLoad", jsonParas.toString());
        }
    }
    


    /**
     * 如果设备模式变成  模式0:离线; 1:在线/远程;2:在线/本地。  在线模式下，任务生产中，此时切到离线模式，任务状态建议改成取消，CANCLE 
     * @param jsonParas
     * @param request
     * @param apiRoutePath
     * @return
     */
	public JSONObject deviceModeChange(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject res = new JSONObject();
        try {
            // 验证参数
        	//string paras = "stationId=" + stationId + "&deviceMode=" + deviceMode;
            String stationId = jsonParas.getString("stationId");
            String eqpId = jsonParas.getString("stationCode");
            String deviceMode = jsonParas.getString("deviceMode");
            //离线模式。
            if("0".equals(deviceMode)) {
            	// 更新当前WORK任务为FINISH
                Optional<Document> workTask = findWorkTask(eqpId);
                if (workTask.isPresent()) {
                    String planId = workTask.get().getString("plan_id");
                    updateTaskStatus(planId, "CANCEL","在线模式切换到离线，任务取消");
                    //任务执行失败。
                    reportJobDataProcess(eqpId, planId, 0, 0, "Failed", request, apiRoutePath);
                    log.info("已更新任务状态为CANCEL,Failed 并上报: planId={}", planId);
                } else {
                    log.warn("未找到WORK状态任务");
                }
            }
            res.put("Success", true);
            res.put("Msg", "任务切换流程完成");
        }catch (Exception e) {
        	log.error(e.getMessage());
        	res.put("Success", false);
            res.put("Msg", "任务切换流程异常"+e.getMessage());
		}
		return res;
	}

    /**
     * 检查设备是否可以下发配方并执行下发流程：
     * 1. 检查PLC配方更新标志 (D6012=1)
     * 2. 检查是否存在WORK状态任务
     * 3. 查询最早的PLAN状态任务
     * 4. 下发配方到PLC并更新任务状态为WORK
     * @param eqpId 设备ID
     * @param request HTTP请求对象，可为null
     * @param apiRoutePath API路径，可为null
     *
     * @return 处理结果JSON对象
     */
    public JSONObject checkAndDownloadRecipe(String eqpId, HttpServletRequest request, String apiRoutePath,Optional<Document> planTask ) {
        log.info("开始检查配方下发: EqpId={}", eqpId);
        JSONObject result = createDefaultResult();

        try {
            // 获取PLC客户端代码和标签配置
            String clientCode = getClientCode(eqpId, PLC_CLIENT_TYPE, request, apiRoutePath);
            PlcTagConfig plcTags = new PlcTagConfig(clientCode);
            // 检查是否上机成功
            if (!checkReloadMachineFlag(eqpId, plcTags)) {
                result.put("Msg", "请先扫物料标签上机");
                return result;
            }
            // 检查是否允许下发配方
            if (!isRecipeUpdateAllowed(eqpId, plcTags)) {
                result.put("Msg", "设备当前不允许下发配方");
                return result;
            }

            // 检查是否存在WORK状态任务
            if (hasWorkTask(eqpId)) {
                result.put("Msg", "存在WORK状态任务，无需下发新配方");
                return result;
            }

            // 查询最早的PLAN状态任务
            if(planTask==null) {
            	planTask = findEarliestPlanTask(eqpId);
            }
            if (!planTask.isPresent()) {
                result.put("Msg", "未找到PLAN状态任务");
                return result;
            }

            Document planDoc = planTask.get();
            String planId = planDoc.getString("plan_id");
            String recipeData = planDoc.getString("recipe_data");

            log.info("找到PLAN状态任务: planId={}", planId);

            // 下发配方到PLC
            if (!downloadRecipeToPlc(eqpId, recipeData, clientCode)) {
                return failResult("配方下发失败", "", "checkAndDownloadRecipe", "");
            }

            // 请求配方下发并等待PLC响应
            if (!requestAndWaitForPlcResponse(eqpId, plcTags)) {
                return failResult("PLC未响应配方下发请求", "", "checkAndDownloadRecipe", "");
            }
            // 允许设备生产
            allowProduction(eqpId, plcTags);
            // 更新任务状态为WORK
            updateTaskStatus(planId, "WORK");
            // 上报任务开始
            reportJobDataProcess(eqpId, planId, 0, 0, "Start", request, apiRoutePath);
            result.put("Success", true);
            result.put("Msg", "配方下发成功: planId=" + planId);
            result.put("PlanId", planId);
            return result;
        } catch (Exception ex) {
            log.error("检查并下发配方异常: {}", ex.getMessage(), ex);
            return failResult("检查并下发配方异常: " + ex.getMessage(), "", "checkAndDownloadRecipe", "");
        }
    }


    /**
     * 处理任务切换流程：
     * 1. 等待批次结束信号 (D6014=1)
     * 2. 更新任务状态为FINISH并上报
     * 3. 重置任务切换标志 (D6117=0)
     * 4. 检查并下发相同料号的PLAN任务配方
     * @param eqpId 设备ID
     * @param request HTTP请求对象，可为null
     * @param apiRoutePath API路径，可为null
     * @return 处理结果JSON对象
     */
    public JSONObject LotEndEvent(String eqpId, HttpServletRequest request, String apiRoutePath) {
        log.info("开始任务切换流程: EqpId={}", eqpId);
        JSONObject result = createDefaultResult();
        try {
            // 获取PLC客户端代码和标签配置
            String clientCode = getClientCode(eqpId, PLC_CLIENT_TYPE, request, apiRoutePath);
            PlcTagConfig plcTags = new PlcTagConfig(clientCode);
            log.info("检测到批次结束信号D6014=1: EqpId={}", eqpId);
            // 更新当前WORK任务为FINISH
            Optional<Document> workTask = findWorkTask(eqpId);
            if (workTask.isPresent()) {
                String planId = workTask.get().getString("plan_id");
                updateTaskStatus(planId, "FINISH");
                reportJobDataProcess(eqpId, planId, 0, 0, "End", request, apiRoutePath);
                log.info("已更新任务状态为FINISH并上报: planId={}", planId);
            } else {
                log.warn("未找到WORK状态任务");
            }
            
            // 重置任务切换标志
            cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, plcTags.getChangeTaskFlagTag(), "0", true);
            // 检查并下发相同料号的PLAN任务配方
            checkAndDownloadRecipeWithMaterialCheck(eqpId, request, apiRoutePath,workTask,plcTags);
            result.put("Success", true);
            result.put("Msg", "任务切换流程完成");
            return result;
        } catch (Exception ex) {
            log.error("任务切换异常: {}", ex.getMessage(), ex);
            return failResult("任务切换异常: " + ex.getMessage(), "", "handleTaskChange", "");
        }
    }


    /**
     * 检查是否有相同料号的PLAN任务并自动下发配方：
     * 1. 查询最近完成的FINISH任务的物料编码
     * 2. 查询最早的PLAN任务
     * 3. 比较物料编码是否匹配
     * 4. 若匹配，自动下发配方；若不匹配，触发换膜警报
     *
     * @param eqpId 设备ID
     * @param request HTTP请求对象，可为null
     * @param apiRoutePath API路径，可为null
     * @return 处理结果JSON对象
     */
    public JSONObject checkAndDownloadRecipeWithMaterialCheck(String eqpId, HttpServletRequest request, String apiRoutePath,Optional<Document> finishTask,PlcTagConfig plcTags) {
        log.info("检查相同料号的PLAN任务: EqpId={}", eqpId);
        JSONObject result = createDefaultResult();
        try {
            // 获取PLC客户端代码和标签配置
            String finishPn = finishTask.get().getString("pn");
            // 查询最早的PLAN任务
            Optional<Document> planTask = findEarliestPlanTask(eqpId);
            if (!planTask.isPresent()) {
                result.put("Msg", "无PLAN状态任务");
                return result;
            }
            Document planDoc = planTask.get();
            String pn = planDoc.getString("pn");
            String planId = planDoc.getString("plan_id");
            // 检查物料是否匹配
            if (finishPn.equals(pn)) {
                log.info("料号匹配，自动下发配方: planId={}, Pn={}", planId, pn);
                return checkAndDownloadRecipe(eqpId, request, apiRoutePath,planTask);
            }
            // 料号不匹配，触发换膜警报
            log.info("料号不匹配，触发换膜警报: finishPn={}, pn={}", finishPn, pn);
            triggerMaterialChangeAlert(eqpId, plcTags);
            result.put("Success", true);
            result.put("Msg", "料号不同，需人工换膜，已触发换膜警报");
            result.put("finishPn", finishPn);
            result.put("pn", pn);
            return result;
        } catch (Exception ex) {
            log.error("检查物料匹配性异常: {}", ex.getMessage(), ex);
            return failResult("检查物料匹配性异常: " + ex.getMessage(), "", "checkAndDownloadRecipeWithMaterialCheck", "");
        }
    }


    /**
     * 上报设备任务进展信息：
     * 支持多种状态：
     *  Create: 创建任务
		Update：	更新任务
		Failed：	执行任务失败
		Delete：	删除任务
		Start ：	开始生产
		End：	生产结束
     * @param eqpId 设备ID
     * @param jobId 任务ID
     * @param totalCount 任务总数量
     * @param okCount 任务OK板数量
     * @param processCode 任务状态码
     * @param request HTTP请求对象，可为null
     * @param apiRoutePath API路径，可为null
     * @return 处理结果JSON对象
     */
    @Async
    public JSONObject reportJobDataProcess(String eqpId, String jobId, int totalCount, int okCount, String processCode,HttpServletRequest request, String apiRoutePath) {
        log.info("开始上报任务进展: EqpId={}, JobId={}, ProcessCode={}, TotalCount={}, OkCount={}",eqpId, jobId, processCode, totalCount, okCount);
        try {
            // 验证参数
            if (!StringUtils.hasText(eqpId)) {
                return failResult("设备ID不能为空", "", "EQP_EquipmentJobDataProcessReport", "");
            }
            if (!StringUtils.hasText(jobId)) {
                return failResult("任务ID不能为空", "", "EQP_EquipmentJobDataProcessReport", "");
            }
            if (!StringUtils.hasText(processCode)) {
                return failResult("任务状态码不能为空", "", "EQP_EquipmentJobDataProcessReport", "");
            }
            // 验证processCode有效性
            if (!Arrays.asList("Create", "Update", "Failed", "Start", "End", "Delete").contains(processCode)) {
                return failResult("无效的任务状态码: " + processCode, "", "EQP_EquipmentJobDataProcessReport", "");
            }
            // 查询接口配置
            String esbInterfCode = "EQP_EquipmentJobDataProcessReport";
            String sqlInterf = "SELECT COALESCE(esb_prod_intef_url,'') esb_prod_intef_url, " +
                    "COALESCE(esb_dev_intef_url,'') esb_dev_intef_url, " +
                    "COALESCE(esb_test_result,'') esb_test_result " +
                    "FROM sys_core_esb_interf WHERE esb_interf_code='" + esbInterfCode + "' AND enable_flag='Y' LIMIT 1";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, request, apiRoutePath);
            if (itemList.isEmpty()) {
                return failResult("未配置接口: " + esbInterfCode, "", esbInterfCode, "");
            }
            // 获取接口配置
            Map<String, Object> config = itemList.get(0);
            String esbProdIntefUrl = config.get("esb_prod_intef_url").toString();
            if (!StringUtils.hasText(esbProdIntefUrl)) {
                return failResult("接口PROD-URL为空: " + esbInterfCode, "", esbInterfCode, "");
            }
            boolean useTestData = StringUtils.startsWithIgnoreCase(config.get("esb_dev_intef_url").toString(), "Y") && StringUtils.hasText(config.get("esb_test_result").toString());
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("EqpId", eqpId);
            requestBody.put("JobId", jobId);
            requestBody.put("TotalCount", totalCount);
            requestBody.put("OkCount", okCount);
            requestBody.put("ProcessCode", processCode);

            JSONObject paraJsonObject = EapZhcyInterfCommon.CreateHeader(esbInterfCode);
            paraJsonObject.put("Content", requestBody);
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            // 调用接口或返回测试数据
            JSONObject result= new JSONObject();
            if (useTestData) {
                log.info("任务进展上报使用测试数据: {}", config.get("esb_test_result"));
                result = JSONObject.parseObject(config.get("esb_test_result").toString());
                result.put("Msg", "使用数据库配置测试数据返回");
            } else {
                result = eapZhcySendInterfFunc.PostJbBackJb(esbProdIntefUrl, paraJsonObject,esbInterfCode,request,apiRoutePath);
            }
            // 记录日志
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            cFuncLogInterf.Insert(startDate, endDate, "EQP_EquipmentJobDataProcessReport", true, "", paraJsonObject.toString(), result.toString(), result.getBoolean("Success"), result.getString("Msg"), request);
            log.info("任务进展上报完成: Success={}", result.getBoolean("Success"));
            return result;
        } catch (Exception ex) {
            log.error("任务进展上报异常: {}", ex.getMessage(), ex);
            return failResult("任务进展上报异常: " + ex.getMessage(), "", "EQP_EquipmentJobDataProcessReport", "");
        }
    }

    /**
     * 获取设备的PLC客户端代码。
     *
     * @param eqpId 设备ID
     * @param clientType 客户端类型（如Plc、Ais）
     * @param request HTTP请求对象，可为null
     * @param apiRoutePath API路径，可为null
     * @return PLC客户端代码
     * @throws Exception 查询失败时抛出异常
     */
    public String getClientCode(String eqpId, String clientType, HttpServletRequest request, String apiRoutePath) throws Exception {
        if (!StringUtils.hasText(eqpId)) {
            throw new Exception("设备ID不能为空");
        }
        String effectiveClientType = StringUtils.hasText(clientType) ? clientType : PLC_CLIENT_TYPE;
        String sqlClientCode = "SELECT client_code FROM scada_client WHERE station_code='" + eqpId + "' AND client_code LIKE '%" + effectiveClientType + "' AND enable_flag='Y'";
        List<Map<String, Object>> lstsqlClientCode = cFuncDbSqlExecute.ExecSelectSql(eqpId, sqlClientCode, false, request, apiRoutePath);
        if (lstsqlClientCode.isEmpty()) {
            throw new Exception("未找到设备" + effectiveClientType + "客户端配置: " + eqpId);
        }
        return lstsqlClientCode.get(0).get("client_code").toString();
    }

    // 工具方法

    /**
     * 创建默认的JSON结果对象。
     */
    private JSONObject createDefaultResult() {
        JSONObject result = new JSONObject();
        result.put("Success", false);
        result.put("Msg", "");
        result.put("isSaveFlag", true);
        return result;
    }

    /**
     * 创建失败结果。
     */
    private JSONObject failResult(String msg, String requestId, String interfCode, String requestParas) {
        log.error(msg);
        return EapZhcyInterfCommon.CreateFaillResult(msg, requestId, interfCode, requestParas);
    }

    /**
     * 检查是否存在WORK状态任务。
     */
    private boolean hasWorkTask(String eqpId) {
        Query query = new Query(Criteria.where("lot_status").is("WORK")
                .and("enable_flag").is("Y")
                .and("eqp_id").is(eqpId));
        return mongoTemplate.count(query, A_EAP_APS_PLAN) > 0;
    }

    /**
     * 查询最早的PLAN状态任务。
     */
    private Optional<Document> findEarliestPlanTask(String eqpId) {
        Query query = new Query(Criteria.where("lot_status").is("PLAN")
                .and("enable_flag").is("Y")
                .and("eqp_id").is(eqpId))
                .with(Sort.by(Sort.Direction.ASC, "create_time"))
                .limit(1);
        try (MongoCursor<Document> cursor = mongoTemplate.getCollection(A_EAP_APS_PLAN)
                .find(query.getQueryObject())
                .sort(query.getSortObject())
                .noCursorTimeout(true)
                .batchSize(1)
                .iterator()) {
            return cursor.hasNext() ? Optional.of(cursor.next()) : Optional.empty();
        }
    }

    /**
     * 查询PLAN的 一个任务。
     */
    public Optional<Document> findPlanTaskById(String eqpId,String planId) {
        Query query = new Query(Criteria.where("lot_status").is("PLAN")
                .and("enable_flag").is("Y")
                .and("eqp_id").is(eqpId)
        		.and("plan_id").is(planId))
                .with(Sort.by(Sort.Direction.ASC, "create_time"))
                .limit(1);
        try (MongoCursor<Document> cursor = mongoTemplate.getCollection(A_EAP_APS_PLAN)
                .find(query.getQueryObject())
                .sort(query.getSortObject())
                .noCursorTimeout(true)
                .batchSize(1)
                .iterator()) {
            return cursor.hasNext() ? Optional.of(cursor.next()) : Optional.empty();
        }
    }

    /**
     * 查询当前WORK状态任务。
     */
    private Optional<Document> findWorkTask(String eqpId) {
        Query query = new Query(Criteria.where("lot_status").is("WORK")
                .and("enable_flag").is("Y")
                .and("eqp_id").is(eqpId))
                .limit(1);
        try (MongoCursor<Document> cursor = mongoTemplate.getCollection(A_EAP_APS_PLAN)
                .find(query.getQueryObject())
                .noCursorTimeout(true)
                .batchSize(1)
                .iterator()) {
            return cursor.hasNext() ? Optional.of(cursor.next()) : Optional.empty();
        }
    }

    /**
     * 查询最近完成的FINISH任务。
     */
    private Optional<Document> findLatestFinishTask(String eqpId) {
        Query query = new Query(Criteria.where("lot_status").is("FINISH")
                .and("enable_flag").is("Y")
                .and("eqp_id").is(eqpId))
                .with(Sort.by(Sort.Direction.DESC, "update_time"))
                .limit(1);
        try (MongoCursor<Document> cursor = mongoTemplate.getCollection(A_EAP_APS_PLAN)
                .find(query.getQueryObject())
                .sort(query.getSortObject())
                .noCursorTimeout(true)
                .batchSize(1)
                .iterator()) {
            return cursor.hasNext() ? Optional.of(cursor.next()) : Optional.empty();
        }
    }

    /**
     * 校验物料是否匹配。
     * @throws Exception
     */
    private boolean validateMaterial(String eqpId, String qrCode, String pn, HttpServletRequest request,String apiRoutePath) throws Exception {
        String materialCheckInterfCode = "EQP_MaterialLableReport";
        String sql = "SELECT COALESCE(esb_prod_intef_url,'') esb_prod_intef_url, " +
                "COALESCE(esb_dev_intef_url,'') esb_dev_intef_url, " +
                "COALESCE(esb_test_result,'') esb_test_result " +
                "FROM sys_core_esb_interf WHERE esb_interf_code='" + materialCheckInterfCode + "' AND enable_flag='Y' LIMIT 1";
        List<Map<String, Object>> configs = cFuncDbSqlExecute.ExecSelectSql("AIS", sql, false, request, apiRoutePath);
        if (configs.isEmpty()) {
            log.error("未配置物料校验接口: {}", materialCheckInterfCode);
            return false;
        }

        Map<String, Object> config = configs.get(0);
        String materialCheckUrl = config.get("esb_prod_intef_url").toString();
        boolean useMaterialTestData = StringUtils.startsWithIgnoreCase(config.get("esb_dev_intef_url").toString(), "Y") &&
                StringUtils.hasText(config.get("esb_test_result").toString());
        JSONObject requestBody = new JSONObject();
        requestBody.put("EqpId", eqpId);
        requestBody.put("QRCode", qrCode);
        requestBody.put("DryFilm", pn);
        JSONObject materialCheckRequest = EapZhcyInterfCommon.CreateHeader(materialCheckInterfCode);
        materialCheckRequest.put("Content", requestBody);
        JSONObject result = useMaterialTestData? JSONObject.parseObject(config.get("esb_test_result").toString()): eapZhcySendInterfFunc.PostJbBackJb(materialCheckUrl, materialCheckRequest,materialCheckInterfCode,request,apiRoutePath);;
        return result != null && result.getBoolean("Success") && result.getJSONObject("Content").getBooleanValue("Result");
    }

    /**
     * 检查是否允许下发配方。
     * 检查D6012（RecipeUpd）是否为1，表示机台是否允许配方修改
     * 生产中：D6012=0，生产结束：D6012=1
     *
     * @param eqpId 设备ID
     * @param plcTags PLC标签配置
     * @return 是否允许下发配方
     * @throws Exception 异常情况
     */
    private boolean isRecipeUpdateAllowed(String eqpId, PlcTagConfig plcTags) throws Exception {
        JSONArray tagArray = cFuncUtilsCellScada.ReadTagByStation(eqpId, plcTags.getRecipeUpdateTag());
        if (tagArray == null || tagArray.isEmpty()) {
            log.error("读取配方更新标志D6012失败: EqpId={}", eqpId);
            return false;
        }
        String recipeUpdValue = tagArray.getJSONObject(0).getString("tag_value");
        boolean allowed = "1".equals(recipeUpdValue);
        if (allowed) {
            log.info("设备允许下发配方，D6012=1: EqpId={}", eqpId);
        } else {
            log.info("设备当前不允许下发配方，D6012={}: EqpId={}", recipeUpdValue, eqpId);
        }
        return allowed;
    }

    /**
     * 检查重新上机标志。
     * 检查D6118（ReLoadMachineFlag）是否为2，表示上机成功
     * 0: 不需要上机，1: 必须重新上机，2: 上机成功
     *
     * @param eqpId 设备ID
     * @param plcTags PLC标签配置
     * @return 是否允许下发配方
     * @throws Exception 异常情况
     */
    private boolean checkReloadMachineFlag(String eqpId, PlcTagConfig plcTags) throws Exception {
        JSONArray tagArray = cFuncUtilsCellScada.ReadTagByStation(eqpId, plcTags.getReloadMachineFlagTag());
        if (tagArray == null || tagArray.isEmpty()) {
            log.error("读取重新上机标志D6118失败: EqpId={}", eqpId);
            return false;
        }
        String reloadValue = tagArray.getJSONObject(0).getString("tag_value");
        boolean allowed = "2".equals(reloadValue);
        if (allowed) {
            log.info("上机标志正常，D6118=2: EqpId={}", eqpId);
        } else {
            log.info("上机标志异常，D6118={}: EqpId={}", reloadValue, eqpId);
        }
        return allowed;
    }

    /**
     * 下发配方数据到PLC。
     * 将配方数据写入到PLC的配方地址
     *
     * @param eqpId 设备ID
     * @param recipeData 配方数据，JSON格式的字符串
     * @param clientCode PLC客户端代码
     * @return 是否成功下发配方数据
     */
    private boolean downloadRecipeToPlc(String eqpId, String recipeData, String clientCode) {
        if (!StringUtils.hasText(recipeData)) {
            log.info("无配方数据需要下发: EqpId={}", eqpId);
            return true;
        }

        log.info("开始下发配方数据到PLC: EqpId={}", eqpId);
        try {
            JSONArray recipeDataArray = JSONObject.parseArray(recipeData);
            log.info("配方数据包含{}\u4e2a项: EqpId={}", recipeDataArray.size(), eqpId);

            for (int i = 0; i < recipeDataArray.size(); i++) {
                JSONObject item = recipeDataArray.getJSONObject(i);
                String itemName = item.getString("ItemName");
                String itemValue = item.getString("ItemValue");
                String recipeItemTag = clientCode + "/PcRecipe/" + itemName;

                String writeResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, recipeItemTag, itemValue, true);
                if (!writeResult.isEmpty()) {
                    log.error("写入配方项失败: ItemName={}, ItemValue={}, 错误={}", itemName, itemValue, writeResult);
                    return false;
                }

                log.debug("已写入配方项: ItemName={}, ItemValue={}", itemName, itemValue);
            }

            log.info("所有配方数据已成功写入PLC: EqpId={}", eqpId);
            return true;
        } catch (Exception ex) {
            log.error("解析或写入配方数据异常: EqpId={}, 错误={}", eqpId, ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * 请求配方下发并等待PLC响应。
     *
     * 正确的配方下发流程：
     * 1. 设置D6104=1（RecipeDownReq）请求下发配方
     * 2. 等待PLC设置D6011=1（RecipeSelFinish）表示配方选择完成
     * 3. 设置D6104=0清除请求标志
     * 4. 设置D6109=1（AllowWork）允许设备生产
     * 5. PLC读取到D6104=0时，置D6011=0
     * 6. PLC读取到D6109=1时，通知上游开始进板生产
     *
     * @param eqpId 设备ID
     * @param plcTags PLC标签配置
     * @return 是否成功下发配方
     * @throws Exception 异常情况
     */
    private boolean requestAndWaitForPlcResponse(String eqpId, PlcTagConfig plcTags) throws Exception {
        // 1. 设置D6104=1（RecipeDownReq）请求下发配方
        String writeResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, plcTags.getRecipeDownRequestTag(), "1", true);
        if (!writeResult.isEmpty()) {
            log.error("设置配方下发请求失败: {}", writeResult);
            return false;
        }
        log.info("已设置配方下发请求D6104=1: EqpId={}", eqpId);

        // 2. 等待PLC设置D6011=1（RecipeSelFinish）表示配方选择完成
        boolean recipeSelFinishDetected = false;
        for (int i = 0; i < MAX_RETRIES; i++) {
            Thread.sleep(RETRY_INTERVAL_MS);
            JSONArray recipeSelFinishArray = cFuncUtilsCellScada.ReadTagByStation(eqpId, plcTags.getRecipeSelFinishTag());
            if (recipeSelFinishArray != null && !recipeSelFinishArray.isEmpty() &&
                    "1".equals(recipeSelFinishArray.getJSONObject(0).getString("tag_value"))) {
                recipeSelFinishDetected = true;
                log.info("检测到配方选择完成标志D6011=1: EqpId={}", eqpId);
                break;
            }
            log.debug("等待配方选择完成标志，尝试次数: {}", i + 1);
        }

        if (!recipeSelFinishDetected) {
            log.error("PLC未设置配方选择完成标志，超时: EqpId={}", eqpId);
            return false;
        }

        // 3. 设置D6104=0清除请求标志
        String clearReqResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, plcTags.getRecipeDownRequestTag(), "0", true);
        if (!clearReqResult.isEmpty()) {
            log.warn("清除配方下发请求标志失败: {}", clearReqResult);
            // 继续执行，不返回错误
        } else {
            log.info("已清除配方下发请求标志D6104=0: EqpId={}", eqpId);
        }

        // 4. 设置D6109=1（AllowWork）允许设备生产
        // 这一步在allowProduction方法中实现，这里不需要重复设置

        return true;
    }

    /**
     * 允许设备开始生产。
     * 设置D6109=1（AllowWork）允许设备生产
     * PLC读取到D6109=1时，通知上游开始进板生产
     */
    private void allowProduction(String eqpId, PlcTagConfig plcTags) {
        // 设置D6109=1（AllowWork）允许设备生产
        String result = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, plcTags.getAllowWorkTag(), "1", true);
        if (!result.isEmpty()) {
            log.warn("设置允许生产标志D6109=1失败: {}", result);
        } else {
            log.info("已设置允许生产标志D6109=1: EqpId={}", eqpId);
        }
    }


    /**
     * 触发物料更换警报（蜂鸣器和警报标志）。
     */
    private void triggerMaterialChangeAlert(String eqpId, PlcTagConfig plcTags) {
        String buzzerResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, plcTags.getBuzzerTag(), "1", true);
        if (!buzzerResult.isEmpty()) {
            log.warn("设置蜂鸣器D6102失败: {}", buzzerResult);
        } else {
            log.info("已设置蜂鸣器D6102=1: EqpId={}", eqpId);
        }

        String alertResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, plcTags.getMaterialChangeAlertTag(), "1", true);
        if (!alertResult.isEmpty()) {
            log.warn("设置物料更换警报D6119失败: {}", alertResult);
        } else {
            log.info("已设置物料更换警报D6119=1: EqpId={}", eqpId);
        }

        String reloadResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, plcTags.getReloadMachineFlagTag(), "1", true);
        if (!reloadResult.isEmpty()) {
            log.warn("设置重新上机标志D6118失败: {}", reloadResult);
        } else {
            log.info("已设置重新上机标志D6118=1: EqpId={}", eqpId);
        }
    }
    
    /**
     * 更新任务状态。
     */
    private void updateTaskStatus(String planId, String status) {
    	updateTaskStatus(planId,status,"");
    }
    
    private void updateTaskStatus(String planId, String status,String msg) {
        Query query = new Query(Criteria.where("plan_id").is(planId));
        Update update = new Update().set("lot_status", status).set("update_time", new Date());
        if("FINISH".equalsIgnoreCase(status)||"CANCEL".equalsIgnoreCase(status)) {
        	update.set("plan_end_time", new Date());
        }else if("WORK".equalsIgnoreCase(status)) {
        	update.set("plan_start_time", new Date());
        }
        if(StringUtils.hasText(msg)) {
        	update.set("msg",msg);
        }
        mongoTemplate.updateFirst(query, update, A_EAP_APS_PLAN);
    }

    /**
     * 创建存在WORK任务的响应。
     */
    private JSONObject createWorkTaskExistsResponse(String requestId) {
        JSONObject response = new JSONObject();
        response.put("Code", "0000");
        response.put("Success", true);
        response.put("Msg", "有正在加工的任务，最新物料校验通过");
        response.put("DateTime", new SimpleDateFormat(DATE_TIME_FORMAT).format(new Date()));
        response.put("Content", new JSONObject());
        response.put("RequestId", requestId);
        return response;
    }

    /**
     * 创建成功结果。
     */
    private JSONObject createSuccessResult(String planId, String pn, String qrCode, String requestId) {
        JSONObject result = new JSONObject();
        result.put("Success", true);
        result.put("Msg", String.format("扫描并上机成功: JobId=%s, Pn=%s, QRCode=%s", planId, pn, qrCode));
        result.put("JobId", planId);
        result.put("Pn", pn);
        result.put("QRCode", qrCode);
        result.put("RequestId", requestId);
        return result;
    }

}