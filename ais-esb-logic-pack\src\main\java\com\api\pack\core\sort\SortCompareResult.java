package com.api.pack.core.sort;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.pack.core.board.BoardCompareResult;
import com.api.pack.core.board.BoardConst;
import com.api.pack.core.board.PCSBoard;
import com.api.pack.core.board.SETBoard;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.Map;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SortCompareResult extends BoardCompareResult
{
    private String status;

    @JsonProperty("ng_code")
    private int ngCode;

    @JsonProperty("ng_msg")
    private String ngMsg;

    @JsonProperty("deposit_position")
    private Integer depositPosition;

    private Map<String, Object> plan;

    private Map<String, Object> sortSwitch;

    private SETBoard setBoard;

    private Collection<PCSBoard> pcsBoards;

    private Long costTime;

    public SortCompareResult(String status, int ngCode, String ngMsg)
    {
        super(status, ngCode, ngMsg, null);
        this.status = status;
        this.ngCode = ngCode;
        this.ngMsg = ngMsg;
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isOK()
    {
        return BoardConst.STATUS_OK.equals(this.status);
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isNG()
    {
        return BoardConst.STATUS_NG.equals(this.status);
    }

    public String toResultString()
    {
        JSONObject result = new JSONObject();
        result.put("array_status", this.status);
        result.put("array_ng_code", this.ngCode);
        result.put("array_ng_msg", this.ngMsg);
        if (this.depositPosition != null)
        {
            result.put("deposit_position", String.valueOf(this.depositPosition));
        }
        else
        {
            result.put("deposit_position", this.isOK() ? "1" : "2");
        }
        if (this.plan != null)
        {
            result.put("lot_num", this.plan.get("lot_num"));
            result.put("plant_code", this.plan.get("plant_code"));
            result.put("cycle_period", String.valueOf(this.plan.get("cycle_period")));
            result.put("finish_ok_count", this.plan.getOrDefault("finish_ok_count", 0));
            result.put("finish_ng_count", this.plan.getOrDefault("finish_ng_count", 0));
        }
        if (this.sortSwitch != null && this.sortSwitch.containsKey(SortConst.READ_DURATION))
        {
            result.put("sort_rules_read_duration", this.sortSwitch.get(SortConst.READ_DURATION));
        }
        if (this.setBoard != null)
        {
            result.put("array_id", this.setBoard.getBoardId());
            result.put("array_barcode", this.setBoard.getBoardBarcode());
            result.put("array_level", this.setBoard.getBoardLevel());
            result.put("array_index", this.setBoard.getBoardIndex());
            result.put("array_bd_count", String.valueOf(this.pcsBoards != null ? this.pcsBoards.size() : 0));
            result.put("board_turn", this.setBoard.getBoardTurn());
            result.put("board_sn", this.setBoard.getBoardSn());
            result.put("board_result", this.setBoard.getBoardResult());
            result.put("xout_act_num", this.setBoard.getXoutActualNumber());
        }
        if (this.costTime != null)
        {
            result.put("cost_time", this.costTime + "ms");
        }
        return result.toJSONString();
    }
}