package com.api.pack.core.op;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 任务逻辑
 * 1.查询当前作业任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@RestController
@Slf4j
@RequestMapping("/pack/core/op")
public class PackCoreOpTaskController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.查询当前作业任务
    @RequestMapping(value = "/PackCoreOpWorkTaskSel", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String PackCoreOpWorkTaskSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pack/core/op/PackCoreOpWorkTaskSel";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable="a_pack_aps_plan";
        try{
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
            MongoCursor<Document> iteratorBigData= mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            List<Map<String, Object>> itemList=new ArrayList<>();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow=new HashMap<>();
                String cyclePeriod = String.valueOf(docItemBigData.get("cycle_period"));
                String modelVersion = String.valueOf(docItemBigData.get("model_version"));
                mapBigDataRow.put("plan_id",docItemBigData.getString("plan_id"));
                mapBigDataRow.put("task_from",docItemBigData.getString("task_from"));
                mapBigDataRow.put("task_type",docItemBigData.getString("task_type"));
                mapBigDataRow.put("lot_num",docItemBigData.getString("lot_num"));
                mapBigDataRow.put("model_type",docItemBigData.getString("model_type"));
                mapBigDataRow.put("model_version", modelVersion);
                mapBigDataRow.put("plan_lot_count",docItemBigData.getInteger("plan_lot_count"));
                mapBigDataRow.put("unit_count",docItemBigData.getInteger("unit_count"));
                mapBigDataRow.put("array_type",docItemBigData.getString("array_type"));
                mapBigDataRow.put("bd_type",docItemBigData.getString("bd_type"));
                mapBigDataRow.put("m_length", new BigDecimal(String.valueOf(docItemBigData.get("m_length"))).doubleValue());
                mapBigDataRow.put("m_width", new BigDecimal(String.valueOf(docItemBigData.get("m_width"))).doubleValue());
                mapBigDataRow.put("m_tickness", new BigDecimal(String.valueOf(docItemBigData.get("m_tickness"))).doubleValue());
                mapBigDataRow.put("m_weight", new BigDecimal(String.valueOf(docItemBigData.get("m_weight"))).doubleValue());
                mapBigDataRow.put("cycle_period", cyclePeriod);
                mapBigDataRow.put("ship_address", docItemBigData.getString("ship_address")); // 出货地 added by jay-y 2024-07-19
                itemList.add(mapBigDataRow);
                iteratorBigData.close();
            }
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex) {
            errorMsg= "查询当前作业任务异常:"+ex.getMessage();
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
