package com.api.eap.core.mongo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbMongoIndex;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.eap.project.zhcd.EapZhCdMongoIndexFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 1.EAP标准MongoDB索引创建
 * 2.根据系统参数读取判断项目索引创建
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */

@Service
@Slf4j
public class EapCoreMongoIndexFunc {
    @Autowired
    private CFuncDbMongoIndex cFuncDbMongoIndex;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapZhCdMongoIndexFunc eapZhCdMongoIndexFunc;

    //创建表索引
    @Async
    public void CreateEapIndex() {
        String Eap_MongoDbIndexList = cFuncDbSqlResolve.GetParameterValue("Eap_MongoDbIndexList");
        if (Eap_MongoDbIndexList != null && !Eap_MongoDbIndexList.equals("")) {
            String[] lst = Eap_MongoDbIndexList.split(",", -1);
            if (lst != null && lst.length > 0) {
                for (String itemName : lst) {
                    if (itemName.equals("EAP")) {
                        CreateEapCoreIndex();
                        eapZhCdMongoIndexFunc.CreateEapZhCdIndex();//珠海崇达定制表索引
                    }
                }
            }
        }
    }

    //标准表索引
    @Async
    public void CreateEapCoreIndex() {
        CreateEapApsPlanIndex();
        CreateEapApsPlanDIndex();
        CreateEapMeStationFlowIndex();
        CreateEapMeStationHmiShowIndex();
        CreateEapMeStationUserIndex();
        CreateEapMeInterfOfflineIndex();
        CreateEapMeStatUtilityIndex();
        CreateEapApsProSpecIndex();
        CreateEapApsDummyPlanIndex();
        CreateEapApsRecipeIndex();
        CreateEapApsAndonIndex();
        CreateEapMePalletQueueIndex();
        CreateEapMePanelQueueIndex();
        CreateEapApsDummyUsageIndex();
        CreateEapMeZjRecordIndex();
        CreateEapUnLoadNgPnlIndex();
        CreateEapUnLoadOkPnlIndex();
        CreateEapMeLaserIndex();
    }

    //创建a_eap_aps_plan动态索引
    @Async
    public void CreateEapApsPlanIndex() {
        String tableName = "a_eap_aps_plan";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "plan_id", "station_id", "task_from", "group_lot_num",
                "lot_num", "lot_short_num", "port_code", "pallet_num", "group_lot_status", "lot_status", "task_error_code", "group_id",
                "material_code", "pdb_rule", "face_code", "inspect_count", "ori_plan_id", "manual_wip_flag"};
        int keep_days = 90;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_aps_plan索引成功");
            else log.warn("创建Mongo表a_eap_aps_plan索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_aps_plan索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_aps_plan_d动态索引
    @Async
    public void CreateEapApsPlanDIndex() {
        String tableName = "a_eap_aps_plan_d";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "plan_d_id", "plan_id", "panel_barcode"};
        int keep_days = 90;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_aps_plan_d索引成功");
            else log.warn("创建Mongo表a_eap_aps_plan_d索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_aps_plan_d索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_me_station_flow动态索引
    @Async
    public void CreateEapMeStationFlowIndex() {
        String tableName = "a_eap_me_station_flow";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "station_flow_id", "station_id", "plan_id", "task_from", "group_lot_num",
                "lot_num", "lot_short_num", "port_code", "pallet_num", "panel_barcode", "panel_status", "panel_ng_code", "inspect_flag", "dummy_flag",
                "manual_judge_code", "panel_flag", "user_name", "tray_barcode", "face_code", "offline_flag", "group_id", "sys_model","virtu_pallet_num"};
        int keep_days = 90;
        try {
            //added by chenru 2024-03-05 根据系统参数设置保存天数重新设置索引保留天数
            String sql = "select COALESCE(parameter_val,'0') parameter_val from sys_parameter where " +
                    "parameter_code='STATION_FLOW_KEEPDAYS' and enable_flag='Y' ";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sql, false, null, "");
            if (itemList != null && itemList.size() > 0) {
                //如果配置了保存时间,并且设置的保存时间不等于原来的索引时间，先删除索引
                String indexName = tableName + "_item_date";
                int keep_days_set = Integer.parseInt(itemList.get(0).get("parameter_val").toString());
                int keep_days_old = 0;
                List<IndexInfo> indexInfoList = mongoTemplate.indexOps(tableName).getIndexInfo();
                for (IndexInfo indexInfo : indexInfoList) {
                    if (indexInfo.getName().equals(indexName)) {
                        if (indexInfo.getExpireAfter().get() != null) {
                            keep_days_old = (int) indexInfo.getExpireAfter().get().getSeconds();
                            if (keep_days_old != keep_days_set * 24 * 3600) {
                                mongoTemplate.indexOps(tableName).dropIndex(indexName);
                                keep_days = keep_days_set;
                            }
                        }
                        break;
                    }
                }
            }
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_me_station_flow索引成功");
            else log.warn("创建Mongo表a_eap_me_station_flow索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_me_station_flow索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_me_station_hmi_show动态索引
    @Async
    public void CreateEapMeStationHmiShowIndex() {
        String tableName = "a_eap_me_station_hmi_show";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "hmi_show_id", "station_id", "finish_flag"};
        int keep_days = 10;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_me_station_hmi_show索引成功");
            else log.warn("创建Mongo表a_eap_me_station_hmi_show索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_me_station_hmi_show索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_me_station_user动态索引
    @Async
    public void CreateEapMeStationUserIndex() {
        String tableName = "a_eap_me_station_user";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "check_user_id", "station_id", "checkout_flag"};
        int keep_days = 30;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_me_station_user索引成功");
            else log.warn("创建Mongo表a_eap_me_station_user索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_me_station_user索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_me_interf_offline动态索引
    @Async
    public void CreateEapMeInterfOfflineIndex() {
        String tableName = "a_eap_me_interf_offline";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "interf_offline_id", "station_id", "function_name", "up_flag"};
        int keep_days = 7;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_me_interf_offline索引成功");
            else log.warn("创建Mongo表a_eap_me_interf_offline索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_me_interf_offline索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_me_stat_utility动态索引
    @Async
    public void CreateEapMeStatUtilityIndex() {
        String tableName = "a_eap_me_stat_utility";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "stat_utility_id", "station_id", "shift_start_date", "shift_end_date", "shift_code", "up_flag"};
        int keep_days = 90;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_me_stat_utility索引成功");
            else log.warn("创建Mongo表a_eap_me_stat_utility索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_me_stat_utility索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_aps_pro_spec动态索引
    @Async
    public void CreateEapApsProSpecIndex() {
        String tableName = "a_eap_aps_plan";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "spec_id", "station_id", "task_from", "spec_seq", "spec_info"};
        int keep_days = 90;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_aps_pro_spec索引成功");
            else log.warn("创建Mongo表a_eap_aps_pro_spec索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_aps_pro_spec索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_aps_dummy_plan动态索引
    @Async
    public void CreateEapApsDummyPlanIndex() {
        String tableName = "a_eap_aps_dummy_plan";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "plan_id", "station_id", "task_from", "group_lot_num",
                "lot_num", "lot_short_num", "port_code", "pallet_num", "group_lot_status", "lot_status", "task_error_code", "group_id",
                "material_code", "pdb_rule", "face_code", "inspect_count"};
        int keep_days = 90;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_aps_plan索引成功");
            else log.warn("创建Mongo表a_eap_aps_plan索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_aps_plan索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_aps_dummy_plan_d动态索引
    @Async
    public void CreateEapApsDummyPlanDIndex() {
        String tableName = "a_eap_aps_dummy_plan_d";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "plan_d_id", "plan_id", "panel_barcode"};
        int keep_days = 90;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_aps_plan_d索引成功");
            else log.warn("创建Mongo表a_eap_aps_plan_d索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_aps_plan_d索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_aps_recipe动态索引
    @Async
    public void CreateEapApsRecipeIndex() {
        String tableName = "a_eap_aps_recipe";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "recipe_id", "station_id", "lot_num", "batch_no", "panel_length", "panel_width",
                "panel_tickness", "ink_type", "material", "min_aperture", "a_r", "jack_panel_tickness", "scraping_speed", "scraping_pressure",
                "vacuum_degree", "pre_lifting_net_height", "lifting_net_height", "plates_distance", "note"};
        int keep_days = 0;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_aps_recipe索引成功");
            else log.warn("创建Mongo表a_eap_aps_recipe索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_aps_recipe索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_andon_event动态索引
    @Async
    public void CreateEapApsAndonIndex() {
        String tableName = "a_eap_andon_event";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "andon_id", "station_id", "andon_type", "happen_date", "reset_date",
                "happen_user", "reset_user", "cost_times"};
        int keep_days = 0;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_andon_event索引成功");
            else log.warn("创建Mongo表a_eap_andon_event索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_andon_event索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_me_pallet_queue动态索引
    @Async
    public void CreateEapMePalletQueueIndex() {
        String tableName = "a_eap_me_pallet_queue";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "station_id", "lot_num", "pallet_num"};
        int keep_days = 7;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_me_pallet_queue索引成功");
            else log.warn("创建Mongo表a_eap_me_pallet_queue索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_me_pallet_queue索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_me_pallet_queue动态索引
    @Async
    public void CreateEapMePanelQueueIndex() {
        String tableName = "a_eap_me_panel_queue";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "station_id", "panel_barcode", "panel_status", "use_flag"};
        int keep_days = 7;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_me_panel_queue索引成功");
            else log.warn("创建Mongo表a_eap_me_panel_queue索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_me_panel_queue索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_operation_log动态索引
    @Async
    public void CreateEapApsOperationIndex() {
        String tableName = "a_eap_operation_log";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "operation_id", "operation_code", "operation_type", "operation_des", "operation_status"};
        int keep_days = 90;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_operation_log索引成功");
            else log.warn("创建Mongo表a_eap_operation_log索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_operation_log索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_aps_dummy_usage动态索引
    @Async
    public void CreateEapApsDummyUsageIndex() {
        String tableName = "a_eap_aps_dummy_usage";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "dummy_id", "dummy_code", "cur_usage_count"};
        int keep_days = 0;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_aps_dummy_usage索引成功");
            else log.warn("创建a_eap_aps_dummy_usage索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_aps_dummy_usage索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_me_zj_record动态索引
    @Async
    public void CreateEapMeZjRecordIndex() {
        String tableName = "a_eap_me_zj_record";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "zj_record_id", "station_id", "pallet_barcode","panel_barcode"};
        int keep_days = 30;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_me_zj_record索引成功");
            else log.warn("创建a_eap_me_zj_record索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_me_zj_record索引失败:" + ex.getMessage());
        }
    }


    //创建a_eap_aps_unload_ng_panel动态索引
    @Async
    public void CreateEapUnLoadNgPnlIndex() {
        String tableName = "a_eap_aps_unload_ng_panel";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "ok_panel_id", "station_code", "lot_num","panel_barcode"};
        int keep_days = 7;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_aps_unload_ng_panel索引成功");
            else log.warn("创建a_eap_aps_unload_ng_panel索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_aps_unload_ng_panel索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_aps_load_ok_panel动态索引
    @Async
    public void CreateEapUnLoadOkPnlIndex() {
        String tableName = "a_eap_aps_load_ok_panel";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "ok_panel_id", "station_code", "lot_num","panel_barcode"};
        int keep_days = 7;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_aps_load_ok_panel索引成功");
            else log.warn("创建a_eap_aps_load_ok_panel索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_aps_load_ok_panel索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_me_laser动态索引
    @Async
    public void CreateEapMeLaserIndex() {
        String tableName = "a_eap_me_laser";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "station_id", "group_lot_num", "lot_num","laser_barcode"};
        int keep_days = 7;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_me_laser索引成功");
            else log.warn("创建a_eap_me_laser索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_me_laser索引失败:" + ex.getMessage());
        }
    }
}
