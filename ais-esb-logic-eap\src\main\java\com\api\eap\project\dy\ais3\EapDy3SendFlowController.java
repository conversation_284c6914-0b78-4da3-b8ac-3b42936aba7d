package com.api.eap.project.dy.ais3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.dy.EapDyInterfCommon;
import com.api.eap.project.dy.EapDySendFlowFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定颖EAP发送流程数据定义接口
 * 1.PortStatusChangeReport:[接口]端口状态发生变化推送到EAP
 * 2.CarrierIDReport:[接口]载具扫描上报验证
 * 3.CarrierStatusReport:[接口]开始/结束/取消/终止投板（收板）时上报
 * 4.CCDDataReport:[接口]CCD读码上报
 * 5.FetchOutReport:[接口]每放一片板需要上报（针对放板机）
 * 6.StoreInReport:[接口]每收一片板需要上报（针对收板机）
 * 7.JobCountReport:[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
 * 8.PanelDataUploadReport:[接口]每片Panel收放台車事件上報
 * 9.WIPTrackingReport:[接口]每一lot完板上报
 * 10.CarrierDataUploadReport:[接口]最后全部任务完板后上报
 * 11.JobDataCreateModifyReport:[接口]人工操作界面拿走一片板子(手工录入)
 * 12.JobRemoveRecoveryReport:[接口]人工操作界面放回板子(手工录入)
 * 13.EachPositionReport:[接口]暂存位置信息动态上报
 * 14.EachPanelDataDownLoad:[上下游接口]当片信息(上游传递给下游)
 * 15.BcOffLineLotDownLoad:[上下游接口]BC离线工单信息下发
 * 16.BcOffLineLoginInDownLoad:[上下游接口]BC离线同步登入
 * 17.LotFinishDownLoad:[上下游接口]传递到下游设备结批信息
 * 18.ProductionModeDownLoad:[上下游接口]首件状态下发
 * 19.HeartBeatCheckDownLoad:[上下游接口]检测下游心跳状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/dy3/interf/send")
public class EapDy3SendFlowController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDySendFlowFunc eapDySendFlowFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;

    //1.[接口]端口状态发生变化推送到EAP
    @RequestMapping(value = "/PortStatusChangeReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDy3PortStatusChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy3/interf/send/PortStatusChangeReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String port_status = jsonParas.getString("port_status");//UDCM、LDRQ、WAIT、PROC、ABOT、CANE、PREN、UDRQ、
            String pallet_num = jsonParas.getString("pallet_num");
            Integer left_count = jsonParas.getInteger("left_count");
            String eqp_job_type = jsonParas.getString("eqp_job_type");//FCL、ECL、ECR、FCU、FCR、ECRFCL、FCUECL
            if (eqp_job_type == null) eqp_job_type = "";
            if (pallet_num == null) pallet_num = "";
            if (left_count == null) left_count = 0;

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String allow_sb_emptypallet_flag = "N";
            String user_name = "";
            String port_ng_flag = "N";
            String pallet_reload_flag = "N";
            if (port_status.equals("LDRQ")) pallet_reload_flag = "Y";
            if (port_status.equals("UDCM") || port_status.equals("LDRQ")) {
                pallet_num = "";
                left_count = 0;
            }

            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //端口状态上报
            eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, port_status, allow_sb_emptypallet_flag, pallet_num, String.valueOf(left_count), user_name, port_ng_flag, pallet_reload_flag,eqp_job_type,station_attr);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "端口状态上报EAP异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
