pack.ccdDataError=CCD\u677F\u4EF6\u6570\u636E\u4E3A\u7A7A
pack.pcsCountError=\u6B63\u53CD\u9762PCS\u677F\u4EF6\u6570\u91CF\u9519\u8BEF
pack.formatError={0}\u62A5\u6587\u683C\u5F0F\u9519\u8BEF
pack.unconformity={0}\u4FE1\u606F\u4E0E{1}\u4FE1\u606F\u4E0D\u4E00\u81F4
pack.unconformityWithValues={0}\u4FE1\u606F{1}\u4E0E{2}\u4FE1\u606F{3}\u4E0D\u4E00\u81F4
pack.noDetected={0}\u672A\u68C0\u6D4B\u5230{1}\u4FE1\u606F
pack.notMatch={0} {1}\u4FE1\u606F\u68C0\u6D4B\u503C{2}\u4E0E\u8BBE\u5B9A\u503C{3}\u4E0D\u5339\u914D
pack.notMatchAll={0} {1}\u4FE1\u606F\u68C0\u6D4B\u4E0E\u8BBE\u5B9A\u4E0D\u5339\u914D
pack.notConfigured=\u672A\u914D\u7F6E\u5BF9\u5E94"{0}"\u7684\u68C0\u6D4B\u4FE1\u606F
pack.compareRules.FrontAndBackSort=\u524D\u540E\u6BD4\u5BF9
pack.compareRules.FrontAndBackSort.notMatch={0} {1}\u4FE1\u606F\u6B63\u9762\u68C0\u6D4B\u503C{2}\u4E0E\u53CD\u9762\u68C0\u6D4B\u503C{3}\u4E0D\u5339\u914D
pack.compareRules.XoutNPSort=X\u6570/\u4F4D
pack.compareRules.XoutSort.notMatch={0}\u6B63\u9762\u753BX\u6570{1}\u4E0E\u53CD\u9762\u753BX\u6570{2}\u4E0D\u4E00\u81F4
pack.compareRules.XoutSort.notMatchForFront={0}\u6B63\u9762\u753BX\u4E0E\u53CD\u9762\u672A\u753BX
pack.compareRules.XoutSort.notMatchForBack={0}\u53CD\u9762\u753BX\u4E0E\u6B63\u9762\u672A\u753BX
pack.compareRules.XoutSort.markIsOk={0}\u753BX\uFF0C\u4F46\u5149\u5B66\u70B9\u68C0\u6D4BOK
pack.compareRules.XoutSort.markIsOK={0}\u753BX\uFF0C\u4F46\u5149\u5B66\u70B9\u68C0\u6D4BOK
pack.compareRules.XoutSort.markIsNG={0}\u672A\u753BX\uFF0C\u4E14\u5149\u5B66\u70B9\u68C0\u6D4BNG
pack.compareRules.XoutSort.notMatchOfPcs={0}\u6B63\u9762PCS\u6570\u91CF{1}\u4E0E\u53CD\u9762PCS\u6570\u91CF{2}\u4E0D\u4E00\u81F4
pack.compareRules.XoutSort.notMatchForActual={0}\u62A5\u6587\u753BX\u6570{1}\u4E0E\u5B9E\u9645\u503C{2}\u4E0D\u4E00\u81F4
pack.compareRules.XoutSort.notMatchForRule={0}\u753BX\u6570"{1}X"\u4E0E\u89C4\u5219\u8BBE\u5B9A\u503C"{2}"\u4E0D\u4E00\u81F4
pack.compareRules.XoutSort.ruleIsInvalid=XOut\u89C4\u5219\u65E0\u6548
pack.compareRules.LevelSort.notMatch={0}\u6B63\u9762\u7B49\u7EA7"{1}"\u4E0E\u53CD\u9762\u7B49\u7EA7"{2}"\u4E0D\u4E00\u81F4
pack.compareRules.LevelSort.greaterThanStand={0}\u4E8C\u7EF4\u7801\u7B49\u7EA7"{1}"\u5927\u4E8E\u8BBE\u5B9A\u7B49\u7EA7"{2}"
pack.compareRules.BoardDirectionSort.notMatch={0}\u6B63\u9762\u677F\u4EF6\u65B9\u5411"{1}"\u4E0E\u53CD\u9762\u677F\u4EF6\u65B9\u5411"{2}"\u4E0D\u4E00\u81F4
pack.compareRules.BarLengthSort.notMatch={0}\u6B63\u9762\u6761\u7801\u957F\u5EA6{1}\u4E0E\u53CD\u9762\u6761\u7801\u957F\u5EA6{2}\u4E0D\u4E00\u81F4
pack.compareRules.BarLengthSort.notMatchForRule={0}\u6761\u7801\u957F\u5EA6{1}\u4E0E\u89C4\u5219\u8BBE\u5B9A\u503C{2}\u4E0D\u4E00\u81F4
pack.compareRules.BarCaseSort.notMatchForRule={0}\u6761\u7801\u5927\u5C0F\u5199{1}\u4E0E\u89C4\u5219\u8BBE\u5B9A\u503C{2}\u4E0D\u4E00\u81F4
pack.compareRules.MultiCodeSort.repeated={0}\u6761\u7801{1}\u91CD\u590D
pack.compareRules.DirMarkSort.statusIsNG={0}\u5149\u5B66\u70B9\u72B6\u6001NG
pack.compareRules.ETPassSort.notMatch={0} {1}\u4FE1\u606F\u68C0\u6D4B\u503C{2}\u4E0E\u7535\u6D4B\u901A\u8FC7\u4FE1\u606F\u8BBE\u5B9A\u503C\u4E0D\u5339\u914D