package com.api.pack.project.avary.op;

import com.alibaba.fastjson.JSONObject;
import com.api.base.Const;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pack.core.pile.PileException;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * Pile堆叠逻辑
 * 1.保存与判断堆叠
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
@RestController
@Slf4j
@RequestMapping("/pack/project/avary/op")
public class PackAvaryOpPileController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.打包前校验PLC传入的SetSNList
    // 校验堆叠逻辑优化 modified by jay-y 2024/09/25
    @RequestMapping(value = "/PackAvaryOpSetSNListCheck", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String PackAvaryOpSetSNListCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pack/core/op/PackAvaryOpSetSNListCheck";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String meArrayTable = "a_pack_me_array";
        String mePileTable = "a_pack_me_pile";
        String apsPlanTable = "a_pack_aps_plan";
        String result = "";//model_type + "," + pile_ng_code + "," + pile_ng_msg + "," + pile_index + "," + pile_status + "," + plan_finish_flag+","+pileId

        String pileId = CFuncUtilsSystem.CreateUUID(true);
        int plcStackQtyValueInt = 0;
        String[] setSnArray = new String[]{};
        List<String> setSnList = new ArrayList<>();
        String sumSunValues = "";

        String user_name = "";
        String pile_barcode = "";
        int pile_index = 0;
        String pile_status = "NG";
        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
        String lot_num = "";
        String model_type = "";
        Integer unit_count = 0;//当前工作中订单单包set数量
        int xout_act_num = 0;//写入当前包内单SET板件最大的XOUT数量
        String batch_no = "";
        Document plan = null;//当前工作中的订单
        int pile_ng_code = 1;//1:ok；2、数据重复；3、长度异常；4、PLC和PC数据校验异常；5、数据量异常；6：其他，
        String pile_ng_msg = "";
        String plan_finish_flag = "0";
        StringBuilder pc_array_list = new StringBuilder();

        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            pile_barcode = jsonParas.getString("pile_barcode");
            sumSunValues = jsonParas.getString("sumSunValues");
            String plcFinishType = jsonParas.getString("plcFinishType");//完板类型，1正常完板，2强制完板，强制完板不需要进行板件数量校验
            String plcStackQtyValue = jsonParas.getString("plcStackQtyValue");//打包数量
            if (plcStackQtyValue != null && !plcStackQtyValue.equals("")) {
                plcStackQtyValueInt = Integer.parseInt(plcStackQtyValue);
            }

            // 获取当前工作订单信息
            Query planQuery = new Query();
            planQuery.addCriteria(Criteria.where("lot_status").is("WORK"));
            MongoCursor<Document> iterator = mongoTemplate.getCollection(apsPlanTable).
                    find(planQuery.getQueryObject()).
                    sort(planQuery.getSortObject()).
                    noCursorTimeout(true).
                    batchSize(10).
                    iterator();
            if (iterator.hasNext()) {
                plan = iterator.next();
                unit_count = plan.getInteger("unit_count");
                model_type = plan.getString("model_type");
                lot_num = plan.getString("lot_num");
                batch_no = plan.getString("batch_no");
                iterator.close();
            }

            setSnArray = sumSunValues.split(",");
            setSnList = Arrays.asList(setSnArray);

            // 判断setSn集合是否为空
            if (setSnArray.length == 0)
            {
                pile_ng_code = 5;
                pile_ng_msg = "传入SetSn集合为空";
                result = model_type + "," + pile_ng_code + "," + pile_ng_msg + "," + pile_index + "," + pile_status + "," +
                        plan_finish_flag + "," + batch_no + "," + xout_act_num + "," + pileId;
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            // 判断setSn数量是否一致
            if (plcFinishType.equals("1")) {
                if (plcStackQtyValueInt != unit_count) {
                    pile_ng_code = 5;
                    pile_ng_msg = "PLC给出堆叠数量{" + plcStackQtyValue + "}与设定SetSn集合不一致";
                    throw new PileException(pile_ng_msg, pile_ng_code);
                }
            }

            // 判断PLC堆叠数量与实际数量是否一致 added by jay-y 2024/08/17
            if (plcStackQtyValueInt != setSnArray.length) {
                pile_ng_code = 5;
                pile_ng_msg = "PLC给出堆叠数量{" + plcStackQtyValue + "}与实际SetSn集合不一致";
                throw new PileException(pile_ng_msg, pile_ng_code);
            }

            // 判断setSn是否在a_pack_me_array，并且状态是OK,并需要判定是否是否绑定pile_barcode，如果在表示已经被打包，不能继续使用，需要先解绑
            String sqlSortCount = "select count(1) from a_pack_fmod_sort " +
                    "where enable_flag='Y' and sort_flag='Y' and sort_code='SetComparison'";
            int sortCount = cFuncDbSqlResolve.GetSelectCount(sqlSortCount);

            List<String> setSnListPC = new ArrayList<>();
            Query setsQuery = new Query();
            setsQuery.addCriteria(Criteria.where("board_sn").in(setSnList));
            setsQuery.addCriteria(Criteria.where("enable_flag").is("Y"));
            setsQuery.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
            iterator = mongoTemplate.getCollection(meArrayTable).
                    find(setsQuery.getQueryObject()).
                    limit(plcStackQtyValueInt).
                    sort(setsQuery.getSortObject()).
                    noCursorTimeout(true).
                    batchSize(10).
                    iterator();
            while (iterator.hasNext()) {
                Document arrayDoc = iterator.next();
                String board_sn = arrayDoc.getString("board_sn");
                String old_pile_barcode = arrayDoc.getString("pile_barcode");
                if (!ObjectUtils.isEmpty(old_pile_barcode)) {
                    pile_ng_code = 2;
                    pile_ng_msg = "PLC给出的堆叠SetSN{" + board_sn + "}已经被包{" + old_pile_barcode + "}绑定，不能重复使用";
                    throw new PileException(pile_ng_msg, pile_ng_code);
                }
                String board_status = arrayDoc.getString("array_status");
                if (!board_status.equals("OK")) {
                    pile_ng_code = 3;
                    pile_ng_msg = "PLC给出的堆叠SetSN{" + board_sn + "}未通过OK校验";
                    throw new PileException(pile_ng_msg, pile_ng_code);
                }
                setSnListPC.add(board_sn);
                if (pc_array_list.length() == 0)
                {
                    pc_array_list = new StringBuilder(board_sn);
                }
                else
                {
                    pc_array_list.append(",").append(board_sn);
                }
            }
            iterator.close();

            Set<String> set = new HashSet<>();
            for (String setSn : setSnArray) {
                if (setSn.equals("@NC@")) continue;
                if (!set.add(setSn)) {
                    pile_ng_code = 2;
                    pile_ng_msg = "PLC给出的堆叠SetSn{" + setSn + "}存在重复项";
                    throw new PileException(pile_ng_msg, pile_ng_code);
                }
                if (setSn.length() != 14) {
                    pile_ng_code = 3;
                    pile_ng_msg = "PLC给出的堆叠SetSn{" + setSn + "}长度不为14位校验失败";
                    throw new PileException(pile_ng_msg, pile_ng_code);
                }
                // 堆叠顺序校验
                if (sortCount > 0) {
                    if (!setSnListPC.contains(setSn)) {
                        pile_ng_code = 4;
                        pile_ng_msg = "PLC反馈SET数据和PC计划SET数据校验异常";
                        throw new PileException(pile_ng_msg, pile_ng_code);
                    }
                }
            }

            // 获取当前用户信息
            Query userQuery = new Query();
            userQuery.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            userQuery.addCriteria(Criteria.where("checkout_flag").is("N"));
            userQuery.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> userData = mongoTemplate.getCollection(meUserTable).
                    find(userQuery.getQueryObject()).
                    sort(userQuery.getSortObject()).
                    noCursorTimeout(true).
                    batchSize(1).
                    iterator();
            if (userData.hasNext()) {
                Document itemDoc = userData.next();
                user_name = itemDoc.getString("user_name");
                userData.close();
            }

            // 获取当前最大pile_index
            Query pileIndexQuery = new Query();
            pileIndexQuery.with(Sort.by(Sort.Direction.DESC, "pile_index"));
            pileIndexQuery.addCriteria(Criteria.where("lot_num").is(lot_num));
            MongoCursor<Document> pileData = mongoTemplate.getCollection(mePileTable).
                    find(pileIndexQuery.getQueryObject()).
                    sort(pileIndexQuery.getSortObject()).
                    noCursorTimeout(true).
                    batchSize(1).
                    iterator();
            if (pileData.hasNext()) {
                Document docItemBigData = pileData.next();
                pile_index = docItemBigData.getInteger("pile_index");
            }
            pileData.close();

            // 更新array上pile_barcode信息，校验OK才做数据绑定 added by jay-y 2024/09/25
            Query validSetsQuery = new Query();
            validSetsQuery.addCriteria(Criteria.where("board_sn").in(Arrays.asList(setSnArray)));
            validSetsQuery.addCriteria(Criteria.where("enable_flag").is("Y"));
            validSetsQuery.addCriteria(Criteria.where("pile_use_flag").is("N"));
            validSetsQuery.addCriteria(Criteria.where("array_status").is("OK"));
            Update validSetsUpdate = new Update();
            validSetsUpdate.set("pile_barcode", pile_barcode);
            validSetsUpdate.set("pile_use_flag", "Y");
            mongoTemplate.updateMulti(validSetsQuery, validSetsUpdate, meArrayTable);
        } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
            errorMsg = "打包前校验PLC传入的SetSNList发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        } finally {
            //新增pile数据
            List<Map<String, Object>> lstPileSets = new ArrayList<>();
            Map<String, Object> mapQDataItem = new HashMap<>();
            mapQDataItem.put("item_date", item_date);
            mapQDataItem.put("item_date_val", item_date_val);
            mapQDataItem.put("pile_id", pileId);
            mapQDataItem.put("pile_barcode", pile_barcode);
            mapQDataItem.put("custom_barcode", "");
            mapQDataItem.put("lot_num", lot_num);
            mapQDataItem.put("model_type", model_type);
            mapQDataItem.put("pile_index", pile_index + 1);
            mapQDataItem.put("array_count", setSnArray.length);
            mapQDataItem.put("pc_array_list", pc_array_list.toString());
            mapQDataItem.put("plc_array_list", sumSunValues);
            mapQDataItem.put("trunk_flag", "N");
            mapQDataItem.put("pile_user", user_name);
            if (pile_ng_code == 1) pile_status = "OK";
            mapQDataItem.put("pile_status", pile_status);
            mapQDataItem.put("pile_ng_code", pile_ng_code);
            mapQDataItem.put("pile_ng_msg", pile_ng_msg);
            mapQDataItem.put("enable_flag", "Y");
            mapQDataItem.put("unbind_flag", "N");
            mapQDataItem.put("unbind_user", "");
            mapQDataItem.put("unbind_time", "");
            mapQDataItem.put("unbind_way", "");
            // 新增申请、打印、校验等返回及联合状态 added by jay-y 2024/08/27
            mapQDataItem.put("apply_return_code", "");
            mapQDataItem.put("apply_return_msg", "");
            mapQDataItem.put("print_return_code", "");
            mapQDataItem.put("print_return_msg", "");
            mapQDataItem.put("inspect_result_code", "");
            mapQDataItem.put("inspect_result_msg", "");
            mapQDataItem.put("union_status", Const.ON);
            lstPileSets.add(mapQDataItem);
            try {
                mongoTemplate.insert(lstPileSets, mePileTable);
                if (transResult.isEmpty())
                {
                    result = model_type + "," + pile_ng_code + "," + pile_ng_msg + "," + pile_index + "," + pile_status + "," +
                            plan_finish_flag + "," + batch_no + "," + xout_act_num + "," + pileId;
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                }
            } catch (Exception ex) {
                errorMsg = "新增pile数据发生异常:" + ex.getMessage();
                log.error(errorMsg);
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
        }
        return transResult;
    }
}
