package com.api.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedBy;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.LastModifiedBy;
import org.springframework.data.annotation.LastModifiedDate;

import java.io.Serializable;
import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public abstract class IMybatisBasic implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("删除标志")
    @TableField("enable_flag")
    protected String enableFlag;

    @ApiModelProperty("创建者")
    @CreatedBy
    @TableField("created_by")
    protected String createdBy;

    @ApiModelProperty("创建时间")
    @CreatedDate
    @TableField("creation_date")
    protected Date creationDate;

    @ApiModelProperty("最后更新者")
    @LastModifiedBy
    @TableField("last_updated_by")
    protected String lastUpdatedBy;

    @ApiModelProperty("最后更新时间")
    @LastModifiedDate
    @TableField("last_update_date")
    protected Date lastUpdateDate;

    @ApiModelProperty("用户")
    @JsonProperty("user_name")
    @JSONField(name = "user_name")
    @TableField(exist = false)
    protected String user;

    public String toJSON()
    {
        return this.toJSON(false);
    }

    public String toJSON(boolean prettyFormat)
    {
        return JSON.toJSONString(this, prettyFormat);
    }

    public JSONObject toJSONObject()
    {
        return JSONObject.parseObject(this.toJSON());
    }
}
