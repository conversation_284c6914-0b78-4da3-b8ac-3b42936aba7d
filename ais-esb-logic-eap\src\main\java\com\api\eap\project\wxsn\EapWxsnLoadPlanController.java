package com.api.eap.project.wxsn;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 放板机生产计划处理逻辑
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/wxsn/load")
public class EapWxsnLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;

    //判断是否存在放板任务
    @RequestMapping(value = "/EapCoreLoadPlanExistJudge", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanExistJudge(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapCoreLoadPlanExistJudge";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在放板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //放板机Panel校验
    @RequestMapping(value = "/EapCoreLoadPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapCoreLoadPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String inspect_flag = jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            Integer code_length = jsonParas.getInteger("code_length");//需要截取的条码长度

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (group_lot_num == null) group_lot_num = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (inspect_flag == null || inspect_flag.equals("")) inspect_flag = "N";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义

            long station_id_long = Long.parseLong(station_id);

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //1.判断是否存在PLAN状态下group_lot_num,若存在则进行更新为WORK状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "WORK");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            //查询该工单下全部的plan_id
            List<String> lstPlanId = new ArrayList<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前计划中或者执行中的子任务
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String port_code = "";
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer fp_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                fp_count = docItemBigData.getInteger("fp_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                iteratorBigData.close();
            }
            plan_lot_count = fp_count > 0 ? fp_count : plan_lot_count;
            //未找到任务报错
            if (plan_id.equals("")) {
                errorMsg = "未找到生产任务,请先导入生产任务";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            //4.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," + panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK";//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                        if (ng_manual_pass_value.equals("1")) {
                            panel_status = "NG_PASS";
                            panel_ng_code = 4;
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        }
                    } else {
                        //先判定板件条码是不是属于当前批号
                        if (!group_lot_num.equals("")) {
                            String str = panel_barcode.substring(0, code_length);
                            if (!str.equals(group_lot_num)) {
                                if (ng_manual_pass_value.equals("1")) {
                                    panel_status = "NG_PASS";
                                    panel_ng_code = 4;
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                } else {
                                    panel_status = "NG";
                                    panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                }
                            } else {
                                panel_ng_code = 10;
                                //再进行板件条码顺序判断，符合顺序给0（plc收到是1），不符合顺序给10（plc收到是11）
                                Query queryBigDataD = new Query();
                                queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                queryBigDataD.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
                                iteratorBigData = mongoTemplate.getCollection("a_eap_aps_plan_d").find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                long count = mongoTemplate.getCollection("a_eap_aps_plan_d").countDocuments(queryBigData.getQueryObject());
                                int i = (int) count;
                                while (iteratorBigData.hasNext()) {
                                    Document docItemBigData = iteratorBigData.next();
                                    String panelBarcode = docItemBigData.getString("panel_barcode");
                                    if (panelBarcode.equals(panel_barcode)) {
                                        if (i == panel_index) {
                                            panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        } else {
                                            panel_ng_code = 10;
                                        }
                                        break;
                                    }
                                    i--;
                                }
                                if (iteratorBigData.hasNext()) iteratorBigData.close();
                                panel_status = "OK";
                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                            }
                        }
                        //判断是否母批下条码重码
                        if (panel_status.equals("OK")) {
                            Query queryBigDataFlow = new Query();
                            queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                            queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                            queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                            queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                            queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                            long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                            if (panelCount > 0) {
                                if (ng_auto_pass_value.equals("1")) {
                                    panel_status = "NG_PASS";
                                    panel_ng_code = 4;
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                } else {
                                    panel_status = "NG";
                                    panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_count >= plan_lot_count) lot_status = "FINISH";
            updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            String UsePlcFinishCountFlag = cFuncDbSqlResolve.GetParameterValue("UsePlcFinishCountFlag");
            if(!UsePlcFinishCountFlag.equals("Y")) {
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            //更新首检数量
            if (inspect_flag.equals("Y")) {//首检必须要全部合格件,否则再次从0开始
                if (panel_status.equals("OK") || panel_status.equals("NG_PASS")) inspect_finish_count++;
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," + panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //放板机Panel校验
    @RequestMapping(value = "/EapCoreLoadPlanPanelCheckAndSave01", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanPanelCheckAndSave01(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapCoreLoadPlanPanelCheckAndSave01";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String inspect_flag = jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            Integer code_length = jsonParas.getInteger("code_length");//需要截取的条码长度
            String face_code = jsonParas.getString("face_code");//板件面次
            String reporting_face = jsonParas.getString("reporting_face");//板件面次

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (inspect_flag == null || inspect_flag.equals("")) inspect_flag = "N";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (face_code == null) face_code = "";
            if (StringUtils.isEmpty(reporting_face)) reporting_face = "";
            Integer face_code_int = Integer.parseInt(face_code);

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //查询该工单下全部的plan_id
            List<String> lstPlanId = new ArrayList<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            Map<String, Object> planData = new HashMap<String, Object>();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                planData.putAll(docItemBigData);
                lstPlanId.add(docItemBigData.getString("plan_id"));
                iteratorBigData.close();
            } else {
                //判断是否得到的计划无,说明没有WORK状态下的任务，需要按时间顺序将第一条任务改成WORK状态,然后再查询
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    planData.putAll(docItemBigData);
                    lstPlanId.add(docItemBigData.getString("plan_id"));
                    iteratorBigData.close();
                }
            }

            //2.获取当前计划中或者执行中的子任务
            String plan_id = "";
            String task_from = "";
            String group_lot_num = planData.containsKey("group_lot_num") ? planData.get("group_lot_num").toString() : "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer fp_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                fp_count = docItemBigData.getInteger("fp_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                iteratorBigData.close();
            }
            plan_lot_count = fp_count > 0 ? fp_count : plan_lot_count;
            //未找到任务报错
            if (plan_id.equals("")) {
                errorMsg = "未找到生产任务,请先导入生产任务";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            Query countQuery = new Query();
            countQuery.addCriteria(Criteria.where("station_id").is(station_id_long));
            countQuery.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            countQuery.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //在保留面次为空时则直接计数，又或者在保留面次与当前面次一直时计数
            if (reporting_face.equals(face_code)) {
                countQuery.addCriteria(Criteria.where("face_code").is(face_code_int));
            }
            Long stationFlowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(countQuery.getQueryObject());
            panel_index = stationFlowCount.intValue() + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code_int);
            mapBigDataRow.put("offline_flag", "N");

            //4.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," + panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK";//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                        if (ng_manual_pass_value.equals("1")) {
                            panel_status = "NG_PASS";
                            panel_ng_code = 4;
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        }
                    } else {
                        //先判定板件条码是不是属于当前批号
                        if (!group_lot_num.equals("")) {
                            String str = panel_barcode.substring(0, code_length);
                            String str2 = group_lot_num.substring(0, code_length);
                            if (!str.equals(str2)) {
                                if (ng_manual_pass_value.equals("1")) {
                                    panel_status = "NG_PASS";
                                    panel_ng_code = 4;
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                } else {
                                    panel_status = "NG";
                                    panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                }
                            } else {
                                panel_ng_code = 10;
                                //再进行板件条码顺序判断，符合顺序给0（plc收到是1），不符合顺序给10（plc收到是11）
                                Query queryBigDataD = new Query();
                                queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                queryBigDataD.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
                                iteratorBigData = mongoTemplate.getCollection("a_eap_aps_plan_d").find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                long count = mongoTemplate.getCollection("a_eap_aps_plan_d").countDocuments(queryBigData.getQueryObject());
                                int i = (int) count;
                                while (iteratorBigData.hasNext()) {
                                    Document docItemBigData = iteratorBigData.next();
                                    String panelBarcode = docItemBigData.getString("panel_barcode");
                                    if (panelBarcode.equals(panel_barcode)) {
                                        if (i == panel_index) {
                                            panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        } else {
                                            panel_ng_code = 10;
                                        }
                                        break;
                                    }
                                    i--;
                                }
                                if (iteratorBigData.hasNext()) iteratorBigData.close();
                                panel_status = "OK";
                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                            }
                        }
                        //判断是否母批下条码重码
                        if (panel_status.equals("OK")) {
                            Query queryBigDataFlow = new Query();
                            queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                            queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                            queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                            queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                            queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                            queryBigDataFlow.addCriteria(Criteria.where("face_code").is(face_code_int));
                            long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                            if (panelCount > 0) {
                                if (ng_auto_pass_value.equals("1")) {
                                    panel_status = "NG_PASS";
                                    panel_ng_code = 4;
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                } else {
                                    panel_status = "NG";
                                    panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            //在保留面次为空时则直接计数，又或者在保留面次与当前面次一直时计数
            if ((StringUtils.isEmpty(reporting_face) || "0".equals(reporting_face)) || reporting_face.equals(face_code)) {
                finish_count++;
                if (panel_status.equals("NG")) finish_ng_count++;
                else finish_ok_count++;
            }
            String lot_status = "WORK";
            if (finish_count >= plan_lot_count) lot_status = "FINISH";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            String UsePlcFinishCountFlag = cFuncDbSqlResolve.GetParameterValue("UsePlcFinishCountFlag");
            if(!UsePlcFinishCountFlag.equals("Y")) {
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            //更新首检数量
            if (inspect_flag.equals("Y")) {//首检必须要全部合格件,否则再次从0开始
                if (panel_status.equals("OK") || panel_status.equals("NG_PASS")) inspect_finish_count++;
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," + panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            planData.put("panel_result", result);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, JSON.toJSONString(planData), "", 0);
        } catch (Exception ex) {
            errorMsg = "异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //移走一片板子
    @RequestMapping(value = "/EapCoreLoadPlanRemoveOne", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanRemoveOne(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapCoreLoadPlanRemoveOne";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String plan_id = "";
            Integer finish_count = -1;
            Integer finish_ng_count = -1;
            String station_flow_id = jsonParas.getString("station_flow_id");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                if (plan_id == null || plan_id.equals("")) plan_id = "0";
                iteratorBigData.close();
            }
            if (!plan_id.equals("")) {
                //先做删除
                mongoTemplate.remove(queryBigData, meStationFlowTable);
                //先查询数据
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    finish_count = docItemBigData.getInteger("finish_count");
                    finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                    finish_count = finish_count - 1;
                    finish_ng_count = finish_ng_count - 1;
                    if (finish_count < 0) finish_count = 0;
                    if (finish_ng_count < 0) finish_ng_count = 0;
                    iteratorBigData.close();
                }
                //再做更新
                if (finish_count >= 0) {
                    Update updateBigData = new Update();
                    updateBigData.set("finish_count", finish_count);
                    updateBigData.set("finish_ng_count", finish_ng_count);
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "移走一片板子发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更新板子合格标志和板件信息
    @RequestMapping(value = "/EapCoreLoadPlanPanelReplace", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanPanelReplace(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapCoreLoadPlanPanelReplace";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String plan_id = "";
            Integer finish_ng_count = -1;
            Integer finish_ok_count = -1;
            Integer inspect_finish_count = -1;
            String station_flow_id = jsonParas.getString("station_flow_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String inspect_flag = jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                if (plan_id == null || plan_id.equals("")) plan_id = "0";
                iteratorBigData.close();
            }
            if (!plan_id.equals("")) {
                //先做标识更新
                Update updateBigData = new Update();
                updateBigData.set("panel_status", "NG_PASS");
                updateBigData.set("panel_ng_code", 4);
                updateBigData.set("panel_ng_msg", planCommonFunc.getPanelNgMsg(4));
                updateBigData.set("manual_judge_code", "1");
                updateBigData.set("panel_barcode", panel_barcode);
                mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);
                //先查询数据
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                    finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                    inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                    finish_ng_count = finish_ng_count - 1;
                    finish_ok_count = finish_ok_count + 1;
                    if (inspect_flag.equals("Y")) inspect_finish_count = inspect_finish_count + 1;
                    if (finish_ng_count < 0) finish_ng_count = 0;
                    if (finish_ok_count < 0) finish_ok_count = 0;
                    if (inspect_finish_count < 0) inspect_finish_count = 0;
                    iteratorBigData.close();
                }
                //再做更新
                if (finish_ng_count >= 0) {
                    updateBigData = new Update();
                    updateBigData.set("finish_ok_count", finish_ok_count);
                    updateBigData.set("finish_ng_count", finish_ng_count);
                    updateBigData.set("inspect_finish_count", inspect_finish_count);
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "更新板子合格标志和板件信息发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更改任务完工状态以及查询完工明细
    @RequestMapping(value = "/EapCoreLoadPlanUpdateFinishStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanUpdateFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapCoreLoadPlanUpdateFinishStatus";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer fp_count2 = docItemBigData.getInteger("fp_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                String material_code = docItemBigData.getString("material_code");
                String other_attribute = docItemBigData.getString("other_attribute");
                String work_mode = docItemBigData.getString("work_mode");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("fp_count", fp_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("other_attribute", other_attribute);
                jbItem2.put("material_code", material_code);
                jbItem2.put("work_mode", work_mode);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //更改完工任务状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("task_error_code", task_error_code_int);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    @RequestMapping(value = "/EapCoreLoadGetTask", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadGetTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapCoreLoadGetTask";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            List<Map<String, Object>> itemList = new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(new String[]{"PLAN", "WORK"}));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            JSONObject result = new JSONObject();
            if (iteratorBigData.hasNext()) {
                result.putAll(iteratorBigData.next());
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, result.toJSONString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //获取当前工位上一个任务
    @RequestMapping(value = "/GetLastLoadPlanByStation", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetLastLoadPlanByStation(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/GetLastLoadPlanByStation";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            List<Map<String, Object>> itemList = new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(new String[]{"PLAN", "WORK", "FINISH"}));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            String group_lot_num = "";
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, group_lot_num, "", 0);
        } catch (Exception ex) {
            errorMsg = "获取当前工位上一个任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更改任务完工数量
    @RequestMapping(value = "/EapCoreLoadPlanUpdateFinishCount", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanUpdateFinishCount(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapCoreLoadPlanUpdateFinishCount";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String finish_count = jsonParas.getString("finish_count");
            //1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();

            Update updateBigData = new Update();
            updateBigData.set("finish_count", Integer.parseInt(finish_count));
            updateBigData.set("finish_ok_count", Integer.parseInt(finish_count));
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //存储Dummy的使用次数
    @RequestMapping(value = "/EapWxsnCommonDummyUsageCountSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnCommonDummyUsageCountSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapWxsnCommonDummyUsageCountSave";
        String transResult = "";
        String errorMsg = "";
        String apsDummyUsageTable = "a_eap_aps_dummy_usage";
        try {
            String result = "NG";
            String dummyMaxUsageCount = cFuncDbSqlResolve.GetParameterValue("DummyMaxUsageCount");
            if (dummyMaxUsageCount.equals("")) dummyMaxUsageCount = "10";
            Integer max_usage_count = Integer.parseInt(dummyMaxUsageCount);
            String dummy_code = jsonParas.getString("dummy_code");
            Integer cur_usage_count = 0;
            String dummy_id = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("dummy_code").is(dummy_code));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsDummyUsageTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                dummy_id = docItemBigData.getString("dummy_id");
                cur_usage_count = docItemBigData.getInteger("cur_usage_count");
                iteratorBigData.close();
            }
            cur_usage_count += 1;
            if (dummy_id.equals("")) {
                Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                dummy_id = CFuncUtilsSystem.CreateUUID(true);
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("dummy_id", dummy_id);
                mapBigDataRow.put("dummy_code", dummy_code);
                mapBigDataRow.put("cur_usage_count", cur_usage_count);
                mongoTemplate.insert(mapBigDataRow, apsDummyUsageTable);
            } else {
                Update updateBigData = new Update();
                updateBigData.set("cur_usage_count", cur_usage_count);
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsDummyUsageTable);
            }
            if (cur_usage_count + 1 <= max_usage_count) {
                result = "OK";
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "保存Dummy使用次数异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //存储治具条码信息
    @RequestMapping(value = "/EapWxsnLoadMeZjPalletSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnLoadMeZjPalletSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapWxsnLoadMeZjPalletSave";
        String transResult = "";
        String errorMsg = "";
        String meZjRecordTable = "a_eap_me_zj_record";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String pallet_barcode = jsonParas.getString("pallet_barcode");
            String zj_record_id = CFuncUtilsSystem.CreateUUID(true);
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("zj_record_id", zj_record_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("pallet_barcode", pallet_barcode);
            mapBigDataRow.put("panel_barcode", "");
            mongoTemplate.insert(mapBigDataRow, meZjRecordTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "存储治具条码信息异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //存储治具条码信息
    @RequestMapping(value = "/EapWxsnLoadMeZjPalletSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnLoadMeZjPalletSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/load/EapWxsnLoadMeZjPalletSelect";
        String transResult = "";
        String errorMsg = "";
        String meZjRecordTable = "a_eap_me_zj_record";
        try {
            String panel_barcode = jsonParas.getString("panel_barcode");
            String zj_record_id = "";
            String pallet_barcode = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("panel_barcode").is(""));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meZjRecordTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                zj_record_id = docItemBigData.getString("zj_record_id");
                pallet_barcode = docItemBigData.getString("pallet_barcode");
                iteratorBigData.close();
            }
            if (!zj_record_id.equals("")) {
                Query query = new Query();
                query.addCriteria(Criteria.where("zj_record_id").is(zj_record_id));
                Update updateBigData = new Update();
                updateBigData.set("panel_barcode", panel_barcode);
                mongoTemplate.updateFirst(query, updateBigData, meZjRecordTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, pallet_barcode, "", 0);
        } catch (Exception ex) {
            errorMsg = "查询治具条码信息异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
