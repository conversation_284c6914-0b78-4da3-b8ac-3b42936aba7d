package com.api.mes.core.recipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 1.判断扫描是否完成
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-25
 */
@RestController
@Slf4j
@RequestMapping("/mes/core/recipe")
public class MesCoreRecipeMaterialController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //判断扫描是否完成
    @Transactional
    @RequestMapping(value = "/MesCoreRecipeJudgeMaterialFinish", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeJudgeMaterialFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeJudgeMaterialFinish";
        String transResult="";
        String errorMsg="";
        String station_code="";
        String proceduce_id="";
        String finish_flag="N";
        try{
            station_code = jsonParas.getString("station_code");
            proceduce_id=jsonParas.getString("proceduce_id");
            String sqlCount="select count(1) from " +
                    "c_mes_me_cr_pdure_bom " +
                    "where proceduce_id="+proceduce_id+" " +
                    "and station_code='"+station_code+"' " +
                    "and (verify_finish_flag='N' or batch_finish_flag='N')";
            Integer count=cFuncDbSqlResolve.GetSelectCount(sqlCount);
            if(count<=0) finish_flag="Y";
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,finish_flag,"",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "查询当前工位{"+station_code+"},工序ID{"+proceduce_id+"},物料是否扫描完成异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
