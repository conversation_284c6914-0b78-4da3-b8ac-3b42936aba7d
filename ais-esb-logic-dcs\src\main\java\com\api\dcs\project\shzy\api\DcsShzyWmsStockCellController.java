package com.api.dcs.project.shzy.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 上海中冶
 * 1.生成库位单格像素基础信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-9
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/shzy/api/stock")
public class DcsShzyWmsStockCellController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //库存单格像素生成
    @RequestMapping(value = "/DcsShzyWmsStockCellCreate", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsShzyWmsStockCellCreate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/shzy/api/stock/DcsShzyWmsStockCellCreate";
        String transResult="";
        String errorMsg="";
        try{
            String ware_house=jsonParas.getString("ware_house");
            String stock_group_code=jsonParas.getString("stock_group_code");
            Integer cell_row_count=jsonParas.getInteger("cell_row_count");
            Integer cell_col_count=jsonParas.getInteger("cell_col_count");
            //1.先做删除
            String sqlDelete="delete from b_dcs_wms_map_stock_cell " +
                    "where ware_house='"+ware_house+"' " +
                    "and stock_group_code='"+stock_group_code+"'";
            cFuncDbSqlExecute.ExecUpdateSql("AIS",sqlDelete,false,request,apiRoutePath);
            //再做增加
            String sqlCount="select count(1) from b_dcs_wms_map_stock_cell";
            int cell_id=cFuncDbSqlResolve.GetSelectCount(sqlCount);
            for(int i=1;i<=cell_row_count;i++){
                for(int j=1;j<=cell_col_count;j++){
                    cell_id++;
                    String cell_code="R"+i+"C"+j;
                    String sqlInsert="insert into b_dcs_wms_map_stock_cell " +
                            "(created_by,creation_date,cell_id,ware_house,stock_group_code," +
                            "cell_code,cell_row,cell_col,enable_flag,lock_flag,lock_task_num,fill_flag,fill_task_num,temp_cell_flag) values " +
                            "('AIS','"+ CFuncUtilsSystem.GetNowDateTime("") +"',"+cell_id+"," +
                            "'"+ware_house+"','"+stock_group_code+"'," +
                            "'"+cell_code+"',"+i+","+j+",'Y','N','','N','','N')";
                    cFuncDbSqlExecute.ExecUpdateSql("AIS",sqlInsert,false,request,apiRoutePath);
                }
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
