package com.api.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
public abstract class IMongoBasic implements Serializable
{
    private static final long serialVersionUID = 1L;

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Id
    @Field("_id")
    protected ObjectId _id;

    @JsonProperty("id")
    @JSONField(name = "id")
    @Transient
    protected String id;

    @JsonProperty("item_date")
    @JSONField(name = "item_date")
    @Field("item_date")
    protected Date itemDate;

    @JsonProperty("item_date_val")
    @JSONField(name = "item_date_val")
    @Field("item_date_val")
    protected Long itemDateVal;

    @JsonProperty("enable_flag")
    @JSONField(name = "enable_flag")
    @Field("enable_flag")
    protected String enableFlag;

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isEnabled()
    {
        return Const.FLAG_Y.equals(this.enableFlag);
    }

    public String getId()
    {
        if (this.id == null && this._id != null)
        {
            this.id = this._id.toHexString();
        }
        return this.id;
    }

    public void setId(String id)
    {
        this.id = id;
        this._id = new ObjectId(id);
    }

    public String toJSON()
    {
        return this.toJSON(false);
    }

    public String toJSON(boolean prettyFormat)
    {
        return JSON.toJSONString(this, prettyFormat);
    }

    public JSONObject toJSONObject()
    {
        return JSONObject.parseObject(this.toJSON());
    }

    public void completeBeforeSave()
    {
        if (ObjectUtils.isEmpty(this.getItemDate()))
        {
            LocalDateTime now = LocalDateTime.now();
            Date itemDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
            Long itemDateVal = Long.parseLong(now.format(DateTimeFormatter.ofPattern(Const.DATE_FORMAT_DEFAULT)));
            this.setItemDate(itemDate);
            this.setItemDateVal(itemDateVal);
        }
        if (ObjectUtils.isEmpty(this.getEnableFlag()))
        {
            this.setEnableFlag(Const.FLAG_Y);
        }
    }
}
