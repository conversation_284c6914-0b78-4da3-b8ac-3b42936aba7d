package com.api.pack.core.board;

import com.api.base.Const;
import com.api.base.IMongoBasicService;
import com.api.pack.core.sort.SortCompareResultEvent;
import com.api.pack.core.sort.SortCompareResult;
import org.springframework.context.event.EventListener;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Date;

@Service
@Transactional(rollbackFor = Exception.class)
public class PCSBoardService extends IMongoBasicService<PCSBoard, PCSBoardRepository>
{
    private final MongoTemplate mongoTemplate;

    public PCSBoardService(PCSBoardRepository repository, MongoTemplate mongoTemplate)
    {
        super(repository);
        this.mongoTemplate = mongoTemplate;
    }

    @Async
    @EventListener
    public void handleSortCompareResultEvent(SortCompareResultEvent event)
    {
        SortCompareResult compareResult = event.getSource();
        if (compareResult == null || compareResult.getPcsBoards() == null || compareResult.getPcsBoards().isEmpty())
        {
            return;
        }
        Collection<PCSBoard> pcsBoards = compareResult.getPcsBoards();
        this.saveAll(pcsBoards);
    }

    public void disableByParentBoardBarcode(String parentBoardBarcode)
    {
        LocalDateTime now = LocalDateTime.now();
        Date itemDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        Long itemDateVal = Long.parseLong(now.format(DateTimeFormatter.ofPattern(Const.DATE_FORMAT_DEFAULT)));
        Query query = new Query();
        query.addCriteria(Criteria.where("array_barcode").is(parentBoardBarcode));
        query.addCriteria(Criteria.where(Const.PROPERTY_ENABLE_FLAG).is(Const.FLAG_Y));
        Update update = new Update();
        update.set(Const.PROPERTY_ITEM_DATE, itemDate);
        update.set(Const.PROPERTY_ITEM_DATE_VAL, itemDateVal);
        update.set(Const.PROPERTY_ENABLE_FLAG, Const.FLAG_N);
        this.mongoTemplate.updateMulti(query, update, PCSBoard.class);
    }
}
