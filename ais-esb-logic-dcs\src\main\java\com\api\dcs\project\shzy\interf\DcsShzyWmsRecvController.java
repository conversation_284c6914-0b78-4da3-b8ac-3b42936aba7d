package com.api.dcs.project.shzy.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <p>
 * WMS接受流程对外接口
 * 1.分拣请求开始任务
 * 2.分拣放置完成报工
 * 3.WMS接受中控出库任务
 * 4.WMS接受中控切割与焊接任务
 * 5.WMS接受中控定期查询库存信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/shzy/interf/wms/recv")
public class DcsShzyWmsRecvController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private DcsShzyWmsRecvFunc dcsShzyWmsRecvFunc;
    @Autowired
    private MongoTemplate mongoTemplate;


    //1.分拣请求开始任务
    @RequestMapping(value = "/DcsShzyWmsRecvFjStartTask", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsShzyWmsRecvFjStartTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/shzy/interf/wms/recv/DcsShzyWmsRecvFjStartTask";
//        //2025-02-26 51 - 69行 为新增，原有逻辑未删除  当下发入库任务的时候卡控有无出库任务 wms调度任务同时只能存在一种任务执行
//        Query query = new Query();
//        query.addCriteria(Criteria.where("task_status").in("WORK","PLAN")); //查询任务状态为WORK的
//        List<Document> documentList = mongoTemplate.find(query, Document.class, "b_dcs_wms_car_task");
//        //判断表里所有的数据 同时存在任务状态为 WORK 或者 PLAN的
//        if (documentList != null && documentList.size() > 0) {
//            String startTime = CFuncUtilsSystem.GetNowDateTime("");
//            long timestamp = System.currentTimeMillis();//获取当前的时间戳
//            JSONObject json = new JSONObject();
//            String message1 = "程控行车当前有出库任务正在执行，请稍后再试";
//            json.put("code","500");
//            json.put("message",message1);
//            json.put("timestamp",timestamp);
//            String endTime = CFuncUtilsSystem.GetNowDateTime("");
//            //记录日志
//            cFuncLogInterf.Insert(startTime, endTime, "RecvFjStartTask", true, "", jsonParas.toString(), json.toJSONString(), false, message1, request);
//            return json.toJSONString(); //返回json格式
//        }

        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsShzyWmsRecvFunc.RecvFjStartTask(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //2.分拣放置完成报工
    @RequestMapping(value = "/DcsShzyWmsRecvFjFinishTask", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsShzyWmsRecvFjFinishTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/shzy/interf/wms/recv/DcsShzyWmsRecvFjFinishTask";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsShzyWmsRecvFunc.RecvFjFinishTask(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //3.WMS接受中控出库任务
    @RequestMapping(value = "/DcsShzyWmsRecvStockOutTask", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsShzyWmsRecvStockOutTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/shzy/interf/wms/recv/DcsShzyWmsRecvStockOutTask";
//        //2025-02-26 查询任务状态为WORK的时候 做出卡控 114 - 128行 为新增逻辑，原有逻辑未删除
//        Query query = new Query();
//        query.addCriteria(Criteria.where("task_status").in("WORK","PLAN"));
//        List<Document> documentList = mongoTemplate.find(query, Document.class, "b_dcs_wms_fj_task");
//        if (documentList != null && documentList.size() > 0) {
//            String startTime = CFuncUtilsSystem.GetNowDateTime("");
//            JSONObject json = new JSONObject();
//            String message1 = "程控行车当前有入库任务正在执行，请稍后再试";
//            json.put("code","500");
//            json.put("message",message1);
//            String endTime = CFuncUtilsSystem.GetNowDateTime("");
//            //记录日志
//            cFuncLogInterf.Insert(startTime, endTime, "RecvStockOutTask", true, "", jsonParas.toString(), json.toJSONString(), false, message1, request);
//            return json.toJSONString();
//        }

        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsShzyWmsRecvFunc.RecvStockOutTask(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //4.WMS接受中控切割与焊接任务
    @RequestMapping(value = "/DcsShzyWmsRecvDailyKitTask", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsShzyWmsRecvDailyKitTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/shzy/interf/wms/recv/DcsShzyWmsRecvDailyKitTask";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsShzyWmsRecvFunc.RecvDailyKitTask(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //5.WMS接受中控定期查询库存信息
    @RequestMapping(value = "/DcsShzyWmsRecvSelStockInfo", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsShzyWmsRecvSelStockInfo(HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/shzy/interf/wms/recv/DcsShzyWmsRecvSelStockInfo";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jsonParas=new JSONObject();
        JSONObject jbResult = dcsShzyWmsRecvFunc.RecvSelStockInfo(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }
}
