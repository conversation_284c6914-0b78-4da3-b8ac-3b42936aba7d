package com.api.eap.project.zhcy;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <p>
 * 志圣-珠海超毅-EAP发送接口控制器
 * EQP -> EAP 接口
 * EQP-EAP-001 连线检查 - EQP_AliveCheck
 * EQP-EAP-002 设备状态上报 - EQP_EquipmentCurrentStatus
 * EQP-EAP-004 设备警报上报 - EQP_EquipmentAlarmReport
 * EQP-EAP-005 设备任务进展信息上报 - EQP_EquipmentJobDataProcessReport
 * EQP-EAP-009 设备制程/量测数据报告 - EQP_ProcessDataReport
 * EQP-EAP-010 扫码枪扫描二维码信息上报 - EQP_QRCodeReadReport
 * EQP-EAP-017 设备请求身份信息 - EQP_IcCodeReport
 * EQP-EAP-025 压膜机扫描物料标签上报 - EQP_MaterialLableReport
 * </p>
 * <AUTHOR>
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/zhcy/interf/send")
public class EapZhcySendInterfController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;

    @Autowired
    private EapZhcySendInterfFunc eapZhcySendInterfFunc;

    @Autowired
    private EapZhcySendBusinessFunc eapZhcySendBusinessFunc;

    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    
    @Autowired
    private MongoTemplate mongoTemplate;
    // PLC客户端类型
    private static final String PLC_CLIENT_TYPE = "Plc";

    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    
    //连线检查
    @RequestMapping(value = "/EQP_AliveCheck", method = {RequestMethod.POST})
    public String EQP_AliveCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/EQP_AliveCheck";
        return processRequest(jsonParas, request, "EQP_AliveCheck",apiRoutePath);
    }

    //设备状态上报
    @RequestMapping(value = "/EQP_EquipmentCurrentStatus", method = {RequestMethod.POST})
    public String EQP_EquipmentCurrentStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/EQP_EquipmentCurrentStatus";
        return processRequest(jsonParas, request, "EQP_EquipmentCurrentStatus",apiRoutePath);
    }
    
    //设备报警上报
    @RequestMapping(value = "/EQP_EquipmentAlarmReport", method = {RequestMethod.POST})
    public String EQP_EquipmentAlarmReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/EQP_EquipmentAlarmReport";
        return processRequest(jsonParas, request, "EQP_EquipmentAlarmReport",apiRoutePath);
    }

    //设备任务进展信息上报
    @RequestMapping(value = "/EQP_EquipmentJobDataProcessReport", method = {RequestMethod.POST})
    public String EQP_EquipmentJobDataProcessReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/EQP_EquipmentJobDataProcessReport";
        return processRequest(jsonParas, request, "EQP_EquipmentJobDataProcessReport",apiRoutePath);
    }

    //设备制程/量测数据报告
    @RequestMapping(value = "/EQP_ProcessDataReport", method = {RequestMethod.POST})
    public String EQP_ProcessDataReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/EQP_ProcessDataReport";
        return processRequest(jsonParas, request, "EQP_ProcessDataReport",apiRoutePath);
    }

    //扫码枪扫描二维码信息上报
    @RequestMapping(value = "/EQP_QRCodeReadReport", method = {RequestMethod.POST})
    public String EQP_QRCodeReadReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/EQP_QRCodeReadReport";
        return processRequest(jsonParas, request, "EQP_QRCodeReadReport",apiRoutePath);
    }

    //设备请求身份信息
    @RequestMapping(value = "/EQP_IcCodeReport", method = {RequestMethod.POST})
    public String EQP_IcCodeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/EQP_IcCodeReport";
        return processRequest(jsonParas, request, "EQP_IcCodeReport",apiRoutePath);
    }

    //扫码上机
    @RequestMapping(value = "/ScanAndLoad", method = {RequestMethod.POST})
    public String ScanAndLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/ScanAndLoad";
        JSONObject response = eapZhcySendBusinessFunc.processScanAndLoad(jsonParas, request, apiRoutePath);
        return EapZhcyInterfCommon.convertToWebFormat(response).toJSONString();
    }

    /**
     * 设备模式改变  模式0:离线; 1:在线/远程;2:在线/本地。  在线模式下，任务生产中，此时切到离线模式，任务状态建议改成取消，CANCLE 
     */
    @RequestMapping(value = "/DeviceModeChange", method = {RequestMethod.POST})
    public String DeviceModeChange(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        //先上报EAP。
    	try {
    		processRequest(jsonParas, request, "EQP_EquipmentControlMode","/eap/project/zhcy/interf/send/EQP_EquipmentControlMode");	
		} catch (Exception e) {
			log.error(e.getMessage(),e);
		}
    	String apiRoutePath = "/eap/project/zhcy/interf/send/DeviceModeChange";
        JSONObject response = eapZhcySendBusinessFunc.deviceModeChange(jsonParas, request, apiRoutePath);
        return EapZhcyInterfCommon.convertToEventFormat(response).toJSONString();
    }
    
    /**
     * 手动下发生产任务（备用/测试使用）
     * 检查设备是否可以下发配方，如果可以则下发配方并更新任务状态
     * @param jsonParas 请求参数，必须包含stationId字段
     * @param request HTTP请求对象
     * @return 处理结果
     */
    @RequestMapping(value = "/ManuallySendPlan", method = {RequestMethod.POST})
    public String ManuallySendPlan(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/ManuallySendPlan";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        try {
            // 验证参数
            String stationId = jsonParas.getString("stationId");
            String planId  =  jsonParas.getString("planId");
            if (stationId == null || stationId.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "stationId参数不能为空");
                return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
            }
            // 查询工位信息
            String sqlStation = "select station_code from sys_fmod_station where station_id=" + stationId;
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("-1", sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "未找到stationId对应的工位信息: " + stationId);
                return errorResult.toJSONString();
            }
            String eqpId = itemListStation.get(0).get("station_code").toString();
            //查找当前配方数据
            Optional<Document>  planTask = eapZhcySendBusinessFunc.findPlanTaskById(eqpId,planId);
            if (planTask == null || !planTask.isPresent()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "未找到任务：" + planId);
                return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
            }
            JSONObject result = eapZhcySendBusinessFunc.checkAndDownloadRecipe(eqpId, request, apiRoutePath,planTask);
            // 记录日志
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            cFuncLogInterf.Insert(startDate, endDate, "checkAndDownloadRecipe", true, "", jsonParas.toString(), result.toString(), result.getBoolean("Success"), result.getString("Msg"), request);
            return result.toJSONString();
        } catch (Exception ex) {
            String errorMsg = "手动下发生产任务异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            JSONObject errorResult = new JSONObject();
            errorResult.put("Success", false);
            errorResult.put("Msg", errorMsg);
            // 记录日志
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            cFuncLogInterf.Insert(startDate, endDate, "checkAndDownloadRecipe", true, "", jsonParas.toString(), errorResult.toString(), false, errorMsg, request);
            return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
        }
    }

    /**
     * 手动结束当前加工任务（模拟测试使用）
     * 将指定任务状态更新为FINISH并上报到EAP
     * @param jsonParas 请求参数，必须包含stationId和jobId字段
     * @param request HTTP请求对象
     * @return 处理结果
     */
    @RequestMapping(value = "/ManuallyEndWorkPlan", method = {RequestMethod.POST})
    public String ManuallyEndWorkPlan(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/ManuallyEndJob";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        try {
            // 验证参数
            String stationId = jsonParas.getString("stationId");
            String jobId = jsonParas.getString("jobId");
            if (stationId == null || stationId.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "stationId参数不能为空");
                return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
            }
            if (jobId == null || jobId.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "jobId参数不能为空");
                return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
            }
            // 查询工位信息
            String eqpId = getEqpIdByStationId(stationId, request, apiRoutePath);
            // 查询工作中的任务信息
            Query jobQuery = new Query(Criteria.where("plan_id").is(jobId)
                    .and("eqp_id").is(eqpId)
                    .and("lot_status").is("WORK")
                    .and("enable_flag").is("Y"));
            
            Document jobDoc = mongoTemplate.findOne(jobQuery, Document.class, "a_eap_aps_plan");
            if (jobDoc == null) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "未找到WORK的任务: " + jobId);
                return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
            }
            // 获取PLC客户端代码
            String clientCode =  getClientCode(eqpId, PLC_CLIENT_TYPE, request, apiRoutePath);
            // 设置任务切换标志 D6117=1
            String changeTaskFlagTag = clientCode + "/PcStatus/ChangeTaskFlag";
            String writeResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, changeTaskFlagTag, "1", true);
            if (!writeResult.isEmpty()) {
                String errorMsg = "设置任务切换标志D6117=1失败: " + writeResult;
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", errorMsg);
                return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
            }
            log.info("已设置任务切换标志D6117=1: EqpId={}, JobId={}", eqpId, jobId);
            // 记录日志
            JSONObject errorResult = new JSONObject();
            errorResult.put("Success", true);
            errorResult.put("Msg", "结束当前加工任务信号已经下发、等待任务结束事件");
            return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
        } catch (Exception ex) {
            String errorMsg = "手动结束任务异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            JSONObject errorResult = new JSONObject();
            errorResult.put("Success", false);
            errorResult.put("Msg", errorMsg);
            // 记录日志
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            cFuncLogInterf.Insert(startDate, endDate, "ManuallyEndJob", true, "",jsonParas.toString(), errorResult.toString(), false, errorMsg, request);
            return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
        }
    }

    /**
     * 工单结束事件： 触发任务切换流：D6014=1后调用任务结束接口。（PlcStatus/LotEnd ，1代表批次结束）
     * @param jsonParas 请求参数，必须包含stationId字段
     * @param request HTTP请求对象
     * @return 处理结果
     */
    @RequestMapping(value = "/LotEndEvent", method = {RequestMethod.POST})
    public String LotEndEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/send/LotEndEvent";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        try {
            // 验证参数
            String stationId = jsonParas.getString("stationId");
            if (stationId == null || stationId.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "stationId参数不能为空");
                return EapZhcyInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            // 查询工位信息
            String sqlStation = "select station_code from sys_fmod_station where station_id=" + stationId;
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("-1", sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.isEmpty()) {
                JSONObject errorResult = new JSONObject();
                errorResult.put("Success", false);
                errorResult.put("Msg", "未找到stationId对应的工位信息: " + stationId);
                return EapZhcyInterfCommon.convertToEventFormat(errorResult).toJSONString();
            }
            String eqpId = itemListStation.get(0).get("station_code").toString();
            // 调用任务切换方法
            JSONObject result = eapZhcySendBusinessFunc.LotEndEvent(eqpId, request, apiRoutePath);
            // 记录日志
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            cFuncLogInterf.Insert(startDate, endDate, "handleTaskChange", true, "", jsonParas.toString(), result.toString(), result.getBoolean("Success"), result.getString("Msg"), request);
            return  EapZhcyInterfCommon.convertToWebFormat(result).toJSONString();
        } catch (Exception ex) {
            String errorMsg = "任务切换异常: " + ex.getMessage();
            log.error(errorMsg, ex);
            JSONObject errorResult = new JSONObject();
            errorResult.put("Success", false);
            errorResult.put("Msg", errorMsg);
            // 记录日志
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            cFuncLogInterf.Insert(startDate, endDate, "handleTaskChange", true, "", jsonParas.toString(), errorResult.toString(), false, errorMsg, request);
            return EapZhcyInterfCommon.convertToWebFormat(errorResult).toJSONString();
        }
    }

    /**
     * 获取设备的PLC客户端代码
     * 根据设备ID查询对应的PLC客户端代码
     *
     * @param eqpId 设备ID
     * @param clientType 客户端类型，例如 "Plc" 或 "Ais"
     * @param request HTTP请求对象，可为null
     * @param apiRoutePath API路径，可为null
     * @return 客户端代码，如果未找到则返回空字符串
     * @throws Exception 当查询失败时抛出异常
     */
    public String getClientCode(String eqpId, String clientType, HttpServletRequest request, String apiRoutePath) throws Exception {
        if (eqpId == null || eqpId.isEmpty()) {
            throw new Exception("设备ID不能为空");
        }
        if (clientType == null || clientType.isEmpty()) {
            // 默认使用PLC客户端
            clientType = "Plc";
        }
        String sqlClientCode = "SELECT client_code from scada_client where station_code='" + eqpId + "' and client_code like '%" + clientType + "' and enable_flag='Y'";
        List<Map<String, Object>> lstsqlClientCode = cFuncDbSqlExecute.ExecSelectSql(eqpId, sqlClientCode, false, request, apiRoutePath);
        if (lstsqlClientCode == null || lstsqlClientCode.isEmpty()) {
            throw new Exception("未找到设备的" + clientType + "客户端配置: " + eqpId);
        }
        return lstsqlClientCode.get(0).get("client_code").toString();
    }

    /**
     * 获取设备代码
     * 根据工位ID查询对应的设备代码
     *
     * @param stationId 工位ID
     * @param request HTTP请求对象，可为null
     * @param apiRoutePath API路径，可为null
     * @return 设备代码，如果未找到则抛出异常
     * @throws Exception 当查询失败时抛出异常
     */
    public String getEqpIdByStationId(String stationId, HttpServletRequest request, String apiRoutePath) throws Exception {
        if (stationId == null || stationId.isEmpty()) {
            throw new Exception("stationId参数不能为空");
        }
        String sqlStation = "select station_code from sys_fmod_station where station_id=" + stationId;
        List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("-1", sqlStation, false, request, apiRoutePath);

        if (itemListStation == null || itemListStation.isEmpty()) {
            throw new Exception("未找到stationId对应的工位信息: " + stationId);
        }

        return itemListStation.get(0).get("station_code").toString();
    }

    /**
     * 创建标准错误响应
     *
     * @param errorMsg 错误信息
     * @return 错误响应JSON字符串
     */
    public String createErrorResponse(String errorMsg) {
        JSONObject errorResult = new JSONObject();
        errorResult.put("Success", false);
        errorResult.put("Msg", errorMsg);
        return errorResult.toJSONString();
    }

    /**
     * 处理所有请求
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param esbInterfCode 接口代码
     * @param apiRoutePath API路径
     * @return 处理结果字符串
     */
    private String processRequest(JSONObject jsonParas, HttpServletRequest request, String esbInterfCode,String apiRoutePath) {
        JSONObject jbResult = eapZhcySendInterfFunc.processFunc(jsonParas, esbInterfCode,request,apiRoutePath);
        String responseParas = jbResult.getString("responseParas");
        if(StringUtils.hasText(responseParas)) {
        	jbResult= JSONObject.parseObject(responseParas);
        }else {
        	jbResult.remove("isSaveFlag");
        }
        return EapZhcyInterfCommon.convertToEventFormat(jbResult).toJSONString();
    }
}