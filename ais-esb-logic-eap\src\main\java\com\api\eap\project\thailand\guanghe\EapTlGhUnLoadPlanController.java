package com.api.eap.project.thailand.guanghe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.api.eap.project.xinai.EapXinaiLdiSendController;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 泰国广合放板机定制生产计划处理逻辑
 * 1.收板机Panel校验
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/guanghe/unload")
public class EapTlGhUnLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.收板机Panel校验
    //1.1到达载具容量退载具
    //1.2到达载具限高退载具
    //1.3下工站代码变化退载具
    //1.4下工站配方变化退载具
    //1.5一车一批退载具
    //1.6特殊板件退载具
    //1.7不同炉批号切换载具
    //1.8根据分堆号切换载具
    @RequestMapping(value = "/EapTlGhUnLoadPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhUnLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/unload/EapTlGhUnLoadPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String meUpRecordTable = "a_eap_me_up_record";
        String meDragRecordTable = "a_eap_me_drag_record";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            Integer up_ng_code = jsonParas.getInteger("up_ng_code");//上游OK/NG标识(0:OK,1:NG,2:陪镀板)
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            String isStackStation = jsonParas.getString("isStackStation");//判断是否是堆叠工位
            if (isStackStation == null) isStackStation = "N";
            //当前主生产端口信息
            String port_group_id = jsonParas.getString("port_group_id");
            String plan_id = jsonParas.getString("plan_id");
            Integer port_index = jsonParas.getInteger("port_index");
            Integer port_volume = jsonParas.getInteger("port_volume");
            String port_remaining_limit_str = jsonParas.getString("port_remaining_limit");
            if (port_remaining_limit_str == null || port_remaining_limit_str.equals("") || port_remaining_limit_str.equals("0"))
                port_remaining_limit_str = "1000";
            Double port_remaining_limit = Double.parseDouble(port_remaining_limit_str);//剩余限高
            Integer port_count = jsonParas.getInteger("port_count");
            String port_pallet_num = jsonParas.getString("port_pallet_num");
            String port_pallet_type = jsonParas.getString("port_pallet_type");
            Integer next_port_index = jsonParas.getInteger("next_port_index");
            String port_nextstation = jsonParas.getString("port_nextstation");//批次的下工站代码
            String port_nextstationrecipe = jsonParas.getString("port_nextstationrecipe");//批次的下工站配方
            String port_furnacebatch = jsonParas.getString("port_furnacebatch");//端口炉号

            String next_port_pallet_type = jsonParas.getString("next_port_pallet_type");//下一端口载具类型
            if (next_port_pallet_type == null) next_port_pallet_type = "NoPallet";
            String pallet_type_check = jsonParas.getString("pallet_type_check");//是否需要判断载具类型
            if (pallet_type_check == null) pallet_type_check = "N";
            //当前上游NG端口信息
            Integer ng_port_index = jsonParas.getInteger("ng_port_index");//-1代表无专门的上游NG收板端口,0代表载具未载入
            Integer ng_port_volume = jsonParas.getInteger("ng_port_volume");
            Double ng_port_remaining_limit = jsonParas.getDouble("ng_port_remaining_limit");//剩余限高
            if (ng_port_remaining_limit == null)
                ng_port_remaining_limit = 1000.0;
            Integer ng_port_lotcount = jsonParas.getInteger("ng_port_lotcount");
            String ng_port_planid_list = jsonParas.getString("ng_port_planid_list");
            Integer ng_port_count = jsonParas.getInteger("ng_port_count");
            String ng_port_pallet_num = jsonParas.getString("ng_port_pallet_num");
            String ng_port_pallet_type = jsonParas.getString("ng_port_pallet_type");
            String loadUnLoadModel = jsonParas.getString("loadUnLoadModel");//侧出机状态：侧出机状态:放板模式与收板模式切换(0初始状态，1放板模式，2收板模式，3直通模式)
            String CheckDownEquipStatusFlag = cFuncDbSqlResolve.GetParameterValue("CheckDownEquipStatusFlag");//校验下游设备状态标记
            String PnlCheckLengthStr = cFuncDbSqlResolve.GetParameterValue("PnlCheckLength");//pnl条码校验截取长度(-1标识不用截取，大于0标识需截取长度)
            if (PnlCheckLengthStr == null || PnlCheckLengthStr.equals(""))
                PnlCheckLengthStr = "-1";
            int PnlCheckLengthInt = Integer.parseInt(PnlCheckLengthStr);

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String group_id = "";
            String lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:上游NG,10其他异常
            Integer plc_ng_code = 0;//1-10代表OK,其他为NG，11陪镀板，
            Integer panel_index = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer finish_up_ng_count = 0;
            Double panel_tickness = 10.0;
            String panel_flag = "0";
            String lot_first_flag = "N";
            String lot_finish_flag = "N";
            String port_back_flag = "N";//生产端口是否退载具
            String next_port_flag = "N";//是否跳转到下一生产端口
            String next_port_back_flag = "N";//生产端口是否退载具
            String use_ng_port_flag = "N";//是否使用上游NG端口
            String ng_port_back_flag = "N";//NG工位是否退载具
            String plan_port_code = "";//计划选择端口号
            String plan_pallet_num = "";//计划载具条码
            String plan_pallet_type = "";//计划载具类型
            String flow_port_code = "";//过站信息端口号
            String flow_pallet_num = "";//过站载具条码
            String flow_pallet_type = "";//过站载具类型
            String nextstation = "";//批次的下工站代码
            String nextstationrecipe = "";//批次的下工站配方
            String port_sign = "";
            if (sys_model == 0) offline_flag = "Y";
            if (plan_id.equals("")) lot_first_flag = "Y";
            String first_flag = "N";
            String LastSubLotFlag = "N";
            //配方信息
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件,3：不停机首件（内外层）
            Integer inspect_count = 0;//首件数量
            Integer inspect_finish_count = 0;//首件完成数量
            String upmode = "";//收板模式，1：按批次收/2，3：按载具容量收（批次完整性为前提）/4：数量收/5：一换一载具
            String furnacebatch = "";//批次的炉号
            JSONArray OutPnl = new JSONArray();//外层码
            String SideExitBin = "";//侧出下机位区
            String SideExitMode = "";//侧出模式 0：不侧出，1：侧出
            String nextstationvehicaltype = "";//下工站载具类型,校验系统下发的与设备实际在用的载具是否一致
            String layerlot = "";//外层批次号
            //1.判断是否为Dummy板
            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                queryBigDataDummy.addCriteria(Criteria.where("dummy_flag").is("Y"));
                long dummyCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //2.判断是否是托钢板、陪渡板（mes下发记录）
            if (panel_ng_code == 0 && panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                long dummyCount = mongoTemplate.getCollection(meDragRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }

            //判断是否是首件板
//            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
//                Query queryBigDataInspect = new Query();
//                queryBigDataInspect.addCriteria(Criteria.where("station_id").is(station_id));
//                queryBigDataInspect.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
//                queryBigDataInspect.addCriteria(Criteria.where("inspect_flag").is("Y"));
//                long inspectCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataInspect.getQueryObject());
//                if (inspectCount > 0) {
//                    inspect_flag = "Y";
//                }
//            }
            //根据plc给出的标识判定是否是陪镀板
            if (up_ng_code == 2) {
                dummy_flag = "Y";
                panel_ng_code = 11;
            }

            String station_offline_flag = "N";//最后一个工位标识
            String sqlStation = "select COALESCE(offline_flag,'N') offline_flag from sys_fmod_station " +
                    "where station_id=" + station_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                    false, null, "");
            if (itemListStation != null && itemListStation.size() > 0) {
                station_offline_flag = itemListStation.get(0).get("offline_flag").toString();
            }

            //2.判断板件状态
            if (sys_model > 0 && dummy_flag.equals("N")) {
                if (lot_first_flag.equals("Y")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        if (pallet_type_check.equals("Y")) {
                            String pallet_type = mapLotInfo.get("pallet_type") == null ? "" : mapLotInfo.get("pallet_type").toString();
                            if (pallet_type.equals(port_pallet_type)) {
                                iteratorBigData.close();
                            } else if (pallet_type.equals(next_port_pallet_type)) {
                                if (next_port_index > 0) next_port_flag = "Y";
                                else panel_ng_code = 10;//无端口收板
                                iteratorBigData.close();
                            }
                        } else
                            iteratorBigData.close();
                    }
                } else {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                }
                if (mapLotInfo == null) {
                    panel_ng_code = 8;
                    if (up_ng_code == 1) {
                        panel_ng_code = 9;
                        if (ng_port_index > 0) {
                            use_ng_port_flag = "Y";
                            ng_port_count++;
                            if (ng_port_count >= ng_port_volume) {
                                ng_port_back_flag = "Y";
                            }
                            ng_port_remaining_limit = ng_port_remaining_limit - panel_tickness;
                            if (ng_port_remaining_limit < panel_tickness) {
                                ng_port_back_flag = "Y";
                            }
                        }
                    }
                } else {
                    group_id = mapLotInfo.get("group_id").toString();
                    plan_id = mapLotInfo.get("plan_id").toString();
                    lot_status = mapLotInfo.get("lot_status").toString();
                    target_lot_count = Integer.parseInt(mapLotInfo.get("target_lot_count").toString());
                    panel_tickness = Double.parseDouble(mapLotInfo.get("panel_tickness").toString());
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    String attribute3 = mapLotInfo.get("attribute3").toString();//上游NG板件数量
                    LastSubLotFlag = mapLotInfo.get("attribute2").toString();//放板机母批最后子批标识
                    if (attribute3 == null || attribute3.equals("")) attribute3 = "0";
                    finish_up_ng_count = Integer.parseInt(attribute3);
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    String item_info = mapLotInfo.get("item_info").toString();
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jsonObject = JSONObject.parseObject(item_info);
                        firstmode = jsonObject.getString("firstmode") == null ? "" : jsonObject.getString("firstmode");
                        upmode = jsonObject.getString("upmode") == null ? "" : jsonObject.getString("upmode");
                        nextstation = jsonObject.get("nextstation") == null ? "" : jsonObject.get("nextstation").toString();
                        nextstationrecipe = jsonObject.get("nextstationrecipe") == null ? "" : jsonObject.get("nextstationrecipe").toString();
                        String OutPnl_group = jsonObject.getString("OutPnl");
                        if (OutPnl_group != null && !OutPnl_group.equals("")) {
                            OutPnl = JSONArray.parseArray(OutPnl_group);
                        }
                        SideExitBin = jsonObject.getString("SideExitBin");//侧出下机位区
                        SideExitMode = jsonObject.getString("SideExitMode");//侧出模式
                        if (SideExitMode == null) SideExitMode = "1";
                        if (SideExitBin == null) SideExitBin = station_code;
                        furnacebatch = jsonObject.getString("heatno");
                        nextstationvehicaltype = jsonObject.getString("nextstationvehicaltype");
                        if (nextstationvehicaltype == null) nextstationvehicaltype = "";
                        String heatnumberListStr = jsonObject.getString("heatnumberList");
                        if (heatnumberListStr != null) {
                            JSONArray heatnumberList = JSONArray.parseArray(heatnumberListStr);
                            if (heatnumberList != null && heatnumberList.size() > 0) {
                                JSONObject jbItem = heatnumberList.getJSONObject(0);
                                layerlot = jbItem.getString("layerlot");
                            }
                        }
                    }
                    if (up_ng_code == 0) {
                        if (port_index <= 0) panel_ng_code = 10;//无端口收板
                        else {
                            if (lot_first_flag.equals("Y")) {
                                //一换一载具  其他什么条件都不再判断
                                if (upmode.equals("5")) {
                                    if (port_group_id != null && !port_group_id.equals("") && group_id != null && !group_id.equals("") && !group_id.equals(port_group_id)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                } else {
                                    //1.判断数量
                                    if ((port_count + target_lot_count) > port_volume) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //2.判断限高
                                    if (target_lot_count * panel_tickness > port_remaining_limit) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //3.判断批次的下工站代码
                                    if (port_nextstation != null && !port_nextstation.equals("") && nextstation != null && !nextstation.equals("") && !nextstation.equals(port_nextstation)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //4.判断批次的下工站配方
                                    if (port_nextstationrecipe != null && !port_nextstationrecipe.equals("") && nextstationrecipe != null && !nextstationrecipe.equals("") && !nextstationrecipe.equals(port_nextstationrecipe)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //5.判断批次的炉批次
                                    if (port_furnacebatch != null && !port_furnacebatch.equals("") && furnacebatch != null && !furnacebatch.equals("") && !furnacebatch.equals(port_furnacebatch)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //6.判断下工站载具类型
                                    if (!isStackStation.equals("Y")) {
                                        if (SideExitMode.equals("1") && SideExitBin.equals(station_code)
                                                && !nextstationvehicaltype.equals(port_pallet_type)) {
                                            if (port_count > 0) port_back_flag = "Y";
                                            if (next_port_index > 0 && nextstationvehicaltype.equals(next_port_pallet_type))
                                                next_port_flag = "Y";
                                            else panel_ng_code = 10;//无端口收板
                                        }
                                    }
                                }
                            }
                            //2：下料首件，首件做完退载具
                            if (firstmode.equals("2")) {
                                inspect_flag = "Y";
                                panel_ng_code = 13; //首件模式，收板全部放在NG工位
                                target_lot_count = inspect_count;//任务数量=首件数量
                            }
                            if (panel_ng_code == 0) {
                                if (panel_model == 1) {//有Panel读码模式
                                    if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                        if (panel_barcode.equals("")) panel_ng_code = 7;
                                        else panel_ng_code = 2;
                                    } else {
                                        long okCount = 0;
                                        if (OutPnl != null && OutPnl.size() > 0) {
                                            if (OutPnl.contains(panel_barcode)) {
                                                okCount = 1l;
                                            }
                                        } else {
                                            String panel_barcode_check = panel_barcode;
                                            if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                                panel_barcode_check.substring(0, PnlCheckLengthInt);
                                            }
                                            Query queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                                    sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                            if (iteratorBigDataD.hasNext()) {
                                                okCount = 1l;
                                                Document docItem = iteratorBigDataD.next();
                                                panel_flag = docItem.getString("panel_attr");
                                                iteratorBigDataD.close();
                                            }
                                        }
                                        if (okCount <= 0) panel_ng_code = 12;
                                        else {
                                            String panel_barcode_check = panel_barcode;
                                            if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                                panel_barcode_check.substring(0, PnlCheckLengthInt);
                                            }
                                            Query queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (flowCount > 0) {
                                                panel_ng_code = 3;//重码
                                            }
                                        }
                                        if (panel_flag.equals("1")) {
                                            inspect_flag = "Y";
                                            if (station_offline_flag.equals("Y")) {
                                                panel_ng_code = 13; //首件也需要放到NG工位
                                            }
                                        }
                                    }
                                }
                                if (isStackStation.equals("Y")) {
                                    if (panel_tickness > port_remaining_limit) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                }
                            }
                        }
                    } else {
                        if (up_ng_code == 1) {
                            finish_up_ng_count += 1;
                        }
                        panel_ng_code = 9;
                        if (ng_port_index > 0) {
                            use_ng_port_flag = "Y";
                            ng_port_count++;
                            if (ng_port_count >= ng_port_volume) {
                                ng_port_back_flag = "Y";
                            }

                            ng_port_remaining_limit = ng_port_remaining_limit - panel_tickness;
                            if (ng_port_remaining_limit < panel_tickness) {
                                ng_port_back_flag = "Y";
                            }
                        }
                    }

                    if (panel_ng_code == 0 && (firstmode.equals("1") || firstmode.equals("3")) && inspect_finish_count < inspect_count) {
                        inspect_flag = "Y";
                        inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                    }
                    //重码放行但不计数
                    if (panel_ng_code != 3)
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                    panel_index = finish_count;
                    //读码NG的直接放行，算到收板OK
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                    } else if (panel_ng_code != 3) {
                        finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                    }
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        if (next_port_flag.equals("N")) {
                            port_count++;
                            port_remaining_limit = port_remaining_limit - panel_tickness;
                        }
                    }
                    //判断当前作业端口是否退载具
                    if (finish_count >= target_lot_count) {
                        lot_finish_flag = "Y";
                        //上游NG时退载具
                        if (finish_up_ng_count > 0) {
                            if (port_index > 0) port_back_flag = "Y";
                        } else {
                            if (port_index > 0) {
                                //判断是否是堆叠工位，如果是堆叠工位，需要判断当前任务是否分了多个载具（根据限高判断），如果超过一个载具，直接退载具
                                if (isStackStation.equals("Y")) {
                                    String LimitHeight = cFuncDbSqlResolve.GetParameterValue("LimitHeight");
                                    if (LimitHeight.equals("")) LimitHeight = "20";
                                    Double LimitHeight_Double = Double.parseDouble(LimitHeight);//工位限高
                                    if (LimitHeight_Double <= panel_tickness * target_lot_count) {
                                        if (next_port_flag.equals("Y")) {
                                            next_port_back_flag = "Y";
                                        } else {
                                            port_back_flag = "Y";
                                        }
                                    }
                                }
                                //一载具换一载具，母批结束时候退载具，根据任务attribute2=Y判断是最后一个子批
                                if (upmode.equals("5")) {
                                    if (LastSubLotFlag != null && LastSubLotFlag.equals("Y")) {
                                        port_back_flag = "Y";
                                    }
                                } else {
                                    //一车一批
                                    if (upmode.equals("1")) {
                                        port_back_flag = "Y";
                                    } else {
                                        if (port_count >= port_volume) port_back_flag = "Y";
                                        else if (port_remaining_limit < panel_tickness) port_back_flag = "Y";
                                        else {
                                            //读取下一个任务
                                            Integer next_count = 0;
                                            Double next_panel_tickness = 10.0;
                                            queryBigData = new Query();
                                            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                                            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                                            queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                                            queryBigData.addCriteria(Criteria.where("plan_id").ne(plan_id));
                                            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                                            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                                                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                            if (iteratorBigData.hasNext()) {
                                                Document document = iteratorBigData.next();
                                                next_count = document.getInteger("target_lot_count");
                                                next_panel_tickness = Double.parseDouble(document.get("panel_tickness").toString());
                                                iteratorBigData.close();
                                            }
                                            if (port_count + next_count > port_volume) port_back_flag = "Y";
                                            if (next_count * next_panel_tickness > port_remaining_limit)
                                                port_back_flag = "Y";
                                        }
                                    }
                                }
                            }
                        }
                        //判断上游NG工位批次数量是否达到最大值
                        if (ng_port_index > 0 && ng_port_lotcount > 0) {
                            if (panel_ng_code == 9) {
                                if (ng_port_planid_list.equals("")) ng_port_planid_list = plan_id;
                                else {
                                    String[] tempList = ng_port_planid_list.split(",", -1);
                                    if (!Arrays.asList(tempList).contains(plan_id))
                                        ng_port_planid_list += "," + plan_id;
                                }
                            }
                            if (!ng_port_planid_list.equals("")) {
                                String[] tempList = ng_port_planid_list.split(",", -1);
                                if (tempList != null && tempList.length >= ng_port_lotcount) {
                                    ng_port_back_flag = "Y";
                                }
                            }
                        }
                    }
                    //判断是否存在特殊板件
                    if (panel_flag.equals("6")) {
                        if (port_index > 0) port_back_flag = "Y";
                    }
                }
            }

            //3.锁定选择端口以及返回给PLC代码
            if (dummy_flag.equals("Y")) {
                plc_ng_code = panel_ng_code;
            } else {
                if (sys_model > 0) {
                    plc_ng_code = panel_ng_code + 10;
                    plan_pallet_num = port_pallet_num;
                    plan_pallet_type = port_pallet_type;
                    flow_pallet_num = port_pallet_num;
                    flow_pallet_type = port_pallet_type;
                    //重码放行但不计数
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 3 || panel_ng_code == 7) {
                        if (next_port_flag.equals("Y")) {
                            plc_ng_code = next_port_index;
                            plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, next_port_index, plan_port_code, port_sign);
                            flow_port_code = plan_port_code;
                        } else {
                            if (port_index > 0) {
                                plc_ng_code = port_index;
                            }
                        }
                    }
                    if (mapLotInfo != null && next_port_flag.equals("N") && port_index > 0) {
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, port_index, plan_port_code, port_sign);
                        flow_port_code = plan_port_code;
                    }
                    if (use_ng_port_flag.equals("Y")) {
                        plc_ng_code = ng_port_index;
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, ng_port_index, flow_port_code, port_sign);
                        flow_pallet_num = ng_port_pallet_num;
                        flow_pallet_type = ng_port_pallet_type;
                    }
                } else {
                    plc_ng_code = 1;
                }
            }

            //判断是否直通，直通直接给plc反馈10；1.根据侧进侧出模式判断
            String straight_flag = "N";//直通标识
            if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 3 || panel_ng_code == 7) {
                if (CheckDownEquipStatusFlag.equals("Y")) {
                    if (SideExitMode.equals("0") && !loadUnLoadModel.equals("1") && station_offline_flag.equals("N")) {
                        //直通模式&下游非放板模式&非线尾工位  视为直通
                        port_index = 10;
                        plc_ng_code = 10;
                        straight_flag = "Y";
                    } else if (SideExitMode.equals("1")) {
                        if (!SideExitBin.equals(station_code) && !loadUnLoadModel.equals("1")) {
                            //侧出模式&当前工位不是侧出工位  视为直通
                            port_index = 10;
                            plc_ng_code = 10;
                            straight_flag = "Y";
                        }
                    }
                } else {
                    if (SideExitMode.equals("0") && station_offline_flag.equals("N")) {
                        //直通模式&下游非放板模式&非线尾工位  视为直通
                        port_index = 10;
                        plc_ng_code = 10;
                        straight_flag = "Y";
                    } else if (SideExitMode.equals("1")) {
                        if (!SideExitBin.equals(station_code)) {
                            //侧出模式&当前工位不是侧出工位  视为直通
                            port_index = 10;
                            plc_ng_code = 10;
                            straight_flag = "Y";
                        }
                    }
                }
            }

            //判断是否直通，直通直接给plc反馈10；2.根据首件模式判断
            if (firstmode.equals("3") && inspect_flag.equals("Y")) {
                //firstmode=3&inspect_flag=Y  视为直通
                port_index = 10;
                plc_ng_code = 10;
                straight_flag = "Y";
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : (panel_ng_code == 4 || panel_ng_code == 2 || panel_ng_code == 7) ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_id").toString());
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);
            mapBigDataRow.put("straight_flag", straight_flag);

            JSONObject jbResult = new JSONObject();
            String first_bind_flag = "N";
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                //更改
                Update updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                updateBigData.set("attribute3", finish_up_ng_count.toString());
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                String port_code = mapLotInfo.get("port_code").toString();
                if (port_code.equals("") && !plan_port_code.equals("")) {
                    first_flag = "Y";
                    first_bind_flag = "Y";
                    updateBigData.set("port_code", plan_port_code);
                    updateBigData.set("pallet_num", plan_pallet_num);
                    updateBigData.set("pallet_type", plan_pallet_type);
                    updateBigData.set("lot_status", "WORK");
                    updateBigData.set("group_lot_status", "WORK");
                }
                if (lot_finish_flag.equals("Y")) {
                    String task_start_time = mapLotInfo.get("task_start_time").toString();
                    if (task_start_time.equals("")) task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    long task_cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                    Integer task_error_code = 0;
                    if (target_lot_count == finish_ok_count) task_error_code = 1;
                    else if (target_lot_count > finish_ok_count) task_error_code = 2;
                    else if (target_lot_count < finish_ok_count) task_error_code = 3;
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("group_lot_status", "FINISH");
                    updateBigData.set("task_end_time", task_end_time);
                    updateBigData.set("task_cost_time", task_cost_time);
                    updateBigData.set("task_error_code", task_error_code);
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //组合返回数据
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            jbResult.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("plc_ng_code", plc_ng_code);
            jbResult.put("first_bind_flag", first_bind_flag);
            jbResult.put("lot_finish_flag", lot_finish_flag);
            jbResult.put("port_back_flag", port_back_flag);
            jbResult.put("next_port_flag", next_port_flag);
            jbResult.put("next_port_back_flag", next_port_back_flag);
            jbResult.put("use_ng_port_flag", use_ng_port_flag);
            jbResult.put("ng_port_back_flag", ng_port_back_flag);
            jbResult.put("port_count", port_count);
            jbResult.put("ng_port_count", ng_port_count);
            jbResult.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            jbResult.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            jbResult.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            jbResult.put("nextstation", nextstation);
            jbResult.put("nextstationrecipe", nextstationrecipe);
            jbResult.put("port_remaining_limit", port_remaining_limit);
            jbResult.put("ng_port_remaining_limit", ng_port_remaining_limit);
            jbResult.put("furnacebatch", furnacebatch);
            jbResult.put("port_index", port_index);
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("first_flag", first_flag);
            jbResult.put("SideExitMode", SideExitMode);//侧出模式 0：不侧出，1：侧出
            jbResult.put("loadUnLoadModel", loadUnLoadModel);
            jbResult.put("lastSubLotFlag", LastSubLotFlag);//母批最后子批标识
            jbResult.put("layerlot", layerlot);//外层批次号

            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.收板机Panel校验 裁膜分堆
    //2.1不存在NG工位
    //2.2需要根据板件条码、分堆信息来判定当前板件应该放到哪个工位
    //2.3根据任务状态来查找收板任务
    @RequestMapping(value = "/EapTlGhUnLoadPlanPanelCheckAndSave2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhUnLoadPlanPanelCheckAndSave2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/unload/EapTlGhUnLoadPlanPanelCheckAndSave2";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String meUpRecordTable = "a_eap_me_up_record";
        String meDragRecordTable = "a_eap_me_drag_record";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            Integer up_ng_code = jsonParas.getInteger("up_ng_code");//上游OK/NG标识(0:OK,1:NG,2:陪镀板)
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            String loadUnLoadModel = jsonParas.getString("loadUnLoadModel");//侧出机状态：放板模式与收板模式切换(0放板模式,1收板模式)
            String CheckDownEquipStatusFlag = cFuncDbSqlResolve.GetParameterValue("CheckDownEquipStatusFlag");//校验下游设备状态标记
            String PnlCheckLengthStr = cFuncDbSqlResolve.GetParameterValue("PnlCheckLength");//pnl条码校验截取长度(-1标识不用截取，大于0标识需截取长度)
            if (PnlCheckLengthStr == null || PnlCheckLengthStr.equals(""))
                PnlCheckLengthStr = "-1";
            int PnlCheckLengthInt = Integer.parseInt(PnlCheckLengthStr);

            //当前主生产端口信息
            String plan_id = "";//当前任务
            Integer port_index = 10;//板件对应端口   1/2/3/4为分堆工位，根据配方分堆信息放对应分堆工位；不在分堆信息里的给10，
            Integer port_volume = 0;//端口限量
            Double port_remaining_limit = 1000.0;//端口剩余限高
            Integer port_count = 0;//端口收板当前数量
            String port_pallet_num = "";//端口载具码
            String port_pallet_type = "";//端口载具类型
            String port_nextstation = "";//端口批次的下工站代码
            String port_nextstationrecipe = "";//端口批次的下工站配方
            String port_furnacebatch = "";//端口炉批号
            String port_stackingrules = "";//端口当前分堆号
            String first_flag = "N";
            //生产端口信息
            JSONArray port_infos = new JSONArray();
            JSONArray ng_port_infos = new JSONArray();
            String port_infos_str = jsonParas.getString("port_infos");
            if (port_infos_str != null && !port_infos_str.equals(""))
                port_infos = JSONArray.parseArray(port_infos_str);
            //当前上游NG端口信息
            String ng_port_infos_str = jsonParas.getString("ng_port_infos");
            if (ng_port_infos_str != null && !ng_port_infos_str.equals(""))
                ng_port_infos = JSONArray.parseArray(ng_port_infos_str);

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String group_id = "";
            String lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:上游NG,10其他异常
            Integer plc_ng_code = 0;//1-10代表OK,其他为NG，11陪镀板，
            Integer panel_index = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Double panel_tickness = 10.0;
            String panel_flag = "0";
            String lot_first_flag = "N";
            String lot_finish_flag = "N";
            String port_back_flag = "N";//生产端口是否退载具
            String next_port_flag = "N";//是否跳转到下一生产端口
            String use_ng_port_flag = "N";//是否使用上游NG端口
            String ng_port_back_flag = "N";//NG工位是否退载具
            String plan_port_code = "";//计划选择端口号
            String plan_pallet_num = "";//计划载具条码
            String plan_pallet_type = "";//计划载具类型
            String flow_port_code = "";//过站信息端口号
            String flow_pallet_num = "";//过站载具条码
            String flow_pallet_type = "";//过站载具类型
            String nextstation = "";//批次的下工站代码
            String nextstationrecipe = "";//批次的下工站配方
            String port_sign = "";
            if (sys_model == 0) offline_flag = "Y";
            String group_lot_num = "";//母批号
            String straight_flag = "N";
            //配方中的信息
            String upmode = "";//收板模式，1：按批次收/2，3：按载具容量收（批次完整性为前提）/4：数量收/5：一换一载具
            JSONArray OutPnl = new JSONArray();//外层码
            String furnacebatch = "";//批次的炉号
            JSONArray stackingrulesArray = new JSONArray();//批次的分堆信息
            String limitheight = "";//堆叠限高
            String SideExitBin = "";//侧出下机位区
            String SideExitMode = "";//侧出模式 0：不侧出，1：侧出
            String nextstationvehicaltype = "";//下工站载具类型,校验系统下发的与设备实际在用的载具是否一致
            String layerlot = "";

            //1.判断是否为Dummy板
            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                queryBigDataDummy.addCriteria(Criteria.where("dummy_flag").is("Y"));
                long dummyCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //2.判断是否是托钢板、陪渡板（mes下发记录）
            if (panel_ng_code == 0 && panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                long dummyCount = mongoTemplate.getCollection(meDragRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //根据plc给出的标识判定是否是陪镀板
            if (up_ng_code == 2) {
                dummy_flag = "Y";
                panel_ng_code = 11;
            }
            //2.判断板件状态
            if (sys_model > 0 && dummy_flag.equals("N")) {
                //查询WORK状态下的任务
                String[] lot_status_list = new String[]{"PLAN", "WORK"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(lot_status_list));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                while (iteratorBigData.hasNext()) {
                    Map<String, Object> map = iteratorBigData.next();
                    JSONObject jsonObject = JSONObject.parseObject(map.get("item_info").toString());
                    JSONArray pnlList = JSONArray.parseArray(jsonObject.getString("OutPnl"));
                    if (pnlList.contains(panel_barcode)) {
                        mapLotInfo = map;
                        break;
                    }
                }

                if (iteratorBigData.hasNext()) iteratorBigData.close();
                if (mapLotInfo == null) {
                    panel_ng_code = 8;
                    if (up_ng_code == 1) {
                        panel_ng_code = 9;
                    }
                } else {
                    group_id = mapLotInfo.get("group_id").toString();
                    group_lot_num = mapLotInfo.get("group_lot_num").toString();
                    plan_id = mapLotInfo.get("plan_id").toString();
                    lot_status = mapLotInfo.get("lot_status").toString();
                    target_lot_count = Integer.parseInt(mapLotInfo.get("target_lot_count").toString());
                    panel_tickness = Double.parseDouble(mapLotInfo.get("panel_tickness").toString());
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    if (finish_count == 0) lot_first_flag = "Y";
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    String item_info = mapLotInfo.get("item_info").toString();
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jsonObject = JSONObject.parseObject(item_info);
                        upmode = jsonObject.getString("upmode") == null ? "" : jsonObject.getString("upmode");
                        nextstation = jsonObject.get("nextstation") == null ? "" : jsonObject.get("nextstation").toString();
                        nextstationrecipe = jsonObject.get("nextstationrecipe") == null ? "" : jsonObject.get("nextstationrecipe").toString();
                        limitheight = jsonObject.get("limitheight") == null ? "0" : jsonObject.get("limitheight").toString();
                        String stackingrules_group = jsonObject.getString("stackingrules");
                        if (stackingrules_group != null && !stackingrules_group.equals("")) {
                            stackingrulesArray = JSONArray.parseArray(stackingrules_group);
                        }
                        furnacebatch = jsonObject.getString("heatno");
                        String OutPnl_group = jsonObject.getString("OutPnl");
                        if (OutPnl_group != null && !OutPnl_group.equals("")) {
                            OutPnl = JSONArray.parseArray(OutPnl_group);
                        }
                        SideExitBin = jsonObject.getString("SideExitBin");//侧出下机位区
                        SideExitMode = jsonObject.getString("SideExitMode");//侧出模式
                        nextstationvehicaltype = jsonObject.getString("nextstationvehicaltype");
                        if (nextstationvehicaltype == null) nextstationvehicaltype = "";
                    }
                    if (up_ng_code == 0) {
                        if (panel_model == 1) {//有Panel读码模式
                            if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                if (panel_barcode.equals("")) panel_ng_code = 7;
                                else panel_ng_code = 2;
                            } else {
                                //1、先判断是否混批
                                long okCount = 0;
                                if (OutPnl != null && OutPnl.size() > 0) {
                                    if (OutPnl.contains(panel_barcode)) {
                                        okCount = 1l;
                                    }
                                } else {
                                    String panel_barcode_check = panel_barcode;
                                    if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                        panel_barcode_check.substring(0, PnlCheckLengthInt);
                                    }
                                    Query queryBigDataD = new Query();
                                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                    MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                            sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                    if (iteratorBigDataD.hasNext()) {
                                        okCount = 1l;
                                        Document docItem = iteratorBigDataD.next();
                                        panel_flag = docItem.getString("panel_attr");
                                        iteratorBigDataD.close();
                                    }
                                }
                                if (okCount <= 0) panel_ng_code = 12;//混批
                                else {
                                    String panel_barcode_check = panel_barcode;
                                    if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                        panel_barcode_check.substring(0, PnlCheckLengthInt);
                                    }
                                    Query queryBigDataD = new Query();
                                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                    long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                    if (flowCount > 0) {
                                        panel_ng_code = 3;//重码
                                    } else {
                                        panel_ng_code = 0;//OK
                                        //2、判断工位
                                        if (stackingrulesArray != null && stackingrulesArray.size() > 0) {
                                            //2.1 获取板件所属分堆信息
                                            JSONObject jbStackItem = null;//分堆信息
                                            String outpnlflag = "0";//0正常，1首件，2 制中件，3修理件，4混批混料，5读码NG，6特殊板件，7缺陷板件，8涨缩后 层偏位预报废板（裁磨分板机放入NG工位）
                                            Integer maxstackpnlnum = 0;//最大分堆板件数量
                                            JSONArray jbNoStackPorts = new JSONArray();//未绑定分堆号的工位
                                            JSONObject jbPort = null;//分堆选定的端口
                                            for (int i = 0; i < stackingrulesArray.size(); i++) {
                                                JSONObject jbItem = stackingrulesArray.getJSONObject(i);
                                                JSONArray outpnlids = jbItem.getJSONArray("outpnlids");//外层码
                                                Integer stackpnlnum = jbItem.getInteger("stackpnlnum");//每堆的板件数量
                                                String mostrange = jbItem.getString("mostrange");//是否是最大分堆，1：最大分堆，0：其余分堆
                                                if (mostrange.equals("1")) {
                                                    maxstackpnlnum = stackpnlnum;
                                                }
                                                for (int j = 0; j < outpnlids.size(); j++) {
                                                    JSONObject item = outpnlids.getJSONObject(j);
                                                    if (item.getString("outpnlid").equals(panel_barcode)) {
                                                        jbStackItem = jbItem;
                                                        outpnlflag = item.getString("flag");
                                                        break;
                                                    }
                                                }
                                            }
                                            //2.2 判断侧出标识
                                            if (jbStackItem != null) {
                                                //8涨缩后 层偏位预报废板（裁磨分板机放入NG工位）
                                                if (outpnlflag.equals("8") || outpnlflag.equals("7")) {
                                                    panel_ng_code = 20;
                                                    try {
                                                        port_index = Integer.parseInt(mapLotInfo.get("port_code").toString());
                                                    } catch (Exception ex) {
                                                        port_index = 0;
                                                    }
                                                } else {
                                                    String stackingrules = jbStackItem.getString("stackingrules");//分堆号
                                                    String mostrange = jbStackItem.getString("mostrange");//是否是最大分堆，1：最大分堆，0：其余分堆
                                                    if (SideExitMode.equals("0")) {
                                                        if (CheckDownEquipStatusFlag.equals("Y")) {
                                                            //侧出模式 0：不侧出,需要进一步判断是否是最大分堆，如果是，不进分堆工位，否则进一步判断分堆工位；并且侧出机非侧入模式
                                                            if (mostrange.equals("1") && !loadUnLoadModel.equals("1")) {
                                                                //是最大分堆，不进分堆工位
                                                                port_index = 10;
                                                                straight_flag = "Y";
                                                            } else {
                                                                //判断母批中最大分数任务是否已经做完,做完最大分堆直接直流
                                                                Query queryTask = new Query();
                                                                queryTask.addCriteria(Criteria.where("station_id").is(station_id));
                                                                queryTask.addCriteria(Criteria.where("lot_status").ne("FINISH"));
                                                                queryTask.addCriteria(Criteria.where("attribute1").is("1"));
                                                                long taskCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryTask.getQueryObject());
                                                                if (taskCount <= 0 && !loadUnLoadModel.equals("1")) {
                                                                    port_index = 10;
                                                                    straight_flag = "Y";
                                                                } else {
                                                                    //判断分堆工位
                                                                    if (port_infos != null && port_infos.size() > 0) {
                                                                        for (int j = 0; j < port_infos.size(); j++) {
                                                                            JSONObject jbItem = port_infos.getJSONObject(j);
                                                                            String stackingrules_port = jbItem.getString("port_stackingrules");
                                                                            if (stackingrules_port == null || stackingrules_port.equals("")) {
                                                                                jbNoStackPorts.add(jbItem);
                                                                            } else if (stackingrules.equals(stackingrules_port)) {
                                                                                jbPort = jbItem;
                                                                            }
                                                                        }
                                                                        if (jbPort == null) {
                                                                            if (jbNoStackPorts != null && jbNoStackPorts.size() > 0) {
                                                                                for (int k = 0; k < jbNoStackPorts.size(); k++) {
                                                                                    jbPort = jbNoStackPorts.getJSONObject(k);
                                                                                    Integer port_count_k = jbPort.getInteger("port_count");
                                                                                    Integer port_volume_k = jbPort.getInteger("port_volume");
                                                                                    double port_remaining_limit_k = jbPort.getDouble("port_remaining_limit");
                                                                                    String port_furnacebatch_k = jbPort.getString("port_furnacebatch");
                                                                                    String port_nextstation_k = jbPort.getString("port_nextstation");
                                                                                    String port_nextstationrecipe_k = jbPort.getString("port_nextstationrecipe");
                                                                                    String port_pallet_type_k = jbPort.getString("port_pallet_type");
                                                                                    //1.判断数量
                                                                                    if ((port_count_k + target_lot_count) > port_volume_k) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    //2.判断限高
                                                                                    if (target_lot_count * panel_tickness > port_remaining_limit_k) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    //3.判断批次的下工站代码
                                                                                    if (port_nextstation_k != null && !port_nextstation_k.equals("") && nextstation != null && !nextstation.equals("") && !nextstation.equals(port_nextstation_k)) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    //4.判断批次的下工站配方
                                                                                    if (port_nextstationrecipe_k != null && !port_nextstationrecipe_k.equals("") && nextstationrecipe != null && !nextstationrecipe.equals("") && !nextstationrecipe.equals(port_nextstationrecipe_k)) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    //5.载具类型
                                                                                    if (!nextstationvehicaltype.equals(port_pallet_type_k)) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    if (jbPort != null) {
                                                                                        jbPort.put("port_stackingrules", stackingrules);
                                                                                        break;
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                    if (jbPort == null) {
                                                                        port_index = 0;
                                                                        panel_ng_code = 10;//无端口收板
                                                                    } else {
                                                                        port_index = jbPort.getInteger("port_index");
                                                                        port_count = jbPort.getInteger("port_count");
                                                                        port_pallet_type = jbPort.getString("port_pallet_type");
                                                                        port_pallet_num = jbPort.getString("port_pallet_num");
                                                                        port_volume = jbPort.getInteger("port_volume");
                                                                        port_nextstation = jbPort.getString("port_nextstation");
                                                                        port_nextstationrecipe = jbPort.getString("port_nextstationrecipe");
                                                                        port_remaining_limit = jbPort.getDouble("port_remaining_limit");
                                                                        port_furnacebatch = jbPort.getString("port_furnacebatch");
                                                                        port_stackingrules = jbPort.getString("port_stackingrules");
                                                                    }
                                                                }
                                                            }
                                                        } else {
                                                            if (mostrange.equals("1")) {
                                                                //是最大分堆，不进分堆工位
                                                                port_index = 10;
                                                                straight_flag = "Y";
                                                            } else {
                                                                //判断母批中最大分数任务是否已经做完,做完最大分堆直接直流
                                                                Query queryTask = new Query();
                                                                queryTask.addCriteria(Criteria.where("station_id").is(station_id));
                                                                queryTask.addCriteria(Criteria.where("lot_status").ne("FINISH"));
                                                                queryTask.addCriteria(Criteria.where("attribute1").is("1"));
                                                                long taskCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryTask.getQueryObject());
                                                                if (taskCount <= 0) {
                                                                    port_index = 10;
                                                                    straight_flag = "Y";
                                                                } else {
                                                                    //判断分堆工位
                                                                    if (port_infos != null && port_infos.size() > 0) {
                                                                        for (int j = 0; j < port_infos.size(); j++) {
                                                                            JSONObject jbItem = port_infos.getJSONObject(j);
                                                                            String stackingrules_port = jbItem.getString("port_stackingrules");
                                                                            if (stackingrules_port == null || stackingrules_port.equals("")) {
                                                                                jbNoStackPorts.add(jbItem);
                                                                            } else if (stackingrules.equals(stackingrules_port)) {
                                                                                jbPort = jbItem;
                                                                            }
                                                                        }
                                                                        if (jbPort == null) {
                                                                            if (jbNoStackPorts != null && jbNoStackPorts.size() > 0) {
                                                                                for (int k = 0; k < jbNoStackPorts.size(); k++) {
                                                                                    jbPort = jbNoStackPorts.getJSONObject(k);
                                                                                    Integer port_count_k = jbPort.getInteger("port_count");
                                                                                    Integer port_volume_k = jbPort.getInteger("port_volume");
                                                                                    double port_remaining_limit_k = jbPort.getDouble("port_remaining_limit");
                                                                                    String port_furnacebatch_k = jbPort.getString("port_furnacebatch");
                                                                                    String port_nextstation_k = jbPort.getString("port_nextstation");
                                                                                    String port_nextstationrecipe_k = jbPort.getString("port_nextstationrecipe");
                                                                                    String port_pallet_type_k = jbPort.getString("port_pallet_type");
                                                                                    //1.判断数量
                                                                                    if ((port_count_k + target_lot_count) > port_volume_k) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    //2.判断限高
                                                                                    if (target_lot_count * panel_tickness > port_remaining_limit_k) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    //3.判断批次的下工站代码
                                                                                    if (port_nextstation_k != null && !port_nextstation_k.equals("") && nextstation != null && !nextstation.equals("") && !nextstation.equals(port_nextstation_k)) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    //4.判断批次的下工站配方
                                                                                    if (port_nextstationrecipe_k != null && !port_nextstationrecipe_k.equals("") && nextstationrecipe != null && !nextstationrecipe.equals("") && !nextstationrecipe.equals(port_nextstationrecipe_k)) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    //5.载具类型
                                                                                    if (!nextstationvehicaltype.equals(port_pallet_type_k)) {
                                                                                        jbPort = null;
                                                                                        continue;
                                                                                    }
                                                                                    if (jbPort != null) {
                                                                                        jbPort.put("port_stackingrules", stackingrules);
                                                                                        break;
                                                                                    }
                                                                                }
                                                                            }
                                                                        }
                                                                    }
                                                                    if (jbPort == null) {
                                                                        port_index = 0;
                                                                        panel_ng_code = 10;//无端口收板
                                                                    } else {
                                                                        port_index = jbPort.getInteger("port_index");
                                                                        port_count = jbPort.getInteger("port_count");
                                                                        port_pallet_type = jbPort.getString("port_pallet_type");
                                                                        port_pallet_num = jbPort.getString("port_pallet_num");
                                                                        port_volume = jbPort.getInteger("port_volume");
                                                                        port_nextstation = jbPort.getString("port_nextstation");
                                                                        port_nextstationrecipe = jbPort.getString("port_nextstationrecipe");
                                                                        port_remaining_limit = jbPort.getDouble("port_remaining_limit");
                                                                        port_furnacebatch = jbPort.getString("port_furnacebatch");
                                                                        port_stackingrules = jbPort.getString("port_stackingrules");
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    } else {
                                                        //判断分堆工位
                                                        if (port_infos != null && port_infos.size() > 0) {
                                                            for (int j = 0; j < port_infos.size(); j++) {
                                                                JSONObject jbItem = port_infos.getJSONObject(j);
                                                                String stackingrules_port = jbItem.getString("port_stackingrules");
                                                                if (stackingrules_port == null || stackingrules_port.equals("")) {
                                                                    jbNoStackPorts.add(jbItem);
                                                                } else if (stackingrules.equals(stackingrules_port)) {
                                                                    jbPort = jbItem;
                                                                }
                                                            }
                                                            if (jbPort == null) {
                                                                if (jbNoStackPorts != null && jbNoStackPorts.size() > 0) {
                                                                    for (int k = 0; k < jbNoStackPorts.size(); k++) {
                                                                        jbPort = jbNoStackPorts.getJSONObject(k);
                                                                        Integer port_count_k = jbPort.getInteger("port_count");
                                                                        Integer port_volume_k = jbPort.getInteger("port_volume");
                                                                        double port_remaining_limit_k = jbPort.getDouble("port_remaining_limit");
                                                                        String port_furnacebatch_k = jbPort.getString("port_furnacebatch");
                                                                        String port_nextstation_k = jbPort.getString("port_nextstation");
                                                                        String port_nextstationrecipe_k = jbPort.getString("port_nextstationrecipe");
                                                                        String port_pallet_type_k = jbPort.getString("port_pallet_type");
                                                                        //1.判断数量
                                                                        if ((port_count_k + target_lot_count) > port_volume_k) {
                                                                            jbPort = null;
                                                                            continue;
                                                                        }
                                                                        //2.判断限高
                                                                        if (target_lot_count * panel_tickness > port_remaining_limit_k) {
                                                                            jbPort = null;
                                                                            continue;
                                                                        }
                                                                        //3.判断批次的下工站代码
                                                                        if (port_nextstation_k != null && !port_nextstation_k.equals("") && nextstation != null && !nextstation.equals("") && !nextstation.equals(port_nextstation_k)) {
                                                                            jbPort = null;
                                                                            continue;
                                                                        }
                                                                        //4.判断批次的下工站配方
                                                                        if (port_nextstationrecipe_k != null && !port_nextstationrecipe_k.equals("") && nextstationrecipe != null && !nextstationrecipe.equals("") && !nextstationrecipe.equals(port_nextstationrecipe_k)) {
                                                                            jbPort = null;
                                                                            continue;
                                                                        }
                                                                        //5.载具类型
                                                                        if (!nextstationvehicaltype.equals(port_pallet_type_k)) {
                                                                            jbPort = null;
                                                                            continue;
                                                                        }
                                                                        if (jbPort != null) {
                                                                            jbPort.put("port_stackingrules", stackingrules);
                                                                            break;
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                        }
                                                        if (jbPort == null) {
                                                            port_index = 0;
                                                            panel_ng_code = 10;//无端口收板
                                                        } else {
                                                            port_index = jbPort.getInteger("port_index");
                                                            port_count = jbPort.getInteger("port_count");
                                                            port_pallet_type = jbPort.getString("port_pallet_type");
                                                            port_pallet_num = jbPort.getString("port_pallet_num");
                                                            port_volume = jbPort.getInteger("port_volume");
                                                            port_nextstation = jbPort.getString("port_nextstation");
                                                            port_nextstationrecipe = jbPort.getString("port_nextstationrecipe");
                                                            port_remaining_limit = jbPort.getDouble("port_remaining_limit");
                                                            port_furnacebatch = jbPort.getString("port_furnacebatch");
                                                            port_stackingrules = jbPort.getString("port_stackingrules");
                                                        }
                                                    }
                                                }
                                            } else {
                                                panel_ng_code = 12;//混批
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    //重码放行但不计数
                    if (panel_ng_code != 3)
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                    panel_index = finish_count;
                    //读码NG的直接放行，算到收板OK
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                    } else if (panel_ng_code != 3) {
                        finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                    }
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        if (next_port_flag.equals("N")) {
                            port_count++;
                            port_remaining_limit = port_remaining_limit - panel_tickness;
                        }
                    }
                    //判断当前作业端口是否退载具
                    if (finish_count >= target_lot_count) {
                        lot_finish_flag = "Y";
                        if (port_index > 0) {
                            //一车一批退载具
                            if (upmode.equals("1")) {
                                port_back_flag = "Y";
                            } else {
                                if (port_count >= port_volume) port_back_flag = "Y";
                                else if (port_remaining_limit < panel_tickness) port_back_flag = "Y";
                                else {
                                    //读取下一个任务
                                    Integer next_count = 0;
                                    Double next_panel_tickness = 10.0;
                                    queryBigData = new Query();
                                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                                    queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                                    queryBigData.addCriteria(Criteria.where("plan_id").ne(plan_id));
                                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                                    MongoCursor<Document> iteratorBigData1 = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                    if (iteratorBigData1.hasNext()) {
                                        Document document = iteratorBigData1.next();
                                        next_count = document.getInteger("target_lot_count");
                                        next_panel_tickness = Double.parseDouble(document.get("panel_tickness").toString());
                                        iteratorBigData.close();
                                    }
                                    if (port_count + next_count > port_volume) port_back_flag = "Y";
                                    if (next_count * next_panel_tickness > port_remaining_limit) port_back_flag = "Y";
                                }
                            }
                        }
                    }
                    //判断是否存在特殊板件
                    if (panel_flag.equals("6")) {
                        if (port_index > 0) port_back_flag = "Y";
                    }
                }
            }
            //3.锁定选择端口以及返回给PLC代码
            if (dummy_flag.equals("Y")) {
                plc_ng_code = panel_ng_code;
            } else {
                if (sys_model > 0) {
                    plc_ng_code = panel_ng_code + 10;
                    plan_pallet_num = port_pallet_num;
                    plan_pallet_type = port_pallet_type;
                    flow_pallet_num = port_pallet_num;
                    flow_pallet_type = port_pallet_type;
                    //重码放行但不计数
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 3 || panel_ng_code == 7) {
                        if (port_index > 0) {
                            plc_ng_code = port_index;
                        }
                    }
                    if (mapLotInfo != null && next_port_flag.equals("N") && port_index > 0) {
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, port_index, plan_port_code, port_sign);
                        flow_port_code = plan_port_code;
                    }
                } else {
                    plc_ng_code = 1;
                }
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : (panel_ng_code == 4 || panel_ng_code == 2 || panel_ng_code == 7) ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_id").toString());
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);
            mapBigDataRow.put("straight_flag", straight_flag);

            JSONObject jbResult = new JSONObject();
            String first_bind_flag = "N";
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                //更改
                Update updateBigData = new Update();
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                String port_code = mapLotInfo.get("port_code").toString();
                if (finish_count == 1) {
                    first_flag = "Y";
                    first_bind_flag = "Y";
                    updateBigData.set("port_code", plan_port_code);
                    updateBigData.set("pallet_num", plan_pallet_num);
                    updateBigData.set("pallet_type", plan_pallet_type);
                    updateBigData.set("lot_status", "WORK");
                    updateBigData.set("group_lot_status", "WORK");
                }
                if (lot_finish_flag.equals("Y")) {
                    String task_start_time = mapLotInfo.get("task_start_time").toString();
                    if (task_start_time.equals("")) task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    long task_cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                    Integer task_error_code = 0;
                    if (target_lot_count == finish_ok_count) task_error_code = 1;
                    else if (target_lot_count > finish_ok_count) task_error_code = 2;
                    else if (target_lot_count < finish_ok_count) task_error_code = 3;
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("group_lot_status", "FINISH");
                    updateBigData.set("task_end_time", task_end_time);
                    updateBigData.set("task_cost_time", task_cost_time);
                    updateBigData.set("task_error_code", task_error_code);
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }
            //判断母批是否完成
            String last_panel_sort = "N";
            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(lot_status_list));
            long taskCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
            if (taskCount <= 0) {
                last_panel_sort = "Y";
            }
            //组合返回数据
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            jbResult.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : opCommonFunc.GetPortInfoByPortIndex(station_id, port_index, plan_port_code, port_sign));
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("plc_ng_code", plc_ng_code);
            jbResult.put("first_bind_flag", first_bind_flag);
            jbResult.put("lot_finish_flag", lot_finish_flag);
            jbResult.put("port_back_flag", port_back_flag);
            jbResult.put("next_port_flag", next_port_flag);
            jbResult.put("use_ng_port_flag", use_ng_port_flag);
            jbResult.put("ng_port_back_flag", ng_port_back_flag);
            jbResult.put("port_count", port_count);
            jbResult.put("ng_port_count", 0);
            jbResult.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            jbResult.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            jbResult.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            jbResult.put("nextstation", nextstation);
            jbResult.put("nextstationrecipe", nextstationrecipe);
            jbResult.put("port_remaining_limit", port_remaining_limit);
            jbResult.put("ng_port_remaining_limit", "0");
            jbResult.put("furnacebatch", furnacebatch);
            jbResult.put("port_index", port_index);
            jbResult.put("first_flag", first_flag);
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("port_stackingrules", port_stackingrules);
            jbResult.put("SideExitMode", SideExitMode);//侧出模式 0：不侧出，1：侧出
            jbResult.put("last_panel_sort", last_panel_sort);//是否是母批最后一片
            jbResult.put("loadUnLoadModel", loadUnLoadModel);
            jbResult.put("lastSubLotFlag", "Y");//母批最后子批标识
            jbResult.put("layerlot", layerlot);//外层批次号
            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.收板机Panel校验 一体机
    //1.收板机Panel校验
    //1.1到达载具容量退载具
    //1.2到达载具限高退载具
    //1.3下工站代码变化退载具
    //1.4下工站配方变化退载具
    //1.5一车一批退载具
    //1.6特殊板件退载具
    //1.7不同炉批号切换载具
    //1.8根据分堆号切换载具
    @RequestMapping(value = "/EapTlGhUnLoadPlanPanelCheckAndSave3", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhUnLoadPlanPanelCheckAndSave3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/unload/EapTlGhUnLoadPlanPanelCheckAndSave3";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String meUpRecordTable = "a_eap_me_up_record";
        String meDragRecordTable = "a_eap_me_drag_record";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            Integer up_ng_code = jsonParas.getInteger("up_ng_code");//上游OK/NG标识(0:OK,1:NG,2:陪镀板)
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            String isStackStation = jsonParas.getString("isStackStation");//判断是否是堆叠工位
            if (isStackStation == null) isStackStation = "N";
            //当前主生产端口信息
            String port_group_id = jsonParas.getString("port_group_id");
            String plan_id = jsonParas.getString("plan_id");
            Integer port_index = jsonParas.getInteger("port_index");
            Integer port_volume = jsonParas.getInteger("port_volume");
            String port_remaining_limit_str = jsonParas.getString("port_remaining_limit");
            if (port_remaining_limit_str == null || port_remaining_limit_str.equals("") || port_remaining_limit_str.equals("0"))
                port_remaining_limit_str = "1000";
            Double port_remaining_limit = Double.parseDouble(port_remaining_limit_str);//剩余限高
            Integer port_count = jsonParas.getInteger("port_count");
            String port_pallet_num = jsonParas.getString("port_pallet_num");
            String port_pallet_type = jsonParas.getString("port_pallet_type");
            Integer next_port_index = jsonParas.getInteger("next_port_index");
            String port_nextstation = jsonParas.getString("port_nextstation");//批次的下工站代码
            String port_nextstationrecipe = jsonParas.getString("port_nextstationrecipe");//批次的下工站配方
            String port_furnacebatch = jsonParas.getString("port_furnacebatch");//端口炉号

            String next_port_pallet_type = jsonParas.getString("next_port_pallet_type");//下一端口载具类型
            if (next_port_pallet_type == null) next_port_pallet_type = "NoPallet";
            String pallet_type_check = jsonParas.getString("pallet_type_check");//是否需要判断载具类型
            if (pallet_type_check == null) pallet_type_check = "N";
            //当前上游NG端口信息
            Integer ng_port_index = jsonParas.getInteger("ng_port_index");//-1代表无专门的上游NG收板端口,0代表载具未载入
            Integer ng_port_volume = jsonParas.getInteger("ng_port_volume");
            Double ng_port_remaining_limit = jsonParas.getDouble("ng_port_remaining_limit");//剩余限高
            Integer ng_port_lotcount = jsonParas.getInteger("ng_port_lotcount");
            String ng_port_planid_list = jsonParas.getString("ng_port_planid_list");
            Integer ng_port_count = jsonParas.getInteger("ng_port_count");
            String ng_port_pallet_num = jsonParas.getString("ng_port_pallet_num");
            String ng_port_pallet_type = jsonParas.getString("ng_port_pallet_type");
            String loadUnLoadModel = jsonParas.getString("loadUnLoadModel");//侧出机状态：放板模式与收板模式切换(0放板模式,1收板模式)
            String CheckDownEquipStatusFlag = cFuncDbSqlResolve.GetParameterValue("CheckDownEquipStatusFlag");//校验下游设备状态标记
            String PnlCheckLengthStr = cFuncDbSqlResolve.GetParameterValue("PnlCheckLength");//pnl条码校验截取长度(-1标识不用截取，大于0标识需截取长度)
            if (PnlCheckLengthStr == null || PnlCheckLengthStr.equals(""))
                PnlCheckLengthStr = "-1";
            int PnlCheckLengthInt = Integer.parseInt(PnlCheckLengthStr);

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String group_id = "";
            String lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:上游NG,10其他异常
            Integer plc_ng_code = 0;//1-10代表OK,其他为NG，11陪镀板，
            Integer panel_index = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer finish_up_ng_count = 0;
            Double panel_tickness = 10.0;
            String panel_flag = "0";
            String lot_first_flag = "N";
            String lot_finish_flag = "N";
            String port_back_flag = "N";//生产端口是否退载具
            String next_port_flag = "N";//是否跳转到下一生产端口
            String next_port_back_flag = "N";//生产端口是否退载具
            String use_ng_port_flag = "N";//是否使用上游NG端口
            String ng_port_back_flag = "N";//NG工位是否退载具
            String plan_port_code = "";//计划选择端口号
            String plan_pallet_num = "";//计划载具条码
            String plan_pallet_type = "";//计划载具类型
            String flow_port_code = "";//过站信息端口号
            String flow_pallet_num = "";//过站载具条码
            String flow_pallet_type = "";//过站载具类型
            String nextstation = "";//批次的下工站代码
            String nextstationrecipe = "";//批次的下工站配方
            String port_sign = "";
            if (sys_model == 0) offline_flag = "Y";
            if (plan_id.equals("")) lot_first_flag = "Y";
            String first_flag = "N";
            String LastSubLotFlag = "N";
            //配方信息
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件,3：不停机首件（内外层）
            Integer inspect_count = 0;//首件数量
            Integer inspect_finish_count = 0;//首件完成数量
            String upmode = "";//收板模式，1：按批次收/2，3：按载具容量收（批次完整性为前提）/4：数量收/5：一换一载具
            String furnacebatch = "";//批次的炉号
            JSONArray OutPnl = new JSONArray();//外层码
            String SideExitBin = "";//侧出下机位区
            String SideExitMode = "";//侧出模式 0：不侧出，1：侧出
            String nextstationvehicaltype = "";//下工站载具类型,校验系统下发的与设备实际在用的载具是否一致
            String layerlot = "";//外层批次号

            //1.判断是否为Dummy板
            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                queryBigDataDummy.addCriteria(Criteria.where("dummy_flag").is("Y"));
                long dummyCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //2.判断是否是托钢板、陪渡板（mes下发记录）
            if (panel_ng_code == 0 && panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                long dummyCount = mongoTemplate.getCollection(meDragRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //判断是否是首件板
//            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
//                Query queryBigDataInspect = new Query();
//                queryBigDataInspect.addCriteria(Criteria.where("station_id").is(station_id));
//                queryBigDataInspect.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
//                queryBigDataInspect.addCriteria(Criteria.where("inspect_flag").is("Y"));
//                long inspectCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataInspect.getQueryObject());
//                if (inspectCount > 0) {
//                    inspect_flag = "Y";
//                }
//            }
            //根据plc给出的标识判定是否是陪镀板
            if (up_ng_code == 2) {
                dummy_flag = "Y";
                panel_ng_code = 11;
            }

            String station_offline_flag = "N";//最后一个工位标识
            String station_attr = "";//工位属性
            String sqlStation = "select COALESCE(offline_flag,'N') offline_flag," +
                    "COALESCE(station_attr,'') station_attr from sys_fmod_station " +
                    "where station_id=" + station_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                    false, null, "");
            if (itemListStation != null && itemListStation.size() > 0) {
                station_offline_flag = itemListStation.get(0).get("offline_flag").toString();
                station_attr = itemListStation.get(0).get("station_attr").toString();
            }

            //2.判断板件状态
            if (sys_model > 0 && dummy_flag.equals("N")) {
                if (lot_first_flag.equals("Y")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    if (!station_attr.toLowerCase().equals("unload")) {
                        queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));
                    }
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        if (pallet_type_check.equals("Y")) {
                            String pallet_type = mapLotInfo.get("pallet_type") == null ? "" : mapLotInfo.get("pallet_type").toString();
                            if (pallet_type.equals(port_pallet_type)) {
                                iteratorBigData.close();
                            } else if (pallet_type.equals(next_port_pallet_type)) {
                                if (next_port_index > 0) next_port_flag = "Y";
                                else panel_ng_code = 10;//无端口收板
                                iteratorBigData.close();
                            }
                        } else
                            iteratorBigData.close();
                    }
                } else {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                }
                if (mapLotInfo == null) {
                    panel_ng_code = 8;
                    if (up_ng_code == 1) {
                        panel_ng_code = 9;
                        if (ng_port_index > 0) {
                            use_ng_port_flag = "Y";
                            ng_port_count++;
                            if (ng_port_count >= ng_port_volume) {
                                ng_port_back_flag = "Y";
                            }
                            ng_port_remaining_limit = ng_port_remaining_limit - panel_tickness;
                            if (ng_port_remaining_limit < panel_tickness) {
                                ng_port_back_flag = "Y";
                            }
                        }
                    }
                } else {
                    group_id = mapLotInfo.get("group_id").toString();
                    plan_id = mapLotInfo.get("plan_id").toString();
                    lot_status = mapLotInfo.get("lot_status").toString();
                    target_lot_count = Integer.parseInt(mapLotInfo.get("target_lot_count").toString());
                    panel_tickness = Double.parseDouble(mapLotInfo.get("panel_tickness").toString());
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    String attribute3 = mapLotInfo.get("attribute3").toString();//上游NG板件数量
                    LastSubLotFlag = mapLotInfo.get("attribute2").toString();//放板机母批最后子批标识
                    if (attribute3 == null || attribute3.equals("")) attribute3 = "0";
                    finish_up_ng_count = Integer.parseInt(attribute3);
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    String item_info = mapLotInfo.get("item_info").toString();
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jsonObject = JSONObject.parseObject(item_info);
                        firstmode = jsonObject.getString("firstmode") == null ? "" : jsonObject.getString("firstmode");
                        upmode = jsonObject.getString("upmode") == null ? "" : jsonObject.getString("upmode");
                        nextstation = jsonObject.get("nextstation") == null ? "" : jsonObject.get("nextstation").toString();
                        nextstationrecipe = jsonObject.get("nextstationrecipe") == null ? "" : jsonObject.get("nextstationrecipe").toString();
                        String OutPnl_group = jsonObject.getString("OutPnl");
                        if (OutPnl_group != null && !OutPnl_group.equals("")) {
                            OutPnl = JSONArray.parseArray(OutPnl_group);
                        }
                        SideExitBin = jsonObject.getString("SideExitBin");//侧出下机位区
                        SideExitMode = jsonObject.getString("SideExitMode");//侧出模式
                        if (SideExitMode == null) SideExitMode = "1";
                        if (SideExitBin == null) SideExitBin = station_code;
                        furnacebatch = jsonObject.getString("heatno");
                        nextstationvehicaltype = jsonObject.getString("nextstationvehicaltype");
                        if (nextstationvehicaltype == null) nextstationvehicaltype = "";
                        String heatnumberListStr = jsonObject.getString("heatnumberList");
                        if (heatnumberListStr != null) {
                            JSONArray heatnumberList = JSONArray.parseArray(heatnumberListStr);
                            if (heatnumberList != null && heatnumberList.size() > 0) {
                                JSONObject jbItem = heatnumberList.getJSONObject(0);
                                layerlot = jbItem.getString("layerlot");
                            }
                        }
                    }
                    if (up_ng_code == 0) {
                        if (port_index <= 0) panel_ng_code = 10;//无端口收板
                        else {
                            if (lot_first_flag.equals("Y")) {
                                //一换一载具  其他什么条件都不再判断
                                if (upmode.equals("5")) {
                                    if (port_group_id != null && !port_group_id.equals("") && group_id != null && !group_id.equals("") && !group_id.equals(port_group_id)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                } else {
                                    //1.判断数量
                                    if ((port_count + target_lot_count) > port_volume) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //2.判断限高
                                    if (target_lot_count * panel_tickness > port_remaining_limit) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //3.判断批次的下工站代码
                                    if (port_nextstation != null && !port_nextstation.equals("") && nextstation != null && !nextstation.equals("") && !nextstation.equals(port_nextstation)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //4.判断批次的下工站配方
                                    if (port_nextstationrecipe != null && !port_nextstationrecipe.equals("") && nextstationrecipe != null && !nextstationrecipe.equals("") && !nextstationrecipe.equals(port_nextstationrecipe)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //5.判断批次的炉批次
                                    if (port_furnacebatch != null && !port_furnacebatch.equals("") && furnacebatch != null && !furnacebatch.equals("") && !furnacebatch.equals(port_furnacebatch)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //6.判断下工站载具类型
                                    if (!isStackStation.equals("Y")) {
                                        if (SideExitMode.equals("1") && SideExitBin.equals(station_code)
                                                && !nextstationvehicaltype.equals(port_pallet_type)) {
                                            if (port_count > 0) port_back_flag = "Y";
                                            if (next_port_index > 0 && nextstationvehicaltype.equals(next_port_pallet_type))
                                                next_port_flag = "Y";
                                            else panel_ng_code = 10;//无端口收板
                                        }
                                    }
                                }
                            }
                            //2：下料首件，首件做完退载具
                            if (firstmode.equals("2")) {
                                inspect_flag = "Y";
                                panel_ng_code = 13; //首件模式，收板全部放在NG工位
                                target_lot_count = inspect_count;//任务数量=首件数量
                            }
                            if (panel_ng_code == 0) {
                                if (panel_model == 1) {//有Panel读码模式
                                    if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                        if (panel_barcode.equals("")) panel_ng_code = 7;
                                        else panel_ng_code = 2;
                                    } else {
                                        long okCount = 0;
                                        if (OutPnl != null && OutPnl.size() > 0) {
                                            if (OutPnl.contains(panel_barcode)) {
                                                okCount = 1l;
                                            }
                                        } else {
                                            String panel_barcode_check = panel_barcode;
                                            if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                                panel_barcode_check.substring(0, PnlCheckLengthInt);
                                            }
                                            Query queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                                    sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                            if (iteratorBigDataD.hasNext()) {
                                                okCount = 1l;
                                                Document docItem = iteratorBigDataD.next();
                                                panel_flag = docItem.getString("panel_attr");
                                                iteratorBigDataD.close();
                                            }
                                        }
                                        if (okCount <= 0) panel_ng_code = 12;
                                        else {
                                            String panel_barcode_check = panel_barcode;
                                            if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                                panel_barcode_check.substring(0, PnlCheckLengthInt);
                                            }
                                            Query queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (flowCount > 0) {
                                                panel_ng_code = 3;//重码
                                            }
                                        }
                                        if (panel_flag.equals("1")) {
                                            inspect_flag = "Y";
                                            if (station_offline_flag.equals("Y")) {
                                                panel_ng_code = 13; //首件也需要放到NG工位
                                            }
                                        }
                                    }
                                }
                                if (isStackStation.equals("Y")) {
                                    if (panel_tickness > port_remaining_limit) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                }
                            }
                        }
                    } else {
                        if (up_ng_code == 1) {
                            finish_up_ng_count += 1;
                        }
                        panel_ng_code = 9;
                        if (ng_port_index > 0) {
                            use_ng_port_flag = "Y";
                            ng_port_count++;
                            if (ng_port_count >= ng_port_volume) {
                                ng_port_back_flag = "Y";
                            }

                            ng_port_remaining_limit = ng_port_remaining_limit - panel_tickness;
                            if (ng_port_remaining_limit < panel_tickness) {
                                ng_port_back_flag = "Y";
                            }
                        }
                    }

                    if (panel_ng_code == 0 && (firstmode.equals("1") || firstmode.equals("3")) && inspect_finish_count < inspect_count) {
                        inspect_flag = "Y";
                        inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                    }
                    //重码放行但不计数
                    if (panel_ng_code != 3)
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                    panel_index = finish_count;
                    //读码NG的直接放行，算到收板OK
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                    } else if (panel_ng_code != 3) {
                        finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                    }
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        if (next_port_flag.equals("N")) {
                            port_count++;
                            port_remaining_limit = port_remaining_limit - panel_tickness;
                        }
                    }
                    //判断当前作业端口是否退载具
                    if (finish_count >= target_lot_count) {
                        lot_finish_flag = "Y";
                        //上游NG时退载具
                        if (finish_up_ng_count > 0) {
                            if (port_index > 0) port_back_flag = "Y";
                        } else {
                            if (port_index > 0) {
                                //判断是否是堆叠工位，如果是堆叠工位，需要判断当前任务是否分了多个载具（根据限高判断），如果超过一个载具，直接退载具
                                if (isStackStation.equals("Y")) {
                                    String LimitHeight = cFuncDbSqlResolve.GetParameterValue("LimitHeight");
                                    if (LimitHeight.equals("")) LimitHeight = "20";
                                    Double LimitHeight_Double = Double.parseDouble(LimitHeight);//工位限高
                                    if (LimitHeight_Double <= panel_tickness * target_lot_count) {
                                        if (next_port_flag.equals("Y")) {
                                            next_port_back_flag = "Y";
                                        } else {
                                            port_back_flag = "Y";
                                        }
                                    }
                                }
                                //一载具换一载具，母批结束时候退载具，根据任务attribute2=Y判断是最后一个子批
                                if (upmode.equals("5")) {
                                    if (LastSubLotFlag != null && LastSubLotFlag.equals("Y")) {
                                        port_back_flag = "Y";
                                    }
                                } else {
                                    //一车一批
                                    if (upmode.equals("1")) {
                                        port_back_flag = "Y";
                                    } else {
                                        if (port_count >= port_volume) port_back_flag = "Y";
                                        else if (port_remaining_limit < panel_tickness) port_back_flag = "Y";
                                        else {
                                            //读取下一个任务
                                            Integer next_count = 0;
                                            Double next_panel_tickness = 10.0;
                                            queryBigData = new Query();
                                            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                                            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                                            queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                                            queryBigData.addCriteria(Criteria.where("plan_id").ne(plan_id));
                                            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                                            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                                                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                            if (iteratorBigData.hasNext()) {
                                                Document document = iteratorBigData.next();
                                                next_count = document.getInteger("target_lot_count");
                                                next_panel_tickness = Double.parseDouble(document.get("panel_tickness").toString());
                                                iteratorBigData.close();
                                            }
                                            if (port_count + next_count > port_volume) port_back_flag = "Y";
                                            if (next_count * next_panel_tickness > port_remaining_limit)
                                                port_back_flag = "Y";
                                        }
                                    }
                                }
                            }
                        }
                        //判断上游NG工位批次数量是否达到最大值
                        if (ng_port_index > 0 && ng_port_lotcount > 0) {
                            if (panel_ng_code == 9) {
                                if (ng_port_planid_list.equals("")) ng_port_planid_list = plan_id;
                                else {
                                    String[] tempList = ng_port_planid_list.split(",", -1);
                                    if (!Arrays.asList(tempList).contains(plan_id))
                                        ng_port_planid_list += "," + plan_id;
                                }
                            }
                            if (!ng_port_planid_list.equals("")) {
                                String[] tempList = ng_port_planid_list.split(",", -1);
                                if (tempList != null && tempList.length >= ng_port_lotcount) {
                                    ng_port_back_flag = "Y";
                                }
                            }
                        }
                    }
                    //判断是否存在特殊板件
                    if (panel_flag.equals("6")) {
                        if (port_index > 0) port_back_flag = "Y";
                    }
                }
            }

            //3.锁定选择端口以及返回给PLC代码
            if (dummy_flag.equals("Y")) {
                plc_ng_code = panel_ng_code;
            } else {
                if (sys_model > 0) {
                    plc_ng_code = panel_ng_code + 10;
                    plan_pallet_num = port_pallet_num;
                    plan_pallet_type = port_pallet_type;
                    flow_pallet_num = port_pallet_num;
                    flow_pallet_type = port_pallet_type;
                    //重码放行但不计数
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 3 || panel_ng_code == 7) {
                        if (next_port_flag.equals("Y")) {
                            plc_ng_code = next_port_index;
                            plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, next_port_index, plan_port_code, port_sign);
                            flow_port_code = plan_port_code;
                        } else {
                            if (port_index > 0) {
                                plc_ng_code = port_index;
                            }
                        }
                    }
                    if (mapLotInfo != null && next_port_flag.equals("N") && port_index > 0) {
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, port_index, plan_port_code, port_sign);
                        flow_port_code = plan_port_code;
                    }
                    if (use_ng_port_flag.equals("Y")) {
                        plc_ng_code = ng_port_index;
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, ng_port_index, flow_port_code, port_sign);
                        flow_pallet_num = ng_port_pallet_num;
                        flow_pallet_type = ng_port_pallet_type;
                    }
                } else {
                    plc_ng_code = 1;
                }
            }

            //判断是否直通，直通直接给plc反馈10；1.根据侧进侧出模式判断
            String straight_flag = "N";//直通标识
            if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 3 || panel_ng_code == 7) {
                if (CheckDownEquipStatusFlag.equals("Y")) {
                    if (SideExitMode.equals("0") && !loadUnLoadModel.equals("1") && station_offline_flag.equals("N")) {
                        //直通模式&下游非放板模式&非线尾工位  视为直通
                        port_index = 10;
                        plc_ng_code = 10;
                        straight_flag = "Y";
                    } else if (SideExitMode.equals("1")) {
                        if (!SideExitBin.equals(station_code) && !loadUnLoadModel.equals("1")) {
                            //侧出模式&当前工位不是侧出工位  视为直通
                            port_index = 10;
                            plc_ng_code = 10;
                            straight_flag = "Y";
                        }
                    }
                } else {
                    if (SideExitMode.equals("0") && station_offline_flag.equals("N")) {
                        //直通模式&下游非放板模式&非线尾工位  视为直通
                        port_index = 10;
                        plc_ng_code = 10;
                        straight_flag = "Y";
                    } else if (SideExitMode.equals("1")) {
                        if (!SideExitBin.equals(station_code)) {
                            //侧出模式&当前工位不是侧出工位  视为直通
                            port_index = 10;
                            plc_ng_code = 10;
                            straight_flag = "Y";
                        }
                    }
                }
            }

            //判断是否直通，直通直接给plc反馈10；2.根据首件模式判断
            if (firstmode.equals("3") && inspect_flag.equals("Y")) {
                //firstmode=3&inspect_flag=Y  视为直通
                port_index = 10;
                plc_ng_code = 10;
                straight_flag = "Y";
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : (panel_ng_code == 4 || panel_ng_code == 2 || panel_ng_code == 7) ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_id").toString());
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);
            mapBigDataRow.put("straight_flag", straight_flag);

            JSONObject jbResult = new JSONObject();
            String first_bind_flag = "N";
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                //更改
                Update updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                updateBigData.set("attribute3", finish_up_ng_count.toString());
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                String port_code = mapLotInfo.get("port_code").toString();
                if (port_code.equals("") && !plan_port_code.equals("")) {
                    first_flag = "Y";
                    first_bind_flag = "Y";
                    updateBigData.set("port_code", plan_port_code);
                    updateBigData.set("pallet_num", plan_pallet_num);
                    updateBigData.set("pallet_type", plan_pallet_type);
                    updateBigData.set("lot_status", "WORK");
                    updateBigData.set("group_lot_status", "WORK");
                }
                if (lot_finish_flag.equals("Y")) {
                    String task_start_time = mapLotInfo.get("task_start_time").toString();
                    if (task_start_time.equals("")) task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    long task_cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                    Integer task_error_code = 0;
                    if (target_lot_count == finish_ok_count) task_error_code = 1;
                    else if (target_lot_count > finish_ok_count) task_error_code = 2;
                    else if (target_lot_count < finish_ok_count) task_error_code = 3;
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("group_lot_status", "FINISH");
                    updateBigData.set("task_end_time", task_end_time);
                    updateBigData.set("task_cost_time", task_cost_time);
                    updateBigData.set("task_error_code", task_error_code);
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //组合返回数据
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            jbResult.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("plc_ng_code", plc_ng_code);
            jbResult.put("first_bind_flag", first_bind_flag);
            jbResult.put("lot_finish_flag", lot_finish_flag);
            jbResult.put("port_back_flag", port_back_flag);
            jbResult.put("next_port_flag", next_port_flag);
            jbResult.put("next_port_back_flag", next_port_back_flag);
            jbResult.put("use_ng_port_flag", use_ng_port_flag);
            jbResult.put("ng_port_back_flag", ng_port_back_flag);
            jbResult.put("port_count", port_count);
            jbResult.put("ng_port_count", ng_port_count);
            jbResult.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            jbResult.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            jbResult.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            jbResult.put("nextstation", nextstation);
            jbResult.put("nextstationrecipe", nextstationrecipe);
            jbResult.put("port_remaining_limit", port_remaining_limit);
            jbResult.put("ng_port_remaining_limit", ng_port_remaining_limit);
            jbResult.put("furnacebatch", furnacebatch);
            jbResult.put("port_index", port_index);
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("first_flag", first_flag);
            jbResult.put("SideExitMode", SideExitMode);//侧出模式 0：不侧出，1：侧出
            jbResult.put("loadUnLoadModel", loadUnLoadModel);
            jbResult.put("lastSubLotFlag", LastSubLotFlag);//母批最后子批标识
            jbResult.put("layerlot", layerlot);//外层批次号

            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //配板机
    //1.收板机Panel校验
    //1.1到达载具容量退载具
    //1.2到达载具限高退载具
    //1.3下工站代码变化退载具
    //1.4下工站配方变化退载具
    //1.5一车一批退载具
    //1.6特殊板件退载具
    //1.7不同炉批号切换载具
    //1.8根据分堆号切换载具
    @RequestMapping(value = "/EapTlGhUnLoadPlanPanelCheckAndSave4", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhUnLoadPlanPanelCheckAndSave4(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/unload/EapTlGhUnLoadPlanPanelCheckAndSave4";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String meUpRecordTable = "a_eap_me_up_record";
        String meDragRecordTable = "a_eap_me_drag_record";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            Integer up_ng_code = jsonParas.getInteger("up_ng_code");//上游OK/NG标识(0:OK,1:NG,2:陪镀板)
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            String isStackStation = jsonParas.getString("isStackStation");//判断是否是堆叠工位
            if (isStackStation == null) isStackStation = "N";
            //当前主生产端口信息
            String port_group_id = jsonParas.getString("port_group_id");
            String plan_id = jsonParas.getString("plan_id");
            Integer port_index = jsonParas.getInteger("port_index");
            Integer port_volume = jsonParas.getInteger("port_volume");
            String port_remaining_limit_str = jsonParas.getString("port_remaining_limit");
            if (port_remaining_limit_str == null || port_remaining_limit_str.equals("") || port_remaining_limit_str.equals("0"))
                port_remaining_limit_str = "1000";
            Double port_remaining_limit = Double.parseDouble(port_remaining_limit_str);//剩余限高
            Integer port_count = jsonParas.getInteger("port_count");
            String port_pallet_num = jsonParas.getString("port_pallet_num");
            String port_pallet_type = jsonParas.getString("port_pallet_type");
            Integer next_port_index = jsonParas.getInteger("next_port_index");
            String port_nextstation = jsonParas.getString("port_nextstation");//批次的下工站代码
            String port_nextstationrecipe = jsonParas.getString("port_nextstationrecipe");//批次的下工站配方
            String port_furnacebatch = jsonParas.getString("port_furnacebatch");//端口炉号

            String next_port_pallet_type = jsonParas.getString("next_port_pallet_type");//下一端口载具类型
            if (next_port_pallet_type == null) next_port_pallet_type = "NoPallet";
            String pallet_type_check = jsonParas.getString("pallet_type_check");//是否需要判断载具类型
            if (pallet_type_check == null) pallet_type_check = "N";
            //当前上游NG端口信息
            Integer ng_port_index = jsonParas.getInteger("ng_port_index");//-1代表无专门的上游NG收板端口,0代表载具未载入
            Integer ng_port_volume = jsonParas.getInteger("ng_port_volume");
            Double ng_port_remaining_limit = jsonParas.getDouble("ng_port_remaining_limit");//剩余限高
            if (ng_port_remaining_limit == null)
                ng_port_remaining_limit = 1000.0;
            Integer ng_port_lotcount = jsonParas.getInteger("ng_port_lotcount");
            String ng_port_planid_list = jsonParas.getString("ng_port_planid_list");
            Integer ng_port_count = jsonParas.getInteger("ng_port_count");
            String ng_port_pallet_num = jsonParas.getString("ng_port_pallet_num");
            String ng_port_pallet_type = jsonParas.getString("ng_port_pallet_type");
            String loadUnLoadModel = jsonParas.getString("loadUnLoadModel");//侧出机状态：放板模式与收板模式切换(0放板模式,1收板模式)
            String CheckDownEquipStatusFlag = cFuncDbSqlResolve.GetParameterValue("CheckDownEquipStatusFlag");//校验下游设备状态标记
            String PnlCheckLengthStr = cFuncDbSqlResolve.GetParameterValue("PnlCheckLength");//pnl条码校验截取长度(-1标识不用截取，大于0标识需截取长度)
            if (PnlCheckLengthStr == null || PnlCheckLengthStr.equals(""))
                PnlCheckLengthStr = "-1";
            int PnlCheckLengthInt = Integer.parseInt(PnlCheckLengthStr);

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String group_id = "";
            String lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:上游NG,10其他异常
            Integer plc_ng_code = 0;//1-10代表OK,其他为NG，11陪镀板，
            Integer panel_index = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer finish_up_ng_count = 0;
            Double panel_tickness = 10.0;
            String panel_flag = "0";
            String lot_first_flag = "N";
            String lot_finish_flag = "N";
            String port_back_flag = "N";//生产端口是否退载具
            String next_port_flag = "N";//是否跳转到下一生产端口
            String next_port_back_flag = "N";//生产端口是否退载具
            String use_ng_port_flag = "N";//是否使用上游NG端口
            String ng_port_back_flag = "N";//NG工位是否退载具
            String plan_port_code = "";//计划选择端口号
            String plan_pallet_num = "";//计划载具条码
            String plan_pallet_type = "";//计划载具类型
            String flow_port_code = "";//过站信息端口号
            String flow_pallet_num = "";//过站载具条码
            String flow_pallet_type = "";//过站载具类型
            String nextstation = "";//批次的下工站代码
            String nextstationrecipe = "";//批次的下工站配方
            String port_sign = "";
            if (sys_model == 0) offline_flag = "Y";
            if (plan_id.equals("")) lot_first_flag = "Y";
            String first_flag = "N";
            String LastSubLotFlag = "N";
            //配方信息
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件,3：不停机首件（内外层）
            Integer inspect_count = 0;//首件数量
            Integer inspect_finish_count = 0;//首件完成数量
            String upmode = "";//收板模式，1：按批次收/2，3：按载具容量收（批次完整性为前提）/4：数量收/5：一换一载具
            String furnacebatch = "";//批次的炉号
            String SideExitBin = "";//侧出下机位区
            String SideExitMode = "";//侧出模式 0：不侧出，1：侧出
            String nextstationvehicaltype = "";//下工站载具类型,校验系统下发的与设备实际在用的载具是否一致
            String layerlot = "";//外层批次号
            JSONArray layerStructcureList = new JSONArray();//配套任务端口层别对应批次标识，配套机收板以此判断收板

            //1.判断是否为Dummy板
            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                queryBigDataDummy.addCriteria(Criteria.where("dummy_flag").is("Y"));
                long dummyCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //2.判断是否是托钢板、陪渡板（mes下发记录）
            if (panel_ng_code == 0 && panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                long dummyCount = mongoTemplate.getCollection(meDragRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //判断是否是首件板
//            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
//                Query queryBigDataInspect = new Query();
//                queryBigDataInspect.addCriteria(Criteria.where("station_id").is(station_id));
//                queryBigDataInspect.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
//                queryBigDataInspect.addCriteria(Criteria.where("inspect_flag").is("Y"));
//                long inspectCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataInspect.getQueryObject());
//                if (inspectCount > 0) {
//                    inspect_flag = "Y";
//                }
//            }
            //根据plc给出的标识判定是否是陪镀板
            if (up_ng_code == 2) {
                dummy_flag = "Y";
                panel_ng_code = 11;
            }

            String station_offline_flag = "N";//最后一个工位标识
            String sqlStation = "select COALESCE(offline_flag,'N') offline_flag from sys_fmod_station " +
                    "where station_id=" + station_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                    false, null, "");
            if (itemListStation != null && itemListStation.size() > 0) {
                station_offline_flag = itemListStation.get(0).get("offline_flag").toString();
            }

            //2.判断板件状态
            if (sys_model > 0 && dummy_flag.equals("N")) {
                if (lot_first_flag.equals("Y")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        if (pallet_type_check.equals("Y")) {
                            String pallet_type = mapLotInfo.get("pallet_type") == null ? "" : mapLotInfo.get("pallet_type").toString();
                            if (pallet_type.equals(port_pallet_type)) {
                                iteratorBigData.close();
                            } else if (pallet_type.equals(next_port_pallet_type)) {
                                if (next_port_index > 0) next_port_flag = "Y";
                                else panel_ng_code = 10;//无端口收板
                                iteratorBigData.close();
                            }
                        } else
                            iteratorBigData.close();
                    }
                } else {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                }
                if (mapLotInfo == null) {
                    panel_ng_code = 8;
                    if (up_ng_code == 1) {
                        panel_ng_code = 9;
                        if (ng_port_index > 0) {
                            use_ng_port_flag = "Y";
                            ng_port_count++;
                            if (ng_port_count >= ng_port_volume) {
                                ng_port_back_flag = "Y";
                            }
                            ng_port_remaining_limit = ng_port_remaining_limit - panel_tickness;
                            if (ng_port_remaining_limit < panel_tickness) {
                                ng_port_back_flag = "Y";
                            }
                        }
                    }
                } else {
                    group_id = mapLotInfo.get("group_id").toString();
                    plan_id = mapLotInfo.get("plan_id").toString();
                    lot_status = mapLotInfo.get("lot_status").toString();
                    target_lot_count = Integer.parseInt(mapLotInfo.get("target_lot_count").toString());
                    panel_tickness = Double.parseDouble(mapLotInfo.get("panel_tickness").toString());
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    String attribute3 = mapLotInfo.get("attribute3").toString();//上游NG板件数量
                    LastSubLotFlag = mapLotInfo.get("attribute2").toString();//放板机母批最后子批标识
                    if (attribute3 == null || attribute3.equals("")) attribute3 = "0";
                    finish_up_ng_count = Integer.parseInt(attribute3);
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    String item_info = mapLotInfo.get("item_info").toString();
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jsonObject = JSONObject.parseObject(item_info);
                        firstmode = jsonObject.getString("firstmode") == null ? "" : jsonObject.getString("firstmode");
                        upmode = jsonObject.getString("upmode") == null ? "" : jsonObject.getString("upmode");
                        nextstation = jsonObject.get("nextstation") == null ? "" : jsonObject.get("nextstation").toString();
                        nextstationrecipe = jsonObject.get("nextstationrecipe") == null ? "" : jsonObject.get("nextstationrecipe").toString();
                        SideExitBin = jsonObject.getString("SideExitBin");//侧出下机位区
                        SideExitMode = jsonObject.getString("SideExitMode");//侧出模式
                        if (SideExitMode == null) SideExitMode = "1";
                        if (SideExitBin == null) SideExitBin = station_code;
                        furnacebatch = jsonObject.getString("heatno");
                        nextstationvehicaltype = jsonObject.getString("nextstationvehicaltype");
                        if (nextstationvehicaltype == null) nextstationvehicaltype = "";
                        String heatnumberListStr = jsonObject.getString("heatnumberList");
                        if (heatnumberListStr != null) {
                            JSONArray heatnumberList = JSONArray.parseArray(heatnumberListStr);
                            if (heatnumberList != null && heatnumberList.size() > 0) {
                                JSONObject jbItem = heatnumberList.getJSONObject(0);
                                layerlot = jbItem.getString("layerlot");
                            }
                        }

                        String layerStructcureStr = jsonObject.getString("layerStructcure");
                        if (layerStructcureStr != null) {
                            layerStructcureList = JSONArray.parseArray(layerStructcureStr);
                        }
                    }
                    if (up_ng_code == 0) {
                        if (port_index <= 0) panel_ng_code = 10;//无端口收板
                        else {
                            if (lot_first_flag.equals("Y")) {
                                //一换一载具  其他什么条件都不再判断
                                if (upmode.equals("5")) {
                                    if (port_group_id != null && !port_group_id.equals("") && group_id != null && !group_id.equals("") && !group_id.equals(port_group_id)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                } else {
                                    //1.判断数量
                                    if ((port_count + target_lot_count) > port_volume) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //2.判断限高
                                    if (target_lot_count * panel_tickness > port_remaining_limit) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //3.判断批次的下工站代码
                                    if (port_nextstation != null && !port_nextstation.equals("") && nextstation != null && !nextstation.equals("") && !nextstation.equals(port_nextstation)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //4.判断批次的下工站配方
                                    if (port_nextstationrecipe != null && !port_nextstationrecipe.equals("") && nextstationrecipe != null && !nextstationrecipe.equals("") && !nextstationrecipe.equals(port_nextstationrecipe)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //5.判断批次的炉批次
                                    if (port_furnacebatch != null && !port_furnacebatch.equals("") && furnacebatch != null && !furnacebatch.equals("") && !furnacebatch.equals(port_furnacebatch)) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                    //6.判断下工站载具类型
                                    if (!isStackStation.equals("Y")) {
                                        if (SideExitMode.equals("1") && SideExitBin.equals(station_code)
                                                && !nextstationvehicaltype.equals(port_pallet_type)) {
                                            if (port_count > 0) port_back_flag = "Y";
                                            if (next_port_index > 0 && nextstationvehicaltype.equals(next_port_pallet_type))
                                                next_port_flag = "Y";
                                            else panel_ng_code = 10;//无端口收板
                                        }
                                    }
                                }
                            }
                            //2：下料首件，首件做完退载具
                            if (firstmode.equals("2")) {
                                inspect_flag = "Y";
                                panel_ng_code = 13; //首件模式，收板全部放在NG工位
                                target_lot_count = inspect_count;//任务数量=首件数量
                            }
                            if (panel_ng_code == 0) {
                                if (panel_model == 1) {//有Panel读码模式
                                    if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                        if (panel_barcode.equals("")) panel_ng_code = 7;
                                        else panel_ng_code = 2;
                                    } else {
                                        long okCount = 0;
                                        if (layerStructcureList != null && layerStructcureList.size() > 0) {
                                            String pnlflag = panel_barcode.substring(panel_barcode.length() - 2);
                                            int lay_index = finish_ok_count % layerStructcureList.size();
                                            String lotflag = layerStructcureList.getJSONObject(lay_index).get("lotflag").toString();
                                            if (lotflag.equals(pnlflag)) {
                                                okCount = 1l;
                                            }
                                        } else {
                                            String panel_barcode_check = panel_barcode;
                                            if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                                panel_barcode_check.substring(0, PnlCheckLengthInt);
                                            }
                                            Query queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                                    sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                            if (iteratorBigDataD.hasNext()) {
                                                okCount = 1l;
                                                Document docItem = iteratorBigDataD.next();
                                                panel_flag = docItem.getString("panel_attr");
                                                iteratorBigDataD.close();
                                            }
                                        }
                                        if (okCount <= 0) panel_ng_code = 12;
                                        else {
                                            String panel_barcode_check = panel_barcode;
                                            if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                                panel_barcode_check.substring(0, PnlCheckLengthInt);
                                            }
                                            Query queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (flowCount > 0) {
                                                panel_ng_code = 3;//重码
                                            }
                                        }
                                        if (panel_flag.equals("1")) {
                                            inspect_flag = "Y";
                                            if (station_offline_flag.equals("Y")) {
                                                panel_ng_code = 13; //首件也需要放到NG工位
                                            }
                                        }
                                    }
                                }
                                if (isStackStation.equals("Y")) {
                                    if (panel_tickness > port_remaining_limit) {
                                        if (port_count > 0) port_back_flag = "Y";
                                        if (next_port_index > 0) next_port_flag = "Y";
                                        else panel_ng_code = 10;//无端口收板
                                    }
                                }
                            }
                        }
                    } else {
                        if (up_ng_code == 1) {
                            finish_up_ng_count += 1;
                        }
                        panel_ng_code = 9;
                        if (ng_port_index > 0) {
                            use_ng_port_flag = "Y";
                            ng_port_count++;
                            if (ng_port_count >= ng_port_volume) {
                                ng_port_back_flag = "Y";
                            }

                            ng_port_remaining_limit = ng_port_remaining_limit - panel_tickness;
                            if (ng_port_remaining_limit < panel_tickness) {
                                ng_port_back_flag = "Y";
                            }
                        }
                    }

                    if (panel_ng_code == 0 && (firstmode.equals("1") || firstmode.equals("3")) && inspect_finish_count < inspect_count) {
                        inspect_flag = "Y";
                        inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                    }
                    //重码放行但不计数
                    if (panel_ng_code != 3)
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                    panel_index = finish_count;
                    //读码NG的直接放行，算到收板OK
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                    } else if (panel_ng_code != 3) {
                        finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                    }
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        if (next_port_flag.equals("N")) {
                            port_count++;
                            port_remaining_limit = port_remaining_limit - panel_tickness;
                        }
                    }
                    //判断当前作业端口是否退载具
                    if (finish_count >= target_lot_count) {
                        lot_finish_flag = "Y";
                        //上游NG时退载具
                        if (finish_up_ng_count > 0) {
                            if (port_index > 0) port_back_flag = "Y";
                        } else {
                            if (port_index > 0) {
                                //判断是否是堆叠工位，如果是堆叠工位，需要判断当前任务是否分了多个载具（根据限高判断），如果超过一个载具，直接退载具
                                if (isStackStation.equals("Y")) {
                                    String LimitHeight = cFuncDbSqlResolve.GetParameterValue("LimitHeight");
                                    if (LimitHeight.equals("")) LimitHeight = "20";
                                    Double LimitHeight_Double = Double.parseDouble(LimitHeight);//工位限高
                                    if (LimitHeight_Double <= panel_tickness * target_lot_count) {
                                        if (next_port_flag.equals("Y")) {
                                            next_port_back_flag = "Y";
                                        } else {
                                            port_back_flag = "Y";
                                        }
                                    }
                                }
                                //一载具换一载具，母批结束时候退载具，根据任务attribute2=Y判断是最后一个子批
                                if (upmode.equals("5")) {
                                    if (LastSubLotFlag != null && LastSubLotFlag.equals("Y")) {
                                        port_back_flag = "Y";
                                    }
                                } else {
                                    //一车一批
                                    if (upmode.equals("1")) {
                                        port_back_flag = "Y";
                                    } else {
                                        if (port_count >= port_volume) port_back_flag = "Y";
                                        else if (port_remaining_limit < panel_tickness) port_back_flag = "Y";
                                        else {
                                            //读取下一个任务
                                            Integer next_count = 0;
                                            Double next_panel_tickness = 10.0;
                                            queryBigData = new Query();
                                            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                                            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                                            queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                                            queryBigData.addCriteria(Criteria.where("plan_id").ne(plan_id));
                                            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                                            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                                                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                            if (iteratorBigData.hasNext()) {
                                                Document document = iteratorBigData.next();
                                                next_count = document.getInteger("target_lot_count");
                                                next_panel_tickness = Double.parseDouble(document.get("panel_tickness").toString());
                                                iteratorBigData.close();
                                            }
                                            if (port_count + next_count > port_volume) port_back_flag = "Y";
                                            if (next_count * next_panel_tickness > port_remaining_limit)
                                                port_back_flag = "Y";
                                        }
                                    }
                                }
                            }
                        }
                        //判断上游NG工位批次数量是否达到最大值
                        if (ng_port_index > 0 && ng_port_lotcount > 0) {
                            if (panel_ng_code == 9) {
                                if (ng_port_planid_list.equals("")) ng_port_planid_list = plan_id;
                                else {
                                    String[] tempList = ng_port_planid_list.split(",", -1);
                                    if (!Arrays.asList(tempList).contains(plan_id))
                                        ng_port_planid_list += "," + plan_id;
                                }
                            }
                            if (!ng_port_planid_list.equals("")) {
                                String[] tempList = ng_port_planid_list.split(",", -1);
                                if (tempList != null && tempList.length >= ng_port_lotcount) {
                                    ng_port_back_flag = "Y";
                                }
                            }
                        }
                    }
                    //判断是否存在特殊板件
                    if (panel_flag.equals("6")) {
                        if (port_index > 0) port_back_flag = "Y";
                    }
                }
            }

            //3.锁定选择端口以及返回给PLC代码
            if (dummy_flag.equals("Y")) {
                plc_ng_code = panel_ng_code;
            } else {
                if (sys_model > 0) {
                    plc_ng_code = panel_ng_code + 10;
                    plan_pallet_num = port_pallet_num;
                    plan_pallet_type = port_pallet_type;
                    flow_pallet_num = port_pallet_num;
                    flow_pallet_type = port_pallet_type;
                    //重码放行但不计数
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 3 || panel_ng_code == 7) {
                        if (next_port_flag.equals("Y")) {
                            plc_ng_code = next_port_index;
                            plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, next_port_index, plan_port_code, port_sign);
                            flow_port_code = plan_port_code;
                        } else {
                            if (port_index > 0) {
                                plc_ng_code = port_index;
                            }
                        }
                    }
                    if (mapLotInfo != null && next_port_flag.equals("N") && port_index > 0) {
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, port_index, plan_port_code, port_sign);
                        flow_port_code = plan_port_code;
                    }
                    if (use_ng_port_flag.equals("Y")) {
                        plc_ng_code = ng_port_index;
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, ng_port_index, flow_port_code, port_sign);
                        flow_pallet_num = ng_port_pallet_num;
                        flow_pallet_type = ng_port_pallet_type;
                    }
                } else {
                    plc_ng_code = 1;
                }
            }

            //判断是否直通，直通直接给plc反馈10；1.根据侧进侧出模式判断
            String straight_flag = "N";//直通标识
            if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 3 || panel_ng_code == 7) {
                if (CheckDownEquipStatusFlag.equals("Y")) {
                    if (SideExitMode.equals("0") && !loadUnLoadModel.equals("1") && station_offline_flag.equals("N")) {
                        //直通模式&下游非放板模式&非线尾工位  视为直通
                        port_index = 10;
                        plc_ng_code = 10;
                        straight_flag = "Y";
                    } else if (SideExitMode.equals("1")) {
                        if (!SideExitBin.equals(station_code) && !loadUnLoadModel.equals("1")) {
                            //侧出模式&当前工位不是侧出工位  视为直通
                            port_index = 10;
                            plc_ng_code = 10;
                            straight_flag = "Y";
                        }
                    }
                } else {
                    if (SideExitMode.equals("0") && station_offline_flag.equals("N")) {
                        //直通模式&下游非放板模式&非线尾工位  视为直通
                        port_index = 10;
                        plc_ng_code = 10;
                        straight_flag = "Y";
                    } else if (SideExitMode.equals("1")) {
                        if (!SideExitBin.equals(station_code)) {
                            //侧出模式&当前工位不是侧出工位  视为直通
                            port_index = 10;
                            plc_ng_code = 10;
                            straight_flag = "Y";
                        }
                    }

                }
            }

            //判断是否直通，直通直接给plc反馈10；2.根据首件模式判断
            if (firstmode.equals("3") && inspect_flag.equals("Y")) {
                //firstmode=3&inspect_flag=Y  视为直通
                port_index = 10;
                plc_ng_code = 10;
                straight_flag = "Y";
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : (panel_ng_code == 4 || panel_ng_code == 2 || panel_ng_code == 7) ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_id").toString());
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);
            mapBigDataRow.put("straight_flag", straight_flag);

            JSONObject jbResult = new JSONObject();
            String first_bind_flag = "N";
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                //更改
                Update updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                updateBigData.set("attribute3", finish_up_ng_count.toString());
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                String port_code = mapLotInfo.get("port_code").toString();
                if (port_code.equals("") && !plan_port_code.equals("")) {
                    first_flag = "Y";
                    first_bind_flag = "Y";
                    updateBigData.set("port_code", plan_port_code);
                    updateBigData.set("pallet_num", plan_pallet_num);
                    updateBigData.set("pallet_type", plan_pallet_type);
                    updateBigData.set("lot_status", "WORK");
                    updateBigData.set("group_lot_status", "WORK");
                }
                if (lot_finish_flag.equals("Y")) {
                    String task_start_time = mapLotInfo.get("task_start_time").toString();
                    if (task_start_time.equals("")) task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    long task_cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                    Integer task_error_code = 0;
                    if (target_lot_count == finish_ok_count) task_error_code = 1;
                    else if (target_lot_count > finish_ok_count) task_error_code = 2;
                    else if (target_lot_count < finish_ok_count) task_error_code = 3;
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("group_lot_status", "FINISH");
                    updateBigData.set("task_end_time", task_end_time);
                    updateBigData.set("task_cost_time", task_cost_time);
                    updateBigData.set("task_error_code", task_error_code);
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //组合返回数据
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            jbResult.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("plc_ng_code", plc_ng_code);
            jbResult.put("first_bind_flag", first_bind_flag);
            jbResult.put("lot_finish_flag", lot_finish_flag);
            jbResult.put("port_back_flag", port_back_flag);
            jbResult.put("next_port_flag", next_port_flag);
            jbResult.put("next_port_back_flag", next_port_back_flag);
            jbResult.put("use_ng_port_flag", use_ng_port_flag);
            jbResult.put("ng_port_back_flag", ng_port_back_flag);
            jbResult.put("port_count", port_count);
            jbResult.put("ng_port_count", ng_port_count);
            jbResult.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            jbResult.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            jbResult.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            jbResult.put("nextstation", nextstation);
            jbResult.put("nextstationrecipe", nextstationrecipe);
            jbResult.put("port_remaining_limit", port_remaining_limit);
            jbResult.put("ng_port_remaining_limit", ng_port_remaining_limit);
            jbResult.put("furnacebatch", furnacebatch);
            jbResult.put("port_index", port_index);
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("first_flag", first_flag);
            jbResult.put("SideExitMode", SideExitMode);//侧出模式 0：不侧出，1：侧出
            jbResult.put("loadUnLoadModel", loadUnLoadModel);
            jbResult.put("lastSubLotFlag", LastSubLotFlag);//母批最后子批标识
            jbResult.put("layerlot", layerlot);//外层批次号

            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.收板机Panel校验 内层vrs
    //1.收板机Panel校验
    //判定依据：暂存机标识、aoi结果、首件标识
    @RequestMapping(value = "/EapTlGhUnLoadPlanPanelCheckAndSave5", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhUnLoadPlanPanelCheckAndSave5(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/unload/EapTlGhUnLoadPlanPanelCheckAndSave5";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String meUpRecordTable = "a_eap_me_up_record";
        String mePanelQueueTable = "a_eap_me_panel_queue";
        String meDragRecordTable = "a_eap_me_drag_record";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            Integer up_ng_code = jsonParas.getInteger("up_ng_code");//上游OK/NG标识(0:OK,1:NG,2:陪镀板)
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            String zc_flag = jsonParas.getString("zc_flag");//当前板件暂存标识(0:正常件;1:暂存件;内层VRS)
            String zc_lot_num = jsonParas.getString("zc_lot_num");//暂存机板件lot_num
            //当前主生产端口信息
            String plan_id = jsonParas.getString("plan_id");
            Integer port_index = jsonParas.getInteger("port_index");
            String port_pallet_num = jsonParas.getString("port_pallet_num");
            String port_pallet_type = jsonParas.getString("port_pallet_type");
            String lot_num = "";
            String PnlCheckLengthStr = cFuncDbSqlResolve.GetParameterValue("PnlCheckLength");//pnl条码校验截取长度(-1标识不用截取，大于0标识需截取长度)
            if (PnlCheckLengthStr == null || PnlCheckLengthStr.equals(""))
                PnlCheckLengthStr = "-1";
            int PnlCheckLengthInt = Integer.parseInt(PnlCheckLengthStr);

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String group_id = "";
            String lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:上游NG,10其他异常
            Integer plc_ng_code = 0;//1-10代表OK,其他为NG，11陪镀板，
            Integer panel_index = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            String panel_flag = "0";
            String lot_first_flag = "N";
            String lot_finish_flag = "N";
            String plan_port_code = "";//计划选择端口号
            String plan_pallet_num = "";//计划载具条码
            String plan_pallet_type = "";//计划载具类型
            String flow_port_code = "";//过站信息端口号
            String flow_pallet_num = "";//过站载具条码
            String flow_pallet_type = "";//过站载具类型
            String port_sign = "";
            if (sys_model == 0) offline_flag = "Y";
            if (plan_id.equals("")) lot_first_flag = "Y";
            String first_flag = "N";
            //配方信息
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件,3：不停机首件（内外层）
            Integer inspect_count = 0;//首件数量
            Integer inspect_finish_count = 0;//首件完成数量

            //1.判断是否为Dummy板
            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                queryBigDataDummy.addCriteria(Criteria.where("dummy_flag").is("Y"));
                long dummyCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //2.判断是否是托钢板、陪渡板（mes下发记录）
            if (panel_ng_code == 0 && panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                long dummyCount = mongoTemplate.getCollection(meDragRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //根据plc给出的标识判定是否是陪镀板
            if (up_ng_code == 2) {
                dummy_flag = "Y";
                panel_ng_code = 9;
            }

            //2.判断板件状态
            if (sys_model > 0 && dummy_flag.equals("N")) {
                if (zc_flag.equals("1")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(zc_lot_num));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                } else {
                    if (lot_first_flag.equals("Y")) {
                        queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                        queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                        queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                        queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                        MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                                sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                        if (iteratorBigData.hasNext()) {
                            mapLotInfo = iteratorBigData.next();
                            iteratorBigData.close();
                        }
                    } else {
                        queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                        queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                        MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                                sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                        if (iteratorBigData.hasNext()) {
                            mapLotInfo = iteratorBigData.next();
                            iteratorBigData.close();
                        }
                    }
                }
                if (mapLotInfo == null) {
                    panel_ng_code = 8;
                    if (up_ng_code == 1) {
                        panel_ng_code = 9;
                    }
                } else {
                    lot_num = mapLotInfo.get("lot_num").toString();
                    group_id = mapLotInfo.get("group_id").toString();
                    plan_id = mapLotInfo.get("plan_id").toString();
                    lot_status = mapLotInfo.get("lot_status").toString();
                    target_lot_count = Integer.parseInt(mapLotInfo.get("target_lot_count").toString());
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    String item_info = mapLotInfo.get("item_info").toString();
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jsonObject = JSONObject.parseObject(item_info);
                        firstmode = jsonObject.getString("firstmode") == null ? "" : jsonObject.getString("firstmode");
                    }
                    if (up_ng_code == 0) {
                        if (port_index <= 0) panel_ng_code = 10;//无端口收板
                        else {
                            if (panel_ng_code == 0) {
                                if (panel_model == 1) {//有Panel读码模式
                                    if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                        if (panel_barcode.equals("")) panel_ng_code = 7;
                                        else panel_ng_code = 2;
                                    } else {
                                        long okCount = 0;
                                        String panel_barcode_check = panel_barcode;
                                        if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                            panel_barcode_check.substring(0, PnlCheckLengthInt);
                                        }
                                        Query queryBigDataD = new Query();
                                        queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                        queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                        MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                                sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                        if (iteratorBigDataD.hasNext()) {
                                            okCount = 1l;
                                            Document docItem = iteratorBigDataD.next();
                                            panel_flag = docItem.getString("panel_attr");
                                            iteratorBigDataD.close();
                                        }
                                        if (okCount <= 0) panel_ng_code = 12;
                                        else {
                                            queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (flowCount > 0) {
                                                panel_ng_code = 3;//重码
                                            }
                                            //判断AOI结果
                                            queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("station_id").is(station_id));
                                            queryBigDataD.addCriteria(Criteria.where("lot_num").is(lot_num));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                            queryBigDataD.addCriteria(Criteria.where("panel_status").is("OK"));
                                            long okPanelCount = mongoTemplate.getCollection(mePanelQueueTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (okPanelCount <= 0) {
                                                panel_ng_code = 9;//aoi校验失败
                                            }
                                        }
                                        if (panel_flag.equals("1")) {
                                            inspect_flag = "Y";
                                            panel_ng_code = 13; //首件也需要放到NG工位
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        panel_ng_code = 9;
                    }

                    if (panel_ng_code == 0 && (firstmode.equals("1") || firstmode.equals("3")) && inspect_finish_count < inspect_count) {
                        inspect_flag = "Y";
                        inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                    }
                    //非暂存机过来的板件修改任务信息
                    if (!zc_flag.equals("1")) {
                        //重码放行但不计数
                        if (panel_ng_code != 3)
                            finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                        panel_index = finish_count;
                        if (panel_ng_code == 0) {
                            finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                        } else if (panel_ng_code != 3) {
                            finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                        }
                        if (finish_count >= target_lot_count) {
                            lot_finish_flag = "Y";
                        }
                    }
                }
            }

            //3.锁定选择端口以及返回给PLC代码
            if (dummy_flag.equals("Y")) {
                plc_ng_code = panel_ng_code;
            } else {
                if (sys_model > 0) {
                    plc_ng_code = panel_ng_code + 10;
                    plan_pallet_num = port_pallet_num;
                    plan_pallet_type = port_pallet_type;
                    flow_pallet_num = port_pallet_num;
                    flow_pallet_type = port_pallet_type;
                    //重码放行但不计数
                    if (panel_ng_code == 0 || panel_ng_code == 3) {
                        if (port_index > 0) {
                            plc_ng_code = port_index;
                        }
                    }
                    if (mapLotInfo != null && port_index > 0) {
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, port_index, plan_port_code, port_sign);
                        flow_port_code = plan_port_code;
                    }
                } else {
                    plc_ng_code = 1;
                }
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : (panel_ng_code == 4 || panel_ng_code == 2 || panel_ng_code == 7) ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_id").toString());
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);
            mapBigDataRow.put("straight_flag", "");

            JSONObject jbResult = new JSONObject();
            String first_bind_flag = "N";
            //先更新状态[非Dummy模式]
            if (!zc_flag.equals("1")) {
                if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                    //更改
                    Update updateBigData = new Update();
                    updateBigData.set("inspect_finish_count", inspect_finish_count);
                    updateBigData.set("finish_count", finish_count);
                    updateBigData.set("finish_ok_count", finish_ok_count);
                    updateBigData.set("finish_ng_count", finish_ng_count);
                    if (finish_count == 1 && lot_status.equals("PLAN")) {
                        updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                    }
                    String port_code = mapLotInfo.get("port_code").toString();
                    if (port_code.equals("") && !plan_port_code.equals("")) {
                        first_flag = "Y";
                        first_bind_flag = "Y";
                        updateBigData.set("port_code", plan_port_code);
                        updateBigData.set("pallet_num", plan_pallet_num);
                        updateBigData.set("pallet_type", plan_pallet_type);
                        updateBigData.set("lot_status", "WORK");
                        updateBigData.set("group_lot_status", "WORK");
                    }
                    if (lot_finish_flag.equals("Y")) {
                        String task_start_time = mapLotInfo.get("task_start_time").toString();
                        if (task_start_time.equals("")) task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                        String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                        long task_cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                        Integer task_error_code = 0;
                        if (target_lot_count == finish_ok_count) task_error_code = 1;
                        else if (target_lot_count > finish_ok_count) task_error_code = 2;
                        else if (target_lot_count < finish_ok_count) task_error_code = 3;
                        updateBigData.set("lot_status", "FINISH");
                        updateBigData.set("group_lot_status", "FINISH");
                        updateBigData.set("task_end_time", task_end_time);
                        updateBigData.set("task_cost_time", task_cost_time);
                        updateBigData.set("task_error_code", task_error_code);
                    }
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }
            }

            //组合返回数据
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : lot_num);
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            jbResult.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("plc_ng_code", plc_ng_code);
            jbResult.put("first_bind_flag", first_bind_flag);
            jbResult.put("lot_finish_flag", lot_finish_flag);
            jbResult.put("port_back_flag", "");
            jbResult.put("next_port_flag", "");
            jbResult.put("next_port_back_flag", "");
            jbResult.put("use_ng_port_flag", "");
            jbResult.put("ng_port_back_flag", "");
            jbResult.put("port_count", 0);
            jbResult.put("ng_port_count", 0);
            jbResult.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            jbResult.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            jbResult.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            jbResult.put("nextstation", "");
            jbResult.put("nextstationrecipe", "");
            jbResult.put("port_remaining_limit", 1000);
            jbResult.put("ng_port_remaining_limit", 1000);
            jbResult.put("furnacebatch", "");
            jbResult.put("port_index", 10);
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("first_flag", first_flag);
            jbResult.put("SideExitMode", "");//侧出模式 0：不侧出，1：侧出
            jbResult.put("loadUnLoadModel", "");
            jbResult.put("lastSubLotFlag", "");//母批最后子批标识
            jbResult.put("layerlot", "");//外层批次号

            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //6.收板机Panel校验 暂存机/分流机
    //1.收板机Panel校验
    //判定依据：首件标识
    @RequestMapping(value = "/EapTlGhUnLoadPlanPanelCheckAndSave6", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhUnLoadPlanPanelCheckAndSave6(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/unload/EapTlGhUnLoadPlanPanelCheckAndSave6";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String meUpRecordTable = "a_eap_me_up_record";
        String meDragRecordTable = "a_eap_me_drag_record";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            Integer up_ng_code = 0;//上游OK/NG标识(0:OK,1:NG,2:陪镀板)
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            //当前主生产端口信息
            String plan_id = jsonParas.getString("plan_id");
            Integer port_index = jsonParas.getInteger("port_index");
            String port_pallet_num = "";
            String port_pallet_type = "";
            String lot_num = "";
            String PnlCheckLengthStr = cFuncDbSqlResolve.GetParameterValue("PnlCheckLength");//pnl条码校验截取长度(-1标识不用截取，大于0标识需截取长度)
            if (PnlCheckLengthStr == null || PnlCheckLengthStr.equals(""))
                PnlCheckLengthStr = "-1";
            int PnlCheckLengthInt = Integer.parseInt(PnlCheckLengthStr);

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String group_id = "";
            String lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:上游NG,10其他异常
            Integer plc_ng_code = 0;//1-10代表OK,其他为NG，11陪镀板，
            Integer panel_index = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            String panel_flag = "0";
            String lot_first_flag = "N";
            String lot_finish_flag = "N";
            String plan_port_code = "";//计划选择端口号
            String plan_pallet_num = "";//计划载具条码
            String plan_pallet_type = "";//计划载具类型
            String flow_port_code = "";//过站信息端口号
            String flow_pallet_num = "";//过站载具条码
            String flow_pallet_type = "";//过站载具类型
            String port_sign = "";
            if (sys_model == 0) offline_flag = "Y";
            if (plan_id.equals("")) lot_first_flag = "Y";
            String first_flag = "N";
            //配方信息
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件,3：不停机首件（内外层）
            Integer inspect_count = 0;//首件数量
            Integer inspect_finish_count = 0;//首件完成数量

            //1.判断是否为Dummy板
            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                queryBigDataDummy.addCriteria(Criteria.where("dummy_flag").is("Y"));
                long dummyCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //2.判断是否是托钢板、陪渡板（mes下发记录）
            if (panel_ng_code == 0 && panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                long dummyCount = mongoTemplate.getCollection(meDragRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //根据plc给出的标识判定是否是陪镀板
            if (up_ng_code == 2) {
                dummy_flag = "Y";
                panel_ng_code = 9;
            }

            //2.判断板件状态
            if (sys_model > 0 && dummy_flag.equals("N")) {
                if (lot_first_flag.equals("Y")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                } else {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                }
                if (mapLotInfo == null) {
                    panel_ng_code = 8;
                    if (up_ng_code == 1) {
                        panel_ng_code = 9;
                    }
                } else {
                    lot_num = mapLotInfo.get("lot_num").toString();
                    group_id = mapLotInfo.get("group_id").toString();
                    plan_id = mapLotInfo.get("plan_id").toString();
                    lot_status = mapLotInfo.get("lot_status").toString();
                    target_lot_count = Integer.parseInt(mapLotInfo.get("target_lot_count").toString());
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    String item_info = mapLotInfo.get("item_info").toString();
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jsonObject = JSONObject.parseObject(item_info);
                        firstmode = jsonObject.getString("firstmode") == null ? "" : jsonObject.getString("firstmode");
                    }
                    if (up_ng_code == 0) {
                        if (port_index <= 0) panel_ng_code = 10;//无端口收板
                        else {
                            if (panel_ng_code == 0) {
                                if (panel_model == 1) {//有Panel读码模式
                                    if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                        if (panel_barcode.equals("")) panel_ng_code = 7;
                                        else panel_ng_code = 2;
                                    } else {
                                        long okCount = 0;
                                        String panel_barcode_check = panel_barcode;
                                        if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                            panel_barcode_check.substring(0, PnlCheckLengthInt);
                                        }
                                        Query queryBigDataD = new Query();
                                        queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                        queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                        MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                                sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                        if (iteratorBigDataD.hasNext()) {
                                            okCount = 1l;
                                            Document docItem = iteratorBigDataD.next();
                                            panel_flag = docItem.getString("panel_attr");
                                            iteratorBigDataD.close();
                                        }
                                        if (okCount <= 0) panel_ng_code = 12;
                                        else {
                                            queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (flowCount > 0) {
                                                panel_ng_code = 3;//重码
                                            }
                                        }
                                        if (panel_flag.equals("1")) {
                                            inspect_flag = "Y";
                                            panel_ng_code = 13; //首件也需要放到NG工位
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        panel_ng_code = 9;
                    }

                    if (panel_ng_code == 0 && (firstmode.equals("1") || firstmode.equals("3")) && inspect_finish_count < inspect_count) {
                        inspect_flag = "Y";
                        inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                    }
                    //重码放行但不计数
                    if (panel_ng_code != 3)
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                    panel_index = finish_count;
                    if (panel_ng_code == 0) {
                        finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                    } else if (panel_ng_code != 3) {
                        finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                    }
                    if (finish_count >= target_lot_count) {
                        lot_finish_flag = "Y";
                    }
                }
            }

            //3.锁定选择端口以及返回给PLC代码
            if (dummy_flag.equals("Y")) {
                plc_ng_code = panel_ng_code;
            } else {
                if (sys_model > 0) {
                    plc_ng_code = panel_ng_code + 10;
                    plan_pallet_num = port_pallet_num;
                    plan_pallet_type = port_pallet_type;
                    flow_pallet_num = port_pallet_num;
                    flow_pallet_type = port_pallet_type;
                    //重码放行但不计数
                    if (panel_ng_code == 0 || panel_ng_code == 3) {
                        if (port_index > 0) {
                            plc_ng_code = port_index;
                        }
                    }
                    if (mapLotInfo != null && port_index > 0) {
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, port_index, plan_port_code, port_sign);
                        flow_port_code = plan_port_code;
                    }
                } else {
                    plc_ng_code = 1;
                }
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : (panel_ng_code == 4 || panel_ng_code == 2 || panel_ng_code == 7) ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_id").toString());
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);
            mapBigDataRow.put("straight_flag", "");

            JSONObject jbResult = new JSONObject();
            String first_bind_flag = "N";
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                //更改
                Update updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                    first_flag = "Y";
                    first_bind_flag = "Y";
                    updateBigData.set("port_code", plan_port_code);
                    updateBigData.set("pallet_num", plan_pallet_num);
                    updateBigData.set("pallet_type", plan_pallet_type);
                    updateBigData.set("lot_status", "WORK");
                    updateBigData.set("group_lot_status", "WORK");
                }
                String port_code = mapLotInfo.get("port_code").toString();
                if (lot_finish_flag.equals("Y")) {
                    String task_start_time = mapLotInfo.get("task_start_time").toString();
                    if (task_start_time.equals("")) task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    long task_cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                    Integer task_error_code = 0;
                    if (target_lot_count == finish_ok_count) task_error_code = 1;
                    else if (target_lot_count > finish_ok_count) task_error_code = 2;
                    else if (target_lot_count < finish_ok_count) task_error_code = 3;
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("group_lot_status", "FINISH");
                    updateBigData.set("task_end_time", task_end_time);
                    updateBigData.set("task_cost_time", task_cost_time);
                    updateBigData.set("task_error_code", task_error_code);
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //组合返回数据
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : lot_num);
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            jbResult.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("plc_ng_code", plc_ng_code);
            jbResult.put("first_bind_flag", first_bind_flag);
            jbResult.put("lot_finish_flag", lot_finish_flag);
            jbResult.put("port_back_flag", "");
            jbResult.put("next_port_flag", "");
            jbResult.put("next_port_back_flag", "");
            jbResult.put("use_ng_port_flag", "");
            jbResult.put("ng_port_back_flag", "");
            jbResult.put("port_count", 0);
            jbResult.put("ng_port_count", 0);
            jbResult.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            jbResult.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            jbResult.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            jbResult.put("nextstation", "");
            jbResult.put("nextstationrecipe", "");
            jbResult.put("port_remaining_limit", 1000);
            jbResult.put("ng_port_remaining_limit", 1000);
            jbResult.put("furnacebatch", "");
            jbResult.put("port_index", 10);
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("first_flag", first_flag);
            jbResult.put("SideExitMode", "");//侧出模式 0：不侧出，1：侧出
            jbResult.put("loadUnLoadModel", "");
            jbResult.put("lastSubLotFlag", "");//母批最后子批标识
            jbResult.put("layerlot", "");//外层批次号

            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //7.收板机Panel校验 翻面机
    //1.收板机Panel校验
    @RequestMapping(value = "/EapTlGhUnLoadPlanPanelCheckAndSave7", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhUnLoadPlanPanelCheckAndSave7(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/unload/EapTlGhUnLoadPlanPanelCheckAndSave6";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String meUpRecordTable = "a_eap_me_up_record";
        String meDragRecordTable = "a_eap_me_drag_record";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            Integer up_ng_code = 0;//上游OK/NG标识(0:OK,1:NG,2:陪镀板)
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            //当前主生产端口信息
            String plan_id = jsonParas.getString("plan_id");
            Integer port_index = jsonParas.getInteger("port_index");
            String port_pallet_num = "";
            String port_pallet_type = "";
            String lot_num = "";
            String PnlCheckLengthStr = cFuncDbSqlResolve.GetParameterValue("PnlCheckLength");//pnl条码校验截取长度(-1标识不用截取，大于0标识需截取长度)
            if (PnlCheckLengthStr == null || PnlCheckLengthStr.equals(""))
                PnlCheckLengthStr = "-1";
            int PnlCheckLengthInt = Integer.parseInt(PnlCheckLengthStr);

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String group_id = "";
            String lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:上游NG,10其他异常
            Integer plc_ng_code = 0;//1-10代表OK,其他为NG，11陪镀板，
            Integer panel_index = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            String panel_flag = "0";
            String lot_first_flag = "N";
            String lot_finish_flag = "N";
            String plan_port_code = "";//计划选择端口号
            String plan_pallet_num = "";//计划载具条码
            String plan_pallet_type = "";//计划载具类型
            String flow_port_code = "";//过站信息端口号
            String flow_pallet_num = "";//过站载具条码
            String flow_pallet_type = "";//过站载具类型
            String port_sign = "";
            if (sys_model == 0) offline_flag = "Y";
            if (plan_id.equals("")) lot_first_flag = "Y";
            String first_flag = "N";
            //配方信息
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件,3：不停机首件（内外层）
            Integer inspect_count = 0;//首件数量
            String flippingFlag = "0";//是否翻面：0不翻面、1翻面
            String locationlayer = "";//任务配方层别
            String FlipFirstModeTag = cFuncDbSqlResolve.GetParameterValue("FlipFirstModeTag");//翻面机是否判断首件
            Integer inspect_finish_count = 0;//首件完成数量
            String flippinglistStr = "";

            //1.判断是否为Dummy板
            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                queryBigDataDummy.addCriteria(Criteria.where("dummy_flag").is("Y"));
                long dummyCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //2.判断是否是托钢板、陪渡板（mes下发记录）
            if (panel_ng_code == 0 && panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                long dummyCount = mongoTemplate.getCollection(meDragRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //根据plc给出的标识判定是否是陪镀板
            if (up_ng_code == 2) {
                dummy_flag = "Y";
                panel_ng_code = 9;
            }

            //2.判断板件状态
            if (sys_model > 0 && dummy_flag.equals("N")) {
                if (lot_first_flag.equals("Y")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                } else {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                }
                if (mapLotInfo == null) {
                    panel_ng_code = 8;
                    if (up_ng_code == 1) {
                        panel_ng_code = 9;
                    }
                } else {
                    lot_num = mapLotInfo.get("lot_num").toString();
                    group_id = mapLotInfo.get("group_id").toString();
                    plan_id = mapLotInfo.get("plan_id").toString();
                    lot_status = mapLotInfo.get("lot_status").toString();
                    target_lot_count = Integer.parseInt(mapLotInfo.get("target_lot_count").toString());
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    String item_info = mapLotInfo.get("item_info").toString();
                    if (item_info != null && !item_info.equals("")) {
                        com.alibaba.fastjson2.JSONObject jsonObject = com.alibaba.fastjson2.JSONObject.parseObject(item_info);
                        firstmode = jsonObject.getString("firstmode") == null ? "" : jsonObject.getString("firstmode");
                        flippinglistStr = jsonObject.getString("flippinglist");
                    }
                    if (up_ng_code == 0) {
                        if (port_index <= 0) panel_ng_code = 10;//无端口收板
                        else {
                            if (panel_ng_code == 0) {
                                if (panel_model == 1) {//有Panel读码模式
                                    if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                        if (panel_barcode.equals("")) panel_ng_code = 7;
                                        else panel_ng_code = 2;
                                        //没有读到码时候需要查询配方是否翻面标记
                                        if (flippinglistStr != null && !flippinglistStr.equals("")) {
                                            com.alibaba.fastjson2.JSONArray flippinglist = com.alibaba.fastjson2.JSONArray.parseArray(flippinglistStr);
                                            if (flippinglist != null && flippinglist.size() > 0) {
                                                for (int i = 0; i < flippinglist.size(); i++) {
                                                    com.alibaba.fastjson2.JSONObject flipping = flippinglist.getJSONObject(i);
                                                    String eqpno = flipping.getString("eqpno");
                                                    if (eqpno != null && eqpno.equals(station_code)) {
                                                        flippingFlag = flipping.getString("flippingFlag");
                                                    }
                                                }
                                            }
                                        }
                                    } else {
                                        long okCount = 0;
                                        String panel_barcode_check = panel_barcode;
                                        if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                            panel_barcode_check.substring(0, PnlCheckLengthInt);
                                        }
                                        Query queryBigDataD = new Query();
                                        queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                        queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                        MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                                sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                        if (iteratorBigDataD.hasNext()) {
                                            okCount = 1l;
                                            Document docItem = iteratorBigDataD.next();
                                            panel_flag = docItem.getString("panel_attr");
                                            iteratorBigDataD.close();
                                        }
                                        if (okCount <= 0) panel_ng_code = 12;
                                        else {
                                            queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (flowCount > 0) {
                                                panel_ng_code = 3;//重码
                                            }
                                        }
                                        if (panel_flag.equals("1")) {
                                            inspect_flag = "Y";
                                            panel_ng_code = 13; //首件也需要放到NG工位
                                        }
                                        //读到码时候需要根据板件后两位层别与任务配方层别比较，对比一致不翻面，否则翻面
                                        if (flippinglistStr != null && !flippinglistStr.equals("")) {
                                            com.alibaba.fastjson2.JSONArray flippinglist = com.alibaba.fastjson2.JSONArray.parseArray(flippinglistStr);
                                            if (flippinglist != null && flippinglist.size() > 0) {
                                                for (int i = 0; i < flippinglist.size(); i++) {
                                                    com.alibaba.fastjson2.JSONObject flipping = flippinglist.getJSONObject(i);
                                                    String eqpno = flipping.getString("eqpno");
                                                    if (eqpno != null && eqpno.equals(station_code)) {
                                                        locationlayer = flipping.getString("flippingEnd");
                                                    }
                                                }
                                            }
                                        }
                                        if (locationlayer != null && !locationlayer.equals("")) {
                                            String pnlLayer = panel_barcode.substring(panel_barcode.length() - 2);
                                            if (pnlLayer.equals(locationlayer)) flippingFlag = "0";
                                            else flippingFlag = "1";
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        panel_ng_code = 9;
                    }

                    if (FlipFirstModeTag.equals("Y")) {
                        if (panel_ng_code == 0 && (firstmode.equals("1") || firstmode.equals("3")) && inspect_finish_count < inspect_count) {
                            inspect_flag = "Y";
                            inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                        }
                    }
                    //重码放行但不计数
                    if (panel_ng_code != 3)
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                    panel_index = finish_count;
                    if (panel_ng_code == 0) {
                        finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                    } else if (panel_ng_code != 3) {
                        finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                    }
                    if (finish_count >= target_lot_count) {
                        lot_finish_flag = "Y";
                    }
                }
            }

            //3.锁定选择端口以及返回给PLC代码
            if (dummy_flag.equals("Y")) {
                plc_ng_code = panel_ng_code;
            } else {
                if (sys_model > 0) {
                    plc_ng_code = panel_ng_code + 10;
                    plan_pallet_num = port_pallet_num;
                    plan_pallet_type = port_pallet_type;
                    flow_pallet_num = port_pallet_num;
                    flow_pallet_type = port_pallet_type;
                    //重码放行但不计数
                    if (panel_ng_code == 0 || panel_ng_code == 3) {
                        if (port_index > 0) {
                            plc_ng_code = port_index;
                        }
                    }
                    if (mapLotInfo != null && port_index > 0) {
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, port_index, plan_port_code, port_sign);
                        flow_port_code = plan_port_code;
                    }
                } else {
                    plc_ng_code = 1;
                }
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : (panel_ng_code == 4 || panel_ng_code == 2 || panel_ng_code == 7) ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_id").toString());
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);
            mapBigDataRow.put("straight_flag", "");

            com.alibaba.fastjson2.JSONObject jbResult = new com.alibaba.fastjson2.JSONObject();
            String first_bind_flag = "N";
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                //更改
                Update updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                    first_flag = "Y";
                    first_bind_flag = "Y";
                    updateBigData.set("port_code", plan_port_code);
                    updateBigData.set("pallet_num", plan_pallet_num);
                    updateBigData.set("pallet_type", plan_pallet_type);
                    updateBigData.set("lot_status", "WORK");
                    updateBigData.set("group_lot_status", "WORK");
                }
                String port_code = mapLotInfo.get("port_code").toString();
                if (lot_finish_flag.equals("Y")) {
                    String task_start_time = mapLotInfo.get("task_start_time").toString();
                    if (task_start_time.equals("")) task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    long task_cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                    Integer task_error_code = 0;
                    if (target_lot_count == finish_ok_count) task_error_code = 1;
                    else if (target_lot_count > finish_ok_count) task_error_code = 2;
                    else if (target_lot_count < finish_ok_count) task_error_code = 3;
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("group_lot_status", "FINISH");
                    updateBigData.set("task_end_time", task_end_time);
                    updateBigData.set("task_cost_time", task_cost_time);
                    updateBigData.set("task_error_code", task_error_code);
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //组合返回数据
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : lot_num);
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            jbResult.put("pallet_type", offline_flag.equals("Y") ? "" : flow_pallet_type);
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("plc_ng_code", plc_ng_code);
            jbResult.put("first_bind_flag", first_bind_flag);
            jbResult.put("lot_finish_flag", lot_finish_flag);
            jbResult.put("port_back_flag", "");
            jbResult.put("next_port_flag", "");
            jbResult.put("next_port_back_flag", "");
            jbResult.put("use_ng_port_flag", "");
            jbResult.put("ng_port_back_flag", "");
            jbResult.put("port_count", 0);
            jbResult.put("ng_port_count", 0);
            jbResult.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            jbResult.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            jbResult.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            jbResult.put("nextstation", "");
            jbResult.put("nextstationrecipe", "");
            jbResult.put("port_remaining_limit", 1000);
            jbResult.put("ng_port_remaining_limit", 1000);
            jbResult.put("furnacebatch", "");
            jbResult.put("port_index", 10);
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("first_flag", first_flag);
            jbResult.put("SideExitMode", "");//侧出模式 0：不侧出，1：侧出
            jbResult.put("loadUnLoadModel", "");
            jbResult.put("lastSubLotFlag", "");//母批最后子批标识
            jbResult.put("layerlot", "");//外层批次号
            jbResult.put("flippingFlag", flippingFlag);//0不翻面、1翻面

            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
