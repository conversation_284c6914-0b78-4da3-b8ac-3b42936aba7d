package com.api.eap.project.jxhb;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.project.thailand.guanghe.EapTlGhInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * (江西红板)EAP发送事件功能函数
 * 1.嫁动率上报
 * 2.生产参数实时值上报
 * 3.报警上传
 * 4.设备状态上报
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@Service
@Slf4j
public class EapJxHbSendEventFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private EapTlGhInterfCommon eapTlGhInterfCommon;
    @Autowired
    private EapJxHbSendEventSubFunc eapJxHbSendEventSubFunc;

    //1.嫁动率上报
    @Async
    public void ActivationReport(Long station_id,String station_code,String station_des,String station_num,
                                 String shift_name,String activation) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapJxHbSendEventSubFunc.ActivationReport(station_id,station_code,station_des,
                station_num,shift_name,activation);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //2.生产参数实时值上报
    @Async
    public void ProductParasReport(Long station_id,String station_code,String station_des,String station_num,
                                   String recipe_code,String panel_width,String panel_thick,
                                   String device_status,String total_output,String unit_output,
                                   String plan_count,String finish_count,
                                   String putboard_time,String putboard_signal,
                                   String device_mode) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapJxHbSendEventSubFunc.ProductParasReport(station_id,station_code,station_des,
                station_num,recipe_code,panel_width,panel_thick,
                device_status,total_output,unit_output,plan_count,finish_count,
                putboard_time,putboard_signal,device_mode);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //3.报警上传
    @Async
    public void AlarmReport(Long station_id,String station_code,String station_des,String station_num,
                            String alarm_code,String alarm_des,String alarm_value) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapJxHbSendEventSubFunc.AlarmReport(station_id,station_code,station_des,
                station_num,alarm_code,alarm_des,alarm_value);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //4.设备状态上报
    @Async
    public void DeviceStatusReport(Long station_id,String station_code,String station_des,String station_num,
                                   String device_status) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapJxHbSendEventSubFunc.DeviceStatusReport(station_id,station_code,station_des,
                station_num,device_status);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }
}
