package com.api.eap.core4.unload;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * AIS4.0 收板机生产计划处理逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2024-7-29
 */
@RestController
@Slf4j
@RequestMapping("/eap/core4/unload")
public class EapCore4UnLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //子批放板机完工同步到收板机
    @RequestMapping(value = "/EapCore4UnLoadSubLotLoadFinish", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4UnLoadSubLotLoadFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/unload/EapCore4UnLoadSubLotLoadFinish";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");//收板机工位
            String sqlStation = "select station_id " +
                    "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            JSONObject load_lot_list = jsonParas.getJSONObject("load_lot_list");
            //放扳机信息
            String group_lot_num = load_lot_list.getString("group_lot_num");
            String ori_plan_id = load_lot_list.getString("ori_plan_id");
            String lot_num = load_lot_list.getString("lot_num");
            Integer plan_lot_count = load_lot_list.getInteger("plan_lot_count");
            Integer finish_ok_count = load_lot_list.getInteger("finish_ok_count");
            String last_panel_barcode = load_lot_list.getString("last_panel_barcode");
            String last_panel_status = load_lot_list.getString("last_panel_status");
            String last_inspect_flag = load_lot_list.getString("last_inspect_flag");
            String last_dummy_flag = load_lot_list.getString("last_dummy_flag");

            //更新数据
            String[] lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(lot_status));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status));
            if (ori_plan_id == null || ori_plan_id.equals("")) {
                queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            } else {
                queryBigData.addCriteria(Criteria.where("ori_plan_id").is(ori_plan_id));
            }
            Update updateBigData = new Update();
            updateBigData.set("target_lot_count", finish_ok_count);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            log.info("放扳机完工更新收扳机子批数量：{}", load_lot_list);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "放板机完工同步到收板机异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //根据工单ID集合退载具更新状态
    @RequestMapping(value = "/EapCore4UnLoadFinishUpdByPlanIdList", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4UnLoadFinishUpdByPlanIdList(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/unload/EapCore4UnLoadFinishUpdByPlanIdList";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String port_code = jsonParas.getString("port_code");
            String ng_port_flag = jsonParas.getString("ng_port_flag");//ng工位标识，ng工位退载具的时候不需要结束任务
            if (ng_port_flag == null) ng_port_flag = "N";
            String plan_id_list = jsonParas.getString("plan_id_list");
            Integer task_error_code = jsonParas.getInteger("task_error_code");

            if (!ng_port_flag.equals("Y")) {
                if (plan_id_list != null && !plan_id_list.equals("")) {
                    String[] planIdList = plan_id_list.split(",", -1);
                    String[] lot_status = new String[]{"WAIT", "PLAN", "WORK"};
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("plan_id").in(planIdList));
                    queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status));
                    Update updateBigData = new Update();
                    updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                    updateBigData.set("task_error_code", task_error_code);
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("group_lot_status", "FINISH");
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "退载具更新状态异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //判断是否存在收板任务
    @RequestMapping(value = "/EapCore4UnLoadPlanExistJudge", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4UnLoadPlanExistJudge(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/unload/EapCore4UnLoadPlanExistJudge";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            long station_id = jsonParas.getLong("station_id");
            List<Map<String, Object>> itemList = new ArrayList<>();
            Map<String, Object> mapBigDataRow = null;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                mapBigDataRow = iteratorBigData.next();
                mapBigDataRow.remove("_id");
                mapBigDataRow.remove("item_date");
                mapBigDataRow.remove("item_date_val");
                mapBigDataRow.remove("station_id");
                mapBigDataRow.remove("group_lot_status");
                mapBigDataRow.remove("lot_status");
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据PlanID查询任务信息异常:" + ex;
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
