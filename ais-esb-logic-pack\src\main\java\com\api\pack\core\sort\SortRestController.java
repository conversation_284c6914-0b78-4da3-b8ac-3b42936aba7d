package com.api.pack.core.sort;

import com.alibaba.fastjson2.JSONObject;
import com.api.base.Const;
import com.api.base.IMybatisRestController;
import com.api.base.IResponseBody;
import com.api.pack.core.ccd.CCDBoardsMessage;
import com.api.pack.core.plan.PlanService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.bson.Document;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;


@RestController
@RequestMapping("/api/v1/sorts")
public class SortRestController extends IMybatisRestController<Sort, SortService, Long>
{
    private final PlanService planService;

    private final SortCompareService sortCompareService;

    private final SortSplitRuleService sortSplitRuleService;

    public SortRestController(SortService service, PlanService planService, SortCompareService sortCompareService, SortSplitRuleService sortSplitRuleService)
    {
        super(service);
        this.planService = planService;
        this.sortCompareService = sortCompareService;
        this.sortSplitRuleService = sortSplitRuleService;
    }

    @ApiOperation("功能（线扫分选比对）")
    @ApiResponses({
            @ApiResponse(code = 200, message = "比对完成"), // 200
            @ApiResponse(code = 401, message = "无访问权限"), // 401
            @ApiResponse(code = 403, message = "被禁止访问"), // 403
            @ApiResponse(code = 404, message = "不支持访问"), // 404
            @ApiResponse(code = 500, message = "服务器异常") // 500
    })
    @PostMapping("/{id}")
    public IResponseBody postAction(@PathVariable String id, @RequestParam(required = false) Map<String, String> params, @RequestBody String bodyData)
    {
        String action = params.getOrDefault(Const.KEY_ACTION, SortConst.ACTION_CCD_COMPARE);
        String lang = params.get(Const.KEY_LANG);
        if (!ObjectUtils.isEmpty(lang))
        {
            Locale.setDefault(Locale.forLanguageTag(lang));
        }
        IResponseBody result = IResponseBody.error("未知操作");
        switch (action)
        {
            case SortConst.ACTION_CCD_COMPARE:
                result = this.handleCCDCompare(JSONObject.parseObject(bodyData, SortCompareVO.class));
                break;
            case SortConst.ACTION_PILE_VERIFY:
                // TODO: handle pile verify
                break;
        }
        return result;
    }

    private IResponseBody handleCCDCompare(SortCompareVO vo)
    {
        //计时开始
        long l1 = System.currentTimeMillis();
        if (ObjectUtils.isEmpty(vo.getCcdData()))
        {
            return IResponseBody.error("CCD报文为空");
        }
        CCDBoardsMessage ccdData = CCDBoardsMessage.fromJSON(vo.getCcdData());
        if (ObjectUtils.isEmpty(ccdData))
        {
            return IResponseBody.error("CCD报文解析失败");
        }
        // 获取当前任务
        Document plan = this.planService.getCurrent();
        if (plan == null || plan.isEmpty())
        {
            return IResponseBody.error("当前未下发任务，无法执行线扫判断，请先选择任务进行下发");
        }
        // 获取当前任务的配方
        String recipeParams = plan.getString("recipe_paras");
        if (ObjectUtils.isEmpty(recipeParams))
        {
            return IResponseBody.error("当前任务未配置配方");
        }
        // 根据任务配方获取分选截取规则
        com.alibaba.fastjson.JSONObject recipe = com.alibaba.fastjson.JSONObject.parseObject(recipeParams);
        plan.put("recipe", recipe);
        List<SortSplitRule.Item> setSortRules = new LinkedList<>();
        List<SortSplitRule.Item> pcsSortRules = new LinkedList<>();
        Long sortSplitRuleId = recipe.getLong("sort_split_rule_id");
        if (sortSplitRuleId != null)
        {
            SortSplitRule splitRule = SortSplitRule.byId(sortSplitRuleId, this.sortSplitRuleService);
            if (splitRule != null && !ObjectUtils.isEmpty(splitRule.getItems()))
            {
                for (SortSplitRule.Item item : splitRule.getItems())
                {
                    if (SortConst.BOARD_CATEGORY_SET.equals(item.getBoardType()))
                    {
                        setSortRules.add(item);
                    }
                    else if (SortConst.BOARD_CATEGORY_PCS.equals(item.getBoardType()))
                    {
                        pcsSortRules.add(item);
                    }
                }
            }
        }
        // 获取可用且开启的分选规则
        com.alibaba.fastjson.JSONObject sortSwitch = this.service.getSortSwitch();
        SortCompareResult result = this.sortCompareService.compare(ccdData, plan, recipe, sortSwitch, setSortRules, pcsSortRules);
        //计时结束
        long l2 = System.currentTimeMillis();
        long costTime = l2 - l1;
        result.setCostTime(costTime);
        return IResponseBody.ok(result.toResultString(), Const.OK);
    }
}
