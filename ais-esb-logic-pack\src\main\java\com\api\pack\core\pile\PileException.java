package com.api.pack.core.pile;

import com.api.pack.core.board.BoardConst;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 包异常类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class PileException extends RuntimeException
{
    private String message;
    private String status;
    private int code;

    public PileException(String message)
    {
        this(message, BoardConst.NG, BoardConst.CODE_NG);
    }

    public PileException(String message, int code)
    {
        this(message);
        this.code = code;
    }

    public PileException(String message, String status, int code)
    {
        super(message);
        this.message = message;
        this.status = status;
        this.code = code;
    }
}
