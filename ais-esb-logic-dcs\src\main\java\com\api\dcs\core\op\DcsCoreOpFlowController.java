package com.api.dcs.core.op;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.wms.DcsStockCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 工位逻辑
 * 1.保存过站信息
 * 2.更改过站离开时间
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/op")
public class DcsCoreOpFlowController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsOpComomFunc dcsOpComomFunc;
    @Autowired
    private DcsStockCommonFunc dcsStockCommonFunc;

    //1.保存过站信息
    @RequestMapping(value = "/DcsCoreOpFlowSave", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreOpFlowSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/op/DcsCoreOpFlowSave";
        String transResult = "";
        String errorMsg = "";
        String station_code="";
        String task_num="";
        String meStationFlowTable="b_dcs_me_station_flow";
        try{
            String now_date_str= CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd");//获取当前日期,用于计算班次
            String create_date= CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(create_date);
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id=CFuncUtilsSystem.CreateUUID(true);

            station_code =jsonParas.getString("station_code");
            task_num=jsonParas.getString("task_num");
            String task_from=jsonParas.getString("task_from");
            String serial_num=jsonParas.getString("serial_num");
            String lot_num=jsonParas.getString("lot_num");
            String pallet_num=jsonParas.getString("pallet_num");
            String model_type=jsonParas.getString("model_type");
            String arrive_date= jsonParas.getString("arrive_date");//到达时间
            String leave_date=jsonParas.getString("leave_date");//离开时间
            Long cost_time=jsonParas.getLong("cost_time");//消耗时间
            String quality_sign=jsonParas.getString("quality_sign");//合格标志
            String pallet_use_count=jsonParas.getString("pallet_use_count");
            if(task_from==null) task_from="";
            if(serial_num==null) serial_num="";
            if(lot_num==null) lot_num="";
            if(pallet_num==null) pallet_num="";
            if(model_type==null) model_type="";
            if(leave_date==null) leave_date="";
            if(cost_time==null) cost_time=0L;
            if(quality_sign==null || quality_sign.equals("")) quality_sign="OK";
            if(pallet_use_count==null || pallet_use_count.equals("")) pallet_use_count="0";

            //1.根据工位号查询工位信息
            Map<String, Object> mapStation=dcsOpComomFunc.GetStationInfo(station_code);
            if(mapStation==null){
                errorMsg="未能根据工位号{"+station_code+"}查找工位基础数据";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String prod_line_id=mapStation.get("prod_line_id").toString();
            String station_des=mapStation.get("station_des").toString();
            String station_attr=mapStation.get("station_attr").toString();
            String online_flag=mapStation.get("online_flag").toString();
            String offline_flag=mapStation.get("offline_flag").toString();
            Integer beat_times=Integer.parseInt(mapStation.get("beat_times").toString());
            String staff_id="admin";
            String shif_code="";
            String shif_des="";
            String material_code="";
            String material_des="";
            String material_draw="";
            Double m_length=0D;
            Double m_width=0D;
            Double m_height=0D;
            Double m_weight=0D;
            String m_texture="";
            String cut_texture="";

            //2.查询当前工位登入者
            String sqlLogin="select COALESCE(user_code,'') user_code " +
                    "from sys_login " +
                    "where station_code='"+station_code+"' and out_flag='N' " +
                    "order by login_id desc LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListLogin=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlLogin,
                    false,request,apiRoutePath);
            if(itemListLogin!=null && itemListLogin.size()>0){
                staff_id=itemListLogin.get(0).get("user_code").toString();
            }

            //3.查询当前班次
            String sqlShift="select shift_code,shift_name,shift_start_time,shift_end_time " +
                    "from sys_fmod_shift " +
                    "where prod_line_id="+prod_line_id+" and enable_flag='Y' " +
                    "order by shift_order";
            List<Map<String, Object>> itemListShift=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlShift,
                    false,request,apiRoutePath);
            if(itemListShift!=null && itemListShift.size()>0){
                for(Map<String, Object> map : itemListShift){
                    String shift_start_time=map.get("shift_start_time").toString();
                    String shift_end_time=map.get("shift_end_time").toString();
                    String shift_start_date_str=now_date_str+" "+shift_start_time;
                    String shift_end_date_str=now_date_str+" "+shift_end_time;
                    Date shift_start_date=CFuncUtilsSystem.GetMongoISODate(shift_start_date_str);
                    Date shift_end_date=CFuncUtilsSystem.GetMongoISODate(shift_end_date_str);
                    if(shift_start_date.compareTo(shift_end_date)>0){//开始时间在结束时间之前,代表跨班次
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(shift_end_date);
                        cal.add(Calendar.DATE,1);
                        shift_end_date =cal.getTime();
                    }
                    if(item_date.compareTo(shift_start_date)>=0 && item_date.compareTo(shift_end_date)<=0){
                        shif_code=map.get("shift_code").toString();
                        shif_des=map.get("shift_name").toString();
                        break;
                    }
                }
            }

            //4.查询型号信息
            Map<String, Object> mapModel=dcsStockCommonFunc.GetModelInfo("",model_type);
            if(mapModel!=null){
                material_code=mapModel.get("material_code").toString();
                material_des=mapModel.get("material_des").toString();
                material_draw=mapModel.get("material_draw").toString();
                m_length=Double.parseDouble(mapModel.get("m_length").toString());
                m_width=Double.parseDouble(mapModel.get("m_width").toString());
                m_height=Double.parseDouble(mapModel.get("m_height").toString());
                m_weight=Double.parseDouble(mapModel.get("m_weight").toString());
                m_texture=mapModel.get("m_texture").toString();
                cut_texture=mapModel.get("cut_texture").toString();
            }

            //5.先禁用之前过站信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            Update updateBigData = new Update();
            updateBigData.set("enable_flag", "N");
            mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);

            //6.保存过站数据
            Map<String, Object> mapBigDataRow=new HashMap<>();
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("station_flow_id",station_flow_id);
            mapBigDataRow.put("station_code",station_code);
            mapBigDataRow.put("station_des",station_des);
            mapBigDataRow.put("task_num",task_num);
            mapBigDataRow.put("task_from",task_from);
            mapBigDataRow.put("serial_num",serial_num);
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("pallet_num",pallet_num);
            mapBigDataRow.put("material_code",material_code);
            mapBigDataRow.put("material_des",material_des);
            mapBigDataRow.put("material_draw",material_draw);
            mapBigDataRow.put("model_type",model_type);
            mapBigDataRow.put("m_length",m_length);
            mapBigDataRow.put("m_width",m_width);
            mapBigDataRow.put("m_height",m_height);
            mapBigDataRow.put("m_weight",m_weight);
            mapBigDataRow.put("m_texture",m_texture);
            mapBigDataRow.put("cut_texture",cut_texture);
            mapBigDataRow.put("pass_way","AUTO");
            mapBigDataRow.put("pass_by","");
            mapBigDataRow.put("pass_date","");
            mapBigDataRow.put("pass_remarks","");
            mapBigDataRow.put("arrive_date",arrive_date);
            mapBigDataRow.put("leave_date",leave_date);
            mapBigDataRow.put("cost_time",cost_time);
            mapBigDataRow.put("shif_code",shif_code);
            mapBigDataRow.put("shif_des",shif_des);
            mapBigDataRow.put("online_flag",online_flag);
            mapBigDataRow.put("offline_flag",offline_flag);
            mapBigDataRow.put("quality_sign",quality_sign);
            mapBigDataRow.put("bg_flag","N");
            mapBigDataRow.put("bg_check_msg","");
            mapBigDataRow.put("up_flag","N");
            mapBigDataRow.put("up_ng_code",0);
            mapBigDataRow.put("up_ng_msg","");
            mapBigDataRow.put("enable_flag","Y");
            mongoTemplate.insert(mapBigDataRow,meStationFlowTable);

            //6.工件到达:更新MIS实时状态表
            dcsOpComomFunc.MisArriveUpdate(staff_id,arrive_date,station_code,station_des,station_attr,
                    task_num,task_from,serial_num,model_type,lot_num,pallet_num,
                    pallet_use_count,beat_times,shif_code,shif_des,mapModel);

            transResult=CFuncUtilsLayUiResut.GetStandJson(true,null,station_flow_id,"",0);
        }
        catch (Exception ex) {
            errorMsg= "当前工位号{"+station_code+"},保存工件编号{"+task_num+"},过站信息发生异常"+ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.更改过站离开时间
    @RequestMapping(value = "/DcsCoreOpFlowUpdateLeave", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreOpFlowUpdateLeave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/op/DcsCoreOpFlowUpdateLeave";
        String transResult = "";
        String errorMsg = "";
        String station_code="";
        String station_flow_id_list="";
        String meStationFlowTable="b_dcs_me_station_flow";
        try{
            station_code = jsonParas.getString("station_code");//工位号
            station_flow_id_list= jsonParas.getString("station_flow_id_list");//过站ID集合
            String leave_date=jsonParas.getString("leave_date");//离开时间
            if(leave_date==null || leave_date.equals("")) leave_date=CFuncUtilsSystem.GetNowDateTime("");
            Long cost_time=jsonParas.getLong("cost_time");
            if(cost_time==null) cost_time=(long)0;
            String[] lstStationFlowId=station_flow_id_list.split(",",-1);
            if(lstStationFlowId==null || lstStationFlowId.length<=0 || station_flow_id_list==null || station_flow_id_list.equals("")){
                transResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
                return transResult;
            }
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").in(lstStationFlowId));
            Update updateBigData = new Update();
            updateBigData.set("leave_date", leave_date);
            updateBigData.set("cost_time", cost_time);
            mongoTemplate.updateMulti(queryBigData, updateBigData, meStationFlowTable);

            //更改MIS离开状态
            dcsOpComomFunc.MisLeaveUpdate(leave_date,station_code,cost_time);

            transResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex) {
            errorMsg= "当前工位号{"+station_code+"},更新过站ID集合{"+station_flow_id_list+"}离开时间异常"+ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
