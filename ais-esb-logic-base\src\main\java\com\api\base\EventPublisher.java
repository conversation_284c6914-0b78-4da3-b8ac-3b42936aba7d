package com.api.base;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

@Component
public class EventPublisher implements ApplicationEventPublisherAware
{
    private static ApplicationEventPublisher INSTANCE;

    @Override
    public void setApplicationEventPublisher(@NonNull ApplicationEventPublisher eventPublisher)
    {
        if (INSTANCE == null)
        {
            INSTANCE = eventPublisher;
        }
    }

    public static void publish(Object event)
    {
        INSTANCE.publishEvent(event);
    }
}
