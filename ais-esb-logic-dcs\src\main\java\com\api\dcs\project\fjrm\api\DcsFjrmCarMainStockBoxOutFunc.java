package com.api.dcs.project.fjrm.api;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.dcs.core.wms.DcsCarCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 空废料框出库路线创建
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
@Slf4j
public class DcsFjrmCarMainStockBoxOutFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsCarCommonFunc dcsCarCommonFunc;//天车
    @Autowired
    private DcsFjrmIntefTaskCommonFunc dcsFjrmIntefTaskCommonFunc;//主任务
    @Autowired
    private DcsFjrmStockCommonFunc dcsFjrmStockCommonFunc;//库位
    @Autowired
    private DcsFjrmWharfCommonFunc dcsFjrmWharfCommonFunc;//码头
    @Autowired
    private DcsFjrmWasteBoxCommonFunc dcsFjrmWasteBoxCommonFunc;//废料框

    //创建任务与路线规划
    public Map<String, Object> CreateTaskRoute(String userID, HttpServletRequest request, String apiRoutePath,
                                  String ware_house, String car_code) throws Exception{
        String errorMsg = "";
        String task_type="WASTE_BOX_OUT_TASK";// WASTE_BOX_OUT_TASK-空废料框出库
        Map<String, Object> mapResult=new HashMap<>();
        mapResult.put("passFlag",false);//是否允许越过此调度
        mapResult.put("errorMsg",errorMsg);//当前调度错误信息

        //1.判断天车是否存在
        Map<String, Object> mapCar= dcsCarCommonFunc.GetCarInfo(car_code);
        if(mapCar==null){
            throw new Exception("天车编码{"+car_code+"}不存在天车基础数据中");
        }
        //2.查询是否存在-空废料框出库-任务
        Map<String, Object> mapCarTask=dcsFjrmIntefTaskCommonFunc.GetIntefTaskInfoByTaskType(ware_house,task_type);
        if(mapCarTask==null){
            errorMsg="未找到任务类型为{"+task_type+"}的空废料框出库任务";
            mapResult.put("passFlag",true);
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String task_id=mapCarTask.get("task_id").toString();
        String task_num=mapCarTask.get("task_num").toString();
        String task_from=mapCarTask.get("task_from").toString();
        String task_way=mapCarTask.get("task_way").toString();
        //String from_stock_code=mapCarTask.get("from_stock_code").toString();
        //String to_stock_code=mapCarTask.get("to_stock_code").toString();
        //String lot_num=mapCarTask.get("lot_num").toString();
        //String material_code=mapCarTask.get("material_code").toString();
        //Float width=Float.valueOf(mapCarTask.get("width").toString());
        //Float error_min=Float.valueOf(mapCarTask.get("error_min").toString());
        //Float error_max=Float.valueOf(mapCarTask.get("error_max").toString());
        String wharf_code=mapCarTask.get("wharf_code").toString();
        //String waste_box_code=mapCarTask.get("waste_box_code").toString();

        //3.查找来源库位-空废料框位
        Map<String, Object> mapStockFrom= dcsFjrmStockCommonFunc.GetKflkStockOut(ware_house);
        if(mapStockFrom==null){
            errorMsg="任务类型为{"+task_type+"}的空废料框出库任务,未能找到可以出库的空废料框库位和库存";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String from_stock_code=mapStockFrom.get("stock_code").toString();
        Integer from_location_x=Integer.parseInt(mapStockFrom.get("location_x").toString());
        Integer from_location_y=Integer.parseInt(mapStockFrom.get("location_y").toString());
        Integer from_location_z=Integer.parseInt(mapStockFrom.get("location_z").toString());
        Integer from_stock_count=Integer.parseInt(mapStockFrom.get("stock_count").toString());
        String waste_box_code=mapStockFrom.get("waste_box_code").toString(); //废料框编码

        //4.查找废料框信息-废料框高度
        Map<String, Object> mapWasteBox= dcsFjrmWasteBoxCommonFunc.GetWasteBoxInfoByWasteBoxCode(waste_box_code);
        Float waste_box_height=Float.parseFloat(mapWasteBox.get("waste_box_height").toString());//高度

        //5.查找码头信息-目标位置
        Map<String, Object> mapWharf= dcsFjrmWharfCommonFunc.GetWharfInfoLock(ware_house,wharf_code,task_num);
        if(mapWharf==null){
            errorMsg="任务类型为{"+task_type+"}的空废料框出库任务,未能根据码头编码{"+wharf_code+"}查找到码头基础信息";
            mapResult.put("errorMsg",errorMsg);
            return mapResult;
        }
        String to_stock_code = wharf_code;//目标库位=码头
        Integer to_location_x=Integer.parseInt(mapWharf.get("location_x").toString());//X坐标
        Integer to_location_y=Integer.parseInt(mapWharf.get("location_y").toString());//Y坐标
        Integer to_location_z=Integer.parseInt(mapWharf.get("location_z").toString());//Z坐标
        to_location_z=to_location_z+waste_box_height.intValue();

        //6.插入调度任务：b_dcs_wms_lock_car_task PLAN
        String lot_num="";//批次号
        String serial_num="";//序列号(material_code)
        String model_type="";//型号(waste_box_code)
        Long car_task_id= dcsCarCommonFunc.LockCarTaskIns(userID,request,apiRoutePath,"N",car_code,ware_house,
                task_id,0L,task_num,task_from,task_way,task_type,serial_num,lot_num,
                model_type,0L,from_stock_code,to_stock_code);

        Integer para_f=0;//旋转角度(0度,180度)
        Integer para_r=1;//R任务类型(1叉取、2放置、3避让、4寻中、5抛丸)
        Integer para_a=3;//A位置代码(1上料位、2下料位、3库位、4抛丸位)
        Integer para_h=0;//半高标志位,1为半高位
        //6.1 插取路线：b_dcs_wms_lock_car_route
        Long car_route_id= dcsCarCommonFunc.LockCarRouteIns(userID,request,apiRoutePath,car_task_id,ware_house,car_code,
                "CHA_QU",1,from_location_x,from_location_y,from_location_z,
                0L,"","","N",
                "N","","",from_stock_count,
                "","","",
                para_f,para_r,para_a,para_h);

        para_r=2;//R任务类型(1叉取、2放置、3避让、4寻中、5抛丸)
        //6.2 放置路线
        car_route_id= dcsCarCommonFunc.LockCarRouteIns(userID,request,apiRoutePath,car_task_id,ware_house,car_code,
                "FANG_ZHI",2,to_location_x,to_location_y,to_location_z,
                0L,"","","N",
                "N","","",0,
                "","","",
                para_f,para_r,para_a,para_h);
        return mapResult;
    }

}
