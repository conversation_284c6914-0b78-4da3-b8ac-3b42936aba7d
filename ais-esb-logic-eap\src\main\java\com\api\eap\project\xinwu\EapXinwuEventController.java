package com.api.eap.project.xinwu;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.dy.EapDyInterfCommon;
import com.api.eap.project.dy.EapDySendFlowFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 新武event处理
 * 1.EapRecipeParasSel:解析配方信息
 * </p>
 *
 * <AUTHOR>
 * @since 2025-02-27
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/xinwu")
public class EapXinwuEventController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDySendFlowFunc eapDySendFlowFunc;

    //1.解析配方信息
    @RequestMapping(value = "/EapRecipeParasSel", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapRecipeParasSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/xinwu/EapRecipeParasSel";
        String selectResult = "";
        String errorMsg = "";
        try {
            //接受参数
            String dir_path = jsonParas.getString("dir_path");//文件夹目录
            List<Map<String, Object>> lstRecipeDocuments = new ArrayList<>();
            try {
                //1.获取目录下的所有文件（包括子目录中的文件）
                List<Path> cndFiles = Files.walk(Paths.get(dir_path))
                        .filter(Files::isRegularFile) // 确保是文件而非目录
                        .filter(path -> path.toString().endsWith(".cnd")) // 筛选出.cnd文件
                        .sorted((f1, f2) -> { // 按最后修改时间排序
                            try {
                                return Long.compare(Files.getLastModifiedTime(f2).toMillis(), Files.getLastModifiedTime(f1).toMillis());
                            } catch (IOException e) {
                                throw new RuntimeException(e);
                            }
                        })
                        .collect(Collectors.toList());

                //2.遍历并解析每个文件
                int fileCount = 0;
                for (Path file : cndFiles) {
                    fileCount++;
                    Map<String, Object> mapRecipeRow = new LinkedHashMap<>();
                    String fileName = file.getFileName().toString();
                    int lastDotIndex = fileName.lastIndexOf('.');
                    mapRecipeRow.put("model" + fileCount + "_recipe_code", fileName.substring(0, lastDotIndex));//配方code
                    String filePath = file.toAbsolutePath().toString();
                    BufferedReader br = new BufferedReader(new FileReader(filePath));
                    String line;
                    int lineCount = 0;
                    while ((line = br.readLine()) != null && lineCount < 4) {
                        String[] columns = line.split(",");
                        if (columns != null && columns.length >= 10) {
                            lineCount++;
                            String mbk = columns[2];//脈波寬
                            String fs = columns[4];//發數
                            String markno = columns[5];//mask no
                            String energy = columns[9];//能量
                            mapRecipeRow.put("model" + fileCount + "_mbk_line" + lineCount, mbk);//脈波寬
                            mapRecipeRow.put("model" + fileCount + "_fs_line" + lineCount, fs);//發數
                            mapRecipeRow.put("model" + fileCount + "_markno_line" + lineCount, markno);//mask no
                            mapRecipeRow.put("model" + fileCount + "_energy_line" + lineCount, energy);//能量
                        }
                    }
                    lstRecipeDocuments.add(mapRecipeRow);
                }
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, lstRecipeDocuments, "", "", 0);

            } catch (IOException e) {
                errorMsg = "解析下发cnd文件失败" + e.getMessage();
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }
        } catch (Exception ex) {
            errorMsg = "解析下发cnd文件失败" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
