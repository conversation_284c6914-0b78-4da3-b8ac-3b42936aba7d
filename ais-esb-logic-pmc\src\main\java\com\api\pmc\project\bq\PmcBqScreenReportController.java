package com.api.pmc.project.bq;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 * 北汽工位屏接口
 * 1.工位生产订单
 * 2.工位生产订单队列
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/project/bq")
public class PmcBqScreenReportController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;
    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private CFuncDbSqlMapper cFuncDbSqlMapper;

    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    private Cache e;

    //1.工位生产订单
    @RequestMapping(value = "/PmcBqStationScreenMakeOrder", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqStationScreenMakeOrder(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/project/bq/PmcBqStationScreenMakeOrder";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）

            List<Map<String, Object>> stationScreenList = new ArrayList<>();
            Map<String, Object> stationScreenItem = new HashMap<>();

            //1、计划数量(ORACLE 当天)、完成数量(AIS)
            stationScreenItem.put("plan_num", "5");//计划数量
            stationScreenItem.put("finish_num", "2");//完成数量
            //2、当前工位实时工件信息
            String make_order = "";
            String vin = "";
            String dms = "";
            String item_project = "";
            String small_model_type = "";
            String material_color = "";
            String material_size = "";
            String engine_num = "";
            String driver_way = "";
            String VEHICLE_BODY_SERIAL_NUMBER = "";
            String sqlStationStatus = "select COALESCE(make_order,'') make_order," +
                    "COALESCE(vin,'') vin," +
                    "COALESCE(dms,'') dms," +
                    "COALESCE(item_project,'') item_project," +
                    "COALESCE(small_model_type,'') small_model_type," +
                    "COALESCE(material_color,'') material_color," +
                    "COALESCE(material_size,'') material_size," +
                    "COALESCE(engine_num,'') engine_num," +
                    "COALESCE(driver_way,'') driver_way " +
                    "from d_pmc_me_station_status " +
                    "where station_status='1' " +
                    "and set_sign='NORMAL' " +
                    "and check_status='OK' " +
                    "and station_code='" + station_code + "' " +
                    "and work_center_code='" + work_center_code + "' " +
                    "limit 1 ";
            List<Map<String, Object>> itemListStationStatus = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationStatus, false, request, apiRoutePath);
            if (itemListStationStatus != null && itemListStationStatus.size() > 0) {
                make_order = itemListStationStatus.get(0).get("make_order").toString();
                vin = itemListStationStatus.get(0).get("vin").toString();
                dms = itemListStationStatus.get(0).get("dms").toString();
                item_project = itemListStationStatus.get(0).get("item_project").toString();
                small_model_type = itemListStationStatus.get(0).get("small_model_type").toString();
                material_color = itemListStationStatus.get(0).get("material_color").toString();
                material_size = itemListStationStatus.get(0).get("material_size").toString();
                engine_num = itemListStationStatus.get(0).get("engine_num").toString();
                driver_way = itemListStationStatus.get(0).get("driver_way").toString();

                JSONObject reqObj = new JSONObject();
                reqObj.put("make_order", make_order);
                JSONObject jsonObjectMoRes = cFuncUtilsRest.PostJbBackJb("http://10.140.4.15:9090/aisEsbOra/pmc/project/bq/PmcBqStationSerialNumber", reqObj);
                Integer moCode = jsonObjectMoRes.getInteger("code");
                String msg = jsonObjectMoRes.getString("msg");
                if (moCode != 0) {
                    errorMsg = "订单{" + make_order + "},获取上位MES车身流水号异常:" + msg;
                    selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return selectResult;
                }
                JSONArray oraArry = jsonObjectMoRes.getJSONArray("data");
                if (oraArry == null || oraArry.size() <= 0) {
                    errorMsg = "订单{" + make_order + "},获取上位MES车身流水号异常:" + msg;
                    selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return selectResult;
                }
                JSONObject oraJsonObject = oraArry.getJSONObject(0);
                VEHICLE_BODY_SERIAL_NUMBER = oraJsonObject.getString("VEHICLE_BODY_SERIAL_NUMBER");//车身流水号
            }
            //格式化返回值
            List<Map<String, Object>> makeOrderList = new ArrayList<>();
            Map<String, Object> makeOrderItem = new HashMap<>();
            makeOrderItem.put("make_order", make_order);
            makeOrderItem.put("vin", vin);
            makeOrderItem.put("dms", dms);
            makeOrderItem.put("small_model_type", small_model_type);
            makeOrderItem.put("material_color", material_color);
            makeOrderItem.put("material_size", material_size);
            makeOrderItem.put("engine_num", engine_num);
            makeOrderItem.put("driver_way", driver_way);
            //3、查询特征值
            String vpp_s = "";
            String bbl_s = "";
            String bbr_s = "";//驾驶室顶高度
            String bbf_s = "";//驾驶室地板高度
            String bok_s = "";
            String y26 = "";
            String bed_s = "";
            String bor_s = "";
            String mih_s = "";
            String y48 = "";
            String mwv_s = "";
            String mat_s = "";
            String mbk_s = "";
            String bdu_s = "";
            String y05 = "";
            String mps_s = "";
            String pfp_s = "";


            String pes_s = "";//发动机品牌
            String pep_s = "";//发动机功率
            String ptb_s = "";//变速箱品牌
            String ptr_s = "";//是否带液力缓速器
            String y19 = "";//油箱配置
            String y92 = "";//油箱配置
            String beb_s = "";//保险杠（大、小）
            String ccf_s = "";//鞍座配置
            String y29 = "";//轮胎型号
            String vfd_s = "";//驾驶员位置
            String zys = "";//颜色
            String bsm_s = "";//驾驶室悬置型式
            String mcp_s = "";//驻车空调
            String y35 = "";//车架
            String prr018 = "";//后桥速比
            String paf_s = "";//前桥载荷
            String par_s = "";//后桥载荷
            String csf_s = "";//前后板簧片数
            String csr_s = "";
            String cst_s = "";//前悬形式
            String vpr_s = "";//后悬架
            String czt_s = "";//转向器型号
            if (work_center_code.equals("HA")) {
                String sqlOnline = "select COALESCE(vpp_s,'') vpp_s, " +
                        "COALESCE(bbl_s,'') bbl_s, " +
                        "COALESCE(bbr_s,'') bbr_s, " +
                        "COALESCE(bbf_s,'') bbf_s, " +
                        "COALESCE(bok_s,'') bok_s, " +
                        "COALESCE(y26,'') y26, " +
                        "COALESCE(bed_s,'') bed_s, " +
                        "COALESCE(bor_s,'') bor_s " +
                        "from d_pmc_me_flow_online " +
                        "where enable_flag='Y' " +
                        "and work_center_code='" + work_center_code + "' " +
                        "and make_order='" + make_order + "' " +
                        "LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListOnline = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
                if (itemListOnline != null && itemListOnline.size() > 0) {
                    vpp_s = itemListOnline.get(0).get("vpp_s").toString();
                    bbl_s = itemListOnline.get(0).get("bbl_s").toString();
                    bbr_s = itemListOnline.get(0).get("bbr_s").toString();
                    bbf_s = itemListOnline.get(0).get("bbf_s").toString();
                    bok_s = itemListOnline.get(0).get("bok_s").toString();
                    y26 = itemListOnline.get(0).get("y26").toString();
                    bed_s = itemListOnline.get(0).get("bed_s").toString();
                    bor_s = itemListOnline.get(0).get("bor_s").toString();
                }
            } else if (work_center_code.equals("ZA")) {
                String feature = "";//特征
                String feature_value_desc = "";//特征描述
                String sqlFeature = "select COALESCE(feature,'') feature," +
                        "COALESCE(feature_value_desc,'') feature_value_desc " +
                        "from d_pmc_fmod_feature " +
                        "where dms='" + dms + "' " +
                        "and item_project='" + item_project + "' ";
                List<Map<String, Object>> itemListFeature = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlFeature, false, request, apiRoutePath);
                if (itemListFeature != null && itemListFeature.size() > 0) {
                    for (Map<String, Object> featureValue : itemListFeature) {
                        feature = featureValue.get("feature").toString();
                        feature_value_desc = featureValue.get("feature_value_desc").toString();
                        switch (feature) {
                            case "BBR_S":
                                bbr_s = feature_value_desc;//驾驶室顶高度
                                break;
                            case "BBF_S":
                                bbf_s = feature_value_desc;//驾驶室地板高度
                                break;
                            case "PES_S":
                                pes_s = feature_value_desc;//发动机品牌
                                break;
                            case "PEP_S":
                                pep_s = feature_value_desc;//发动机功率
                                break;
                            case "PTB_S":
                                ptb_s = feature_value_desc;//变速箱品牌
                                break;
                            case "PTR_S":
                                ptr_s = feature_value_desc;//是否带液力缓速器
                                break;
                            case "Y19":
                                y19 = feature_value_desc;//油箱配置
                                break;
                            case "Y92":
                                y92 = feature_value_desc;//油箱配置
                                break;
                            case "BEB_S":
                                beb_s = feature_value_desc;//保险杠（大、小）
                                break;
                            case "CCF_S":
                                ccf_s = feature_value_desc;//鞍座配置
                                break;
                            case "Y29":
                                y29 = feature_value_desc;//轮胎型号
                                break;
                            case "VFD_S":
                                vfd_s = feature_value_desc;//驾驶员位置
                                break;
                            case "ZYS":
                                zys = feature_value_desc;//颜色
                                break;
                            case "BSM_S":
                                bsm_s = feature_value_desc;//驾驶室悬置型式
                                break;
                            case "MCP_S":
                                mcp_s = feature_value_desc;//驻车空调
                                break;
                            case "Y35":
                                y35 = feature_value_desc;//车架
                                break;
                            case "PRR018":
                                prr018 = feature_value_desc;//后桥速比
                                break;
                            case "PAF_S":
                                paf_s = feature_value_desc;//前桥载荷
                                break;
                            case "PAR_S":
                                par_s = feature_value_desc;//后桥载荷
                                break;
                            case "CSF_S":
                                csf_s = feature_value_desc;//前后板簧片数
                                break;
                            case "CSR_S":
                                csr_s = feature_value_desc;
                                break;
                            case "CST_S":
                                cst_s = feature_value_desc;//前悬形式
                                break;
                            case "VPR_S":
                                vpr_s = feature_value_desc;//后悬架
                                break;
                            case "CZT_S":
                                czt_s = feature_value_desc;//转向器型号
                                break;

                            case "BOR_S":
                                bor_s = feature_value_desc;//天窗
                                break;
                            case "BOK_S":
                                bok_s = feature_value_desc;//工具箱
                                break;
                            case "BBL_S":
                                bbl_s = feature_value_desc;//驾驶室长度
                                break;
                            case "MIH_S":
                                mih_s = feature_value_desc;//独立热源
                                break;
                            case "Y48":
                                y48 = feature_value_desc;//逆变器
                                break;
                            case "MWV_S":
                                mwv_s = feature_value_desc;//全景环
                                break;
                            case "MAT_S":
                                mat_s = feature_value_desc;//ETC设备
                                break;
                            case "MBK_S":
                                mbk_s = feature_value_desc;//驻车制动
                                break;
                            case "BDU_S":
                                bdu_s = feature_value_desc;//卧铺
                                break;
                            case "Y05":
                                y05 = feature_value_desc;//副驾座椅
                                break;
                            case "MPS_S":
                                mps_s = feature_value_desc;//卧铺USB
                                break;
                            case "PFP_S":
                                pfp_s = feature_value_desc;//一键启动
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            makeOrderItem.put("vpp_s", vpp_s);
            makeOrderItem.put("bbl_s", bbl_s);
            makeOrderItem.put("bbr_s", bbr_s);
            makeOrderItem.put("bbf_s", bbf_s);
            makeOrderItem.put("bok_s", bok_s);
            makeOrderItem.put("y26", y26);
            makeOrderItem.put("bed_s", bed_s);
            makeOrderItem.put("bor_s", bor_s);

            makeOrderItem.put("pes_s", pes_s);
            makeOrderItem.put("pep_s", pep_s);
            makeOrderItem.put("ptb_s", ptb_s);
            makeOrderItem.put("ptr_s", ptr_s);
            makeOrderItem.put("y19", y19);
            makeOrderItem.put("y92", y92);
            makeOrderItem.put("beb_s", beb_s);
            makeOrderItem.put("ccf_s", ccf_s);
            makeOrderItem.put("y29", y29);
            makeOrderItem.put("vfd_s", vfd_s);
            makeOrderItem.put("zys", zys);
            makeOrderItem.put("bsm_s", bsm_s);
            makeOrderItem.put("mcp_s", mcp_s);
            makeOrderItem.put("y35", y35);
            makeOrderItem.put("prr018", prr018);
            makeOrderItem.put("paf_s", paf_s);
            makeOrderItem.put("par_s", par_s);
            makeOrderItem.put("csf_s", csf_s);
            makeOrderItem.put("csr_s", csr_s);
            makeOrderItem.put("cst_s", cst_s);
            makeOrderItem.put("vpr_s", vpr_s);
            makeOrderItem.put("czt_s", czt_s);

            makeOrderItem.put("mih_s", mih_s);
            makeOrderItem.put("y48", y48);
            makeOrderItem.put("mwv_s", mwv_s);
            makeOrderItem.put("mat_s", mat_s);
            makeOrderItem.put("mbk_s", mbk_s);
            makeOrderItem.put("bdu_s", bdu_s);
            makeOrderItem.put("y05", y05);
            makeOrderItem.put("mps_s", mps_s);
            makeOrderItem.put("pfp_s", pfp_s);
            makeOrderItem.put("VEHICLE_BODY_SERIAL_NUMBER", VEHICLE_BODY_SERIAL_NUMBER);
            makeOrderList.add(makeOrderItem);
            stationScreenItem.put("make_order_list", makeOrderList);//当前工位实时工件信息

            stationScreenList.add(stationScreenItem);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, stationScreenList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "工位生产订单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.工位生产订单队列
    @RequestMapping(value = "/PmcBqStationScreenQueue", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqStationScreenQueue(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/project/bq/PmcBqStationScreenQueue";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）

            List<Map<String, Object>> stationScreenList = new ArrayList<>();
            Map<String, Object> stationScreenItem = new HashMap<>();

            //工位生产订单队列 来自3个状态：已完成1条(过站)、进行中1条(当前工位实时工件信息)、未开始2条(工位生产订单)
            List<Map<String, Object>> queueList = new ArrayList<>();
            //(1)进行中1条(当前工位实时工件信息)
            String make_order = "";//当前工位生成订单
            String sqlStationStatus = "select '2' rownum," +
                    "COALESCE(make_order,'') make_order," +
                    "COALESCE(vin,'') vin," +
                    "COALESCE(dms,'') dms," +
                    "COALESCE(small_model_type,'') small_model_type," +
                    "COALESCE(material_color,'') material_color," +
                    "COALESCE(material_size,'') material_size," +
                    "COALESCE(engine_num,'') engine_num," +
                    "COALESCE(driver_way,'') driver_way," +
                    "'进行中' status " +
                    "from d_pmc_me_station_status " +
                    "where station_status='1' " +
                    "and set_sign='NORMAL' " +
                    "and check_status='OK' " +
                    "and station_code='" + station_code + "' " +
                    "and work_center_code='" + work_center_code + "' " +
                    "limit 1 ";
            List<Map<String, Object>> itemListStationStatus = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationStatus, false, request, apiRoutePath);
            if (itemListStationStatus != null && itemListStationStatus.size() > 0) {
                make_order = itemListStationStatus.get(0).get("make_order").toString();
            }
            //(2)已完成1条(过站)
            String sqlStationFlow = "select '1' rownum," +
                    "COALESCE(make_order,'') make_order," +
                    "COALESCE(vin,'') vin," +
                    "COALESCE(dms,'') dms," +
                    "COALESCE(small_model_type,'') small_model_type," +
                    "COALESCE(material_color,'') material_color," +
                    "COALESCE(material_size,'') material_size," +
                    "'' engine_num," +
                    "'' driver_way," +
                    "'已完成' status " +
                    "from d_pmc_me_station_flow " +
                    "where make_order <> '' " +
                    "and set_sign='NORMAL' " +
                    "and check_status='OK' " +
                    "and station_code='" + station_code + "' " +
                    "and work_center_code='" + work_center_code + "' ";
            if (make_order != null && !make_order.equals("")) {
                sqlStationFlow += " and make_order <> '" + make_order + "' ";
            }
            sqlStationFlow += " order by station_flow_id desc limit 1 ";
            List<Map<String, Object>> itemListStationFlow = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationFlow, false, request, apiRoutePath);
            if (itemListStationFlow != null && itemListStationFlow.size() > 0) {
                queueList.add(itemListStationFlow.get(0));
            }
            if (itemListStationStatus != null && itemListStationStatus.size() > 0) {
                queueList.add(itemListStationStatus.get(0));
            }
            //(3)未开始2条(工位生产订单)
//            String sqlStationMo = "select COALESCE(a.mo_work_order,0) rownum," +
//                    "COALESCE(a.make_order,'') make_order," +
//                    "COALESCE(b.vin,'') vin," +
//                    "COALESCE(b.dms,'') dms," +
//                    "COALESCE(b.small_model_type,'') small_model_type," +
//                    "COALESCE(b.material_color,'') material_color," +
//                    "COALESCE(b.material_size,'') material_size," +
//                    "COALESCE(b.engine_num,'') engine_num," +
//                    "COALESCE(b.driver_way,'') driver_way," +
//                    "'未开始' status " +
//                    "from d_pmc_me_station_mo a,d_pmc_me_flow_online b " +
//                    "where b.make_order=a.make_order " +
//                    "and a.work_status='PLAN' " +
//                    "and a.set_sign='NONE' " +
//                    "and a.station_code='" + station_code + "' ";
//            if (make_order != null && !make_order.equals("")) {
//                sqlStationMo += " and a.make_order <> '" + make_order + "' ";
//            }
//            sqlStationMo += " order by a.mo_work_order limit 2 ";

            //(3)未开始2条(工位生产订单)
            String sqlStationMo = "SELECT '3'rownum,COALESCE(make_order,'')make_order,COALESCE(vin,'')vin," +
                    "COALESCE(dms,'')dms,COALESCE(small_model_type,'')small_model_type,COALESCE(material_color,'')material_color," +
                    "COALESCE(material_size,'')material_size,COALESCE(engine_num,'')engine_num,COALESCE(driver_way,'')driver_way," +
                    "'未开始'status FROM d_pmc_me_flow_online ";
            if (make_order != null && !make_order.equals("")) {
                sqlStationMo += " WHERE flow_online_id>(select flow_online_id from d_pmc_me_flow_online where make_order='" + make_order + "') ";
            }
            sqlStationMo += " ORDER BY flow_online_id LIMIT 2 ";
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, request, apiRoutePath);
            if (itemListStationMo != null && itemListStationMo.size() > 0) {
                for (Map<String, Object> stationMo : itemListStationMo) {
                    queueList.add(stationMo);
                }
            }
            //(4) 根据排序设置序号为1，2，3，4
            int i = 1;
            for (Map<String, Object> item : queueList) {
                item.put("rownum", i);
                i++;
            }
            stationScreenItem.put("station_mo_list", queueList);//工位生产订单队列

            stationScreenList.add(stationScreenItem);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, stationScreenList, "", "", queueList.size());
        } catch (Exception ex) {
            errorMsg = "工位生产订单队列异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //3.工位紧急事件
    @RequestMapping(value = "/PmcBqStationScreenEmergency", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqStationScreenEmergency(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/project/bq/PmcBqStationScreenEmergency";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）

            List<Map<String, Object>> stationScreenList = new ArrayList<>();
            Map<String, Object> stationScreenItem = new HashMap<>();
            //紧急事件(安灯事件)
            String sqlAndonEvent = "select COALESCE(work_center_code,'') work_center_code," +
                    "COALESCE(prod_line_code,'') prod_line_code," +
                    "COALESCE(station_code,'') station_code," +
                    "COALESCE(station_des,'') station_des," +
                    "COALESCE(andon_type,'') andon_type," +
                    "COALESCE(andon_des,'') andon_des," +
                    "COALESCE(andon_limit_time,0) andon_limit_time," +
                    "COALESCE(happen_date,'') happen_date," +
                    "COALESCE(reset_date,'') reset_date," +
                    "COALESCE(cost_time,0) cost_time," +
                    "COALESCE(over_time_flag,'N') over_time_flag " +
                    "from d_pmc_me_andon_event " +
                    "where enable_flag='Y' " +
                    "and reset_flag='N' " +
                    "and work_center_code='" + work_center_code + "' ";
            if (station_code != null && !station_code.equals("")) {
                sqlAndonEvent += "and station_code='" + station_code + "' ";
            }
            List<Map<String, Object>> itemListAndonEvent = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlAndonEvent, false, request, apiRoutePath);
            stationScreenItem.put("andon_list", itemListAndonEvent);//紧急事件

            stationScreenList.add(stationScreenItem);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, stationScreenList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "工位紧急事件异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //4.工位工艺质量
    @RequestMapping(value = "/PmcBqStationScreenQuality", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqStationScreenQuality(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/project/bq/PmcBqStationScreenQuality";
        String selectResult = "";
        String errorMsg = "";
        String methodJz = "/aisEsbOra/pmc/core/interf/PmcCoreMesOilFillingSel";//加注
        String methodNj = "/aisEsbOra/pmc/core/interf/PmcCoreMesBasicTorqueSel";//拧紧
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String make_order = jsonParas.getString("make_order");//工位
            String vin = jsonParas.getString("vin");//工位

            List<Map<String, Object>> stationScreenList = new ArrayList<>();
            Map<String, Object> stationScreenItem = new HashMap<>();
            //1、加注工艺质量
            List<Map<String, Object>> jzQualityList = new ArrayList<>();
            String device_code_jz = "";//设备编号(加注)
            String shijijzl = "0";//实际加注量
            String panding = "0";//加注结果判定(0未判定1合格2不合格)
            String sqlStationDeviceJz = "select COALESCE(device_code,'') device_code " +
                    "from d_pmc_fmod_station_device " +
                    "where enable_flag='Y' " +
                    "and station_device_type='JZ' " +
                    "and station_code='" + station_code + "' " +
                    "and work_center_code='" + work_center_code + "' ";
            List<Map<String, Object>> itemListStationDeviceJz = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationDeviceJz, false, request, apiRoutePath);
            if (itemListStationDeviceJz != null && itemListStationDeviceJz.size() > 0) {
                for (Map<String, Object> jzStationDevice : itemListStationDeviceJz) {
                    device_code_jz += "'" + jzStationDevice.get("device_code").toString() + "',";
                }
                device_code_jz = device_code_jz.substring(0, device_code_jz.length() - 1);
                //(1)查询ORACLE数据
                if (PmcCoreServer.EsbUrl == null ||
                        PmcCoreServer.EsbUrl.isEmpty()) {
                    pmcCoreServerInit.ServerInit();
                }
                JSONObject jsonObjectJzReq = new JSONObject();
                jsonObjectJzReq.put("order_prod", make_order);
                jsonObjectJzReq.put("device_codes", device_code_jz);
                JSONObject jsonObjectJzRes = cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl + methodJz, jsonObjectJzReq);
                Integer moCode = jsonObjectJzRes.getInteger("code");
                if (moCode == 0) {
                    JSONArray oraArryJz = jsonObjectJzRes.getJSONArray("data");
                    if (oraArryJz != null && oraArryJz.size() > 0) {
                        for (int i = 0; i < oraArryJz.size(); i++) {
                            JSONObject jzObject = oraArryJz.getJSONObject(i);
                            Map<String, Object> jzQualityDataItem = new HashMap<>();
                            jzQualityDataItem.put("quality_des", jzObject.getString("PART_NAME1"));//描述(零件名称1)
                            jzQualityDataItem.put("quality_rule", jzObject.getString("FILLING_QUANTITY"));//要求(加注量)
                            //(2)查询实际加注数据
                            Query query = new Query();
                            query.addCriteria(Criteria.where("vin").regex("^.*" + vin + ".*$"));
                            query.addCriteria(Criteria.where("shebeibh").regex("^.*" + jzObject.getString("FACILITY_CODE") + ".*$"));
                            List<Document> documentList = mongoTemplate.find(query, Document.class, "d_pmc_me_station_quality_fill");
                            if (documentList != null && documentList.size() > 0) {
                                Document docItem = documentList.get(0);
                                shijijzl = docItem.getString("shijijzl");
                                panding = docItem.getString("panding");
                            }
                            jzQualityDataItem.put("quality_actual", shijijzl);//实际值
                            jzQualityDataItem.put("judge_flag", panding);//合格标志(0未判定 1合格 2不合格)
                            jzQualityList.add(jzQualityDataItem);
                        }
                    }
                }
            }
            stationScreenItem.put("jz_quality_list", jzQualityList);
            //(2)拧紧工艺质量
            List<Map<String, Object>> njQualityList = new ArrayList<>();
            String device_code_nj = "";//设备编号(加注)
            String torque = "0";//扭矩实际值
            String tightening_status = "NG";//加注结果判定(OK合格，NG不合格)
            String sqlStationDeviceNj = "select COALESCE(device_code,'') device_code " +
                    "from d_pmc_fmod_station_device " +
                    "where enable_flag='Y' " +
                    "and station_device_type='NJ' " +
                    "and station_code='" + station_code + "' " +
                    "and work_center_code='" + work_center_code + "' ";
            List<Map<String, Object>> itemListStationDeviceNj = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationDeviceNj, false, request, apiRoutePath);
            if (itemListStationDeviceNj != null && itemListStationDeviceNj.size() > 0) {
                for (Map<String, Object> njStationDevice : itemListStationDeviceNj) {
                    device_code_nj += "'" + njStationDevice.get("device_code").toString() + "',";
                }
                device_code_nj = device_code_nj.substring(0, device_code_nj.length() - 1);
                //(1)查询ORACLE数据
                if (PmcCoreServer.EsbUrl == null ||
                        PmcCoreServer.EsbUrl.isEmpty()) {
                    pmcCoreServerInit.ServerInit();
                }
                JSONObject jsonObjectNjReq = new JSONObject();
                jsonObjectNjReq.put("order_prod", make_order);
                jsonObjectNjReq.put("device_codes", device_code_nj);
                JSONObject jsonObjectNjRes = cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl + methodNj, jsonObjectNjReq);
                Integer moCode = jsonObjectNjRes.getInteger("code");
                if (moCode == 0) {
                    JSONArray oraArryNj = jsonObjectNjRes.getJSONArray("data");
                    if (oraArryNj != null && oraArryNj.size() > 0) {
                        for (int i = 0; i < oraArryNj.size(); i++) {
                            JSONObject njObject = oraArryNj.getJSONObject(i);
                            //(2)查询实际拧紧数据
                            Query query = new Query();
                            query.addCriteria(Criteria.where("vin").regex("^.*" + vin + ".*$"));
                            query.addCriteria(Criteria.where("device_code").regex("^.*" + njObject.getString("FACILITY_CODE") + ".*$"));
                            List<Document> documentList = mongoTemplate.find(query, Document.class, "d_pmc_me_station_quality_shaft");
                            if (documentList != null && documentList.size() > 0) {
                                for (int j = 0; j < documentList.size(); j++) {
                                    Document docItem = documentList.get(j);
                                    Map<String, Object> njQualityDataItem = new HashMap<>();
                                    njQualityDataItem.put("quality_des", njObject.getString("PART_CODE"));//描述(TORQUE_MAX)
                                    njQualityDataItem.put("quality_rule", njObject.getString("TORQUE_MIN") + "-" +
                                            njObject.getString("TORQUE_MAX"));//要求(力矩MIN值-力矩MAX值)
                                    torque = docItem.getString("torque_value");
                                    tightening_status = docItem.getString("shaft_status");
                                    njQualityDataItem.put("quality_actual", torque);//实际值
                                    njQualityDataItem.put("judge_flag", tightening_status);//合格标志(0未判定 1合格 2不合格)
                                    njQualityDataItem.put("item_date_val", docItem.getLong("item_date_val"));//时间
                                    njQualityList.add(njQualityDataItem);
                                }
                            }
                        }
                    }
                }
            }
            List<Map<String, Object>> list = njQualityList.stream().sorted((e1, e2) -> {
                // 升序
                // return Long.compare((Long) e1.get("item_date_val"), (Long) e2.get("item_date_val"));
                // 降序
                return -Long.compare((Long) e1.get("item_date_val"), (Long) e2.get("item_date_val"));
            }).collect(Collectors.toList());
            if (list != null && list.size() > 7) {
                list = list.subList(0, 7);
            }

            stationScreenItem.put("nj_quality_list", list);
            //(3)涂胶工艺质量
            List<Map<String, Object>> tjQualityList = new ArrayList<>();
            /*
            Map<String, Object> tjQualityDataItem=new HashMap<>();
            tjQualityDataItem.put("quality_des","7");//描述
            tjQualityDataItem.put("quality_rule","8");//要求
            tjQualityDataItem.put("quality_actual","9");//实际值
            jzQualityDataItem.put("judge_flag","1");//合格标志(0未判定 1合格 2不合格)
            tjQualityList.add(tjQualityDataItem);*/
            stationScreenItem.put("tj_quality_list", tjQualityList);

            stationScreenList.add(stationScreenItem);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, stationScreenList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "工位工艺质量异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //----------------------------------------------------------------------------------
    //5.当前工位实时工件信息
    @RequestMapping(value = "/PmcBqStationScreenStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqStationScreenStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/project/bq/PmcBqStationScreenStatus";
        String selectResult = "";
        String errorMsg = "";
        try {
            String prod_line_code = jsonParas.getString("prod_line_code");//生产线编码
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String prod_line_type = jsonParas.getString("prod_line_type");//产线(工位分段，例如：NS1、NS2)

            List<Map<String, Object>> stationScreenList = new ArrayList<>();
            Map<String, Object> stationScreenItem = new HashMap<>();
            //1、获取产线
            String prod_line_des = "";//产线描述
            String sqlProdLine = "select COALESCE(prod_line_des,'') prod_line_des " +
                    "from sys_fmod_prod_line " +
                    "where enable_flag='Y' " +
                    "and work_center_code='" + work_center_code + "' ";
            if (prod_line_code != null && !prod_line_code.equals("")) {
                sqlProdLine += "and prod_line_code='" + prod_line_code + "' ";
            }
            List<Map<String, Object>> itemListProdLine = cFuncDbSqlExecute.ExecSelectSql(prod_line_code, sqlProdLine, false, request, apiRoutePath);
            if (itemListProdLine != null && itemListProdLine.size() > 0) {
                prod_line_des = itemListProdLine.get(0).get("prod_line_des").toString();
            }
            if (prod_line_type != null && prod_line_type.equals("NS2")) {
                stationScreenItem.put("prod_line_des", "内饰2线");//产线描述
            } else if (prod_line_type != null && prod_line_type.equals("NS1")) {
                stationScreenItem.put("prod_line_des", "内饰1线");//产线描述
            } else {
                stationScreenItem.put("prod_line_des", prod_line_des);//产线描述
            }
            //2、当前工位实时工件信息
            String sqlStationStatus = "select COALESCE(station_code,'') station_code," +
                    "COALESCE(make_order,'') make_order " +
                    "from d_pmc_me_station_status " +
                    "where work_center_code='" + work_center_code + "' ";
            if (prod_line_code != null && !prod_line_code.equals("")) {
                sqlStationStatus += "and prod_line_code='" + prod_line_code + "' ";
            }
            if (prod_line_type != null && !prod_line_type.equals("")) {
                sqlStationStatus += "and line_section_code='" + prod_line_type + "' ";
            }
            sqlStationStatus += "order by station_status_id ";
            List<Map<String, Object>> itemListStationStatus = cFuncDbSqlExecute.ExecSelectSql(prod_line_code, sqlStationStatus, false, request, apiRoutePath);
            stationScreenItem.put("make_order_list", itemListStationStatus);//当前工位实时工件信息

            stationScreenList.add(stationScreenItem);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, stationScreenList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "当前工位实时工件信息异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //6.工位生产订单队列(焊装)
    @RequestMapping(value = "/PmcBqHaStationScreenQueue_bak", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqHaStationScreenQueue_bak(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/project/bq/PmcBqHaStationScreenQueue";
        String selectResult = "";
        String errorMsg = "";
        try {
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String prod_line_code = jsonParas.getString("prod_line_code");//生产线
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_type = jsonParas.getString("prod_line_type");//产线(工位分段，例如：UB)

            //工位生产订单队列 来自3个状态：已完成3条(过站)、进行中(当前工位实时工件信息status=1)、待生产(工位生产订单)
            List<Map<String, Object>> haScreenList = new ArrayList<>();
            //数据
            Integer rownum = 1;//序号
            String white_car_adjust = "";//白车身调整总成
            Integer flow_online_id_max = 0;//当前工位生产订单序号
            String make_order = "";//当前工位生产订单
            Integer finish_count = 0;//已完成
            Integer going_count = 0;//进行中
            String status = "";//已完成、进行中、待生产
            String vpp_s = "";//平台
            String bbl_s = "";//驾驶室长度
            String bbr_s = "";//驾驶室顶高度
            String bbf_s = "";//驾驶室地板高度
            String bok_s = "";//工具箱
            String y26 = "";//变速箱型式
            String bed_s = "";//车门装饰板
            String bor_s = "";//天窗形式
            //1、查询已完成的3条记录
            String sqlFinish = "select online.flow_online_id, " +
                    "COALESCE(online.make_order,'') make_order," +
                    "TO_CHAR(online.creation_date, 'yyyy-MM-dd') creation_date," +
                    "COALESCE(online.white_car_adjust,'') white_car_adjust," +
                    "COALESCE(online.vpp_s,'') vpp_s," +
                    "COALESCE(online.bbl_s,'') bbl_s," +
                    "COALESCE(online.bbr_s,'') bbr_s," +
                    "COALESCE(online.bbf_s,'') bbf_s," +
                    "COALESCE(online.bok_s,'') bok_s," +
                    "COALESCE(online.y26,'') y26," +
                    "COALESCE(online.bed_s,'') bed_s," +
                    "COALESCE(online.bor_s,'') bor_s " +
                    "from d_pmc_me_station_flow flow " +
                    "join d_pmc_me_flow_online online " +
                    "on flow.make_order=online.make_order " +
                    "where flow.make_order <> '' and flow.set_sign='NORMAL' " +
                    "and flow.check_status='OK' and flow.station_code='"+station_code+"' " +
                    "and flow.work_center_code='"+work_center_code+"' " +
                    "and online.make_order not in (select online.make_order from d_pmc_me_flow_online " +
                    "online join d_pmc_me_station_status status on online.make_order=status.make_order " +
                    "where status.station_code='"+station_code+"' and status.station_status='1') " +
                    "order by flow.station_flow_id desc limit 3";
            List<Map<String, Object>> itemListFinish = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlFinish, false, request, apiRoutePath);
            if (itemListFinish != null && itemListFinish.size() > 0) {
                for (int i = itemListFinish.size() - 1; i >= 0; i--) {
                    flow_online_id_max = Integer.valueOf(itemListFinish.get(i).get("flow_online_id").toString());
                    make_order = itemListFinish.get(i).get("make_order").toString();

                    String creation_date_start = itemListFinish.get(i).get("creation_date").toString() + " 00:00:00";
                    String creation_date_end = itemListFinish.get(i).get("creation_date").toString() + " 23:59:59";
                    String sql="select COALESCE(make_order,'') make_order from d_pmc_me_flow_online " +
                            "where creation_date>='"+creation_date_start+"' and creation_date<='"+creation_date_end+"' " +
                            "order by flow_online_id";
                    List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sql, false, request, apiRoutePath);
                    String finalMake_order = make_order;
                    rownum = itemList.indexOf(itemList.stream().filter(e->e.get("make_order").toString().equals(finalMake_order)).findAny().orElse(null));

                    white_car_adjust = itemListFinish.get(i).get("white_car_adjust").toString();
                    vpp_s = itemListFinish.get(i).get("vpp_s").toString();
                    bbl_s = itemListFinish.get(i).get("bbl_s").toString();
                    bbr_s = itemListFinish.get(i).get("bbr_s").toString();
                    bbf_s = itemListFinish.get(i).get("bbf_s").toString();
                    bok_s = itemListFinish.get(i).get("bok_s").toString();
                    y26 = itemListFinish.get(i).get("y26").toString();
                    bed_s = itemListFinish.get(i).get("bed_s").toString();
                    bor_s = itemListFinish.get(i).get("bor_s").toString();
                    Map<String, Object> haScreenItem = new HashMap<>();
                    haScreenItem.put("rownum", rownum);
                    haScreenItem.put("white_car_adjust", white_car_adjust);
                    haScreenItem.put("make_order", make_order);
                    haScreenItem.put("status", "已完成");
                    haScreenItem.put("vpp_s", vpp_s);
                    haScreenItem.put("bbl_s", bbl_s);
                    haScreenItem.put("bbr_s", bbr_s);
                    haScreenItem.put("bbf_s", bbf_s);
                    haScreenItem.put("bok_s", bok_s);
                    haScreenItem.put("y26", y26);
                    haScreenItem.put("bed_s", bed_s);
                    haScreenItem.put("bor_s", bor_s);
                    haScreenList.add(haScreenItem);
                }
            }
            //2、查询进行中的1条记录
            String sqlGoing = "select online.flow_online_id, " +
                    "COALESCE(online.make_order,'') make_order," +
                    "TO_CHAR(online.creation_date, 'yyyy-MM-dd') creation_date," +
                    "COALESCE(online.white_car_adjust,'') white_car_adjust," +
                    "COALESCE(online.vpp_s,'') vpp_s," +
                    "COALESCE(online.bbl_s,'') bbl_s," +
                    "COALESCE(online.bbr_s,'') bbr_s," +
                    "COALESCE(online.bbf_s,'') bbf_s," +
                    "COALESCE(online.bok_s,'') bok_s," +
                    "COALESCE(online.y26,'') y26," +
                    "COALESCE(online.bed_s,'') bed_s," +
                    "COALESCE(online.bor_s,'') bor_s " +
                    "from d_pmc_me_flow_online online " +
                    "join d_pmc_me_station_status status " +
                    "on online.make_order=status.make_order " +
                    "where status.station_code='" + station_code + "' " +
                    "and status.station_status='1' ";
            List<Map<String, Object>> itemListGoing = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlGoing, false, null, apiRoutePath);
            if (itemListGoing != null && itemListGoing.size() > 0) {
                int flow_online_id = Integer.valueOf(itemListGoing.get(0).get("flow_online_id").toString());
                if (flow_online_id > flow_online_id_max)
                    flow_online_id_max = flow_online_id;
                make_order = itemListGoing.get(0).get("make_order").toString();

                String creation_date_start = itemListGoing.get(0).get("creation_date").toString() + " 00:00:00";
                String creation_date_end = itemListGoing.get(0).get("creation_date").toString() + " 23:59:59";
                String sql="select COALESCE(make_order,'') make_order from d_pmc_me_flow_online " +
                        "where creation_date>='"+creation_date_start+"' and creation_date<='"+creation_date_end+"' " +
                        "order by flow_online_id";
                List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sql, false, request, apiRoutePath);
                String finalMake_order = make_order;
                rownum = itemList.indexOf(itemList.stream().filter(e->e.get("make_order").toString().equals(finalMake_order)).findAny().orElse(null));

                white_car_adjust = itemListGoing.get(0).get("white_car_adjust").toString();
                vpp_s = itemListGoing.get(0).get("vpp_s").toString();
                bbl_s = itemListGoing.get(0).get("bbl_s").toString();
                bbr_s = itemListGoing.get(0).get("bbr_s").toString();
                bbf_s = itemListGoing.get(0).get("bbf_s").toString();
                bok_s = itemListGoing.get(0).get("bok_s").toString();
                y26 = itemListGoing.get(0).get("y26").toString();
                bed_s = itemListGoing.get(0).get("bed_s").toString();
                bor_s = itemListGoing.get(0).get("bor_s").toString();
                Map<String, Object> haScreenItem = new HashMap<>();
                haScreenItem.put("rownum", rownum);
                haScreenItem.put("white_car_adjust", white_car_adjust);
                haScreenItem.put("make_order", make_order);
                haScreenItem.put("status", "进行中");
                haScreenItem.put("vpp_s", vpp_s);
                haScreenItem.put("bbl_s", bbl_s);
                haScreenItem.put("bbr_s", bbr_s);
                haScreenItem.put("bbf_s", bbf_s);
                haScreenItem.put("bok_s", bok_s);
                haScreenItem.put("y26", y26);
                haScreenItem.put("bed_s", bed_s);
                haScreenItem.put("bor_s", bor_s);
                haScreenList.add(haScreenItem);
            }
            //3、查询待执行的数据
            int count = 7 - itemListFinish.size() - itemListGoing.size();
            String sqlStandBy = "select online.flow_online_id, " +
                    "COALESCE(online.make_order,'') make_order," +
                    "TO_CHAR(online.creation_date, 'yyyy-MM-dd') creation_date," +
                    "COALESCE(online.white_car_adjust,'') white_car_adjust," +
                    "COALESCE(online.vpp_s,'') vpp_s," +
                    "COALESCE(online.bbl_s,'') bbl_s," +
                    "COALESCE(online.bbr_s,'') bbr_s," +
                    "COALESCE(online.bbf_s,'') bbf_s," +
                    "COALESCE(online.bok_s,'') bok_s," +
                    "COALESCE(online.y26,'') y26," +
                    "COALESCE(online.bed_s,'') bed_s," +
                    "COALESCE(online.bor_s,'') bor_s " +
                    "from d_pmc_me_flow_online online " +
                    "where online.flow_online_id>" + flow_online_id_max +
                    " and online.enable_flag='Y' " +
                    " and online.prod_line_code='" + prod_line_code + "' " +
                    " and online.work_center_code='" + work_center_code + "' " +
                    " order by online.flow_online_id limit " + count;
            List<Map<String, Object>> itemListStandBy = cFuncDbSqlExecute.ExecSelectSql(prod_line_code, sqlStandBy, false, request, apiRoutePath);
            if (itemListStandBy != null && itemListStandBy.size() > 0) {
                for (int i = 0; i < itemListStandBy.size(); i++) {
                    white_car_adjust = itemListStandBy.get(i).get("white_car_adjust").toString();
                    make_order = itemListStandBy.get(i).get("make_order").toString();

                    String creation_date_start = itemListStandBy.get(i).get("creation_date").toString() + " 00:00:00";
                    String creation_date_end = itemListStandBy.get(i).get("creation_date").toString() + " 23:59:59";
                    String sql="select COALESCE(make_order,'') make_order from d_pmc_me_flow_online " +
                            "where creation_date>='"+creation_date_start+"' and creation_date<='"+creation_date_end+"' " +
                            "order by flow_online_id";
                    List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sql, false, request, apiRoutePath);
                    String finalMake_order = make_order;
                    rownum = itemList.indexOf(itemList.stream().filter(e->e.get("make_order").toString().equals(finalMake_order)).findAny().orElse(null));

                    vpp_s = itemListStandBy.get(i).get("vpp_s").toString();
                    bbl_s = itemListStandBy.get(i).get("bbl_s").toString();
                    bbr_s = itemListStandBy.get(i).get("bbr_s").toString();
                    bbf_s = itemListStandBy.get(i).get("bbf_s").toString();
                    bok_s = itemListStandBy.get(i).get("bok_s").toString();
                    y26 = itemListStandBy.get(i).get("y26").toString();
                    bed_s = itemListStandBy.get(i).get("bed_s").toString();
                    bor_s = itemListStandBy.get(i).get("bor_s").toString();
                    Map<String, Object> haScreenItem = new HashMap<>();
                    haScreenItem.put("rownum", rownum);
                    haScreenItem.put("white_car_adjust", white_car_adjust);
                    haScreenItem.put("make_order", make_order);
                    haScreenItem.put("status", "待生产");
                    haScreenItem.put("vpp_s", vpp_s);
                    haScreenItem.put("bbl_s", bbl_s);
                    haScreenItem.put("bbr_s", bbr_s);
                    haScreenItem.put("bbf_s", bbf_s);
                    haScreenItem.put("bok_s", bok_s);
                    haScreenItem.put("y26", y26);
                    haScreenItem.put("bed_s", bed_s);
                    haScreenItem.put("bor_s", bor_s);
                    haScreenList.add(haScreenItem);
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, haScreenList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "工位生产订单队列(焊装)异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //6.工位生产订单队列(焊装)
    @RequestMapping(value = "/PmcBqHaStationScreenQueue", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqHaStationScreenQueue(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/project/bq/PmcBqHaStationScreenQueue";
        String selectResult = "";
        String errorMsg = "";
        try {
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String prod_line_code = jsonParas.getString("prod_line_code");//生产线
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_type = jsonParas.getString("prod_line_type");//产线(工位分段，例如：UB)

            //工位生产订单队列 来自3个状态：已完成3条(过站)、进行中(当前工位实时工件信息status=1)、待生产(工位生产订单)
            List<Map<String, Object>> haScreenList = new ArrayList<>();
            //数据
            Integer rownum = 1;//序号
            String white_car_adjust = "";//白车身调整总成
            Integer station_mo_id_max = 0;//当前工位生产订单序号
            String make_order = "";//当前工位生产订单
            Integer finish_count = 0;//已完成
            Integer going_count = 0;//进行中
            String status = "";//已完成、进行中、待生产
            String vpp_s = "";//平台
            String bbl_s = "";//驾驶室长度
            String bbr_s = "";//驾驶室顶高度
            String bbf_s = "";//驾驶室地板高度
            String bok_s = "";//工具箱
            String y26 = "";//变速箱型式
            String bed_s = "";//车门装饰板
            String bor_s = "";//天窗形式
            //1、查询已完成的3条记录
            String sqlFinish = "SELECT mo.station_mo_id, " +
                    "COALESCE ( mo.make_order, '' ) make_order, " +
                    "TO_CHAR( mo.creation_date, 'yyyy-MM-dd' ) creation_date, " +
                    "COALESCE ( online.white_car_adjust, '' ) white_car_adjust, " +
                    "COALESCE ( online.vpp_s, '' ) vpp_s, " +
                    "COALESCE ( online.bbl_s, '' ) bbl_s, " +
                    "COALESCE ( online.bbr_s, '' ) bbr_s, " +
                    "COALESCE ( online.bbf_s, '' ) bbf_s, " +
                    "COALESCE ( online.bok_s, '' ) bok_s, " +
                    "COALESCE ( online.y26, '' ) y26, " +
                    "COALESCE ( online.bed_s, '' ) bed_s, " +
                    "COALESCE ( online.bor_s, '' ) bor_s " +
                    "FROM d_pmc_me_station_flow flow " +
                    "JOIN d_pmc_me_station_mo mo ON flow.make_order = mo.make_order  AND flow.station_code = mo.station_code " +
                    "JOIN sys_fmod_station station on flow.station_code =station.station_code " +
                    "LEFT JOIN d_pmc_me_flow_online online ON flow.make_order = online.make_order " +
                    "WHERE flow.make_order <> ''  AND flow.set_sign = 'NORMAL'  AND flow.check_status = 'OK'  " +
                    "AND COALESCE(station.sub_station_code,station.station_code) = '"+station_code+"'  " +
                    "AND flow.work_center_code = '"+work_center_code+"'  AND mo.make_order NOT IN " +
                    "( SELECT mo.make_order  FROM d_pmc_me_station_mo mo " +
                    "JOIN d_pmc_me_station_status status ON mo.make_order = status.make_order " +
                    "JOIN sys_fmod_station station on status.station_code =station.station_code " +
                    "WHERE COALESCE(station.sub_station_code,station.station_code) = '"+station_code+"' " +
                    "AND status.station_status = '1'  ) " +
                    "ORDER BY flow.station_flow_id DESC  LIMIT 3 " ;
            List<Map<String, Object>> itemListFinish = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlFinish, false, request, apiRoutePath);
            if (itemListFinish != null && itemListFinish.size() > 0) {
                for (int i = itemListFinish.size() - 1; i >= 0; i--) {
                    station_mo_id_max = Integer.valueOf(itemListFinish.get(i).get("station_mo_id").toString());
                    make_order = itemListFinish.get(i).get("make_order").toString();

                    String creation_date_start = itemListFinish.get(i).get("creation_date").toString() + " 00:00:00";
                    String creation_date_end = itemListFinish.get(i).get("creation_date").toString() + " 23:59:59";
                    String sql="select COALESCE(mo.make_order,'') make_order " +
                            "from d_pmc_me_station_mo mo " +
                            "JOIN sys_fmod_station station ON mo.station_code = station.station_code " +
                            "where mo.creation_date>='"+creation_date_start+"' and mo.creation_date<='"+creation_date_end+"' " +
                            "and COALESCE ( station.sub_station_code, station.station_code ) = '"+station_code+"'  " +
                            "order by mo.mo_work_order";
                    List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sql, false, request, apiRoutePath);
                    String finalMake_order = make_order;
                    rownum = itemList.indexOf(itemList.stream().filter(e->e.get("make_order").toString().equals(finalMake_order)).findAny().orElse(null));

                    white_car_adjust = itemListFinish.get(i).get("white_car_adjust").toString();
                    vpp_s = itemListFinish.get(i).get("vpp_s").toString();
                    bbl_s = itemListFinish.get(i).get("bbl_s").toString();
                    bbr_s = itemListFinish.get(i).get("bbr_s").toString();
                    bbf_s = itemListFinish.get(i).get("bbf_s").toString();
                    bok_s = itemListFinish.get(i).get("bok_s").toString();
                    y26 = itemListFinish.get(i).get("y26").toString();
                    bed_s = itemListFinish.get(i).get("bed_s").toString();
                    bor_s = itemListFinish.get(i).get("bor_s").toString();
                    Map<String, Object> haScreenItem = new HashMap<>();
                    haScreenItem.put("rownum", rownum);
                    haScreenItem.put("white_car_adjust", white_car_adjust);
                    haScreenItem.put("make_order", make_order);
                    haScreenItem.put("status", "已完成");
                    haScreenItem.put("vpp_s", vpp_s);
                    haScreenItem.put("bbl_s", bbl_s);
                    haScreenItem.put("bbr_s", bbr_s);
                    haScreenItem.put("bbf_s", bbf_s);
                    haScreenItem.put("bok_s", bok_s);
                    haScreenItem.put("y26", y26);
                    haScreenItem.put("bed_s", bed_s);
                    haScreenItem.put("bor_s", bor_s);
                    haScreenList.add(haScreenItem);
                }
            }
            //2、查询进行中的1条记录
            String sqlGoing = "SELECT mo.station_mo_id, " +
                    "COALESCE ( mo.make_order, '' ) make_order, " +
                    "TO_CHAR( mo.creation_date, 'yyyy-MM-dd' ) creation_date, " +
                    "COALESCE ( online.white_car_adjust, '' ) white_car_adjust, " +
                    "COALESCE ( online.vpp_s, '' ) vpp_s, " +
                    "COALESCE ( online.bbl_s, '' ) bbl_s, " +
                    "COALESCE ( online.bbr_s, '' ) bbr_s, " +
                    "COALESCE ( online.bbf_s, '' ) bbf_s, " +
                    "COALESCE ( online.bok_s, '' ) bok_s, " +
                    "COALESCE ( online.y26, '' ) y26, " +
                    "COALESCE ( online.bed_s, '' ) bed_s, " +
                    "COALESCE ( online.bor_s, '' ) bor_s " +
                    "FROM d_pmc_me_station_mo mo " +
                    "JOIN d_pmc_me_station_status status ON mo.make_order = status.make_order and mo.station_code=status.station_code " +
                    "JOIN sys_fmod_station station ON status.station_code = station.station_code " +
                    "LEFT JOIN d_pmc_me_flow_online online ON status.make_order = online.make_order " +
                    "WHERE COALESCE ( station.sub_station_code, station.station_code ) = '"+station_code+"'  " +
                    "AND status.station_status = '1' ";
            List<Map<String, Object>> itemListGoing = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlGoing, false, null, apiRoutePath);
            if (itemListGoing != null && itemListGoing.size() > 0) {
                int station_mo_id = Integer.valueOf(itemListGoing.get(0).get("station_mo_id").toString());
                if (station_mo_id > station_mo_id_max)
                    station_mo_id_max = station_mo_id;
                make_order = itemListGoing.get(0).get("make_order").toString();

                String creation_date_start = itemListGoing.get(0).get("creation_date").toString() + " 00:00:00";
                String creation_date_end = itemListGoing.get(0).get("creation_date").toString() + " 23:59:59";
                String sql="select COALESCE(mo.make_order,'') make_order " +
                        "from d_pmc_me_station_mo mo " +
                        "JOIN sys_fmod_station station ON mo.station_code = station.station_code " +
                        "where mo.creation_date>='"+creation_date_start+"' and mo.creation_date<='"+creation_date_end+"' " +
                        "and COALESCE ( station.sub_station_code, station.station_code ) = '"+station_code+"'  " +
                        "order by mo.mo_work_order";
                List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sql, false, request, apiRoutePath);
                String finalMake_order = make_order;
                rownum = itemList.indexOf(itemList.stream().filter(e->e.get("make_order").toString().equals(finalMake_order)).findAny().orElse(null));

                white_car_adjust = itemListGoing.get(0).get("white_car_adjust").toString();
                vpp_s = itemListGoing.get(0).get("vpp_s").toString();
                bbl_s = itemListGoing.get(0).get("bbl_s").toString();
                bbr_s = itemListGoing.get(0).get("bbr_s").toString();
                bbf_s = itemListGoing.get(0).get("bbf_s").toString();
                bok_s = itemListGoing.get(0).get("bok_s").toString();
                y26 = itemListGoing.get(0).get("y26").toString();
                bed_s = itemListGoing.get(0).get("bed_s").toString();
                bor_s = itemListGoing.get(0).get("bor_s").toString();
                Map<String, Object> haScreenItem = new HashMap<>();
                haScreenItem.put("rownum", rownum);
                haScreenItem.put("white_car_adjust", white_car_adjust);
                haScreenItem.put("make_order", make_order);
                haScreenItem.put("status", "进行中");
                haScreenItem.put("vpp_s", vpp_s);
                haScreenItem.put("bbl_s", bbl_s);
                haScreenItem.put("bbr_s", bbr_s);
                haScreenItem.put("bbf_s", bbf_s);
                haScreenItem.put("bok_s", bok_s);
                haScreenItem.put("y26", y26);
                haScreenItem.put("bed_s", bed_s);
                haScreenItem.put("bor_s", bor_s);
                haScreenList.add(haScreenItem);
            }
            //3、查询待执行的数据
            int count = 7 - itemListFinish.size() - itemListGoing.size();
            String sqlStandBy = "SELECT mo.station_mo_id, " +
                    "COALESCE ( mo.make_order, '' ) make_order," +
                    "TO_CHAR( mo.creation_date, 'yyyy-MM-dd' ) creation_date, " +
                    "COALESCE ( online.white_car_adjust, '' ) white_car_adjust, " +
                    "COALESCE ( online.vpp_s, '' ) vpp_s, " +
                    "COALESCE ( online.bbl_s, '' ) bbl_s, " +
                    "COALESCE ( online.bbr_s, '' ) bbr_s, " +
                    "COALESCE ( online.bbf_s, '' ) bbf_s, " +
                    "COALESCE ( online.bok_s, '' ) bok_s, " +
                    "COALESCE ( online.y26, '' ) y26, " +
                    "COALESCE ( online.bed_s, '' ) bed_s, " +
                    "COALESCE ( online.bor_s, '' ) bor_s " +
                    "FROM d_pmc_me_station_mo mo " +
                    "JOIN sys_fmod_station station ON mo.station_code = station.station_code " +
                    "LEFT JOIN d_pmc_me_flow_online online ON mo.make_order = online.make_order " +
                    "WHERE mo.station_mo_id > " +station_mo_id_max+
                    " AND mo.set_sign = 'NONE'  " +
                    " AND COALESCE ( station.sub_station_code, station.station_code ) = '"+station_code+"'  " +
                    " AND online.enable_flag = 'Y'  AND online.prod_line_code = '"+prod_line_code+"'  " +
                    " AND online.work_center_code = '"+work_center_code+"' " +
                    " ORDER BY online.flow_online_id  LIMIT " + count;
            List<Map<String, Object>> itemListStandBy = cFuncDbSqlExecute.ExecSelectSql(prod_line_code, sqlStandBy, false, request, apiRoutePath);
            if (itemListStandBy != null && itemListStandBy.size() > 0) {
                for (int i = 0; i < itemListStandBy.size(); i++) {
                    white_car_adjust = itemListStandBy.get(i).get("white_car_adjust").toString();
                    make_order = itemListStandBy.get(i).get("make_order").toString();

                    String creation_date_start = itemListStandBy.get(i).get("creation_date").toString() + " 00:00:00";
                    String creation_date_end = itemListStandBy.get(i).get("creation_date").toString() + " 23:59:59";
                    String sql="select COALESCE(mo.make_order,'') make_order " +
                            "from d_pmc_me_station_mo mo " +
                            "JOIN sys_fmod_station station ON mo.station_code = station.station_code " +
                            "where mo.creation_date>='"+creation_date_start+"' and mo.creation_date<='"+creation_date_end+"' " +
                            "and COALESCE ( station.sub_station_code, station.station_code ) = '"+station_code+"'  " +
                            "order by mo.mo_work_order";
                    List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sql, false, request, apiRoutePath);
                    String finalMake_order = make_order;
                    rownum = itemList.indexOf(itemList.stream().filter(e->e.get("make_order").toString().equals(finalMake_order)).findAny().orElse(null));

                    vpp_s = itemListStandBy.get(i).get("vpp_s").toString();
                    bbl_s = itemListStandBy.get(i).get("bbl_s").toString();
                    bbr_s = itemListStandBy.get(i).get("bbr_s").toString();
                    bbf_s = itemListStandBy.get(i).get("bbf_s").toString();
                    bok_s = itemListStandBy.get(i).get("bok_s").toString();
                    y26 = itemListStandBy.get(i).get("y26").toString();
                    bed_s = itemListStandBy.get(i).get("bed_s").toString();
                    bor_s = itemListStandBy.get(i).get("bor_s").toString();
                    Map<String, Object> haScreenItem = new HashMap<>();
                    haScreenItem.put("rownum", rownum);
                    haScreenItem.put("white_car_adjust", white_car_adjust);
                    haScreenItem.put("make_order", make_order);
                    haScreenItem.put("status", "待生产");
                    haScreenItem.put("vpp_s", vpp_s);
                    haScreenItem.put("bbl_s", bbl_s);
                    haScreenItem.put("bbr_s", bbr_s);
                    haScreenItem.put("bbf_s", bbf_s);
                    haScreenItem.put("bok_s", bok_s);
                    haScreenItem.put("y26", y26);
                    haScreenItem.put("bed_s", bed_s);
                    haScreenItem.put("bor_s", bor_s);
                    haScreenList.add(haScreenItem);
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, haScreenList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "工位生产订单队列(焊装)异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //7.总装工位生产订单队列
    @RequestMapping(value = "/PmcBqZaStationScreenOrderQueue", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqZaStationScreenOrderQueue(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/project/bq/PmcBqZaStationScreenOrderQueue";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            int limit = 4;
            String lastMakeOrder = "";  // 最后一个过站订单

            List<Map<String, Object>> stationScreenList = new ArrayList<>();
            Map<String, Object> stationScreenItem = new HashMap<>();

            //工位生产订单队列 来自3个状态：已完成1条(过站)、进行中1条(当前工位实时工件信息)、未开始2条(工位生产订单)
            List<Map<String, Object>> queueList = new ArrayList<>();
            //(1)进行中1条(当前工位实时工件信息)
            String make_order = "";//当前工位生成订单
            String sqlStationStatus = "select '2' rownum," +
                    "COALESCE(make_order,'') make_order," +
                    "COALESCE(vin,'') vin," +
                    "COALESCE(dms,'') dms," +
                    "COALESCE(small_model_type,'') small_model_type," +
                    "COALESCE(material_color,'') material_color," +
                    "COALESCE(material_size,'') material_size," +
                    "COALESCE(engine_num,'') engine_num," +
                    "COALESCE(driver_way,'') driver_way," +
                    "'进行中' status " +
                    "from d_pmc_me_station_status " +
                    "where station_status='1' " +
                    "and set_sign='NORMAL' " +
                    "and check_status='OK' " +
                    "and station_code='" + station_code + "' " +
                    "and work_center_code='" + work_center_code + "' " +
                    "limit 1 ";
            List<Map<String, Object>> itemListStationStatus = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationStatus, false, request, apiRoutePath);
            if (itemListStationStatus != null && itemListStationStatus.size() > 0) {
                make_order = itemListStationStatus.get(0).get("make_order").toString();
            }
            //(2)已完成1条(过站)
            String sqlStationFlow = "select '1' rownum," +
                    "COALESCE(make_order,'') make_order," +
                    "COALESCE(vin,'') vin," +
                    "COALESCE(dms,'') dms," +
                    "COALESCE(small_model_type,'') small_model_type," +
                    "COALESCE(material_color,'') material_color," +
                    "COALESCE(material_size,'') material_size," +
                    "'' engine_num," +
                    "'' driver_way," +
                    "'已完成' status " +
                    "from d_pmc_me_station_flow " +
                    "where make_order <> '' " +
                    "and set_sign='NORMAL' " +
                    "and check_status='OK' " +
                    "and station_code='" + station_code + "' " +
                    "and work_center_code='" + work_center_code + "' ";
            if (make_order != null && !make_order.equals("")) {
                sqlStationFlow += " and make_order <> '" + make_order + "' ";
            }
            sqlStationFlow += " order by station_flow_id desc limit 1 ";
            List<Map<String, Object>> itemListStationFlow = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationFlow, false, request, apiRoutePath);
            if (itemListStationFlow != null && itemListStationFlow.size() > 0) {
                queueList.add(itemListStationFlow.get(0));
                limit = 3;
                lastMakeOrder = (String) itemListStationFlow.get(0).get("make_order");
            }
            if (itemListStationStatus != null && itemListStationStatus.size() > 0) {
                queueList.add(itemListStationStatus.get(0));
                limit = (limit == 3 ? 2 : 3);
                lastMakeOrder = (String) itemListStationStatus.get(0).get("make_order");
            }


            //(3)未开始2条(工位生产订单)
            String sqlStationMo = "select COALESCE(b.make_order,'') make_order," +
                    "COALESCE(b.vin,'') vin," +
                    "COALESCE(b.dms,'') dms," +
                    "COALESCE(b.small_model_type,'') small_model_type," +
                    "COALESCE(b.material_color,'') material_color," +
                    "COALESCE(b.material_size,'') material_size," +
                    "COALESCE(b.engine_num,'') engine_num," +
                    "COALESCE(b.driver_way,'') driver_way," +
                    "'未开始' status " +
                    "from d_pmc_me_flow_online b " +
                    "where work_center_code='" + work_center_code + "' " +
                    "and flow_online_id > (select flow_online_id from d_pmc_me_flow_online where make_order = '" + lastMakeOrder + "' and work_center_code = 'ZA' limit 1)";
            sqlStationMo += " order by b.flow_online_id limit " + limit;
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, request, apiRoutePath);
            if (itemListStationMo != null && itemListStationMo.size() > 0) {
                for (Map<String, Object> stationMo : itemListStationMo) {
                    queueList.add(stationMo);
                }
            }

            for (int i = 0; i < queueList.size(); i++) {
                Map<String, Object> map = queueList.get(i);
                map.put("rownum", String.format("%03d", (i + 1)));
            }

            stationScreenItem.put("station_mo_list", queueList);//工位生产订单队列

            stationScreenList.add(stationScreenItem);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, stationScreenList, "", "", queueList.size());
        } catch (Exception ex) {
            errorMsg = "工位生产订单队列异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}

