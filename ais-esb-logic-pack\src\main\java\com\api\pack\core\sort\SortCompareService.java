package com.api.pack.core.sort;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.pack.core.board.*;
import com.api.pack.core.ccd.CCDBoardsMessage;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
public class SortCompareService implements ApplicationEventPublisherAware
{
    private final MessageSource messageSource;

    private final BoardF2BComparator boardF2BComparator = new BoardF2BComparator();

    private ApplicationEventPublisher eventPublisher;

    public SortCompareService(MessageSource messageSource)
    {
        this.messageSource = messageSource;
    }

    @Override
    public void setApplicationEventPublisher(@NonNull ApplicationEventPublisher applicationEventPublisher)
    {
        this.eventPublisher = applicationEventPublisher;
    }

    public SortCompareResult compare(CCDBoardsMessage ccdData, Document plan, JSONObject recipe, JSONObject sortSwitch, List<SortSplitRule.Item> setSortRules, List<SortSplitRule.Item> pcsSortRules)
    {
        SortCompareResult sortCompareResult;
        // 初始化SET板件信息
        String boardId = UUID.randomUUID().toString().replace("-", "");
        Integer boardIndex = plan.getInteger("finish_ok_count", 0) + 1; // 板序号
        Object boardLength = plan.get("m_length"); // 板长(mm)
        Object boardWidth = plan.get("m_width"); // 板宽(mm)
        Object boardThickness = plan.get("m_thickness");
        if (boardThickness == null)
        {
            boardThickness = plan.get("m_tickness"); // 板厚(mm) 兼容此前的拼写错误
        }
        Object boardWeight = plan.get("m_weight"); // 板重(g)
        String lotNum = String.valueOf(plan.get("lot_num")); // 工单号/批号
        String taskType = String.valueOf(plan.get("task_type")); // 任务类型
        String cyclePeriod = String.valueOf(plan.get("cycle_period")); // 周期
        String partNumber = String.valueOf(plan.get("model_type")); // 料号
        String partVersion = String.valueOf(plan.get("model_version")); // 版本
        String batchNo = String.valueOf(plan.get("batch_no")); // 批号
        String laserBatchNo = String.valueOf(plan.get("laser_batch_no")); // 镭射批号
        String typesettingNo = String.valueOf(plan.get("typesetting_no")); // 排版数
        String customerMn = String.valueOf(plan.get("customer_mn")); // 客户料号
        String ulCode = String.valueOf(plan.get("ul_code")); // UL号
        Object planId = plan.get("_id"); // 工单ID
        if (planId instanceof ObjectId)
        {
            planId = ((ObjectId) planId).toHexString();
        }
        SETBoard setBoard = new SETBoard(boardId);
        setBoard.setBoardSn(ccdData.getTransmitId());
        setBoard.setBoardIndex(boardIndex);
        setBoard.setBoardLength(new BigDecimal(String.valueOf(boardLength)).doubleValue());
        setBoard.setBoardWidth(new BigDecimal(String.valueOf(boardWidth)).doubleValue());
        setBoard.setBoardThickness(new BigDecimal(String.valueOf(boardThickness)).doubleValue());
        setBoard.setBoardWeight(new BigDecimal(String.valueOf(boardWeight)).doubleValue());
        setBoard.setLotNum(lotNum);
        setBoard.setTaskType(taskType);
        setBoard.setCyclePeriod(cyclePeriod);
        setBoard.setPartNumber(partNumber);
        setBoard.setPartVersion(partVersion);
        setBoard.setBatchNo(batchNo);
        setBoard.setLaserBatchNo(laserBatchNo);
        setBoard.setTypesettingNo(typesettingNo);
        setBoard.setCustomerMn(customerMn);
        setBoard.setUlCode(ulCode);
        setBoard.setPlanId(String.valueOf(planId));
        if (sortSwitch.containsKey(SortConst.X_OUT))
        {
            String xOutRuleVal = sortSwitch.getString(SortConst.X_OUT);
            setBoard.setXoutFlag(BoardConst.FLAG_Y);
            try
            {
                int xOutRuleValInt = Integer.parseInt(xOutRuleVal.replace("X", ""));
                setBoard.setXoutSetNumber(xOutRuleValInt);
            }
            catch (NumberFormatException ex)
            {
                log.warn("X_OUT规则值处理后仍不是数字，无法转换为整数：{}", xOutRuleVal);
            }
        }
        setBoard.setMultiAspect(new HashMap<>());
        Map<Integer, PCSBoard> pcsBoards = new HashMap<>();
        try
        {
            JSONObject planJSONObject = new JSONObject(plan);
            String setBoardCategory = SortConst.BOARD_CATEGORY_SET.toUpperCase();
            String pcsBoardCategory = SortConst.BOARD_CATEGORY_PCS.toUpperCase();
            String setBoardType = recipe.getString("array_type");
            String pcsBoardType = recipe.getString("bd_type");
            boolean isSingleSide = SortConst.BOARD_SIDE_SINGLE.equals(setBoardType);
            boolean isDoubleSide = SortConst.BOARD_SIDE_DOUBLE.equals(setBoardType);
            Map<String, JSONObject> multiAspect = ccdData.getMultiAspect();
            Map<String, JSONArray> multiAspectChildren = ccdData.getMultiAspectChildren();
            setBoard.setBoardName(setBoardCategory);
            if (multiAspect == null || multiAspect.isEmpty())
            {
                String errMessage = messageSource.getMessage(SortConst.COMPARE_MESSAGES_PACK_CCD_DATA_ERROR, null, Locale.getDefault());
                throw new SortCompareException(errMessage);
            }
            else if (multiAspect.size() == 2)
            {
                // 补全并检查板件数据是否完整
                setBoard.completeAndCheckFromMultiAspect(setBoardType, pcsBoardType, multiAspect, boardF2BComparator, messageSource, sortSwitch.containsKey(SortConst.BOARD_F2B));
            }
            for (String orient : ccdData.getMultiAspect().keySet()) // 板件正反面，理论最多2次循环
            {
                if (isSingleSide && !orient.equals(setBoard.getBoardOrient())) // 单面板件，只比对板件有效面，跳过无效面
                {
                    continue;
                }
                JSONObject set = ccdData.getMultiAspect().get(orient);
                JSONArray pcsArr = ccdData.getMultiAspectChildren().get(orient);
                for (SortSplitRule.Item sortRule : setSortRules)
                {
                    if (isSkipCompare(setBoardCategory, set, sortSwitch, setBoardType, sortRule))
                    {
                        continue;
                    }
                    // 比对SET板件信息（根据板件本身相关信息/工单计划相关设定），异常时抛出异常并记录NG信息
                    this.eventPublisher.publishEvent(new SortCompareOriginEvent(new SortCompareOrigin(setBoardType, setBoardCategory, orient, set, setBoardType, setBoardCategory, orient, set, planJSONObject, sortRule, sortRule.getCode())));
                }
                for (Object pcsItem : pcsArr)
                {
                    if (pcsItem instanceof JSONObject)
                    {
                        String pcsBoardId = UUID.randomUUID().toString().replace("-", "");
                        JSONObject pcs = (JSONObject) pcsItem;
                        PCSBoard pcsBoard = new PCSBoard(orient, pcs);
                        pcsBoard.setBoardId(pcsBoardId);
                        pcsBoard.setParentBoard(setBoard);
                        pcsBoard.setBoardType(pcsBoardType);
                        Integer pcsIndex = pcsBoard.getBoardIndex();
                        pcsBoards.putIfAbsent(pcsIndex, pcsBoard.toOK());
                        for (SortSplitRule.Item sortRule : pcsSortRules)
                        {
                            if (isSkipCompare(pcsBoardCategory, pcs, sortSwitch, pcsBoardType, sortRule))
                            {
                                continue;
                            }
                            try
                            {
                                // 比对PCS与SET板件上的信息（根据板件本身相关信息/工单计划相关设定），异常时抛出异常并记录NG信息
                                this.eventPublisher.publishEvent(new SortCompareOriginEvent(new SortCompareOrigin(pcsBoardType, pcsBoardCategory, orient, pcsIndex, pcs, setBoardType, setBoardCategory, orient, set, planJSONObject, sortRule, sortRule.getCode())));
                            }
                            catch (SortException ex)
                            {
                                pcsBoards.put(pcsIndex, pcsBoard.toNG(ex.getCode(), ex.getMessage()));
                                setBoard.toNG(ex.getCode(), ex.getMessage());
                            }
                        }
                    }
                }
                if (!setBoard.isOK()) // SET板件已NG，不用再继续比对
                {
                    throw new SortCompareException(setBoard.getBoardNgMsg(), setBoard.getBoardNgCode());
                }
            }
            for (String sort : sortSwitch.keySet())
            {
                // 跳过默认比对规则
                if (!SortConst.NON_DEFAULT.contains(sort))
                {
                    continue;
                }
                if (multiAspect.size() == 2 && multiAspectChildren.size() == 2)
                {
                    SortSplitRule.Item sortRule = new SortSplitRule.Item(sort, sort, sortSwitch.getString(sort), false);
                    JSONObject frontSet = multiAspect.get(SortConst.BOARD_FRONT);
                    JSONArray frontPcsArr = multiAspectChildren.get(SortConst.BOARD_FRONT);
                    JSONObject backSet = multiAspect.get(SortConst.BOARD_BACK);
                    JSONArray backPcsArr = multiAspectChildren.get(SortConst.BOARD_BACK);
                    if ((isDoubleSide && frontPcsArr.size() != backPcsArr.size()) // 双面板件，正反面PCS数量不一致
                            || (isSingleSide && frontPcsArr.isEmpty() && backPcsArr.isEmpty()) // 单面板件，正反面PCS数量都为空
                    )
                    {
                        String errMessage = messageSource.getMessage(SortConst.COMPARE_MESSAGES_PACK_PCS_COUNT_ERROR, null, Locale.getDefault());
                        throw new SortCompareException(errMessage);
                    }
                    else
                    {
                        int frontPcsArrSize = frontPcsArr.size();
                        int backPcsArrSize = backPcsArr.size();
                        boolean useFront = frontPcsArrSize >= backPcsArrSize;
                        boolean isEqual = frontPcsArrSize == backPcsArrSize;
                        int cycle = useFront ? frontPcsArr.size() : backPcsArr.size();
                        JSONObject set = useFront ? frontSet : backSet;
                        JSONArray pcsArr = useFront ? frontPcsArr : backPcsArr;
                        // 比对SET板件信息（根据单面/正反面/工单计划相关设定），异常时抛出异常并记录NG信息
                        this.eventPublisher.publishEvent(new SortCompareOriginEvent(new SortCompareOrigin(setBoardType, setBoardCategory, SortConst.BOARD_FRONT, null, frontSet, setBoardType, setBoardCategory, SortConst.BOARD_BACK, null, isEqual ? backSet : set, planJSONObject, sortRule, sort)));
                        for (int i = 0; i < cycle; i++)
                        {
                            JSONObject frontPcs = isEqual ? frontPcsArr.getJSONObject(i) : pcsArr.getJSONObject(i);
                            Integer frontPcsIndex = frontPcs.getIntValue(SortConst.BOARD_CATEGORY_PCS + BoardConst.NUM);
                            JSONObject backPcs = isEqual ?  backPcsArr.getJSONObject(i) : pcsArr.getJSONObject(i);
                            Integer backPcsIndex = backPcs.getIntValue(SortConst.BOARD_CATEGORY_PCS + BoardConst.NUM);
                            try
                            {
                                // 比对PCS板件信息（根据单面/正反面/工单计划相关设定），异常时抛出异常并记录NG信息
                                this.eventPublisher.publishEvent(new SortCompareOriginEvent(new SortCompareOrigin(pcsBoardType, pcsBoardCategory, SortConst.BOARD_FRONT, frontPcsIndex, frontPcs, pcsBoardType, pcsBoardCategory, SortConst.BOARD_BACK, backPcsIndex, backPcs, planJSONObject, sortRule, sort)));
                            }
                            catch (SortException ex)
                            {
                                pcsBoards.put(frontPcsIndex, new PCSBoard(SortConst.BOARD_FRONT, frontPcs).toNG(ex.getCode(), ex.getMessage()));
                                throw ex;
                            }
                        }
                    }
                }
            }
            if (setBoard.isOK())
            {
                sortCompareResult = new SortCompareResult(SortConst.STATUS_OK, SortConst.CODE_OK, SortConst.BLANK);
                setBoard.toOK();
            }
            else
            {
                sortCompareResult = new SortCompareResult(SortConst.STATUS_NG, setBoard.getBoardNgCode(), setBoard.getBoardNgMsg());
            }
        }
        catch (Exception ex)
        {
            if (ex instanceof SortException)
            {
                sortCompareResult = new SortCompareResult(SortConst.STATUS_NG, ((SortException) ex).getCode(), ex.getMessage());
                setBoard.toNG(sortCompareResult.getNgCode(), sortCompareResult.getNgMsg());
            }
            else
            {
                log.error(ex.getMessage(), ex);
                sortCompareResult = new SortCompareResult(SortConst.STATUS_NG, SortConst.CODE_NG, ex.getMessage());
                setBoard.toNG(sortCompareResult.getNgCode(), sortCompareResult.getNgMsg());
            }
        }
        sortCompareResult.setPlan(plan);
        sortCompareResult.setSortSwitch(sortSwitch);
        sortCompareResult.setSetBoard(setBoard);
        Collection<PCSBoard> pcsArr = pcsBoards.values();
        if (!setBoard.isOK())
        {
            pcsArr.forEach(pcsBoard -> pcsBoard.setEnableFlag(BoardConst.FLAG_N));
        }
        sortCompareResult.setPcsBoards(pcsArr);
        this.eventPublisher.publishEvent(new SortCompareResultEvent(sortCompareResult));
        return sortCompareResult;
    }

    /**
     * 是否跳过比对
     *
     * @param srcCategory 源板件类别
     * @param srcPanel 源板件信息
     * @param sortSwitch 分选开关
     * @param rowType 行类型
     * @param sortRule 分选规则
     * @return 是否跳过比对
     */
    private boolean isSkipCompare(String srcCategory, JSONObject srcPanel, JSONObject sortSwitch, String rowType, SortSplitRule.Item sortRule)
    {
        if (sortSwitch.containsKey(sortRule.getCode()))
        {
            String sortVal = sortSwitch.getString(sortRule.getCode());
            boolean checkDataType = false;
            if (sortVal != null)
            {
                List<String> boardCategories = Arrays.asList(sortVal.split(","));
                // 如果规则要求是有码板件，然后指定数据板件类型不为空且不为ALL且与规则要求板件类型不匹配，则跳过
                checkDataType = !ObjectUtils.isEmpty(sortVal) // 分选规则值不为空
                        && !"ALL".equals(sortVal) && !boardCategories.isEmpty() // 分选规则值不为ALL 且 分选规则值不为空
                        && (boardCategories.contains(SortConst.BOARD_CATEGORY_SET) || boardCategories.contains(SortConst.BOARD_CATEGORY_PCS)) // 分选规则值包含SET或PCS
                        && !boardCategories.contains(srcCategory);
            }
            // 如果规则要求是有码板件，然后是无码板件，则跳过
            boolean hasQR = sortRule.getRequiredBarcode() != null && sortRule.getRequiredBarcode() && "None".equals(rowType);
            // 周期或批次比对时，CustomerQRC1和CustomerQRC2相等时，则跳过 -- 淮安鹏鼎项目 added by jay-y 2024/05/22
            boolean isCycleOrBatch = (sortRule.getCode().endsWith(SortConst.CYCLE_PERIOD)  // 周期比对
                    || sortRule.getCode().endsWith(SortConst.BATCH_NUMBER)) // 批次比对
                    && !ObjectUtils.isEmpty(srcPanel) // 源板件不为空
                    && srcPanel.getString(SortConst.CCD_CONTENT_SET_CUSTOMER_QRC1) != null && srcPanel.getString(SortConst.CCD_CONTENT_SET_CUSTOMER_QRC1).equals(srcPanel.getString(SortConst.CCD_CONTENT_SET_CUSTOMER_QRC2)) // CustomerQRC1和CustomerQRC2相等
                    ;
            return hasQR || checkDataType || isCycleOrBatch;
        }
        return true;
    }
}
