package com.api.dcs.project.fjrm.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 主任务调度逻辑
 * 1.根据条件创建主任务
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/fjrm/intefTask")
public class DcsFjrmIntefTaskController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.根据条件创建主任务
    @Transactional
    @RequestMapping(value = "/DcsFjrmIntefTaskCreate", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsFjrmIntefTaskCreate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/fjrm/intefTask/DcsFjrmIntefTaskCreate";
        String transResult="";
        String errorMsg="";
        String userID="-1";
        try{
            //1.获取参数
            userID=jsonParas.getString("userID");
            //格式化数据
            long taskIdSeq = cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_intef_task_id_seq", false);
            //任务来源 DATA_SOURCES：MES-MES系统、OWN-自身维护、RGV-RGV触发
            String task_from=jsonParas.getString("task_from");
            //自动生成任务号
            String task_num = task_from+CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss");
            //任务方式 TASK_WAY：(自动/半自动/手动)
            String task_way="AUTO";
            //任务类型 APS_TASK_TYPE：
            // WASTE_BOX_IN_TASK-空废料框入库
            // WASTE_BOX_OUT_TASK-空废料框出库
            // FULL_IN_TASK-满框入库
            // FULL_OUT_TASK-满框出库任务
            // MOVE_TASK-倒垛
            String task_type=jsonParas.getString("task_type");
            //库区域
            String ware_house=jsonParas.getString("ware_house");
            String from_stock_code=jsonParas.getString("from_stock_code");
            String to_stock_code=jsonParas.getString("to_stock_code");
            String lot_num=jsonParas.getString("lot_num");//批次号
            String material_code=jsonParas.getString("material_code");//物料编码
            String width=jsonParas.getString("width");//总重量(KG)
            String error_min=jsonParas.getString("error_min");//化学成分异常最小值
            String error_max=jsonParas.getString("error_max");//化学成分异常最大值
            String task_order=String.valueOf(taskIdSeq);//任务排序
            //任务状态 PROD_TASK_STATUS PLAN-计划、WORK-进行中、FINISH-完成、CANCEL-取消
            String task_status=jsonParas.getString("task_status");
            String wharf_code=jsonParas.getString("wharf_code");//码头编码
            String waste_box_code=jsonParas.getString("waste_box_code");//废料框编码
            if (from_stock_code == null || "".equals(from_stock_code)) from_stock_code = "";
            if (to_stock_code == null || "".equals(to_stock_code)) to_stock_code = "";
            if (error_min == null ||"".equals(error_min)) error_min = "0";
            if (error_max == null ||"".equals(error_max)) error_max = "0";
            //新增
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            String insertSql = "INSERT INTO b_dcs_wms_intef_task (created_by, creation_date, " +
                    "task_id, task_num, task_from, task_way, task_type, ware_house, " +
                    "from_stock_code, to_stock_code, lot_num, material_code, width, " +
                    "error_min, error_max, task_order, task_status, wharf_code, waste_box_code) " +
                    "VALUES ('" + userID + "','" + nowDateTime + "','" + taskIdSeq + "','" +
                    task_num + "','" +
                    task_from+ "','" +
                    task_way + "','" +
                    task_type + "','" +
                    ware_house + "','" +
                    from_stock_code + "','" +
                    to_stock_code + "','" +
                    lot_num + "','" +
                    material_code + "'," +
                    width+ "," +
                    error_min + "," +
                    error_max + "," +
                    task_order + ",'" +
                    task_status+ "','" +
                    wharf_code+ "','" +
                    waste_box_code + "')";
            cFuncDbSqlExecute.ExecUpdateSql(userID, insertSql, true, request, apiRoutePath);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null, task_num,"",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "根据条件创建主任务发生异常:"+ex;
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
