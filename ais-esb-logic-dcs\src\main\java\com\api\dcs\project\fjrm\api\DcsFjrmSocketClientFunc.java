package com.api.dcs.project.fjrm.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 福建瑞闽公共方法
 * 1.W20A1 调运计划回应报文(暂时不用)
 * 2.W20A2 删除调运计划回应报文(暂时不用)
 * 3.W20C1 化学成分请求报文(暂时不用)
 *
 * 4.W20D1 废料库存数据报文
 * 5.W20S1 调运命令状态报文
 * 6.W20B1 回应物料请求总计划
 * 7.W20B2 发送废料框待出库计划
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
@Slf4j
public class DcsFjrmSocketClientFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    //1.Socket客户端发送事件-内容
    //event：事件名称
    //content：内容
    public JSONObject SendSocketClient(String event, String content) throws Exception{
        String errorMsg="";
        JSONObject jbMapCell=null;
        try{
            // 服务端socket.io连接通信地址
            String url = "http://127.0.0.1:8888";

            jbMapCell=new JSONObject();
            jbMapCell.put("status","OK");
            jbMapCell.put("event","");
            jbMapCell.put("content","");
        }
        catch (Exception ex){
            errorMsg="获取Socket客户端发送事件-内容异常:"+ex;
            throw new Exception(errorMsg);
        }
        return jbMapCell;
    }

}
