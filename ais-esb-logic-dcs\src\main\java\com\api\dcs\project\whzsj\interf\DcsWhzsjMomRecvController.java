package com.api.dcs.project.whzsj.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Slf4j
@RequestMapping("/dcs/project/whzsj/interf/mom/recv")
public class DcsWhzsjMomRecvController {
    @Resource
    private DcsWhzsjMomRecvPlanListFunc dcsWhzsjMomRecvPlanListFunc;
    @Resource
    private DcsWhzsjMomRecvPlanMoveFunc dcsWhzsjMomRecvPlanMoveFunc;
    @Resource
    private DcsWhzsjMomRecvPlanCutFunc dcsWhzsjMomRecvPlanCutFunc;

    @Resource
    private CFuncLogInterf cFuncLogInterf;

    //1.MOM计划：到货清单、移库计划、切割计划
    @RequestMapping("/recWhzsjMomPlan")
    public String recWhzsjMomPlan(@RequestBody JSONObject jsonObject) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        //解析数据
        String interNo = jsonObject.getString("interNo");//接口编号
        //到货清单(IF_CMWH_GBKWMS_MOM_01)
        //移库计划下发接口(IF_CMWH_GBKWMS_MOM_02)
        //切割计划下发接口(IF_CMWH_GBKWMS_MOM_03)
        JSONObject jbResult = null;
        if("IF_CMWH_GBKWMS_MOM_01".equals(interNo)){
            jbResult = dcsWhzsjMomRecvPlanListFunc.receiveMomPlanList(jsonObject);
        }
        else if("IF_CMWH_GBKWMS_MOM_02".equals(interNo)){
            jbResult = dcsWhzsjMomRecvPlanMoveFunc.receiveMomPlanMove(jsonObject);
        }
        else if("IF_CMWH_GBKWMS_MOM_03".equals(interNo)){
            jbResult = dcsWhzsjMomRecvPlanCutFunc.receiveMomPlanCut(jsonObject);
        }
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        Integer code = jbResult.getInteger("code");
        cFuncLogInterf.Insert(startDate, endDate, "recWhzsjMomPlan",
                false, null, jsonObject.toJSONString(), jbResult.toJSONString(),
                Integer.valueOf(0).equals(code) ? true : false, jbResult.getString("msg"), null);
        return jbResult.toJSONString();
    }
}
