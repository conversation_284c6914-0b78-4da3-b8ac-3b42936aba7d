package com.api.pack.core.op;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * Pile堆叠逻辑
 * 1.保存与判断堆叠
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-29
 */
@RestController
@Slf4j
@RequestMapping("/pack/core/op")
public class PackCoreOpPileController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.打包前校验PLC传入的SetSNList
    @RequestMapping(value = "/PackCoreOpSetSNListCheck", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String PackCoreOpSetSNListCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pack/core/op/PackCoreOpSetSNListCheck";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String meArrayTable = "a_pack_me_array";
        String mePileTable = "a_pack_me_pile";
        String apsPlanTable = "a_pack_aps_plan";
        String result = "";//model_type + "," + pile_ng_code + "," + pile_ng_msg + "," + pile_index + "," + pile_status + "," + plan_finish_flag
        try {
            String user_name = "";
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String pile_barcode = jsonParas.getString("pile_barcode");
            String sumSunValues = jsonParas.getString("sumSunValues");
            String plcFinishType = jsonParas.getString("plcFinishType");//完板类型，1正常完板，2强制完板，强制完板不需要进行板件数量校验
            String plcStackQtyValue = jsonParas.getString("plcStackQtyValue");//打包数量
            int pile_index = 0;
            String pile_status = "NG";
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String lot_num = "";
            String model_type = "";
            Integer unit_count = 0;//当前工作中订单单包set数量
            Integer xout_act_num = 0;//写入当前包内单SET板件最大的XOUT数量
            String batch_no="";
            Document plan = null;//当前工作中的订单
            int pile_ng_code = 1;//1:ok；2、数据重复；3、长度异常；4、PLC和PC数据校验异常；5、数据量异常；6：其他，
            String pile_ng_msg = "";
            String plan_finish_flag = "0";
            int plcStackQtyValueInt = 0;
            if (plcStackQtyValue != null && !plcStackQtyValue.equals("")) {
                plcStackQtyValueInt = Integer.parseInt(plcStackQtyValue);
            }

            //0.获取当前工作订单信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            MongoCursor<Document> iterator = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            if (iterator.hasNext()) {
                plan = iterator.next();
                unit_count = plan.getInteger("unit_count");
                model_type = plan.getString("model_type");
                lot_num = plan.getString("lot_num");
                batch_no=plan.getString("batch_no");
                iterator.close();
            }

            String[] setSnArray = sumSunValues.split(",");
            //1.数据校验
            //1.1.判断setSn集合是否为空
            if (setSnArray == null || setSnArray.length == 0) {
                pile_ng_code = 5;
//                pile_ng_msg = "传入SetSn集合为空";
                // 英化
                pile_ng_msg = "The incoming SetSn collection is empty";
                result = model_type + "," + pile_ng_code + "," + pile_ng_msg + "," + pile_index + "," + pile_status + "," +
                        plan_finish_flag + "," + batch_no + "," + xout_act_num;
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            // 判断PLC堆叠数量与实际数量是否一致 added by jay-y 2024/08/17
            if (plcStackQtyValueInt != setSnArray.length) {
                pile_ng_code = 5;
                pile_ng_msg = "PLC给出堆叠数量{" + plcStackQtyValue + "}与实际SetSn集合不一致";
            }

            //1.2.判断setSn数量是否一致
            if (plcFinishType.equals("1")) {
                if (plcStackQtyValueInt != unit_count) {
                    pile_ng_code = 5;
//                    pile_ng_msg = "PLC给出堆叠数量{" + plcStackQtyValue + "}与设定SetSn集合不一致";
                    // 英化
                    pile_ng_msg = "The stack quantity given by PLC {" + plcStackQtyValue + "} is inconsistent with the set SetSn collection";
                }
            }

            //1.3.判断setSn是否在a_pack_me_array，并且状态是OK,并需要判定是否绑定pile_barcode，如果存在表示已经被打包，不能继续使用，需要先解绑
            String sqlSortCount = "select count(1) from a_pack_fmod_sort " +
                    "where enable_flag='Y' and sort_flag='Y' and sort_code='SetComparison'";
            Integer sortCount = cFuncDbSqlResolve.GetSelectCount(sqlSortCount);

            List<String> setSnListPC = new ArrayList<>();
            String pc_array_list = "";
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.addCriteria(Criteria.where("pile_use_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("unbind_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
            iterator = mongoTemplate.getCollection(meArrayTable).find(queryBigData.getQueryObject()).limit(plcStackQtyValueInt).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            while (iterator.hasNext()) {
                Document arrayDoc = iterator.next();
                String board_sn = arrayDoc.getString("board_sn");
                setSnListPC.add(board_sn);
                if (pc_array_list.equals("")) pc_array_list = board_sn;
                else pc_array_list = pc_array_list + "," + board_sn;
            }
            if (iterator.hasNext()) iterator.close();
            Set<String> set = new HashSet<>();
            if (setSnArray != null && setSnArray.length > 0) {
                for (int i = 0; i < setSnArray.length; i++) {
                    String setSn = setSnArray[i];
                    if (setSn.equals("@NC@")) continue;
                    if (!set.add(setSn) && pile_ng_code == 1) {
                        pile_ng_code = 2;
//                        pile_ng_msg = "PLC给出的堆叠SetSn{" + setSn + "}存在重复项";
                        // 英化
                        pile_ng_msg = "The stack SetSn given by PLC {" + setSn + "} has duplicate items";
                    }
                    if (setSn.length() != 14 && pile_ng_code == 1) {
                        pile_ng_code = 3;
//                        pile_ng_msg = "PLC给出的堆叠SetSn{" + setSn + "}长度不为14位校验失败";
                        // 英化
                        pile_ng_msg = "The length of the stack SetSn given by PLC {" + setSn + "} is not 14 digits and the verification fails";
                    }
                    //开启校验
                    if (sortCount > 0) {
                        if (!setSnListPC.contains(setSn) && pile_ng_code == 1) {
                            pile_ng_code = 4;
//                            pile_ng_msg = "PLC反馈SET数据和PC计划SET数据校验异常";
                            // 英化
                            pile_ng_msg = "PLC feedback SET data and PC plan SET data verification abnormal";
                        }
                    }
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("board_sn").is(setSn));
                    queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                    queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
                    iterator = mongoTemplate.getCollection(meArrayTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (!iterator.hasNext()) {
                        if (pile_ng_code == 1) {
                            pile_ng_code = 3;
//                            pile_ng_msg = "PLC给出的堆叠SetSN{" + setSn + "}未通过OK校验";
                            // 英化
                            pile_ng_msg = "The stack SetSN given by PLC {" + setSn + "} did not pass the OK verification";
                        }
                    } else {
                        Document arrayDoc = iterator.next();
                        String array_barcode = arrayDoc.getString("array_barcode");//对应set码
                        String pile_barcode_array = arrayDoc.getString("pile_barcode");//绑定的pile_barcode
                        int xout_act_num_set = arrayDoc.getInteger("xout_act_num");//xout_act_num
                        if (xout_act_num_set > xout_act_num) xout_act_num = xout_act_num_set;
                        iterator.close();

                        if (pile_barcode_array != null && !pile_barcode_array.equals("")) {
                            if (pile_ng_code == 1) {
                                pile_ng_code = 2;
//                                pile_ng_msg = "PLC给出的堆叠SetSN{" + setSn + "}已经被包{" + pile_barcode_array + "}绑定，不能重复使用";
                                // 英化
                                pile_ng_msg = "The stack SetSN given by PLC {" + setSn + "} has been bound by package {" + pile_barcode_array + "} and cannot be reused";
                            }
                        }
                    }
                }
            }

            //2.获取当前用户信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //3.获取当前最大pile_index
            queryBigData = new Query();
            queryBigData.with(Sort.by(Sort.Direction.DESC, "pile_index"));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            iteratorBigData = mongoTemplate.getCollection(mePileTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                pile_index = docItemBigData.getInteger("pile_index");
                iteratorBigData.close();
            }

            //4.保存pile信息，并更新array上pile_barcode信息
            if (setSnArray != null && setSnArray.length > 0) {
                if(pile_ng_code==1) {
                    //校验OK才做数据绑定
                    for (int i = 0; i < setSnArray.length; i++) {
                        String setSn = setSnArray[i];
                        //修改array对应的pile_barcode和解绑状态为N
                        Query query = new Query();
                        query.addCriteria(Criteria.where("board_sn").is(setSn));
                        query.addCriteria(Criteria.where("enable_flag").is("Y"));
                        query.addCriteria(Criteria.where("pile_use_flag").is("N"));
                        query.addCriteria(Criteria.where("array_status").is("OK"));
                        Update updateBigData = new Update();
                        updateBigData.set("pile_barcode", pile_barcode);
                        updateBigData.set("pile_use_flag", "Y");
                        mongoTemplate.updateMulti(query, updateBigData, meArrayTable);
                    }
                }
                //新增pile数据
                List<Map<String, Object>> lstPileSets = new ArrayList<>();
                Map<String, Object> mapQDataItem = new HashMap<>();
                mapQDataItem.put("item_date", item_date);
                mapQDataItem.put("item_date_val", item_date_val);
                mapQDataItem.put("pile_id", CFuncUtilsSystem.CreateUUID(true));
                mapQDataItem.put("pile_barcode", pile_barcode);
                mapQDataItem.put("custom_barcode", "");
                mapQDataItem.put("lot_num", lot_num);
                mapQDataItem.put("model_type", model_type);
                mapQDataItem.put("pile_index", pile_index + 1);
                mapQDataItem.put("array_count", setSnArray.length);
                mapQDataItem.put("pc_array_list", pc_array_list);
                mapQDataItem.put("plc_array_list", sumSunValues);
                mapQDataItem.put("trunk_flag", "N");
                mapQDataItem.put("pile_user", user_name);
                if (pile_ng_code == 1) pile_status = "OK";
                mapQDataItem.put("pile_status", pile_status);
                mapQDataItem.put("pile_ng_code", pile_ng_code);
                mapQDataItem.put("pile_ng_msg", pile_ng_msg);
                mapQDataItem.put("pile_ng_msg", pile_ng_msg);
                mapQDataItem.put("enable_flag", "Y");
                mapQDataItem.put("unbind_flag", "N");
                mapQDataItem.put("unbind_user", "");
                mapQDataItem.put("unbind_time", "");
                mapQDataItem.put("unbind_way", "");
                lstPileSets.add(mapQDataItem);
                if (lstPileSets.size() > 0) {
                    mongoTemplate.insert(lstPileSets, mePileTable);
                }
            }
            //5.修改任务信息中的 获取所有ok与ng的pile信息 finish_pile_count 完成打包数量 finish_lable_count 打包次数
            Query lotNumQuery = new Query().addCriteria(Criteria.where("lot_num").is(lot_num));
            List<Map> piles = mongoTemplate.find(lotNumQuery, Map.class, mePileTable);
            Update updateBigData = null;
            if (!CollectionUtils.isEmpty(piles) && plan != null) {
                updateBigData = new Update();
                //完成打包次数
                Long count = piles.stream().filter(f -> {
                    return f.containsKey("pile_status") ? f.get("pile_status").equals("OK") : false;
                }).count();
                //完成打包数量
                Long sum = piles.stream().mapToLong(m -> {
                    if (m.containsKey("array_count") && m.containsKey("pile_status") && m.get("pile_status").equals("OK")) {
                        return Long.parseLong(m.get("array_count").toString());
                    }
                    return 0;
                }).sum();

                updateBigData.set("finish_pile_count", sum);
                updateBigData.set("finish_lable_count", count);
                //6.若是发现完成打包数量等于计划数量则将任务状态变更为 finish
                if (plan.containsKey("unit_count") && plan.containsKey("finish_pile_count") && plan.containsKey("plan_lot_count")) {
                    String task_start_time = plan.get("task_start_time").toString();
                    Integer plan_lot_count = Integer.parseInt(plan.get("plan_lot_count").toString());
                    Integer lable_count = plan_lot_count / unit_count;
                    lable_count = (plan_lot_count % unit_count) != 0 ? lable_count += 1 : lable_count;
                    if (lable_count.equals(count.intValue())) {
                        updateBigData.set("lot_status", "FINISH");
                        String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                        updateBigData.set("task_end_time", task_end_time);
                        long cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                        updateBigData.set("task_cost_time", String.valueOf(cost_time));
                        plan_finish_flag = "1";
                    }
                }
            }

            if (updateBigData != null) {
                Query query = new Query();
                query.addCriteria(Criteria.where("lot_num").is(lot_num));
                query.addCriteria(Criteria.where("lot_status").is("WORK"));
                mongoTemplate.updateFirst(query, updateBigData, apsPlanTable);
            }
            result = model_type + "," + pile_ng_code + "," + pile_ng_msg + "," + pile_index + "," + pile_status + "," +
                    plan_finish_flag + "," + batch_no + "," + xout_act_num;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "打包前校验PLC传入的SetSNList发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
