package com.api.pack.core.fmod;

import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 出货地明细
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-12
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("a_pack_fmod_recipe_print_label_detail")
public class FmodRecipeShipAddressDetail implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @JsonProperty("shipaddress_detail_id")
    @JSONField(name = "shipaddress_detail_id")
    @TableId(value = "shipaddress_detail_id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "出货地ID")
    @JsonProperty("shipaddress_id")
    @JSONField(name = "shipaddress_id")
    @TableField(value = "shipaddress_id")
    private Long parentId;

    @ApiModelProperty(value = "序号")
    @JsonProperty("index")
    @JSONField(name = "index")
    @TableField(value = "index")
    private Integer index;

    @ApiModelProperty(value = "分割字段名称")
    @JsonProperty("split_name")
    @JSONField(name = "split_name")
    @TableField(value = "split_name")
    private String splitName;

    @ApiModelProperty("反转标识")
    @JsonProperty("reversed_flag")
    @JSONField(name = "reversed_flag")
    @TableField("reversed_flag")
    private String reversedFlag;

    @ApiModelProperty(value = "分割起始位置")
    @JsonProperty("split_index")
    @JSONField(name = "split_index")
    @TableField(value = "split_index")
    private String splitIndex;

    @ApiModelProperty(value = "分割长度")
    @JsonProperty("split_length")
    @JSONField(name = "split_length")
    @TableField(value = "split_length")
    private String splitLength;

    @ApiModelProperty(value = "分割比对函数")
    @JsonProperty("split_compare_func")
    @JSONField(name = "split_compare_func")
    @TableField(value = "split_compare_func")
    private String splitCompareFunc;

    @ApiModelProperty(value = "MES数据来源")
    @JsonProperty("mes_from")
    @JSONField(name = "mes_from")
    @TableField(value = "mes_from")
    private String mesFrom;

    @ApiModelProperty(value = "MES数据来源字段")
    @JsonProperty("mes_field")
    @JSONField(name = "mes_field")
    @TableField(value = "mes_field")
    private String mesField;

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @TableField(exist = false)
    private String mesFieldValue;

    @ApiModelProperty(value = "MES数据分割起始位置")
    @JsonProperty("mes_split_index")
    @JSONField(name = "mes_split_index")
    @TableField(value = "mes_split_index")
    private String mesSplitIndex;

    @ApiModelProperty(value = "MES数据分割长度")
    @JsonProperty("mes_split_length")
    @JSONField(name = "mes_split_length")
    @TableField(value = "mes_split_length")
    private String mesSplitLength;

    @ApiModelProperty("可用标志")
    @JsonProperty("enable_flag")
    @JSONField(name = "enable_flag")
    @TableField("enable_flag")
    private String enableFlag;

    public static List<FmodRecipeShipAddressDetail> listByParentId(Long parentId, FmodRecipeShipAddressDetailMapper mapper)
    {
        QueryWrapper<FmodRecipeShipAddressDetail> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(Const.PROPERTY_ENABLE_FLAG,  Const.FLAG_Y);
        queryWrapper.eq("shipaddress_id", parentId);
        return mapper.selectList(queryWrapper);
    }

    public String splitData(String data)
    {
        return this.reverseData(this.split(data, splitIndex, splitLength));
    }

    public String splitMesData(String data)
    {
        return this.split(data, mesSplitIndex, mesSplitLength);
    }

    private String split(String data, String index, String length)
    {
        if (ObjectUtils.isEmpty(index) || ObjectUtils.isEmpty(length))
        {
            return data;
        }
        if (ObjectUtils.isEmpty(data))
        {
            return "";
        }
        try
        {
            int i = Integer.parseInt(index);
            int l = Integer.parseInt(length);
            if (l == 0)
            {
                return data;
            }
            if (i < 0 || i >= data.length())
            {
                return "";
            }
            if (i + l > data.length())
            {
                return data.substring(i);
            }
            return data.substring(i, i + l);
        }
        catch (Exception e)
        {
            return data;
        }
    }

    public String reverseData(String data)
    {
        if (ObjectUtils.isEmpty(data))
        {
            return "";
        }
        if (Const.FLAG_Y.equals(reversedFlag))
        {
            return new StringBuilder(data).reverse().toString();
        }
        return data;
    }
}
