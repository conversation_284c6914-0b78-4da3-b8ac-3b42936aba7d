package com.api.pack.core.board;

import com.api.pack.core.sort.SortCompareRule;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BoardCompareRule extends SortCompareRule
{
    private String sourceProperty; // 板件比对规则源属性

    public BoardCompareRule(String name, String targetValue)
    {
        super(name, targetValue);
    }
}
