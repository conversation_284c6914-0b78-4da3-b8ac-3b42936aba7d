package com.api.eap.project.zbxc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbMongoIndex;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 1.淄博芯材(配板机)MongoDB索引创建
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */

@Service
@Slf4j
public class EapZbXcMongoIndexFunc {
    @Autowired
    private CFuncDbMongoIndex cFuncDbMongoIndex;

    //珠海崇达系列表索引
    @Async
    public void CreateEapZbXcIndex() {
        CreateEapPbjApsPlanIndex();
        CreateEapPbjApsPlanDIndex();
        CreateEapPbjMeArrayIndex();
    }

    //创建a_eap_pbj_aps_plan动态索引
    @Async
    public void CreateEapPbjApsPlanIndex() {
        String tableName = "a_eap_pbj_aps_plan";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "plan_id","task_from", "lot_num",
                "model_type", "lot_level", "lot_status", "left_on_flag", "right_on_flag", "enable_flag"};
        int keep_days = 360;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_pbj_aps_plan索引成功");
            else log.warn("创建Mongo表a_eap_pbj_aps_plan索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_pbj_aps_plan索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_pbj_aps_plan_d动态索引
    @Async
    public void CreateEapPbjApsPlanDIndex() {
        String tableName = "a_eap_pbj_aps_plan_d";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "plan_d_id","plan_id", "product_no", "enable_flag",
                         "scan_check_flag","top_level","bot_level"};
        int keep_days = 360;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_pbj_aps_plan_d索引成功");
            else log.warn("创建Mongo表a_eap_pbj_aps_plan_d索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_pbj_aps_plan_d索引失败:" + ex.getMessage());
        }
    }

    //创建a_eap_pbj_me_array动态索引
    @Async
    public void CreateEapPbjMeArrayIndex() {
        String tableName = "a_eap_pbj_me_array";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "me_array_id","plan_id", "lot_num",
                                         "lot_level","port_index","task_index","set_ng_code","panel_top_barcode"};
        int keep_days = 360;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表a_eap_pbj_me_array索引成功");
            else log.warn("创建Mongo表a_eap_pbj_me_array索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表a_eap_pbj_me_array索引失败:" + ex.getMessage());
        }
    }
}
