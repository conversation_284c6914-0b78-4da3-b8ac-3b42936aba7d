package com.api.pmc.core.andon;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 安灯接口
 * 1.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/andon")
public class PmcCoreAndonController {
    @Autowired
    private PmcCoreAndonBase pmcCoreAndonBase;

    //1.安灯按钮事件新增
    @Transactional
    @RequestMapping(value = "/PmcAndonBtnEventIns", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcAndonBtnEventIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/andon/PmcAndonBtnEventIns";
        String selectResult="";
        String errorMsg="";
        List<Map<String, Object>> itemListMo=null;
        try{
            log.info("--------------------[PmcAndonEventIns]安灯按钮事件新增开始---------------------");
            log.info("接收参数："+jsonParas.toString());

            String jobject=jsonParas.getString("jobject");//传入值
            if(jobject!=null && !jobject.equals("")) {
                JSONObject jsonObject= JSONObject.parseObject(jobject);
                if(jsonObject!=null){
                    String btn_tag_id="";//按钮TAG_ID
                    String btn_tag_val="";//Tag值
                    JSONArray btnArray=jsonObject.getJSONArray("andon_data");
                    if(btnArray != null && btnArray.size()>0) {
                        for (int i = 0; i < btnArray.size(); i++) {
                            JSONObject btnObject = btnArray.getJSONObject(i);
                            btn_tag_id = btnObject.getString("btn_tag_id");
                            btn_tag_val=btnObject.getString("btn_tag_val");

                            pmcCoreAndonBase.AndonBtnEventIns(request,apiRoutePath,btn_tag_id,btn_tag_val);
                        }
                    }
                }
            }

            log.info("--------------------[PmcAndonEventIns]安灯按钮事件新增结束---------------------");

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListMo,"","",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "安灯按钮事件新增异常"+ex.getMessage();

            log.info("--------------------[PmcAndonEventIns]安灯按钮事件新增异常信息："+errorMsg);
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.安灯工位牌事件新增
    @Transactional
    @RequestMapping(value = "/PmcAndonCardEventIns", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcAndonCardEventIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/andon/PmcAndonCardEventIns";
        String selectResult="";
        String errorMsg="";
        List<Map<String, Object>> itemListMo=null;
        try{
            log.info("--------------------[PmcAndonCardEventIns]安灯工位牌事件新增开始---------------------");
            log.info("接收参数："+jsonParas.toString());

            String jobject=jsonParas.getString("jobject");//传入值
            if(jobject!=null && !jobject.equals("")) {
                JSONObject jsonObject= JSONObject.parseObject(jobject);
                if(jsonObject!=null){
                    String card_tag_id="";//工位牌TAG_ID
                    String card_tag_val="";//Tag值
                    JSONArray cardArray=jsonObject.getJSONArray("andon_data");
                    if(cardArray != null && cardArray.size()>0) {
                        for (int i = 0; i < cardArray.size(); i++) {
                            JSONObject cardObject = cardArray.getJSONObject(i);
                            card_tag_id = cardObject.getString("card_tag_id");
                            card_tag_val=cardObject.getString("card_tag_val");

                            pmcCoreAndonBase.AndonCardEventIns(request,apiRoutePath,card_tag_id,card_tag_val);
                        }
                    }
                }
            }

            log.info("--------------------[PmcAndonCardEventIns]安灯工位牌事件新增结束---------------------");

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListMo,"","",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "安灯工位牌事件新增异常"+ex.getMessage();

            log.info("--------------------[PmcAndonCardEventIns]安灯工位牌事件新增异常信息："+errorMsg);
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}

