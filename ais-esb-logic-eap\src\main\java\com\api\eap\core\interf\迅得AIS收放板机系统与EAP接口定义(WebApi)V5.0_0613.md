**迅得AIS收放板机系统与EAP接口定义**

**WebApi方式**

作 者：周俊

公 司：迅得机械

建档日期：2021-01-06

上次更新：2022-06-13

版 本：5.0

## 文档控制

**更改记录**

| 日期  | 作者  | 版本  | 备注  |
| --- | --- | --- | --- |
|     |     |     |     |
| --- | --- | --- | --- |
| 2021/1/6 | 周俊  | 1.0 | 编写初始版本 |
| 2022/1/17 | 周俊  | 2.0 | 增加EAP下发报警信息到AIS并显示在HMI |
| 2022/3/16 | 周俊  | 3.0 | EAP下发任务中增加panel_level层级 |
| 2022/4/13 | 周俊  | 4.0 | 增加AIS与EAP的心跳检测 |
| 2022/6/13 | 周俊  | 5.0 | 修改路由地址 |
|     |     |     |     |

**复核**

| 职位  | 姓名  |
| --- | --- |
|     |     |
| --- | --- |
|     |     |
|     |     |
|     |     |
|     |     |
|     |     |
|     |     |

目 录

文档控制 2

1\. 文档概述 4

2\. 约定 5

2.1 接口参数约定 5

2.2 基础访问地址 8

3\. 接口清单 9

4\. 接口内容 11

4.1 员工登入验证(EAP发布) 11

4.2 AIS请求搬入载具(EAP发布) 12

4.3 AIS上报AGV安全验证进度(EAP发布) 12

4.4 AIS主动请求任务(EAP发布) 13

4.5 AIS接受EAP下发任务(AIS发布) 14

4.6 AIS任务取消(AIS发布) 15

4.7 AIS申请允许放板(EAP发布) 16

4.8 EAP通知允许/不允许放板(AIS发布) 16

4.9 AIS主动询问PanelID合格标志(EAP发布) 17

4.10 AIS实时上报PanelID事件(EAP发布) 18

4.11 AIS完板上报事件(EAP发布) 19

4.12 AIS请求退载具(EAP发布) 20

4.13 AIS上报设备状态(EAP发布) 21

4.14 AIS上报设备报警信息(EAP发布) 21

4.15 AIS接受远程控制启动与停止(AIS发布) 22

4.16 AIS接受远程控制HMI文本显示(AIS发布) 23

4.17 AIS通讯状态测试(AIS发布) 23

4.18 EAP通讯状态测试(EAP发布) 24

4.19 首板通过请求放行(EAP发布) 25

4.20 收板机先进后出请求切换工单号(AIS发布) 25

## 文档概述

本文档为迅得AIS收放板机系统(以下简称AIS)与EAP/MES(以下简称EAP)进行业务数据传输与回传接口规范文档，AIS系统与EAP/MES系统对接时需按照本文档的规范进行接口开发配置。

## 约定

AIS系统与EAP系统相互集成接口统一采用Restful WebApi接口方式，请求方式为POST，请求和响应的主体是JSON结构，使用的编码是UTF-8；具体定义如下所示：

| 接口方式 | Restful WebApi，基于Http协议开发 |
| --- | --- |
| 请求方式 | POST |
| Content-Type | application/json |
| 编码规则 | UTF-8 |
| **安全验证** |     |
| 请求方IP地址 | 通过解析HttpRequest获取请求方IP地址，系统判断请求方IP地址是否在白名单 |

### 2.1 接口参数约定

1. 输入参数：即接口请求时输入参数，指接口调用时的输入参数，采用JsonObject格式，参数如果为主从表，则采用Json嵌套数组结构；
2. 返回值参数：即接口调用返回值参数，指接口调用时的返回值，采用JsonObject格式；
3. 输入参数和返回值参数中的属性名均使用小写字母，无大写；

以下是针对AIS系统与EAP系统接口开发标准基本参数规范；

**AIS系统标准基本参数定义：**

1.1 输入参数定义

| 参数名 | 参数描述 | 数据类型 | 是否必填 |
| --- | --- | --- | --- |
| request_uuid | 请求码，长度 32 位的 GUID，每个请求具有唯<br><br>一编号。由请求方随机生成。示例<br><br>“253704d8-17d9-47a3-bafc-8239da5948<br><br>e0”，用于追溯单次请求记录； | string | 是   |
| request_time | 请求时间，字符串类型，格式为 yyyy-MM-dd<br><br>HH:mm:ss；填写时间主要为了考虑一些产生的数据请求并非实时数据； | string | 是   |
| sys_name | 数据来源系统(填写EAP系统名称如EAP) | string | 是   |
| 其他参数 | 依据实际业务进行定义 |     |     |

1.2 返回参数定义

| 参数名 | 参数描述 | 数据类型 | 是否必填 |
| --- | --- | --- | --- |
| response_uuid | 与请求码request_uuid相同，为了确保请求数据与返回数据是同一个接口事务 | string | 是   |
| response_time | 回应时间，字符串类型，格式为 yyyy-MM-dd<br><br>HH:mm:ss；填写时间主要为了考虑一些产生的数据回复时并非实时数据； | string | 是   |
| code | 返回代码,0代表正常，其他代表异常 | int | 是   |
| msg | 返回消息,如果正常可以为空，如果异常需要异常消息 | string | 否   |
| 其他参数 | 依据实际业务进行定义 |     |     |

1.3 异常码定义

| 异常码 | 异常码描述 |
| --- | --- |
| \-1 |     |
| \-2 |     |
| \-3 |     |
| \-4 |     |
| \-5 |     |
| \-6 |     |
| \-7 |     |
| \-8 |     |
| \-9 |     |
| \-10 |     |
| \-11 |     |
| \-12 |     |
| \-13 |     |
| \-14 |     |
| \-15 |     |
| \-16 |     |
| \-17 |     |
| \-99 | 系统未知异常,try catch捕捉exception |

**EAP系统标准基本参数定义(EAP的标准规格只是用于参考，实际可以按照EAP的规格来)：**

2.1 输入参数定义

| 参数名 | 参数描述 | 数据类型 | 是否必填 |
| --- | --- | --- | --- |
| request_uuid | 请求码，长度 32 位的 GUID，每个请求具有唯<br><br>一编号。由请求方随机生成。示例<br><br>“253704d8-17d9-47a3-bafc-8239da5948<br><br>e0”，用于追溯单次请求记录； | string | 是   |
| request_time | 请求时间，字符串类型，格式为 yyyy-MM-dd<br><br>HH:mm:ss；填写时间主要为了考虑一些产生的数据请求并非实时数据； | string | 是   |
| sys_name | 数据来源系统(填写AIS系统名称如AIS) | string | 是   |
| user_id | 当前设备登入用户名 | string | 否   |
| station_code | 工位编号/设备资产编号 | string | 是   |
| 其他参数 | 依据实际业务进行定义 |     | 否   |
|     |     |     |     |

2.2 返回参数定义

| 参数名 | 参数描述 | 数据类型 | 是否必填 |
| --- | --- | --- | --- |
| response_uuid | 与请求码request_uuid相同，为了确保请求数据与返回数据是同一个接口事务 | string | 是   |
| response_time | 回应时间，字符串类型，格式为 yyyy-MM-dd<br><br>HH:mm:ss；填写时间主要为了考虑一些产生的数据回复时并非实时数据； | string | 是   |
| code | 返回代码,0代表正常，其他代表异常 | int | 是   |
| msg | 返回消息,如果正常可以为空，如果异常需要异常消息 | string | 否   |
| 其他参数 | 依据实际业务进行定义 |     |     |

2.3 异常码定义

| 异常码 | 异常码描述 |
| --- | --- |
| \-1 |     |
| \-2 |     |
| \-3 |     |
| \-4 |     |
| \-5 |     |
| \-6 |     |
| \-7 |     |
| \-8 |     |
| \-9 |     |
| \-99 | 系统未知异常,try catch捕捉exception |

### 2.2 基础访问地址

|     | 参数  | 参数描述 |
| --- | --- | --- |
| AIS系统 | base_url | <http://ip:9090/aisEsbApi/eap/core/interf> |
| ip  | 系统上线后才能给出 |
| port | 9090 |
| EAP系统 | base_url | EAP定义 |
| ip  | EAP定义 |
| port | EAP定义 |

## 接口清单

### 本章节定义AIS系统以及EAP系统需要开发配置的接口清单如下

| 序号  | 场景  | 接口名称 | 接口提供方 | 接口调用方 |
| --- | --- | --- | --- | --- |
| 1   | 扫描员工号进行验证返回员工信息 | 员工登入验证 | EAP系统 | AIS系统 |
| 2   | 当收放板机端口缺载具时调用EAP此接口上报 | AIS请求搬入载具 | EAP系统 | AIS系统 |
| 3   | AGV搬入搬出进行安全验证状态与进度进行上报，调用EAP此接口 | AIS上报AGV安全验证进度 | EAP系统 | AIS系统 |
| 4   | 放板机/收板机载入完成后主动向EAP申请任务 | AIS主动请求任务 | EAP系统 | AIS系统 |
| 5   | 放板机与收板机可以接受EAP下发生产任务 | AIS接受EAP下发任务 | AIS系统 | EAP系统 |
| 6   | 当EAP判定需要远程取消任务时可以调用AIS此接口 | AIS任务取消 | AIS系统 | EAP系统 |
| 7   | 放板机或者收板机载入完成后，可向EAP进行申请是否允许放板 | AIS申请允许放板 | EAP系统 | AIS系统 |
| 8   | AIS申请批次开始或者是否可以开始放板，EAP给与反馈接口 | EAP通知允许/不允许放板 | AIS系统 | EAP系统 |
| 9   | 当需要由EAP判断Panel合格标志时，收放板机每次抓取都可以调用此接口由EAP判断 | AIS主动询问PanelID合格标志 | EAP系统 | AIS系统 |
| 10  | 每放一块板或者收一块板时，AIS实时将Panel信息上传到EAP系统 | AIS实时上报PanelID事件 | EAP系统 | AIS系统 |
| 11  | 当放板完成或者收板完成(正常/异常情况下，都会将放板结果与完板接口上报给EAP系统) | AIS完板上报事件 | EAP系统 | AIS系统 |
| 12  | AIS请求EAP退载具 | AIS请求退载具 | EAP系统 | AIS系统 |
| 13  | AIS上报设备状态 | AIS主动将设备实时状态进行上报给EAP | EAP系统 | AIS系统 |
| 14  | AIS上报设备报警 | AIS主动将设备实时报警信息进行上报给EAP | EAP系统 | AIS系统 |
| 15  | AIS接受远程控制启动与停止 | AIS接受远程控制启动与停止 | AIS系统 | EAP系统 |
| 16  | AIS接受来自EAP发送HMI显示文本 | AIS接受HMI显示文本显示 | AIS系统 | EAP系统 |
| 17  | EAP可以定时检测AIS工位是否在线 | AIS通讯连接测试 | AIS系统 | EAP系统 |
| 18  | AIS定期检测EAP在线状态，以及获取EAP服务器时间进行更新AIS本地 | EAP通讯连接测试 | EAP系统 | AIS系统 |
| 19  | 首板通过请求放行 | 当首板完成请求EAP是否允许开始投板 | EAP系统 | AIS系统 |
| 20  | 收板机先进后出请求工单号 | 收板机先进后出请求工单号 | AIS系统 | EAP系统 |

## 接口内容

### 4.1 员工登入验证(EAP发布)

接口描述：当开启员工登入验证时，员工在AIS系统输入员工号或者扫描员工号，AIS系统调用EAP系统

校验员工身份信息并返回员工信息显示到人机界面；

| 提供方 | EAP系统 |     |     |     |     |
| --- | --- |     |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |     |
| 地址  | base_url/checkUserInfo |     |     |     |     |
| 类型  | 参数名 |     | 数据类型 | 是否可为空 | 描述  |
|     | request_uuid |     | string | N   | 请求GUID |
| request_time |     | string | N   | 请求时间 |
| sys_name |     | string | N   | 请求系统名称：AIS |
| station_code |     | string | N   | 工位号/设备编号(唯一) |
| user_id |     | string | N   | 员工号 |
| 应答  | response_uuid |     | string | N   | 返回请求GUID |
| response_time |     | string | N   | 应答时间 |
| code |     | int | N   | 返回代码 |
| msg |     | string | Y   | 消息/异常描述 |
| user_info |     | jsonobject | Y   |     |
|     | nick_name | string | Y   | 别名  |
|     | dept_id | string | Y   | 部门  |
|     | shift_id | string | Y   | 班次  |

### 4.2 AIS请求搬入载具(EAP发布)

接口描述：AIS与EAP联机模式下，当放板机或者收板机端口缺载具时，AIS系统调用EAP系统该

接口，请求EAP呼叫AGV配送载具到对应端口【AGV模式下】；

| 提供方 | EAP系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |
| 地址  | base_url/callPalletEvent |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
|     | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | 请求系统名称：AIS |
| user_id | string | Y   | 员工号 |
| station_code | string | N   | 工位号/设备编号 |
| port_code | string | N   | 端口号 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.3 AIS上报AGV安全验证进度(EAP发布)

接口描述：当为AGV模式下，放板机与收板机与AGV进行安全验证，放板机与收板机将安全验证进度

上报到EAP，用于安全监控；

| 提供方 | EAP系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |
| 地址  | base_url/upAgvSaftCheckStatusEvent |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | 请求系统名称：AIS |
| user_id | string | Y   | 员工号 |
| station_code | string | N   | 工位号/设备编号 |
| port_code | string | N   | 端口号 |
| move_type | string | N   | AGV移动类型<br><br>IN:搬入载具<br><br>OUT:搬出载具 |
| check_status | string | N   | 安全验证进度<br><br>1：AGV请求进入<br><br>2：设备允许进入<br><br>3：AGV完成任务后离开<br><br>4：结束安全验证流程 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.4 AIS主动请求任务(EAP发布)

接口描述：当AIS与EAP联机模式下,放板机或者收板机(可根据实际情况开启对应配置)在载具载入完成后，扫描载具条码/无载具条码/天盖条码/手工工单直接请求EAP下发生产任务到AIS；

| 提供方 | EAP系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |
| 地址  | base_url/askDownTaskEvent |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | 请求系统名称：AIS |
| user_id | string | Y   | 员工号 |
| station_code | string | N   | 工位号/设备编号 |
| port_code | string | N   | 端口号 |
| request_barcode | string | Y   | 请求条码信息 |
| request_type | string | N   | 请求任务条码类型：<br><br>PALLET：载具条码<br><br>AGV：AGV任务号<br><br>TRAY：天盖条码<br><br>LOT：手工工单 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.5 AIS接受EAP下发任务(AIS发布)

接口描述：当AIS主动申请任务，或者EAP也可以提前将生产任务下发到放板机或者收板机，由放板机或者收板机按照队列进行生产；这个接口集中考虑了一车一批，一车多批的情况；但是每次只能接受载具的任务；

| 提供方 | AIS系统 |     |     |     |     |
| --- | --- |     |     |     |     | --- | --- | --- |
| 调用方 | EAP系统 |     |     |     |     |
| 地址  | base_url/EapCoreInterfRecvTask |     |     |     |     |
| 类型  | 参数名 |     | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid |     | string | N   | 请求GUID |
| request_time |     | string | N   | 请求时间 |
| sys_name |     | string | N   | EAP系统名称EAP |
| station_code |     | string | N   | 工位号/设备编号 |
| group_lot_num |     | string | Y   | 母批号 |
| pallet_num |     | string | Y   | 载具条码 |
| top_tray_num |     | string | Y   | 天盖条码 |
| port_code |     | string | Y   | 端口号 |
| pallet_type |     | string | Y   | 载具类型 |
| product_type |     | string | Y   | 生产模式 (1:dummy(陪镀板）;0:生产板) |
| inspect_count |     | int | N   | 首件数量,>0则代表需要首检 |
| panel_model |     | int | N   | 是否扫描板件,1扫描,0不扫描 |
| task_list |     | jsonarray | N   | 任务集合 |
|     | lot_num | string | N   | 批次号 |
|     | short_lot_num | string | Y   | 批次简码(AIS也可以通过简码判断板件OK/NG) |
|     | material_code | string | Y   | 物料编码 |
|     | lot_index | int | N   | 排序  |
|     | plan_count | int | N   | 任务数量 |
|     | pallet_length | float | N   | 板长  |
|     | pallet_width | float | N   | 板宽  |
|     | pallet_height | float | Y   | 板厚  |
|     | between_times | int | N   | 间隔时间(单位秒),0为不设定 |
|     | panel_list | string | Y   | PanelList条码集合，以,分割 |
|     | lot_level_list | string | Y   | 层级集合，以,分割 |
|     | item_info | string | Y   | 属性参数，如需要下发到下游设备的属性参数集合，用json表示 |
|     | attribute1 | string | Y   | 预留1 |
|     | attribute2 | string | Y   | 预留2 |
|     | attribute3 | string | Y   | 预留2 |
| 应答  | response_uuid |     | string | N   | 返回请求GUID |
| response_time |     | string | N   | 应答时间 |
| code |     | int | N   | 返回代码 |
| msg |     | string | Y   | 消息/异常描述 |

### 4.6 AIS任务取消(AIS发布)

接口描述：EAP可以提前将AIS未生产任务进行取消；

| 提供方 | AIS系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | EAP系统 |     |     |     |
| 地址  | base_url/EapCoreInterfCancelTask |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | EAP系统名称EAP |
| station_code | string | N   | 工位号/设备编号 |
| group_lot_num | string | Y   | 母批号 |
| lot_num | string | N   | 批次号，如有批量则用,分割 |
| cancel_msg | string | Y   | 取消原因 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.7 AIS申请允许放板(EAP发布)

接口描述：当载具载入完成后，收放板机判断具备条件，若在EAP联机且受控EAP控制放板或者收板时，调用此接口请求EAP是否允许放板或者收板；当然此接口也可以被认为是上报收放板机选择了那个任务；

| 提供方 | EAP系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |
| 地址  | base_url/askEapAllowStartEvent |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | 请求系统名称：AIS |
| user_id | string | Y   | 员工号 |
| station_code | string | N   | 工位号/设备编号 |
| port_code | string | N   | 端口号 |
| group_lot_num | string | Y   | 母批号 |
| lot_num | string | Y   | 批次号,若有多个则用,分割 |
| request_barcode | string | Y   | 请求条码信息 |
| request_type | string | N   | 请求任务条码类型：<br><br>PALLET：载具条码<br><br>AGV：AGV任务号<br><br>TRAY：天盖条码<br><br>LOT：手工工单 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.8 EAP通知允许/不允许放板(AIS发布)

接口描述：放板机请求EAP是否允许放板，EAP通知AIS允许放板或者不允许放板或者一些错误指令

| 提供方 | AIS系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | EAP系统 |     |     |     |
| 地址  | base_url/EapCoreInterfRecvAllowWork |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | 请求系统名称：EAP |
| station_code | string | N   | 工位号/设备编号 |
| port_code | string | N   | 端口号 |
| allow_code | int | N   | 允许代码：1允许放板/2主制程未准备好/3任务取消/4需要等待/5其他 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.9 AIS主动询问PanelID合格标志(EAP发布)

接口描述：当AIS与EAP联机模式，且EAP强制要求每片板子在过放板机或者收板机的时候都需要受控于EAP，由EAP定义放板或者收板是到那个端口，是OK/NG；

| 提供方 | EAP系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |
| 地址  | base_url/checkPanelStatusEvent |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | 请求系统名称：AIS |
| user_id | string | Y   | 员工号 |
| station_code | string | N   | 工位号/设备编号 |
| port_code | string | Y   | 端口号 |
| group_lot_num | string | Y   | 母批号 |
| lot_num | string | Y   | 批次号 |
| short_lot_num | string | Y   | 批次简码 |
| pallet_num | string | Y   | 载具条码 |
| top_tray_num | string | Y   | 天盖条码 |
| tray_num | string | Y   | Tray盘码 |
| panel_index | int | N   | 板件序号 |
| panel_barcode | string | Y   | panel条码 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |
| panel_result | int | N   | panel校验结果<br><br>针对放板机：0OK,1混板,2读码异常,3重码,4强制越过,5其他<br><br>针对收板机：1-10为端口号,11混板,12读码异常,13重码,14强制越过,15其他 |

### 4.10 AIS实时上报PanelID事件(EAP发布)

接口描述：当AIS与EAP联机模式，且EAP要求AIS每抓一片板子或者放一片板子都需要实时上报给EAP时，则需要执行此接口；

| 提供方 | EAP系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |
| 地址  | base_url/panelResultUpEvent |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | 请求系统名称：AIS |
| user_id | string | Y   | 员工号 |
| station_code | string | N   | 工位号/设备编号 |
| port_code | string | Y   | 端口号 |
| offline_flag | string | N   | Y为离线补报,N为实时上报 |
| group_lot_num | string | Y   | 母批号 |
| lot_num | string | Y   | 批次号 |
| short_lot_num | string | Y   | 批次简码 |
| pallet_num | string | Y   | 载具条码 |
| td_tray_num | string | Y   | 天盖/地盖条码 |
| tray_num | string | Y   | Tray盘码 |
| panel_barcode | string | Y   | panel条码(可为空) |
| panel_status | string | N   | 判定结果(OK/NG/NG_PASS) |
| panel_code | int | N   | 上报扫描代码<br><br>0合格,1混板,2读码异常,3重码,4强制越过,5其他 |
| panel_msg | string | Y   | 上报扫描代码描述 |
| panel_index | int | N   | 序号  |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.11 AIS完板上报事件(EAP发布)

接口描述：当AIS与EAP联机模式，AIS完成放板或者收板时，将panel明细信息上报给EAP；

| 提供方 | EAP系统 |     |     |     |     |     |
| --- | --- |     |     |     |     |     | --- | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |     |     |
| 地址  | base_url/taskTrackFinishEvent |     |     |     |     |     |
| 类型  | 参数名 |     |     | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid |     |     | string | N   | 请求GUID |
| request_time |     |     | string | N   | 请求时间 |
| sys_name |     |     | string | N   | 请求系统名称：AIS |
| user_id |     |     | string | Y   | 员工号 |
| station_code |     |     | string | N   | 工位号/设备编号 |
| port_code |     |     | string | N   | 端口号 |
| offline_flag |     |     | string | N   | Y为离线补报,N为实时上报 |
| group_lot_num |     |     | string | Y   | 母批号 |
| pallet_num |     |     | string | Y   | 载具条码 |
| td_tray_num |     |     | string | Y   | 天盖/地盖条码 |
| lot_wip_list |     |     | jsonarray | Y   | 工单完工集合 |
|     | lot_num |     | string | Y   | 批次号 |
|     | short_lot_num |     | string | Y   | 批次简码 |
|     | plan_count |     | int | N   | 计划数量 |
|     | finish_count |     | int | N   | 完成数量 |
|     | finish_ok_count |     | int | N   | 完成OK数量 |
|     | finish_ng_count |     | int | N   | 完成NG数量 |
|     | panel_list |     | jsonarray | Y   | 完成集合 |
|     |     | panel_barcode | string | N   | panel条码，若无则虚拟条码 |
|     |     | panel_status | string | N   | panel合格标志(OK/NG/NG_PASS) |
|     |     | panel_index | int | N   | 序号  |
|     |     | panel_time | string | N   | panel时间(YYYY-MM-DD HH:MM:SS) |
|     |     | panel_code | int | N   | panel代码<br><br>0合格,1混板,2读码异常,3重码,4强制越过,5其他 |
|     |     | panel_msg | string | Y   | panel代码描述 |
|     |     | tray_num | string | Y   | Tray盘条码 |
|     |     | panel_user_id | string | Y   | 操作者 |
| 应答  | response_uuid |     |     | string | N   | 返回请求GUID |
| response_time |     |     | string | N   | 应答时间 |
| code |     |     | int | N   | 返回代码 |
| msg |     |     | string | Y   | 消息/异常描述 |

### 4.12 AIS请求退载具(EAP发布)

接口描述：当AIS与EAP联机模式，AIS请求EAP退载具；

| 提供方 | EAP系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |
| 地址  | base_url/askPalletMoveOutEvent |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | 请求系统名称：AIS |
| user_id | string | Y   | 员工号 |
| station_code | string | N   | 工位号/设备编号 |
| port_code | string | Y   | 端口号 |
| group_lot_num | string | Y   | 母批号 |
| lot_num | string | Y   | 批次号,若有多个则用,分割 |
| pallet_num | string | Y   | 载具条码 |
| td_tray_num | string | Y   | 天盖/地盖条码 |
| out_code | int | N   | 退载具代码:<br><br>1正常退、2少板退、3多板退、4强制退、5其他退 |
| out_msg | string | Y   | 退载具代码描述 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.13 AIS上报设备状态(EAP发布)

接口描述：当AIS设备状态发生变更时上报设备状态到EAP；

| 提供方 | EAP系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |
| 地址  | base_url/upDeviceStatusEvent |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | 请求系统名称：AIS |
| user_id | string | Y   | 员工号 |
| station_code | string | N   | 工位号/设备编号 |
| device_status | string | N   | 设备状态(IDLE空闲，BUSY 忙碌，ALARM 故障，STOP 暂停，KEEP保养、RUN运行) |
| control_model | string | N   | 设备模式(ON 自动,OFF 手动) |
| cim_model | string | N   | 作业模式(LOCAL本地、REMOTE远程联机) |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.14 AIS上报设备报警信息(EAP发布)

接口描述：当AIS设备发生设备报警时,上报到EAP；

| 提供方 | EAP系统 |     |     |     |     |
| --- | --- |     |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |     |
| 地址  | base_url/upDeviceAlarmEvent |     |     |     |     |
| 类型  | 参数名 |     | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid |     | string | N   | 请求GUID |
| sys_name |     | string | N   | 请求系统名称：AIS |
| user_id |     | string | Y   | 员工号 |
| station_code |     | string | N   | 工位号/设备编号 |
| alarm_list |     | jsonarray | N   | 报警集合 |
|     | alarm_code | string | N   | 报警代码 |
|     | alarm_msg | string | N   | 报警文本 |
|     | alarm_level | int | N   | 报警级别 |
|     | alarm_time | string | N   | 报警/复位时间 |
|     | alarm_value | string | N   | 报警值,ON为发生,OFF为复位 |
| 应答  | response_uuid |     | string | N   | 返回请求GUID |
| response_time |     | string | N   | 应答时间 |
| code |     | int | N   | 返回代码 |
| msg |     | string | Y   | 消息/异常描述 |

### 4.15 AIS接受远程控制启动与停止(AIS发布)

接口描述：当EAP识别到生产故障或者需要HOLD生产时，可以控制收放板机停止放板与收板；

| 提供方 | AIS系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | EAP系统 |     |     |     |
| 地址  | base_url/EapCoreInterfControlStop |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | EAP系统名称EAP |
| station_code | string | N   | 工位号/设备编号 |
| control_value | string | N   | 控制命令(RUN/STOP) |
| control_msg | string | Y   | 请求备注 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.16 AIS接受远程控制HMI文本显示(AIS发布)

接口描述：当EAP需要推送消息到收放板机HMI显示时,可以调用此接口，AIS接受后会发送到HMI进行文本显示或者弹框显示；

| 提供方 | AIS系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | EAP系统 |     |     |     |
| 地址  | base_url/EapCoreInterfRecvHmiShow |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | EAP系统名称EAP |
| station_code | string | N   | 工位号/设备编号 |
| hmi_msg_code | int | N   | 文本显示代码 |
| hmi_msg | string | Y   | 文本显示内容 |
| hmi_dlg_flag | string | N   | 是否弹窗等待人工确认(Y/N) |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |

### 4.17 AIS通讯状态测试(AIS发布)

接口描述：EAP可以定期调用此接口测试EAP与AIS的通讯状态；

| 提供方 | AIS系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | EAP系统 |     |     |     |
| 地址  | base_url/EapCoreInterfConnTest |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | EAP系统名称EAP |
| station_code | string | N   | 工位号/设备编号 |
| token | string | Y   | 备用标识参数 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |
| server_time | string | N   | AIS本地时间，格式为 yyyy-MM-dd HH:mm:ss |

### 4.18 EAP通讯状态测试(EAP发布)

接口描述：AIS可以定期调用此接口测试EAP与AIS的通讯状态,同时获取EAP的服务器时间更新同步本地时间

| 提供方 | EAP系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |
| 地址  | base_url/eapConnTest |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | AIS系统名称AIS |
| user_id | string | Y   | 员工号 |
| station_code | string | N   | 工位号/设备编号 |
| token | string | Y   | 备用标识参数 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |
| server_time | string | N   | EAP服务器时间，格式为 yyyy-MM-dd HH:mm:ss |

### 4.19 首板通过请求放行(EAP发布)

接口描述：当AIS首检完成，调用此接口询问EAP是否首检OK，首检OK则执行批次开始，NG则继续进行首检

| 提供方 | EAP系统 |     |     |     |     |
| --- | --- |     |     |     |     | --- | --- | --- |
| 调用方 | AIS系统 |     |     |     |     |
| 地址  | base_url/inspectConfirmEvent |     |     |     |     |
| 类型  | 参数名 |     | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid |     | string | N   | 请求GUID |
| request_time |     | string | N   | 请求时间 |
| sys_name |     | string | N   | AIS系统名称AIS |
| user_id |     | string | Y   | 员工号 |
| station_code |     | string | N   | 工位号/设备编号 |
| group_lot_num |     | string | Y   | 母批号 |
| inspect_count |     | int | N   | 首检数量 |
| inspect_list |     | jsonarray | N   | 首检集合 |
|     | lot_num | string | Y   | 批次号 |
|     | panel_barcode | string | N   | panel条码，若无则虚拟条码 |
|     | inspect_time | string | N   | 放行时间 |
| 应答  | response_uuid |     | string | N   | 返回请求GUID |
| response_time |     | string | N   | 应答时间 |
| code |     | int | N   | 返回代码 |
| msg |     | string | Y   | 消息/异常描述 |
| inspect_check_result |     | int | N   | EAP判定首检结果：1合格,2不合格需要继续首检,3不合格但是不需要继续首检 |

### 4.20 收板机先进后出请求切换工单号(AIS发布)

接口描述：当收板机先进后出时，开放接口允许系统控制切换工单进行收板

| 提供方 | AIS系统 |     |     |     |
| --- | --- |     |     |     | --- | --- | --- |
| 调用方 | EAP系统 |     |     |     |
| 地址  | base_url/EapCoreInterfUnLoadChangeLot |     |     |     |
| 类型  | 参数名 | 数据类型 | 是否可为空 | 描述  |
| 请求  | request_uuid | string | N   | 请求GUID |
| request_time | string | N   | 请求时间 |
| sys_name | string | N   | AIS系统名称AIS |
| station_code | string | N   | 工位号/设备编号 |
| group_lot_num | string | Y   | 母批号 |
| lot_num | string | N   | 批次号,若有多个则用,分割；若强制结束当前批次，则给0 |
| 应答  | response_uuid | string | N   | 返回请求GUID |
| response_time | string | N   | 应答时间 |
| code | int | N   | 返回代码 |
| msg | string | Y   | 消息/异常描述 |