package com.api.eap.project.zbxc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 淄博芯材EAP发送接口方法内容
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-10
 */
@Service
@Slf4j
public class EapZbXcSendInterfFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapZbXcInterfCommon eapZbXcInterfCommon;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    //[EAP]设备初始化建立通讯请求上报
    public JSONObject eapZbXcSendAreYouThereReply(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="AreYouThere";
        String esbInterfCode="EapAreYouThere";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String s = cFuncUtilsCellScada.WriteTagByStation("", station_code, "SCADA_BEAT/LoadEap", "0", true);
            String station_id=jsonParas.getString("station_id");
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,null,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
//            Integer code=jbBackResult.getInteger("Code");
//            String msg=jbBackResult.getString("MessageCH");
            Integer code=200;
            String msg="成功";
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次通讯心跳成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]设备切换CIM状态上报
    public JSONObject eapZbXcSendEQPCommunicationStatusReport(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="EQPCommunicationStatusReport";
        String esbInterfCode="EapEQPCommunicationStatusReport";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String cim_value=jsonParas.getString("cim_value");//1EAP模式,0AIS模式
//            String communicationStatus="CIMOff";
//            if(cim_value.equals("1")) communicationStatus="CIMOn";
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("CommunicationStatus",cim_value);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次切换CIM状态成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]设备切换手动模式自动模式
    public JSONObject eapZbXcSendEQPRunningModeReport(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="EQPRunningModeReport";
        String esbInterfCode="EapEQPRunningModeReport";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String model_value=jsonParas.getString("model_value");//1自动,0手动
//            String mode="Manua";
//            if(model_value.equals("1")) model_value="Auto";
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("Status",model_value);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次手动自动状态成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]设备向EAP请求时间
    public JSONObject eapZbXcSendEQPDateTimeRequest(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="EQPDateTimeRequest";
        String esbInterfCode="EapEQPDateTimeRequest";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次时间请求成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]设备发生状态改变时调用
    public JSONObject eapZbXcSendEQPStatusChangeReport(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="EQPStatusChangeReport";
        String esbInterfCode="EapEQPStatusChangeReport";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String device_status=jsonParas.getString("device_status");//IDLE(待机)、RUN(运行)、DOWN(停机)、PM(保养)
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("Status",device_status);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次状态改变成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]设备发生报警时调用
    public JSONObject eapZbXcSendEQPAlarmReport(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="EQPAlarmReport";
        String esbInterfCode="EapEQPAlarmReport";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String alarm_id=jsonParas.getString("alarm_id");
            String alarm_flag=jsonParas.getString("alarm_flag");
            String alarm_value=jsonParas.getString("alarm_value");
            String alarm_desc=jsonParas.getString("alarm_desc");
            if(alarm_desc==null)  alarm_desc="";
            String alarm_level="2";
            String alarm_status="0";
            if(alarm_flag.equals("Y")) alarm_level="1";
            if(alarm_value.equals("1")) alarm_status="1";
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("AlarmID",alarm_id);
            jbBody.put("AlarmLevel",alarm_level);
            jbBody.put("AlarmStatus",alarm_status);
            jbBody.put("AlarmText",alarm_desc);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次设备报警成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]取片事件报告
    public JSONObject eapZbXcSendFetchOutJobEventReport(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="FetchOutJobEventReport";
        String esbInterfCode="EapFetchOutJobEventReport";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String port_code=jsonParas.getString("port_code");
            String status=jsonParas.getString("status");
            String pallet_num=jsonParas.getString("pallet_num");
            String lot_num=jsonParas.getString("lot_num");
            String panel_barcode=jsonParas.getString("panel_barcode");
            if(pallet_num==null) pallet_num="";
            if(lot_num==null) lot_num="";
            if(panel_barcode==null) panel_barcode="";
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("PortID",port_code);
            jbBody.put("CarrierID",pallet_num);
            jbBody.put("PanelID",panel_barcode);
            jbBody.put("StripID","");
            jbBody.put("FetchOutTime",CFuncUtilsSystem.GetMongoDataValue(CFuncUtilsSystem.GetMongoISODate(""))+"");
            jbBody.put("Result",status);
            jbBody.put("LotID",lot_num);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次取片事件成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]放片事件报告
    public JSONObject eapZbXcSendStoreInJobEventReport(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="StoreInJobEventReport";
        String esbInterfCode="EapStoreInJobEventReport";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String port_code=jsonParas.getString("port_code");
            String status=jsonParas.getString("status");
            String pallet_num=jsonParas.getString("pallet_num");
            String lot_num=jsonParas.getString("lot_num");
            String panel_barcode=jsonParas.getString("panel_barcode");
            if(pallet_num==null) pallet_num="";
            if(lot_num==null) lot_num="";
            if(panel_barcode==null) panel_barcode="";
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("PortID",port_code);
            jbBody.put("CarrierID",pallet_num);
            jbBody.put("StripID","");
            jbBody.put("StoreInTime",CFuncUtilsSystem.GetMongoDataValue(CFuncUtilsSystem.GetMongoISODate(""))+"");
            jbBody.put("PanelID",panel_barcode);
            jbBody.put("Result",status);
            jbBody.put("LotID",lot_num);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次放片事件成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]收放板机工位状态报告
    public JSONObject eapZbXcSendCarrierStatusChangeReport(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="CarrierStatusChangeReport";
        String esbInterfCode="EapCarrierStatusChangeReport";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String port_code=jsonParas.getString("port_code");
            String port_status=jsonParas.getString("port_status");//1.空、2.载入完成、3.等待开始命令、4.生产中、5.完工、6.任务取消、7.任务中止、8.卸料、9.首件完成
            String pallet_num=jsonParas.getString("pallet_num");
            String lot_num=jsonParas.getString("lot_num");
            String finish_count=jsonParas.getString("finish_count");//完成数量
            String finish_panel_list=jsonParas.getString("finish_panel_list");//完成panel集合,用逗号隔开

            if(pallet_num==null) pallet_num="";
            if(lot_num==null) lot_num="";
            if(finish_count==null || finish_count.equals("")) finish_count="0";
            if(finish_panel_list==null) finish_panel_list="";
            Integer finish_count_int=Integer.parseInt(finish_count);
            JSONArray pnlList=new JSONArray();
            if(!finish_panel_list.equals("")){
                log.debug("finish_panel_list：{}",finish_panel_list);
                String[] tempList=finish_panel_list.split(",",-1);
                if(tempList!=null && tempList.length>0){
                    for(int i=0;i<tempList.length;i++){
                        String panel_barcode=tempList[i];
                        JSONObject jbItem=new JSONObject();
                        jbItem.put("PanelID",panel_barcode);
                        pnlList.add(jbItem);
                    }
                }
            }
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("PortID",port_code);
            jbBody.put("PortStatus",port_status);
            jbBody.put("LotID",lot_num);
            jbBody.put("CarrierID",pallet_num);
            jbBody.put("PanelQTY",finish_count_int);
            jbBody.put("PanelList",pnlList);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次工位状态{"+port_status+"}成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]收放板机搬运模式上报
    public JSONObject eapZbXcSendPortTransferModeChangeReport(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="PortTransferModeChangeReport";
        String esbInterfCode="EapPortTransferModeChangeReport";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String port_code=jsonParas.getString("port_code");
            String auto_flag=jsonParas.getString("auto_flag");
            String portTransferMode="Manual";
            if(auto_flag.equals("Y")) portTransferMode="Auto";
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("PortID",port_code);
            jbBody.put("PortTransferMode",portTransferMode);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次搬运模式切换事件成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //[EAP]Panel信息请求
    public JSONObject eapZbXcSendPanelInformationRequest(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="PanelInformationRequest";
        String esbInterfCode="EapPanelInformationRequest";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String pallet_num=jsonParas.getString("pallet_num");
            String lot_num=jsonParas.getString("lot_num");
            String panel_barcode=jsonParas.getString("panel_barcode");
            if(pallet_num==null) pallet_num="";
            if(lot_num==null) lot_num="";
            if(panel_barcode==null) panel_barcode="";
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("CarrierID",pallet_num);
            jbBody.put("PanelID",panel_barcode);
            jbBody.put("LotID",lot_num);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次Panel信息请求成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
    //[EAP]定时采集总耗电量上报
    public JSONObject eapZbXcSendEQPDataCollectionReport(JSONObject jsonParas) throws Exception{
        JSONObject jbResult=null;
        String errorMsg="";
        String funcName="EQPDataCollectionReport";
        String esbInterfCode="EQPDataCollectionReport";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        try{
            String station_code=jsonParas.getString("station_code");
            String station_id=jsonParas.getString("station_id");
            String power_umption=jsonParas.getString("power_umption");
            String sqlStation="select station_id,COALESCE(station_attr,'') station_attr,station_des " +
                    "from sys_fmod_station where station_code='"+station_code+"'";
            List<Map<String, Object>> lstStation=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlStation,false,null,null);
            Assert.notEmpty(lstStation, "工位配置异常");
            Map<String, Object> stationInfo = lstStation.stream().findFirst().get();
            String stationDes = stationInfo.get("station_des").toString();
            //1.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jbBody=new JSONObject();
            jbBody.put("ProductNO", "");
            jbBody.put("LotID", "");
            JSONArray objects = new JSONArray();
            JSONObject parameterList = new JSONObject();
            parameterList.put("Name",stationDes+"当前总耗电量");
            parameterList.put("Value",power_umption);
            objects.add(parameterList);
            jbBody.put("ParameterList", objects);
            JSONObject postParas=eapZbXcInterfCommon.CreateSendParas(station_id,station_code,funcName,jbBody,
                    1,"","");
            requestParas=postParas.toString();
            JSONObject jsonObjectBack= eapZbXcInterfCommon.PostJbBackJb(esb_prod_intef_url,station_code,postParas);
            responseParas=jsonObjectBack.toString();
            JSONObject jbBackResult=jsonObjectBack.getJSONObject("Result");
            Integer code=jbBackResult.getInteger("Code");
            String msg=jbBackResult.getString("MessageCH");
            if(code!=1){
                errorMsg=msg;
                jbResult=new JSONObject();
                jbResult.put("isSaveFlag",true);
                jbResult.put("esbInterfCode",esbInterfCode);
                jbResult.put("token",token);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //成功
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送一次切换CIM状态成功");
        }
        catch (Exception ex){
            errorMsg="发生未知异常:"+ex.getMessage();
            jbResult=new JSONObject();
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
