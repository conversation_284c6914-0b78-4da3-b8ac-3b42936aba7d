package com.api.eap.project.zhcy;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 志圣-珠海超毅-EAP接收接口控制器
 * 1.初始化状态请求
 * 2.时间同步
 * 3.询问设备关键参数
 * 4.任务信息下发
 * 5.通知设备任务更改
 * 6.远程提示信息下达
 * 7.膜材料校验接口
 * </p>
 *
 * <AUTHOR>
 * @since
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/zhcy/interf/recv")
public class EapZhcyRecvInterfController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapZhcyRecvInterfFunc eapZhishengRecvInterfFunc;

    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //初始化状态请求
    @RequestMapping(value = "/EAP_InitialDataRequest", method = {RequestMethod.POST})
    public String EAP_InitialDataRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/recv/EAP_InitialDataRequest";
        return processRequest(jsonParas, request, "EAP_InitialDataRequest",apiRoutePath);
    }

    //时间同步
    @RequestMapping(value = "/EAP_DateTimeSyncCommand", method = {RequestMethod.POST})
    public String EAP_DateTimeSyncCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/recv/EAP_DateTimeSyncCommand";
        return processRequest(jsonParas, request, "EAP_DateTimeSyncCommand",apiRoutePath);
    }

    //任务信息下发
    @RequestMapping(value = "/EAP_JobDataDownload", method = {RequestMethod.POST})
    public String JobInfoDownload(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/recv/EAP_JobDataDownload";
        return processRequest(jsonParas, request, "EAP_JobDataDownload",apiRoutePath);
    }

    //通知设备任务更改
    @RequestMapping(value = "/EAP_JobDataModifyCommand", method = {RequestMethod.POST})
    public String EAP_JobDataModifyCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/recv/EAP_JobDataModifyCommand";
        return processRequest(jsonParas, request, "EAP_JobDataModifyCommand",apiRoutePath);
    }

    //远程提示信息下达
    @RequestMapping(value = "/EAP_CIMMessageCommand", method = {RequestMethod.POST})
    public String EAP_CIMMessageCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/zhcy/interf/recv/EAP_CIMMessageCommand";
        return processRequest(jsonParas, request, "EAP_CIMMessageCommand",apiRoutePath);
    }

    //统一处理请求
    private String processRequest(JSONObject jsonParas, HttpServletRequest request, String esbInterfCode,String apiRoutePath) {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        String ipAllowList= cFuncDbSqlResolve.GetParameterValue("EQP_IP_ALLOW_LIST");
        //如果ipAllowList为*，则不做IP限制,如果ipAllowList包涵当前IP，则不做IP限制，下面有bug，需要修改
        if (ipAllowList == null || ipAllowList.isEmpty()) {
            ipAllowList = "*";
        }
        String ip = getRequestIp(request);
        if (!ipAllowList.equals("*") && !ipAllowList.contains(ip)) {
        	String errorMsg ="IP"+ip+" not allowed,ipAllowList:"+ipAllowList;
        	log.error(errorMsg);
            //return "IP not allowed";
            return EapZhcyInterfCommon.CreateFaillResult(errorMsg,jsonParas.getString("RequestId"),"",jsonParas.toString()).toString();
        }
        JSONObject jbResult = eapZhishengRecvInterfFunc.processFunc(jsonParas, esbInterfCode, request,apiRoutePath);
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String token = "";
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("Success");
        String message = jbResult.getString("Msg");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        if (isSaveFlag ==null || isSaveFlag) {
        	if(!StringUtils.hasText(responseParas)) {
        		responseParas =jbResult.toString();
        	}
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, true, token,requestParas, responseParas, successFlag, message, request);
        }
        if(responseParas!=null) {
        	 return responseParas;
        }else {
        	jbResult.remove("isSaveFlag");
        	return jbResult.toString();
        }
    }
    /**
     * 获取请求用户的IP地址
     * @param request
     * @return
     */
    public static String getRequestIp(HttpServletRequest request) {
        String ip = request.getHeader("x-forwarded-for");
        // 处理多级代理，获取第一个非 unknown 的有效 IP
        if (ip != null && !ip.isEmpty() && !"unknown".equalsIgnoreCase(ip)) {
            ip = ip.split(",")[0].trim();  // 取第一个 IP
        } else {
            ip = request.getHeader("X-Real-IP");  // 常见于 Nginx 代理
        }
        // 备用方案：尝试从其他头部获取
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.isEmpty() || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("RemoteAddr");
        }
        return ip;
    }
}