package com.api.eap.project.xinai;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 芯爱曝光机接口处理
 * 1.接受LDI基板搬入模式通知
 * 2.接受LDI基板投入可否和2DC通知
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/xinai/interf/ldi/recv/RestAPI")
public class EapXinaiLdiRecvController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapXinaiLdiRecvFunc eapXinaiLdiRecvFunc;

    //1.接受LDI基板搬入模式通知
    @RequestMapping(value = "/PanelLoadingMode",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapXinaiLdiRecvPanelLoadingMode(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/xinai/interf/ldi/recv/PanelLoadingMode";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapXinaiLdiRecvFunc.PanelLoadingMode(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //2.接受LDI基板投入可否和2DC通知
    @RequestMapping(value = "/ForcedPanelLoading2DCReply",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapXinaiLdiRecvForcedPanelLoading2DCReply(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/xinai/interf/ldi/recv/ForcedPanelLoading2DCReply";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapXinaiLdiRecvFunc.ForcedPanelLoading2DCReply(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }
}
