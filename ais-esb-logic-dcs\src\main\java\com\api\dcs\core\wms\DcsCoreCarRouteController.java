package com.api.dcs.core.wms;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * WMS天车调度路线逻辑
 * 1.查询天车锁定调度路线
 * 2.根据路线ID获取状态
 * 3.修改天车调度路线状态
 * 4.查询路线任务状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/wms")
public class DcsCoreCarRouteController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsRouteCommonFunc dcsRouteCommonFunc;

    //1.查询天车锁定调度路线
    @RequestMapping(value = "/DcsCoreWmsCarRouteSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarRouteSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/wms/DcsCoreWmsCarRouteSel";
        String selectResult="";
        String errorMsg="";
        try{
            //1.获取参数
            String car_code=jsonParas.getString("car_code");
            String select_one_flag=jsonParas.getString("select_one_flag");//是否只选择单任务

            //2.查询任务
            String sqlLockCarRouteSel="select " +
                    "a.car_route_id,a.ware_house,a.car_task_type,a.step_code,a.step_name," +
                    "a.location_x,a.location_y,a.location_z,a.step_status," +
                    "COALESCE(a.step_start_limit_id,0) step_start_limit_id," +
                    "COALESCE(a.step_start_limit_step,'') step_start_limit_step," +
                    "COALESCE(a.step_start_limit_status,'') step_start_limit_status," +
                    "need_check_gd_flag,need_check_model_flag," +
                    "COALESCE(a.if_tags_list,'') if_tags_list," +
                    "COALESCE(a.if_tags_value,'') if_tags_value," +
                    "COALESCE(a.stock_count,0) stock_count," +
                    "COALESCE(a.check_gd_tag,'') check_gd_tag," +
                    "COALESCE(a.check_gd_value,'') check_gd_value," +
                    "COALESCE(a.check_model_tag,'') check_model_tag," +
                    "para_f,para_r,para_a,para_h," +
                    "COALESCE(b.car_task_id,0) car_task_id," +
                    "COALESCE(b.cycle_code,'') cycle_code," +
                    "COALESCE(b.task_id,'') task_id," +
                    "COALESCE(b.task_num,'') task_num," +
                    "COALESCE(b.task_type,'') task_type," +
                    "COALESCE(b.model_type,'') model_type," +
                    "COALESCE(b.from_stock_code,'') from_stock_code," +
                    "COALESCE(b.to_stock_code,'') to_stock_code," +
                    "COALESCE(b.execute_status,'') execute_status," +
                    "COALESCE(c.m_length,0) m_length," +
                    "COALESCE(c.m_width,0) m_width," +
                    "COALESCE(c.m_height,0) m_height," +
                    "COALESCE(c.m_weight,0) m_weight " +
                    "from b_dcs_wms_lock_car_route a left join b_dcs_wms_lock_car_task b " +
                    "on a.car_task_id=b.car_task_id left join b_dcs_fmod_model c " +
                    "on b.model_id=c.model_id " +
                    "where a.car_code='"+car_code+"'";
            if(select_one_flag!=null && select_one_flag.equals("Y")){
                 sqlLockCarRouteSel+=" and (a.step_status='PLAN' or a.step_status='WORK') " +
                         "order by a.car_route_id LIMIT 1 OFFSET 0";
            }
            else{
                sqlLockCarRouteSel+=" order by a.car_route_id";
            }
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlLockCarRouteSel,
                    false,request,apiRoutePath);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.根据路线ID获取状态
    @RequestMapping(value = "/DcsCoreWmsCarRouteSelByID", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarRouteSelByID(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/wms/DcsCoreWmsCarRouteSelByID";
        String selectResult="";
        String errorMsg="";
        try{
            //1.获取参数
            String car_route_id=jsonParas.getString("car_route_id");

            //2.查询任务
            String sqlLockCarRouteSel="select step_status " +
                    "from b_dcs_wms_lock_car_route "+
                    "where car_route_id="+car_route_id;
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlLockCarRouteSel,
                    false,request,apiRoutePath);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //3.修改天车调度路线状态
    @Transactional
    @RequestMapping(value = "/DcsCoreWmsCarRouteUpd", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarRouteUpd(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/wms/DcsCoreWmsCarRouteUpd";
        String transResult="";
        String errorMsg="";
        String userID="-1";
        try{
            //1.获取参数
            userID=request.getParameter("userID");
            String nowDateTime=CFuncUtilsSystem.GetNowDateTime("");
            //1.1 ROUTE参数
            String car_code=jsonParas.getString("car_code");
            String car_route_id=jsonParas.getString("car_route_id");
            String car_task_type=jsonParas.getString("car_task_type");
            String step_status=jsonParas.getString("step_status");
            //1.2 TASK参数
            String task_status_flag=jsonParas.getString("task_status_flag");//是否更新任务状态
            String finish_check_gd_flag=jsonParas.getString("finish_check_gd_flag");//是否检查了辊道
            String check_model_flag=jsonParas.getString("check_model_flag");//是否检查了机型
            String check_model_result=jsonParas.getString("check_model_result");//检查型号结果OK/NG
            String car_task_id=jsonParas.getString("car_task_id");
            if(car_task_id==null) car_task_id="";

            //2.先更新ROUTE状态
            if(!step_status.equals("")){
                String sqlRouteUpd="update b_dcs_wms_lock_car_route set " +
                        "last_updated_by='"+userID+"'," +
                        "last_update_date='"+nowDateTime+"'," +
                        "step_status='"+step_status+"' " +
                        "where car_route_id="+car_route_id;
                cFuncDbSqlExecute.ExecUpdateSql(userID,sqlRouteUpd,false,request,apiRoutePath);
            }

            //3.更新任务状态
            if(task_status_flag.equals("Y") && (!car_task_id.equals("") && !car_task_id.equals("0"))){
                //3.1 更新检查了辊道
                if(finish_check_gd_flag.equals("Y")){
                    String sqlTaskUpd="update b_dcs_wms_lock_car_task set " +
                            "last_updated_by='"+userID+"'," +
                            "last_update_date='"+nowDateTime+"'," +
                            "finish_check_gd_flag='"+finish_check_gd_flag+"'," +
                            "finish_check_gd_date='"+nowDateTime+"' " +
                            "where car_task_id="+car_task_id;
                    cFuncDbSqlExecute.ExecUpdateSql(userID,sqlTaskUpd,false,request,apiRoutePath);
                }
                //3.2 更新检查了机型
                if(check_model_flag.equals("Y")){
                    String sqlTaskUpd="update b_dcs_wms_lock_car_task set " +
                            "last_updated_by='"+userID+"'," +
                            "last_update_date='"+nowDateTime+"'," +
                            "check_model_flag='"+check_model_flag+"'," +
                            "check_model_date='"+nowDateTime+"'," +
                            "check_model_result='"+check_model_result+"' " +
                            "where car_task_id="+car_task_id;
                    cFuncDbSqlExecute.ExecUpdateSql(userID,sqlTaskUpd,false,request,apiRoutePath);
                }
                //3.3 判断是否任务开始
                if(car_task_type.equals("CHA_QU") && step_status.equals("WORK")){
                    String sqlTaskUpd="update b_dcs_wms_lock_car_task set " +
                            "last_updated_by='"+userID+"'," +
                            "last_update_date='"+nowDateTime+"'," +
                            "task_start_date='"+nowDateTime+"'," +
                            "tell_start_date='"+nowDateTime+"'," +
                            "execute_status='WORK' " +
                            "where car_task_id="+car_task_id;
                    cFuncDbSqlExecute.ExecUpdateSql(userID,sqlTaskUpd,false,request,apiRoutePath);
                }
                //3.4 路线结束进行更新
                if(step_status.equals("FINISH") && !car_task_type.equals("BI_RANG")){
                    dcsRouteCommonFunc.RouteFinishUpd(userID,car_code,car_task_id,car_task_type);
                }
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.查询路线任务状态
    @RequestMapping(value = "/DcsCoreWmsCarRouteStatusSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarRouteStatusSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/wms/DcsCoreWmsCarRouteStatusSel";
        String transResult="";
        String errorMsg="";
        String wmsCarTaskTable="b_dcs_wms_car_task";
        String task_status="";
        String step_status="";
        try{
            //1.获取参数
            String task_num=jsonParas.getString("task_num");
            String car_task_type=jsonParas.getString("car_task_type");

            //2.先查询天车任务主状态
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(wmsCarTaskTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                task_status=docItemBigData.getString("task_status");
                iteratorBigData.close();
            }

            //3.查找天车任务类型状态
            if(task_status.equals("PLAN") || task_status.equals("WORK")){
                String sqlLockCarRouteSel="select " +
                        "b.step_status " +
                        "from b_dcs_wms_lock_car_task a inner join b_dcs_wms_lock_car_route b " +
                        "on a.car_task_id=b.car_task_id " +
                        "where a.task_num='"+task_num+"' and b.car_task_type='"+car_task_type+"' " +
                        "order by b.car_route_id desc LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlLockCarRouteSel,
                        false,request,apiRoutePath);
                if(itemList!=null && itemList.size()>0){
                    step_status=itemList.get(0).get("step_status").toString();
                }
            }

            String result=task_status+","+step_status;
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
