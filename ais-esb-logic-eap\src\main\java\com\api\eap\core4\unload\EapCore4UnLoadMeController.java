package com.api.eap.core4.unload;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * AIS4.0 收板机工艺制造处理逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2024-7-29
 */
@RestController
@Slf4j
@RequestMapping("/eap/core4/unload")
public class EapCore4UnLoadMeController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //保存放板机panel数据到a_eap_me_up_record
    //plan_id,lot_num,panel_index,panel_barcode,dummy_flag,inspect_flag
    @RequestMapping(value = "/EapCore4UnLoadSaveUpRecord", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4UnLoadSaveUpRecord(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/unload/EapCore4UnLoadSaveUpRecord";
        String transResult = "";
        String errorMsg = "";
        String meUpRecordTable = "a_eap_me_up_record";
        try {
            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select station_id,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            JSONObject panelMsg = jsonParas.getJSONObject("panelMsg");
            String plan_id = panelMsg.getString("plan_id");
            String lot_num = panelMsg.getString("lot_num");
            String layerlot = panelMsg.getString("layerlot");//外层批次号
            Integer panel_ng_code = panelMsg.getInteger("panel_ng_code");
            String panel_barcode = panelMsg.getString("panel_barcode");
            Integer panel_model = panelMsg.getInteger("panel_model");
            Integer sys_model = panelMsg.getInteger("sys_model");
            String dummy_flag = panelMsg.getString("dummy_flag");
            String inspect_flag = panelMsg.getString("inspect_flag");
            String first_flag = panelMsg.getString("first_flag");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            long panelCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigData.getQueryObject());
            Integer panel_index = (int) panelCount + 1;

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String up_record_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("up_record_id", up_record_id);
            mapBigDataRow.put("station_id", Long.parseLong(station_id));
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("layerlot", layerlot);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("first_flag", first_flag);
            mongoTemplate.insert(mapBigDataRow, meUpRecordTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "退载具更新状态异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //完批时清空批次缓存信息
    @RequestMapping(value = "/EapCore4UnLoadDeleteUpRecord", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4UnLoadDeleteUpRecord(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/unload/EapCore4UnLoadDeleteUpRecord";
        String transResult = "";
        String errorMsg = "";
        String meUpRecordTable = "a_eap_me_up_record";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String lot_num = jsonParas.getString("lot_num");

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            mongoTemplate.remove(queryBigData, meUpRecordTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "退载具更新状态异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
