package com.api.eap.project.mflex;

import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;

/**
 * 泰国维信检查条形码。
 * <AUTHOR>
 * @since 2025-06-05
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/mflex/interf/tlwx")
public class EAPMflexInterfTlwxController {
    @Resource
    private CFuncLogInterf cFuncLogInterf;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private EapMflexInterfFunc eapMflexInterfFunc;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Resource
    private CFuncDbSqlExecute cFuncDbSqlExecute; 

    //1.写入Tag值
    @RequestMapping(value = "/CoreScadaWriteTag", method = {RequestMethod.POST,RequestMethod.GET})
    public String CoreScadaWriteTag(@RequestBody JSONArray jsonParas) throws Exception{
        String selectResult="";
        String errorMsg="";
        try{
            for(int i=0;i<jsonParas.size();i++){
                JSONObject jb=jsonParas.getJSONObject(i);
                String userName=jb.getString("user_name");
                String stationCode=jb.getString("station_code");
//                String clientCode=jb.getString("client_code");
                String equipmentID=jb.getString("equipmentID");
                String computerName=jb.getString("computerName");
                JSONArray jsonArrayTagList=jb.getJSONArray("tag_list");
                Boolean isAsyn=jb.getBoolean("isAsyn");
                // 先构建tagKey和tagValue字符串
                StringBuilder tagKeysBuilder = new StringBuilder();
                StringBuilder tagValuesBuilder = new StringBuilder();
                // 检查是否包含需要验证的面板条码
                String panelBarCodeToCheck = null;
                String panelType = null;
                for(int j=0;j<jsonArrayTagList.size();j++){
                    JSONObject jbTag=jsonArrayTagList.getJSONObject(j);
                    String tagKey=jbTag.getString("tag_key");
                    String tagValue=jbTag.getString("tag_value");
                    // 添加分隔符（除了第一个元素）
                    if(j > 0){
                        tagKeysBuilder.append(",");
                        tagValuesBuilder.append("&");
                    }
                    tagKeysBuilder.append(tagKey);
                    tagValuesBuilder.append(tagValue);
                    // 检查是否是需要验证的面板条码
                    if(tagKey != null && tagKey.endsWith("LoadPlcPanelBarCode")){
                        panelBarCodeToCheck = tagValue;
                        panelType = "Load";
//                        log.info("检测到放板机面板条码: {}", panelBarCodeToCheck);
                    } else if(tagKey != null && tagKey.endsWith("UnLoadPlcPanelBarCode")){
                        panelBarCodeToCheck = tagValue;
                        panelType = "UnLoad";
//                        log.info("检测到收板机面板条码: {}", panelBarCodeToCheck);
                    }
                }
                String tagKeysString = tagKeysBuilder.toString();
                String tagValuesString = tagValuesBuilder.toString();
//                log.info("写入SCADA标签 - TagKeys: {}", tagKeysString);
//                log.info("写入SCADA标签 - TagValues: {}", tagValuesString);
                // 先执行标签写入到SCADA系统
                String writeStatus= cFuncUtilsCellScada.WriteTagByStation(userName, stationCode, tagKeysString, tagValuesString, isAsyn);
                if(!writeStatus.equals("")){
                    log.error("SCADA标签写入失败: {}", writeStatus);
                    selectResult=CFuncUtilsLayUiResut.GetErrorJson(writeStatus);
                    return selectResult;
                }
//                log.info("SCADA标签写入成功");
                // 如果检测到需要验证的面板条码，先进行验证
                if(panelBarCodeToCheck != null && !panelBarCodeToCheck.isEmpty()){
                    try {
                        log.info("开始验证面板条码: {} (类型: {})", panelBarCodeToCheck, panelType);
                        // 构建验证请求的messageContent - 根据接口文档规范
                        JSONObject messageContentJson = new JSONObject();
                        messageContentJson.put("panelID", panelBarCodeToCheck);
                        messageContentJson.put("equipmentID", equipmentID);
                        messageContentJson.put("computerName", computerName);
                        // 从tag_list中提取其他相关信息
                        String scanId = "";
//                        String opID = "";
                        for(int k=0; k<jsonArrayTagList.size(); k++){
                            JSONObject tagItem = jsonArrayTagList.getJSONObject(k);
                            String tagKey = tagItem.getString("tag_key");
                            String tagValue = tagItem.getString("tag_value");
                            if(tagKey != null && tagKey.endsWith("ScanId")){
                                scanId = tagValue;
                            }
                        }
                        if(!scanId.isEmpty()){
                            messageContentJson.put("scanId", scanId);
                        }
                        if(userName != null && !userName.isEmpty()){
                            messageContentJson.put("opID", userName);
                        }
                        log.info("面板条码验证请求参数: {}", messageContentJson.toString());
                        // 调用UploadData方法验证面板条码
                        JSONObject checkResult = eapMflexInterfFunc.UploadData(stationCode, "UploadPanelIdCheck", "130001", messageContentJson.toString(), "");
                        log.info("面板条码验证结果: {}", checkResult);
                        // 解析验证结果 - 根据接口文档解析响应结构
                        if(checkResult != null){
                            String message = checkResult.getString("message");
                            String responseParas = checkResult.getString("responseParas");

                            // 解析响应参数获取isSuccess字段
                            boolean isSuccess = false;
                            String detailedError = message;
                            String checkResultStatus = "NG";

                            if(responseParas != null && !responseParas.isEmpty()){
                                try {
                                    JSONObject responseJson = JSONObject.parseObject(responseParas);

                                    // 检查顶层的IsSuccess字段
                                    if(responseJson.containsKey("IsSuccess")){
                                        isSuccess = responseJson.getBoolean("IsSuccess");
                                    } else if(responseJson.containsKey("isSuccess")){
                                        isSuccess = responseJson.getBoolean("isSuccess");
                                    }

                                    // 解析Information字段获取详细信息
                                    if(responseJson.containsKey("Information")){
                                        String information = responseJson.getString("Information");
                                        if(information != null && !information.isEmpty()){
                                            try {
                                                JSONObject infoJson = JSONObject.parseObject(information);

                                                // 从Information中获取isSuccess字段（优先级更高）
                                                if(infoJson.containsKey("isSuccess")){
                                                    isSuccess = infoJson.getBoolean("isSuccess");
                                                }

                                                // 获取详细错误信息
                                                if(infoJson.containsKey("errMessage")){
                                                    detailedError = infoJson.getString("errMessage");
                                                }

                                                // 检查是否需要停线
                                                if(infoJson.containsKey("isStopLine") && infoJson.getBoolean("isStopLine")){
                                                    detailedError += " (需要停线)";
                                                }

                                                // 记录成功时的关键信息
                                                if(isSuccess){
                                                    String lotID = infoJson.getString("lotID");
                                                    String currentStep = infoJson.getString("currentStep");
                                                    String workflowStep = infoJson.getString("workflowStep");
                                                    String checkResultValue = infoJson.getString("checkResult");
                                                    log.info("面板条码验证成功 - LotID: {}, 当前站别: {}, 下个工序: {}, 校验结果: {}",
                                                            lotID, currentStep, workflowStep, checkResultValue);
                                                }

                                            } catch (Exception infoParseEx) {
                                                log.warn("解析Information字段失败: {}", infoParseEx.getMessage());
                                            }
                                        }
                                    }

                                } catch (Exception parseEx) {
                                    log.warn("解析响应参数失败: {}", parseEx.getMessage());
                                }
                            }

                            // 根据isSuccess字段确定结果
                            if(isSuccess){
                                checkResultStatus = "OK";
                                log.info("面板条码验证通过: {} - 结果: {}", panelBarCodeToCheck, checkResultStatus);
                            } else {
                                checkResultStatus = "NG";
                                log.warn("面板条码验证失败: {} - 结果: {}, 错误: {}", panelBarCodeToCheck, checkResultStatus, detailedError);
                            }

                            // 记录最终验证结果
                            log.info("面板条码: {}, 类型: {}, 验证结果: {}", panelBarCodeToCheck, panelType, checkResultStatus);
                        }
                    } catch (Exception checkEx) {
                        log.error("面板条码验证异常: " + checkEx.getMessage(), checkEx);
                        // 验证异常但标签已写入，记录错误但不返回错误
                        log.warn("面板条码验证异常但SCADA标签已写入: PanelBarCode={}", panelBarCodeToCheck);
                    }
                }
            }
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
