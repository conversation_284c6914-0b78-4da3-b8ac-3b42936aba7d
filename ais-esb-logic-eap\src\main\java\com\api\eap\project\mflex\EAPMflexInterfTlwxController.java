package com.api.eap.project.mflex;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.net.ftp.FTPClient;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;

import lombok.extern.slf4j.Slf4j;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncScadaClientApi;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 泰国维信检查条形码。
 * <AUTHOR>
 * @since 2025-06-05
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/mflex/interf/tlwx")
public class EAPMflexInterfTlwxController {
    @Resource
    private CFuncLogInterf cFuncLogInterf;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private EapMflexInterfFunc eapMflexInterfFunc;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Resource
    private CFuncDbSqlExecute cFuncDbSqlExecute; 

    //1.写入Tag值
    @RequestMapping(value = "/CoreScadaWriteTag", method = {RequestMethod.POST,RequestMethod.GET})
    public String CoreScadaWriteTag(@RequestBody JSONArray jsonParas) throws Exception{
        String selectResult="";
        String errorMsg="";
        try{
            for(int i=0;i<jsonParas.size();i++){
                JSONObject jb=jsonParas.getJSONObject(i);
                String userName=jb.getString("user_name");
                String stationCode=jb.getString("station_code");
                String clientCode=jb.getString("client_code");
                JSONArray jsonArrayTagList=jb.getJSONArray("tag_list");
                Boolean isAsyn=jb.getBoolean("isAsyn");
                List<Map<String, String>> itemListTagWrite=new ArrayList<>();
                for(int j=0;j<jsonArrayTagList.size();j++){
                    JSONObject jbTag=jsonArrayTagList.getJSONObject(j);
                    String tagKey=jbTag.getString("tag_key");
                    String tagValue=jbTag.getString("tag_value");
                    Map<String, String> map=new HashMap<>();
                    map.put("tag_key",tagKey);
                    map.put("tag_value",tagValue);
                    itemListTagWrite.add(map);
                }
                String writeStatus= cFuncUtilsCellScada.WriteTagByStation(userName, stationCode, stationCode, clientCode, isAsyn);
                //.WriteScadaTagValue(userName,stationCode,itemListTagWrite,isAsyn);
                if(!writeStatus.equals("")){//写入失败
                    selectResult=CFuncUtilsLayUiResut.GetErrorJson(writeStatus);
                    return selectResult;
                }
            }
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
