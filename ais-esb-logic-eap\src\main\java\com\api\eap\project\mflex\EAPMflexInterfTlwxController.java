package com.api.eap.project.mflex;


import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import org.apache.commons.net.ftp.FTPClient;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;

import lombok.extern.slf4j.Slf4j;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncScadaClientApi;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 泰国维信检查条形码。
 * <AUTHOR>
 * @since 2025-06-05
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/mflex/interf/tlwx")
public class EAPMflexInterfTlwxController {
    @Resource
    private CFuncLogInterf cFuncLogInterf;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private EapMflexInterfFunc eapMflexInterfFunc;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Resource
    private CFuncDbSqlExecute cFuncDbSqlExecute; 

    //1.写入Tag值
    @RequestMapping(value = "/CoreScadaWriteTag", method = {RequestMethod.POST,RequestMethod.GET})
    public String CoreScadaWriteTag(@RequestBody JSONArray jsonParas) throws Exception{
        String selectResult="";
        String errorMsg="";
        try{
            for(int i=0;i<jsonParas.size();i++){
                JSONObject jb=jsonParas.getJSONObject(i);
                String userName=jb.getString("user_name");
                String stationCode=jb.getString("station_code");
                String clientCode=jb.getString("client_code");
                JSONArray jsonArrayTagList=jb.getJSONArray("tag_list");
                Boolean isAsyn=jb.getBoolean("isAsyn");

                // 检查是否包含需要验证的面板条码
                String panelBarCodeToCheck = null;
                String panelType = null; // "Load" 或 "UnLoad"

                for(int j=0;j<jsonArrayTagList.size();j++){
                    JSONObject jbTag=jsonArrayTagList.getJSONObject(j);
                    String tagKey=jbTag.getString("tag_key");
                    String tagValue=jbTag.getString("tag_value");

                    // 检查是否是需要验证的面板条码
                    if(tagKey != null && tagKey.endsWith("LoadPlcPanelBarCode")){
                        panelBarCodeToCheck = tagValue;
                        panelType = "Load";
                        log.info("检测到放板机面板条码: {}", panelBarCodeToCheck);
                    } else if(tagKey != null && tagKey.endsWith("UnLoadPlcPanelBarCode")){
                        panelBarCodeToCheck = tagValue;
                        panelType = "UnLoad";
                        log.info("检测到收板机面板条码: {}", panelBarCodeToCheck);
                    }
                }

                // 如果检测到需要验证的面板条码，先进行验证
                if(panelBarCodeToCheck != null && !panelBarCodeToCheck.isEmpty()){
                    try {
                        log.info("开始验证面板条码: {} (类型: {})", panelBarCodeToCheck, panelType);

                        // 构建验证请求的messageContent
                        JSONObject messageContentJson = new JSONObject();
                        messageContentJson.put("panelID", panelBarCodeToCheck);
                        messageContentJson.put("stationCode", stationCode);
                        messageContentJson.put("panelType", panelType);

                        // 调用UploadData方法验证面板条码
                        JSONObject checkResult = eapMflexInterfFunc.UploadData(stationCode, "UploadPanelIdCheck", "130001", messageContentJson.toString(), "");
                        log.info("面板条码验证结果: {}", checkResult);

                        // 解析验证结果
                        if(checkResult != null){
                            Boolean successFlag = checkResult.getBoolean("successFlag");
                            String message = checkResult.getString("message");

                            if(successFlag == null || !successFlag){ // 验证失败
                                log.warn("面板条码验证失败: {} - {}", panelBarCodeToCheck, message);

                                // 返回NG数据
                                JSONObject ngResult = new JSONObject();
                                ngResult.put("Success", false);
                                ngResult.put("Code", "NG");
                                ngResult.put("Message", "面板条码验证失败: " + message);
                                ngResult.put("PanelBarCode", panelBarCodeToCheck);
                                ngResult.put("PanelType", panelType);

                                return CFuncUtilsLayUiResut.GetErrorJson("面板条码验证失败: " + message);
                            }
                        }

                        log.info("面板条码验证通过: {}", panelBarCodeToCheck);

                    } catch (Exception checkEx) {
                        log.error("面板条码验证异常: " + checkEx.getMessage(), checkEx);
                        return CFuncUtilsLayUiResut.GetErrorJson("面板条码验证异常: " + checkEx.getMessage());
                    }
                }

                // 构建tagKey和tagValue字符串
                StringBuilder tagKeysBuilder = new StringBuilder();
                StringBuilder tagValuesBuilder = new StringBuilder();

                for(int j=0;j<jsonArrayTagList.size();j++){
                    JSONObject jbTag=jsonArrayTagList.getJSONObject(j);
                    String tagKey=jbTag.getString("tag_key");
                    String tagValue=jbTag.getString("tag_value");

                    // 添加分隔符（除了第一个元素）
                    if(j > 0){
                        tagKeysBuilder.append(",");
                        tagValuesBuilder.append("&");
                    }

                    tagKeysBuilder.append(tagKey);
                    tagValuesBuilder.append(tagValue);
                }

                String tagKeysString = tagKeysBuilder.toString();
                String tagValuesString = tagValuesBuilder.toString();

                log.info("写入SCADA标签 - TagKeys: {}", tagKeysString);
                log.info("写入SCADA标签 - TagValues: {}", tagValuesString);

                String writeStatus= cFuncUtilsCellScada.WriteTagByStation(userName, stationCode, tagKeysString, tagValuesString, isAsyn);
                //.WriteScadaTagValue(userName,stationCode,itemListTagWrite,isAsyn);
                if(!writeStatus.equals("")){//写入失败
                    selectResult=CFuncUtilsLayUiResut.GetErrorJson(writeStatus);
                    return selectResult;
                }
            }
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
