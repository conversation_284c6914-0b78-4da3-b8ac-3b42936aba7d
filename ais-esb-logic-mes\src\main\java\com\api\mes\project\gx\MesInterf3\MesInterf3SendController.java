package com.api.mes.project.gx.MesInterf3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 国轩改造项目上报信息相关接口
 * 1.首工序获取工单接口
 * 2.非工序获取工单接口
 * 3.全工序单个物料投入校验接口
 * 4.全工序全量物料投入校验接口
 * 5.模组线生产数据上传接口
 * 6.Pack线生产数据上传接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@RestController
@Slf4j
@RequestMapping("/mes/project/gx/interf3")
public class MesInterf3SendController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private MesInterf3SendFunc mesInterf3SendFunc;

    //1.首工序获取工单接口
    @RequestMapping(value = "/GetSourceOrderInfoByProcess", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetSourceOrderInfoByProcess(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/interf3/GetSourceOrderInfoByProcess";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = mesInterf3SendFunc.GetSourceOrderInfoByProcess(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //2.非首工序获取工单
    @RequestMapping(value = "/GetOtherOrderInfoByProcess", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetOtherOrderInfoByProcess(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/interf3/GetOtherOrderInfoByProcess";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = mesInterf3SendFunc.GetOtherOrderInfoByProcess(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //3.单个物料校验接口
    @RequestMapping(value = "/MaterialCheckInput", method = {RequestMethod.POST, RequestMethod.GET})
    public String MaterialCheckInput(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/interf3/MaterialCheckInput";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = mesInterf3SendFunc.MaterialCheckInput(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //4.全量物料校验接口
    @RequestMapping(value = "/CompleteCheckInput", method = {RequestMethod.POST, RequestMethod.GET})
    public String CompleteCheckInput(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/interf3/CompleteCheckInput";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = mesInterf3SendFunc.CompleteCheckInput(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //5.模组数据上传MES接口
    @RequestMapping(value = "/PushMessageToMes", method = {RequestMethod.POST, RequestMethod.GET})
    public String PushMessageToMes(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/interf3/PushMessageToMes";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = mesInterf3SendFunc.PushMessageToMes(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //6.PACK数据上传MES接口
    @RequestMapping(value = "/PushPackMessageToMes", method = {RequestMethod.POST, RequestMethod.GET})
    public String PushPackMessageToMes(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/interf3/PushPackMessageToMes";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = mesInterf3SendFunc.PushPackMessageToMes(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }
}
