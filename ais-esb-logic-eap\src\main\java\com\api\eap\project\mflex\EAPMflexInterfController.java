package com.api.eap.project.mflex;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import com.api.common.db.CFuncDbSqlResolve;
import org.apache.commons.net.ftp.FTPFile;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.*;
import java.net.HttpURLConnection;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 维信接口处理
 * 1.扫描上报MES获取JDE料号
 * </p>
 *
 * <AUTHOR>
 * @since 2023-01-13
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/mflex/interf")
public class EAPMflexInterfController {
    @Resource
    private CFuncLogInterf cFuncLogInterf;
    @Resource
    private MongoTemplate mongoTemplate;
    @Resource
    private EapMflexInterfFunc eapMflexInterfFunc;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Resource
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Value("${recipe.upload.localDir:/AIS/recipe}")
    private String localDir;
    @Value("${recipe.upload.fileType:.csv}")
    private String fileType;


    //1.PLASMA获取料号
    @RequestMapping(value = "/EAPMflexGetJDEPruoduct", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexGetJDEPruoduct(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/mflex/interf/EAPMflexGetJDEPruoduct";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String panel = jsonParas.getString("panel");
            JSONObject information = jsonParas.getJSONObject("information");

            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapMflexInterfFunc.GetJDEPruoduct(station_code, panel, information);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, responseParas, "", 0);
        } catch (Exception ex) {
            errorMsg = "Plasma获取料号信息异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.上报MES数据信息
    @RequestMapping(value = "/EAPMflexUploadData", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexUploadData(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/mflex/interf/EAPMflexUploadData";
        String transResult = "";
        String errorMsg = "";
        String station_code = jsonParas.getString("station_code");
        String messageCode = jsonParas.getString("messageCode");
        String messageContent = "";
        String info = null;
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        String esbInterfCode = "";
        try {
            try {
                messageContent = jsonParas.getJSONObject("messageContent").toJSONString();
            } catch (Exception e) {
                try {
                    messageContent = jsonParas.getJSONArray("messageContent").toJSONString();
                } catch (Exception exception) {
                    messageContent = jsonParas.getString("messageContent");
                }
            }
            try {
                info = jsonParas.getJSONObject("info").toJSONString();
            } catch (Exception e) {
                try {
                    info = jsonParas.getJSONArray("info").toJSONString();
                } catch (Exception exception) {
                    info = jsonParas.getString("info");
                }
            }
            JSONObject jbResult = new JSONObject();
            //必须记录日志,用于泰国维信，多个标签的时候，下面的日志不再写。日志在分支里面去记录。
            boolean needtWriteLog = true;
            switch (messageCode) {
                case "1"://上报MES化学清洗电导率
                    esbInterfCode = "UploadChemicalCleanConductivity";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "10002"://Plasma提交Panel信息
                    esbInterfCode = "UploadPanelInfo";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "30001"://生产参数上传
                    esbInterfCode = "UploadProductionData";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "40001"://机台状态上传
                case "60001"://机台状态上传
                case "160001"://设备状态上传
                    esbInterfCode = "UploadDeviceStatus";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "50001"://设备参数上传
                case "150001"://设备参数上传
                    esbInterfCode = "UploadDeviceParam";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "70001"://环境参数上传
                case "170001"://环境参数上传
                    esbInterfCode = "UploadEnvironmentParam";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "80001"://员工登录
                    esbInterfCode = "UploadUserLogin";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "90001"://设备自检
                    esbInterfCode = "DeviceSelfCheck";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "100001"://工具验证
                    esbInterfCode = "UploadToolCheck";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "110001"://物料验证
                    esbInterfCode = "UploadMaterialCheck";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "120001"://产品批次验证
                    esbInterfCode = "UploadBatchCheck";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                case "130001"://生产扫码上传验证
                	//如果是泰国维信。扫描的板子码出现是多个。则分解一下，第一个一个上传。。循环上报给EAP系统。 'panellD\":616638309703T;1816638309704T\
                	String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
                    if (projectCode.equals("TLWX")) {
                        // 解析 messageContent 中的 JSON 数据
                        if (messageContent != null && !messageContent.isEmpty()) {
                            try {
                                JSONObject messageContentJson = JSONObject.parseObject(messageContent);
                                // 检查是否包含 panelID 字段
                                if (messageContentJson.containsKey("panelID")) {
                                    String panelID = messageContentJson.getString("panelID");
                                    // 检查 panelID 是否包含分号，表示有多个值
                                    if (panelID != null && panelID.contains(";")) {
                                        // 分割 panelID
                                        String[] panelIDs = panelID.split(";");
                                        log.info("发现多个 panelID 值，共 {} 个", panelIDs.length);
                                        // 循环处理每个 panelID
                                        for (String singlePanelID : panelIDs) {
                                            if (singlePanelID != null && !singlePanelID.trim().isEmpty()) {
                                                // 创建新的 messageContent JSON，替换 panelID
                                                JSONObject newMessageContentJson = new JSONObject(messageContentJson);
                                                newMessageContentJson.put("panelID", singlePanelID.trim());
                                                // 调用处理单个 panelID 的方法
                                                log.info("处理单个 panelID: {}", singlePanelID.trim());
                                                esbInterfCode = "UploadPanelIdCheck";
                                                jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, newMessageContentJson.toJSONString(), info);
                                                Boolean prodFlag = true;
                                                Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
                                                String token = jbResult.getString("token");
                                                String requestParas = jbResult.getString("requestParas");
                                                Boolean successFlag = jbResult.getBoolean("successFlag");
                                                String message = jbResult.getString("message");
                                                String endDate = CFuncUtilsSystem.GetNowDateTime("");
                                                String responseParas = jbResult.getString("responseParas");
                                                //记录日志
                                                if (isSaveFlag) {
                                                    cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
                                                }
                                                needtWriteLog = false;
                                            }
                                        }
                                    }
                                }
                            } catch (Exception e) {
                                log.error("解析 messageContent JSON 时发生错误: {}", e.getMessage());
                            }
                        }
                    	
                    }{
                    	esbInterfCode = "UploadPanelIdCheck";
                        jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    }
                    break;
                case "140001"://检测结果上传
                    esbInterfCode = "UploadTestResult";
                    jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info);
                    break;
                default:
                    break;
            }
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag && needtWriteLog) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, responseParas, "", 0);
        } catch (Exception ex) {
            cFuncLogInterf.Insert(startDate, CFuncUtilsSystem.GetNowDateTime(""), esbInterfCode, false, "", messageContent, "", false, "上报数据异常", null);
            errorMsg = "上报数据异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.申请上传配方
    @RequestMapping(value = "/EAPMflexApplyUploadRecipe", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexApplyUploadRecipe(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/mflex/interf/EAPMflexApplyUploadRecipe";
        String transResult = "";
        String errorMsg = "";
        String user_name = "";
        String meUserTable = "a_eap_me_station_user";
        String startDate = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String recipe_name = jsonParas.getString("recipe_name");
            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapMflexInterfFunc.ApplyUploadRecipe(station_code, recipe_name, user_name);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject responseParas = jbResult.getJSONObject("responseParas");
            //配方文件名称=配方描述+配方版本
            String response = responseParas == null ? "" : responseParas.toJSONString();
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, response, successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, response, "", 0);
        } catch (Exception ex) {
            cFuncLogInterf.Insert(startDate, CFuncUtilsSystem.GetNowDateTime(""), "ApplyUploadRecipe", false, "", jsonParas.toJSONString(), "", false, "申请上传接口异常", null);
            errorMsg = "上报信息异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.上传结果通知
    @RequestMapping(value = "/EAPMflexRecipeUploadResultNotice", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexRecipeUploadResultNotice(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/mflex/interf/EAPMflexRecipeUploadResultNotice";
        String transResult = "";
        String errorMsg = "";
        String user_name = "";
        String meUserTable = "a_eap_me_station_user";
        String startDate = "";
        try {
            JSONObject data = jsonParas.getJSONObject("data");
            JSONArray uploadResults = jsonParas.getJSONArray("upload_results");
            String sessionId = data.getString("session");
            startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapMflexInterfFunc.RecipeUploadResultNotice(sessionId, uploadResults);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject responseParas = jbResult.getJSONObject("responseParas");
            String response = responseParas.toJSONString();
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, response, successFlag, message, null);
            }
            if (!successFlag) {
                return CFuncUtilsLayUiResut.GetErrorJson(message);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, response, "", 0);

        } catch (Exception ex) {
            cFuncLogInterf.Insert(startDate, CFuncUtilsSystem.GetNowDateTime(""), "RecipeUploadResultNotice", false, "", jsonParas.toJSONString(), "", false, "上传结果通知接口异常", null);
            errorMsg = "上传配方异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


    //5.上传配方
    @RequestMapping(value = "/UploadRecipe", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexUploadRecipe(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        String[] files = jsonObject.getString("recipe_name").split(",");
        JSONObject data = jsonObject.getJSONObject("data");
        JSONObject ftpInfo = data.getJSONObject("ftp");
        String recipePath = data.getString("recipePath");
        String ftpUser = ftpInfo.getString("ftpUser");
        Integer ftpPort = ftpInfo.getInteger("ftpPort");
        String ftpPwd = ftpInfo.getString("ftpPwd");
        String ftpIp = ftpInfo.getString("ftpIp");
        String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
        if (projectCode.equals("YCWX"))
            ftpIp = "**********";
        FTPClient ftpClient = new FTPClient();
        List<Map<String, String>> uploadResults = new ArrayList<Map<String, String>>();
        FileInputStream fileInputStream = null;
        try {
            ftpClient.connect(ftpIp, ftpPort);
            ftpClient.login(ftpUser, ftpPwd);
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
            ftpClient.enterLocalPassiveMode(); // 如果服务器在防火墙后面，需要设置被动模式
            for (String file : files) {
                String localPath = localDir + "/" + file + fileType;
                //中文上传失败问题
                String remotePath = new String((recipePath + "/" + file + fileType).getBytes("GBK"), "iso-8859-1");
                File localFile = new File(localPath);
                if (!localFile.exists()) {
                    return CFuncUtilsLayUiResut.GetErrorJson("配方文件不存在");
                }
                fileInputStream = new FileInputStream(localPath);
                boolean response = ftpClient.storeFile(remotePath, fileInputStream);
                if (!response) {
                    return CFuncUtilsLayUiResut.GetErrorJson("文件上传失败");
                }
                HashMap<String, String> uploadResult = new HashMap<String, String>();
                // 文件路径
                uploadResult.put("attachPath", recipePath);
                //文件名称
                uploadResult.put("attachName", file);
                //文件类型
                uploadResult.put("attachType", fileType);
                uploadResults.add(uploadResult);
                fileInputStream.close();
            }
            ftpClient.logout();
        } catch (IOException ex) {
            return CFuncUtilsLayUiResut.GetErrorJson("上传异常：" + ex.getMessage());
        } finally {
            try {
                if (ftpClient.isConnected()) {
                    ftpClient.disconnect();
                }
                if (fileInputStream != null) {
                    fileInputStream.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return CFuncUtilsLayUiResut.GetStandJson(true, null, JSONObject.toJSONString(uploadResults), "", 0);
    }

    //3.申请下载配方
    @RequestMapping(value = "/EAPMflexApplyDownloadRecipe", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexApplyDownloadRecipe(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/mflex/interf/EAPMflexApplyDownloadRecipe";
        String transResult = "";
        String errorMsg = "";
        String user_name = "";
        String meUserTable = "a_eap_me_station_user";
        String startDate = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String recipe_name = jsonParas.getString("recipe_name");
            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapMflexInterfFunc.ApplyDownloadRecipe(station_code, recipe_name, user_name);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject responseParas = jbResult.getJSONObject("responseParas");
            //配方文件名称=配方描述
            String response = responseParas == null ? "" : responseParas.toJSONString();
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, response, successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, response, "", 0);
        } catch (Exception ex) {
            cFuncLogInterf.Insert(startDate, CFuncUtilsSystem.GetNowDateTime(""), "ApplyDownloadRecipe", false, "", jsonParas.toJSONString(), "", false, "申请下载接口异常", null);
            errorMsg = "下载配发异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.下载配方
    @RequestMapping(value = "/DownloadRecipe", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexDownloadRecipe(@RequestBody JSONObject jsonObject, HttpServletRequest request) {
        String[] files = jsonObject.getString("recipe_name").split(",");
        String lot = jsonObject.getString("recipe_name");
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray recipes = data.getJSONArray("recipe");
        JSONObject ftpInfo = data.getJSONObject("ftp");
        String ftpUser = ftpInfo.getString("ftpUser");
        Integer ftpPort = ftpInfo.getInteger("ftpPort");
        String ftpPwd = ftpInfo.getString("ftpPwd");
        String ftpIp = ftpInfo.getString("ftpIp");
        if (ftpIp == null || ftpIp.equals("")) ftpIp = "**********";
        FTPClient ftpClient = new FTPClient();
        List<Map<String, Object>> downloadResults = new ArrayList<Map<String, Object>>();
        try {
            ftpClient.connect(ftpIp, ftpPort);
            ftpClient.login(ftpUser, ftpPwd);
            ftpClient.enterLocalPassiveMode(); // 如果服务器在防火墙后面，需要设置被动模式
            for (int i = 0; i < recipes.size(); i++) {
                Map<String, Object> result = new HashMap<>();
                JSONObject recipe = recipes.getJSONObject(i);
                String recipePath = recipe.getString("recipePath");
                String recipeName = recipe.getString("recipeName");
                String RecipeFileType = cFuncDbSqlResolve.GetParameterValue("RecipeFileType");
                if (RecipeFileType != null && !RecipeFileType.equals(""))
                    fileType = RecipeFileType;
                String remoteFilePath = recipePath + "/" + recipeName + fileType;
                String localFilePath = localDir + "/" + recipeName + fileType;
                boolean download = Download(ftpClient, remoteFilePath, localFilePath);
                //数据存储
                if (download) {
                    log.debug("数据加载并存储 targetFile ：{}", localFilePath);
                    eapMflexInterfFunc.loadData(localFilePath, recipeName, lot);
                }
                result.put("success", download);
                result.put("recipeName", recipeName);
                downloadResults.add(result);
            }
        } catch (Exception ex) {
            ex.printStackTrace();
        }
        return CFuncUtilsLayUiResut.GetStandJson(true, null, JSONObject.toJSONString(downloadResults), "", 0);
    }

    @RequestMapping(value = "/loadData", method = {RequestMethod.POST, RequestMethod.GET})
    public String loadData(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        eapMflexInterfFunc.loadData("D:\\AIS\\recipe\\测试配方001-1.0.2.csv", "测试配方001", "");
        return "";
    }

    //4.下载结果通知
    @RequestMapping(value = "/EAPMflexRecipeDownloadResultNotice", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexRecipeDownloadResultNotice(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/mflex/interf/EAPMflexRecipeDownloadResultNotice";
        String transResult = "";
        String errorMsg = "";
        String user_name = "";
        String meUserTable = "a_eap_me_station_user";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        try {
            JSONObject data = jsonParas.getJSONObject("data");
            JSONObject jbResult = eapMflexInterfFunc.RecipeDownloadResultNotice(data.getString("session"), jsonParas.getJSONArray("downLoad_results"));
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject responseParas = jbResult.getJSONObject("responseParas");
            String response = responseParas == null ? "" : responseParas.toJSONString();
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, response, successFlag, message, null);
            }
            if (!successFlag) {
                return CFuncUtilsLayUiResut.GetErrorJson(message);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, response, "", 0);
        } catch (Exception ex) {
            cFuncLogInterf.Insert(startDate, CFuncUtilsSystem.GetNowDateTime(""), "RecipeDownloadResultNotice", false, "", jsonParas.toJSONString(), "", false, "申请下载接口异常", null);
            errorMsg = "下载结果通知异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


    //申请下发
    @RequestMapping(value = "/EAPMflexApplyDistributeRecipe", method = {RequestMethod.POST, RequestMethod.GET})
    public String EAPMflexApplyDistributeRecipe(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String apiRoutePath = "/eap/project/mflex/interf/EAPMflexApplyDistributeRecipe";
        String transResult = "";
        String errorMsg = "";
        String user_name = "";
        String meUserTable = "a_eap_me_station_user";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String recipe_name = jsonParas.getString("recipe_name");
            //1.获取当前用户信息
            Query queryBigData = new Query();
            String[] split = null;
            if (StringUtils.isEmpty(recipe_name))
//                    || (split = recipe_name.split("-")).length < 1)
            {
                throw new NullPointerException("要下发的配方信息为空");
            }
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //将配方所有的点位拼接在一起以及值拼接在一起  key:DesPlc/PlcRecipe/PlcRecipe271,DesPlc/PlcRecipe/PlcRecipe272 value:222,0,0,0
            List<Map<String, Object>> maps = cFuncDbSqlExecute.ExecSelectSql(station_code, "SELECT string_agg(tag.tag,',')as tag_key,string_agg(d.parameter_val,'&') as tag_value FROM ( SELECT * FROM a_eap_fmod_recipe WHERE recipe_name = '" + recipe_name + "' ) AS recipe LEFT JOIN a_eap_fmod_recipe_detail AS d ON recipe.recipe_id = d.recipe_id LEFT JOIN ( SELECT concat ( client_code, '/',tag_group_code, '/', tag_code ) AS tag,tag.tag_id  FROM scada_client client LEFT JOIN scada_tag_group gro ON client.client_id = gro.client_id LEFT JOIN scada_tag AS tag ON gro.tag_group_id = tag.tag_group_id  ) tag on tag.tag_id=d.tag_id ", false, request, apiRoutePath);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, JSONObject.toJSONString(maps.stream().findFirst().get()), "", 0);
        } catch (Exception ex) {
            errorMsg = "下载配发异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    private boolean Download(FTPClient ftpClient, String remoteFile, String localFile) {
        OutputStream outputStream = null;
        boolean result = false;
        try {
            ftpClient.enterLocalPassiveMode();
            log.error("localPath:{}", localFile);
            outputStream = Files.newOutputStream(Paths.get(localFile));
            // ftp默认使用ISO-8859-1编码格式,所以这里需要转换为ISO-8859-1，“解决文件名为中文时，下载后为空文件的问题”
            String remoteFileName = new String(remoteFile.getBytes("GBK"), StandardCharsets.ISO_8859_1);
            log.error("downlad path:{}", remoteFileName);
            result = ftpClient.retrieveFile(remoteFileName, outputStream);
        } catch (IOException ex) {
            log.error("下载失败：{}", ex.getMessage());
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                ftpClient.disconnect();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;
    }

    //5.员工登录
    @RequestMapping(value = "/EapUserLogin", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapUserLogin(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/mflex/interf/EapUserLogin";
        String transResult = "";
        String errorMsg = "";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        String esbInterfCode = "UploadUserLogin";
        String messageContent = "";
        try {
            String username = jsonParas.getString("username");
            String password = jsonParas.getString("password");
            String messageCode = "80001";

            //1.获取工位号
            String stationSql = "select COALESCE(station_code,'') station_code from sys_fmod_station where enable_flag='Y' limit 1";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(username, stationSql, false, null, "");
            if (itemListStation == null || itemListStation.size() == 0) {
                throw new IllegalStateException("查询不到当前可用的工位信息");
            }
            String station_code = itemListStation.get(0).get("station_code").toString();
            //2.获取员工角色
            String roleSql = "select COALESCE(BB.role_des,'') role_des from sys_user AA join sys_role BB on AA.role_id=BB.role_id where aa.user_code='" + username + "' limit 1";
            List<Map<String, Object>> itemListRole = cFuncDbSqlExecute.ExecSelectSql(username, roleSql, false, null, "");
            if (itemListRole == null || itemListRole.size() == 0) {
                throw new IllegalStateException("查询不到用户角色信息");
            }
            String rolename = itemListRole.get(0).get("role_des").toString();
            //2.访问接口
            JSONObject messageContentObject = new JSONObject();
            messageContentObject.put("EmployeeId", username);
            messageContentObject.put("Password", password);
            messageContent = messageContentObject.toJSONString();

            JSONObject info = new JSONObject();
            info.put("RoleName", rolename);
            JSONObject jbResult = eapMflexInterfFunc.UploadData(station_code, esbInterfCode, messageCode, messageContent, info.toString());
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, responseParas, "", 0);
        } catch (Exception ex) {
            cFuncLogInterf.Insert(startDate, CFuncUtilsSystem.GetNowDateTime(""), esbInterfCode, false, "", messageContent, "", false, "登录失败", null);
            errorMsg = "登录失败：" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


}
