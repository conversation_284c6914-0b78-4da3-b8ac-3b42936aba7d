package com.api.pack.core.sort;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONPath;
import com.api.pack.core.board.Board;
import com.api.pack.core.board.BoardConst;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.Locale;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SortCompareData
{
    private String name;
    private String type;
    private String orient;
    private Object origin;
    private String path;
    private Integer index;
    private String value;

    public SortCompareData(Board board, String path, boolean isChar)
    {
        if (board != null)
        {
            this.name = board.getBoardName();
            this.type = board.getBoardCategory();
            this.orient = board.getBoardOrient();
            this.path = path;
            if (isChar)
            {
                this.origin = board.getBoardChar();
                this.resolve();
            } else
            {
                this.origin = board;
                // Java反射获取对象属性值
                Field field = ReflectionUtils.findField(board.getClass(), path);
                if (field != null)
                {
                    ReflectionUtils.makeAccessible(field);
                    this.value = String.valueOf(ReflectionUtils.getField(field, board));
                    this.index = board.getBoardIndex();
                }
            }
        }
    }

    public SortCompareData(Board board, String property)
    {
        this(board, property, false);
    }

    public SortCompareData(String type, String orient, Object origin, String path, Integer index, String value)
    {
        this.type = type;
        this.orient = orient;
        this.origin = origin;
        this.path = path;
        this.index = index;
        Locale locale = Locale.getDefault();
        String dataType = type + (!ObjectUtils.isEmpty(index) ? "[" + index + "]" : BoardConst.BLANK);
        String dataOrient = orient.toUpperCase();
        if (Locale.SIMPLIFIED_CHINESE.equals(locale))
        {
            dataType = dataType + "板";
            dataOrient = "back".equals(orient) ? "反面" : "正面";
            this.name = String.format("%s板%s", dataType, dataOrient);
        }
        else
        {
            this.name = String.format("%s %s", dataType, dataOrient);
        }
        if (ObjectUtils.isEmpty(value))
        {
            this.resolve();
        } else
        {
            this.value = value;
        }
    }

    public SortCompareData(String type, String orient, Object origin, String path, Integer index)
    {
        this(type, orient, origin, path, index, null);
    }

    public SortCompareData(String type, String orient, Object origin, String path)
    {
        this(type, orient, origin, path, null, null);
    }

    private void resolve()
    {
        if (ObjectUtils.isEmpty(this.origin) || ObjectUtils.isEmpty(this.path))
        {
            this.value = BoardConst.BLANK;
            return;
        }
        if (this.origin instanceof String)
        {
            this.origin = JSON.parse((String) this.origin);
        }
        this.value = String.valueOf(JSONPath.eval(this.origin, this.path));
    }

    public boolean isNULL()
    {
        return BoardConst.VALUE_NULL.equals(this.value);
    }

    public boolean isNC()
    {
        return BoardConst.VALUE_NC.equals(this.value);
    }
}