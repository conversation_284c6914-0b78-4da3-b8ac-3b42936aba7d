package com.api.mes.project.sh;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-21
 */
@RestController
@Slf4j
@RequestMapping("/mes/project/sh")
public class MesShMeMaterialRelController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    //配餐工位物料绑定到主物料上
    @RequestMapping(value = "/MesShPcMeMaterialRelIns", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesShPcMeMaterialRelIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/sh/MesShPcMeMaterialRelIns";
        String transResult = "";
        String errorMsg = "";
        String meMaterialRelTable = "c_mes_me_material_rel";
        try {
            //1.查询主物料和非主物料
            String prod_line_code = jsonParas.getString("prod_line_code");
            String station_code = jsonParas.getString("station_code");
            String serial_num_main = "";
            String small_model_type_main = "";
            String sqlMainMaterial = "select COALESCE(material_code,'') material_code," +
                    "COALESCE(exact_barcode,'') exact_barcode," +
                    "COALESCE(material_des,'') material_des  " +
                    "from c_mes_me_cr_pdure_bom " +
                    "where main_material_flag='Y' " +
                    "and verify_flag='Y' " +
                    "and prod_line_code='" + prod_line_code + "' " +
                    "and station_code='" + station_code + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMainMaterial = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMainMaterial, true, request, apiRoutePath);
            if (itemListMainMaterial != null && itemListMainMaterial.size() > 0) {
                small_model_type_main = itemListMainMaterial.get(0).get("material_code").toString();
                serial_num_main = itemListMainMaterial.get(0).get("exact_barcode").toString();

                Query query = new Query();
                query.addCriteria(Criteria.where("station_code").is("RAL01-OP010-B"));
                query.addCriteria(Criteria.where("exact_barcode").is(serial_num_main));
                MongoCursor<Document> iterator = mongoTemplate.getCollection("c_mes_me_station_material").find(query.getQueryObject()).sort(query.getSortObject()).noCursorTimeout(true).batchSize(1000).iterator();
                while (iterator.hasNext()) {
                    errorMsg = "主物料码已经绑定";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                //格式化新增数据
                Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                //新增非主物料
                List<Map<String, Object>> lstDocuments = new ArrayList<>();
                String sqlSubMaterial = "select COALESCE(material_code,'') material_code," +
                        "COALESCE(exact_barcode,'') exact_barcode," +
                        "COALESCE(material_batch,'') material_batch," +
                        "COALESCE(material_des,'') material_des  " +
                        "from c_mes_me_cr_pdure_bom " +
                        "where main_material_flag='N' " +
                        "and (verify_flag = 'Y' OR batch_flag = 'Y') " +
                        "and prod_line_code='" + prod_line_code + "' " +
                        "and station_code='" + station_code + "' ";
                List<Map<String, Object>> itemListSubMaterial = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlSubMaterial, true, request, apiRoutePath);
                if (itemListSubMaterial != null && itemListSubMaterial.size() > 0) {
                    for (int i = 0; i < itemListSubMaterial.size(); i++) {
                        String small_model_type_sub = itemListSubMaterial.get(i).get("material_code").toString();
                        String serial_num_sub = itemListSubMaterial.get(i).get("exact_barcode").toString();
                        if(serial_num_sub == null || serial_num_sub.length() == 0){
                            serial_num_sub = itemListSubMaterial.get(i).get("material_batch").toString();
                        }

                        Map<String, Object> mapDataItem = new HashMap<>();
                        String material_rel_id = CFuncUtilsSystem.CreateUUID(true);
                        mapDataItem.put("item_date", item_date);
                        mapDataItem.put("item_date_val", item_date_val);
                        mapDataItem.put("material_rel_id", material_rel_id);
                        mapDataItem.put("main_prod_line_code", prod_line_code);
                        mapDataItem.put("main_serial_num", serial_num_main);
                        mapDataItem.put("main_material_type", small_model_type_main);
                        //分线
                        mapDataItem.put("sub_prod_line_code", prod_line_code);
                        mapDataItem.put("sub_serial_num", serial_num_sub);
                        mapDataItem.put("sub_material_type", small_model_type_sub);
                        //上传标识
                        mapDataItem.put("up_flag", "N");
                        mapDataItem.put("up_ng_code", -1);
                        mapDataItem.put("up_ng_msg", "");
                        mapDataItem.put("enable_flag", "Y");
                        lstDocuments.add(mapDataItem);
                    }
                }
                mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, meMaterialRelTable).insert(lstDocuments).execute();
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "分线绑定到总线信息存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //主线上线工位物料绑定到总成线存储
    @RequestMapping(value = "/MesShMaterialBindingAssembly", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesShMaterialBindingAssembly(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/sh/MesShMaterialBindingAssembly";
        String transResult = "";
        String errorMsg = "";
        String serial_num = "";
        String meMaterialRelTable = "c_mes_me_material_rel";
        String n1 = "";
        try {
            long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            serial_num = jsonParas.getString("serial_num");
            String PLC_Sarestring1 = jsonParas.getString("PLC_Sarestring1");
            String station_flow_id = jsonParas.getString("station_flow_id");
            String prod_line_code = jsonParas.getString("prod_line_code");

            //根据减速器后壳查询其他物料
            List<Map<String, Object>> lstMDocuments = new ArrayList<>();
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String main_material_type = "";//material_code
            String main_serial_num = "";//exact_barcode

            JSONArray material_list = new JSONArray();
            Query queryBigDataD = new Query();
            queryBigDataD.addCriteria(Criteria.where("main_serial_num").is(PLC_Sarestring1));
            queryBigDataD.addCriteria(Criteria.where("enable_flag").is("Y"));
            MongoCursor<Document> iterator = mongoTemplate.getCollection(meMaterialRelTable).find(queryBigDataD.getQueryObject()).
                    sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            while (iterator.hasNext()) {
                Document docItem = iterator.next();
                main_material_type = docItem.getString("main_material_type");
                main_serial_num = docItem.getString("main_serial_num");

                String sub_material_type = docItem.getString("sub_material_type");//material_code
                String sub_serial_num = docItem.getString("sub_serial_num");//exact_barcode
                JSONObject item = new JSONObject();
                item.put("material_code", sub_material_type);
                item.put("exact_barcode", sub_serial_num);
                material_list.add(item);
            }
            if (iterator.hasNext()) iterator.close();
            JSONObject item_main = new JSONObject();
            item_main.put("material_code", main_material_type);
            item_main.put("exact_barcode", main_serial_num);
            material_list.add(item_main);
            if (material_list != null && material_list.size() > 0) {
                for (int i = 0; i < material_list.size(); i++) {
                    JSONObject material = material_list.getJSONObject(i);
                    //查询子物料信息
                    Map<String, Object> mapMDataItem = new HashMap<>();
                    String material_trace_id = CFuncUtilsSystem.CreateUUID(true);
                    String trace_d_time = CFuncUtilsSystem.GetNowDateTime("");
                    String material_code = material.getString("material_code");
                    String exact_barcode = material.getString("exact_barcode");
                    String sqlMaterialScan = "select " +
                            "material_code,material_des,usage," +
                            "COALESCE(material_uom,'') material_uom,COALESCE(material_attr,'') material_attr," +
                            "main_material_flag,verify_flag,batch_flag," +
                            "COALESCE(bom_version,'') bom_version " +
                            "from c_mes_fmod_small_model_bom " +
                            "where material_code='" + material_code + "'" ;
                    List<Map<String, Object>> itemListBom = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMaterialScan,
                            false, request, apiRoutePath);
                    if (itemListBom != null && itemListBom.size() > 0) {
                        mapMDataItem.put("item_date", item_date);
                        mapMDataItem.put("item_date_val", item_date_val);
                        mapMDataItem.put("material_trace_id", material_trace_id);
                        mapMDataItem.put("station_flow_id", station_flow_id);
                        mapMDataItem.put("station_code", station_code);
                        mapMDataItem.put("serial_num", serial_num);
                        mapMDataItem.put("prod_line_code", prod_line_code);
                        mapMDataItem.put("material_code", itemListBom.get(0).get("material_code").toString());
                        mapMDataItem.put("material_des", itemListBom.get(0).get("material_des").toString());
                        mapMDataItem.put("use_count", Integer.parseInt(itemListBom.get(0).get("usage").toString()));
                        mapMDataItem.put("material_uom", itemListBom.get(0).get("material_uom").toString());
                        mapMDataItem.put("material_attr", itemListBom.get(0).get("material_attr").toString());
                        mapMDataItem.put("exact_barcode", exact_barcode);
                        mapMDataItem.put("main_material_flag", itemListBom.get(0).get("main_material_flag").toString());
                        mapMDataItem.put("verify_flag", itemListBom.get(0).get("verify_flag").toString());
                        mapMDataItem.put("batch_flag", itemListBom.get(0).get("batch_flag").toString());
                        mapMDataItem.put("bom_version", itemListBom.get(0).get("bom_version").toString());
                        mapMDataItem.put("trace_d_time", trace_d_time);
                        lstMDocuments.add(mapMDataItem);
                    }
                }
            }
            if (lstMDocuments.size() > 0) {
                mongoTemplate.insert(lstMDocuments, "c_mes_me_station_material");
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "总成码{" + serial_num + "}绑定物料异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
