package com.api.pmc.core.flow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 冲压流程
 * 1.冲压换模
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/flow")
public class PmcCoreFlowCaController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;
    @Autowired
    private RestTemplate restTemplate;

    //1.冲压换模
    @Transactional
    @RequestMapping(value = "/PmcCaChangeModeEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCaChangeModeEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCaChangeModeEvent";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            log.info("--------------------[PmcCaChangeModeEvent]冲压换模开始---------------------");
            log.info("接收参数：" + jsonParas.toString());

            String prod_line_code = "CYA";//产线编码(冲压A线)
            String station_code = "";//工位号
            String esb_interf_code = "AisReportStationToMesCa";//AIS传递过点信息到MES
            String jobject = jsonParas.getString("jobject");//传入值
            if (jobject != null && !jobject.equals("")) {
                JSONObject jsonObject = JSONObject.parseObject(jobject);
                String tag_key = "";//TAG键
                String old_val = "";//Tag值(老)
                String new_val = "";//Tag值(旧)
                SimpleDateFormat fmt = new SimpleDateFormat("yyyyMMdd");
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                JSONArray tagArray = jsonObject.getJSONArray("tag_data");
                if (tagArray != null && tagArray.size() > 0) {
                    //1、获取大屏
                    String last_update_date = "";//修改时间
                    String make_order = "";//订单
                    String theory_finish = "0";//理论完成
                    String actual_num = "0";//实际产量
                    String day_theory = "";//当班理论
                    String day_actual = "";//当班实际完成
                    String mould_switch_time = "";//模具切换时间(换模启动→换模结束)
                    String sqlScreenContent = "select last_update_date," +
                            "COALESCE(make_order,'') make_order," +
                            "COALESCE(theory_finish,'0') theory_finish," +
                            "COALESCE(actual_num,'0') actual_num," +
                            "COALESCE(day_theory,'0') day_theory," +
                            "COALESCE(day_actual,'0') day_actual " +
                            "from d_pmc_fmod_screen_set_content " +
                            "where enable_flag='Y' " +
                            "and prod_line_code='" + prod_line_code + "' ";
                    List<Map<String, Object>> itemListScreenContent = cFuncDbSqlExecute.ExecSelectSql(prod_line_code, sqlScreenContent, false, request, apiRoutePath);
                    if (itemListScreenContent != null && itemListScreenContent.size() > 0) {
                        last_update_date = itemListScreenContent.get(0).get("last_update_date").toString();
                        //if(fmt.format(last_update_date).equals(fmt.format(nowDateTime)))
                        {
                            theory_finish = itemListScreenContent.get(0).get("theory_finish").toString();
                            actual_num = itemListScreenContent.get(0).get("actual_num").toString();
                        }
                        make_order = itemListScreenContent.get(0).get("make_order").toString();
                        day_theory = itemListScreenContent.get(0).get("day_theory").toString();
                        day_actual = itemListScreenContent.get(0).get("day_actual").toString();
                    }
                    //2、修改
                    String updStation = "update d_pmc_fmod_screen_set_content set ";
                    for (int i = 0; i < tagArray.size(); i++) {
                        JSONObject tagObject = tagArray.getJSONObject(i);
                        tag_key = tagObject.getString("tag_key");
                        old_val = tagObject.getString("old_val");
                        new_val = tagObject.getString("new_val");
                        //换模启动
                        if (tag_key.contains("ADC_RUN") &&
                                new_val.equals("1")) {
                            updStation += " last_update_date='" + nowDateTime + "',";
                        }
                        //换模结束
                        else if (tag_key.contains("ADC_END") &&
                                new_val.equals("1")) {
                            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                            long arriveTime = df.parse(last_update_date).getTime();
                            long leaveTime = df.parse(nowDateTime).getTime();
                            long diffTime = (leaveTime - arriveTime) / 1000;
                            mould_switch_time = String.valueOf(diffTime);

                            updStation += " mould_switch_time='" + mould_switch_time + "',";
                        }

                        //理论完成(目标产量)
                        else if (tag_key.contains("Scheduled_Production")) {
                            updStation += " theory_finish='" + new_val + "',";
                        }
                        //实际产量
                        else if (tag_key.contains("Act_Production")) {
                            updStation += " actual_num='" + new_val + "',";
                        }
                        //实际SPM(实际节拍(10倍))
                        else if (tag_key.contains("Act_SPM")) {
                            updStation += " actual_spm='" + new_val + "',";
                        }
                        //实际模具号
                        else if (tag_key.contains("Die_ID_Act")) {
                            if (!old_val.isEmpty() &&
                                    !old_val.equals(new_val)) {
                                //执行上位MES接口(注意：跨天从0开始累加)
                                updStation += " day_theory='" + (Integer.parseInt(theory_finish) + Integer.parseInt(day_theory)) + "',";
                                updStation += " day_actual='" + (Integer.parseInt(actual_num) + Integer.parseInt(day_actual)) + "',";

                                //2、初始化接口信息
                                if (PmcCoreServer.EsbUrl == null ||
                                        PmcCoreServer.EsbUrl.isEmpty()) {
                                    pmcCoreServerInit.ServerInit();
                                }
                                //3、调用 下发焊装Hzdk
                                String interfParas = PmcCoreServer.InterfBaseInfoList.get(esb_interf_code).toString();
                                //workshop=interfParas.split("&&")[0].toString();
                                String interfUrl = interfParas.split("&&")[1].toString();
                                //格式化接口数据
                                JSONObject jsonObjectReq = new JSONObject();
                                jsonObjectReq.put("orderProd", make_order);

                                log.info("【PmcCaChangeModeEvent】下发接口传参：" + jsonObjectReq.toString());
                                JSONObject jsonObjectRes = cFuncUtilsRest.PostJbBackJb(interfUrl, jsonObjectReq);
                                log.info("【PmcCaChangeModeEvent】下发接口返回值：" + jsonObjectRes.toString());
                                Integer backCode = jsonObjectRes.getInteger("code");
                                String backMsg = jsonObjectRes.getString("msg");
                                if (backCode != 0) {
                                    errorMsg = "接口调用{" + esb_interf_code + "},异常" + backMsg;
                                    selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                                    return selectResult;
                                }
                            }
                        }

                        //整线状态
                        else if (tag_key.contains("StationFault")) {
                            updStation += " tag_line='" + new_val + "',";
                        }
                        //OP10(压机故障状态)
                        else if (tag_key.contains("CA_Device01") &&
                                tag_key.contains("Press_Alam")) {
                            updStation += " tag_op10='" + new_val + "',";
                        }
                        //OP20(压机故障状态)
                        else if (tag_key.contains("CA_Device02") &&
                                tag_key.contains("Press_Alam")) {
                            updStation += " tag_op20='" + new_val + "',";
                        }
                        //OP30(压机故障状态)
                        else if (tag_key.contains("CA_Device03") &&
                                tag_key.contains("Press_Alam")) {
                            updStation += " tag_op30='" + new_val + "',";
                        }
                        //OP40(压机故障状态)
                        else if (tag_key.contains("CA_Device04") &&
                                tag_key.contains("Press_Alam")) {
                            updStation += " tag_op40='" + new_val + "',";
                        }
                        //OP50(压机故障状态)
                        else if (tag_key.contains("CA_Device05") &&
                                tag_key.contains("Press_Alam")) {
                            updStation += " tag_op50='" + new_val + "',";
                        }
                        //只参与计算，不记录到表
                        else if (tag_key.contains("AutoIsRunning")) {

                        }

                        updStation += "last_updated_by='" + prod_line_code + "' " +
                                "where enable_flag='Y' " +
                                "and prod_line_code='" + prod_line_code + "' ";
                        cFuncDbSqlExecute.ExecUpdateSql(prod_line_code, updStation, true, null, apiRoutePath);
                    }
                }
            }

            log.info("--------------------[PmcCaChangeModeEvent]冲压换模结束---------------------");

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "冲压换模异常：" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //1.冲压机实时数据
    @Transactional
    @RequestMapping(value = "/PmcCaRealDataEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCaRealDataEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCaRealDataEvent";
        String selectResult = "";
        String errorMsg = "";
        try {
            log.info("--------------------[PmcCaRealDataEvent]冲压机实时数据开始---------------------");
            log.info("接收参数：" + jsonParas.toString());
            //1、初始化接口信息
            if (PmcCoreServer.EsbUrl == null ||
                    PmcCoreServer.EsbUrl.isEmpty()) {
                pmcCoreServerInit.ServerInit();
            }
            String esb_interf_code = "AisReportRealDataMeds";//上报MEDS冲压机实时数据
            String interfParas = PmcCoreServer.InterfBaseInfoList.get(esb_interf_code).toString();
            String interfUrl = interfParas.split("&&")[1].toString();
            //格式化接口数据
            //头
            JSONObject jsonHeader = new JSONObject();
            jsonHeader.put("biztransactionid", "MES_TO_MEDS_CY" + CFuncUtilsSystem.CreateUUID(false));
            jsonHeader.put("count", "1");
            jsonHeader.put("consumer", "");
            jsonHeader.put("srvlevel", "1");
            jsonHeader.put("account", "");
            jsonHeader.put("password", "");

            //内容
            JSONArray array = new JSONArray();
            JSONObject jobject = jsonParas.getJSONObject("jobject");//传入值
            if (jobject != null) {
                array.add(jobject);
            }

            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("head",jsonHeader);
            jsonObjectReq.put("list",array);

            log.info("【PmcCaChangeModeEvent】下发接口传参：" + jsonObjectReq.toString());
            JSONObject jsonObjectRes = this.PostJbBackJb(interfUrl, jsonObjectReq);
            log.info("【PmcCaChangeModeEvent】下发接口返回值：" + jsonObjectRes.toString());

            log.info("--------------------[PmcCaChangeModeEvent]冲压机实时数据结束---------------------");
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "冲压实时数据异常：" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    public JSONObject PostJbBackJb(String url, JSONObject jsonParas) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        // Base64加密
        String auth = "MES:MES_User1Jdsjdin9?";
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        String authHeader = "Basic " + encodedAuth;
        headers.add("Authorization", authHeader);

        HttpEntity<JSONObject> formEntity = new HttpEntity(jsonParas, headers);
        JSONObject jsonResult = (JSONObject)restTemplate.postForObject(url, formEntity, JSONObject.class, new Object[0]);
        return jsonResult;
    }
}
