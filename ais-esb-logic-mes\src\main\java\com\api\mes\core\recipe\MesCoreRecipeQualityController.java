package com.api.mes.core.recipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.core.BulkOperations;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 1.根据订单查询工位配方
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-25
 */
@RestController
@Slf4j
@RequestMapping("/mes/core/recipe")
public class MesCoreRecipeQualityController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //查询当前工序对应的质量组
    @RequestMapping(value = "/MesCoreRecipeQualitySelect", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeQualitySelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeQualitySelect";
        String selectResult="";
        String errorMsg="";
        String station_code="";
        try{
            station_code = jsonParas.getString("station_code");//工位号
            String proceduce_id= jsonParas.getString("proceduce_id");//工序ID
            String group_order = jsonParas.getString("group_order");//组号
            if(group_order==null || group_order.equals("")) group_order="0";
            Integer group_order_int=Integer.parseInt(group_order);
            String sqlQuality="select quality_id,group_order,COALESCE(tag_id,0) tag_id," +
                    "COALESCE(tag_quality_sign_id,0) tag_quality_sign_id " +
                    "from c_mes_me_cr_pdure_quality where station_code='"+station_code+"' " +
                    "and proceduce_id="+proceduce_id+" and quality_from='SCADA' ";
            if(group_order_int>0){
                sqlQuality+=" and group_order="+group_order_int+"";
            }
            sqlQuality+=" order by group_order,tag_col_order,tag_col_inner_order";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlQuality,
                    false,request,apiRoutePath);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "查询当前工位{"+station_code+"}工序对应的质量组异常:"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //查询当前工序对应的质量组
    @RequestMapping(value = "/MesCoreRecipeQualitySelect2", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeQualitySelect2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeQualitySelect2";
        String selectResult="";
        String errorMsg="";
        String station_code="";
        try{
            station_code = jsonParas.getString("station_code");//工位号
            String proceduceIds = jsonParas.getString("proceduce_id");//工序ID
            String group_order = jsonParas.getString("group_order");//组号
            if(group_order==null || group_order.equals("")) group_order="0";
            String sql="SELECT q.quality_id, q.group_order, COALESCE ( q.tag_id, 0 ) tag_id, q.proceduce_id, COALESCE ( q.tag_quality_sign_id, 0 ) tag_quality_sign_id, pdure.work_order_by FROM c_mes_me_cr_pdure_quality as q left join c_mes_me_cr_pdure as pdure on q.proceduce_id=pdure.proceduce_id WHERE q.station_code='"+station_code+"' AND q.proceduce_id IN ("+proceduceIds+")  AND q.quality_from = 'SCADA' ORDER BY pdure.work_order_by";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sql,
                    false,request,apiRoutePath);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,null,JSONObject.toJSONString(new TreeMap<>(itemList.stream().collect(Collectors.groupingBy(g -> g.get("work_order_by")))).values()) ,"",0);
        }
        catch (Exception ex){
            errorMsg= "查询当前工位{"+station_code+"}工序对应的质量组异常:"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //查询当前工序对应的质量组
    //唐山/金寨国轩 拧紧数据 程序号是0的不采集
    @RequestMapping(value = "/MesCoreRecipeQualitySelect3", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeQualitySelect3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeQualitySelect3";
        String selectResult="";
        String errorMsg="";
        String station_code="";
        try{
            station_code = jsonParas.getString("station_code");//工位号
            String proceduce_id= jsonParas.getString("proceduce_id");//工序ID
            String group_order = jsonParas.getString("group_order");//组号
            if(group_order==null || group_order.equals("")) group_order="0";
            Integer group_order_int=Integer.parseInt(group_order);
            String sqlQuality="select quality_id,group_order,COALESCE(tag_id,0) tag_id," +
                    "COALESCE(tag_quality_sign_id,0) tag_quality_sign_id " +
                    "from c_mes_me_cr_pdure_quality where station_code='"+station_code+"' " +
                    "and proceduce_id="+proceduce_id+" and quality_from='SCADA' and COALESCE(progm_num,0)!=0 ";
            if(group_order_int>0){
                sqlQuality+=" and group_order="+group_order_int+"";
            }
            sqlQuality+=" order by group_order,tag_col_order,tag_col_inner_order";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlQuality,
                    false,request,apiRoutePath);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "查询当前工位{"+station_code+"}工序对应的质量组异常:"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //保存对应的质量数据以及Recipe数据
    @RequestMapping(value = "/MesCoreRecipeQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeQualitySave";
        String transResult="";
        String errorMsg="";
        String station_code="";
        String serial_num="";
        String meQualityTable="c_mes_me_station_quality";
        String meStationFlowTable="c_mes_me_station_flow";
        String meStationRecipeTable="c_mes_me_recipe_data";
        try{
            String last_main_quality_sign="";
            String main_quality_sign="OK";
            String trace_d_time= CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(trace_d_time);
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);

            String prod_line_code=jsonParas.getString("prod_line_code");//产线编码
            String station_id=jsonParas.getString("station_id");//工位ID
            station_code = jsonParas.getString("station_code");//工位号
            String proceduce_id= jsonParas.getString("proceduce_id");//工序ID
            serial_num= jsonParas.getString("serial_num");//工件编号
            String station_flow_id=jsonParas.getString("station_flow_id");//过站ID
            String group_order=jsonParas.getString("group_order");//组号
            if(group_order==null || group_order.equals("")) group_order="-1";
            JSONArray jsonArrayData=jsonParas.getJSONArray("quality_data");
            //1.查询过站信息合格标志
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                last_main_quality_sign=docItemBigData.getString("quality_sign");
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();
            //2.查询需要保存的配方参数信息
            String sqlParas="select distinct from_tag_id " +
                    "from c_mes_fmod_recipe_craft_paras " +
                    "where from_station_id="+station_id+"";
            List<Map<String, Object>> itemListParas=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlParas,
                    false,request,apiRoutePath);
            List<String> lstParasTag=new ArrayList<>();
            if(itemListParas!=null && itemListParas.size()>0){
                for(Map<String, Object> map : itemListParas){
                    if(map.containsKey("from_tag_id")){
                        String tag_id=map.get("from_tag_id").toString();
                        if(tag_id!=null && !tag_id.equals("-1") && !tag_id.equals("0")){
                            if(!lstParasTag.contains(tag_id)) lstParasTag.add(tag_id);
                        }
                    }
                }
            }
            //3.查询当前需要保存的质量数据
            String sqlCrQuality="select quality_id,quality_from," +
                    "COALESCE(tag_id,0) tag_id," +
                    "group_order,group_name," +
                    "COALESCE(tag_col_order,1) tag_col_order," +
                    "COALESCE(tag_col_inner_order,1) tag_col_inner_order," +
                    "COALESCE(bolt_code,0) bolt_code,torque," +
                    "COALESCE(progm_num,0) progm_num,quality_for," +
                    "tag_des,tag_uom," +
                    "COALESCE(theory_value,'-1') theory_value," +
                    "COALESCE(down_limit,'-1') down_limit," +
                    "COALESCE(upper_limit,'-1') upper_limit," +
                    "quality_save_flag " +
                    "from c_mes_me_cr_pdure_quality " +
                    "where proceduce_id="+proceduce_id+" and station_code='"+station_code+"' ";
            if(!group_order.equals("-1") && !group_order.equals("0")){//按组存储
                sqlCrQuality+=" and group_order="+group_order+" ";
            }
            sqlCrQuality+=" order by group_order,tag_col_order,tag_col_inner_order";
            List<Map<String, Object>> itemListCrQuality=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlCrQuality,
                    false,request,apiRoutePath);
            //4.解析质量数据
            Map<String,String> mapQualityValue=new HashMap<>();
            Map<String,String> mapQualitySign=new HashMap<>();
            Map<String,String> mapTagValue=new HashMap<>();
            List<Map<String, Object>> lstQDocuments=new ArrayList<>();//保存质量数据集合
            List<Map<String, Object>> lstParasDocuments=new ArrayList<>();//保存配方参数集合
            if(jsonArrayData!=null && jsonArrayData.size()>0){
                for(int i=0;i<jsonArrayData.size();i++){
                    JSONObject jbItem=jsonArrayData.getJSONObject(i);
                    String quality_id=jbItem.getString("quality_id");
                    String tag_id=jbItem.getString("tag_id");
                    String tag_value=jbItem.getString("tag_value");
                    String quality_sign=jbItem.getString("quality_sign");
                    if(quality_sign.equals("OK")) quality_sign="OK";
                    else quality_sign="NG";
                    if(!mapQualityValue.containsKey(quality_id)) mapQualityValue.put(quality_id,tag_value);
                    if(!mapQualitySign.containsKey(quality_id)) mapQualitySign.put(quality_id,quality_sign);
                    if(!mapTagValue.containsKey(tag_id)) mapTagValue.put(tag_id,tag_value);
                }
            }
            //5.将要保存的信息塞入到实例
            if(itemListCrQuality!=null && itemListCrQuality.size()>0){
                for(Map<String, Object> map : itemListCrQuality){
                    String quality_id=map.get("quality_id").toString();
                    int group_order_int=Integer.parseInt(map.get("group_order").toString());
                    String group_name=map.get("group_name").toString();
                    int tag_col_order=Integer.parseInt(map.get("tag_col_order").toString());
                    long tag_id=Long.parseLong(map.get("tag_id").toString());
                    int tag_col_inner_order=Integer.parseInt(map.get("tag_col_inner_order").toString());
                    String quality_for=map.get("quality_for").toString();
                    String tag_des=map.get("tag_des").toString();
                    String tag_uom=map.get("tag_uom").toString();
                    String theory_value=map.get("theory_value").toString();
                    String down_limit_str=map.get("down_limit").toString();
                    String upper_limit_str=map.get("upper_limit").toString();
                    double down_limit=-1;
                    double upper_limit=-1;
                    try{
                        down_limit=Double.parseDouble(down_limit_str);
                    }catch (Exception ex1){}
                    try{
                        upper_limit=Double.parseDouble(upper_limit_str);
                    }catch (Exception ex2){}
                    int bolt_code=Integer.parseInt(map.get("bolt_code").toString());
                    int progm_num=Integer.parseInt(map.get("progm_num").toString());
                    String quality_save_flag=map.get("quality_save_flag").toString();
                    String tag_value="";
                    String quality_d_sign="NG";
                    String mes_quality_d_sign="NG";//这个目前没有MES判断的需求
                    if(mapQualityValue.containsKey(quality_id)) tag_value=mapQualityValue.get(quality_id).toString();
                    if(mapQualitySign.containsKey(quality_id)) quality_d_sign=mapQualitySign.get(quality_id).toString();
//                    try {
//                        if(Double.parseDouble(tag_value)<=upper_limit  && Double.parseDouble(tag_value)>=down_limit &&  upper_limit!=-1 &&  down_limit!=-1){
//                            quality_d_sign="OK";
//                        } else if (Double.parseDouble(tag_value)>=down_limit &&  upper_limit==-1) {
//                            quality_d_sign="OK";
//                        } else if (Double.parseDouble(tag_value)<=upper_limit  &&  down_limit==-1) {
//                            quality_d_sign="OK";
//                        } else if (upper_limit==-1  &&  down_limit==-1) {
//                            quality_d_sign=quality_d_sign;
//                        } else quality_d_sign="NG";
//                    }catch (Exception ex3){}
                    mes_quality_d_sign=quality_d_sign;
                    //只针对需要保存质量数据的进行判断
                    if(quality_save_flag.equals("Y")){
                        if(quality_d_sign.equals("NG")) main_quality_sign="NG";
                        Map<String, Object> mapQDataItem=new HashMap<>();
                        mapQDataItem.put("item_date",item_date);
                        mapQDataItem.put("item_date_val",item_date_val);
                        mapQDataItem.put("quality_trace_id",CFuncUtilsSystem.CreateUUID(true));
                        mapQDataItem.put("station_flow_id",station_flow_id);
                        mapQDataItem.put("prod_line_code",prod_line_code);
                        mapQDataItem.put("station_code",station_code);
                        mapQDataItem.put("serial_num",serial_num);
                        mapQDataItem.put("group_order",group_order_int);
                        mapQDataItem.put("group_name",group_name);
                        mapQDataItem.put("tag_col_order",tag_col_order);
                        mapQDataItem.put("tag_id",tag_id);
                        mapQDataItem.put("tag_col_inner_order",tag_col_inner_order);
                        mapQDataItem.put("quality_for",quality_for);
                        mapQDataItem.put("tag_des",tag_des);
                        mapQDataItem.put("tag_uom",tag_uom);
                        if(theory_value.equals("ERRORCODE_SH")){
                            String sql="SELECT  fastcode_des " +
                                    "FROM sys_fastcode " +
                                    "WHERE" +
                                    "  fastcode_group_id = 62 " +
                                    "  AND fastcode_code = '"+tag_value+"'";
                            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sql, false,request,apiRoutePath);
                            if(itemList!=null && itemList.size()>0){
                                for(int i=0;i<itemList.size();i++){
                                    theory_value = itemList.get(i).get("fastcode_des").toString();
                                }
                            }
                        }
                        mapQDataItem.put("theory_value",theory_value);
                        mapQDataItem.put("down_limit",down_limit);
                        mapQDataItem.put("upper_limit",upper_limit);
                        mapQDataItem.put("bolt_code",bolt_code);
                        mapQDataItem.put("progm_num",progm_num);
                        mapQDataItem.put("tag_value",tag_value);
                        mapQDataItem.put("quality_d_sign",quality_d_sign);
                        mapQDataItem.put("mes_quality_d_sign",mes_quality_d_sign);
                        mapQDataItem.put("trace_d_time",trace_d_time);
                        lstQDocuments.add(mapQDataItem);
                    }
                    //判断是否需要存储配方
                    if(lstParasTag.contains(String.valueOf(tag_id)) && mapQualityValue.containsKey(quality_id)){
                        Map<String, Object> mapParasItem=new HashMap<>();
                        mapParasItem.put("item_date",item_date);
                        mapParasItem.put("item_date_val",item_date_val);
                        mapParasItem.put("recipe_data_id",CFuncUtilsSystem.CreateUUID(true));
                        mapParasItem.put("prod_line_code",prod_line_code);
                        mapParasItem.put("station_code",station_code);
                        mapParasItem.put("station_id",Long.parseLong(station_id));
                        mapParasItem.put("serial_num",serial_num);
                        mapParasItem.put("tag_id",tag_id);
                        mapParasItem.put("tag_des",tag_des);
                        mapParasItem.put("tag_value",tag_value);
                        mapParasItem.put("enable_flag","Y");
                        lstParasDocuments.add(mapParasItem);
                    }
                    //更新当前工序质量实时值
                    String sqlUpdateQ="update c_mes_me_cr_pdure_quality set " +
                            "quality_status='"+quality_d_sign+"'," +
                            "tag_value='"+tag_value+"' " +
                            "where quality_id="+quality_id;
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdateQ, false,request,apiRoutePath);
                }
            }
            //6.保存质量数据
            if(lstQDocuments.size()>0){
                mongoTemplate.insert(lstQDocuments,meQualityTable);
            }
            //7.保存配方数据
            if(lstParasDocuments.size()>0){
                mongoTemplate.insert(lstParasDocuments,meStationRecipeTable);
            }
            //8.更新工位合格标志
            if(last_main_quality_sign.equals("")){
                last_main_quality_sign=main_quality_sign;
            }
            else{
                if(last_main_quality_sign.equals("OK") && main_quality_sign.equals("NG")){
                    last_main_quality_sign="NG";
                }
            }
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            Update updateBigData = new Update();
            updateBigData.set("quality_sign", last_main_quality_sign);
            mongoTemplate.updateMulti(queryBigData, updateBigData, meStationFlowTable);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "保存当前工位{"+station_code+"},当前工件{"+serial_num+"}质量数据异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //保存对应的质量数据以及Recipe数据  程序号为0时不保存
    @RequestMapping(value = "/MesCoreRecipeQualitySave2", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeQualitySave2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeQualitySave2";
        String transResult="";
        String errorMsg="";
        String station_code="";
        String serial_num="";
        String meQualityTable="c_mes_me_station_quality";
        String meStationFlowTable="c_mes_me_station_flow";
        String meStationRecipeTable="c_mes_me_recipe_data";
        try{
            String last_main_quality_sign="";
            String main_quality_sign="OK";
            String trace_d_time= CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(trace_d_time);
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);

            String prod_line_code=jsonParas.getString("prod_line_code");//产线编码
            String station_id=jsonParas.getString("station_id");//工位ID
            station_code = jsonParas.getString("station_code");//工位号
            String proceduce_id= jsonParas.getString("proceduce_id");//工序ID
            serial_num= jsonParas.getString("serial_num");//工件编号
            String station_flow_id=jsonParas.getString("station_flow_id");//过站ID
            String group_order=jsonParas.getString("group_order");//组号
            if(group_order==null || group_order.equals("")) group_order="-1";
            JSONArray jsonArrayData=jsonParas.getJSONArray("quality_data");
            //1.查询过站信息合格标志
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                last_main_quality_sign=docItemBigData.getString("quality_sign");
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();
            //2.查询需要保存的配方参数信息
            String sqlParas="select distinct from_tag_id " +
                    "from c_mes_fmod_recipe_craft_paras " +
                    "where from_station_id="+station_id+"";
            List<Map<String, Object>> itemListParas=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlParas,
                    false,request,apiRoutePath);
            List<String> lstParasTag=new ArrayList<>();
            if(itemListParas!=null && itemListParas.size()>0){
                for(Map<String, Object> map : itemListParas){
                    if(map.containsKey("from_tag_id")){
                        String tag_id=map.get("from_tag_id").toString();
                        if(tag_id!=null && !tag_id.equals("-1") && !tag_id.equals("0")){
                            if(!lstParasTag.contains(tag_id)) lstParasTag.add(tag_id);
                        }
                    }
                }
            }
            //3.查询当前需要保存的质量数据
            String sqlCrQuality="select quality_id,quality_from," +
                    "COALESCE(tag_id,0) tag_id," +
                    "group_order,group_name," +
                    "COALESCE(tag_col_order,1) tag_col_order," +
                    "COALESCE(tag_col_inner_order,1) tag_col_inner_order," +
                    "COALESCE(bolt_code,0) bolt_code,torque," +
                    "COALESCE(progm_num,0) progm_num,quality_for," +
                    "tag_des,tag_uom," +
                    "COALESCE(theory_value,'-1') theory_value," +
                    "COALESCE(down_limit,'-1') down_limit," +
                    "COALESCE(upper_limit,'-1') upper_limit," +
                    "quality_save_flag " +
                    "from c_mes_me_cr_pdure_quality " +
                    "where proceduce_id="+proceduce_id+" and station_code='"+station_code+"' and  COALESCE(progm_num,0) != '0'";
            if(!group_order.equals("-1") && !group_order.equals("0")){//按组存储
                sqlCrQuality+=" and group_order="+group_order+" ";
            }
            sqlCrQuality+=" order by group_order,tag_col_order,tag_col_inner_order";
            List<Map<String, Object>> itemListCrQuality=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlCrQuality,
                    false,request,apiRoutePath);
            //4.解析质量数据
            Map<String,String> mapQualityValue=new HashMap<>();
            Map<String,String> mapQualitySign=new HashMap<>();
            Map<String,String> mapTagValue=new HashMap<>();
            List<Map<String, Object>> lstQDocuments=new ArrayList<>();//保存质量数据集合
            List<Map<String, Object>> lstParasDocuments=new ArrayList<>();//保存配方参数集合
            if(jsonArrayData!=null && jsonArrayData.size()>0){
                for(int i=0;i<jsonArrayData.size();i++){
                    JSONObject jbItem=jsonArrayData.getJSONObject(i);
                    String quality_id=jbItem.getString("quality_id");
                    String tag_id=jbItem.getString("tag_id");
                    String tag_value=jbItem.getString("tag_value");
                    String quality_sign=jbItem.getString("quality_sign");
                    if(quality_sign.equals("OK")) quality_sign="OK";
                    else quality_sign="NG";
                    if(!mapQualityValue.containsKey(quality_id)) mapQualityValue.put(quality_id,tag_value);
                    if(!mapQualitySign.containsKey(quality_id)) mapQualitySign.put(quality_id,quality_sign);
                    if(!mapTagValue.containsKey(tag_id)) mapTagValue.put(tag_id,tag_value);
                }
            }
            //5.将要保存的信息塞入到实例
            if(itemListCrQuality!=null && itemListCrQuality.size()>0){
                for(Map<String, Object> map : itemListCrQuality){
                    String quality_id=map.get("quality_id").toString();
                    int group_order_int=Integer.parseInt(map.get("group_order").toString());
                    String group_name=map.get("group_name").toString();
                    int tag_col_order=Integer.parseInt(map.get("tag_col_order").toString());
                    long tag_id=Long.parseLong(map.get("tag_id").toString());
                    int tag_col_inner_order=Integer.parseInt(map.get("tag_col_inner_order").toString());
                    String quality_for=map.get("quality_for").toString();
                    String tag_des=map.get("tag_des").toString();
                    String tag_uom=map.get("tag_uom").toString();
                    String theory_value=map.get("theory_value").toString();
                    String down_limit_str=map.get("down_limit").toString();
                    String upper_limit_str=map.get("upper_limit").toString();
                    double down_limit=-1;
                    double upper_limit=-1;
                    try{
                        down_limit=Double.parseDouble(down_limit_str);
                    }catch (Exception ex1){}
                    try{
                        upper_limit=Double.parseDouble(upper_limit_str);
                    }catch (Exception ex2){}
                    int bolt_code=Integer.parseInt(map.get("bolt_code").toString());
                    int progm_num=Integer.parseInt(map.get("progm_num").toString());
                    String quality_save_flag=map.get("quality_save_flag").toString();
                    String tag_value="";
                    String quality_d_sign="NG";
                    String mes_quality_d_sign="NG";//这个目前没有MES判断的需求
                    if(mapQualityValue.containsKey(quality_id)) tag_value=mapQualityValue.get(quality_id).toString();
                    if(mapQualitySign.containsKey(quality_id)) quality_d_sign=mapQualitySign.get(quality_id).toString();
                    mes_quality_d_sign=quality_d_sign;
                    //只针对需要保存质量数据的进行判断
                    if(quality_save_flag.equals("Y")){
                        if(quality_d_sign.equals("NG")) main_quality_sign="NG";
                        Map<String, Object> mapQDataItem=new HashMap<>();
                        mapQDataItem.put("item_date",item_date);
                        mapQDataItem.put("item_date_val",item_date_val);
                        mapQDataItem.put("quality_trace_id",CFuncUtilsSystem.CreateUUID(true));
                        mapQDataItem.put("station_flow_id",station_flow_id);
                        mapQDataItem.put("prod_line_code",prod_line_code);
                        mapQDataItem.put("station_code",station_code);
                        mapQDataItem.put("serial_num",serial_num);
                        mapQDataItem.put("group_order",group_order_int);
                        mapQDataItem.put("group_name",group_name);
                        mapQDataItem.put("tag_col_order",tag_col_order);
                        mapQDataItem.put("tag_id",tag_id);
                        mapQDataItem.put("tag_col_inner_order",tag_col_inner_order);
                        mapQDataItem.put("quality_for",quality_for);
                        mapQDataItem.put("tag_des",tag_des);
                        mapQDataItem.put("tag_uom",tag_uom);
                        mapQDataItem.put("theory_value",theory_value);
                        mapQDataItem.put("down_limit",down_limit);
                        mapQDataItem.put("upper_limit",upper_limit);
                        mapQDataItem.put("bolt_code",bolt_code);
                        mapQDataItem.put("progm_num",progm_num);
                        mapQDataItem.put("tag_value",tag_value);
                        mapQDataItem.put("quality_d_sign",quality_d_sign);
                        mapQDataItem.put("mes_quality_d_sign",mes_quality_d_sign);
                        mapQDataItem.put("trace_d_time",trace_d_time);
                        lstQDocuments.add(mapQDataItem);
                    }
                    //判断是否需要存储配方
                    if(lstParasTag.contains(String.valueOf(tag_id)) && mapQualityValue.containsKey(quality_id)){
                        Map<String, Object> mapParasItem=new HashMap<>();
                        mapParasItem.put("item_date",item_date);
                        mapParasItem.put("item_date_val",item_date_val);
                        mapParasItem.put("recipe_data_id",CFuncUtilsSystem.CreateUUID(true));
                        mapParasItem.put("prod_line_code",prod_line_code);
                        mapParasItem.put("station_code",station_code);
                        mapParasItem.put("station_id",Long.parseLong(station_id));
                        mapParasItem.put("serial_num",serial_num);
                        mapParasItem.put("tag_id",tag_id);
                        mapParasItem.put("tag_des",tag_des);
                        mapParasItem.put("tag_value",tag_value);
                        mapParasItem.put("enable_flag","Y");
                        lstParasDocuments.add(mapParasItem);
                    }
                    //更新当前工序质量实时值
                    String sqlUpdateQ="update c_mes_me_cr_pdure_quality set " +
                            "quality_status='"+quality_d_sign+"'," +
                            "tag_value='"+tag_value+"' " +
                            "where quality_id="+quality_id;
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdateQ, false,request,apiRoutePath);
                }
            }
            //6.保存质量数据
            if(lstQDocuments.size()>0){
                mongoTemplate.insert(lstQDocuments,meQualityTable);
            }
            //7.保存配方数据
            if(lstParasDocuments.size()>0){
                mongoTemplate.insert(lstParasDocuments,meStationRecipeTable);
            }
            //8.更新工位合格标志
            if(last_main_quality_sign.equals("")){
                last_main_quality_sign=main_quality_sign;
            }
            else{
                if(last_main_quality_sign.equals("OK") && main_quality_sign.equals("NG")){
                    last_main_quality_sign="NG";
                }
            }
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            Update updateBigData = new Update();
            updateBigData.set("quality_sign", last_main_quality_sign);
            mongoTemplate.updateMulti(queryBigData, updateBigData, meStationFlowTable);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "保存当前工位{"+station_code+"},当前工件{"+serial_num+"}质量数据异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //保存对应的质量数据(质量数据不在配方里维护，解析组合而成，湘油泵项目)
    @RequestMapping(value = "/MesCoreRecipeQualitySave3", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeQualitySave3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeQualitySave3";
        String transResult="";
        String errorMsg="";
        String station_code="";
        String serial_num="";
        String meQualityTable="c_mes_me_station_quality";
        String meStationFlowTable="c_mes_me_station_flow";
        String meStationRecipeTable="c_mes_me_recipe_data";
        try{
            String last_main_quality_sign="";
            String trace_d_time= CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(trace_d_time);
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);

            String prod_line_code=jsonParas.getString("prod_line_code");//产线编码
            String station_id=jsonParas.getString("station_id");//工位ID
            station_code = jsonParas.getString("station_code");//工位号
            String proceduce_id= jsonParas.getString("proceduce_id");//工序ID
            serial_num= jsonParas.getString("serial_num");//工件编号
            String station_flow_id=jsonParas.getString("station_flow_id");//过站ID
            String group_order=jsonParas.getString("group_order");//组号
            if(group_order==null || group_order.equals("")) group_order="-1";
            String main_quality_sign=jsonParas.getString("main_quality_sign");
            JSONArray jsonArrayData=jsonParas.getJSONArray("quality_data");
            //1.查询过站信息合格标志
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                last_main_quality_sign=docItemBigData.getString("quality_sign");
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();

            //4.解析质量数据
            List<Map<String, Object>> lstQDocuments=new ArrayList<>();//保存质量数据集合
            if(jsonArrayData!=null && jsonArrayData.size()>0){
                for(int i=0;i<jsonArrayData.size();i++){
                    JSONObject jbItem=jsonArrayData.getJSONObject(i);
                    String tag_id=jbItem.getString("tag_id");
                    String tag_des=jbItem.getString("tag_des");
                    String tag_value=jbItem.getString("tag_value");
                    String quality_sign=jbItem.getString("quality_sign");
                    double down_limit=-1;
                    double upper_limit=-1;
                    Map<String, Object> mapQDataItem=new HashMap<>();
                    mapQDataItem.put("item_date",item_date);
                    mapQDataItem.put("item_date_val",item_date_val);
                    mapQDataItem.put("quality_trace_id",CFuncUtilsSystem.CreateUUID(true));
                    mapQDataItem.put("station_flow_id",station_flow_id);
                    mapQDataItem.put("prod_line_code",prod_line_code);
                    mapQDataItem.put("station_code",station_code);
                    mapQDataItem.put("serial_num",serial_num);
                    mapQDataItem.put("group_order",0);
                    mapQDataItem.put("group_name","视觉检测");
                    mapQDataItem.put("tag_col_order",i);
                    mapQDataItem.put("tag_id",tag_id);
                    mapQDataItem.put("tag_col_inner_order",i);
                    mapQDataItem.put("quality_for",tag_des);
                    mapQDataItem.put("tag_des",tag_des);
                    mapQDataItem.put("tag_uom","");
                    mapQDataItem.put("theory_value","");
                    mapQDataItem.put("down_limit",down_limit);
                    mapQDataItem.put("upper_limit",upper_limit);
                    mapQDataItem.put("bolt_code","");
                    mapQDataItem.put("progm_num","");
                    mapQDataItem.put("tag_value",tag_value);
                    mapQDataItem.put("quality_d_sign",quality_sign);
                    mapQDataItem.put("mes_quality_d_sign","");
                    mapQDataItem.put("trace_d_time",trace_d_time);
                    lstQDocuments.add(mapQDataItem);
                }
            }
            //6.保存质量数据
            if(lstQDocuments.size()>0){
                mongoTemplate.insert(lstQDocuments,meQualityTable);
            }
            //8.更新工位合格标志
            if(last_main_quality_sign.equals("")){
                last_main_quality_sign=main_quality_sign;
            }
            else{
                if(last_main_quality_sign.equals("OK") && main_quality_sign.equals("NG")){
                    last_main_quality_sign="NG";
                }
            }
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            Update updateBigData = new Update();
            updateBigData.set("quality_sign", last_main_quality_sign);
            mongoTemplate.updateMulti(queryBigData, updateBigData, meStationFlowTable);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "保存当前工位{"+station_code+"},当前工件{"+serial_num+"}质量数据异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
