
package com.api.eap.project.dy.p1.wcf;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>PanelDataUploadReportRequestBody complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="PanelDataUploadReportRequestBody"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="report_dt" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="keep_reason" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="panel_id" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="slot_no" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="edc_infos" type="{http://tempuri.org/}PanelDataUploadReportRequestEDC" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PanelDataUploadReportRequestBody", propOrder = {
    "reportDt",
    "keepReason",
    "panelId",
    "slotNo",
    "edcInfos"
})
public class PanelDataUploadReportRequestBody {

    @XmlElement(name = "report_dt")
    protected String reportDt;
    @XmlElement(name = "keep_reason")
    protected String keepReason;
    @XmlElement(name = "panel_id")
    protected String panelId;
    @XmlElement(name = "slot_no")
    protected String slotNo;
    @XmlElement(name = "edc_infos")
    protected PanelDataUploadReportRequestEDC edcInfos;

    /**
     * 获取reportDt属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReportDt() {
        return reportDt;
    }

    /**
     * 设置reportDt属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReportDt(String value) {
        this.reportDt = value;
    }

    /**
     * 获取keepReason属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKeepReason() {
        return keepReason;
    }

    /**
     * 设置keepReason属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKeepReason(String value) {
        this.keepReason = value;
    }

    /**
     * 获取panelId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPanelId() {
        return panelId;
    }

    /**
     * 设置panelId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPanelId(String value) {
        this.panelId = value;
    }

    /**
     * 获取slotNo属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSlotNo() {
        return slotNo;
    }

    /**
     * 设置slotNo属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSlotNo(String value) {
        this.slotNo = value;
    }

    /**
     * 获取edcInfos属性的值。
     * 
     * @return
     *     possible object is
     *     {@link PanelDataUploadReportRequestEDC }
     *     
     */
    public PanelDataUploadReportRequestEDC getEdcInfos() {
        return edcInfos;
    }

    /**
     * 设置edcInfos属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link PanelDataUploadReportRequestEDC }
     *     
     */
    public void setEdcInfos(PanelDataUploadReportRequestEDC value) {
        this.edcInfos = value;
    }

}
