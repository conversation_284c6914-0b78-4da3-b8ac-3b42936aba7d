package com.api.eap.project.guangxin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.eap.project.guangxin.event.BatchesFinishedEvent;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

/**
 * <p>
 * EAP广芯事件服务
 * 1. 处理完批事件
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-1
 */
@Service
@Slf4j
public class EapGxEventService {
    private final MongoTemplate mongoTemplate;

    public EapGxEventService(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @EventListener
    public void handleBatchesFinishedEvent(BatchesFinishedEvent event) {
        JSONArray loadTaskSet = event.getSource(); // 收板任务集合
        for (int i = 0; i < loadTaskSet.size(); i++) {
            JSONObject loadTask = loadTaskSet.getJSONObject(i);
//            String groupLotNum = loadTask.getString("group_lot_num");
//            // 提取实际母批单号，根据母批号查询放板机任务
//            String[] groupLotNums = groupLotNum.split("_");
//            groupLotNum = groupLotNums[0]; // 实际母批单号
            String lot_num = loadTask.getString("lot_num").substring(0, 8);//用lot_num前8位校验
            // 计划任务母批总数量：plan_lot_count
            int planLotCount = loadTask.getInteger("plan_lot_count"); // 计划任务母批总数量
            int currentTargetLotCount = loadTask.getInteger("target_lot_count"); // 当前目标数量
            int currentFinishOkCount = loadTask.getInteger("finish_ok_count"); // 当前OK数量
            int groupFinishOkCount = 0; // 母批总OK数量
            int groupFinishNgCount = 0; // 母批总NG数量
            int totalPlans = 0; // 总计划数
            Query groupIsFinishQuery = new Query();
            // 母批所有任务：group_lot_num + ".*" 模糊查询
//            groupIsFinishQuery.addCriteria(Criteria.where("group_lot_num").regex(groupLotNum + ".*"));
            groupIsFinishQuery.addCriteria(Criteria.where("lot_num").regex(lot_num));
            // 母批所有任务按照item_date_val升序排序
            groupIsFinishQuery.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
            // 收放一体机：attribute1 is Y
            if (event.isIntegrated()) {
                groupIsFinishQuery.addCriteria(Criteria.where("attribute1").is("Y"));
            }
            // 状态限制：group_lot_status is FINISH
            groupIsFinishQuery.addCriteria(Criteria.where("group_lot_status").is("FINISH"));
            String procedureCode = null;
            for (Document docItemData : mongoTemplate.getCollection("a_eap_aps_plan").
                    find(groupIsFinishQuery.getQueryObject()).
                    sort(groupIsFinishQuery.getSortObject()).
                    noCursorTimeout(false)) {
                // 当工序代码不一致时，重新计算
                String otherAttributeJSON = docItemData.getString("other_attribute");
                try {
                    JSONObject otherAttribute = JSONObject.parseObject(otherAttributeJSON);
                    if (otherAttribute != null) {
                        String newProcedureCode = otherAttribute.getString("ProcedureCode");
                        if (procedureCode == null) {
                            procedureCode = newProcedureCode;
                        } else if (!newProcedureCode.equals(procedureCode)) {
                            procedureCode = newProcedureCode;
                            totalPlans = 0;
                            groupFinishOkCount = 0;
                            groupFinishNgCount = 0;
                        }
                    }
                } catch (Exception e) {
                    log.error("解析other_attribute异常：{}", otherAttributeJSON, e);
                }
                int itemFinishOkCount = docItemData.getInteger("finish_ok_count");
                int itemFinishNgCount = docItemData.getInteger("finish_ng_count");
                groupFinishOkCount += itemFinishOkCount;
                groupFinishNgCount += itemFinishNgCount;
                totalPlans++;
            }
            // 子批完批 默认值
            loadTask.put("task_finish_type", EapGxConst.SUB_BATCHES_FINISHED);
            //20250210屏蔽，需求变更：收板机母批完批上报需求由--有读码NG的批次，完批上报1,变更为---完批时不判断读码NG条件，其它判断条件不变，有读码NG的批次母批完批也上报2
//            if (groupFinishNgCount > 0)
//            {
//                return;
//            }
            switch (event.getEquipmentType()) {
                case EapGxConst.EQUIPMENT_TYPES_MAGAZINE:
                    // magazine机型：
                    // 1、批次数量小于等于24时，不管实际收板数量，只要与放板机当前批次放板数量一致时，报母批完批（值为2）；
                    // 2、批次数量大于24时，第1批报子批完批（值为1），第2批报母批完批（值为2）。 -- 批次数量大于24时，不管实际收板数量有多少，都会分成两框去收
//                    boolean isGroupFinish = totalPlans > 1 && (totalPlans % 2) == 0;
                    boolean isGroupFinish = totalPlans == 2;
                    if (planLotCount <= 24 && currentFinishOkCount == currentTargetLotCount) {
                        // 母批完批
                        loadTask.put("task_finish_type", EapGxConst.GROUP_BATCHES_FINISHED);
                        if (groupFinishOkCount != planLotCount) {
                            loadTask.put("task_unusual_finish", "2");
                        }
                    } else if (planLotCount > 24 && currentFinishOkCount == currentTargetLotCount && isGroupFinish) {
                        // 母批完批
                        loadTask.put("task_finish_type", EapGxConst.GROUP_BATCHES_FINISHED);
                        if (groupFinishOkCount != planLotCount) {
                            loadTask.put("task_unusual_finish", "2");
                        }
                    }
                    break;
                case EapGxConst.EQUIPMENT_TYPES_MAGAZINE_AIO:
                    // magazine机型（一体机）：CCD打标一体机型放板的时候根本没读码，放板的时候AIS没有计数。报完批的时候不去比较实际放板的数量了。
                    // 1、批次数量小于等于24就报1；
                    // 2、大于24第一批报1第二批报2。
                    isGroupFinish = totalPlans == 2;
                    if (planLotCount <= 24) {
                        // 母批完批
                        loadTask.put("task_finish_type", EapGxConst.GROUP_BATCHES_FINISHED);
                        if (groupFinishOkCount != planLotCount) {
                            loadTask.put("task_unusual_finish", "2");
                        }
                    } else if (isGroupFinish) {
                        // 母批完批
                        loadTask.put("task_finish_type", EapGxConst.GROUP_BATCHES_FINISHED);
                        if (groupFinishOkCount != planLotCount) {
                            loadTask.put("task_unusual_finish", "2");
                        }
                    }
                    break;
                case EapGxConst.EQUIPMENT_TYPES_FOUP:
                    // foup机型：
                    // 1、根据批次数量判断是否完批，收板机收板做数量累计，当累计数量等于批次总数量时，报完批（值为2），其余情况报子批完批（值为1）；
                    // 2、如果出现混批，导致收板数量没达到批次总数量时报子批完批（值为1）。
                    if (groupFinishOkCount == planLotCount) {
                        // 母批完批
                        loadTask.put("task_finish_type", EapGxConst.GROUP_BATCHES_FINISHED);
                        if (groupFinishOkCount != planLotCount) {
                            loadTask.put("task_unusual_finish", "2");
                        }
                    }
                    break;
            }
        }
    }
}
