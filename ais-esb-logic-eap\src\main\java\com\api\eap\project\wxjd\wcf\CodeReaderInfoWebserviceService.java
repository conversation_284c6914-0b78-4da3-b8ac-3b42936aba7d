/**
 * CodeReaderInfoWebserviceService.java
 *
 * This file was auto-generated from WSDL
 * by the Apache Axis 1.4 Apr 22, 2006 (06:55:48 PDT) WSDL2Java emitter.
 */

package com.api.eap.project.wxjd.wcf;

public interface CodeReaderInfoWebserviceService extends javax.xml.rpc.Service {
    public java.lang.String getcodeReaderInfoWebserviceAddress();

    public com.api.eap.project.wxjd.wcf.CodeReaderInfoWebservice_PortType getcodeReaderInfoWebservice() throws javax.xml.rpc.ServiceException;

    public com.api.eap.project.wxjd.wcf.CodeReaderInfoWebservice_PortType getcodeReaderInfoWebservice(java.net.URL portAddress) throws javax.xml.rpc.ServiceException;
}
