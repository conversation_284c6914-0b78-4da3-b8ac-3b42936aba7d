package com.api.eap.project.dy.p1.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.dy.EapDyInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import com.api.eap.project.dy.p1.wcf.*;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.text.SimpleDateFormat;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Map;
import java.util.Date;
import java.util.Calendar;


/**
 * <p>
 * 定颖(P1)EAP事件接口(Sub)公共方法
 * 1.UserVerify:[接口]用户登入登出验证
 * 3.EQPInfoVerify:[接口]检测与EAP通讯并且返回EAP时间由应用进行更新本地时间
 * 4.AlarmReport:[接口]上报报警信息到EAP
 * 5.RealStatusReport:[接口]设备状态上报到EAP
 * 6.StatusChangeReport:[接口]只要报警与状态发生任何变化都需要进行上报到EAP
 * 7.ParamVerify:[接口]查询工单信息
 * 8.UtilityReport:[接口]上报三率
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapDyP1SendEventSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyP1InterfCommon eapDyP1InterfCommon;

    //1.[接口]用户登入登出验证
    public JSONObject UserVerify(Boolean checkOut, String station_code, String user_id) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "UserVerify";
        String esbInterfCode = "UserVerify";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //创建实例
            com.api.eap.project.dy.p1.wcf.DyService service=new com.api.eap.project.dy.p1.wcf.DyService();
            DyServiceSoap serviceSoap= service.getDyServiceSoap();

            //1.创建参数
            JSONObject postParas = new JSONObject();
            RequestHead requestHead=eapDyP1InterfCommon.CreateRequestHeader(funcName, station_code);
            UserVerifyRequestBody requestBody=new UserVerifyRequestBody();
            requestBody.setUserId(user_id);
            JSONObject request_head=(JSONObject)JSONObject.toJSON(requestHead);
            JSONObject request_body=(JSONObject)JSONObject.toJSON(requestBody);
            postParas.put("request_head",request_head);
            postParas.put("request_body",request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject response_body=new JSONObject();
            if(!checkOut){
                UserVerifyResponseItem userVerifyResponseItem= serviceSoap.userVerify(requestHead,requestBody);
                JSONObject jsonObjectBack=(JSONObject)JSONObject.toJSON(userVerifyResponseItem);
                responseParas = jsonObjectBack.toString();
                eapDyP1InterfCommon.CheckResponseHeader(userVerifyResponseItem.getResponseHead());
                UserVerifyResponseBody responseBody= userVerifyResponseItem.getResponseBody();
                if(responseBody==null){
                    errorMsg = "EAP返回response_body数据格式必须包含dept_id|shift_id|user_cname|user_ename字段名";
                    jbResult.put("requestParas", requestParas);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                response_body.put("user_name",responseBody.getUserCname());
                response_body.put("nick_name",responseBody.getUserEname());
                response_body.put("dept_id",responseBody.getDeptId());
                response_body.put("shift_id",responseBody.getShiftId());
            }
            else{
                response_body.put("user_name","");
                response_body.put("nick_name","");
                response_body.put("dept_id","");
                response_body.put("shift_id","");
            }

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.[接口]检测与EAP通讯并且返回EAP时间由应用进行更新本地时间
    public JSONObject EQPInfoVerify(String station_code) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "EQPInfoVerify";
        String esbInterfCode = "EQPInfoVerify";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //创建实例
            com.api.eap.project.dy.p1.wcf.DyService service=new com.api.eap.project.dy.p1.wcf.DyService();
            DyServiceSoap serviceSoap= service.getDyServiceSoap();

            //1.创建参数
            JSONObject postParas = new JSONObject();
            RequestHead requestHead=eapDyP1InterfCommon.CreateRequestHeader(funcName, station_code);
            JSONObject request_head=(JSONObject)JSONObject.toJSON(requestHead);
            postParas.put("request_head",request_head);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject response_body=new JSONObject();
            EqpInfoVerifyResponseItem eqpInfoVerifyResponseItem =serviceSoap.eqpInfoVerify(requestHead);
            JSONObject jsonObjectBack=(JSONObject)JSONObject.toJSON(eqpInfoVerifyResponseItem);
            responseParas = jsonObjectBack.toString();
            eapDyP1InterfCommon.CheckResponseHeader(eqpInfoVerifyResponseItem.getResponseHead());
            EqpInfoVerifyResponseBody responseBody= eqpInfoVerifyResponseItem.getResponseBody();
            if(responseBody==null){
                errorMsg = "EAP返回response_body数据格式为空";
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            XMLGregorianCalendar xmlGregorianCalendar=responseBody.getNow();
            GregorianCalendar gregorianCalendar = xmlGregorianCalendar.toGregorianCalendar();
            Date eapDateTime=new Date(gregorianCalendar.getTimeInMillis());
            //处理格式转换
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String eapDateTimeStr=df.format(eapDateTime);
            response_body.put("now",eapDateTimeStr);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.[接口]上报报警信息到EAP
    public JSONObject AlarmReport(Long station_id, String station_code, String reset_flag, String warn_flag,
                                  String alarm_id, String alarm_desc, String local_flag, String status_code,
                                  String offline_flag) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "AlarmReport";
        String esbInterfCode = "AlarmReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            String alarm_type = "S";
            String alarm_code = "A";
            String keep_reason = "0";
            if(offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";
            if (reset_flag.equals("Y")) alarm_type = "R";
            if (warn_flag.equals("Y")) alarm_code = "W";

            //创建实例
            com.api.eap.project.dy.p1.wcf.DyService service=new com.api.eap.project.dy.p1.wcf.DyService();
            DyServiceSoap serviceSoap= service.getDyServiceSoap();

            //1.创建参数
            RequestHead requestHead=eapDyP1InterfCommon.CreateRequestHeader(funcName, station_code);
            AlarmReportRequestBody requestBody=new AlarmReportRequestBody();
            //获取当前时间
            GregorianCalendar cal = new GregorianCalendar();
            XMLGregorianCalendar date1 = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
            requestBody.setReportDt(date1);
            requestBody.setKeepReason(keep_reason);//0直接上传、1离线、2超时
            requestBody.setAlarmId(alarm_id);
            requestBody.setAlarmCode(alarm_code);
            requestBody.setAlarmType(alarm_type);//S：發生警報、R：警報解除
            requestBody.setAlarmDesc(alarm_desc);
            JSONObject request_head=(JSONObject)JSONObject.toJSON(requestHead);
            request_body=(JSONObject)JSONObject.toJSON(requestBody);
            postParas.put("request_head",request_head);
            postParas.put("request_body",request_body);
            requestParas = postParas.toString();

            //2.若为离线,则放入离线上报
            Boolean isOffSave=false;
            if(offline_flag.equals("Y") || local_flag.equals("Y")) isOffSave=true;
            if (isOffSave) {//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //4.发送数据
            Response alarmResponseItem =serviceSoap.alarmReport(requestHead,requestBody);
            JSONObject jsonObjectBack=(JSONObject)JSONObject.toJSON(alarmResponseItem);
            responseParas = jsonObjectBack.toString();

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            if (ex.getMessage().indexOf("timed out")!=-1) {
                request_body.put("keepReason", "2");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            else {
                request_body.put("keepReason", "1");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //5.[接口]设备状态上报到EAP
    public JSONObject RealStatusReport(String station_code, String local_flag, String auto_flag,
                                       String stop_flag, String repair_flag, String hold_flag,
                                       String status_code, String reset_flag, String warn_flag, String alarm_id,
                                       String short_bettery_flag, String power_on_time, String idle_delay_time,
                                       String current_lot_count, String history_lot_count, String red_light_flag,
                                       String yellow_light_flag, String green_light_flag, String blue_light_flag,
                                       String user_name, String lot_num, String eap_offline_flag, String plc_offline_flag,
                                       String prod_mode, String alarm_desc, String offline_flag,
                                       String station_attr) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "RealStatusReport";
        String esbInterfCode = "RealStatusReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //创建实例
            com.api.eap.project.dy.p1.wcf.DyService service=new com.api.eap.project.dy.p1.wcf.DyService();
            DyServiceSoap serviceSoap= service.getDyServiceSoap();

            String cim_mode = "Remote";
            if (offline_flag.equals("Y")) cim_mode = "Local";
            if(local_flag.equals("Y")) cim_mode = "Local";
            String auto_mode = "Auto";
            if (!auto_flag.equals("Y")) {
                auto_mode = "Manual";
                //cim_mode = "Local";
            }
            String alarm_type = "R";
            String alarm_alarm_id = "0";
            String warn_type = "R";
            String warn_id = "0";
            if (alarm_id != null && !alarm_id.equals("")) {
                if (warn_flag.equals("N")) {//重大报警
                    alarm_alarm_id = alarm_id;
                    if (reset_flag.equals("N")) alarm_type = "S";
                } else {
                    if (reset_flag.equals("N")) warn_type = "S";
                }
            }
            String tower_red = "OFF";
            String tower_yellow = "OFF";
            String tower_green = "OFF";
            String tower_blue = "OFF";
            if (red_light_flag.equals("Y")) tower_red = "ON";
            if (yellow_light_flag.equals("Y")) tower_yellow = "ON";
            if (green_light_flag.equals("Y")) tower_green = "ON";
            if (blue_light_flag.equals("Y")) tower_blue = "ON";

            //1.创建参数
            JSONObject postParas = new JSONObject();
            RequestHead requestHead=eapDyP1InterfCommon.CreateRequestHeader(funcName, station_code);
            RealStatusReportRequestBody requestBody=new RealStatusReportRequestBody();
            requestBody.setStatusId(status_code);
            requestBody.setAutoMode(auto_mode);
            requestBody.setCimMode(cim_mode);
            requestBody.setAlarmId(alarm_alarm_id);
            requestBody.setTowerYellow(tower_yellow);
            requestBody.setTowerBlue(tower_blue);
            requestBody.setTowerGreen(tower_green);
            requestBody.setTowerRed(tower_red);
            JSONObject request_head=(JSONObject)JSONObject.toJSON(requestHead);
            JSONObject request_body=(JSONObject)JSONObject.toJSON(requestBody);
            postParas.put("request_head",request_head);
            postParas.put("request_body",request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject response_body=new JSONObject();
            Response realStatusResponseItem =serviceSoap.realStatusReport(requestHead,requestBody);
            JSONObject jsonObjectBack=(JSONObject)JSONObject.toJSON(realStatusResponseItem);
            responseParas = jsonObjectBack.toString();

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //6.[接口]只要报警与状态发生任何变化都需要进行上报到EAP
    public JSONObject StatusChangeReport(Long station_id, String station_code, String auto_flag,
                                         String status_code, String alarm_id, String local_flag,
                                         String alarm_desc, String offline_flag) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "StatusChangeReport";
        String esbInterfCode = "StatusChangeReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //创建实例
            com.api.eap.project.dy.p1.wcf.DyService service=new com.api.eap.project.dy.p1.wcf.DyService();
            DyServiceSoap serviceSoap= service.getDyServiceSoap();

            //1.创建参数
            String cim_mode = "Remote";
            if (offline_flag.equals("Y")) cim_mode = "Local";
            if(local_flag.equals("Y")) cim_mode = "Local";
            String auto_mode = "Auto";
            if (!auto_flag.equals("Y")) {
                auto_mode = "Manual";
                //cim_mode = "Local";
            }
            String keep_reason = "0";
            if(offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";

            postParas = new JSONObject();
            RequestHead requestHead=eapDyP1InterfCommon.CreateRequestHeader(funcName, station_code);
            StatusChangeReportRequestBody requestBody=new StatusChangeReportRequestBody();
            GregorianCalendar cal = new GregorianCalendar();
            XMLGregorianCalendar date1 = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
            requestBody.setReportDt(date1);
            requestBody.setKeepReason(keep_reason);
            requestBody.setStatusId(status_code);
            requestBody.setAutoMode(auto_mode);
            requestBody.setCimMode(cim_mode);
            requestBody.setAlarmId(alarm_id);
            JSONObject request_head=(JSONObject)JSONObject.toJSON(requestHead);
            request_body=(JSONObject)JSONObject.toJSON(requestBody);
            postParas.put("request_head",request_head);
            postParas.put("request_body",request_body);
            requestParas = postParas.toString();


            //2.若为离线,则放入离线上报
            Boolean isOffSave=false;
            if(offline_flag.equals("Y") || local_flag.equals("Y")) isOffSave=true;
            if (isOffSave) {//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //4.发送数据
            JSONObject response_body=new JSONObject();
            Response statusChangeResponseItem =serviceSoap.statusChangeReport(requestHead,requestBody);
            JSONObject jsonObjectBack=(JSONObject)JSONObject.toJSON(statusChangeResponseItem);
            responseParas = jsonObjectBack.toString();
            eapDyP1InterfCommon.CheckResponseHeader(statusChangeResponseItem.getResponseHead());

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            if (ex.getMessage().indexOf("timed out")!=-1) {
                request_body.put("keepReason", "2");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            else {
                request_body.put("keepReason", "1");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //7.[接口]查询工单信息
    public JSONObject ParamVerify(String station_code, String production_mode, String lot_num) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "ParamVerify";
        String esbInterfCode = "ParamVerify";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //创建实例
            com.api.eap.project.dy.p1.wcf.DyService service=new com.api.eap.project.dy.p1.wcf.DyService();
            DyServiceSoap serviceSoap= service.getDyServiceSoap();

            //1.创建参数
            JSONObject postParas = new JSONObject();
            RequestHead requestHead=eapDyP1InterfCommon.CreateRequestHeader(funcName, station_code);
            ParamVerifyRequestBody requestBody=new ParamVerifyRequestBody();
            requestBody.setLotId(lot_num);
            JSONObject request_head=(JSONObject)JSONObject.toJSON(requestHead);
            JSONObject request_body=(JSONObject)JSONObject.toJSON(requestBody);
            postParas.put("request_head",request_head);
            postParas.put("request_body",request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            ParamVerifyResponseItem paramVerifyResponseItem =serviceSoap.paramVerify(requestHead,requestBody);
            JSONObject jsonObjectBack=(JSONObject)JSONObject.toJSON(paramVerifyResponseItem);
            responseParas = jsonObjectBack.toString();
            eapDyP1InterfCommon.CheckResponseHeader(paramVerifyResponseItem.getResponseHead());
            ParamVerifyResponseBody responseBody=paramVerifyResponseItem.getResponseBody();
            JSONObject response_body=(JSONObject)JSONObject.toJSON(responseBody);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //8.[接口]三率上报
    public JSONObject UtilityReport(Long station_id, String station_code, String shfit_id, JSONArray item_list,
                                    String report_dt, String offline_flag, String local_flag) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "UtilityReport";
        String esbInterfCode = "UtilityReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //创建实例
            com.api.eap.project.dy.p1.wcf.DyService service=new com.api.eap.project.dy.p1.wcf.DyService();
            DyServiceSoap serviceSoap= service.getDyServiceSoap();

            //1.创建参数
            String keep_reason = "0";
            if(offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";

            postParas = new JSONObject();
            RequestHead requestHead=eapDyP1InterfCommon.CreateRequestHeader(funcName, station_code);
            UtilityReportRequestBody requestBody=new UtilityReportRequestBody();
            GregorianCalendar cal = new GregorianCalendar();
            XMLGregorianCalendar date1 = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
            requestBody.setReportDt(date1);
            requestBody.setKeepReason(keep_reason);//0直接上传、1离线、2超时
            requestBody.setShiftId(shfit_id);
            JSONObject request_head=(JSONObject)JSONObject.toJSON(requestHead);
            request_body=(JSONObject)JSONObject.toJSON(requestBody);
            postParas.put("request_head",request_head);
            postParas.put("request_body",request_body);
            requestParas = postParas.toString();

            //2.若为离线,则放入离线上报
            Boolean isOffSave=false;
            if(offline_flag.equals("Y") || local_flag.equals("Y")) isOffSave=true;
            if (isOffSave) {//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //4.发送数据
            JSONObject response_body=new JSONObject();
            Response utilityReportResponseItem =serviceSoap.utilityReport(requestHead,requestBody);
            JSONObject jsonObjectBack=(JSONObject)JSONObject.toJSON(utilityReportResponseItem);
            responseParas = jsonObjectBack.toString();
            eapDyP1InterfCommon.CheckResponseHeader(utilityReportResponseItem.getResponseHead());

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            if (ex.getMessage().indexOf("timed out")!=-1) {
                request_body.put("keepReason", "2");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            else {
                request_body.put("keepReason", "1");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
