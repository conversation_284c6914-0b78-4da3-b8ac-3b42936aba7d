package com.api.eap.project.thailand.guanghe;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 泰国广合接受事件接口
 * 1.HMI报警信息下发
 * 2.
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/guanghe/interf/recv")
public class EapTlGhRecvEventController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapTlGhRecvEventFunc eapTlGhRecvEventFunc;

    //1.HMI报警信息下发
    @RequestMapping(value = "/CIMMessageCommand",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapTlGhCIMMessageCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/guanghe/interf/recv/CIMMessageCommand";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapTlGhRecvEventFunc.CIMMessageCommand(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //2.配方验证结果下发
    @RequestMapping(value = "/RecipeValidationResultCommand",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapTlGhRecipeValidationResultCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/guanghe/interf/recv/RecipeValidationResultCommand";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapTlGhRecvEventFunc.RecipeValidationResultCommand(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }
}
