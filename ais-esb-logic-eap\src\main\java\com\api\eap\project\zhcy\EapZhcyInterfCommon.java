package com.api.eap.project.zhcy;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <p>
 * 志圣EAP接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @Date 2025-04-07
 * @since 
 */
@Service
public class EapZhcyInterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private RestTemplate restTemplate;
    
    //创建报文Header
    public static JSONObject CreateHeader(String func_name) {
        String nowDate = CFuncUtilsSystem.GetNowDateTime("yyyy/MM/dd HH:mm:ss");
        JSONObject jbHeader = new JSONObject();
        jbHeader.put("From", "EQP");
        jbHeader.put("Message", func_name);
        jbHeader.put("DateTime", nowDate);
        jbHeader.put("RequestId", CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmssSSS"));
        return jbHeader;
    }
    
    //创建报文Header
    public static JSONObject CreateHeader(String func_name,String requestId) {
        String nowDate = CFuncUtilsSystem.GetNowDateTime("yyyy/MM/dd HH:mm:ss");
        JSONObject jbHeader = new JSONObject();
        jbHeader.put("From", "EQP");
        jbHeader.put("Message", func_name);
        jbHeader.put("DateTime", nowDate);
        jbHeader.put("RequestId", requestId);
        return jbHeader;
    }
    
    //创建报文Result
//    public static JSONObject CreateResult(boolean success, String code, String message,String requestId) {
//        JSONObject jbResult = new JSONObject();
//        jbResult.put("Code", code);
//        jbResult.put("Success", success);
//        jbResult.put("Msg", message);
//        jbResult.put("DateTime", CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss"));
//        jbResult.put("Content", new JSONObject());
//        jbResult.put("RequestId", requestId);
//        return jbResult;
//    }

    //创建失败报文Result
//    public static JSONObject CreateFaillResult(String message,String requestId) {
//        return CreateFaillResult(message, requestId, "", "");
//    }
    
   /**
    * 创建失败报文Result
    * @param message
    * @param esbInterfCode
    * @param requestParas
    * @return
    */
//	  public static JSONObject CreateFaillResult(String message,String esbInterfCode,String requestParas) {
//	      return CreateFaillResult(message, "", esbInterfCode,requestParas, "");
//	  }
	  
	  /***
	   * 创建失败报文Result
	   * @param message
	   * @param requestId
	   * @param esbInterfCode
	   * @param requestParas
	   * @return
	   */
	  public static JSONObject CreateFaillResult(String message,String requestId,String esbInterfCode,String requestParas) {
	      return CreateFaillResult(message, requestId, esbInterfCode, requestParas, "");
	  }
    
    /***
     * 创建失败报文Result
     * @param message
     * @param requestId
     * @param esbInterfCode
     * @param requestParas
     * @param token
     * @return
     */
    public static JSONObject CreateFaillResult(String message,String requestId,String esbInterfCode,String requestParas,String token) {
        JSONObject jbResult = new JSONObject();
        jbResult.put("Code", "10002");
        jbResult.put("Success", false);
        jbResult.put("Msg", message);
        jbResult.put("DateTime", CFuncUtilsSystem.GetNowDateTime("yyyy/MM/dd HH:mm:ss"));
        jbResult.put("Content", new JSONObject());
        jbResult.put("RequestId", requestId);
        jbResult.put("isSaveFlag",true);
        return jbResult;
    }

    /**
     * 创建报文Result
     * @param message
     * @param requestId
     * @param content
     * @return
     */
    public static JSONObject CreateSuccessResult(String message,String requestId,JSONObject content) {
        JSONObject jbResult = new JSONObject();
        jbResult.put("Code", "0000");
        jbResult.put("Success", true);
        jbResult.put("Msg", message);
        jbResult.put("DateTime", CFuncUtilsSystem.GetNowDateTime("yyyy/MM/dd HH:mm:ss"));
        jbResult.put("Content", content);
        jbResult.put("RequestId", requestId);
        return jbResult;
    }
    
    /**
     * 创建报文Result
     * @param success
     * @param code
     * @param message
     * @param requestId
     * @param content
     * @return
     */
    public static JSONObject CreateResult(boolean success, String code, String message,String requestId,JSONObject content) {
        JSONObject jbResult = new JSONObject();
        jbResult.put("Code", code);
        jbResult.put("Success", success);
        jbResult.put("Msg", message);
        jbResult.put("DateTime", CFuncUtilsSystem.GetNowDateTime("yyyy/MM/dd HH:mm:ss"));
        jbResult.put("Content", content);
        jbResult.put("RequestId", requestId);
        return jbResult;
    }
    
    /**
     * 创建发送参数报文
     * @param func_name
     * @param jbContent
     * @return
     */
    public static JSONObject CreateSendParas(String func_name, JSONObject jbContent) {
        JSONObject jbSendParas = new JSONObject();
        JSONObject jbHeader = CreateHeader(func_name);
        jbSendParas.put("From", jbHeader.getString("From"));
        jbSendParas.put("Message", jbHeader.getString("Message"));
        jbSendParas.put("DateTime", jbHeader.getString("DateTime"));
        jbSendParas.put("Content", jbContent);
        jbSendParas.put("RequestId", jbHeader.getString("RequestId"));
        return jbSendParas;
    }

    //执行接口
    public JSONObject PostJbBackJb(String url, JSONObject jsonParas) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<JSONObject> formEntity = new HttpEntity<>(jsonParas, headers);
        return restTemplate.postForObject(url, formEntity, JSONObject.class);
    }
    
    /**
     * 转换响应格式，给event模块使用。
     * @param originalResponse 原始响应
     * @return 转换后的响应
     */
    public static JSONObject convertToEventFormat(JSONObject originalResponse) {
        JSONObject convertedResponse = new JSONObject();
        if(!originalResponse.getBoolean("Success")) {
        	convertedResponse.put("error", originalResponse.getString("Msg"));
        }
        convertedResponse.put("result", originalResponse.getBoolean("Success") ? "success" : "error");
        convertedResponse.put("data", originalResponse.getJSONObject("Content"));
        return convertedResponse;
    }
    
    /**
     * 转换响应格式，给event模块使用。
     * @param originalResponse 原始响应
     * @return 转换后的响应
     */
    public static JSONObject convertToWebFormat(JSONObject originalResponse) {
        JSONObject convertedResponse = new JSONObject();
        if(!originalResponse.getBoolean("Success")) {
        	convertedResponse.put("msg", originalResponse.getString("Msg"));
        }else{
        	convertedResponse.put("Success", true);
        }
        convertedResponse.put("result", originalResponse.getBoolean("Success") ? "success" : "error");
        convertedResponse.put("code", originalResponse.getBoolean("Success") ? "0" : "9999");
        convertedResponse.put("data", originalResponse.getJSONObject("Content"));
        return convertedResponse;
    }
    
}