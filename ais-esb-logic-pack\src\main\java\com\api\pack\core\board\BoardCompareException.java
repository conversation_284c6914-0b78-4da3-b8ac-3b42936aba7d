package com.api.pack.core.board;

import com.api.pack.core.sort.SortException;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 板件比较异常类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@EqualsAndHashCode(callSuper = true)
public class BoardCompareException extends SortException
{
    public BoardCompareException(String message)
    {
        super(message);
    }

    public BoardCompareException(String message, String sortCode)
    {
        super(message, sortCode);
    }

    public BoardCompareException(String message, String status, int code)
    {
        super(message, status, code);
    }

    public BoardCompareException(String message, String status, int code, String sortCode)
    {
        super(message, status, code, sortCode);
    }
}
