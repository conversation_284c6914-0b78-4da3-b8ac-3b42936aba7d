package com.api.pack.project.tripod.op;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pack.core.board.Board;
import com.api.pack.core.board.BoardConst;
import com.api.pack.core.board.SETBoard;
import com.api.pack.core.ccd.CCDScanMessage;
import com.api.pack.core.sort.SortConst;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;

/**
 * <p>
 * Array方法
 * 1.获取Array与BD解析数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Slf4j
@Service
public class PackTripodOpArrayFuncOfYN
{
    @Autowired
    private final CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private final CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private final MongoTemplate mongoTemplate;

    private final List<SortRule> setSortRules = new LinkedList<>();

    private final List<SortRule> pcsSortRules = new LinkedList<>();

    private final Comparator directionComparator = new DirectionComparator();

    public PackTripodOpArrayFuncOfYN(CFuncDbSqlExecute cFuncDbSqlExecute, CFuncDbSqlResolve cFuncDbSqlResolve, MongoTemplate mongoTemplate)
    {
        this.cFuncDbSqlExecute = cFuncDbSqlExecute;
        this.cFuncDbSqlResolve = cFuncDbSqlResolve;
        this.mongoTemplate = mongoTemplate;

        // 初始化规则集 added by jay-y 2024/04/20
        this.setSortRules.add(new SortRule("DateCode SET character", "CyclePeriodSort", "SetChar.DateCode", "array_dc_split_rule", "cycle_period"));
        this.setSortRules.add(new SortRule("DateCode SET QR code", "QRCyclePeriodSort", "SetQRC", "array_cycle_split_rule", "cycle_period", true));
        this.setSortRules.add(new SortRule("LotNum(CARD) SET character", "BatchNumSort", "SetChar.LotNum", "array_ln_split_rule", "laser_batch_no"));
        this.setSortRules.add(new SortRule("LotNum(CARD) SET QR code", "QRBatchNumSort", "SetQRC", "array_lot_split_rule", "laser_batch_no", true));
        this.setSortRules.add(new SortRule("PartNumber SET character", "ModelSort", "SetChar.PartNumber", null, "model_type"));
        this.setSortRules.add(new SortRule("CProductName SET character", "CustomerMNSort", "SetChar.CProductName", null, "customer_mn"));
        this.setSortRules.add(new SortRule("PlainCode SET character", "ULSort", "SetChar.PlainCode", "array_ul_split_rule", "ul_code"));

        this.pcsSortRules.add(new SortRule("DateCode PCS character", "CyclePeriodSort", "PcsChar.DateCode", "bd_dc_split_rule", "cycle_period"));
        this.pcsSortRules.add(new SortRule("DateCode PCS QR code", "QRCyclePeriodSort", "PcsQRC", "bd_cycle_split_rule", "cycle_period", true));
        this.pcsSortRules.add(new SortRule("LotNum(CARD) PCS character", "BatchNumSort", "PcsChar.LotNum", "bd_ln_split_rule", "laser_batch_no"));
        this.pcsSortRules.add(new SortRule("LotNum(CARD) PCS QR code", "QRBatchNumSort", "PcsQRC", "bd_lot_split_rule", "laser_batch_no", true));
        this.pcsSortRules.add(new SortRule("PartNumber PCS character", "ModelSort", "PcsChar.PartNumber", null, "model_type"));
        this.pcsSortRules.add(new SortRule("CProductName PCS character", "CustomerMNSort", "PcsChar.CProductName", null, "customer_mn"));
        this.pcsSortRules.add(new SortRule("PlainCode PCS character", "ULSort", "PcsChar.PlainCode", "bd_ul_split_rule", "ul_code"));

        this.pcsSortRules.add(new SortRule("Board number QR code", "SetBNSort", "PcsQRC", "bd_set_split_rule", "SetQRC", "array_set_split_rule", true));
    }

    //根据规则获取解析结果
    private JSONObject getSplitResult(String strName, String str, String ruleName, String rule)
    {
        String split_result = "";
        String split_error = "";
        JSONObject jbResult = new JSONObject();
        jbResult.put("split_result", "");
        jbResult.put("split_error", "");
        if (rule != null && (rule.startsWith("Left") || rule.startsWith("Right")))
        {
            String[] ruleParas = rule.split("\\|", -1);
            String direct = ruleParas[0];
            Integer start = Integer.parseInt(ruleParas[1]);
            Integer length = Integer.parseInt(ruleParas[2]);
            if (length <= 0)
            {
//                split_error = "{" + strName + "}根据规则名称{" + ruleName + "}规则方法{" + rule + "}截取字符失败:{截取设置长度" + length + "必须大于0}";
                // 英化
                split_error = "{" + strName + "}According to the rule name{" + ruleName + "}The rule method{" + rule + "}Failed to intercept characters:{The set length" + length + "Must be greater than 0}";
                jbResult.put("split_error", split_error);
                return jbResult;
            }
            if (direct.equals("Left"))
            {
                if (str.length() < start + length)
                {
//                    split_error = "{" + strName + "}根据规则名称{" + ruleName + "}规则方法{" + rule + "}截取字符失败:{字符截止位" + (start + length) + "超出界限}";
                    // 英化
                    split_error = "{" + strName + "}According to the rule name{" + ruleName + "}The rule method{" + rule + "}Failed to intercept characters:{Character end position" + (start + length) + "Out of bounds}";
                    jbResult.put("split_error", split_error);
                    return jbResult;
                }
                split_result = str.substring(start, start + length);
                jbResult.put("split_result", split_result);
            }
            else
            {
                Integer start2 = str.length() - start - 1;
                if (start2 < 0)
                {
//                    split_error = "{" + strName + "}根据规则名称{" + ruleName + "}规则方法{" + rule + "}截取字符失败:{起始位置" + start + "超出界限}";
                    // 英化
                    split_error = "{" + strName + "}According to the rule name{" + ruleName + "}The rule method{" + rule + "}Failed to intercept characters:{Starting position" + start + "Out of bounds}";
                    jbResult.put("split_error", split_error);
                    return jbResult;
                }
                if (str.length() < start2 + length)
                {
//                    split_error = "{" + strName + "}根据规则名称{" + ruleName + "}规则方法{" + rule + "}截取字符失败:{字符截止位" + (start2 + length) + "超出界限}";
                    // 英化
                    split_error = "{" + strName + "}According to the rule name{" + ruleName + "}The rule method{" + rule + "}Failed to intercept characters:{Character stop bit" + (start2 + length) + "Out of bounds}";
                    jbResult.put("split_error", split_error);
                    return jbResult;
                }
                split_result = str.substring(start2, start2 + length);
                jbResult.put("split_result", split_result);
            }
        }
        return jbResult;
    }

    //判断Level等级
    private Integer getLevelInt(String level) throws Exception
    {
        Integer levelInt = 0;
        if (BoardConst.VALUE_NC.equals(level))
        {
            levelInt = 0;
        }
        else if (level.equals("A"))
        {
            levelInt = 1;
        }
        else if (level.equals("B"))
        {
            levelInt = 2;
        }
        else if (level.equals("C"))
        {
            levelInt = 3;
        }
        else if (level.equals("D"))
        {
            levelInt = 4;
        }
        else if (level.equals("E"))
        {
            levelInt = 5;
        }
        else
        {
            levelInt = 6;
        }
        return levelInt;
    }

    //获取用于存储的SET条码
    //完成分选条件：正反面码比对
    private JSONObject getSaveArrayBarCode(JSONObject jbSort, String array_type, String array_barcode_front, String array_barcode_back) throws Exception
    {
        String array_barcode = "";
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        String use_front_flag = "Y";
        JSONObject jbResult = new JSONObject();
        if (array_type.equals("None"))
        {//无码
            array_barcode = "";
        }
        else if (array_type.equals("Single"))
        {//单面
            if (!array_barcode_front.equals("@NC@") && !array_barcode_front.equals("@NULL@"))
            {
                array_barcode = array_barcode_front;
            }
            else
            {
                if (!array_barcode_back.equals("@NC@") && !array_barcode_back.equals("@NULL@"))
                {
                    array_barcode = array_barcode_back;
                    use_front_flag = "N";
                }
            }
            if (array_barcode.equals("@NC@") || array_barcode.equals("@NULL@"))
            {
                array_barcode = "NoRead";
                array_status = "NG";
                array_ng_code = -1;
//                array_ng_msg = "SET条码读取失败";
                // 英化
                array_ng_msg = "SET Barcode read failed";
                use_front_flag = "Y";
            }
        }
        else
        {//双面
            if (!array_barcode_front.equals("@NC@") && !array_barcode_front.equals("@NULL@"))
            {
                array_barcode = array_barcode_front;
            }
            else
            {
                if (!array_barcode_back.equals("@NC@") && !array_barcode_back.equals("@NULL@"))
                {
                    array_barcode = array_barcode_back;
                }
            }
            if (array_barcode.equals("@NC@") || array_barcode.equals("@NULL@"))
            {
                array_barcode = "NoRead";
                array_status = "NG";
                array_ng_code = -1;
//                array_ng_msg = "SET条码读取失败";
                // 英化
                array_ng_msg = "SET Barcode read failed";
            }
            else
            {
                if (!array_barcode_front.equals(array_barcode_back))
                {
                    // 若正面和反面条码不一致,则选择正面条码作为存储
                    array_barcode = array_barcode_front;
                }
                if (jbSort.containsKey("FrontAndBackSort"))
                {
                    if (!array_barcode_front.equals(array_barcode_back))
                    {
                        array_status = "NG";
                        array_ng_code = -2;
//                        array_ng_msg = "SET正面条码{" + array_barcode_front + "}与反面条码{" + array_barcode_back + "}不一致";
                        // 英化
                        array_ng_msg = "SET Front Barcode{" + array_barcode_front + "} is different from Back Barcode{" + array_barcode_back + "}";
                    }
                }
            }
        }
        jbResult.put("array_barcode", array_barcode);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        jbResult.put("use_front_flag", use_front_flag);//是否使用正面
        return jbResult;
    }

    //获取用于存储的SET等级
    //完成分选条件：SET等级判定
    private JSONObject getSaveArrayLevel(JSONObject jbSort, String array_type, String use_front_flag, String array_level_front, String array_level_back) throws Exception
    {
        String array_level = "";
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        if (array_type.equals("None"))
        {//无码
            array_level = "";
        }
        else if (array_type.equals("Single"))
        {//单面
            if (use_front_flag.equals("Y"))
            {
                array_level = array_level_front;
            }
            else
            {
                array_level = array_level_back;
            }
            Integer array_level_int = getLevelInt(array_level);
            if (array_level_int >= 6)
            {
                array_level = "F";
            }
        }
        else
        {
            if (use_front_flag.equals("Y"))
            {
                array_level = array_level_front;
                Integer array_level_a = getLevelInt(array_level_front);
                if (array_level_a >= 6)
                {
                    array_level = "F";
                }
                Integer array_level_b = getLevelInt(array_level_back);
                if (array_level_b > array_level_a)
                {
                    array_level = array_level_back;
                    if (array_level_b >= 6)
                    {
                        array_level = "F";
                    }
                }
            }
        }
        if (!array_level.equals(""))
        {
            if (jbSort.containsKey("SetLevelSort"))
            {
                String stand_array_level = jbSort.getString("SetLevelSort");
                Integer array_level_now = getLevelInt(array_level);
                Integer array_level_stand = getLevelInt(stand_array_level);
                if (array_level_now > array_level_stand)
                {
                    array_status = "NG";
                    array_ng_code = -3;
//                    array_ng_msg = "SET二维码等级{" + array_level + "}低于设定等级{" + stand_array_level + "}";
                    // 英化
                    array_ng_msg = "SET QR code level {" + array_level + "} is lower than the set level {" + stand_array_level + "}";
                }
            }
        }
        jbResult.put("array_level", array_level);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET光学点检测结果
    private JSONObject getSaveArrayMark(String array_type, String use_front_flag, String array_mark_front, String array_mark_back) throws Exception
    {
        String array_mark = "";
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        if (array_type.equals("None"))
        {//无码
            array_mark = "";
        }
        else if (array_type.equals("Single"))
        {//单面
            if (use_front_flag.equals("Y"))
            {
                array_mark = array_mark_front;
            }
            else
            {
                array_mark = array_mark_back;
            }
            if (array_mark.equals("@NC@"))
            {
                array_mark = "";
            }
        }
        else
        {
            array_mark = "";
            if (array_mark_front.equals("NG"))
            {
                array_mark = "NG";
            }
            if (array_mark_back.equals("NG"))
            {
                array_mark = "NG";
            }
            if (array_mark_front.equals("OK") && array_mark_back.equals("OK"))
            {
                array_mark = "OK";
            }
        }
        if (array_mark.equals("NG"))
        {
            array_status = "NG";
            array_ng_code = -4;
//            array_ng_msg = "SET二维码光学点检测NG";
            // 英化
            array_ng_msg = "SET 2D code optical point detection NG";
        }
        jbResult.put("array_mark", array_mark);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET的BD数量
    private JSONObject getSaveArrayBdCount(String bd_type, Integer array_bd_count_front, Integer array_bd_count_back) throws Exception
    {
        Integer array_bd_count = 0;
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        if (array_bd_count_front >= array_bd_count_back)
        {
            array_bd_count = array_bd_count_front;
        }
        else
        {
            array_bd_count = array_bd_count_back;
        }
        if (bd_type.equals("Double"))
        {
            if (array_bd_count_front != array_bd_count_back)
            {
                array_status = "NG";
                array_ng_code = -5;
//                array_ng_msg = "正面PCS数量{" + array_bd_count_front + "}不等于反面PCS数量{" + array_bd_count_back + "}";
                // 英化
                array_ng_msg = "The number of PCS on the front side {" + array_bd_count_front + "} is not equal to the number of PCS on the back side {" + array_bd_count_back + "}";
            }
        }
        if (!bd_type.equals("None"))
        {
            if (array_bd_count <= 0)
            {
                array_status = "NG";
                array_ng_code = -6;
//                array_ng_msg = "线扫反馈SET里面的PCS数量<=0";
                // 英化
                array_ng_msg = "PCS quantity in the SET feedback is less than or equal to 0";
            }
        }
        jbResult.put("array_bd_count", array_bd_count);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET的旋转方向
    private JSONObject getSaveArrayBoardTurn(String use_front_flag, Integer board_turn_front, Integer board_turn_back) throws Exception
    {
        Integer board_turn = 2;
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        if (use_front_flag.equals("Y"))
        {
            if (board_turn_front > 0)
            {
                board_turn = 1;
            }
        }
        else
        {
            if (board_turn_back > 0)
            {
                board_turn = 1;
            }
        }
        jbResult.put("board_turn", board_turn);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET的XOUT实际数量
    private JSONObject getSaveArrayXoutCount(String bd_type, String xout_flag, Integer xout_set_num, Integer xout_act_num_front, Integer xout_act_num_back) throws Exception
    {
        Integer xout_act_num = 0;
        String array_status = "OK";
        int array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        if (xout_act_num_front >= xout_act_num_back)
        {
            xout_act_num = xout_act_num_front;
        }
        else
        {
            xout_act_num = xout_act_num_back;
        }
        if (xout_flag.equals("Y") && !xout_act_num.equals(xout_set_num))
        {
            array_status = "NG";
            array_ng_code = -7;
//            array_ng_msg = "X位实际数量{" + xout_act_num + "}不等于设定X位数量{" + xout_set_num + "}";
            // 英化
            array_ng_msg = "XOUT count{" + xout_act_num + "} is not equal to the set XOUT count{" + xout_set_num + "}";
        }
        if (bd_type.equals("Double"))
        {
            if (!xout_act_num_front.equals(xout_act_num_back) && xout_flag.equals("Y"))
            {
                array_status = "NG";
                array_ng_code = -8;
//                array_ng_msg = "正面X位数量{" + xout_act_num_front + "}不等于反面X位数量{" + xout_act_num_back + "}";
                // 英化
                array_ng_msg = "Front XOUT count{" + xout_act_num_front + "} is not equal to back XOUT count{" + xout_act_num_back + "}";
            }
        }
        jbResult.put("xout_act_num", xout_act_num);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //对Array合格性进行校验
    private JSONObject checkArrayResult(JSONObject jbRecipe, JSONObject jbSort, String array_type, Map<String, Object> mapRowArray) throws Exception
    {
        String dataType = "SET";
        Map<String, JSONObject> panelMap = new HashMap<>();
        if (mapRowArray.containsKey("array_front_info"))
        {
            JSONObject front = JSON.parseObject((String) mapRowArray.get("array_front_info"));
            panelMap.put("front", front);
        }
        if (mapRowArray.containsKey("array_back_info"))
        {
            JSONObject back = JSON.parseObject((String) mapRowArray.get("array_back_info"));
            panelMap.put("back", back);
        }
        // 比对校验SET modified by jay-y 2024/04/24
        for (String orient : panelMap.keySet())
        {
            CompareResult compareResult = this.compare(dataType, orient, panelMap.get(orient), null, jbSort, jbRecipe, array_type, mapRowArray, this.setSortRules);
            if (compareResult != null && compareResult.isNG())
            {
                return compareResult.toJSONObject();
            }
        }

        String meArrayTable = "a_pack_me_array";
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        String array_barcode = mapRowArray.get("array_barcode").toString();
        // 条码长度比对
        if (jbSort.containsKey("BarLengthSort") && !array_type.equals("None"))
        {
            Integer array_length = jbRecipe.getInteger("array_length");
            if (array_length > 0)
            {
                if (array_barcode.length() != array_length)
                {
                    jbResult.put("array_status", "NG");
                    jbResult.put("array_ng_code", -28);
//                    jbResult.put("array_ng_msg", "SET条码长度{" + array_barcode.length() + "}与配方规定长度{" + array_length + "}不等");
                    // 英化
                    jbResult.put("array_ng_msg", "SET barcode length{" + array_barcode.length() + "} is not equal to the recipe length{" + array_length + "}");
                    return jbResult;
                }
            }
        }
        // 条码大小写比对
        if (jbSort.containsKey("BarCaseSort") && !array_type.equals("None"))
        {
            String array_case = jbRecipe.getString("array_case");
            if (array_case.equals("Upper") || array_case.equals("Lower"))
            {
                if (array_case.equals("Upper"))
                {
                    boolean isAllUpper = array_barcode.matches("[A-Z\\d]+");
                    if (!isAllUpper)
                    {
                        jbResult.put("array_status", "NG");
                        jbResult.put("array_ng_code", -29);
//                        jbResult.put("array_ng_msg", "SET条码{" + array_barcode + "}与配方要求全大写不符合");
                        // 英化
                        jbResult.put("array_ng_msg", "SET barcode{" + array_barcode + "} does not meet the requirement of all uppercase letters in the recipe");
                        return jbResult;
                    }
                }
                else
                {
                    boolean isAllLower = array_barcode.matches("[a-z\\d]+");
                    if (!isAllLower)
                    {
                        jbResult.put("array_status", "NG");
                        jbResult.put("array_ng_code", -30);
//                        jbResult.put("array_ng_msg", "SET条码{" + array_barcode + "}与配方要求全小写不符合");
                        // 英化
                        jbResult.put("array_ng_msg", "SET barcode{" + array_barcode + "} does not meet the requirement of all lowercase letters in the recipe");
                        return jbResult;
                    }
                }
            }
        }
        // SET重码对比
        if (jbSort.containsKey("MultiCodeSort") && !array_type.equals("None"))
        {
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.addCriteria(Criteria.where("unbind_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
            queryBigData.addCriteria(Criteria.where("array_barcode").is(array_barcode));
            long arrayCount = mongoTemplate.getCollection(meArrayTable).countDocuments(queryBigData.getQueryObject());
            if (arrayCount > 0)
            {
                jbResult.put("array_status", "NG");
                jbResult.put("array_ng_code", -31);
//                jbResult.put("array_ng_msg", "SET条码{" + array_barcode + "}重复,请先解绑SET条码");
                // 英化
                jbResult.put("array_ng_msg", "SET barcode {" + array_barcode + "} is duplicated, please unbind the SET barcode first");
                return jbResult;
            }
        }
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //获取PCS存储信息以及PCS影响SET分选解析
    private JSONObject checkBdResult(String bd_type, JSONObject jbRecipe, JSONObject jbSort, Map<String, Object> mapRowArray, JSONArray bd_list_front, JSONArray bd_list_back) throws Exception
    {
        String setDataType = "SET";
        Map<String, JSONObject> setPanelMap = new HashMap<>();
        Object front = mapRowArray.get("array_front_info");
        Object back = mapRowArray.get("array_back_info");
        if (!ObjectUtils.isEmpty(front))
        {
            setPanelMap.put("front", JSON.parseObject((String) mapRowArray.get("array_front_info")));
        }
        if (!ObjectUtils.isEmpty(back))
        {
            setPanelMap.put("back", JSON.parseObject((String) mapRowArray.get("array_back_info")));
        }

        String meBdTable = "a_pack_me_bd";
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        Boolean use_front_flag = true;
        String array_id = mapRowArray.get("array_id").toString();
        String array_barcode = mapRowArray.get("array_barcode").toString();
        String split_lot = mapRowArray.get("split_lot").toString();
        String split_model = mapRowArray.get("split_model").toString();
        String xout_flag = mapRowArray.get("xout_flag").toString();
        Integer pcs_level_int = 0;
        String pcs_level = "";
        if (jbSort.containsKey("PcsLevelSort"))
        {
            pcs_level = jbSort.getString("PcsLevelSort");
            pcs_level_int = getLevelInt(pcs_level);
        }
        Boolean isCheckBdIndex = false;
        Integer bd_index_stand = 0;
        Integer bd_index_start = jbRecipe.getInteger("bd_index_start");
        Integer bd_index_incre = jbRecipe.getInteger("bd_index_incre");
        if (jbSort.containsKey("PcsIndexSort"))
        {
            if (bd_index_start >= 0 && bd_index_incre >= 1)
            {
                isCheckBdIndex = true;
            }
        }

        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
        JSONObject jbResult = new JSONObject();
        List<Map<String, Object>> bdRowsList = new ArrayList<>();//BD解析结果

        if (bd_list_front == null || bd_list_front.size() <= 0)
        {
            jbResult.put("array_status", "NG");
            jbResult.put("array_ng_code", -51);
//            jbResult.put("array_ng_msg", "线扫返回正面PCS集合为空");
            // 英化
            jbResult.put("array_ng_msg", "The line scan returns an empty PCS collection on the front");
            return jbResult;
        }
        if (bd_list_back == null || bd_list_back.size() <= 0)
        {
            jbResult.put("array_status", "NG");
            jbResult.put("array_ng_code", -52);
//            jbResult.put("array_ng_msg", "线扫返回反面PCS集合为空");
            // 英化
            jbResult.put("array_ng_msg", "The line scan returns an empty PCS collection on the back");
            return jbResult;
        }
        if (bd_list_front.size() != bd_list_back.size())
        {
            jbResult.put("array_status", "NG");
            jbResult.put("array_ng_code", -53);
//            jbResult.put("array_ng_msg", "线扫返回正面PCS集合数量{" + bd_list_front.size() + "}不等于反面PCS集合数量{" + bd_list_back.size() + "}");
            // 英化
            jbResult.put("array_ng_msg", "The number of positive PCS collections returned by the line scan {" + bd_list_front.size() + "} is not equal to the number of negative PCS collections {" + bd_list_back.size() + "}");
            return jbResult;
        }
        //判断使用正面还是反面作为存储与判断依据
        if (bd_type.equals("Single"))
        {//单面
            JSONObject jbPcsItemFrontFirst = bd_list_front.getJSONObject(0);
            String bd_barcode_front_first = jbPcsItemFrontFirst.getString("PcsQRC");
            if (bd_barcode_front_first.equals("@NC@"))
            {
                use_front_flag = false;
            }
        }
        JSONArray bd_list_front_last = bd_list_front;
        JSONArray bd_list_back_last = bd_list_back;
        if (!use_front_flag)
        {
            bd_list_front_last = bd_list_back;
            bd_list_back_last = bd_list_front;
        }
        String dataType = "PCS";
        Map<String, JSONObject> panelMap = new HashMap<>();
        //循环对PCS数据进行判断
        List<String> lstPcsBarCode = new ArrayList<>();
        for (int i = 0; i < bd_list_front_last.size(); i++)
        {
            JSONObject jbPcsItemFront = bd_list_front_last.getJSONObject(i);
            panelMap.put("front", jbPcsItemFront);
            JSONObject jbPcsItemBack = bd_list_back_last.getJSONObject(i);
            panelMap.put("back", jbPcsItemBack);
            String bd_id = CFuncUtilsSystem.CreateUUID(true);
            Integer bd_index = i + 1;
            String bd_barcode = "";
            String bd_level = "";
            boolean bd_level_judgment_flag = false;
            String bd_mark = "";
            String xout_flag_bd = "N";
            String bd_status = "OK";
            int bd_ng_code = 0;
            String bd_ng_msg = "";
            if (isCheckBdIndex)
            {
                if (i == 0)
                {
                    bd_index_stand = bd_index_start;
                }
                else
                {
                    bd_index_stand = bd_index_stand + bd_index_incre;
                }
            }
            //正面信息
            String bd_barcode_front = jbPcsItemFront.getString("PcsQRC");
            String bd_level_front = jbPcsItemFront.getString("PcsQRCLevel");
            String bd_mark_front = jbPcsItemFront.getString("DirMarkChkRtl");
            Boolean xout_flag_front = jbPcsItemFront.getBoolean("IsXout");
            if (bd_barcode_front.equals("@NC@") || bd_barcode_front.equals("@NULL@") || bd_barcode_front.equals(""))
            {
                bd_barcode_front = "NoRead";
            }
            if (bd_mark_front.equals("@NC@") || bd_mark_front.equals(""))
            {
                bd_mark_front = "";
            }
            Integer bd_level_front_int = getLevelInt(bd_level_front);
            if (bd_level_front_int >= 6)
            {
                bd_level_front = "F";
            }
            //反面信息
            String bd_barcode_back = jbPcsItemBack.getString("PcsQRC");
            String bd_level_back = jbPcsItemBack.getString("PcsQRCLevel");
            String bd_mark_back = jbPcsItemBack.getString("DirMarkChkRtl");
            Boolean xout_flag_back = jbPcsItemBack.getBoolean("IsXout");
            if (bd_barcode_back.equals("@NC@") || bd_barcode_back.equals("@NULL@") || bd_barcode_back.equals(""))
            {
                bd_barcode_back = "NoRead";
            }
            if (bd_mark_back.equals("@NC@") || bd_mark_back.equals(""))
            {
                bd_mark_back = "";
            }
            Integer bd_level_back_int = getLevelInt(bd_level_back);
            if (bd_level_back_int >= 6)
            {
                bd_level_back = "F";
            }
            if (bd_type.equals("Single"))
            {//单面
                bd_barcode = bd_barcode_front;
                bd_mark = bd_mark_front;
                bd_level = bd_level_front;
            }
            if (bd_type.equals("Double"))
            {//双面
                bd_barcode = "NoRead";
                if (!bd_barcode_front.equals("NoRead"))
                {
                    bd_barcode = bd_barcode_front;
                }
                else
                {
                    if (!bd_barcode_back.equals("NoRead"))
                    {
                        bd_barcode = bd_barcode_back;
                    }
                }
                if (bd_mark_front.equals("NG") || bd_mark_back.equals("NG"))
                {
                    bd_mark = "NG";
                }
                if (bd_mark_front.equals("OK") && bd_mark_back.equals("OK"))
                {
                    bd_mark = "OK";
                }
                bd_level = bd_level_front;
                if (bd_level_back_int > bd_level_front_int)
                {
                    bd_level = bd_level_back;
                }
            }
            if (xout_flag_front || xout_flag_back)
            {
                //1.正反面X位判断
                xout_flag_bd = "Y";
                if (xout_flag_front && !xout_flag_back)
                {
                    if (array_status.equals("OK"))
                    {
                        array_status = "NG";
                        array_ng_code = -54;
//                        array_ng_msg = "PCS序号{" + bd_index + "}正面画X,反面未画X";
                        // 英化
                        array_ng_msg = "PCS No{" + bd_index + "} Front X, Back No X";
                    }
                    bd_status = "NG";
                    bd_ng_code = -1;
//                    bd_ng_msg = "正面画X,反面未画X";
                    // 英化
                    bd_ng_msg = "Front X, Back No X";
                }
                else if (!xout_flag_front)
                {
                    if (array_status.equals("OK"))
                    {
                        array_status = "NG";
                        array_ng_code = -55;
//                        array_ng_msg = "PCS序号{" + bd_index + "}反面画X,正面未画X";
                        // 英化
                        array_ng_msg = "PCS No{" + bd_index + "} Back X, Front No X";
                    }
                    bd_status = "NG";
                    bd_ng_code = -2;
//                    bd_ng_msg = "反面画X,正面未画X";
                    // 英化
                    bd_ng_msg = "Back X, Front No X";
                }
            }
            else
            {
                //2.条码逻辑判断
                if (bd_type.equals("Double") && jbSort.containsKey("FrontAndBackSort"))
                {//双面
                    if (!bd_barcode_front.equals(bd_barcode_back))
                    {
                        if (array_status.equals("OK"))
                        {
                            array_status = "NG";
                            array_ng_code = -56;
//                                array_ng_msg = "PCS序号{" + bd_index + "},正面PCS条码为{" + bd_barcode_front + "},反面PCS条码为{" + bd_barcode_back + "},二者不一致";
                            // 英化
                            array_ng_msg = "PCS number {" + bd_index + "}, front PCS barcode is {" + bd_barcode_front + "}, back PCS barcode is {" + bd_barcode_back + "}, the two are inconsistent";
                        }
                        bd_status = "NG";
                        bd_ng_code = -3;
//                            bd_ng_msg = "正面PCS条码为{" + bd_barcode_front + "},反面PCS条码为{" + bd_barcode_back + "},二者不一致";
                        // 英化
                        bd_ng_msg = "Front PCS barcode is {" + bd_barcode_front + "}, back PCS barcode is {" + bd_barcode_back + "}, the two are inconsistent";
                    }
                }
                if (!bd_type.equals("None"))
                {
                    if (bd_status.equals("OK"))
                    {
                        if (bd_mark.equals("NG"))
                        {
                            if (array_status.equals("OK"))
                            {
                                array_status = "NG";
                                array_ng_code = -57;
//                                array_ng_msg = "PCS序号{" + bd_index + "},检测光学点为NG";
                                // 英化
                                array_ng_msg = "PCS number{" + bd_index + "}, detection of optical points is NG";
                            }
                            bd_status = "NG";
                            bd_ng_code = -4;
//                            bd_ng_msg = "检测光学点为NG";
                            // 英化
                            bd_ng_msg = "Detection of optical points is NG";
                        }
                        if (pcs_level_int > 0 && bd_status.equals("OK"))
                        {
                            Integer bd_level_int = getLevelInt(bd_level);
                            bd_level_judgment_flag = bd_level_int <= pcs_level_int;
                            if (bd_level_int > pcs_level_int)
                            {
                                if (array_status.equals("OK"))
                                {
                                    array_status = "NG";
                                    array_ng_code = -58;
//                                    array_ng_msg = "PCS序号{" + bd_index + "},PCS二维码等级{" + bd_level + "}大于设定等级{" + pcs_level + "}";
                                    // 英化
                                    array_ng_msg = "PCS QR code level{" + bd_level + "} is higher than the set level{" + pcs_level + "}";
                                }
                                bd_status = "NG";
                                bd_ng_code = -5;
//                                bd_ng_msg = "PCS二维码等级{" + bd_level + "}大于设定等级{" + pcs_level + "}";
                                // 英化
                                bd_ng_msg = "PCS QR code level{" + bd_level + "} is higher than the set level{" + pcs_level + "}";
                            }
                        }
                        if (bd_barcode.equals("NoRead"))
                        {
                            if (array_status.equals("OK"))
                            {
                                array_status = "NG";
                                array_ng_code = -59;
//                                array_ng_msg = "PCS序号{" + bd_index + "},PCS条码读取失败";
                                // 英化
                                array_ng_msg = "PCS barcode reading failed";
                            }
                            if (bd_status.equals("OK"))
                            {
                                bd_status = "NG";
                                bd_ng_code = -6;
//                                bd_ng_msg = "条码读取失败";
                                // 英化
                                bd_ng_msg = "Barcode reading failed";
                            }
                        }
                        else
                        {
                            //验证重码
                            if (jbSort.containsKey("MultiCodeSort"))
                            {
                                if (lstPcsBarCode.contains(bd_barcode))
                                {
                                    if (array_status.equals("OK"))
                                    {
                                        array_status = "NG";
                                        array_ng_code = -60;
//                                        array_ng_msg = "PCS序号{" + bd_index + "},当前SET中存在相同的PCS条码{" + bd_barcode + "}";
                                        // 英化
                                        array_ng_msg = "The same PCS barcode{" + bd_barcode + "} exists in the current SET";
                                    }
                                    if (bd_status.equals("OK"))
                                    {
                                        bd_status = "NG";
                                        bd_ng_code = -7;
//                                        bd_ng_msg = "当前SET中存在相同的PCS条码{" + bd_barcode + "}";
                                        // 英化
                                        bd_ng_msg = "The same PCS barcode{" + bd_barcode + "} exists in the current SET";
                                    }
                                }
                                else
                                {
                                    lstPcsBarCode.add(bd_barcode);
                                }
                                if (bd_status.equals("OK"))
                                {
                                    Query queryBigData = new Query();
                                    queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                                    queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
                                    queryBigData.addCriteria(Criteria.where("bd_barcode").is(bd_barcode));
                                    long arrayCount = mongoTemplate.getCollection(meBdTable).countDocuments(queryBigData.getQueryObject());
                                    if (arrayCount > 0)
                                    {
                                        if (array_status.equals("OK"))
                                        {
                                            array_status = "NG";
                                            array_ng_code = -61;
//                                            array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}重码,请先解绑";
                                            // 英化
                                            array_ng_msg = "PCS Barcode{" + bd_barcode + "} is repeated, please unbind first";
                                        }
                                        bd_status = "NG";
                                        bd_ng_code = -8;
//                                        bd_ng_msg = "PCS条码{" + bd_barcode + "}重码,请先解绑";
                                        // 英化
                                        bd_ng_msg = "PCS Barcode{" + bd_barcode + "} is repeated, please unbind first";
                                    }
                                }
                            }
                            //条码长度比对
                            if (jbSort.containsKey("BarLengthSort") && bd_status.equals("OK"))
                            {
                                Integer bd_length = jbRecipe.getInteger("bd_length");
                                if (bd_length > 0)
                                {
                                    if (bd_barcode.length() != bd_length)
                                    {
                                        if (array_status.equals("OK"))
                                        {
                                            array_status = "NG";
                                            array_ng_code = -66;
//                                            array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}条码长度{" + bd_barcode.length() + "}不等于设定长度{" + bd_length + "}";
                                            // 英化
                                            array_ng_msg = "PCS Barcode{" + bd_barcode + "} length{" + bd_barcode.length() + "} is not equal to the set length{" + bd_length + "}";
                                        }
                                        bd_status = "NG";
                                        bd_ng_code = -13;
//                                        bd_ng_msg = "PCS条码{" + bd_barcode + "}条码长度{" + bd_barcode.length() + "}不等于设定长度{" + bd_length + "}";
                                        // 英化
                                        bd_ng_msg = "PCS Barcode{" + bd_barcode + "} length{" + bd_barcode.length() + "} is not equal to the set length{" + bd_length + "}";
                                    }
                                }
                            }
                            //条码大小比对
                            if (jbSort.containsKey("BarCaseSort") && bd_status.equals("OK"))
                            {
                                String bd_case = jbRecipe.getString("bd_case");
                                if (bd_case.equals("Upper") || bd_case.equals("Lower"))
                                {
                                    if (bd_case.equals("Upper"))
                                    {
                                        boolean isAllUpper = bd_barcode.matches("[A-Z\\d]+");
                                        if (!isAllUpper)
                                        {
                                            if (array_status.equals("OK"))
                                            {
                                                array_status = "NG";
                                                array_ng_code = -67;
//                                                array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}不全为大写";
                                                // 英化
                                                array_ng_msg = "PCS Barcode{" + bd_barcode + "} is not all uppercase";
                                            }
                                            bd_status = "NG";
                                            bd_ng_code = -14;
//                                            bd_ng_msg = "PCS条码{" + bd_barcode + "}不全为大写";
                                            // 英化
                                            bd_ng_msg = "PCS Barcode{" + bd_barcode + "} is not all uppercase";
                                        }
                                    }
                                    else
                                    {
                                        boolean isAllLower = bd_barcode.matches("[a-z\\d]+");
                                        if (!isAllLower)
                                        {
                                            if (array_status.equals("OK"))
                                            {
                                                array_status = "NG";
                                                array_ng_code = -68;
//                                                array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}不全为小写";
                                                // 英化
                                                array_ng_msg = "PCS Barcode{" + bd_barcode + "} is not all lowercase";
                                            }
                                            bd_status = "NG";
                                            bd_ng_code = -15;
//                                            bd_ng_msg = "PCS条码{" + bd_barcode + "}不全为小写";
                                            // 英化
                                            bd_ng_msg = "PCS Barcode{" + bd_barcode + "} is not all lowercase";
                                        }
                                    }
                                }
                            }
                            //PCS序号比对
                            if (isCheckBdIndex)
                            {
                                String bd_index_split_rule = jbRecipe.getString("bd_index_split_rule");
                                JSONObject jbSplitResult = getSplitResult("PCS条码", bd_barcode, "PCS截取序号", bd_index_split_rule);
                                String split_result = jbSplitResult.getString("split_result");
                                String split_error = jbSplitResult.getString("split_error");
                                if (!split_error.equals(""))
                                {
                                    if (array_status.equals("OK"))
                                    {
                                        array_status = "NG";
                                        array_ng_code = -69;
//                                        array_ng_msg = "PCS序号{" + bd_index + "}," + split_error;
                                        // 英化
                                        array_ng_msg = "PCS Index{" + bd_index + "}," + split_error;
                                    }
                                    if (bd_status.equals("OK"))
                                    {
                                        bd_status = "NG";
                                        bd_ng_code = -16;
                                        bd_ng_msg = split_error;
                                    }
                                }
                                else
                                {
                                    if (!split_result.equals(""))
                                    {
                                        try
                                        {
                                            int bd_index_now = Integer.parseInt(split_result);
                                            if (bd_index_now != bd_index_stand)
                                            {
                                                if (array_status.equals("OK"))
                                                {
                                                    array_status = "NG";
                                                    array_ng_code = -70;
                                                    array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}截取序号{" + bd_index_now + "}不等于要求序号{" + bd_index_stand + "}";
                                                }
                                                if (bd_status.equals("OK"))
                                                {
                                                    bd_status = "NG";
                                                    bd_ng_code = -17;
                                                    bd_ng_msg = "PCS条码{" + bd_barcode + "}截取序号{" + bd_index_now + "}不等于要求序号{" + bd_index_stand + "}";
                                                }
                                            }
                                        }
                                        catch (Exception convError)
                                        {
                                            if (array_status.equals("OK"))
                                            {
                                                array_status = "NG";
                                                array_ng_code = -71;
//                                                array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}截取序号{" + split_result + "}转换Int类型失败";
                                                // 英化
                                                array_ng_msg = "PCS index{" + bd_index + "},PCS barcode{" + bd_barcode + "} split index{" + split_result + "} convert to Int failed";
                                            }
                                            if (bd_status.equals("OK"))
                                            {
                                                bd_status = "NG";
                                                bd_ng_code = -18;
//                                                bd_ng_msg = "PCS条码{" + bd_barcode + "}截取序号{" + split_result + "}转换Int类型失败";
                                                // 英化
                                                bd_ng_msg = "PCS barcode{" + bd_barcode + "} split index{" + split_result + "} convert to Int failed";
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 比对验证PCS modified by jay-y 2024/04/24
                for (String orient : panelMap.keySet())
                {
                    JSONObject panel = panelMap.get(orient);
                    JSONObject setPanel = setPanelMap.get(orient);
                    Integer pcsNum = Integer.parseInt(panel.getString("PcsNum"));
                    CompareResult compareResult = this.compare(dataType, orient, panel, pcsNum, setDataType, orient, setPanel, null, jbSort, jbRecipe, bd_type, mapRowArray, pcsSortRules);
                    if (compareResult != null && compareResult.isNG())
                    {
                        array_status = "NG";
                        array_ng_code = -1;
                        array_ng_msg = compareResult.getNgMsg();
                        if (bd_status.equals("OK"))
                        {
                            bd_status = "NG";
                            bd_ng_code = -9;
                            bd_ng_msg = compareResult.getNgMsg();
                        }
                    }
                }
            }
            //创建保存集合
            Map<String, Object> mapBdItem = new HashMap<>();
            mapBdItem.put("item_date", item_date);
            mapBdItem.put("item_date_val", item_date_val);
            mapBdItem.put("bd_id", bd_id);
            mapBdItem.put("array_id", array_id);
            mapBdItem.put("array_barcode", array_barcode);
            mapBdItem.put("array_status", "");
            mapBdItem.put("bd_barcode", bd_barcode);
            mapBdItem.put("bd_index", bd_index);
            mapBdItem.put("bd_level", bd_level);
            mapBdItem.put("bd_level_judgment", bd_level_judgment_flag ? BoardConst.FLAG_Y : BoardConst.FLAG_N);
            mapBdItem.put("bd_mark", bd_mark);
            mapBdItem.put("xout_flag", xout_flag_bd);
            mapBdItem.put("bd_status", bd_status);
            mapBdItem.put("bd_ng_code", bd_ng_code);
            mapBdItem.put("bd_ng_msg", bd_ng_msg);
            // PCS状态为OK时的PCS记录置为有效
            mapBdItem.put("enable_flag", "OK".equals(bd_status) ? "Y" : "N");
            bdRowsList.add(mapBdItem);
        }
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        jbResult.put("bd", bdRowsList);
        return jbResult;
    }

    //3.获取Array与BD解析数据
    public JSONObject ResolveCcdResult(String user_name, JSONObject jbPlan, JSONObject jbRecipe, JSONObject jbSort, JSONObject ccdData) throws Exception
    {
        JSONObject jbCcdResolveResult = new JSONObject();
        Map<String, Object> mapRowArray = new HashMap<>();//Array解析结果
        List<Map<String, Object>> bdRowsList = new ArrayList<>();//BD解析结果

        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
        String array_id = CFuncUtilsSystem.CreateUUID(true);
        String lot_num = jbPlan.getString("lot_num");
        String plant_order_num = jbPlan.getString("plant_order_num");
        String model_type = jbPlan.getString("model_type");
        String array_type = jbPlan.getString("array_type");
        String bd_type = jbPlan.getString("bd_type");
        int board_result = 0;
        int deposit_position = 2;
        boolean isMultiCode = false; // 代表是否重码
        String xout_flag = "N";
        Integer xout_set_num = null;
        String array_barcode = "";
        String array_level = "";
        String array_mark = "";
        Integer array_bd_count = 0;
        Integer board_turn = 2;
        Integer xout_act_num = 0;
        String split_lot = "";
        String split_model = "";
        String array_status = "OK";

        // 获取CCD数据
        CCDScanMessage ccdMessage = new CCDScanMessage(user_name, array_id, jbPlan, jbRecipe, jbSort, ccdData);
        try
        {
            String cyclePeriod = String.valueOf(jbPlan.get("cycle_period"));
            String modelVersion = String.valueOf(jbPlan.get("model_version"));
            mapRowArray.put("item_date", item_date);
            mapRowArray.put("item_date_val", item_date_val);
            mapRowArray.put("array_id", array_id);
            mapRowArray.put("pile_barcode", "");
            mapRowArray.put("array_barcode", "");
            mapRowArray.put("lot_num", lot_num);
            mapRowArray.put("plant_order_num", plant_order_num);
            mapRowArray.put("array_index", jbPlan.getInteger("finish_ok_count") + 1);
            mapRowArray.put("board_sn", "");
            mapRowArray.put("array_level", "");
            mapRowArray.put("array_mark", "");
            mapRowArray.put("array_bd_count", 0);
            mapRowArray.put("board_result", 0);
            mapRowArray.put("board_turn", 2);
            mapRowArray.put("deposit_position", deposit_position);
            mapRowArray.put("xout_flag", "N");
            mapRowArray.put("xout_set_num", 0);
            mapRowArray.put("xout_act_num", 0);
            mapRowArray.put("array_status", array_status);
            mapRowArray.put("array_ng_code", 0);
            mapRowArray.put("array_ng_msg", "");
            mapRowArray.put("array_front_info", "");
            mapRowArray.put("array_back_info", "");
            mapRowArray.put("user_name", user_name);
            mapRowArray.put("task_type", jbPlan.getString("task_type"));
            mapRowArray.put("model_type", model_type);
            mapRowArray.put("model_version", modelVersion);
            mapRowArray.put("array_type", array_type);
            mapRowArray.put("bd_type", bd_type);
            mapRowArray.put("m_length", jbPlan.getDouble("m_length"));
            mapRowArray.put("m_width", jbPlan.getDouble("m_width"));
            mapRowArray.put("m_tickness", jbPlan.getDouble("m_tickness"));
            mapRowArray.put("m_weight", jbPlan.getDouble("m_weight"));
            mapRowArray.put("cycle_period", cyclePeriod);
            mapRowArray.put("split_lot", "");
            mapRowArray.put("split_model", "");
            mapRowArray.put("up_flag", "N");
            mapRowArray.put("up_ng_code", 0);
            mapRowArray.put("up_ng_msg", "");
            mapRowArray.put("pile_use_flag", "N");
            mapRowArray.put("enable_flag", "Y");
            mapRowArray.put("unbind_flag", "N");
            mapRowArray.put("unbind_user", "");
            mapRowArray.put("unbind_time", "");
            mapRowArray.put("unbind_way", "");

            // 新增比对数据 added by jay-y 2024/04/20
            mapRowArray.put("batch_no", jbPlan.get("batch_no")); // 批号
            mapRowArray.put("laser_batch_no", jbPlan.get("laser_batch_no")); // 镭射批号
            mapRowArray.put("typesetting_no", jbPlan.get("typesetting_no")); // 排版数
            mapRowArray.put("customer_mn", jbPlan.get("customer_mn")); // 客户料号
            mapRowArray.put("ul_code", jbPlan.get("ul_code")); // UL号

            // 解析CCD数据，目前架构为一次一片，正反面都存在数据
            JSONArray jaCcdData = ccdData.getJSONArray("Content");
            JSONObject jbFirst = jaCcdData.getJSONObject(0);
            JSONObject SetFrontContent = jbFirst.getJSONObject("SetFrontContent");//正面
            JSONObject SetBackContent = jbFirst.getJSONObject("SetBackContent");//反面

            //Array基本存储
            mapRowArray.put("board_sn", SortConst.convertValue(ccdMessage.getOrigin().getBoardSn()));
            mapRowArray.put("array_front_info", ccdMessage.getOrigin().getMultiAspectFront());
            mapRowArray.put("array_back_info", ccdMessage.getOrigin().getMultiAspectBack());
            mapRowArray.put("array_barcode", SortConst.convertValue(ccdMessage.getOrigin().getBoardBarcode()));
            mapRowArray.put("array_level", SortConst.convertValue(ccdMessage.getOrigin().getBoardLevel()));
            mapRowArray.put("array_mark", SortConst.convertValue(ccdMessage.getOrigin().getBoardMark()));
            mapRowArray.put("array_bd_count", ccdMessage.getOrigin().getBoardLayoutNumber());
            mapRowArray.put("board_turn", ccdMessage.getOrigin().getBoardTurn());
            mapRowArray.put("xout_act_num", ccdMessage.getOrigin().getXoutActualNumber());

            // 比对板件方向
            SortRule boardDirectionSort = new SortRule("Board Direction", "BoardDirectionSort", "boardDirection", false);
            this.compare(ccdMessage, this.directionComparator, boardDirectionSort);
            String boardDirectionStr = SortConst.convertValue(ccdMessage.getOrigin().getBoardDirection());
            boardDirectionStr = Const.BLANK.equals(boardDirectionStr) ? "0" : boardDirectionStr;
            int boardDirection = Integer.parseInt(boardDirectionStr);
            if (boardDirection > 0)
            {
                ccdMessage.getOrigin().setBoardTurn(BoardConst.TURN_1);
                mapRowArray.put("board_turn", BoardConst.TURN_1);
            }

            // 获取任务截取批次与料号信息
            String order_lot_split_rule = jbRecipe.getString("order_lot_split_rule");
            String custom_model_split_rule = jbRecipe.getString("custom_model_split_rule");
            // 订单号截取批次规则
            JSONObject jbSplitResult = getSplitResult("订单号", lot_num, "订单号截取批次", order_lot_split_rule);
            String split_result = jbSplitResult.getString("split_result");
            String split_error = jbSplitResult.getString("split_error");
            if (!split_error.equals(""))
            {
                array_status = "NG";
                mapRowArray.put("array_status", array_status);
                mapRowArray.put("array_ng_code", -101);
                mapRowArray.put("array_ng_msg", split_error);
            }
            else
            {
                if (!split_result.equals(""))
                {
                    split_lot = split_result;
                }
            }
            //客户料号截取料号规则
            jbSplitResult = getSplitResult("料号", model_type, "料号截取批次", custom_model_split_rule);
            split_result = jbSplitResult.getString("split_result");
            split_error = jbSplitResult.getString("split_error");
            if (!split_error.equals(""))
            {
                if (array_status.equals("OK"))
                {
                    array_status = "NG";
                    mapRowArray.put("array_status", array_status);
                    mapRowArray.put("array_ng_code", -102);
                    mapRowArray.put("array_ng_msg", split_error);
                }
            }
            else
            {
                if (!split_result.equals(""))
                {
                    split_model = split_result;
                }
            }
            mapRowArray.put("split_lot", split_lot);
            mapRowArray.put("split_model", split_model);

            //2.判断当前设置为几X
            if (jbSort.containsKey("XoutSort"))
            {
                xout_set_num = Integer.parseInt(jbSort.getString("XoutSort").replaceAll("X", ""));
                xout_flag = "Y";
                if (xout_set_num < 0)
                {
                    xout_set_num = 0;
                }
            }
            mapRowArray.put("xout_flag", xout_flag);
            mapRowArray.put("xout_set_num", xout_set_num != null ? xout_set_num : 0);

            //正面数据
            String array_barcode_front = SetFrontContent.getString("SetQRC");
            String array_level_front = SetFrontContent.getString("SetQRCLevel");
            String array_mark_front = SetFrontContent.getString("DirMarkChkRtl");
            Integer array_bd_count_front = SetFrontContent.getInteger("LayoutQty");
            Integer board_turn_front = SetFrontContent.getInteger("Direction");
            Integer xout_act_num_front = SetFrontContent.getInteger("XoutQty");
            JSONArray bd_list_front = SetFrontContent.getJSONArray("PcsMsgList");
            //反面数据
            String array_barcode_back = SetBackContent.getString("SetQRC");
            String array_level_back = SetBackContent.getString("SetQRCLevel");
            String array_mark_back = SetBackContent.getString("DirMarkChkRtl");
            Integer array_bd_count_back = SetBackContent.getInteger("LayoutQty");
            Integer board_turn_back = SetBackContent.getInteger("Direction");
            Integer xout_act_num_back = SetBackContent.getInteger("XoutQty");
            JSONArray bd_list_back = SetBackContent.getJSONArray("PcsMsgList");

            //4.先获取基本的存储数据,需要进行双面比对然后选择合适的数据存储
            //4.1 获取用于存储的SET条码
            JSONObject jbResolveResult = getSaveArrayBarCode(jbSort, array_type, array_barcode_front, array_barcode_back);
            String use_front_flag = jbResolveResult.getString("use_front_flag");//使用正面Y,使用反面N
            array_barcode = jbResolveResult.getString("array_barcode");
            String array_status_result = jbResolveResult.getString("array_status");
            Integer array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            String array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            //4.2 获取用于存储的SET等级
            jbResolveResult = getSaveArrayLevel(jbSort, array_type, use_front_flag, array_level_front, array_level_back);
            array_level = jbResolveResult.getString("array_level");
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            //4.3 获取用于存储的SET光学点检测结果
            jbResolveResult = getSaveArrayMark(array_type, use_front_flag, array_mark_front, array_mark_back);
            array_mark = jbResolveResult.getString("array_mark");
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            //4.4 获取用于存储的SET的BD数量
            jbResolveResult = getSaveArrayBdCount(bd_type, array_bd_count_front, array_bd_count_back);
            array_bd_count = jbResolveResult.getInteger("array_bd_count");
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            //4.5 获取用于存储的SET的旋转方向
            jbResolveResult = getSaveArrayBoardTurn(use_front_flag, board_turn_front, board_turn_back);
            board_turn = jbResolveResult.getInteger("board_turn");
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            //4.6 获取用于存储的SET的画X数量
            jbResolveResult = getSaveArrayXoutCount(bd_type, xout_flag, xout_set_num, xout_act_num_front, xout_act_num_back);
            xout_act_num = jbResolveResult.getInteger("xout_act_num");
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            //Array基本存储
            mapRowArray.put("array_barcode", array_barcode);
            mapRowArray.put("array_level", array_level);
            mapRowArray.put("array_mark", array_mark);
            mapRowArray.put("array_bd_count", array_bd_count);
            mapRowArray.put("board_turn", board_turn);
            mapRowArray.put("xout_act_num", xout_act_num);

            //5.针对SET条码进行分选条件解析
            if (array_status.equals("OK"))
            {
                jbResolveResult = checkArrayResult(jbRecipe, jbSort, array_type, mapRowArray);
                array_status_result = jbResolveResult.getString("array_status");
                array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
                array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
                if (array_status_result.equals("NG"))
                {
                    array_status = array_status_result;
                    mapRowArray.put("array_status", array_status_result);
                    mapRowArray.put("array_ng_code", array_ng_code_result);
                    mapRowArray.put("array_ng_msg", array_ng_msg_result);
                    if (array_ng_code_result == -31)
                    {
                        isMultiCode = true;
                    }
                }
                if (jbResolveResult.containsKey("split_lot"))
                {
                    split_lot = jbResolveResult.getString("split_lot");
                    mapRowArray.put("split_lot", split_lot);
                }
                if (jbResolveResult.containsKey("split_model"))
                {
                    split_model = jbResolveResult.getString("split_model");
                    mapRowArray.put("split_model", split_model);
                }
            }

            //6.获取PCS存储信息以及PCS影响SET分选解析
            jbResolveResult = checkBdResult(bd_type, jbRecipe, jbSort, mapRowArray, bd_list_front, bd_list_back);
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
                if (array_ng_code_result == -60 || array_ng_code_result == -61)
                {
                    isMultiCode = true;
                }
            }
            if (jbResolveResult.containsKey("bd"))
            {
                bdRowsList = (List<Map<String, Object>>) jbResolveResult.get("bd");
                for (Map<String, Object> bdMapItem : bdRowsList)
                {
                    bdMapItem.put("array_status", array_status);
                    // SET和PCS状态同时为OK时的PCS记录才置为有效
                    if ("NG".equals(array_status))
                    {
                        bdMapItem.put("enable_flag", "N");
                    }
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            if (e instanceof CompareException)
            {
                CompareException compareException = (CompareException) e;
                array_status = compareException.getStatus();
                mapRowArray.put("array_status", compareException.getStatus());
                mapRowArray.put("array_ng_code", compareException.getCode());
                mapRowArray.put("array_ng_msg", compareException.getMessage());
            }
        }
        finally
        {
            //7.综合判断board_result和deposit_position
            board_result = 3;
            if (isMultiCode)
            {
                board_result = 4;
            }
            else
            {
                if (array_status.equals("OK"))
                {
                    board_result = 1;
                    // 实际数量为0时输出判断结果为OK，实际数量大于0且小于等于设定数量时为XOUT，不等于设定数量时NG modified by jay-y 2024-07-30
                    if (xout_flag.equals("Y") && xout_act_num > 0 && xout_act_num <= xout_set_num)
                    {
                        board_result = 2;
                    }
                }
            }
            deposit_position = 2;
            if (array_status.equals("OK"))
            {
                deposit_position = 1;
            }
            else
            {
                mapRowArray.put("enable_flag", "N");
                ccdMessage.getOrigin().setEnableFlag(BoardConst.FLAG_N);
            }
            mapRowArray.put("board_result", board_result);
            mapRowArray.put("deposit_position", deposit_position);
            ccdMessage.getOrigin().setBoardResult(board_result);
            ccdMessage.getOrigin().setDepositPosition(deposit_position);
            //返回数据
            jbCcdResolveResult.put("array", mapRowArray);
            jbCcdResolveResult.put("bd", bdRowsList);
//            jbCcdResolveResult.put("array", ccdMessage.getOrigin());
//            jbCcdResolveResult.put("bd", ccdMessage.getOrigin().getChildrenBoards());
        }
        return jbCcdResolveResult;
    }

    private CompareResult compare(String srcDataType, String srcOrient, JSONObject srcPanel, Integer srcIndex, String dstDataType, String dstOrient, JSONObject dstPanel, Integer dstIndex, JSONObject jbSort, JSONObject jbRecipe, String rowType, Map<String, Object> mapRow, List<SortRule> sortRules)
    {
        Comparator comparator = new DefaultComparator();
        for (SortRule sortRule : sortRules)
        {
            // 是否进行比对
            if (jbSort.containsKey(sortRule.getCode()))
            {
                CompareResult compareResult;
                String sortVal = jbSort.getString(sortRule.getCode());
                boolean checkDataType = false;
                if (sortVal != null)
                {
                    String[] dateTypes = sortVal.split(",");
                    checkDataType = !ObjectUtils.isEmpty(sortVal) && !"ALL".equals(sortVal) && dateTypes.length > 0 && !Arrays.asList(dateTypes).contains(srcDataType); // 如果规则要求是有码板件，然后指定数据板件类型不为空且不为ALL且与规则要求板件类型不匹配，则跳过
                }
                boolean hasQR = sortRule.isRequiredQR() && "None".equals(rowType); // 如果规则要求是有码板件，然后是无码板件，则跳过
                if (hasQR || checkDataType)
                {
                    continue;
                }
                if (!ObjectUtils.isEmpty(sortRule.getDstCompareDataValueKey()) && dstPanel != null)
                {
                    compareResult = this.compare(srcDataType, srcOrient, srcPanel, srcIndex, dstDataType, dstOrient, dstPanel, dstIndex, jbRecipe, mapRow, sortRule, comparator);
                }
                else
                {
                    compareResult = this.compare(srcDataType, srcOrient, srcPanel, srcIndex, jbRecipe, mapRow, sortRule, comparator);
                }
                if (compareResult != null && compareResult.isNG())
                {
                    return compareResult;
                }
            }
        }
        return null;
    }

    private CompareResult compare(String dataType, String orient, JSONObject panel, Integer index, JSONObject jbSort, JSONObject jbRecipe, String rowType, Map<String, Object> mapRow, List<SortRule> sortRules)
    {
        return this.compare(dataType, orient, panel, index, null, null, null, null, jbSort, jbRecipe, rowType, mapRow, sortRules);
    }

    private CompareResult compare(String dataType, String orient, JSONObject panel, Integer index, JSONObject jbRecipe, Map<String, Object> mapRow, SortRule sortRule, Comparator comparator)
    {
        String name = sortRule.getName();
        CompareData compareData = index != null ? new CompareData(dataType, orient, panel, sortRule.getCompareDataValueKey(), index) : new CompareData(dataType, orient, panel, sortRule.getCompareDataValueKey());
//        SplitRule splitRule = new SplitRule(name + "截取规则", jbRecipe.getString(sortRule.getSplitRuleValueKey()));
        // 英化
        SplitRule splitRule = new SplitRule(name + " Split Rule", jbRecipe.getString(sortRule.getSplitRuleValueKey()));
        CompareRule compareRule = new CompareRule(name, String.valueOf(mapRow.get(sortRule.getCompareRuleTargetKey())));
        return comparator.compare(compareData, splitRule, compareRule);
    }

    private CompareResult compare(String dataType, String orient, JSONObject srcPanel, Integer srcIndex, String dstDataType, String dstOrient, JSONObject dstPanel, Integer dstIndex, JSONObject jbRecipe, Map<String, Object> mapRow, SortRule sortRule, Comparator comparator)
    {
        String name = sortRule.getName();
        CompareData srcCompareData = srcIndex != null ? new CompareData(dataType, orient, srcPanel, sortRule.getCompareDataValueKey(), srcIndex) : new CompareData(dataType, orient, srcPanel, sortRule.getCompareDataValueKey());
//        SplitRule srcSplitRule = new SplitRule(name + "截取规则", jbRecipe.getString(sortRule.getSplitRuleValueKey()));
        // 英化
        SplitRule srcSplitRule = new SplitRule(name + " Split Rule", jbRecipe.getString(sortRule.getSplitRuleValueKey()));
        CompareData dstCompareData = dstIndex != null ? new CompareData(dstDataType, dstOrient, dstPanel, sortRule.getDstCompareDataValueKey(), dstIndex) : new CompareData(dstDataType, dstOrient, dstPanel, sortRule.getDstCompareDataValueKey());
//        SplitRule dstSplitRule = new SplitRule(name + "截取规则", jbRecipe.getString(sortRule.getDstSplitRuleValueKey()));
        // 英化
        SplitRule dstSplitRule = new SplitRule(name + " Split Rule", jbRecipe.getString(sortRule.getDstSplitRuleValueKey()));
        CompareRule compareRule = new CompareRule(name, String.valueOf(mapRow.get(sortRule.getCompareRuleTargetKey())));
        return comparator.compare(srcCompareData, srcSplitRule, dstCompareData, dstSplitRule, compareRule);
    }

    private void compare(CCDScanMessage msg, Comparator comparator, SortRule sortRule)
    {
        if (!msg.isReady())
        {
//           throw new CompareException("CCD数据不完整，无法进行比对");
            // 英化
            throw new CompareException("CCD Data is incomplete and cannot be compared");
        }
        List<CompareData> data = new LinkedList<>();
        String boardType = msg.getOrigin().getBoardType();
        SETBoard front = msg.getOrigin().getMultiAspect().get(BoardConst.ORIENT_FRONT);
        SETBoard back = msg.getOrigin().getMultiAspect().get(BoardConst.ORIENT_BACK);
        boolean useDouble = BoardConst.DOUBLE.equals(boardType) && front.isValid() && back.isValid();
        boolean useFront = BoardConst.SINGLE.equals(boardType) && front.isValid();
        boolean useBack = BoardConst.SINGLE.equals(boardType) && back.isValid();
        SplitRule splitRule = sortRule.getSplitRule(msg.getRecipe());
        if (useFront)
        {
            data.add(new CompareData(front, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
        }
        else if (useBack)
        {
            data.add(new CompareData(back, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
        }
        else if (useDouble)
        {
            data.add(new CompareData(front, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
            data.add(new CompareData(back, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
        }
        else if (!sortRule.isRequiredQR())
        {
            data.add(new CompareData(front, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
            data.add(new CompareData(back, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
        }
        for (CompareData d : data)
        {
            comparator.compare(d, splitRule, sortRule);
        }
    }

    @Data
    @AllArgsConstructor
    public static class SortRule
    {
        private String name;
        private String code;
        private String compareDataValueKey;
        private String splitRuleValueKey;
        private String compareRuleTargetKey;
        private String dstCompareDataValueKey;
        private String dstSplitRuleValueKey;
        private boolean requiredQR;

        private SplitRule splitRule;
        private SplitRule dstSplitRule;

        public SortRule(String name, String code, String compareDataValueKey, String splitRuleValueKey, boolean requiredQR)
        {
            this.name = name;
            this.code = code;
            this.compareDataValueKey = compareDataValueKey;
            this.splitRuleValueKey = splitRuleValueKey;
            this.requiredQR = requiredQR;
        }

        public SortRule(String name, String code, String compareDataValueKey, boolean requiredQR)
        {
            this(name, code, compareDataValueKey, null, requiredQR);
        }

        public SortRule(String name, String code, boolean requiredQR)
        {
            this(name, code, null, null, requiredQR);
        }

        public SortRule(String name, String code)
        {
            this(name, code, false);
        }

        public SortRule(String name, String code, String compareDataValueKey, String splitRuleValueKey, String compareRuleTargetKey)
        {
            this.name = name;
            this.code = code;
            this.compareDataValueKey = compareDataValueKey;
            this.splitRuleValueKey = splitRuleValueKey;
            this.compareRuleTargetKey = compareRuleTargetKey;
            this.requiredQR = false;
        }

        public SortRule(String name, String code, String compareDataValueKey, String splitRuleValueKey, String compareRuleTargetKey, boolean requiredQR)
        {
            this.name = name;
            this.code = code;
            this.compareDataValueKey = compareDataValueKey;
            this.splitRuleValueKey = splitRuleValueKey;
            this.compareRuleTargetKey = compareRuleTargetKey;
            this.requiredQR = requiredQR;
        }

        public SortRule(String name, String code, String srcCompareDataValueKey, String srcSplitRuleValueKey, String dstCompareDataValueKey, String dstSplitRuleValueKey, boolean requiredQR)
        {
            this.name = name;
            this.code = code;
            this.compareDataValueKey = srcCompareDataValueKey;
            this.splitRuleValueKey = srcSplitRuleValueKey;
            this.dstCompareDataValueKey = dstCompareDataValueKey;
            this.dstSplitRuleValueKey = dstSplitRuleValueKey;
            this.requiredQR = requiredQR;
        }

        public SplitRule getSplitRule(JSONObject recipe)
        {
            if (this.splitRuleValueKey != null)
            {
                String sortCode = recipe.getString(this.splitRuleValueKey);
                if (sortCode != null)
                {
                    this.splitRule = new SplitRule(this.name + " Split Rule", sortCode);
                }
            }
            return this.splitRule;
        }

        public SplitRule getDstSplitRule(JSONObject recipe)
        {
            if (this.dstSplitRuleValueKey != null)
            {
                String sortCode = recipe.getString(this.dstSplitRuleValueKey);
                if (sortCode != null)
                {
                    this.dstSplitRule = new SplitRule(this.name + " Split Rule", sortCode);
                }
            }
            return this.dstSplitRule;
        }
    }

    @Data
    @AllArgsConstructor
    public static class SplitRule
    {
        private String name;
        private String value;
        private int start;
        private int length;
        private Boolean reverse;

        public SplitRule(String name, int start, int length, Boolean reverse)
        {
            this.name = name;
            this.start = start;
            this.length = length;
            this.reverse = reverse;
        }

        public SplitRule(String name, String value)
        {
            this.name = name;
            if (!ObjectUtils.isEmpty(value))
            {
                this.value = value;
                String[] arr = value.split("\\|", -1);
                if (arr.length == 3)
                {
                    if (arr[0].equals("Left"))
                    {
                        this.reverse = false;
                    }
                    else if (arr[0].equals("Right"))
                    {
                        this.reverse = true;
                    }
                    this.start = Integer.parseInt(arr[1]);
                    this.length = Integer.parseInt(arr[2]);
                }
            }
        }

        public Result process(String dataName, String dataValue)
        {
            if (this.reverse == null)
            {
                return new Result(null, dataValue);
            }
            if (this.length <= 0)
            {
//                String err = String.format("%s根据规则名称{%s}规则方法{%s}截取字符失败:{截取设置长度%d必须大于0}", dataName, this.name, this.value, this.length);
                // 英化
                String err = String.format("%s failed to intercept characters according to the rule name {%s} rule method {%s}: {the set length %d must be greater than 0}", dataName, this.name, this.value, this.length);
                return new Result(err, null);
            }
            int dataLength = dataValue.length();
            int actualStart = this.start;
            int splitDataLength = this.start + this.length;
            if (this.reverse)
            {
                dataValue = new StringBuilder(dataValue).reverse().toString();
            }
            if (splitDataLength > dataLength)
            {
//                String err = String.format("%s根据规则名称{%s}规则方法{%s}截取字符失败:{字符截止位%d超出界限}", dataName, this.name, this.value, splitDataLength);
                // 英化
                String err = String.format("%s failed to intercept characters according to the rule name {%s} rule method {%s}: {character end position %d out of bounds}", dataName, this.name, this.value, splitDataLength);
                return new Result(err, null);
            }
            dataValue = dataValue.substring(actualStart, actualStart + this.length);
            if (this.reverse)
            {
                dataValue = new StringBuilder(dataValue).reverse().toString();
            }
            return new Result(null, dataValue);
        }


        @Data
        @AllArgsConstructor
        public static class Result
        {
            private String error;
            private String value;

            public boolean isOk()
            {
                return this.error == null;
            }

            public boolean isError()
            {
                return !this.isOk();
            }
        }
    }

    @Data
    @AllArgsConstructor
    public static class CompareRule
    {
        private String name;
        private String origin;
        private String target;

        public CompareRule(String name, String target)
        {
            this(name, null, target);
        }

        public CompareRule(String name)
        {
            this(name, null, null);
        }

        public Result process(String dataName, String errMessage)
        {
//            String err = String.format("%s未检测到字符%s信息或二维码%s信息", dataName, this.name, this.name);
            // 英化
            String err = String.format("%s did not detect character %s information or two-dimensional code %s information", dataName, this.name, this.name);
            if (ObjectUtils.isEmpty(this.origin) || BoardConst.VALUE_NULL.equals(this.origin))
            {
                return new Result(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            boolean skip = this.origin.equals(BoardConst.VALUE_NC);
            if (!skip && !this.origin.equals(this.target))
            {
                if (ObjectUtils.isEmpty(this.target))
                {
                    return new Result(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
                }
//                err = !ObjectUtils.isEmpty(errMessage) ? errMessage : String.format("%s字符%s{%s}信息不等于设定%s{%s}信息", dataName, this.name, this.origin, this.name, this.target);
                // 英化
                err = !ObjectUtils.isEmpty(errMessage) ? errMessage : String.format("%s character %s{%s} information is not equal to set %s{%s} information", dataName, this.name, this.origin, this.name, this.target);
                return new Result(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            return new Result(BoardConst.STATUS_OK, BoardConst.CODE_OK, BoardConst.BLANK);
        }

        public Result process(String dataName)
        {
            return this.process(dataName, null);
        }

        public static class Result extends CompareResult
        {
            public Result(String status, int ngCode, String ngMsg)
            {
                super(status, ngCode, ngMsg);
            }
        }
    }

    @Data
    public static class CompareData
    {
        private String name;
        private String type;
        private String orient;
        private Object origin;
        private String path;
        private Integer index;
        private String value;

        public CompareData(Board board, String path, boolean isChar)
        {
            if (board != null)
            {
                this.name = board.getBoardName();
                this.type = board.getBoardCategory();
                this.orient = board.getBoardOrient();
                this.path = path;
                if (isChar)
                {
                    this.origin = board.getBoardChar();
                    this.resolve();
                }
                else
                {
                    this.origin = board;
                    // Java反射获取对象属性值
                    Field field = ReflectionUtils.findField(board.getClass(), path);
                    if (field != null)
                    {
                        ReflectionUtils.makeAccessible(field);
                        this.value = String.valueOf(ReflectionUtils.getField(field, board));
                        this.index = board.getBoardIndex();
                    }
                }
            }
        }

        public CompareData(Board board, String property)
        {
            this(board, property, false);
        }

        public CompareData(String type, String orient, Object origin, String path, Integer index, String value)
        {
            this.type = type;
            this.orient = orient;
            this.origin = origin;
            this.path = path;
            this.index = index;
            String dataType = this.type + (!ObjectUtils.isEmpty(this.index) ? "[" + this.index + "]" : BoardConst.BLANK);
//            String dataOrient = "back".equals(this.orient) ? "反" : "正";
//            this.name = String.format("%s板%s面", dataType, dataOrient);
            // 英化
            String dataOrient = BoardConst.ORIENT_BACK.equals(this.orient) ? BoardConst.BACK : BoardConst.FRONT;
            this.name = String.format("%s %s", dataType, dataOrient);
            if (ObjectUtils.isEmpty(value))
            {
                this.resolve();
            }
            else
            {
                this.value = value;
            }
        }

        public CompareData(String type, String orient, Object origin, String path, Integer index)
        {
            this(type, orient, origin, path, index, null);
        }

        public CompareData(String type, String orient, Object origin, String path)
        {
            this(type, orient, origin, path, null, null);
        }

        private void resolve()
        {
            if (ObjectUtils.isEmpty(this.origin) || ObjectUtils.isEmpty(this.path))
            {
                this.value = BoardConst.BLANK;
                return;
            }
            try
            {
                if (this.origin instanceof String)
                {
                    this.origin = JSON.parse((String) this.origin);
                }
                this.value = String.valueOf(JSONPath.eval(this.origin, this.path));
            }
            catch (Exception ex)
            {
                this.value = ex.getMessage();
            }
        }

        public boolean isNULL()
        {
            return BoardConst.VALUE_NULL.equals(this.value);
        }

        public boolean isNC()
        {
            return BoardConst.VALUE_NC.equals(this.value);
        }
    }

    @Data
    @AllArgsConstructor
    public static class CompareResult
    {
        private String status;

        @JsonProperty("ng_code")
        private int ngCode;

        @JsonProperty("ng_msg")
        private String ngMsg;

        @JsonIgnore
        @JSONField(serialize = false, deserialize = false)
        public boolean isOK()
        {
            return BoardConst.STATUS_OK.equals(this.status);
        }

        @JsonIgnore
        @JSONField(serialize = false, deserialize = false)
        public boolean isNG()
        {
            return BoardConst.STATUS_NG.equals(this.status);
        }

        @JsonIgnore
        @JSONField(serialize = false, deserialize = false)
        public boolean isNC()
        {
            return !this.isNG();
        }

        public JSONObject toJSONObject()
        {
            JSONObject result = new JSONObject();
            result.put("array_status", this.status);
            result.put("array_ng_code", this.ngCode);
            result.put("array_ng_msg", this.ngMsg);
            return result;
        }
    }

    public interface Comparator
    {
        default void compare(CompareData data, SplitRule splitRule, SortRule sortRule) throws CompareException
        {
            // 英化
            throw new CompareException("Unimplemented comparison method");
        }

        default CompareResult compare(CompareData data, CompareRule compareRule)
        {
            if (data.isNC())
            {
                return new CompareResult(BoardConst.STATUS_OK, BoardConst.CODE_OK, BoardConst.BLANK);
            }
            if (data.isNULL())
            {
//                String err = String.format("%s未检测到字符%s信息或二维码%s信息", data.getName(), compareRule.getName(), compareRule.getName());
                // 英化
                String err = String.format("%s not detected %s information or QR code %s information", data.getName(), compareRule.getName(), compareRule.getName());
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            if (!ObjectUtils.isEmpty(compareRule.getOrigin()))
            {
                if (compareRule.getOrigin().equals(data.getValue()))
                {
                    return new CompareResult(BoardConst.STATUS_OK, BoardConst.CODE_OK, BoardConst.BLANK);
                }
//                String err = String.format("%s%s信息与%s信息不一致", data.getName(), compareRule.getName(), compareRule.getName());
                // 英化
                String err = String.format("%s %s information is inconsistent with %s information", data.getName(), compareRule.getName(), compareRule.getName());
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            return null;
        }

        default CompareResult compare(CompareData data, SplitRule splitRule, CompareRule compareRule)
        {
            CompareResult compareResult = this.compare(data, compareRule);
            if (compareResult != null)
            {
                return compareResult;
            }
            String dataValue = data.getValue();
            String dataName = data.getName();
            SplitRule.Result splitRuleResult = splitRule.process(dataName, dataValue);
            if (splitRuleResult.isError())
            {
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, splitRuleResult.getError());
            }
            dataValue = splitRuleResult.getValue();
            compareRule.setOrigin(dataValue);
            return compareRule.process(dataName);
        }

        default CompareResult compare(CompareData srcData, SplitRule srcSplitRule, CompareData dstData, SplitRule dstSplitRule, CompareRule compareRule)
        {
            CompareResult srcCompareResult = this.compare(srcData, compareRule);
            if (srcCompareResult != null)
            {
                return srcCompareResult;
            }
            CompareResult dstCompareResult = this.compare(dstData, compareRule);
            if (dstCompareResult != null)
            {
                return dstCompareResult;
            }
            String srcDataValue = srcData.getValue();
            String srcDataName = srcData.getName();
            SplitRule.Result srcSplitRuleResult = srcSplitRule.process(srcDataName, srcDataValue);
            if (srcSplitRuleResult.isError())
            {
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, srcSplitRuleResult.getError());
            }
            srcDataValue = srcSplitRuleResult.getValue();
            compareRule.setOrigin(srcDataValue);
            String dstDataValue = dstData.getValue();
            String dstDataName = dstData.getName();
            SplitRule.Result dstSplitRuleResult = dstSplitRule.process(dstDataName, dstDataValue);
            if (dstSplitRuleResult.isError())
            {
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, dstSplitRuleResult.getError());
            }
            dstDataValue = dstSplitRuleResult.getValue();
            String ruleName = compareRule.getName();
            compareRule.setOrigin(srcDataValue);
            compareRule.setTarget(dstDataValue);
//            String errMessage = String.format("%s%s信息{%s}与%s%s信息{%s}不一致", srcDataName, ruleName, srcDataValue, dstDataName, ruleName, dstDataValue);
            // 英化
            String errMessage = String.format("%s %s information {%s} is inconsistent with %s %s information {%s}", srcDataName, ruleName, srcDataValue, dstDataName, ruleName, dstDataValue);
            return compareRule.process(dstDataName, errMessage);
        }
    }

    @Data
    @AllArgsConstructor
    public static class DefaultComparator implements Comparator
    {
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    public static class DirectionComparator extends DefaultComparator implements Comparator
    {
        @Override
        public void compare(CompareData data, SplitRule splitRule, SortRule sortRule) throws CompareException
        {
            String dataName = data.getName();
            String dataValue = data.getValue();
            if (splitRule != null)
            {
                SplitRule.Result splitRuleResult = splitRule.process(dataName, dataValue);
                if (splitRuleResult.isError())
                {
                    throw new CompareException(splitRuleResult.getError());
                }
                dataValue = splitRuleResult.getValue();
            }
            // SET正面（或反面）初定位识别异常请前往CCD系统查看
            CompareRule rule1 = new CompareRule(sortRule.getName(), dataValue, BoardConst.VALUE_NULL_1);
            String rule1Msg = String.format("%s positioning Training is abnormal, please go to the CCD system to view", dataName);
            if (rule1.process(dataName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new CompareException(rule1Msg);
            }
            // SET正面（或反面）板件放反导致异常
            CompareRule rule2 = new CompareRule(sortRule.getName(), dataValue, BoardConst.VALUE_NULL_2);
            String rule2Msg = String.format("%s product features is abnormal, please go to the CCD system to view", dataName);
            if (rule2.process(dataName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new CompareException(rule2Msg);
            }
            // SET正面（或反面）产品训练异常
            CompareRule rule3 = new CompareRule(sortRule.getName(), dataValue, BoardConst.VALUE_NULL_3);
            String rule3Msg = String.format("%s SET product training is abnormal, please go to the CCD system to view", dataName);
            if (rule3.process(dataName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new CompareException(rule3Msg);
            }
            // CCD手动NG过板
            CompareRule rule4 = new CompareRule(sortRule.getName(), dataValue, BoardConst.VALUE_NULL_4);
            String rule4Msg = "CCD manually releases the NG plates";
            if (rule4.process(dataName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new CompareException(rule4Msg);
            }
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    @NoArgsConstructor
    public static class CompareException extends RuntimeException
    {
        private String status;
        private int code;

        public CompareException(String message)
        {
            this(message, BoardConst.NG, BoardConst.CODE_NG);
        }

        public CompareException(String message, String status, int code)
        {
            super(message);
            this.status = status;
            this.code = code;
        }
    }
}
