package com.api.pmc.core.flow;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * AVI推算基础类
 * 1.AVI推算
 * </p>
 *
 * <AUTHOR>
 * @since 2021-4-7
 */
@Service
@Slf4j
public class PmcCoreAviBase {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    //1.AVI推算
    public void AviIntefTask(HttpServletRequest request,String apiRoutePath,
                             String work_center_code, String station_code,
                             String station_status, String empty_ban_flag, String serial_num, String pallet_num, String staff_id,
                             String make_order, String dms, String item_project, String vin, String small_model_type,
                             String main_material_code, String material_color, String material_size, String shaft_proc_num,
                             String quality_sign, String set_sign, String check_status, String check_code, String check_msg,
                             String engine_num, String driver_way, String status_way) throws Exception {
        try{
            //默认值
            String nowDateTime= CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            String updStationStatus = "update d_pmc_me_station_status set " +
                    "last_updated_by='" + station_code + "'," +
                    "last_update_date='" + nowDateTime + "'," +
                    "station_status='" + station_status + "', " +
                    "empty_ban_flag='" + empty_ban_flag + "', " +
                    "serial_num='" + serial_num + "', " +
                    "pallet_num='" + pallet_num + "', " +
                    "staff_id='" + staff_id + "', " +
                    "make_order='" + make_order + "', " +
                    "dms='" + dms + "', " +
                    "item_project='" + item_project + "', " +
                    "vin='" + vin + "', " +
                    "small_model_type='" + small_model_type + "', " +
                    "main_material_code='" + main_material_code + "', " +
                    "material_color='" + material_color + "', " +
                    "material_size='" + material_size + "', " +
                    "shaft_proc_num='" + shaft_proc_num + "', " +
                    "quality_sign='" + quality_sign + "', " +
                    "set_sign='" + set_sign + "', " +
                    "check_status='" + check_status + "', " +
                    "check_code=" + check_code + ", " +
                    "check_msg='" + check_msg + "', " +
                    "engine_num='" + engine_num + "', " +
                    "driver_way='" + driver_way + "', " +
                    "status_way='" + status_way + "' " +
                    "where station_code='" + station_code + "' " +
                    "and work_center_code='" + work_center_code + "' ";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, updStationStatus, true, request, apiRoutePath);
        }
        catch (Exception ex){
            throw new Exception("AVI推算失败:"+ex.getMessage());
        }
    }

}
