package com.api.pmc.core.screen;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 大屏显示接口
 * 1.大屏显示信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/screen")
public class PmcCoreScreenContentController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;

    //1.大屏显示信息
    @RequestMapping(value = "/PmcCoreScreenContentSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreScreenContentSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/screen/PmcCoreScreenContentSel";
        String selectResult="";
        String errorMsg="";
        try{
            String prod_line_code=jsonParas.getString("prod_line_code");//生产线
            String station_code=jsonParas.getString("station_code");//工位
            if((prod_line_code==null || prod_line_code.equals("")) &&
                    station_code!=null && !station_code.equals("")){
                String sqlStation="select b.prod_line_code " +
                        "from sys_fmod_station a," +
                        "     sys_fmod_prod_line b " +
                        "where a.prod_line_id=b.prod_line_id " +
                        "and a.enable_flag='Y' " +
                        "and b.enable_flag='Y' " +
                        "and a.station_code='"+station_code+"' "+
                        "LIMIT 1 OFFSET 0";
                List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlStation,false,null,apiRoutePath);
                if(itemListStation!=null && itemListStation.size()>0){
                    prod_line_code=itemListStation.get(0).get("prod_line_code").toString();
                }
            }
            //获取大屏
            String sqlScreenContent="select COALESCE(today_plan,'') today_plan," +
                    "COALESCE(current_offline,'') current_offline," +
                    "COALESCE(finish_rate,'') finish_rate," +
                    "COALESCE(jph_actual,'') jph_actual," +
                    "COALESCE(device_mobility,'') device_mobility," +
                    "COALESCE(today_stop,'') today_stop," +
                    "COALESCE(above_show,'') above_show, " +
                    "COALESCE(below_show,'') below_show, " +
                    "COALESCE(background_image,'') background_image " +
                    "from d_pmc_fmod_screen_set_content " +
                    "where enable_flag='Y' " +
                    "and prod_line_code='"+prod_line_code+ "' ";
            List<Map<String, Object>> itemListScreen=cFuncDbSqlExecute.ExecSelectSql(prod_line_code,sqlScreenContent,false,request,apiRoutePath);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListScreen,"","",0);
        }
        catch (Exception ex){
            errorMsg= "大屏信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.大屏冲压显示信息(冲压)
    @RequestMapping(value = "/PmcCoreCaScreenContentSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreCaScreenContentSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/screen/PmcCoreCaScreenContentSel";
        String selectResult="";
        String errorMsg="";
        String method="/aisEsbOra/pmc/core/interf/PmcCoreMesNextMakeOrderSel";
        try{
            String prod_line_code=jsonParas.getString("prod_line_code");//生产线
            String work_center_code=jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            //1、获取上位订单(oracle订单)
            String make_order="";//订单号
            String main_material_code="";//物料编号
            String material_description="";//物料描述
            String order_quantity="";//订单数量
            String main_material_code_next="";//物料编号(下一个)
            String material_description_next="";//物料描述(下一个)
            String order_quantity_next="";//订单数量(下一个)
            //计算
            String current_prod_part="";//当前生产零件
            String current_plan_num="";//计划产量(件)
            String next_prod_part="";//下批次准备
            String next_plan_num="";//下批次计划产量(件)
            if(PmcCoreServer.EsbUrl==null ||
                    PmcCoreServer.EsbUrl.isEmpty()){
                pmcCoreServerInit.ServerInit();
            }
            //获取当前订单
            JSONObject jsonObjectMoReq = new JSONObject();
            jsonObjectMoReq.put("order_prod","");//下一个订单
            jsonObjectMoReq.put("workshop",work_center_code);
            jsonObjectMoReq.put("order_state","1");//订单状态(1.已生成2.已发布3.已上线4.拉入5.下线6.拉出)
            JSONObject jsonObjectMoRes= cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl+method, jsonObjectMoReq);
            Integer moCode=jsonObjectMoRes.getInteger("code");
            if(moCode==0) {
                JSONArray oraArry=jsonObjectMoRes.getJSONArray("data");
                if(oraArry!=null){
                    JSONObject oraJsonObject = oraArry.getJSONObject(0);
                    make_order = oraJsonObject.getString("ORDER_PROD");//生产订单
                    main_material_code = oraJsonObject.getString("MATERIAL_CODE");//物料编码
                    material_description = oraJsonObject.getString("MATERIAL_DESCRIPTION");//颜色
                    order_quantity = oraJsonObject.getString("ORDER_QUANTITY");//订单数量
                }
            }
            if(!make_order.isEmpty()){
                //获取当前订单(下一个)
                JSONObject jsonObjectMoNextReq = new JSONObject();
                jsonObjectMoNextReq.put("order_prod",make_order);//下一个订单
                jsonObjectMoNextReq.put("workshop",work_center_code);
                jsonObjectMoNextReq.put("order_state","1");//订单状态(1.已生成2.已发布3.已上线4.拉入5.下线6.拉出)
                JSONObject jsonObjectMoNextRes= cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl+method, jsonObjectMoNextReq);
                Integer moNextCode=jsonObjectMoNextRes.getInteger("code");
                if(moNextCode==0) {
                    JSONArray oraNextArry=jsonObjectMoNextRes.getJSONArray("data");
                    if(oraNextArry!=null){
                        JSONObject oraJsonObjectNext = oraNextArry.getJSONObject(0);
                        main_material_code_next = oraJsonObjectNext.getString("MATERIAL_CODE");//物料编码
                        material_description_next = oraJsonObjectNext.getString("MATERIAL_DESCRIPTION");//颜色
                        order_quantity_next = oraJsonObjectNext.getString("ORDER_QUANTITY");//订单数量
                    }
                }
            }
            //修改
            String updStation="update d_pmc_fmod_screen_set_content set " +
                    "current_prod_part='"+(main_material_code+"|"+material_description)+"', " +
                    "current_plan_num='"+order_quantity+"', "+
                    "make_order='"+make_order+"', " +
                    "next_prod_part='"+(main_material_code_next+"|"+material_description_next)+"', " +
                    "next_plan_num='"+order_quantity_next+"' "+
                    "where enable_flag='Y' " +
                    "and prod_line_code='"+prod_line_code+ "' ";
            cFuncDbSqlExecute.ExecUpdateSql(prod_line_code, updStation, true, null,apiRoutePath);
            //2、格式化JSON
            List<Map<String, Object>> stationScreenList=new ArrayList<>();
            //获取大屏
            String plan_num="";//当班计划产量(手动维护)
            String target_spm="";//目标SPM(手动维护)
            String plan_prod_time="";//计划生产时间
            String sum_stop_time="";//累计停线时间
            String device_start_rate="";//设备开动率
            String sqlScreenContent="select COALESCE(plan_num,'-1') plan_num," +
                    "COALESCE(current_prod_part,'') current_prod_part," +
                    "COALESCE(current_plan_num,'-1') current_plan_num," +
                    "COALESCE(next_prod_part,'') next_prod_part," +
                    "COALESCE(next_plan_num,'-1') next_plan_num," +
                    "COALESCE(target_spm,'-1') target_spm," +
                    "COALESCE(plan_prod_time,'-1') plan_prod_time, " +
                    "COALESCE(sum_stop_time,'-1') sum_stop_time, " +
                    "COALESCE(device_start_rate,'-1') device_start_rate " +
                    "from d_pmc_fmod_screen_set_content " +
                    "where enable_flag='Y' " +
                    "and prod_line_code='"+prod_line_code+ "' ";
            List<Map<String, Object>> itemListScreenContent=cFuncDbSqlExecute.ExecSelectSql(prod_line_code,sqlScreenContent,false,request,apiRoutePath);
            if(itemListScreenContent!=null && itemListScreenContent.size()>0){
                current_prod_part=itemListScreenContent.get(0).get("current_prod_part").toString();
                current_plan_num=itemListScreenContent.get(0).get("current_plan_num").toString();
                next_prod_part=itemListScreenContent.get(0).get("next_prod_part").toString();
                next_plan_num=itemListScreenContent.get(0).get("next_plan_num").toString();
                plan_num=itemListScreenContent.get(0).get("plan_num").toString();
                target_spm=itemListScreenContent.get(0).get("target_spm").toString();
                plan_prod_time=itemListScreenContent.get(0).get("plan_prod_time").toString();
                sum_stop_time=itemListScreenContent.get(0).get("sum_stop_time").toString();
                device_start_rate=itemListScreenContent.get(0).get("device_start_rate").toString();
                //格式化
                Map<String, Object> stationScreenItem=new HashMap<>();
                stationScreenItem.put("current_prod_part",current_prod_part);
                stationScreenItem.put("current_plan_num",current_plan_num);
                stationScreenItem.put("next_prod_part",next_prod_part);
                stationScreenItem.put("next_plan_num",next_plan_num);
                stationScreenItem.put("plan_num",plan_num);
                stationScreenItem.put("target_spm",target_spm);
                stationScreenItem.put("plan_prod_time",plan_prod_time);
                stationScreenItem.put("sum_stop_time",sum_stop_time);
                stationScreenItem.put("device_start_rate",device_start_rate);
                stationScreenList.add(stationScreenItem);
            } else {
                Map<String, Object> stationScreenItem=new HashMap<>();
                stationScreenItem.put("current_prod_part","0");
                stationScreenItem.put("current_plan_num","0");
                stationScreenItem.put("next_prod_part","0");
                stationScreenItem.put("next_plan_num","0");
                stationScreenItem.put("plan_num","0");
                stationScreenItem.put("target_spm","0");
                stationScreenItem.put("mould_switch_time","0");
                stationScreenItem.put("plan_prod_time","0");
                stationScreenItem.put("sum_stop_time","0");
                stationScreenItem.put("device_start_rate","0");
                stationScreenList.add(stationScreenItem);
            }
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,stationScreenList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "大屏冲压显示信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //3.大屏冲压点位显示信息(冲压)
    @RequestMapping(value = "/PmcCoreCaScreenContentTagSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreCaScreenContentTagSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/screen/PmcCoreCaScreenContentTagSel";
        String selectResult="";
        String errorMsg="";
        try{
            String prod_line_code=jsonParas.getString("prod_line_code");//生产线
            String work_center_code=jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            //返回JSON
            List<Map<String, Object>> stationScreenList=new ArrayList<>();
            //获取大屏
            String theory_finish="";//理论完成
            String actual_num="";//实际产量
            String actual_spm="";//实际SPM
            String mould_switch_time="";//模具切换时间
            String day_theory="";//当班理论
            String day_actual="";//当班实际完成
            String tag_line="";//整线状态
            String tag_op10="";//OP10点位
            String tag_op20="";//OP20点位
            String tag_op30="";//OP30点位
            String tag_op40="";//OP40点位
            String tag_op50="";//OP50点位
            String sqlScreenContent="select COALESCE(theory_finish,'-1') theory_finish," +
                    "COALESCE(actual_num,'-1') actual_num," +
                    "COALESCE(actual_spm,'-1') actual_spm, " +
                    "COALESCE(mould_switch_time,'-1') mould_switch_time, " +
                    "COALESCE(day_theory,'-1') day_theory," +
                    "COALESCE(day_actual,'-1') day_actual," +
                    "COALESCE(tag_line,'-1') tag_line," +
                    "COALESCE(tag_op10,'-1') tag_op10," +
                    "COALESCE(tag_op20,'-1') tag_op20, " +
                    "COALESCE(tag_op30,'-1') tag_op30, " +
                    "COALESCE(tag_op40,'-1') tag_op40, " +
                    "COALESCE(tag_op50,'-1') tag_op50 " +
                    "from d_pmc_fmod_screen_set_content " +
                    "where enable_flag='Y' " +
                    "and prod_line_code='"+prod_line_code+ "' ";
            List<Map<String, Object>> itemListScreenContent=cFuncDbSqlExecute.ExecSelectSql(prod_line_code,sqlScreenContent,false,request,apiRoutePath);
            if(itemListScreenContent!=null && itemListScreenContent.size()>0){
                theory_finish=itemListScreenContent.get(0).get("theory_finish").toString();
                actual_num=itemListScreenContent.get(0).get("actual_num").toString();
                actual_spm=itemListScreenContent.get(0).get("actual_spm").toString();
                float actual_spm_float=Float.parseFloat(actual_spm);
                actual_spm=String.valueOf(Math.round(actual_spm_float/10));
                mould_switch_time=itemListScreenContent.get(0).get("mould_switch_time").toString();
                float mould_switch_time_float=Float.parseFloat(mould_switch_time);
                mould_switch_time=String.format("%.2f",(mould_switch_time_float/60));
                day_theory=itemListScreenContent.get(0).get("day_theory").toString();
                day_actual=itemListScreenContent.get(0).get("day_actual").toString();
                tag_line=itemListScreenContent.get(0).get("tag_line").toString();
                tag_op10=itemListScreenContent.get(0).get("tag_op10").toString();
                tag_op20=itemListScreenContent.get(0).get("tag_op20").toString();
                tag_op30=itemListScreenContent.get(0).get("tag_op30").toString();
                tag_op40=itemListScreenContent.get(0).get("tag_op40").toString();
                tag_op50=itemListScreenContent.get(0).get("tag_op50").toString();
                //格式化
                Map<String, Object> stationScreenItem=new HashMap<>();
                stationScreenItem.put("theory_finish",theory_finish);
                stationScreenItem.put("actual_num",actual_num);
                stationScreenItem.put("actual_spm",actual_spm);
                stationScreenItem.put("mould_switch_time",mould_switch_time);
                stationScreenItem.put("day_theory",day_theory);
                stationScreenItem.put("day_actual",day_actual);
                stationScreenItem.put("tag_line",tag_line);
                stationScreenItem.put("tag_op10",tag_op10);
                stationScreenItem.put("tag_op20",tag_op20);
                stationScreenItem.put("tag_op30",tag_op30);
                stationScreenItem.put("tag_op40",tag_op40);
                stationScreenItem.put("tag_op50",tag_op50);
                stationScreenList.add(stationScreenItem);
            } else {
                Map<String, Object> stationScreenItem=new HashMap<>();
                stationScreenItem.put("theory_finish","0");
                stationScreenItem.put("actual_num","0");
                stationScreenItem.put("actual_spm","0");
                stationScreenItem.put("mould_switch_time","0");
                stationScreenItem.put("day_theory","0");
                stationScreenItem.put("day_actual","0");
                stationScreenItem.put("tag_line","0");
                stationScreenItem.put("tag_op10","0");
                stationScreenItem.put("tag_op20","0");
                stationScreenItem.put("tag_op30","0");
                stationScreenItem.put("tag_op40","0");
                stationScreenItem.put("tag_op50","0");
                stationScreenList.add(stationScreenItem);
            }
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,stationScreenList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "大屏冲压点位显示信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}

