package com.api.pack.project.avary.op;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.base.Const;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pack.core.board.BoardConst;
import com.api.pack.core.board.SETBoard;
import com.api.pack.core.ccd.CCDMappingResultMessage;
import com.api.pack.core.ccd.CCDMappingResultMessageContentItemDetailRepository;
import com.api.pack.core.sort.Sort;
import com.api.pack.core.sort.SortMapper;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.ResourceAccessException;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.net.SocketTimeoutException;
import java.nio.charset.Charset;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 鹏鼎包装机MES接口函数
 * 1.MES下发设备批次信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-08
 */
@Transactional(rollbackFor = Exception.class)
@Service
@Slf4j
public class PackAvaryMesInterfFunc
{
    private final RestTemplate restTemplate;

    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PackAvaryInterfCommon packAvaryInterfCommon;

    @Autowired
    private CCDMappingResultMessageContentItemDetailRepository ccdMesMessageMappingResultRepository;

    @Autowired
    private SortMapper sortMapper;

    public PackAvaryMesInterfFunc()
    {
        SimpleClientHttpRequestFactory requestFactory = new SimpleClientHttpRequestFactory();
        // 设置连接超时时间（单位：毫秒）
        requestFactory.setConnectTimeout(5000);
        // 设置读取超时时间（单位：毫秒）
        requestFactory.setReadTimeout(30000);
        this.restTemplate = new RestTemplate(requestFactory);
    }

    //下载mapping--ftp相关
    public FTPClient ftp;
    public ArrayList<String> arFiles;

    //1.鹏鼎包装机MES接口函数
    public JSONObject MesSetPackingEqpInfoDownLoad(String apiRoutePath, JSONObject jsonParas)
    {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "SetPackingEqpInfo";
        String token = "";
        String apsPlanTable = "a_pack_aps_plan";
        String recipeTable = "a_pack_fmod_recipe";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String userName = "Mes";
        try
        {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "can not find esbInterfCode:{" + esbInterfCode + "} in table sys_core_esb_interf";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "can not find esbInterfCode:{" + esbInterfCode + "} in table sys_core_esb_interf";
                throw new Exception(errorMsg);
            }
            String packType = jsonParas.getString("packType");//Inner:内包 Outer:外包
            String equipmentNo = jsonParas.getString("equipmentNo");//机台
            String lot = jsonParas.getString("lot");//批号---申请内标
            String plantLot = jsonParas.getString("plantLot");//厂内批号
            String product = jsonParas.getString("product");//料号
            String innerNum = jsonParas.getString("innerNum");//内包数---包内数量
            String outerNum = jsonParas.getString("outerNum");//外包数
            String stripLenth = jsonParas.getString("stripLenth");//板长
            String stripWide = jsonParas.getString("stripWide");//版宽
            String stripPly = jsonParas.getString("stripPly");//板厚
//            String PCSWeight = jsonParas.getString("PCSWeight");//单PCS重 由于MES接口下发的取值有误，所以不用，改为配方维护 modified by jay-y 2024/08/17
            String dateCode = jsonParas.getString("dateCode");//周期
            String comments = jsonParas.getString("comments");//备注
            String newSOName = jsonParas.getString("newSOName");//新订单
            String machineName = jsonParas.getString("machineName");//新种名
            String stripWeight = jsonParas.getString("stripWeight");//单Strip重
            String maxWeight = jsonParas.getString("maxWeight");//重量浮动上限值
            String minWeight = jsonParas.getString("minWeight");//重量浮动下限值
            String originalLot = jsonParas.getString("originalLot");//原始批号---调用mes接口用
            String shipAddress = jsonParas.getString("shipAddress");//出货地---原则上需要根据出货地配置标签上码的内容
            String suppliesWeight = jsonParas.getString("suppliesWeight");//单包物料重量
            String certifyLot = jsonParas.getString("certifyLot");//验证批号----线扫批号校验
            JSONArray stripList = jsonParas.getJSONArray("StripList");

            //2.判断lot_num是否重复
            Query query = new Query();
            query.addCriteria(Criteria.where("lot_num").is(lot));
            query.addCriteria(Criteria.where("enable_flag").is("Y")); // 只判断有效数据 modified by jay-y 2024/08/17
            List<HashMap> hashMaps = mongoTemplate.find(query, HashMap.class, apsPlanTable);
            if (!CollectionUtils.isEmpty(hashMaps))
            {
                errorMsg = "lot_num:{" + lot + "}是重复的";
                responseParas = PackAvaryInterfCommon.GetStandJson(false, null, null, errorMsg, 0);
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //3.判断product在系统中是否提前维护
            String recipeSql = "select array_type,bd_type,model_type,m_weight from a_pack_fmod_recipe where model_type = '" + product + "'";
            List<Map<String, Object>> itemListRecipe = cFuncDbSqlExecute.ExecSelectSql(userName, recipeSql, false, null, apiRoutePath);
            if (itemListRecipe.size() == 0 || itemListRecipe == null)
            {
                errorMsg = "料号:{" + product + "} 未在系统中提前维护";
                responseParas = PackAvaryInterfCommon.GetStandJson(false, null, null, errorMsg, 0);
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            Map<String, Object> recipeMap = itemListRecipe.get(0);
            Object arrayType = recipeMap.get("array_type");
            Object bdType = recipeMap.get("bd_type");
            Object mWeight = recipeMap.get("m_weight");

            //4.判断是否当前存在WORK中的订单
            Query query1 = new Query();
            query1.addCriteria(Criteria.where("lot_status").is("WORK"));
            List<HashMap> hashMaps1 = mongoTemplate.find(query1, HashMap.class, apsPlanTable);
            if (!CollectionUtils.isEmpty(hashMaps1))
            {
                errorMsg = "当前已存在生产中的工单，请稍后下发";
                responseParas = PackAvaryInterfCommon.GetStandJson(false, null, null, errorMsg, 0);
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            SimpleDateFormat yyyyMMddHHmmss = new SimpleDateFormat("yyyyMMddHHmmss");
            SimpleDateFormat yyyy_MM_dd_HH_mm_ss = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date = new Date();
            jsonParas.put("item_date_val", Long.parseLong(yyyyMMddHHmmss.format(date)));
            jsonParas.put("item_date", yyyy_MM_dd_HH_mm_ss.format(date));
            jsonParas.put("plan_id", CFuncUtilsSystem.CreateUUID(true));
            jsonParas.put("plan_lot_count", StringUtils.isEmpty(outerNum) ? 0 : Integer.parseInt(outerNum));
            jsonParas.put("unit_count", StringUtils.isEmpty(innerNum) ? 0 : Integer.parseInt(innerNum));
            jsonParas.put("cycle_period", ObjectUtils.isEmpty(dateCode) ? "" : dateCode);
            jsonParas.put("finish_ok_count", 0);
            jsonParas.put("finish_ng_count", 0);
            jsonParas.put("task_cost_time", 0L);
            jsonParas.put("m_length", StringUtils.isEmpty(stripLenth) ? 0.0d : Double.parseDouble(stripLenth));
            jsonParas.put("m_width", StringUtils.isEmpty(stripWide) ? 0.0d : Double.parseDouble(stripWide));
            jsonParas.put("m_tickness", StringUtils.isEmpty(stripPly) ? 0.0d : Double.parseDouble(stripPly));
            jsonParas.put("m_weight", StringUtils.isEmpty(mWeight) ? 0.0d : new BigDecimal(mWeight.toString()).doubleValue());
//            jsonParas.put("m_weight", StringUtils.isEmpty(PCSWeight) ? 0.0d : Double.parseDouble(PCSWeight)); // 由于MES接口下发的取值有误，所以不用，改为配方维护 modified by jay-y 2024/08/17
            jsonParas.put("task_from", "MES");
            jsonParas.put("task_type", packType);
            jsonParas.put("lot_num", lot);
            jsonParas.put("model_type", product);
            jsonParas.put("model_version", "");
            jsonParas.put("array_type", arrayType);
            jsonParas.put("bd_type", bdType);
            jsonParas.put("plant_code", plantLot);
            jsonParas.put("sales_order", "");
            jsonParas.put("sales_item", "");
            jsonParas.put("sales_org", "");
            jsonParas.put("sales_type", "");
            jsonParas.put("custom_pn", "");
            jsonParas.put("custom_po", "");
            jsonParas.put("custom_code", "");
            jsonParas.put("custom_name", "");
            jsonParas.put("split_lot", "");
            jsonParas.put("split_model", "");
            jsonParas.put("lot_status", "PLAN");
            jsonParas.put("task_start_time", "");
            jsonParas.put("task_end_time", "");
            jsonParas.put("recipe_paras", "");
            jsonParas.put("sort_paras", "");
            jsonParas.put("enable_flag", "Y");
            jsonParas.put("finish_pile_count", 0);
            jsonParas.put("finish_lable_count", 0);
            jsonParas.put("batch_no", "");// 批号
            jsonParas.put("laser_batch_no", ""); // 镭射批号
            jsonParas.put("typesetting_no", "");// 排版数
            jsonParas.put("customer_mn", product);// 客户料号
            jsonParas.put("ul_code", "");// UL
            jsonParas.put("equipment_no", equipmentNo);
            jsonParas.put("comments", comments);
            jsonParas.put("new_so_name", newSOName);
            jsonParas.put("machine_name", machineName);
            jsonParas.put("strip_weight", stripWeight);
            jsonParas.put("max_weight", maxWeight);
            jsonParas.put("min_weight", minWeight);
            jsonParas.put("original_lot", originalLot);
            jsonParas.put("ship_address", shipAddress);
            jsonParas.put("supplies_weight", suppliesWeight);
            jsonParas.put("certify_lot", certifyLot);
            jsonParas.put("strip_list", stripList != null ? stripList.toJSONString() : "");
            jsonParas.put("attribute1", "");
            jsonParas.put("attribute2", "");
            jsonParas.put("attribute3", "");
            mongoTemplate.insert(jsonParas, apsPlanTable);
            responseParas = PackAvaryInterfCommon.GetStandJson(true, null, null, "订单接收成功", 0);
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "SetPackingEqpInfo suncess");
            return jbResult;
        }
        catch (Exception ex)
        {
            errorMsg = "SetPackingEqpInfo error: " + ex.getMessage();
            log.error(errorMsg, ex);
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.向MES请求内标码
    public JSONObject MesBagInternalLabelReq(String station_code, String grossWeight, String sortStationCode, String xout_act_num, String plc_stack_qty)
    {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "ahInnerPackingRequest";
        String esb_interf_des = "";
        String token = "";
        String apsPlanTable = "a_pack_aps_plan";
        String packMeArrayTable = "a_pack_me_array";
        String packMePileTable = "a_pack_me_pile";
        String requestParas = "";
        String responseParas = "";
        String userName = "Mes";
        try
        {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "can not find esbInterfCode:{" + esbInterfCode + "} in table sys_core_esb_interf";
                throw new Exception(errorMsg);
            }
            //2.获取当前工作订单信息
            String lot_num = "";
            String qty = !ObjectUtils.isEmpty(plc_stack_qty) ? plc_stack_qty : "0";
            String cycle_period = "0";
            String eqpID = station_code;
            String comments = "";
            String newSOName = "";
            String machineName = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            MongoCursor<Document> iterator = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            if (iterator.hasNext())
            {
                Document plan = iterator.next(); // 当前工作中的订单
                lot_num = plan.getString("lot_num");
                // 修复周期编码问题
                String planCyclePeriod = plan.getString("cycle_period");
                planCyclePeriod = planCyclePeriod.replaceAll("[^0-9]", "");
                cycle_period = String.format("%04d", Integer.parseInt(planCyclePeriod));
                eqpID = plan.getString("equipmentNo");
                comments = plan.getString("comments");
                newSOName = plan.getString("new_so_name");
                machineName = plan.getString("machine_name");
                iterator.close();
            }
            Query lastPileQuery = new Query();
            lastPileQuery.addCriteria(Criteria.where("lot_num").is(lot_num));
            lastPileQuery.addCriteria(Criteria.where("enable_flag").is("Y"));
            lastPileQuery.with(org.springframework.data.domain.Sort.by(org.springframework.data.domain.Sort.Direction.DESC, "item_date_val"));
            Document pile = mongoTemplate.getCollection(packMePileTable).find(lastPileQuery.getQueryObject()).sort(lastPileQuery.getSortObject()).first();
            if (pile == null)
            {
                errorMsg = "未查询到lot_num为{" + lot_num + "}的包数据";
                throw new Exception(errorMsg);
            }
            String plcArrayList = pile.getString("plc_array_list");
            // 查询板件数据
            List<Document> array = new ArrayList<>();
            if (!ObjectUtils.isEmpty(plcArrayList))
            {
                Query arrayQuery = new Query();
                arrayQuery.addCriteria(Criteria.where("lot_num").is(lot_num));
                arrayQuery.addCriteria(Criteria.where("enable_flag").is("Y"));
                arrayQuery.addCriteria(Criteria.where("board_sn").in(Arrays.asList(plcArrayList.split(","))));
                array = mongoTemplate.getCollection(packMeArrayTable).find(arrayQuery.getQueryObject(), Document.class).noCursorTimeout(true).into(new ArrayList<>());
            }
            if (array.size() <= 0)
            {
                errorMsg = "未查询到lot_num为{" + lot_num + "}的包有板件数据";
                throw new Exception(errorMsg);
            }
            qty = "0".equals(qty) ? String.valueOf(array.size()) : qty;
            Map<String, String> parkingDetailsMap = new LinkedHashMap<>();
            for (Document doc : array)
            {
                String arrayBarcode = doc.getString("array_barcode");
                String arrayFrontInfo = doc.getString("array_front_info");
                // {"SetQRC":"@NULL@","SetQRCLevel":"F","DirMarkChkRtl":"NG","Direction":"0","LayoutQty":"3","XoutQty":"1","SetChar":{"PlainCode":"@NC@","DateCode":"@NC@","LotNum":"@NC@","PartNumber":"@NULL@","CProductName":"@NC@"},"PcsMsgList":[{"PcsNum":"1","PcsQRC":"@NC@","PcsQRCLevel":"@NC@","IsXout":"false","DirMarkChkRtl":"OK","Direction":"0","UnRecognise":"false","PcsChar":{"PlainCode":"@NC@","DateCode":"@NC@","LotNum":"@NC@","PartNumber":"@NC@","CProductName":"@NC@"}},{"PcsNum":"2","PcsQRC":"@NC@","PcsQRCLevel":"@NC@","IsXout":"false","DirMarkChkRtl":"OK","Direction":"0","UnRecognise":"false","PcsChar":{"PlainCode":"@NC@","DateCode":"@NC@","LotNum":"@NC@","PartNumber":"@NC@","CProductName":"@NC@"}},{"PcsNum":"3","PcsQRC":"@NC@","PcsQRCLevel":"@NC@","IsXout":"true","DirMarkChkRtl":"OK","Direction":"0","UnRecognise":"false","PcsChar":{"PlainCode":"@NC@","DateCode":"@NC@","LotNum":"@NC@","PartNumber":"@NC@","CProductName":"@NC@"}}],"CustomerQRC1":"@NULL@","CustomerQRC2":"@NC@","CustQRC1Level":"F","CustQRC2Level":"@NC@","RepairLabel":"FALSE","FlowError":"False","FlowErrorMsg":"","PicPath":""}
                if (ObjectUtils.isEmpty(arrayFrontInfo))
                {
                    errorMsg = "查询到lot_num为{" + lot_num + "}的包板件数据不全";
                    throw new Exception(errorMsg);
                }
                JSONObject joArrayFrontInfo = JSONObject.parseObject(arrayFrontInfo);
                SETBoard board = new SETBoard(BoardConst.ORIENT_FRONT, joArrayFrontInfo);
                // 对应添加客户QRC1
                if (!ObjectUtils.isEmpty(arrayBarcode))
                {
                    String customerQRC1 = board.getCustomerQRC1();
                    parkingDetailsMap.put(arrayBarcode, !ObjectUtils.isEmpty(customerQRC1) ? customerQRC1 : Const.BLANK);
                }
            }

            //拿到当前分选条件中的X位/X数设定
            Sort sort = this.sortMapper.selectById(BoardConst.SORT_CODE_XOUT_NP);
            JSONArray jaSort_value = JSONArray.parseArray(sort.getSortValue());
            List<String> pos = new LinkedList<>();
            String XSite = "0";
            String xQty = "0";
            if (Const.FLAG_Y.equals(sort.getEnableFlag()))
            {
                if (!ObjectUtils.isEmpty(xout_act_num))
                {
                    xQty = xout_act_num;
                }
                if (Integer.parseInt(xQty) > 0)
                {
                    for (int i = 0; i < jaSort_value.size(); i++)
                    {
                        JSONObject item = jaSort_value.getJSONObject(i);
                        if (sortStationCode.equals(item.getString("key")))
                        {
                            String posStr = item.getString("pos");
                            if ("".equals(posStr) || "-".equals(posStr))
                            {
                                pos.add("0");
                            }
                            else
                            {
                                pos.add(posStr);
                            }
                            break;
                        }
                    }
                    if (pos.size() > 0)
                    {
                        XSite = String.join(",", pos);
                    }
                }
            }
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("From", "PACKEQU");
            jsonObjectReq.put("Message", "ahInnerPackingRequest");
            jsonObjectReq.put("DateTime", CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss"));
            JSONObject content = new JSONObject();
            content.put("EqpID", eqpID);
            content.put("lot", lot_num);
            content.put("qty", qty);
            content.put("dateCode", cycle_period);
            // 设置打包明细 modify by jay-y 2024-07-15
            content.put("PackingDetail", parkingDetailsMap.keySet());
            content.put("XSite", XSite);
            content.put("xQty", xQty);
            content.put("comments", comments);
            content.put("newSOName", newSOName);
            content.put("machineName", machineName);
            content.put("grossWeight", grossWeight);
            content.put("CustomerCode", parkingDetailsMap.values());
            String contentStr = content.toString();
            jsonObjectReq.put("Content", contentStr);
            requestParas = jsonObjectReq.toString();
            JSONObject jsonObjectMes = this.autoRetryPostJbBack(esb_prod_intef_url, jsonObjectReq, 0);
            responseParas = jsonObjectMes.toString();
            String strCode = jsonObjectMes.getString("strCode");
            boolean bSucc = jsonObjectMes.getBoolean("bSucc");
            String strMsg = jsonObjectMes.getString("strMsg");
            if (!"0000".equals(strCode) && !bSucc)
            {
                // 解析返回的数据并写入条码 added by jay-y 2024-07-18
                Query pileQuery = new Query();
                pileQuery.addCriteria(Criteria.where("pile_id").is(pile.get("pile_id")));
                Update pileUpdate = new Update();
                // 增加申请接口的返回码和返回信息 modified by jay-y 2024/08/26
                pileUpdate.set("apply_return_code", strCode);
                pileUpdate.set("apply_return_msg", strMsg);
                // 增加联合状态为NG added by jay-y 2024/08/27
                pileUpdate.set("union_status", "NG");
                mongoTemplate.updateFirst(pileQuery, pileUpdate, packMePileTable);
                errorMsg = esb_interf_des + "接口返回失败：" + strMsg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("esbInterfReturnCode", strCode);
                jbResult.put("esbInterfReturnMsg", strMsg);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            // 解析返回的数据并写入条码 added by jay-y 2024/07/18
            String resContent = jsonObjectMes.getString("Content");
            Query pileQuery = new Query();
            pileQuery.addCriteria(Criteria.where("pile_id").is(pile.get("pile_id")));
            Update pileUpdate = new Update();
            pileUpdate.set("pile_barcode", resContent);
            pileUpdate.set("custom_barcode", resContent);
            // 增加申请接口的返回码和返回信息 added by jay-y 2024/08/24
            pileUpdate.set("apply_return_code", strCode);
            pileUpdate.set("apply_return_msg", strMsg);
            mongoTemplate.updateFirst(pileQuery, pileUpdate, packMePileTable);
            List<String> arrayIds = array.stream().map(item -> item.getString("array_id")).collect(Collectors.toList());
            Query arrayQuery = new Query();
            arrayQuery.addCriteria(Criteria.where("array_id").in(arrayIds));
            Update arrayUpdate = new Update();
            arrayUpdate.set("pile_barcode", resContent);
            mongoTemplate.updateMulti(arrayQuery, arrayUpdate, packMeArrayTable);

            //获取成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("esbInterfReturnCode", strCode);
            jbResult.put("esbInterfReturnMsg", strMsg);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "ahInnerPackingRequest sucess");
            return jbResult;
        }
        catch (Exception ex)
        {
            errorMsg = "ahInnerPackingRequest error: " + ex.getMessage();
            log.error(errorMsg, ex);
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            if (ObjectUtils.isEmpty(responseParas))
            {
                responseParas = "{\"strMsg\":\"" + errorMsg + "\"}";
            }
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.通知mes打印内包码
    public JSONObject MesBagInternalLabelPrint(String station_code, String barCode)
    {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "ahInnerPackingLabelPrint";
        String esb_interf_des = "";
        String token = "";
        String apsPlanTable = "a_pack_aps_plan";
        String requestParas = "";
        String responseParas = "";
        String userName = "Mes";
        try
        {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "can not find esbInterfCode:{" + esbInterfCode + "} in table sys_core_esb_interf";
                throw new Exception(errorMsg);
            }

            //2.获取当前工作订单信息
            String eqpID = station_code;

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            MongoCursor<Document> iterator = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            if (iterator.hasNext())
            {
                Document plan = iterator.next(); // 当前工作中的订单
                eqpID = plan.getString("equipmentNo");
                iterator.close();
            }

            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("From", "PACKEQU");
            jsonObjectReq.put("Message", "ahInnerPackingLabelPrint");
            jsonObjectReq.put("DateTime", CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss"));
            JSONObject content = new JSONObject();
            content.put("barCode", barCode);
            content.put("equipmentNo", eqpID);
            jsonObjectReq.put("Content", content.toString());
            requestParas = jsonObjectReq.toJSONString();
            JSONObject jsonObjectMes = this.autoRetryPostJbBack(esb_prod_intef_url, jsonObjectReq, 0);
            responseParas = jsonObjectMes.toString();
            String strCode = jsonObjectMes.getString("strCode");
            boolean bSucc = jsonObjectMes.getBoolean("bSucc");
            String strMsg = jsonObjectMes.getString("strMsg");
            if (!"0000".equals(strCode) && !bSucc)
            {
                errorMsg = esb_interf_des + "接口返回失败：" + strMsg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("esbInterfReturnCode", strCode);
                jbResult.put("esbInterfReturnMsg", strMsg);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //获取成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("esbInterfReturnCode", strCode);
            jbResult.put("esbInterfReturnMsg", strMsg);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "ahInnerPackingLabelPrint sucess");
            return jbResult;
        }
        catch (Exception ex)
        {
            errorMsg = "ahInnerPackingLabelPrint error: " + ex.getMessage();
            log.error(errorMsg, ex);
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            if (ObjectUtils.isEmpty(responseParas))
            {
                responseParas = "{\"strMsg\":\"" + errorMsg + "\"}";
            }
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.开始生产获取mapping结果
    public JSONObject ResolveStripInspectDataToDB(String lot_no, String product_no, String process_no)
    {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "GetStripInspectData";
        String esb_interf_des = "";
        String token = "";
        String apsPlanTable = "a_pack_aps_plan";
        String requestParas = "";
        String responseParas = "";
        String userName = "Mes";
        try
        {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "can not find esbInterfCode:{" + esbInterfCode + "} in table sys_core_esb_interf";
                throw new Exception(errorMsg);
            }
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("From", "PACKEQU");
            jsonObjectReq.put("Message", "GetStripInspectData");
            jsonObjectReq.put("DateTime", CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss"));
            JSONObject content = new JSONObject();
            content.put("lot_no", lot_no);
            content.put("product_no", product_no);
            content.put("process_no", process_no);
            jsonObjectReq.put("Content", content.toString());
            requestParas = jsonObjectReq.toJSONString();
            CCDMappingResultMessage ccdMessage = this.postJbBackWithCCDMappingResultMessage(esb_prod_intef_url, jsonObjectReq);
            ccdMessage.getResults().forEach(t -> {
                if (!ObjectUtils.isEmpty(t.getLotNo()))
                {
                    t.setDataFrom("MES");
                }
            });
            this.ccdMesMessageMappingResultRepository.saveAll(ccdMessage.getResults());
            String Code = ccdMessage.getCode();
            boolean Succ = Boolean.parseBoolean(ccdMessage.getSucc());
            String Message = ccdMessage.getMessage();
            responseParas = ccdMessage.toJSON();

            if (!Succ && !Code.equals("0000"))
            {
                errorMsg = esb_interf_des + "接口返回失败：" + Message;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //获取成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "GetStripInspectData sucess");
            return jbResult;
        }
        catch (Exception ex)
        {
            errorMsg = "GetStripInspectData error: " + ex.getMessage();
            log.error(errorMsg, ex);
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            if (ObjectUtils.isEmpty(responseParas))
            {
                responseParas = "{\"strMsg\":\"" + errorMsg + "\"}";
            }
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    private JSONObject autoRetryPostJbBack(String url, JSONObject jsonParas, int retryCount) throws Exception
    {
        try
        {
            retryCount++;
            return this.postJbBackJb(url, jsonParas);
        }
        catch (Exception ex)
        {
            // 判断是否是超时异常
            if (ex instanceof ResourceAccessException)
            {
                if (ex.getCause() instanceof SocketTimeoutException)
                {
                    if (retryCount >= 3)
                    {
                        throw ex;
                    }
                    Thread.sleep(60000);
                    // 超时异常，重试
                    return autoRetryPostJbBack(url, jsonParas, retryCount);
                }
            }
            throw ex;
        }
    }

    private CCDMappingResultMessage postJbBackWithCCDMappingResultMessage(String url, JSONObject jsonParas) throws Exception
    {
        HttpHeaders headers = new HttpHeaders();
        headers.setAcceptCharset(Collections.singletonList(Charset.defaultCharset()));
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<JSONObject> formEntity = new HttpEntity<>(jsonParas, headers);
        return this.restTemplate.postForObject(url, formEntity, CCDMappingResultMessage.class);
    }

    private JSONObject postJbBackJb(String url, JSONObject jsonParas) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<JSONObject> formEntity = new HttpEntity<>(jsonParas, headers);
        return this.restTemplate.postForObject(url, formEntity, JSONObject.class);
    }
}
