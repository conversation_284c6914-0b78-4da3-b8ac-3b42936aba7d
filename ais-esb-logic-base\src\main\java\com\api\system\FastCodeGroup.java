package com.api.system;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.api.base.IMybatisBasic;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 * 快速编码组
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_fastcode_group")
public class FastCodeGroup extends IMybatisBasic implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @JsonProperty("fastcode_group_id")
    @JSONField(name = "fastcode_group_id")
    @TableId(value = "fastcode_group_id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "组编码")
    @JsonProperty("fastcode_group_code")
    @JSONField(name = "fastcode_group_code")
    @TableField(value = "fastcode_group_code")
    private String code;

    @ApiModelProperty(value = "参数描述")
    @JsonProperty("fastcode_group_des")
    @JSONField(name = "fastcode_group_des")
    @TableField(value = "fastcode_group_des")
    private String desc;

    @ApiModelProperty(value = "子编码")
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @TableField(exist = false)
    private List<FastCode> children;

    public static FastCodeGroup byCode(String code, FastCodeGroupService service)
    {
        return service.getByCode(code);
    }

    public Map<String, String> getMapping()
    {
        if (this.getChildren() == null)
        {
            return new HashMap<>();
        }
        return this.getChildren().stream().
                collect(Collectors.toMap(FastCode::getCode, FastCode::getDesc));
    }
}
