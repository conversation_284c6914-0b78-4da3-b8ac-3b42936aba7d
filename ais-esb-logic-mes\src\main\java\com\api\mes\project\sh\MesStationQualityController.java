package com.api.mes.project.sh;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/mes/project/sh")
public class MesStationQualityController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    //过程质量数据查询
    @RequestMapping(value = "/SHqueryStationQualityList", method = {RequestMethod.POST, RequestMethod.GET})
    public String SHqueryStationQualityList(@RequestBody JSONObject jsonParas) throws Exception {
        String selectResult = "";
        String errorMsg = "";
        try {
            JSONArray item_date = jsonParas.getJSONArray("item_date");
            String prod_line_code = jsonParas.getString("prod_line_code");
            String station_code = jsonParas.getString("station_code");
            String make_order = jsonParas.getString("make_order");
            String small_model_type = jsonParas.getString("small_model_type");
            String quality_sign = jsonParas.getString("quality_sign");
            String up_flag = jsonParas.getString("up_flag");
            String serial_num = jsonParas.getString("serial_num");
            String enable_flag = jsonParas.getString("enable_flag");
            Integer tag_id = jsonParas.getInteger("tag_id");
            String quality_for = jsonParas.getString("quality_for");
            String tag_des = jsonParas.getString("tag_des");
            Integer tableSize = jsonParas.getInteger("tableSize");//每页条数
            Integer tablePage = jsonParas.getInteger("page");//第几页
            String pageDirect = jsonParas.getString("page_dirct");//翻页方向
            String pageId = jsonParas.getString("page_id");//page给与的ID
            String product_batch = jsonParas.getString("product_batch");//打刻码
            String print_barcode = jsonParas.getString("print_barcode");//条形码
            Query query = new Query();
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyyMMddHHmmss");
            if (item_date != null) {
                String startTime = sdf2.format(sdf1.parse(item_date.get(0).toString()));
                String endTime = sdf2.format(sdf1.parse(item_date.get(1).toString()));
                query.addCriteria(Criteria.where("item_date_val").gte(Long.parseLong(startTime)).lte(Long.parseLong(endTime)));
            }
            if (prod_line_code != null && !prod_line_code.equals("")) {
                query.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            }
            if (station_code != null && !station_code.equals("")) {
                query.addCriteria(Criteria.where("station_code").is(station_code));
            }
            if (quality_sign != null && !quality_sign.equals("")) {
                query.addCriteria(Criteria.where("quality_sign").is(quality_sign));
            }
            if (up_flag != null && !up_flag.equals("")) {
                query.addCriteria(Criteria.where("up_flag").is(up_flag));
            }
            if (make_order != null && !make_order.equals("")) {
                query.addCriteria(Criteria.where("make_order").is(make_order));
            }
            if (small_model_type != null && !small_model_type.equals("")) {
                query.addCriteria(Criteria.where("small_model_type").is(small_model_type));
            }
            if (enable_flag != null && !enable_flag.equals("")) {
                query.addCriteria(Criteria.where("enable_flag").is(enable_flag));
            }
            //打刻码
            if (product_batch != null && !product_batch.equals("")) {
                query.addCriteria(Criteria.where("product_batch").is(product_batch));
            }
            //条形码
            if (print_barcode != null && !print_barcode.equals("")) {
                query.addCriteria(Criteria.where("print_barcode").regex(print_barcode));
            }
            if (serial_num != null && !serial_num.equals("")) {
                Query querySH = new Query();
                querySH.addCriteria(Criteria.where("serial_num").regex(serial_num));
                MongoCursor<Map> iteratorSH = mongoTemplate.getCollection("c_mes_me_station_material").find(querySH.getQueryObject(), Map.class).sort(querySH.getSortObject()).noCursorTimeout(true).batchSize(1000).iterator();
                String exact_barcode_num_list = "";
                while (iteratorSH.hasNext()) {
                    Map map = iteratorSH.next();
                    map.put("id", map.get("_id").toString());
                    if(map.get("exact_barcode").toString() == null || map.get("exact_barcode").toString().equals("")) continue;
                    String exact_barcode_num=map.get("exact_barcode").toString();
                    if (!exact_barcode_num_list.contains(exact_barcode_num + ",")) {
                        exact_barcode_num_list += exact_barcode_num + ",";
                    }
                }
                exact_barcode_num_list += serial_num;
                query.addCriteria(Criteria.where("serial_num").in(exact_barcode_num_list.split(",")));
            }
            query.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iterator = mongoTemplate.getCollection("c_mes_me_station_flow").find(query.getQueryObject()).sort(query.getSortObject()).noCursorTimeout(true).batchSize(1000).iterator();
            String station_flow_id_list = "";
            while (iterator.hasNext()) {
                Document docItem = iterator.next();
                String station_flow_id = docItem.getString("station_flow_id").toString();
                if (!station_flow_id_list.contains(station_flow_id + ",")) {
                    station_flow_id_list += station_flow_id + ",";
                }
            }

            List<Map<String, Object>> itemList = new ArrayList<>();
            if(station_flow_id_list.equals("")){
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            if (iterator.hasNext()) iterator.close();
            Query query2 = new Query();
            query2.addCriteria(Criteria.where("station_flow_id").in(station_flow_id_list.split(",")));
            if (tag_id != null) {
                query2.addCriteria(Criteria.where("tag_id").is(tag_id));
            }
            if (quality_for != null) {
                query2.addCriteria(Criteria.where("quality_for").is(quality_for));
            }
            if (tag_des != null) {
                query2.addCriteria(Criteria.where("tag_des").is(tag_des));
            }
            long count = mongoTemplate.count(query2, Document.class, "c_mes_me_station_quality");
            //PageRequest page = PageRequest.of(tablePage - 1, tableSize);
            //query2.with(page);
            List<Document> documentList = mongoTemplate.find(query2, Document.class, "c_mes_me_station_quality");
            String stationFlowIdContrast = "";
            Document stationList = null;
            for (Document qualityDoc : documentList) {
                Map<String, Object> integratedDoc = new HashMap<>();
                String stationFlowId = qualityDoc.getString("station_flow_id");
                if(!stationFlowIdContrast.equals(stationFlowId)){ //同一个station_flow_id只查询一次
                    stationFlowIdContrast = stationFlowId;
                    Query stationFlowQuery = new Query();
                    stationFlowQuery.addCriteria(Criteria.where("station_flow_id").is(stationFlowIdContrast));
                    List<Document> list = mongoTemplate.find(stationFlowQuery, Document.class, "c_mes_me_station_flow");
                    stationList  = list.get(0);
                }
                integratedDoc.put("id", stationList!= null ? stationList.getObjectId("_id").toString() : null);
                integratedDoc.put("item_date", stationList !=null ? sdf1.format(stationList.get("item_date")) : null);
                integratedDoc.put("station_des", stationList !=null ? stationList.getString("station_des") : null);
                integratedDoc.put("quality_sign", stationList !=null ? stationList.getString("quality_sign") : null);
                integratedDoc.put("make_order", stationList !=null ? stationList.getString("make_order") : null);
                integratedDoc.put("small_model_type", stationList !=null ? stationList.getString("small_model_type") : null);
                integratedDoc.put("container_num", stationList !=null ? stationList.getString("container_num") : null);
                integratedDoc.put("staff_id", stationList !=null ? stationList.getString("staff_id") : null);
                integratedDoc.put("product_batch", stationList!=null ? stationList.getString("product_batch") : null);
                integratedDoc.put("up_flag", stationList!=null ? stationList.getString("up_flag") : null);
                integratedDoc.put("up_ng_code", stationList!=null ? stationList.getInteger("up_ng_code") : null);
                integratedDoc.put("up_ng_msg", stationList!=null ? stationList.getString("up_ng_msg") : null);
                integratedDoc.put("enable_flag", stationList!=null ? stationList.getString("enable_flag") : null);
                integratedDoc.put("print_barcode", stationList !=null ? stationList.getString("print_barcode") : null);
                integratedDoc.put("prod_line_code", qualityDoc.getString("prod_line_code"));
                integratedDoc.put("station_code", qualityDoc.getString("station_code"));
                integratedDoc.put("serial_num", qualityDoc.getString("serial_num"));
                integratedDoc.put("group_order", qualityDoc.getInteger("group_order"));
                integratedDoc.put("tag_col_order", qualityDoc.getInteger("tag_col_order"));
                integratedDoc.put("quality_for", qualityDoc.getString("quality_for"));
                integratedDoc.put("tag_des", qualityDoc.getString("tag_des"));
                integratedDoc.put("tag_value", qualityDoc.getString("tag_value"));
                integratedDoc.put("quality_d_sign", qualityDoc.getString("quality_d_sign"));
                integratedDoc.put("tag_uom", qualityDoc.getString("tag_uom"));
                integratedDoc.put("theory_value", qualityDoc.getString("theory_value"));
                integratedDoc.put("upper_limit", qualityDoc.getDouble("upper_limit"));
                integratedDoc.put("down_limit", qualityDoc.getDouble("down_limit"));
                integratedDoc.put("bolt_code", qualityDoc.getInteger("bolt_code"));
                integratedDoc.put("progm_num", qualityDoc.getInteger("progm_num"));
                integratedDoc.put("quality_trace_id", qualityDoc.getString("quality_trace_id"));
                itemList.add(integratedDoc);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", count);

        } catch (Exception ex) {
            errorMsg = "查询过站质量数据发生异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);

        }
        return selectResult;
    }
}
