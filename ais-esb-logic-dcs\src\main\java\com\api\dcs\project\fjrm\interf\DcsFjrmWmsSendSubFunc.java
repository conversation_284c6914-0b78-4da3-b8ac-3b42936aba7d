package com.api.dcs.project.fjrm.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.project.shzy.interf.DcsShzyWmsInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * WMS流程接口(Sub)公共方法
 * 1.通知MES库存变化
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
@Slf4j
public class DcsFjrmWmsSendSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsFjrmInterfCommon dcsFjrmInterfCommon;

    //1.通知MES库存变化
    public JSONObject SendMseStockChange(String task_num,String status,String statusMsg) throws Exception {
        JSONObject jbResult = new JSONObject();
        Integer code = 0;
        String errorMsg = "";
        String esbInterfCode = "SendZkFeedFinish";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            postParas.put("feedTaskNo",task_num);
            postParas.put("status",status);
            postParas.put("desc",statusMsg);
            requestParas = postParas.toString();
            jbResult.put("requestParas", requestParas);

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = dcsFjrmInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            jbResult.put("responseParas", responseParas);

            //4.判断数据
            if (jsonObjectBack == null || !jsonObjectBack.containsKey("code") || !jsonObjectBack.containsKey("message")) {
                errorMsg = "接受报文为空或者不包含code|message字段";
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            code = jsonObjectBack.getInteger("code");
            String msg = jsonObjectBack.getString("message");
            if (code != 200) {
                jbResult.put("successFlag", false);
                jbResult.put("message", msg);
                return jbResult;
            }

            //成功
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex;
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
