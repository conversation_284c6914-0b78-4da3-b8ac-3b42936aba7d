package com.api.pack.core.ccd;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.pack.core.board.BoardConst;
import com.api.pack.core.board.PCSBoard;
import com.api.pack.core.board.SETBoard;
import com.api.pack.core.sort.SortConst;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CCDScanMessage
{
    private String transmitId;

    private SETBoard origin;

    private SETBoard front;

    private SETBoard back;

    private JSONObject plan;

    private JSONObject recipe;

    private JSONObject sort;

    public CCDScanMessage(String userName, String setBoardId, JSONObject plan, JSONObject recipe, JSONObject sort, JSONObject ccd)
    {
        this.plan = plan;
        this.recipe = recipe;
        this.sort = sort;

        Double boardLength = plan.getDouble("m_length"); // 板长(mm)
        Double boardWidth = plan.getDouble("m_width"); // 板宽(mm)
        Double boardThickness = plan.getDouble("m_tickness"); // 板厚(mm)
        Double boardWeight = plan.getDouble("m_weight"); // 板重(g)

        String lotNum = String.valueOf(plan.get("lot_num")); // 工单号/批号
        String taskType = String.valueOf(plan.get("task_type")); // 任务类型
        String cyclePeriod = String.valueOf(plan.get("cycle_period")); // 周期
        String partNumber = String.valueOf(plan.get("model_type")); // 料号
        String partVersion = String.valueOf(plan.get("model_version")); // 版本
        String batchNo = String.valueOf(plan.get("batch_no")); // 批号
        String laserBatchNo = String.valueOf(plan.get("laser_batch_no")); // 镭射批号
        String typesettingNo = String.valueOf(plan.get("typesetting_no")); // 排版数
        String customerMn = String.valueOf(plan.get("customer_mn")); // 客户料号
        String ulCode = String.valueOf(plan.get("ul_code")); // UL号

        SETBoard origin = new SETBoard();
        origin.setBoardId(setBoardId);
        origin.setBoardBarcode(BoardConst.NO_READ);
        origin.setBoardLevel(BoardConst.BLANK);
        origin.setBoardLength(boardLength);
        origin.setBoardWidth(boardWidth);
        origin.setBoardThickness(boardThickness);
        origin.setBoardWeight(boardWeight);
        origin.setBoardStatus(BoardConst.OK);
        origin.setBoardTurn(BoardConst.TURN_2);
        origin.setBoardLayoutNumber(0);
        origin.setBoardMark(BoardConst.BLANK);
        origin.setBoardResult(BoardConst.RESULT_OK);

        origin.setUserName(userName);
        origin.setLotNum(lotNum);
        origin.setTaskType(taskType);
        origin.setCyclePeriod(cyclePeriod);
        origin.setPartNumber(partNumber);
        origin.setPartVersion(partVersion);
        origin.setBatchNo(batchNo);
        origin.setLaserBatchNo(laserBatchNo);
        origin.setTypesettingNo(typesettingNo);
        origin.setCustomerMn(customerMn);
        origin.setUlCode(ulCode);
        origin.setXoutFlag(BoardConst.FLAG_N);
        origin.setXoutSetNumber(0);
        origin.setXoutActualNumber(0);

        origin.setPileBarcode(BoardConst.BLANK);
        origin.setPileUseFlag(BoardConst.FLAG_N);
        origin.setBoardNgCode(BoardConst.CODE_OK);
        origin.setBoardNgMsg(BoardConst.BLANK);
        origin.setUpFlag(BoardConst.FLAG_N);
        origin.setUpNgCode(BoardConst.CODE_OK);
        origin.setUpNgMsg(BoardConst.BLANK);
        origin.setUnbindFlag(BoardConst.FLAG_N);
        origin.setUnbindUser(BoardConst.BLANK);
        origin.setUnbindTime(BoardConst.BLANK);
        origin.setUnbindWay(BoardConst.BLANK);
        origin.setDepositPosition(BoardConst.DEPOSIT_POSITION_OK);

        JSONArray content = ccd.getJSONArray(BoardConst.CCD_CONTENT);
        if (content != null && content.size() == 1)
        {
            JSONObject data = content.getJSONObject(0);
            if (data != null)
            {
                String transmitId = data.getString(BoardConst.CCD_CONTENT_TRANSMIT_ID);
                this.transmitId = transmitId;
                origin.setBoardSn(transmitId);
                String multiAspectFront = BoardConst.BLANK; // 多面板正面
                String multiAspectBack = BoardConst.BLANK; // 多面板背面
                String setBoardType = recipe.getString("array_type"); // SET板类型，如：单面板、双面板、无码板
                Integer setBoardIndex = plan.getInteger("finish_ok_count") + 1; // 板序号
                String pcsBoardType = recipe.getString("bd_type"); // PCS板类型，如：单面板、双面板、无码板
                JSONObject frontPanel = data.getJSONObject(BoardConst.SET_FRONT_CONTENT); // 正面
                JSONObject backPanel = data.getJSONObject(BoardConst.SET_BACK_CONTENT); // 背面
                Map<String, SETBoard> multiAspect = new HashMap<>();
                SETBoard front = null;
                SETBoard back = null;
                List<PCSBoard> childrenBoards = new LinkedList<>();
                if (frontPanel != null)
                {
                    multiAspectFront = JSON.toJSONString(frontPanel);
                    front = new SETBoard(BoardConst.ORIENT_FRONT, frontPanel);
                    multiAspect.put(BoardConst.ORIENT_FRONT, front);
                    childrenBoards.addAll(front.getChildrenBoards());
                    this.setFront(front);
                }
                if (backPanel != null)
                {
                    multiAspectBack = JSON.toJSONString(backPanel);
                    back = new SETBoard(BoardConst.ORIENT_BACK, backPanel);
                    multiAspect.put(BoardConst.ORIENT_BACK, back);
                    childrenBoards.addAll(back.getChildrenBoards());
                    this.setBack(back);
                }
                origin.setChildrenBoards(childrenBoards);
                origin.setBoardType(setBoardType);
                origin.setBoardIndex(setBoardIndex);
                origin.setMultiAspectFront(multiAspectFront);
                origin.setMultiAspectBack(multiAspectBack);
                origin.setMultiAspect(multiAspect);
                origin.getChildrenBoards().forEach(board -> board.setBoardType(pcsBoardType));

                try
                {
                    String[] setProps = BoardConst.SET_BOARD_INITIAL_PROPERTIES;
                    boolean useFront = BoardConst.SINGLE.equals(setBoardType) && front != null;
                    boolean useBack = BoardConst.SINGLE.equals(setBoardType) && back != null;
                    if (useFront)
                    {
                        this.copyProperties(front, origin, setProps);
                    }
                    else if (useBack)
                    {
                        this.copyProperties(back, origin, setProps);
                    }
                    else
                    {
                        this.copyProperties(front == null ? (back != null ? back : new SETBoard()) : front, origin, setProps);
                    }
                    origin.setBoardBarcode(SortConst.convertValue(origin.getBoardBarcode()));
                }
                catch (FatalBeanException ex)
                {
                    ex.printStackTrace();
                }
            }
        }
        this.origin = origin;
    }

    public boolean isReady()
    {
        return this.origin != null && this.origin.getMultiAspect() != null;
    }

    private void copyProperties(Object source, Object target, String[] properties)
    {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");
        for (String property : properties)
        {
            PropertyDescriptor sourceProperty = BeanUtils.getPropertyDescriptor(source.getClass(), property);
            PropertyDescriptor targetProperty = BeanUtils.getPropertyDescriptor(target.getClass(), property);
            if (sourceProperty != null && targetProperty != null)
            {
                Method writeMethod = targetProperty.getWriteMethod();
                Method readMethod = sourceProperty.getReadMethod();
                if (readMethod != null && ClassUtils.isAssignable(writeMethod.getParameterTypes()[0], readMethod.getReturnType()))
                {
                    try
                    {
                        if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers()))
                        {
                            readMethod.setAccessible(true);
                        }
                        Object value = readMethod.invoke(source);
                        if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers()))
                        {
                            writeMethod.setAccessible(true);
                        }
                        writeMethod.invoke(target, value);
                    }
                    catch (Throwable ex)
                    {
                        throw new FatalBeanException("Could not copy property '" + property + "' from source to target", ex);
                    }
                }
            }
        }
    }
}
