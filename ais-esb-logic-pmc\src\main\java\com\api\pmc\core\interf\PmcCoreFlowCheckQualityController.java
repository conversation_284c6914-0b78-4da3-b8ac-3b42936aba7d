package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 检测接口
 * 1.储存OBD设备检测数据
 * 2.储存外廓尺寸测量设备数据
 * 3.储存速度校验数据
 * 4.储存轮重-整备质量数据
 * 5.储存地泵数据
 * 6.储存制动数据
 * 7.储存大灯检测仪数据
 * 8.储存声级数据
 * 9.储存侧滑数据
 * 10.储存整体检测数据
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreFlowCheckQualityController {
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.储存OBD设备检测数据
    @RequestMapping(value = "/PmcCoreInterfObdQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfObdQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfObdQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_obd";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String calid="";//CALID
                String cvn="";//CVN
                String testresult="";//检测结果
                String failreason="";//不合格原因
                String testtime="";//检测日期
                String calid1="";//CALID1
                String cvn1="";//CVN1
                String calid2="";//CALID2
                String cvn2="";//CVN2
                String obdtype="";//OBD类型
                String engineid="";//EngineID
                String rearid="";//RearID
                String otherid="";//OtherID
                String connectresult="";//通讯是否成功
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    calid=jbItem.getString("calid");
                    cvn=jbItem.getString("cvn");
                    testresult=jbItem.getString("testresult");
                    failreason=jbItem.getString("failreason");
                    testtime=jbItem.getString("testtime");
                    calid1=jbItem.getString("calid1");
                    cvn1=jbItem.getString("cvn1");
                    calid2=jbItem.getString("calid2");
                    cvn2=jbItem.getString("cvn2");
                    obdtype=jbItem.getString("obdtype");
                    engineid=jbItem.getString("engineid");
                    rearid=jbItem.getString("rearid");
                    otherid=jbItem.getString("otherid");
                    connectresult=jbItem.getString("connectresult");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("calid",calid);
                    mapDataItem.put("cvn",cvn);
                    mapDataItem.put("testresult",testresult);
                    mapDataItem.put("failreason",failreason);
                    mapDataItem.put("testtime",testtime);
                    mapDataItem.put("calid1",calid1);
                    mapDataItem.put("cvn1",cvn1);
                    mapDataItem.put("calid2",calid2);
                    mapDataItem.put("cvn2",cvn2);
                    mapDataItem.put("obdtype",obdtype);
                    mapDataItem.put("engineid",engineid);
                    mapDataItem.put("rearid",rearid);
                    mapDataItem.put("otherid",otherid);
                    mapDataItem.put("connectresult",connectresult);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存OBD设备检测数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //2.储存外廓尺寸测量设备数据
    @RequestMapping(value = "/PmcCoreInterfWlccQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfWlccQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfWlccQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_wlcc";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String outlength="";//外廓长
                String outwidth="";//外廓宽
                String outhigh="";//外廓高
                String wai_judge="";//外廓尺寸判定
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    outlength=jbItem.getString("outlength");
                    outwidth=jbItem.getString("outwidth");
                    outhigh=jbItem.getString("outhigh");
                    wai_judge=jbItem.getString("wai_judge");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("outlength",outlength);
                    mapDataItem.put("outwidth",outwidth);
                    mapDataItem.put("outhigh",outhigh);
                    mapDataItem.put("wai_judge",wai_judge);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存外廓尺寸测量设备数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //3.储存速度校验数据
    @RequestMapping(value = "/PmcCoreInterfSdQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfSdQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfSdQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_sd";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String speed_res="";//车速表_实测值（km/h）_检测数据
                String speed_judge="";//车速表_检测结果
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    speed_res=jbItem.getString("speed_res");
                    speed_judge=jbItem.getString("speed_judge");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("speed_res",speed_res);
                    mapDataItem.put("speed_judge",speed_judge);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存速度校验数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //4.储存轮重-整备质量数据
    @RequestMapping(value = "/PmcCoreInterfLzQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfLzQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfLzQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_lz";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String aw_1l_res="";//一轴左重
                String aw_1r_res="";//一轴右重
                String aw_1_res="";//一轴重
                String aw_2l_res="";//二轴左重
                String aw_2r_res="";//二轴右重
                String aw_2_res="";//二轴重
                String aw_3l_res="";//三轴左重
                String aw_3r_res="";//三轴右重
                String aw_3_res="";//三轴重
                String aw_4l_res="";//四轴左重
                String aw_4r_res="";//四轴右重
                String aw_4_res="";//四轴重
                String aw_t_res="";//整车重
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    aw_1l_res=jbItem.getString("aw_1l_res");
                    aw_1r_res=jbItem.getString("aw_1r_res");
                    aw_1_res=jbItem.getString("aw_1_res");
                    aw_2l_res=jbItem.getString("aw_2l_res");
                    aw_2r_res=jbItem.getString("aw_2r_res");
                    aw_2_res=jbItem.getString("aw_2_res");
                    aw_3l_res=jbItem.getString("aw_3l_res");
                    aw_3r_res=jbItem.getString("aw_3r_res");
                    aw_3_res=jbItem.getString("aw_3_res");
                    aw_4l_res=jbItem.getString("aw_4l_res");
                    aw_4r_res=jbItem.getString("aw_4r_res");
                    aw_4_res=jbItem.getString("aw_4_res");
                    aw_t_res=jbItem.getString("aw_t_res");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("aw_1l_res",aw_1l_res);
                    mapDataItem.put("aw_1r_res",aw_1r_res);
                    mapDataItem.put("aw_1_res",aw_1_res);
                    mapDataItem.put("aw_2l_res",aw_2l_res);
                    mapDataItem.put("aw_2r_res",aw_2r_res);
                    mapDataItem.put("aw_2_res",aw_2_res);
                    mapDataItem.put("aw_3l_res",aw_3l_res);
                    mapDataItem.put("aw_3r_res",aw_3r_res);
                    mapDataItem.put("aw_3_res",aw_3_res);
                    mapDataItem.put("aw_4l_res",aw_4l_res);
                    mapDataItem.put("aw_4r_res",aw_4r_res);
                    mapDataItem.put("aw_4_res",aw_4_res);
                    mapDataItem.put("aw_t_res",aw_t_res);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存轮重-整备质量数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //5.储存地泵数据
    @RequestMapping(value = "/PmcCoreInterfDbQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfDbQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfDbQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_db";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String b_zbzl_res="";//整备质量检测数据
                String b_zbzl_judge="";//整备质量判定
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    b_zbzl_res=jbItem.getString("b_zbzl_res");
                    b_zbzl_judge=jbItem.getString("b_zbzl_judge");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("b_zbzl_res",b_zbzl_res);
                    mapDataItem.put("b_zbzl_judge",b_zbzl_judge);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存地泵数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //6.储存制动数据
    @RequestMapping(value = "/PmcCoreInterfZdQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfZdQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfZdQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_zd";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String b_max_1l_res="";//制动_一轴_左制动_检测数据
                String b_max_1r_res="";//制动_一轴_右制动_检测数据
                String b_sum_1_res="";//制动_一轴_和_检测数据
                String b_dif_1l_res="";//制动_一轴_左平衡_检测数据
                String b_dif_1r_res="";//制动_一轴_右平衡_检测数据
                String b_dif_1_res="";//制动_一轴_差_检测数据
                String b_drg_1l_res="";//制动_一轴_左阻滞_检测数据
                String b_drg_1r_res="";//制动_一轴_右阻滞_检测数据
                String b_max_2l_res="";//制动_二轴_左制动_检测数据
                String b_max_2r_res="";//制动_二轴_右制动_检测数据
                String b_sum_2_res="";//制动_二轴_和_检测数据
                String b_dif_2l_res="";//制动_二轴_左平衡_检测数据
                String b_dif_2r_res="";//制动_二轴_右平衡_检测数据
                String b_dif_2_res="";//制动_二轴_差_检测数据
                String b_drg_2l_res="";//制动_二轴_左阻滞_检测数据
                String b_drg_2r_res="";//制动_二轴_右阻滞_检测数据
                String b_max_3l_res="";//制动_三轴_左制动_检测数据
                String b_max_3r_res="";//制动_三轴_右制动_检测数据
                String b_sum_3_res="";//制动_三轴_和_检测数据
                String b_dif_3l_res="";//制动_三轴_左平衡_检测数据
                String b_dif_3r_res="";//制动_三轴_右平衡_检测数据
                String b_dif_3_res="";//制动_三轴_差_检测数据
                String b_drg_3l_res="";//制动_三轴_左阻滞_检测数据
                String b_drg_3r_res="";//制动_三轴_右阻滞_检测数据
                String b_max_4l_res="";//制动_四轴_左制动_检测数据
                String b_max_4r_res="";//制动_四轴_右制动_检测数据
                String b_sum_4_res="";//制动_四轴_和_检测数据
                String b_dif_4l_res="";//制动_四轴_左平衡_检测数据
                String b_dif_4r_res="";//制动_四轴_右平衡_检测数据
                String b_dif_4_res="";//制动_四轴_差_检测数据
                String b_drg_4l_res="";//制动_四轴_左阻滞_检测数据
                String b_drg_4r_res="";//制动_四轴_右阻滞_检测数据
                String b_park_l_res="";//制动_手刹_左驻车_检测数据
                String b_park_r_res="";//制动_手刹_右驻车_检测数据
                String b_park_t_res="";//制动_手刹_手刹力_检测结果
                String b_sum_t_res="";//制动_整车_和_检测数据
                String b_sum_1_judge="";//制动_一轴_和_检测结果
                String b_dif_1_judge="";//制动_一轴_差_检测结果
                String b_drg_1l_judge="";//制动_一轴_左阻滞_检测结果
                String b_drg_1r_judge="";//制动_一轴_右阻滞_检测结果
                String b_sum_2_judge="";//制动_二轴_和_检测结果
                String b_dif_2_judge="";//制动_二轴_差_检测结果
                String b_drg_2l_judge="";//制动_二轴_左阻滞_检测结果
                String b_drg_2r_judge="";//制动_二轴_右阻滞_检测结果
                String b_sum_3_judge="";//制动_三轴_和_检测结果
                String b_dif_3_judge="";//制动_三轴_差_检测结果
                String b_drg_3l_judge="";//制动_三轴_左阻滞_检测结果
                String b_drg_3r_judge="";//制动_三轴_右阻滞_检测结果
                String b_sum_4_judge="";//制动_四轴_和_检测结果
                String b_dif_4_judge="";//制动_四轴_差_检测结果
                String b_drg_4l_judge="";//制动_四轴_左阻滞_检测结果
                String b_drg_4r_judge="";//制动_四轴_右阻滞_检测结果
                String b_park_judge="";//制动_手刹_驻车_检测结果
                String b_sum_t_judge="";//制动_整车_和_检测结果
                String b_t_judge="";//制动_检测结果
                String abs_f_judge="";//ABS_一轴检测结果
                String abs_r_judge="";//ABS_二轴检测结果
                String abs_m_judge="";//ABS_三轴检测结果
                String abs_4_judge="";//ABS_四轴检测结果
                String abs_judge="";//ABS检测结果
                String aw_2ls_res="";//制动_加载_二轴左重
                String aw_2rs_res="";//制动_加载_二轴右重
                String aw_3ls_res="";//制动_加载_三轴左重
                String aw_3rs_res="";//制动_加载_三轴右重
                String b_max_2ls_res="";//制动_加载_二轴_左制动_检测数据
                String b_max_2rs_res="";//制动_加载_二轴_右制动_检测数据
                String b_sum_2s_res="";//制动_加载_二轴_和_检测数据
                String b_sum_2s_judge="";//制动_加载_二轴_和_检测结果
                String b_dif_2ls_res="";//制动_加载_二轴_左平衡_检测数据
                String b_dif_2rs_res="";//制动_加载_二轴_右平衡_检测数据
                String b_dif_2s_res="";//制动_加载_二轴_差_检测数据
                String b_dif_2s_judge="";//制动_加载_二轴_差_检测结果
                String b_max_3ls_res="";//制动_加载_三轴_左制动_检测数据
                String b_max_3rs_res="";//制动_加载_三轴_右制动_检测数据
                String b_sum_3s_res="";//制动_加载_三轴_和_检测数据
                String b_sum_3s_judge="";//制动_加载_三轴_和_检测结果
                String b_dif_3ls_res="";//制动_加载_三轴_左平衡_检测数据
                String b_dif_3rs_res="";//制动_加载_三轴_右平衡_检测数据
                String b_dif_3s_res="";//制动_加载_三轴_差_检测数据
                String b_dif_3s_judge="";//制动_加载_三轴_差_检测结果
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    b_max_1l_res=jbItem.getString("b_max_1l_res");
                    b_max_1r_res=jbItem.getString("b_max_1r_res");
                    b_sum_1_res=jbItem.getString("b_sum_1_res");
                    b_dif_1l_res=jbItem.getString("b_dif_1l_res");
                    b_dif_1r_res=jbItem.getString("b_dif_1r_res");
                    b_dif_1_res=jbItem.getString("b_dif_1_res");
                    b_drg_1l_res=jbItem.getString("b_drg_1l_res");
                    b_drg_1r_res=jbItem.getString("b_drg_1r_res");
                    b_max_2l_res=jbItem.getString("b_max_2l_res");
                    b_max_2r_res=jbItem.getString("b_max_2r_res");
                    b_sum_2_res=jbItem.getString("b_sum_2_res");
                    b_dif_2l_res=jbItem.getString("b_dif_2l_res");
                    b_dif_2r_res=jbItem.getString("b_dif_2r_res");
                    b_dif_2_res=jbItem.getString("b_dif_2_res");
                    b_drg_2l_res=jbItem.getString("b_drg_2l_res");
                    b_drg_2r_res=jbItem.getString("b_drg_2r_res");
                    b_max_3l_res=jbItem.getString("b_max_3l_res");
                    b_max_3r_res=jbItem.getString("b_max_3r_res");
                    b_sum_3_res=jbItem.getString("b_sum_3_res");
                    b_dif_3l_res=jbItem.getString("b_dif_3l_res");
                    b_dif_3r_res=jbItem.getString("b_dif_3r_res");
                    b_dif_3_res=jbItem.getString("b_dif_3_res");
                    b_drg_3l_res=jbItem.getString("b_drg_3l_res");
                    b_drg_3r_res=jbItem.getString("b_drg_3r_res");
                    b_max_4l_res=jbItem.getString("b_max_4l_res");
                    b_max_4r_res=jbItem.getString("b_max_4r_res");
                    b_sum_4_res=jbItem.getString("b_sum_4_res");
                    b_dif_4l_res=jbItem.getString("b_dif_4l_res");
                    b_dif_4r_res=jbItem.getString("b_dif_4r_res");
                    b_dif_4_res=jbItem.getString("b_dif_4_res");
                    b_drg_4l_res=jbItem.getString("b_drg_4l_res");
                    b_drg_4r_res=jbItem.getString("b_drg_4r_res");
                    b_park_l_res=jbItem.getString("b_park_l_res");
                    b_park_r_res=jbItem.getString("b_park_r_res");
                    b_park_t_res=jbItem.getString("b_park_t_res");
                    b_sum_t_res=jbItem.getString("b_sum_t_res");
                    b_sum_1_judge=jbItem.getString("b_sum_1_judge");
                    b_dif_1_judge=jbItem.getString("b_dif_1_judge");
                    b_drg_1l_judge=jbItem.getString("b_drg_1l_judge");
                    b_drg_1r_judge=jbItem.getString("b_drg_1r_judge");
                    b_sum_2_judge=jbItem.getString("b_sum_2_judge");
                    b_dif_2_judge=jbItem.getString("b_dif_2_judge");
                    b_drg_2l_judge=jbItem.getString("b_drg_2l_judge");
                    b_drg_2r_judge=jbItem.getString("b_drg_2r_judge");
                    b_sum_3_judge=jbItem.getString("b_sum_3_judge");
                    b_dif_3_judge=jbItem.getString("b_dif_3_judge");
                    b_drg_3l_judge=jbItem.getString("b_drg_3l_judge");
                    b_drg_3r_judge=jbItem.getString("b_drg_3r_judge");
                    b_sum_4_judge=jbItem.getString("b_sum_4_judge");
                    b_dif_4_judge=jbItem.getString("b_dif_4_judge");
                    b_drg_4l_judge=jbItem.getString("b_drg_4l_judge");
                    b_drg_4r_judge=jbItem.getString("b_drg_4r_judge");
                    b_park_judge=jbItem.getString("b_park_judge");
                    b_sum_t_judge=jbItem.getString("b_sum_t_judge");
                    b_t_judge=jbItem.getString("b_t_judge");
                    abs_f_judge=jbItem.getString("abs_f_judge");
                    abs_r_judge=jbItem.getString("abs_r_judge");
                    abs_m_judge=jbItem.getString("abs_m_judge");
                    abs_4_judge=jbItem.getString("abs_4_judge");
                    abs_judge=jbItem.getString("abs_judge");
                    aw_2ls_res=jbItem.getString("aw_2ls_res");
                    aw_2rs_res=jbItem.getString("aw_2rs_res");
                    aw_3ls_res=jbItem.getString("aw_3ls_res");
                    aw_3rs_res=jbItem.getString("aw_3rs_res");
                    b_max_2ls_res=jbItem.getString("b_max_2ls_res");
                    b_max_2rs_res=jbItem.getString("b_max_2rs_res");
                    b_sum_2s_res=jbItem.getString("b_sum_2s_res");
                    b_sum_2s_judge=jbItem.getString("b_sum_2s_judge");
                    b_dif_2ls_res=jbItem.getString("b_dif_2ls_res");
                    b_dif_2rs_res=jbItem.getString("b_dif_2rs_res");
                    b_dif_2s_res=jbItem.getString("b_dif_2s_res");
                    b_dif_2s_judge=jbItem.getString("b_dif_2s_judge");
                    b_max_3ls_res=jbItem.getString("b_max_3ls_res");
                    b_max_3rs_res=jbItem.getString("b_max_3rs_res");
                    b_sum_3s_res=jbItem.getString("b_sum_3s_res");
                    b_sum_3s_judge=jbItem.getString("b_sum_3s_judge");
                    b_dif_3ls_res=jbItem.getString("b_dif_3ls_res");
                    b_dif_3rs_res=jbItem.getString("b_dif_3rs_res");
                    b_dif_3s_res=jbItem.getString("b_dif_3s_res");
                    b_dif_3s_judge=jbItem.getString("b_dif_3s_judge");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("b_max_1l_res",b_max_1l_res);
                    mapDataItem.put("b_max_1r_res",b_max_1r_res);
                    mapDataItem.put("b_sum_1_res",b_sum_1_res);
                    mapDataItem.put("b_dif_1l_res",b_dif_1l_res);
                    mapDataItem.put("b_dif_1r_res",b_dif_1r_res);
                    mapDataItem.put("b_dif_1_res",b_dif_1_res);
                    mapDataItem.put("b_drg_1l_res",b_drg_1l_res);
                    mapDataItem.put("b_drg_1r_res",b_drg_1r_res);
                    mapDataItem.put("b_max_2l_res",b_max_2l_res);
                    mapDataItem.put("b_max_2r_res",b_max_2r_res);
                    mapDataItem.put("b_sum_2_res",b_sum_2_res);
                    mapDataItem.put("b_dif_2l_res",b_dif_2l_res);
                    mapDataItem.put("b_dif_2r_res",b_dif_2r_res);
                    mapDataItem.put("b_dif_2_res",b_dif_2_res);
                    mapDataItem.put("b_drg_2l_res",b_drg_2l_res);
                    mapDataItem.put("b_drg_2r_res",b_drg_2r_res);
                    mapDataItem.put("b_max_3l_res",b_max_3l_res);
                    mapDataItem.put("b_max_3r_res",b_max_3r_res);
                    mapDataItem.put("b_sum_3_res",b_sum_3_res);
                    mapDataItem.put("b_dif_3l_res",b_dif_3l_res);
                    mapDataItem.put("b_dif_3r_res",b_dif_3r_res);
                    mapDataItem.put("b_dif_3_res",b_dif_3_res);
                    mapDataItem.put("b_drg_3l_res",b_drg_3l_res);
                    mapDataItem.put("b_drg_3r_res",b_drg_3r_res);
                    mapDataItem.put("b_max_4l_res",b_max_4l_res);
                    mapDataItem.put("b_max_4r_res",b_max_4r_res);
                    mapDataItem.put("b_sum_4_res",b_sum_4_res);
                    mapDataItem.put("b_dif_4l_res",b_dif_4l_res);
                    mapDataItem.put("b_dif_4r_res",b_dif_4r_res);
                    mapDataItem.put("b_dif_4_res",b_dif_4_res);
                    mapDataItem.put("b_drg_4l_res",b_drg_4l_res);
                    mapDataItem.put("b_drg_4r_res",b_drg_4r_res);
                    mapDataItem.put("b_park_l_res",b_park_l_res);
                    mapDataItem.put("b_park_r_res",b_park_r_res);
                    mapDataItem.put("b_park_t_res",b_park_t_res);
                    mapDataItem.put("b_sum_t_res",b_sum_t_res);
                    mapDataItem.put("b_sum_1_judge",b_sum_1_judge);
                    mapDataItem.put("b_dif_1_judge",b_dif_1_judge);
                    mapDataItem.put("b_drg_1l_judge",b_drg_1l_judge);
                    mapDataItem.put("b_drg_1r_judge",b_drg_1r_judge);
                    mapDataItem.put("b_sum_2_judge",b_sum_2_judge);
                    mapDataItem.put("b_dif_2_judge",b_dif_2_judge);
                    mapDataItem.put("b_drg_2l_judge",b_drg_2l_judge);
                    mapDataItem.put("b_drg_2r_judge",b_drg_2r_judge);
                    mapDataItem.put("b_sum_3_judge",b_sum_3_judge);
                    mapDataItem.put("b_dif_3_judge",b_dif_3_judge);
                    mapDataItem.put("b_drg_3l_judge",b_drg_3l_judge);
                    mapDataItem.put("b_drg_3r_judge",b_drg_3r_judge);
                    mapDataItem.put("b_sum_4_judge",b_sum_4_judge);
                    mapDataItem.put("b_dif_4_judge",b_dif_4_judge);
                    mapDataItem.put("b_drg_4l_judge",b_drg_4l_judge);
                    mapDataItem.put("b_drg_4r_judge",b_drg_4r_judge);
                    mapDataItem.put("b_park_judge",b_park_judge);
                    mapDataItem.put("b_sum_t_judge",b_sum_t_judge);
                    mapDataItem.put("b_t_judge",b_t_judge);
                    mapDataItem.put("abs_f_judge",abs_f_judge);
                    mapDataItem.put("abs_r_judge",abs_r_judge);
                    mapDataItem.put("abs_m_judge",abs_m_judge);
                    mapDataItem.put("abs_4_judge",abs_4_judge);
                    mapDataItem.put("abs_judge",abs_judge);
                    mapDataItem.put("aw_2ls_res",aw_2ls_res);
                    mapDataItem.put("aw_2rs_res",aw_2rs_res);
                    mapDataItem.put("aw_3ls_res",aw_3ls_res);
                    mapDataItem.put("aw_3rs_res",aw_3rs_res);
                    mapDataItem.put("b_max_2ls_res",b_max_2ls_res);
                    mapDataItem.put("b_max_2rs_res",b_max_2rs_res);
                    mapDataItem.put("b_sum_2s_res",b_sum_2s_res);
                    mapDataItem.put("b_sum_2s_judge",b_sum_2s_judge);
                    mapDataItem.put("b_dif_2ls_res",b_dif_2ls_res);
                    mapDataItem.put("b_dif_2rs_res",b_dif_2rs_res);
                    mapDataItem.put("b_dif_2s_res",b_dif_2s_res);
                    mapDataItem.put("b_dif_2s_judge",b_dif_2s_judge);
                    mapDataItem.put("b_max_3ls_res",b_max_3ls_res);
                    mapDataItem.put("b_max_3rs_res",b_max_3rs_res);
                    mapDataItem.put("b_sum_3s_res",b_sum_3s_res);
                    mapDataItem.put("b_sum_3s_judge",b_sum_3s_judge);
                    mapDataItem.put("b_dif_3ls_res",b_dif_3ls_res);
                    mapDataItem.put("b_dif_3rs_res",b_dif_3rs_res);
                    mapDataItem.put("b_dif_3s_res",b_dif_3s_res);
                    mapDataItem.put("b_dif_3s_judge",b_dif_3s_judge);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存制动数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //7.储存大灯检测仪数据
    @RequestMapping(value = "/PmcCoreInterfDdQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfDdQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfDdQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_dd";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String lt_i_lo_far_res="";//前照_左灯_远光光强_检测数据
                String lt_v_lo_far_res="";//前照_左灯_远光垂直_检测数据
                String lt_h_lo_far_res="";//前照_左灯_远光水平_检测数据
                String lt_v_lo_low_res="";//前照_左灯_近光垂直_检测数据
                String lt_h_lo_low_res="";//前照_左灯_近光水平_检测数据
                String lt_high_lo_far_res="";//前照_左灯_车灯灯高_检测数据
                String lt_i_ro_far_res="";//前照_右灯_远光光强_检测数据
                String lt_v_ro_far_res="";//前照_右灯_远光垂直_检测数据
                String lt_h_ro_far_res="";//前照_右灯_远光水平_检测数据
                String lt_v_ro_low_res="";//前照_右灯_近光垂直_检测数据
                String lt_h_ro_low_res="";//前照_右灯_近光水平_检测数据
                String lt_high_ro_far_res="";//前照_右灯_车灯灯高_检测数据
                String lt_i_lo_far_judge="";//前照_左灯_远光光强_检测结果
                String lt_v_lo_far_judge="";//前照_左灯_远光垂直_检测结果
                String lt_h_lo_far_judge="";//前照_左灯_远光水平_检测结果
                String lt_v_lo_low_judge="";//前照_左灯_近光垂直_检测结果
                String lt_h_lo_low_judge="";//前照_左灯_近光水平_检测结果
                String lt_i_ro_far_judge="";//前照_右灯_远光光强_检测结果
                String lt_v_ro_far_judge="";//前照_右灯_远光垂直_检测结果
                String lt_h_ro_far_judge="";//前照_右灯_远光水平_检测结果
                String lt_v_ro_low_judge="";//前照_右灯_近光垂直_检测结果
                String lt_h_ro_low_judge="";//前照_右灯_近光水平_检测结果
                String lt_left_judge="";//左灯检测结果
                String lt_right_judge="";//右灯检测结果
                String lt_judge="";//前照灯总判定结果
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    lt_i_lo_far_res=jbItem.getString("lt_i_lo_far_res");
                    lt_v_lo_far_res=jbItem.getString("lt_v_lo_far_res");
                    lt_h_lo_far_res=jbItem.getString("lt_h_lo_far_res");
                    lt_v_lo_low_res=jbItem.getString("lt_v_lo_low_res");
                    lt_h_lo_low_res=jbItem.getString("lt_h_lo_low_res");
                    lt_high_lo_far_res=jbItem.getString("lt_high_lo_far_res");
                    lt_i_ro_far_res=jbItem.getString("lt_i_ro_far_res");
                    lt_v_ro_far_res=jbItem.getString("lt_v_ro_far_res");
                    lt_h_ro_far_res=jbItem.getString("lt_h_ro_far_res");
                    lt_v_ro_low_res=jbItem.getString("lt_v_ro_low_res");
                    lt_h_ro_low_res=jbItem.getString("lt_h_ro_low_res");
                    lt_high_ro_far_res=jbItem.getString("lt_high_ro_far_res");
                    lt_i_lo_far_judge=jbItem.getString("lt_i_lo_far_judge");
                    lt_v_lo_far_judge=jbItem.getString("lt_v_lo_far_judge");
                    lt_h_lo_far_judge=jbItem.getString("lt_h_lo_far_judge");
                    lt_v_lo_low_judge=jbItem.getString("lt_v_lo_low_judge");
                    lt_h_lo_low_judge=jbItem.getString("lt_h_lo_low_judge");
                    lt_i_ro_far_judge=jbItem.getString("lt_i_ro_far_judge");
                    lt_v_ro_far_judge=jbItem.getString("lt_v_ro_far_judge");
                    lt_h_ro_far_judge=jbItem.getString("lt_h_ro_far_judge");
                    lt_v_ro_low_judge=jbItem.getString("lt_v_ro_low_judge");
                    lt_h_ro_low_judge=jbItem.getString("lt_h_ro_low_judge");
                    lt_left_judge=jbItem.getString("lt_left_judge");
                    lt_right_judge=jbItem.getString("lt_right_judge");
                    lt_judge=jbItem.getString("lt_judge");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("lt_i_lo_far_res",lt_i_lo_far_res);
                    mapDataItem.put("lt_v_lo_far_res",lt_v_lo_far_res);
                    mapDataItem.put("lt_h_lo_far_res",lt_h_lo_far_res);
                    mapDataItem.put("lt_v_lo_low_res",lt_v_lo_low_res);
                    mapDataItem.put("lt_h_lo_low_res",lt_h_lo_low_res);
                    mapDataItem.put("lt_high_lo_far_res",lt_high_lo_far_res);
                    mapDataItem.put("lt_i_ro_far_res",lt_i_ro_far_res);
                    mapDataItem.put("lt_v_ro_far_res",lt_v_ro_far_res);
                    mapDataItem.put("lt_h_ro_far_res",lt_h_ro_far_res);
                    mapDataItem.put("lt_v_ro_low_res",lt_v_ro_low_res);
                    mapDataItem.put("lt_h_ro_low_res",lt_h_ro_low_res);
                    mapDataItem.put("lt_high_ro_far_res",lt_high_ro_far_res);
                    mapDataItem.put("lt_i_lo_far_judge",lt_i_lo_far_judge);
                    mapDataItem.put("lt_v_lo_far_judge",lt_v_lo_far_judge);
                    mapDataItem.put("lt_h_lo_far_judge",lt_h_lo_far_judge);
                    mapDataItem.put("lt_v_lo_low_judge",lt_v_lo_low_judge);
                    mapDataItem.put("lt_h_lo_low_judge",lt_h_lo_low_judge);
                    mapDataItem.put("lt_i_ro_far_judge",lt_i_ro_far_judge);
                    mapDataItem.put("lt_v_ro_far_judge",lt_v_ro_far_judge);
                    mapDataItem.put("lt_h_ro_far_judge",lt_h_ro_far_judge);
                    mapDataItem.put("lt_v_ro_low_judge",lt_v_ro_low_judge);
                    mapDataItem.put("lt_h_ro_low_judge",lt_h_ro_low_judge);
                    mapDataItem.put("lt_left_judge",lt_left_judge);
                    mapDataItem.put("lt_right_judge",lt_right_judge);
                    mapDataItem.put("lt_judge",lt_judge);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存大灯检测仪数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //8.储存声级数据
    @RequestMapping(value = "/PmcCoreInterfSjQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfSjQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfSjQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_sj";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String noise_res="";//喇叭声级_检测数据
                String noise_judge="";//喇叭声级_检测结果
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    noise_res=jbItem.getString("noise_res");
                    noise_judge=jbItem.getString("noise_judge");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("noise_res",noise_res);
                    mapDataItem.put("noise_judge",noise_judge);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存声级数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //9.储存侧滑数据
    @RequestMapping(value = "/PmcCoreInterfChQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfChQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfChQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_ch";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String sslip_res="";//侧滑_检测数据
                String sslip_res2="";//侧滑2轴_检测数据(双前桥的车)
                String sslip_judge="";//侧滑_检测结果
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    sslip_res=jbItem.getString("sslip_res");
                    sslip_res2=jbItem.getString("sslip_res2");
                    sslip_judge=jbItem.getString("sslip_judge");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("sslip_res",sslip_res);
                    mapDataItem.put("sslip_res2",sslip_res2);
                    mapDataItem.put("sslip_judge",sslip_judge);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存侧滑数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //10.储存整体检测数据
    @RequestMapping(value = "/PmcCoreInterfZtQualitySave", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreInterfZtQualitySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/interf/PmcCoreInterfZtQualitySave";
        String tranResult = "";
        String errorMsg = "";
        String shaftQTable="d_pmc_me_station_quality_zt";
        try{
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String request_uuid = jsonParas.getString("request_uuid");
            String request_time=jsonParas.getString("request_time");
            String request_attr=jsonParas.getString("request_attr");
            String data_from_sys=jsonParas.getString("data_from_sys");
            JSONArray jaData=jsonParas.getJSONArray("list");
            if(jaData!=null && jaData.size()>0){
                String vin="";//车辆VIN
                String test_duration="";//整体检测时间
                String test_times="";//整体检测次数
                String test_judge="";//整体检测结果
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for(int i=0;i<jaData.size();i++){
                    JSONObject jbItem=jaData.getJSONObject(i);
                    vin=jbItem.getString("vin");
                    test_duration=jbItem.getString("test_duration");
                    test_times=jbItem.getString("test_times");
                    test_judge=jbItem.getString("test_judge");

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);
                    mapDataItem.put("vin",vin);
                    mapDataItem.put("test_duration",test_duration);
                    mapDataItem.put("test_times",test_times);
                    mapDataItem.put("test_judge",test_judge);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                }
                mongoTemplate.insert(lstDocuments,shaftQTable);
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "储存整体检测数据异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }


}
