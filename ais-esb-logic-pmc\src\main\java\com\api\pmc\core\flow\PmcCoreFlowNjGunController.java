package com.api.pmc.core.flow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 拧紧枪流程
 * 1.查询程序编号
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/flow")
public class PmcCoreFlowNjGunController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;

    //1.查询程序编号
    @RequestMapping(value = "/PmcCoreFlowProgramSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreFlowProgramSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/flow/PmcCoreFlowProgramSel";
        String selectResult="";
        String errorMsg="";
        String method="/aisEsbOra/pmc/core/interf/PmcCoreMesBasicTorqueSel";
        List<Map<String, Object>> itemListMo=null;
        try{
            String station_code=jsonParas.getString("station_code");//工位
            String prod_line_code=jsonParas.getString("prod_line_code");//产线
            String make_order=jsonParas.getString("make_order");//订单号
            String device_code=jsonParas.getString("device_code");//设备编号
            //获取oracle 拧紧力矩
            String program_code="";//程序编号
            if(PmcCoreServer.EsbUrl==null ||
                    PmcCoreServer.EsbUrl.isEmpty()){
                pmcCoreServerInit.ServerInit();
            }
            JSONObject jsonObjectMoReq = new JSONObject();
            jsonObjectMoReq.put("order_prod",make_order);//订单编号
            jsonObjectMoReq.put("device_codes","'"+device_code+"'");//设备编码集合
            JSONObject jsonObjectMoRes= cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl+method, jsonObjectMoReq);
            Integer moCode=jsonObjectMoRes.getInteger("code");
            String msg=jsonObjectMoRes.getString("msg");
            if(moCode!=0) {
                errorMsg= "订单{"+make_order+"},设备编码集合{"+device_code+"},获取上位MES拧紧力矩异常:"+msg;
                selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            JSONArray oraArry=jsonObjectMoRes.getJSONArray("data");
            if(oraArry==null || oraArry.size()<=0) {
                errorMsg= "订单{"+make_order+"},设备编码集合{"+device_code+"},获取上位MES拧紧力矩异常:"+msg;
                selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            JSONObject oraJsonObject = oraArry.getJSONObject(0);
            program_code = oraJsonObject.getString("PROGRAM_CODE");//程序编号

            //拼接返回值
            itemListMo=new ArrayList<>();
            Map<String, Object> map=new HashMap<>();
            map.put("program_code",program_code);
            itemListMo.add(map);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListMo,"","",0);
        }
        catch (Exception ex){
            errorMsg= "查询程序编号异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
