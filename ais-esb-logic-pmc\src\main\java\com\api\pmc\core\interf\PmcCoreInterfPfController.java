package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 排放系统接口
 * 1.检测线排放回传结果
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfPfController {
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.检测线排放回传结果
    @RequestMapping(value = "/PmcCorePfResultUpload", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCorePfResultUpload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String selectResult="";
        String errorMsg="";
        String pfFillTable="d_pmc_me_station_quality_pf";
        try{
            log.info("检测线排放回传结果:"+jsonParas.toString());

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            //获取参数
            String vin=jsonParas.getString("vin");//VIN码
            String obd_chk_date=jsonParas.getString("obd_chk_date");//OBD检测日期
            String dis_chk_date=jsonParas.getString("dis_chk_date");//排放检测日期
            String obd_chk_time=jsonParas.getString("obd_chk_time");//OBD检测时间
            String dis_chk_time=jsonParas.getString("dis_chk_time");//排放检测时间
            String obd_result=jsonParas.getString("obd_result");//OBD检测结果
            String dis_result=jsonParas.getString("dis_result");//排放检测结果
            String report_result=jsonParas.getString("report_result");//上报结果
            String chk_section=jsonParas.getString("chk_section");//车辆排放阶段
            String vehicle_data=jsonParas.getString("vehicle_data");//车辆信息
            String envir_data=jsonParas.getString("envir_data");//环境参数
            String test_data=jsonParas.getString("test_data");//检测信息
            String obd_data=jsonParas.getString("obd_data");//OBD数据
            String result_data=jsonParas.getString("result_data");//结果数据
            String device_data=jsonParas.getString("device_data");//设备数据

            //新增
            String nowDateTime= CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            List<Map<String, Object>> lstDocuments=new ArrayList<>();
            Map<String, Object> mapDataItem=new HashMap<>();
            String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
            mapDataItem.put("item_date",item_date);
            mapDataItem.put("item_date_val",item_date_val);
            mapDataItem.put("quality_trace_id",quality_trace_id);

            mapDataItem.put("vin",vin);
            mapDataItem.put("obd_chk_date",obd_chk_date);
            mapDataItem.put("dis_chk_date",dis_chk_date);
            mapDataItem.put("obd_chk_time",obd_chk_time);
            mapDataItem.put("dis_chk_time",dis_chk_time);
            mapDataItem.put("obd_result",obd_result);
            mapDataItem.put("dis_result",dis_result);
            mapDataItem.put("report_result",report_result);
            mapDataItem.put("chk_section",chk_section);
            mapDataItem.put("vehicle_data",vehicle_data);
            mapDataItem.put("envir_data",envir_data);
            mapDataItem.put("test_data",test_data);
            mapDataItem.put("obd_data",obd_data);
            mapDataItem.put("result_data",result_data);
            mapDataItem.put("device_data",device_data);
            mapDataItem.put("up_flag","N");
            mapDataItem.put("up_code",-1);
            mapDataItem.put("up_msg","");
            lstDocuments.add(mapDataItem);
            mongoTemplate.insert(lstDocuments,pfFillTable);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "检测线排放回传结果异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
