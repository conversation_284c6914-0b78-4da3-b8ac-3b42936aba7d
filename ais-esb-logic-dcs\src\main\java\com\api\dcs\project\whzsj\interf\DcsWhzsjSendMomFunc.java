package com.api.dcs.project.whzsj.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * WMS发送流程功能函数
 * 1.下发库存同步
 * 2.移库计划报工
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
@Slf4j
public class DcsWhzsjSendMomFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private DcsWhzsjSendMomSubFunc dcsWhzsjSendMomSubFunc;
    @Autowired
    private DcsInterfCommon dcsInterfCommon;

    //1.下发库存同步
    public void PrintSendTask(String station_code, String mo_id, String task_num, String model_type,
                              Double m_length, Double m_width, Double m_height, JSONArray jsonArrayM) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= dcsWhzsjSendMomSubFunc.PrintSendTask(station_code,task_num,model_type,
                m_length,m_width,m_height,jsonArrayM);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        Integer code=jbResult.getInteger("code");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
            //记录事件
            dcsInterfCommon.InsertApsTaskEvent(mo_id,station_code,esbInterfCode,"下发库存同步",
                    requestParas,responseParas,code,message,successFlag,startDate,endDate,"");
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }
}
