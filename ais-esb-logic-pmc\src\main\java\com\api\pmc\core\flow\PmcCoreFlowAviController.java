package com.api.pmc.core.flow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * AVI流程
 * 1.根据RFID获取订单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/flow")
public class PmcCoreFlowAviController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;
    @Autowired
    private PmcCoreAviBase pmcCoreAviBase;
    @Autowired
    private PmcCoreStationCrosBase pmcCoreStationCrosBase;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //1.根据RFID获取订单
    //(1)如果是上线工位，去MES上位(ORACLE)拿订单
    //(2)否则根据工件编号从线首表获取订单
    // @Transactional
    @RequestMapping(value = "/PmcCoreFlowRfidOnlineSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowRfidOnlineSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowRfidOnlineSel";
        String selectResult = "";
        String errorMsg = "";
        String method = "/aisEsbOra/pmc/core/interf/PmcCoreMesRfidMakeOrderSel";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间(ZA:总装、TA:涂装、HA:焊装、CA:冲压)
            String serial_num = jsonParas.getString("serial_num");//工件编号或RFID(DMS+行 不满20位前面补0)
            String pallet_num = jsonParas.getString("pallet_num");//托盘号(小车编号/滑橇号)
            String station_status = jsonParas.getString("station_status");//工位状态(1正常、2缓存区、3空板过点、4拉入、5拉出)
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //1、获取工位信息
            String station_des = "";//工位描述
            String bg_proceduce_code = "";//报工工序号
            String bg_proceduce_des = "";//报工工序描述
            String line_section_code = "";//产线分段编码(来自快速编码)
            String dx_flag = "";//是否为定序工位
            String online_flag = "";//是否上线工位
            String show_only_flag = "";//是否工位独立显示(Y过站离开是否清空实时工件信息)
            String flow_flag = "";//是否根据当前工位实时工件信息通过状态触发流程图
            String flow_taglist = "";//触发tagOnlyKeyList(做一个json格式,对应不同的工位状态触发对应的tag点)
            String avi_station = "";//AVI推算工位集
            String nj_flag = "";//是否下发拧紧参数
            String up_flag = "";//过站上传标识(MES,VIN打刻,油液加注,LES)
            String sqlStation = "select COALESCE(a.station_des,'') station_des," +
                    "COALESCE(a.bg_proceduce_code,'') bg_proceduce_code, " +
                    "COALESCE(a.bg_proceduce_des,'') bg_proceduce_des, " +
                    "COALESCE(a.line_section_code,'') line_section_code, " +
                    "COALESCE(a.station_attr,'N') dx_flag," +
                    "COALESCE(a.online_flag,'N') online_flag, " +
                    "COALESCE(a.show_only_flag,'N') show_only_flag, " +
                    "COALESCE(a.attribute1,'N') flow_flag, " +
                    "COALESCE(a.attribute2,'') flow_taglist, " +
                    "COALESCE(a.attribute3,'') avi_station, " +
                    "COALESCE(a.attribute4,'N') nj_flag, " +
                    "COALESCE(a.attribute5,'') up_flag " +
                    "from sys_fmod_station a," +
                    "     sys_fmod_prod_line b " +
                    "where a.prod_line_id=b.prod_line_id " +
                    "and a.enable_flag='Y' " +
                    "and b.enable_flag='Y' " +
                    "and a.station_code='" + station_code + "' " +
                    "and b.prod_line_code='" + prod_line_code + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, null, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "产线{" + prod_line_code + "},工位号{" + station_code + "},系统中不存在";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            station_des = itemListStation.get(0).get("station_des").toString();
            bg_proceduce_code = itemListStation.get(0).get("bg_proceduce_code").toString();
            bg_proceduce_des = itemListStation.get(0).get("bg_proceduce_des").toString();
            line_section_code = itemListStation.get(0).get("line_section_code").toString();
            dx_flag = itemListStation.get(0).get("dx_flag").toString();
            online_flag = itemListStation.get(0).get("online_flag").toString();
            show_only_flag = itemListStation.get(0).get("show_only_flag").toString();
            flow_flag = itemListStation.get(0).get("flow_flag").toString();
            flow_taglist = itemListStation.get(0).get("flow_taglist").toString();
            avi_station = itemListStation.get(0).get("avi_station").toString();
            nj_flag = itemListStation.get(0).get("nj_flag").toString();
            up_flag = itemListStation.get(0).get("up_flag").toString();
            //2、线首判断
            String make_order = "";//订单号
            String sale_make_order = ""; //销售订单号
            String dms = "";//DMS号
            String item_project = "";//行项目
            String vin = "";//vin号
            String small_model_type = "";//型号
            String main_material_code = "";//物料编号
            String material_color = "";//颜色
            String material_size = "";//尺寸
            String shaft_proc_num = "";//拧紧程序号
            String engine_num = "";//发动机
            String driver_way = "";//驱动形式
            String publish_number = "";//发布顺序号(上位MES)
            String white_car_adjust = "";//白车身调整总成
            String right_front_door = "";//右前车门焊接总成
            String left_front_door = "";//左前车门焊接总成
            if (online_flag.equals("Y")) {
                //3、获取oracle订单
                if (PmcCoreServer.EsbUrl == null ||
                        PmcCoreServer.EsbUrl.isEmpty()) {
                    pmcCoreServerInit.ServerInit();
                }
                JSONObject jsonObjectMoReq = new JSONObject();
                jsonObjectMoReq.put("rfid", serial_num);//工件编号或RFID(DMS+行 不满20位前面补0)
                jsonObjectMoReq.put("workshop", work_center_code);
                jsonObjectMoReq.put("order_state", "2");//订单状态(1.已生成2.已发布3.已上线4.拉入5.下线6.拉出)
                //jsonObjectMoReq.put("order_state_2", "满足");//齐套性校验
                JSONObject jsonObjectMoRes = cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl + method, jsonObjectMoReq);
                Integer moCode = jsonObjectMoRes.getInteger("code");
                String msg = jsonObjectMoRes.getString("msg");
                if (moCode != 0) {
                    errorMsg = "工位号{" + station_code + "},RFID{" + serial_num + "},车间{" + work_center_code + "},状态{2},获取上位MES订单异常:" + msg;
                    selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return selectResult;
                }
                JSONArray oraArry = jsonObjectMoRes.getJSONArray("data");
                if (oraArry == null || oraArry.size() <= 0) {
                    selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
                    return selectResult;
                }
                JSONObject oraJsonObject = oraArry.getJSONObject(0);
                make_order = oraJsonObject.getString("ORDER_PROD");//生产订单
                String LES_MATERAIL_INSPECTION_STATUS = oraJsonObject.getString("LES_MATERAIL_INSPECTION_STATUS");//齐套性
                if (LES_MATERAIL_INSPECTION_STATUS == null || !LES_MATERAIL_INSPECTION_STATUS.equals("满足")) {
                    itemListMo = new ArrayList<>();
                    Map<String, Object> map = new HashMap<>();
                    map.put("les_materail_inspection_status", "0");//0表示不满足齐套性校验
                    map.put("online_count", "0");
                    map.put("make_order", make_order);
                    itemListMo.add(map);
                    selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
                    return selectResult;
                }
                dms = oraJsonObject.getString("DMS_NUM");//DMS号
                item_project = oraJsonObject.getString("DMS_ROW");//DMS行项目
                sale_make_order = oraJsonObject.getString("ORDER_NUM");//销售订单
                if (StringUtils.isEmpty(sale_make_order)) {
                    sale_make_order = "";
                }
                vin = oraJsonObject.getString("VIN");//vin号
                if (StringUtils.isEmpty(vin)) {
                    vin = "";
                }
                main_material_code = oraJsonObject.getString("MATERIAL_CODE");//物料编码
                if (StringUtils.isEmpty(main_material_code)) {
                    main_material_code = "";
                }
                small_model_type = oraJsonObject.getString("PLATFORM_MODEL");//型号
                if (StringUtils.isEmpty(small_model_type)) {
                    small_model_type = "";
                }
                material_color = oraJsonObject.getString("CAR_COLOUR_CODE");//颜色
                if (StringUtils.isEmpty(material_color)) {
                    material_color = "";
                }
                material_size = oraJsonObject.getString("WIDTH_DIMENSION");//尺寸
                if (StringUtils.isEmpty(material_size)) {
                    material_size = "";
                }
                engine_num = oraJsonObject.getString("ENGINE_MODEL");//发动机
                if (StringUtils.isEmpty(engine_num)) {
                    engine_num = "";
                }
                driver_way = oraJsonObject.getString("DRIVE_MODE");//驱动形式
                if (StringUtils.isEmpty(driver_way)) {
                    driver_way = "";
                }
                publish_number = oraJsonObject.getString("PUBLISH_NUMBER");//发布顺序号

                white_car_adjust = oraJsonObject.getString("WHITE_CAR_ADJUST_ASSEMBLY");//白车身调整总成
                if (StringUtils.isEmpty(white_car_adjust)) {
                    white_car_adjust = "";
                }
                right_front_door = oraJsonObject.getString("RIGHT_FRONT_DOOR_ASSEMBLY");//右前车门焊接总成
                if (StringUtils.isEmpty(right_front_door)) {
                    right_front_door = "";
                }
                left_front_door = oraJsonObject.getString("LEFT_FRONT_DOOR_ASSEMBLY");//左前车门焊接总成
                if (StringUtils.isEmpty(left_front_door)) {
                    left_front_door = "";
                }
                //4、生成线首
                if (station_status.equals("1")) {
                    //判断 是否已经上线过
                    String sqlOnlineCount = "select count(1) " +
                            "from d_pmc_me_flow_online " +
                            "where enable_flag='Y' " +
                            "and work_center_code='" + work_center_code + "' " +
                            "and make_order='" + make_order + "' " +
                            "and serial_num='" + serial_num + "' ";
                    Integer onlineCount = cFuncDbSqlResolve.GetSelectCount(sqlOnlineCount);
                    if (onlineCount > 0) {
                        //拼接返回值
                        itemListMo = new ArrayList<>();
                        Map<String, Object> map = new HashMap<>();
                        map.put("online_count", "1");//>0表示已上线
                        map.put("make_order", make_order);
                        itemListMo.add(map);
                        selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
                        return selectResult;
                    }
                    //工位状态：1正常 才认为是正常首件上线
                    long flowOnlineId = cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_flow_online_id_seq", true);
                    String mo_work_order = String.valueOf(flowOnlineId);//生产订单顺序,针对订单进行排序
                    String sqlOnlineIns = "insert into d_pmc_me_flow_online " +
                            "(created_by,creation_date,flow_online_id," +
                            "work_center_code,prod_line_code,station_code,make_order," +
                            "serial_num,dms,item_project,vin,small_model_type," +
                            "main_material_code,material_color,material_size,shaft_proc_num," +
                            "staff_id,pallet_num,engine_num,driver_way,publish_number," +
                            "white_car_adjust,right_front_door,left_front_door," +
                            "repair_flag,enable_flag,sale_make_order) values " +
                            "('admin','" + nowDateTime + "'," + flowOnlineId + ",'" +
                            work_center_code + "','" + prod_line_code + "','" + station_code + "','" + make_order + "','" +
                            serial_num + "','" + dms + "','" + item_project + "','" + vin + "','" + small_model_type + "','" +
                            main_material_code + "','" + material_color + "','" + material_size + "','" + shaft_proc_num + "'," +
                            "'AIS','" + pallet_num + "','" + engine_num + "','" + driver_way + "','" + publish_number + "','" +
                            white_car_adjust + "','" + right_front_door + "','" + left_front_door + "','N','Y','" + sale_make_order + "')";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlOnlineIns, false, request, apiRoutePath);
                    //5、生成工位生产订单
                    String sqlDxStation = "select COALESCE(station_code,'N') station_code " +
                            "from sys_fmod_station a," +
                            "     sys_fmod_prod_line b " +
                            "where a.prod_line_id=b.prod_line_id " +
                            "and a.enable_flag='Y' " +
                            "and b.enable_flag='Y' " +
                            "and a.station_attr='Y' " +
                            "and a.station_code<>'" + station_code + "' " +
                            "and b.work_center_code='" + work_center_code + "' " +
                            "order by a.prod_line_id,a.station_order ";
                    List<Map<String, Object>> itemListDxStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlDxStation, false, null, apiRoutePath);
                    if (itemListDxStation != null && itemListDxStation.size() > 0) {
                        for (Map<String, Object> mapDxStation : itemListDxStation) {
                            long stationMoId = cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_station_mo_id_seq", true);
                            String stationCodeDx = mapDxStation.get("station_code").toString();
                            String sqlDxStationIns = "insert into d_pmc_me_station_mo " +
                                    "(created_by,creation_date,station_mo_id," +
                                    "prod_line_code,station_code,make_order," +
                                    "dms,item_project,mo_work_order," +
                                    "work_status,set_sign) values " +
                                    "('admin','" + nowDateTime + "'," + stationMoId + ",'" +
                                    prod_line_code + "','" + stationCodeDx + "','" + make_order + "','" +
                                    dms + "','" + item_project + "'," + mo_work_order + "," +
                                    "'PLAN','NONE')";
                            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlDxStationIns, false, request, apiRoutePath);
                        }
                    }
                }
            } else {
                //6.获取线首信息
                String sqlOnline = "select COALESCE(make_order,'') make_order," +
                        "COALESCE(dms,'') dms," +
                        "COALESCE(item_project,'') item_project, " +
                        "COALESCE(vin,'') vin, " +
                        "COALESCE(small_model_type,'') small_model_type, " +
                        "COALESCE(main_material_code,'') main_material_code, " +
                        "COALESCE(material_color,'') material_color, " +
                        "COALESCE(material_size,'') material_size, " +
                        "COALESCE(shaft_proc_num,'') shaft_proc_num, " +
                        "COALESCE(engine_num,'') engine_num, " +
                        "COALESCE(driver_way,'') driver_way, " +
                        "COALESCE(publish_number,'') publish_number " +
                        "from d_pmc_me_flow_online " +
                        "where enable_flag='Y' " +
                        "and work_center_code='" + work_center_code + "' " +
                        "and serial_num='" + serial_num + "' " +
                        "limit 1 offset 0";
                List<Map<String, Object>> itemListOnline = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
                if (itemListOnline == null || itemListOnline.size() <= 0) {
                    selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
                    return selectResult;
                }
                make_order = itemListOnline.get(0).get("make_order").toString();
                dms = itemListOnline.get(0).get("dms").toString();
                item_project = itemListOnline.get(0).get("item_project").toString();
                vin = itemListOnline.get(0).get("vin").toString();
                main_material_code = itemListOnline.get(0).get("main_material_code").toString();
                small_model_type = itemListOnline.get(0).get("small_model_type").toString();
                material_color = itemListOnline.get(0).get("material_color").toString();
                material_size = itemListOnline.get(0).get("material_size").toString();
                shaft_proc_num = itemListOnline.get(0).get("shaft_proc_num").toString();
                engine_num = itemListOnline.get(0).get("engine_num").toString();
                driver_way = itemListOnline.get(0).get("driver_way").toString();
                publish_number = itemListOnline.get(0).get("publish_number").toString();
            }
            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("les_materail_inspection_status", "1");//0表示不满足齐套性校验
            map.put("online_count", "0");//>0表示已上线
            map.put("make_order", make_order);
            map.put("dms", dms);
            map.put("item_project", item_project);
            map.put("serial_num", serial_num);
            map.put("pallet_num", pallet_num);
            map.put("vin", vin);
            map.put("main_material_code", main_material_code);
            map.put("small_model_type", small_model_type);
            map.put("material_color", material_color);
            map.put("material_size", material_size);
            map.put("shaft_proc_num", shaft_proc_num);
            map.put("engine_num", engine_num);
            map.put("driver_way", driver_way);
            map.put("publish_number", publish_number);
            //工位信息
            map.put("station_des", station_des);
            map.put("bg_proceduce_code", bg_proceduce_code);
            map.put("bg_proceduce_des", bg_proceduce_des);
            map.put("line_section_code", line_section_code);
            map.put("dx_flag", dx_flag);
            map.put("online_flag", online_flag);
            map.put("show_only_flag", show_only_flag);
            map.put("flow_flag", flow_flag);
            map.put("flow_taglist", flow_taglist);
            map.put("avi_station", avi_station);
            map.put("nj_flag", nj_flag);
            map.put("up_flag", up_flag);
            itemListMo.add(map);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            // TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "根据RFID获取订单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.校验特定工位是否已过站
    @RequestMapping(value = "/PmcCoreFlowStationCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowStationCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowStationCheck";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String serial_num = jsonParas.getString("serial_num");//工件编号
            //获取过站信息
            String sqlStationFlow = "select COALESCE(make_order,'') make_order " +
                    "from d_pmc_me_station_flow " +
                    "where check_status='OK' " +
                    "and check_code=0 " +
                    "and work_center_code='" + work_center_code + "' " +
                    "and station_code='" + station_code + "' " +
                    "and serial_num='" + serial_num + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStationFlow = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationFlow, false, null, apiRoutePath);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListStationFlow, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "校验特定工位是否已过站异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.校验订单特定工位是否已过站
    @RequestMapping(value = "/PmcCoreMakeOrderFlowStationCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreMakeOrderFlowStationCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreMakeOrderFlowStationCheck";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String make_order = jsonParas.getString("make_order");//工件编号
            //获取过站信息
            String sqlStationFlow = "select COALESCE(make_order,'') make_order " +
                    "from d_pmc_me_station_flow " +
                    "where check_status='OK' " +
                    "and check_code=0 " +
                    "and work_center_code='" + work_center_code + "' " +
                    "and station_code='" + station_code + "' " +
                    "and make_order='" + make_order + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStationFlow = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationFlow, false, null, apiRoutePath);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListStationFlow, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "校验特定工位是否已过站异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //3.RFID获取订单(滑橇号)
    @RequestMapping(value = "/PmcCoreFlowHqhRfidOnlineSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowHqhRfidOnlineSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowHqhRfidOnlineSel";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String serial_num_hqh = jsonParas.getString("serial_num");//工件编号(小车编号/滑橇号)
            String pallet_num = jsonParas.getString("pallet_num");//托盘号
            String stationCodeHq = jsonParas.getString("stationCodeHq");//滑橇绑定工位号
            //1、获取工位信息
            String station_des = "";//工位描述
            String bg_proceduce_code = "";//报工工序号
            String bg_proceduce_des = "";//报工工序描述
            String line_section_code = "";//产线分段编码(来自快速编码)
            String dx_flag = "";//是否为定序工位
            String online_flag = "";//是否上线工位
            String show_only_flag = "";//是否工位独立显示(Y过站离开是否清空实时工件信息)
            String flow_flag = "";//是否根据当前工位实时工件信息通过状态触发流程图
            String flow_taglist = "";//触发tagOnlyKeyList(做一个json格式,对应不同的工位状态触发对应的tag点)
            String avi_station = "";//AVI推算工位集
            String nj_flag = "";//是否下发拧紧参数
            String up_flag = "";//过站上传标识(MES,VIN打刻,油液加注,LES)
            String sqlStation = "select COALESCE(a.station_des,'') station_des," +
                    "COALESCE(a.bg_proceduce_code,'') bg_proceduce_code, " +
                    "COALESCE(a.bg_proceduce_des,'') bg_proceduce_des, " +
                    "COALESCE(a.line_section_code,'') line_section_code, " +
                    "COALESCE(a.station_attr,'N') dx_flag," +
                    "COALESCE(a.online_flag,'N') online_flag, " +
                    "COALESCE(a.show_only_flag,'N') show_only_flag, " +
                    "COALESCE(a.attribute1,'N') flow_flag, " +
                    "COALESCE(a.attribute2,'') flow_taglist, " +
                    "COALESCE(a.attribute3,'') avi_station, " +
                    "COALESCE(a.attribute4,'N') nj_flag, " +
                    "COALESCE(a.attribute5,'') up_flag " +
                    "from sys_fmod_station a," +
                    "     sys_fmod_prod_line b " +
                    "where a.prod_line_id=b.prod_line_id " +
                    "and a.enable_flag='Y' " +
                    "and b.enable_flag='Y' " +
                    "and a.station_code='" + station_code + "' " +
                    "and b.prod_line_code='" + prod_line_code + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, null, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "产线{" + prod_line_code + "},工位号{" + station_code + "},系统中不存在";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            station_des = itemListStation.get(0).get("station_des").toString();
            bg_proceduce_code = itemListStation.get(0).get("bg_proceduce_code").toString();
            bg_proceduce_des = itemListStation.get(0).get("bg_proceduce_des").toString();
            line_section_code = itemListStation.get(0).get("line_section_code").toString();
            dx_flag = itemListStation.get(0).get("dx_flag").toString();
            online_flag = itemListStation.get(0).get("online_flag").toString();
            show_only_flag = itemListStation.get(0).get("show_only_flag").toString();
            flow_flag = itemListStation.get(0).get("flow_flag").toString();
            flow_taglist = itemListStation.get(0).get("flow_taglist").toString();
            avi_station = itemListStation.get(0).get("avi_station").toString();
            nj_flag = itemListStation.get(0).get("nj_flag").toString();
            up_flag = itemListStation.get(0).get("up_flag").toString();
            //2、获取过站信息
            String station_flow_id = "";//过站ID
            String make_order = "";//订单号
            String serial_num = "";//工件编号
            //线首信息
            String mo_work_order = "";//生产订单顺序,针对订单进行排序
            String dms = "";//DMS号
            String item_project = "";//行项目
            String vin = "";//vin号
            String small_model_type = "";//型号
            String main_material_code = "";//物料编号
            String material_color = "";//颜色
            String material_size = "";//尺寸
            String shaft_proc_num = "";//拧紧程序号
            String engine_num = "";//发动机
            String driver_way = "";//驱动形式
            String sqlStationFlow = "select COALESCE(station_flow_id,0) station_flow_id," +
                    "COALESCE(make_order,'') make_order," +
                    "COALESCE(serial_num,'') serial_num " +
                    "from d_pmc_me_station_flow " +
                    "where check_status='OK' " +
                    "and check_code=0 " +
                    "and work_center_code='" + work_center_code + "' " +
                    "and pallet_num='" + serial_num_hqh + "' " +
                    "and station_code = '" + stationCodeHq + "' " +
                    "order by station_flow_id desc " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStationFlow = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationFlow, false, null, apiRoutePath);
            if (itemListStationFlow != null && itemListStationFlow.size() > 0) {
                station_flow_id = itemListStationFlow.get(0).get("station_flow_id").toString();
                make_order = itemListStationFlow.get(0).get("make_order").toString();
                serial_num = itemListStationFlow.get(0).get("serial_num").toString();
                //3、获取线首信息
                String sqlOnline = "select COALESCE(make_order,'') make_order," +
                        "COALESCE(dms,'') dms," +
                        "COALESCE(item_project,'') item_project, " +
                        "COALESCE(vin,'') vin, " +
                        "COALESCE(small_model_type,'') small_model_type, " +
                        "COALESCE(main_material_code,'') main_material_code, " +
                        "COALESCE(material_color,'') material_color, " +
                        "COALESCE(material_size,'') material_size, " +
                        "COALESCE(shaft_proc_num,'') shaft_proc_num, " +
                        "COALESCE(engine_num,'') engine_num, " +
                        "COALESCE(driver_way,'') driver_way " +
                        "from d_pmc_me_flow_online " +
                        "where enable_flag='Y' " +
                        "and work_center_code='" + work_center_code + "' " +
                        "and make_order='" + make_order + "' " +
                        "and serial_num='" + serial_num + "' " +
                        "LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListOnline = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
                if (itemListOnline == null || itemListOnline.size() <= 0) {
                    selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
                    return selectResult;
                }
                make_order = itemListOnline.get(0).get("make_order").toString();
                dms = itemListOnline.get(0).get("dms").toString();
                item_project = itemListOnline.get(0).get("item_project").toString();
                vin = itemListOnline.get(0).get("vin").toString();
                main_material_code = itemListOnline.get(0).get("main_material_code").toString();
                small_model_type = itemListOnline.get(0).get("small_model_type").toString();
                material_color = itemListOnline.get(0).get("material_color").toString();
                material_size = itemListOnline.get(0).get("material_size").toString();
                shaft_proc_num = itemListOnline.get(0).get("shaft_proc_num").toString();
                engine_num = itemListOnline.get(0).get("engine_num").toString();
                driver_way = itemListOnline.get(0).get("driver_way").toString();

                //拼接返回值
                itemListMo = new ArrayList<>();
                Map<String, Object> map = new HashMap<>();
                map.put("station_flow_id", station_flow_id);
                map.put("make_order", make_order);
                map.put("mo_work_order", mo_work_order);
                map.put("dms", dms);
                map.put("item_project", item_project);
                map.put("serial_num", serial_num);
                map.put("pallet_num", pallet_num);
                map.put("vin", vin);
                map.put("main_material_code", main_material_code);
                map.put("small_model_type", small_model_type);
                map.put("material_color", material_color);
                map.put("material_size", material_size);
                map.put("shaft_proc_num", shaft_proc_num);
                map.put("engine_num", engine_num);
                map.put("driver_way", driver_way);
                //工位信息
                map.put("station_des", station_des);
                map.put("bg_proceduce_code", bg_proceduce_code);
                map.put("bg_proceduce_des", bg_proceduce_des);
                map.put("line_section_code", line_section_code);
                map.put("dx_flag", dx_flag);
                map.put("online_flag", online_flag);
                map.put("show_only_flag", show_only_flag);
                map.put("flow_flag", flow_flag);
                map.put("flow_taglist", flow_taglist);
                map.put("avi_station", avi_station);
                map.put("nj_flag", nj_flag);
                map.put("up_flag", up_flag);
                itemListMo.add(map);
            }

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "RFID获取订单(滑橇号)异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //4.判断是否满足生产队列(定序)
    @RequestMapping(value = "/PmcCoreFlowDxStationSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowDxStationSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowDxStationSel";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String make_order = jsonParas.getString("make_order");//订单号
            //1、获取工位生产订单
            String mo_work_order = "";//生产订单顺序
            String sqlStationMo = "select COALESCE(mo_work_order,0) mo_work_order " +
                    "from d_pmc_me_station_mo " +
                    "where work_status='PLAN' " +
                    "and set_sign='NONE' " +
                    "and station_code='" + station_code + "' " +
                    "and make_order='" + make_order + "' " +
                    "Limit 1 offset 0";
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, null, apiRoutePath);
            if (itemListStationMo != null && itemListStationMo.size() > 0) {
                mo_work_order = itemListStationMo.get(0).get("mo_work_order").toString();
                //2、校验定序
                String dx_make_order = "";//满足定序的生成订单
                String sqlDxCheck = "select COALESCE(make_order,'') make_order " +
                        "from d_pmc_me_station_mo " +
                        "where work_status='PLAN' " +
                        "and set_sign='NONE' " +
                        "and station_code='" + station_code + "' " +
                        "and mo_work_order<" + mo_work_order +
                        "order by mo_work_order desc LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListDxCheck = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlDxCheck, false, null, apiRoutePath);
                if (itemListDxCheck != null && itemListDxCheck.size() > 0) {
                    dx_make_order = itemListDxCheck.get(0).get("make_order").toString();

                    //拼接返回值
                    itemListMo = new ArrayList<>();
                    Map<String, Object> map = new HashMap<>();
                    map.put("dx_make_order", dx_make_order);
                    itemListMo.add(map);
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否满足生产队列异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //5.记录过站信息(NG/OK)
    @Transactional
    @RequestMapping(value = "/PmcCoreFlowStationFlowIns", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowStationFlowIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowStationFlowIns";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            //订单信息
            String make_order = jsonParas.getString("make_order");//订单号
            String dms = jsonParas.getString("dms");//DMS号
            String item_project = jsonParas.getString("item_project");//行项目
            String serial_num = jsonParas.getString("serial_num");//工件编号或RFID(DMS+行 不满20位前面补0)
            String pallet_num = jsonParas.getString("pallet_num");//托盘号
            String vin = jsonParas.getString("vin");//vin号
            String main_material_code = jsonParas.getString("main_material_code");//物料编号
            String small_model_type = jsonParas.getString("small_model_type");//型号
            String material_color = jsonParas.getString("material_color");//颜色
            String material_size = jsonParas.getString("material_size");//尺寸
            String shaft_proc_num = jsonParas.getString("shaft_proc_num");//拧紧程序号
            String engine_num = jsonParas.getString("engine_num");//发动机
            String driver_way = jsonParas.getString("driver_way");//驱动形式
            String station_status = jsonParas.getString("station_status");//工位状态(1正常、2缓存区、3空板过点、4拉入、5拉出)
            //过站时间
            String arrive_date = jsonParas.getString("arrive_date");//到达时间
            //过站状态
            String check_status = jsonParas.getString("check_status");//过站校验状态(OK/NG)
            String check_code = jsonParas.getString("check_code");//过站校验代码
            String check_msg = jsonParas.getString("check_msg");//过站校验描述
            //工位信息
            String station_des = jsonParas.getString("station_des");//工位描述
            String bg_proceduce_code = jsonParas.getString("bg_proceduce_code");//报工工序号
            String bg_proceduce_des = jsonParas.getString("bg_proceduce_des");//报工工序描述
            String line_section_code = jsonParas.getString("line_section_code");//产线分段编码(来自快速编码)
            String up_interf_flag = jsonParas.getString("up_interf_flag");//过站上传标识(MES,VIN打刻,油液加注,LES)
            String flow_taglist = jsonParas.getString("flow_taglist");////触发tagOnlyKeyList(做一个json格式,对应不同的工位状态触发对应的tag点)
            //执行函数
            String staff_id = "AIS";//操作者(默认工位号)
            String allow_way = "1";//允许信息来源(1AIS流程图过站；2MES过站；3AGV过站)
            String stationFlowId = pmcCoreStationCrosBase.StationCrosIntefTask(request, apiRoutePath, staff_id, allow_way,
                    station_code, prod_line_code, work_center_code,
                    make_order, dms, item_project, serial_num, pallet_num,
                    vin, main_material_code, small_model_type, material_color,
                    material_size, shaft_proc_num, engine_num, driver_way,
                    station_status, arrive_date,
                    check_status, check_code, check_msg,
                    station_des, bg_proceduce_code, bg_proceduce_des,
                    line_section_code, up_interf_flag, flow_taglist);
            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("station_flow_id", stationFlowId);
            itemListMo.add(map);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "记录过站信息异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //6.获取拧紧参数
    @RequestMapping(value = "/PmcCoreFlowNjStationSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowNjStationSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowNjStationSel";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String make_order = jsonParas.getString("make_order");//订单号
            String vin = jsonParas.getString("vin");//Vin号
            String flow_taglist = jsonParas.getString("flow_taglist");////触发tagOnlyKeyList(做一个json格式,对应不同的工位状态触发对应的tag点)

            log.info("【流程图】拧紧点位【" + flow_taglist + "】");

            if (flow_taglist != null && !flow_taglist.equals("")) {
                //JSONArray jaCell=JSONArray.parseArray(JSONObject.toJSONString(flow_taglist));
                JSONArray jsonArray = JSONArray.parseArray(flow_taglist);
                String tag_list = "";
                String tag_value_list = "";
                String tag_station_code = "";
                String client_code = "";
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if (i == 0) {
                        client_code = object.getString("tagkey").split("/")[0];
                    }
                    tag_list += object.getString("tagkey") + "," +
                            object.getString("make_order") + "," +
                            object.getString("vin") + ",";
                    tag_value_list += "1&" + make_order + "&" + vin;
                }
                tag_list = tag_list.substring(0, tag_list.length() - 1);
                //查询 实例对应的工位
                String sqlClient = "select COALESCE(station_code,'') station_code  " +
                        "from scada_client " +
                        "where enable_flag='Y' " +
                        "and client_code ='" + client_code + "' ";
                List<Map<String, Object>> itemListClient = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlClient, false, null, apiRoutePath);
                if (itemListClient != null && itemListClient.size() > 0) {
                    tag_station_code = itemListClient.get(0).get("station_code").toString();
                }
                log.info("【流程图】拧紧写入【" + tag_station_code + "】,点位【" + tag_list + "】,值【" + tag_value_list + "】");
                errorMsg = cFuncUtilsCellScada.WriteTagByStation(tag_station_code, tag_station_code, tag_list, tag_value_list, true);
                log.info("【流程图】拧紧写入点位结果信息：" + errorMsg);
            }

            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("nj", "1");
            itemListMo.add(map);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "获取拧紧参数异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //7.空板获取AVI推算工位
    @RequestMapping(value = "/PmcCoreFlowBlankBoardSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowBlankBoardSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowBlankBoardSel";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间(ZA:总装、TA:涂装、HA:焊装、CA:冲压)
            //1、获取工位信息
            String avi_station = "";//AVI推算工位集
            String sqlStation = "select COALESCE(a.attribute3,'') avi_station " +
                    "from sys_fmod_station a," +
                    "     sys_fmod_prod_line b " +
                    "where a.prod_line_id=b.prod_line_id " +
                    "and a.enable_flag='Y' " +
                    "and b.enable_flag='Y' " +
                    "and a.station_code='" + station_code + "' " +
                    "and b.prod_line_code='" + prod_line_code + "' " +
                    "limit 1 offset 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "工位号{" + station_code + "},系统中不存在";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            avi_station = itemListStation.get(0).get("avi_station").toString();
            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            //工位信息
            map.put("avi_station", avi_station);
            itemListMo.add(map);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "空板获取AVI推算工位异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //8.AVI推算
    @Transactional
    @RequestMapping(value = "/PmcCoreFlowAviStationSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowAviStationSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowAviStationSel";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String avi_station = jsonParas.getString("avi_station");//AVI推算工位集
            //1、推算循环
            String station_status = "";//工位状态(1正常、2缓存区、3空板过点、4拉入、5拉出)
            String empty_ban_flag = "";//是否为空板
            String serial_num = "";//工件编号或RFID(DMS+行 不满20位前面补0)
            String pallet_num = "";//托盘号
            String staff_id = "";//操作者(默认工位号)
            String make_order = "";//订单号
            String dms = "";//DMS号
            String item_project = "";//行项目
            String vin = "";//vin号
            String small_model_type = "";//型号
            String main_material_code = "";//物料编号
            String material_color = "";//颜色
            String material_size = "";//尺寸
            String shaft_proc_num = "";//拧紧程序号
            String quality_sign = "";//总合格标志
            String set_sign = "";//拉入或者拉出过站点（SET_IN、SET_OUT、NORMAL）
            //过站状态
            String check_status = "";//过站校验状态(OK/NG)
            String check_code = "";//过站校验代码
            String check_msg = "";//过站校验描述
            String engine_num = "";//发动机
            String driver_way = "";//驱动形式
            String status_way = "";//状态计算方式(1过点实际/2推算/3采集)
            if (avi_station != null && !avi_station.equals("")) {
                avi_station = station_code + "," + avi_station;//拼接逻辑循环
                String[] lst = avi_station.split(",", -1);
                if (lst != null && lst.length > 0) {
                    Integer lstLen = lst.length - 1;//数组数量
                    String pre_station_code = station_code;//前工位
                    String cur_station_code = "";//当前工位
                    for (int i = lstLen; i > 0; i--) {
                        pre_station_code = lst[i - 1];
                        cur_station_code = lst[i];
                        //2、获取前工位信息
                        String sqlStationStatus = "select COALESCE(station_status,'') station_status," +
                                "COALESCE(empty_ban_flag,'') empty_ban_flag," +
                                "COALESCE(serial_num,'') serial_num," +
                                "COALESCE(pallet_num,'') pallet_num," +
                                "COALESCE(staff_id,'') staff_id," +
                                "COALESCE(make_order,'') make_order," +
                                "COALESCE(dms,'') dms," +
                                "COALESCE(item_project,'') item_project," +
                                "COALESCE(vin,'') vin," +
                                "COALESCE(small_model_type,'') small_model_type," +
                                "COALESCE(main_material_code,'') main_material_code," +
                                "COALESCE(material_color,'') material_color," +
                                "COALESCE(material_size,'') material_size," +
                                "COALESCE(shaft_proc_num,'') shaft_proc_num," +
                                "COALESCE(quality_sign,'') quality_sign," +
                                "COALESCE(set_sign,'') set_sign," +
                                "COALESCE(check_status,'') check_status," +
                                "COALESCE(check_code,0) check_code," +
                                "COALESCE(check_msg,'') check_msg," +
                                "COALESCE(engine_num,'') engine_num," +
                                "COALESCE(driver_way,'') driver_way," +
                                "COALESCE(status_way,'') status_way " +
                                "from d_pmc_me_station_status " +
                                "where station_code='" + pre_station_code + "' " +
                                "and work_center_code='" + work_center_code + "' ";
                        List<Map<String, Object>> itemListStationStatus = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationStatus, false, request, apiRoutePath);
                        if (itemListStationStatus != null && itemListStationStatus.size() > 0) {
                            station_status = itemListStationStatus.get(0).get("station_status").toString();
                            empty_ban_flag = itemListStationStatus.get(0).get("empty_ban_flag").toString();
                            serial_num = itemListStationStatus.get(0).get("serial_num").toString();
                            pallet_num = itemListStationStatus.get(0).get("pallet_num").toString();
                            staff_id = itemListStationStatus.get(0).get("staff_id").toString();
                            make_order = itemListStationStatus.get(0).get("make_order").toString();
                            dms = itemListStationStatus.get(0).get("dms").toString();
                            item_project = itemListStationStatus.get(0).get("item_project").toString();
                            vin = itemListStationStatus.get(0).get("vin").toString();
                            small_model_type = itemListStationStatus.get(0).get("small_model_type").toString();
                            main_material_code = itemListStationStatus.get(0).get("main_material_code").toString();
                            material_color = itemListStationStatus.get(0).get("material_color").toString();
                            material_size = itemListStationStatus.get(0).get("material_size").toString();
                            shaft_proc_num = itemListStationStatus.get(0).get("shaft_proc_num").toString();
                            quality_sign = itemListStationStatus.get(0).get("quality_sign").toString();
                            set_sign = itemListStationStatus.get(0).get("set_sign").toString();
                            check_status = itemListStationStatus.get(0).get("check_status").toString();
                            check_code = itemListStationStatus.get(0).get("check_code").toString();
                            check_msg = itemListStationStatus.get(0).get("check_msg").toString();
                            engine_num = itemListStationStatus.get(0).get("engine_num").toString();
                            driver_way = itemListStationStatus.get(0).get("driver_way").toString();
                            status_way = itemListStationStatus.get(0).get("status_way").toString();
                            //3、修改订单信息
                            pmcCoreAviBase.AviIntefTask(request, apiRoutePath,
                                    work_center_code, cur_station_code,
                                    station_status, empty_ban_flag, serial_num, pallet_num, staff_id,
                                    make_order, dms, item_project, vin, small_model_type,
                                    main_material_code, material_color, material_size, shaft_proc_num,
                                    quality_sign, set_sign, check_status, check_code, check_msg, engine_num, driver_way, status_way);
                            //清空、复值
                            pre_station_code = "";
                            cur_station_code = "";
                        }
                    }
                }
            }
            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("avi", "1");
            itemListMo.add(map);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "AVI推算异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }


    //9.判断是否满足生产队列(合件)
    @Transactional
    @RequestMapping(value = "/PmcCoreFlowHjStationSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowHjStationSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowHjStationSel";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String station_check = jsonParas.getString("station_check");//校验工位是否合件
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //获取过站信息
            String station_flow_id = "";//过站ID
            String sqlStationFlow = "select COALESCE(station_flow_id,0) station_flow_id " +
                    "from d_pmc_me_station_flow " +
                    "where check_status='OK' " +
                    "and check_code=0 " +
                    "and assembly_finish_flag='N' " +
                    "and work_center_code='" + work_center_code + "' " +
                    "and station_code='" + station_check + "' " +
                    "order by station_flow_id desc " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStationFlow = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationFlow, false, null, apiRoutePath);
            if (itemListStationFlow != null && itemListStationFlow.size() > 0) {
                station_flow_id = itemListStationFlow.get(0).get("station_flow_id").toString();
                //2、修改合件标识
                String updStation = "update d_pmc_me_station_flow set " +
                        "last_updated_by='" + station_code + "'," +
                        "last_update_date='" + nowDateTime + "'," +
                        "assembly_finish_flag='Y' " +
                        " where station_flow_id=" + station_flow_id;
                cFuncDbSqlExecute.ExecUpdateSql(station_code, updStation, true, null, apiRoutePath);

                //拼接返回值
                itemListMo = new ArrayList<>();
                Map<String, Object> map = new HashMap<>();
                map.put("station_flow_id", station_flow_id);
                itemListMo.add(map);
            }

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "判断是否满足生产队列(合件)异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //10.解绑滑橇号
    @Transactional
    @RequestMapping(value = "/PmcCoreFlowHqhUnbindUpd", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowHqhUnbindUpd(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowHqhUnbindUpd";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String station_flow_id = jsonParas.getString("station_flow_id");//过站ID
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //解绑滑橇号
            String updStation = "update d_pmc_me_station_flow set " +
                    "last_updated_by='" + station_code + "'," +
                    "last_update_date='" + nowDateTime + "'," +
                    "pallet_num='' " +
                    " where station_flow_id=" + station_flow_id;
            cFuncDbSqlExecute.ExecUpdateSql(station_code, updStation, true, null, apiRoutePath);

            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("station_flow_id", station_flow_id);
            itemListMo.add(map);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "解绑滑橇号异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //11.接口调用
    @Transactional
    @RequestMapping(value = "/PmcCoreFlowCallInter", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowCallInter(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowCallInter";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String make_order = jsonParas.getString("make_order");//订单号
            String esb_interf_code = jsonParas.getString("esb_interf_code");//接口编号
            //1、获取线首信息
            String dms = "";//DMS号
            String item_project = "";//行项目
            String factory_code = "";//工厂编码
            String factory_des = "";//工厂描述
            String white_car_adjust = "";//白车身调整总成
            String sqlOnline = "select COALESCE(dms,'') dms," +
                    "COALESCE(item_project,'') item_project, " +
                    "COALESCE(white_car_adjust,'') white_car_adjust " +
                    "from d_pmc_me_flow_online " +
                    "where enable_flag='Y' " +
                    "and work_center_code='" + work_center_code + "' " +
                    "and make_order='" + make_order + "' " +
                    "limit 1 offset 0";
            List<Map<String, Object>> itemListOnline = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
            if (itemListOnline == null || itemListOnline.size() <= 0) {
                errorMsg = "车间{" + work_center_code + "},订单号{" + make_order + "},系统中不存在";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            dms = itemListOnline.get(0).get("dms").toString();
            item_project = itemListOnline.get(0).get("item_project").toString();
            white_car_adjust = itemListOnline.get(0).get("white_car_adjust").toString();

            //2、初始化接口信息
            if (PmcCoreServer.EsbUrl == null ||
                    PmcCoreServer.EsbUrl.isEmpty()) {
                pmcCoreServerInit.ServerInit();
            }
            //3、调用 下发焊装Hzdk
            String interfParas = PmcCoreServer.InterfBaseInfoList.get(esb_interf_code).toString();
            //workshop=interfParas.split("&&")[0].toString();
            String interfUrl = interfParas.split("&&")[1].toString();
            //格式化接口数据
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("request_uuid", CFuncUtilsSystem.CreateUUID(true));
            jsonObjectReq.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            jsonObjectReq.put("request_attr", "");
            jsonObjectReq.put("ORDER_PROD", make_order);
            jsonObjectReq.put("DMS_NUM", dms);
            jsonObjectReq.put("DMS_ROW", item_project);
            jsonObjectReq.put("FACTORY_CODE", factory_code);
            jsonObjectReq.put("FACTORY_DESCRIPTION", factory_des);
            jsonObjectReq.put("MATERIAL_CODE", white_car_adjust);

            log.info("【PmcCoreFlowCallInter】下发接口传参：" + jsonObjectReq.toString());
            JSONObject jsonObjectRes = cFuncUtilsRest.PostJbBackJb(interfUrl, jsonObjectReq);
            log.info("【PmcCoreFlowCallInter】下发接口返回值：" + jsonObjectRes.toString());
            Integer backCode = jsonObjectRes.getInteger("code");
            String backMsg = jsonObjectRes.getString("msg");
            if (backCode != 0) {
                errorMsg = "接口调用{" + esb_interf_code + "},异常" + backMsg;
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "接口调用异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //12.判断工位订单是否是预约拉出状态，如果是的话，修改状态为拉出状态
    @RequestMapping(value = "/PmcCoreFlowPreSetOutStationCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowPreSetOutStationCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowPreSetOutStationCheck";
        String selectResult = "";
        String errorMsg = "";
        String userName = "pmc";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String make_order = jsonParas.getString("make_order");//订单号
            //1、判断是否是拉出状态
            String sqlStationMoSetOut = "select station_mo_id,COALESCE(b.station_des,'') station_des " +
                    "from d_pmc_me_station_mo a," +
                    "sys_fmod_station b " +
                    "where a.station_code=b.station_code " +
                    "and a.make_order='" + make_order + "' " +
                    "and a.station_code='" + station_code + "' " +
                    "and a.set_sign='SET_OUT' limit 1 offset 0";
            List<Map<String, Object>> itemStationMoSetOut = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMoSetOut, false, null, apiRoutePath);
            if (itemStationMoSetOut != null && itemStationMoSetOut.size() > 0) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemStationMoSetOut, "", "", itemStationMoSetOut.size());
                return selectResult;
            }

            //1、判断是否是预约拉出状态
            String sqlStationMo = "select station_mo_id,COALESCE(b.station_des,'') station_des " +
                    "from d_pmc_me_station_mo a," +
                    "sys_fmod_station b " +
                    "where a.station_code=b.station_code " +
                    "and a.make_order='" + make_order + "' " +
                    "and a.station_code='" + station_code + "' " +
                    "and a.set_sign='PRE_SET_OUT' limit 1 offset 0";
            List<Map<String, Object>> itemStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, null, apiRoutePath);
            if (itemStationMo != null && itemStationMo.size() > 0) {
                String station_mo_id = itemStationMo.get(0).get("station_mo_id").toString();
                String station_des = itemStationMo.get(0).get("station_des").toString();
                //(1)获取线首信息
                String work_center_code = "";
                String prod_line_code = "";
                String serial_num = "";
                String pallet_num = "";
                String white_car_adjust = "";
                String dms = "";
                String item_project = "";
                String vin = "";
                String small_model_type = "";
                String main_material_code = "";
                String material_color = "";
                String material_size = "";
                String shaft_proc_num = "";
                String sqlOnline = "select COALESCE(work_center_code,'') work_center_code," +
                        "COALESCE(prod_line_code,'') prod_line_code," +
                        "COALESCE(serial_num,'') serial_num," +
                        "COALESCE(pallet_num,'') pallet_num," +
                        "COALESCE(white_car_adjust,'') white_car_adjust," +
                        "COALESCE(dms,'') dms," +
                        "COALESCE(item_project,'') item_project, " +
                        "COALESCE(vin,'') vin, " +
                        "COALESCE(small_model_type,'') small_model_type, " +
                        "COALESCE(main_material_code,'') main_material_code, " +
                        "COALESCE(material_color,'') material_color, " +
                        "COALESCE(material_size,'') material_size, " +
                        "COALESCE(shaft_proc_num,'') shaft_proc_num " +
                        "from d_pmc_me_flow_online " +
                        "where enable_flag='Y' " +
                        //"and station_code='"+station_code+"' "+
                        "and make_order='" + make_order + "' " +
                        "limit 1 offset 0";
                List<Map<String, Object>> itemListOnline = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
                if (itemListOnline != null && itemListOnline.size() > 0) {
                    work_center_code = itemListOnline.get(0).get("work_center_code").toString();
                    prod_line_code = itemListOnline.get(0).get("prod_line_code").toString();
                    serial_num = itemListOnline.get(0).get("serial_num").toString();
                    pallet_num = itemListOnline.get(0).get("pallet_num").toString();
                    white_car_adjust = itemListOnline.get(0).get("white_car_adjust").toString();
                    dms = itemListOnline.get(0).get("dms").toString();
                    item_project = itemListOnline.get(0).get("item_project").toString();
                    vin = itemListOnline.get(0).get("vin").toString();
                    small_model_type = itemListOnline.get(0).get("small_model_type").toString();
                    main_material_code = itemListOnline.get(0).get("main_material_code").toString();
                    material_color = itemListOnline.get(0).get("material_color").toString();
                    material_size = itemListOnline.get(0).get("material_size").toString();
                    shaft_proc_num = itemListOnline.get(0).get("shaft_proc_num").toString();
                }
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");

                //(2)获取后续工序
                String sqlFollowstation = "select c.station_mo_id," +
                        "COALESCE(a.station_code,'') station_code," +
                        "COALESCE(a.station_des,'') station_des " +
                        "from sys_fmod_station a," +
                        "sys_fmod_station b," +
                        "d_pmc_me_station_mo c " +
                        "where a.work_sequence>=b.work_sequence " +
                        "and a.station_code=c.station_code " +
                        "and COALESCE(a.setinout_group,'')=COALESCE(b.setinout_group,'') " +
                        "and c.make_order='" + make_order + "' and b.station_code='" + station_code + "'";
                //焊装需要根据不同的白车身号查询到对应的工艺路线，再根据后续工序进行拉入拉出判定操作
                if (work_center_code.equals("HA")) {
                    //根据白车身调整总成查询上线工位和工艺路线
                    String route_station = "";
                    String sqlRoute = "SELECT prod_line_code,online_station,route_station " +
                            "from d_pmc_fmod_ha_car_route where car_type like '%" + white_car_adjust + "%'";
                    List<Map<String, Object>> itemListRoute = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlRoute, false, null, apiRoutePath);
                    if (itemListRoute != null && itemListRoute.size() > 0) {
                        route_station = itemListRoute.get(0).get("route_station").toString();
                        if (route_station != null && route_station != "") {
                            StringBuilder sb = new StringBuilder().append("(");
                            String[] stations = route_station.split(",");
                            for (int i = 0; i < stations.length; i++) {
                                sb.append("'").append(stations[i]).append("'");
                                if (i < stations.length - 1) sb.append(",");
                            }
                            sb.append(")");
                            sqlFollowstation+=" and a.station_code in " + sb.toString();
                        }
                    }
                }
                List<Map<String, Object>> itemFollowstation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlFollowstation, false, null, apiRoutePath);
                if (itemFollowstation != null && itemFollowstation.size() > 0) {
                    for (int i = 0; i < itemFollowstation.size(); i++) {
                        station_mo_id = itemFollowstation.get(i).get("station_mo_id").toString();
                        String follow_station_code = itemFollowstation.get(i).get("station_code").toString();
                        String follow_station_des = itemFollowstation.get(i).get("station_des").toString();
                        if (follow_station_code.equals(station_code)) {
                            //(3)直接拉出，插入数据
                            String set_type = "SET_OUT";
                            String staff_id = "";
                            long set_report_id = cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_set_in_out_id_seq", false);
                            String insertSql = "insert into d_pmc_me_set_in_out (created_by,creation_date,set_report_id," +
                                    "work_center_code,prod_line_code,station_code,station_des,set_type," +
                                    "serial_num,pallet_num,staff_id,make_order,dms," +
                                    "item_project,vin,small_model_type,main_material_code,material_color,material_size, " +
                                    "shaft_proc_num,set_date,set_marks) values (" + "'" + userName + "','" + nowDateTime + "'," + set_report_id + ",'" +
                                    work_center_code + "','" + prod_line_code + "','" + follow_station_code + "','" + follow_station_des + "','" + set_type + "','" +
                                    serial_num + "','" + pallet_num + "','" + staff_id + "','" + make_order + "','" + dms + "'," +
                                    item_project + ",'" + vin + "','" + small_model_type + "','" + main_material_code + "','" + material_color + "','" + material_size + "','" +
                                    shaft_proc_num + "','','')";
                            cFuncDbSqlExecute.ExecUpdateSql(userName, insertSql, true, request, apiRoutePath);
                        }

                        //2.修改状态为拉出状态
                        String updateSql = "update d_pmc_me_station_mo set " +
                                "last_updated_by='" + userName + "'," +
                                "last_update_date='" + nowDateTime + "'," +
                                "set_sign='SET_OUT' " +
                                " where station_mo_id =" + station_mo_id;
                        cFuncDbSqlExecute.ExecUpdateSql(userName, updateSql, true, request, apiRoutePath);
                    }
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemStationMo, "", "", itemStationMo.size());
        } catch (Exception ex) {
            errorMsg = "接口调用异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //13.校验轮胎图号
    @RequestMapping(value = "/PmcCoreFlowTyreCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowTyreCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowTyreCheck";
        String selectResult = "";
        String errorMsg = "";
        String userName = "pmc";
        try {
            String make_order = jsonParas.getString("make_order");//生产订单
            String station_code = jsonParas.getString("station_code");//工位号
            String tyre_num = jsonParas.getString("tyre_num");//轮胎图号
            //0、判断是否有需要校验的轮胎信息
            String sqlMoTyres = "select order_prod,tyre_material_code,sort from d_pmc_fmod_tyre_sort " +
                    "where order_prod='" + make_order + "' and " +
                    "enable_flag='Y' and checked_flag='N' and COALESCE(checked_result,'') not in ('N','Y') " +
                    "order by sort limit 1";
            List<Map<String, Object>> itemMoTyres = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoTyres, false, null, apiRoutePath);
            if (itemMoTyres == null || itemMoTyres.size() <= 0) {
                //拼接返回值
                ArrayList itemListMoTyre = new ArrayList<>();
                Map<String, Object> map = new HashMap<>();
                map.put("check_code", "100");
                map.put("check_status", "OK");
                itemListMoTyre.add(map);
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMoTyre, "", "", 0);
                return selectResult;
            }

            //1、校验轮胎图号
            String sqlMoTyreNum = "select order_prod,tyre_material_code,sort from d_pmc_fmod_tyre_sort " +
                    "where order_prod='" + make_order + "' and " +
                    "enable_flag='Y' and checked_flag='N' and COALESCE(checked_result,'') not in ('N','Y') " +
                    "order by sort limit 1";
            List<Map<String, Object>> itemMoTyreNum = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoTyreNum, false, null, apiRoutePath);
            if (itemMoTyreNum != null && itemMoTyreNum.size() > 0) {
                //2、更新校验结果
                String tyre_material_code = itemMoTyreNum.get(0).get("tyre_material_code").toString();
                String sort = itemMoTyreNum.get(0).get("sort").toString();
                if (tyre_material_code.equals(tyre_num)) {
                    String updateSql = "update d_pmc_fmod_tyre_sort set checked_result='Y',checked_flag='Y' " +
                            "where order_prod='" + make_order + "' and tyre_material_code='" + tyre_num + "' and " +
                            "enable_flag='Y' and checked_flag='N' and COALESCE(checked_result,'') not in ('N','Y') " +
                            "and sort=" + sort;
                    cFuncDbSqlExecute.ExecUpdateSql(userName, updateSql, true, request, apiRoutePath);
                    String sqlOnly = "select count(1) " +
                            "from d_pmc_fmod_tyre_sort " +
                            "where order_prod='" + make_order + "' and " +
                            "enable_flag='Y' and checked_flag='N' and COALESCE(checked_result,'') not in ('N','Y')";
                    int countOnly = cFuncDbSqlResolve.GetSelectCount(sqlOnly);
                    if (countOnly > 0) {
                        //拼接返回值
                        ArrayList itemListMoTyre = new ArrayList<>();
                        Map<String, Object> map = new HashMap<>();
                        map.put("check_code", "0");
                        map.put("check_status", "OK");
                        itemListMoTyre.add(map);
                        selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMoTyre, "", "", 0);
                        return selectResult;
                    } else {
                        //拼接返回值
                        ArrayList itemListMoTyre = new ArrayList<>();
                        Map<String, Object> map = new HashMap<>();
                        map.put("check_code", "100");
                        map.put("check_status", "OK");
                        itemListMoTyre.add(map);
                        selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMoTyre, "", "", 0);
                        return selectResult;
                    }
                } else {
                    //拼接返回值
                    ArrayList itemListMoTyre = new ArrayList<>();
                    Map<String, Object> map = new HashMap<>();
                    map.put("check_code", "-2");
                    map.put("check_status", "NG");
                    itemListMoTyre.add(map);
                    selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMoTyre, "", "", 0);
                    return selectResult;
                }
            } else {
                //拼接返回值
                ArrayList itemListMoTyre = new ArrayList<>();
                Map<String, Object> map = new HashMap<>();
                map.put("check_code", "-2");
                map.put("check_status", "NG");
                itemListMoTyre.add(map);
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMoTyre, "", "", 0);
                return selectResult;
            }
        } catch (Exception ex) {
            errorMsg = "接口调用异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
