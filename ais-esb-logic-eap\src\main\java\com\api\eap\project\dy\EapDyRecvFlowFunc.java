package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;

/**
 * <p>
 * 定颖EAP接受流程功能函数
 * 1.ProductionInfoDownload:(在线)接受EAP下发任务
 * 2.LotCommandDownload:(在线)接受EAP通知下发任务全部完成命令
 * 3.JobRemoveRecoveryRequest:(在线)收板机接受EAP下发修正计划
 * 4.BcOffLineLotDownLoad:(本地)上游通知下游工单信息
 * 5.LotFinishDownLoad:(本地)结批完成
 * 6.EachPanelDataDownLoad:接受上游设备发送过来的当片信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapDyRecvFlowFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDySendFlowFunc eapDySendFlowFunc;
    @Autowired
    private EapDyRecvFlowFuncNew eapDyRecvFlowFuncNew;

    //1.(在线)接受EAP下发任务
    public JSONObject ProductionInfoDownload(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="ProductionInfoDownload";
        String esbInterfCode="ProductionInfoDownload";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        long station_id=0L;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String apsPlanTable="a_eap_aps_plan";
        try{
            //新版本逻辑
            if(eapDyInterfCommon.CheckDyVersion(3)){
                return eapDyRecvFlowFuncNew.ProductionInfoDownload(jsonParas,request,apiRoutePath);
            }
            //旧版本逻辑
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();

            //3.批量读取TAG数据
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OneCarMultyLotFlagTag="";
            String EapLHTParasTag="";
            String OnOffLineTag="";
            String UnLoadLinkFlagTag="";
            String InspectCountTag="";
            String WebUserIdTag="";
            String SysModelTag="";
            String NgPanelManualFlagTag="";
            String NgPanelPassFlagTag="";
            String ZanCunCcdFlagTag="";
            //值
            String OneCarMultyLotFlag="";//一批多车模式:0一车一批、1一车多批、2一批多车
            String EapLHTParas="";//EAP接口长宽高参数集合
            String OnOffLine="";//在线离线
            String UnLoadLinkFlag="";//投收联机模式
            Integer InspectCount=0;//首件数量
            String WebUserId="";//员工号
            String SysModel="";//本地远程
            String NgPanelManualFlag="";//扫描Panel的NG是否需要人工处理
            String NgPanelPassFlag="";//NG板件时强制放行标识(0不启用、1启用)
            String ZanCunCcdFlag="";//载具读码是否提前
            if (aisMonitorModel.equals("AIS-PC")) {
                OneCarMultyLotFlagTag=station_attr+"Ais/AisConfig/OneCarMultyLotFlag";
                EapLHTParasTag=station_attr+"Ais/AisConfig/EapLHTParas";
                OnOffLineTag=station_attr+"Plc/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais/AisConfig/UnLoadLinkFlag";
                InspectCountTag=station_attr+"Eap/EapStatus/InspectCount";
                WebUserIdTag=station_attr+"Ais/AisStatus/WebUserId";
                SysModelTag=station_attr+"Plc/PlcConfig/SysModel";
                NgPanelManualFlagTag = station_attr+"Ais/AisConfig/NgPanelManualFlag";
                NgPanelPassFlagTag = station_attr+"Ais/AisConfig/NgPanelPassFlag";
                ZanCunCcdFlagTag= station_attr+"Ais/AisConfig/ZanCunCcdFlag";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OneCarMultyLotFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/OneCarMultyLotFlag";
                EapLHTParasTag=station_attr+"Ais_"+station_code+"/AisConfig/EapLHTParas";
                OnOffLineTag=station_attr+"Plc_"+station_code+"/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/UnLoadLinkFlag";
                InspectCountTag=station_attr+"Eap_"+station_code+"/EapStatus/InspectCount";
                WebUserIdTag=station_attr+"Ais_"+station_code+"/AisStatus/WebUserId";
                SysModelTag=station_attr+"Plc_"+station_code+"/PlcConfig/SysModel";
                NgPanelManualFlagTag = station_attr+"Ais_"+station_code+"/AisConfig/NgPanelManualFlag";
                NgPanelPassFlagTag = station_attr+"Ais_"+station_code+"/AisConfig/NgPanelPassFlag";
                ZanCunCcdFlagTag= station_attr+"Ais_"+station_code+"/AisConfig/ZanCunCcdFlag";
            }
            else{
                code=-3;
                errorMsg="AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String sumReadTags=OneCarMultyLotFlagTag+","+EapLHTParasTag+","+OnOffLineTag+","+UnLoadLinkFlagTag+","+InspectCountTag+","+WebUserIdTag+","+SysModelTag+
                    ","+NgPanelManualFlagTag+","+NgPanelPassFlagTag+","+ZanCunCcdFlagTag;
            JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,sumReadTags);
            if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                for(int i=0;i<jsonArrayTag.size();i++){
                    JSONObject jbItem=jsonArrayTag.getJSONObject(i);
                    String tag_key=jbItem.getString("tag_key");
                    String tag_value=jbItem.getString("tag_value");
                    if(tag_key.equals(OneCarMultyLotFlagTag)) OneCarMultyLotFlag=tag_value;
                    if(tag_key.equals(EapLHTParasTag)) EapLHTParas=tag_value;
                    if(tag_key.equals(OnOffLineTag)) OnOffLine=tag_value;
                    if(tag_key.equals(UnLoadLinkFlagTag)) UnLoadLinkFlag=tag_value;
                    if(tag_key.equals(InspectCountTag)) InspectCount=Integer.parseInt(tag_value);
                    if(tag_key.equals(WebUserIdTag)) WebUserId=tag_value;
                    if(tag_key.equals(SysModelTag)) SysModel=tag_value;
                    if(tag_key.equals(NgPanelManualFlagTag)) NgPanelManualFlag=tag_value;
                    if(tag_key.equals(NgPanelPassFlagTag)) NgPanelPassFlag=tag_value;
                    if(tag_key.equals(ZanCunCcdFlagTag)) ZanCunCcdFlag=tag_value;
                }
            }

            //3.1 判断是否断网
            if(OneCarMultyLotFlag==null || OneCarMultyLotFlag.equals("") ||
                    OnOffLine==null || OnOffLine.equals("") ||
                    UnLoadLinkFlag==null || UnLoadLinkFlag.equals("") ||
                    SysModel==null || SysModel.equals("")){
                code=-4;
                errorMsg="读取AIS配置参数时,SCADA断网,请重试";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.2 若是离线模式不接受任务
            if(!OnOffLine.equals("1")){
                code=-5;
                errorMsg="设备当前为离线模式,AIS拒绝接受工单下发";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(!SysModel.equals("1")){
                code=-5;
                errorMsg="设备当前为本地模式,AIS拒绝接受远程工单下发";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.3 若是收扳机,且是联机模式也拒绝接受任务
            if(station_attr.equals("UnLoad") && UnLoadLinkFlag.equals("1")){
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"仅作提醒,投收联机模式不需EAP下发任务");
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message","仅作提醒,投收联机模式不需EAP下发任务");
                return jbResult;
            }

            //3.4 判断EAP接口长宽高参数集合设置是否正确
            if(EapLHTParas==null || EapLHTParas.equals("")) EapLHTParas="S001,S002,S007";
            String[] panel_attr_list=EapLHTParas.split(",",-1);
            if(panel_attr_list==null || panel_attr_list.length<3){
                code=-6;
                errorMsg="未设置正确AIS配置参数{EAP接口长宽高参数集合}值";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String colname_length=panel_attr_list[0];//板长item_id名称
            String colname_width=panel_attr_list[1];//板宽item_id名称
            String colname_tickness=panel_attr_list[2];//板厚item_id名称

            //4.数据解析
            String lot_num=jbRecvBody.getString("lot_id");//批次号
            String material_code=jbRecvBody.getString("prod_id");//產品代碼
            Integer plan_count=jbRecvBody.getInteger("pnl_count");//基板数量
            String lot_short_num=jbRecvBody.getString("lot_short_id");//批次短代码
            String lot_version=jbRecvBody.getString("prod_version");//版號
            String attribute1=jbRecvBody.getString("process_code");//製程代碼
            String attribute2=jbRecvBody.getString("use_in_name");//產品用途
            JSONObject lot_infos=jbRecvBody.getJSONObject("lot_infos");
            if(lot_infos==null){
                code=-7;
                errorMsg="lot_infos为null数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            JSONArray lot_attr_list=lot_infos.getJSONArray("lot");//item_id、item_value
            String lot_attr_list_str="[]";
            if(lot_attr_list==null || lot_attr_list.size()<=0){
                code=-8;
                errorMsg="lot_infos属性数据为空,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            lot_attr_list_str=lot_attr_list.toString();
            String panel_length="0";
            String panel_width="0";
            String panel_tickness="0";
            Integer panel_model=-1;
            String csvFtpPath="";
            String DySmallPanelFlag=cFuncDbSqlResolve.GetParameterValue("Dy_SmallPanel");
            for(int i=0;i<lot_attr_list.size();i++){
                JSONObject jbItem=lot_attr_list.getJSONObject(i);
                String item_id=jbItem.getString("item_id");
                String item_value=jbItem.getString("item_value");
                if(item_id.equals(colname_length)) panel_length=item_value;
                else if(item_id.equals(colname_width)) panel_width=item_value;
                else if(item_id.equals(colname_tickness)) panel_tickness=item_value;
                else if(item_id.equals("S005")){
                    if(DySmallPanelFlag.equals("Y")) csvFtpPath=item_value;
                }
                else if (item_id.equals("S021")) {
                    //0：开启各种校验，1：读码强制放行
                    if (item_value.equals("0") || item_value.equals("1")) {
                        panel_model=Integer.parseInt(item_value);
                    }
                }
                else if(item_id.equals("S004") && DySmallPanelFlag.equals("Y")){
                    //1：开启有panel模式，2：无Panel模式
                    if (item_value.equals("1") || item_value.equals("2")) {
                        if(item_value.equals("1")) panel_model=0;
                        else panel_model=1;
                    }
                }
            }
            if(panel_length==null || panel_length.equals("")) panel_length="0";
            if(panel_width==null || panel_width.equals("")) panel_width="0";
            if(panel_tickness==null || panel_tickness.equals("")) panel_tickness="0";
            if(Float.parseFloat(panel_length)<=0 || Float.parseFloat(panel_width)<=0){
                code=-9;
                errorMsg="EAP下发任务中板长{"+panel_length+"}与板宽{"+panel_width+"}小于等于0,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(lot_num==null || lot_num.equals("") || lot_short_num==null || lot_short_num.equals("") || plan_count==null || plan_count<=0){
                code=-10;
                errorMsg="下发任务中lot_id|lot_short_id|pnl_count参数字段不能为空或者不能为0";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //4.1 判断是否存在重复的lot，若存在则不执行
            String[] group_lot_status_list=new String[]{"WAIT","PLAN","WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            long planCount =  mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
            if(planCount>0){
                String message="仅作提醒,工单{"+lot_num+"}已存在且未完成,EAP无需重复下发";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,message);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message",message);
                return jbResult;
            }

            //4.2 若是一批多车时,若有放扳机任务未完结,此时不允许插入数据
            //modified by chenru20240327 收板机不考虑是否一批多车模式
            Integer lastCarryStatus=0;
            if(station_attr.equals("Load") && OneCarMultyLotFlag.equals("2")){
                if(ZanCunCcdFlag.equals("1")){
                    //1.开启提前读码,判断最近的工单是否为最后一车
                    Integer last_carry_status=0;
                    Boolean exsit_lot=false;
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                    queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if(iteratorBigData.hasNext()){
                        exsit_lot=true;
                        Document docItemBigData = iteratorBigData.next();
                        last_carry_status=docItemBigData.getInteger("pdb_rule");
                        iteratorBigData.close();
                    }
                    if(exsit_lot && last_carry_status!=1){
                        code=-11;
                        errorMsg="当前任务非最后一个载具,不允许下发新的工单";
                        opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                        responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                        jbResult.put("responseParas",responseParas);
                        jbResult.put("successFlag",false);
                        jbResult.put("message",errorMsg);
                        return jbResult;
                    }
                    Thread.sleep(500);//等待500ms再查询
                    //读取点位
                    String lastCarryStatusStr= opCommonFunc.ReadCellOneRedisValue(station_code,station_attr,
                            "Eap","EapStatus","LastCarryStatus");
                    lastCarryStatus=Integer.parseInt(lastCarryStatusStr);
                }
                else{
                    //其他逻辑
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                    planCount =  mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
                    if(planCount>0){
                        code=-11;
                        errorMsg="当前作业模式为一批多车模式,设备有尚未完成工单,不允许下发新的工单";
                        opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                        responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                        jbResult.put("responseParas",responseParas);
                        jbResult.put("successFlag",false);
                        jbResult.put("message",errorMsg);
                        return jbResult;
                    }
                }
            }

            //处理小板FTP数据
            if(DySmallPanelFlag.equals("Y") && !csvFtpPath.equals("") && !csvFtpPath.equals("NA")){
                try{
                    String meLaserTable="a_eap_me_laser";
                    BufferedReader br = new BufferedReader(new FileReader(csvFtpPath));
                    String line;
                    int lineCount=0;
                    Date item_date_laser = CFuncUtilsSystem.GetMongoISODate("");
                    long item_date_val_laser=CFuncUtilsSystem.GetMongoDataValue(item_date_laser);
                    List<Map<String, Object>> lstLaserDocuments = new ArrayList<>();
                    while ((line = br.readLine()) != null) {
                        lineCount++;
                        if(lineCount % 2==0){
                            String[] columns = line.split(",");
                            if(columns!=null && columns.length>=5){
                                String laser_time=columns[0];//时间
                                String laser_panel_id=columns[1];//PNL码
                                String laser_barcode=columns[2];//镭刻码(小板读取的是镭刻码)
                                String laser_array_id=columns[3];//ARRAY_ID
                                String laser_pcs_id=columns[4];//PCS_ID
                                String laser_id=CFuncUtilsSystem.CreateUUID(true);
                                Map<String, Object> mapLaserRow = new HashMap<>();
                                mapLaserRow.put("item_date",item_date_laser);
                                mapLaserRow.put("item_date_val",item_date_val_laser);
                                mapLaserRow.put("laser_id",laser_id);
                                mapLaserRow.put("station_id",station_id);
                                mapLaserRow.put("group_lot_num","");
                                mapLaserRow.put("lot_num",lot_num);
                                mapLaserRow.put("laser_time",laser_time);
                                mapLaserRow.put("laser_panel_id",laser_panel_id);
                                mapLaserRow.put("laser_barcode",laser_barcode);
                                mapLaserRow.put("laser_array_id",laser_array_id);
                                mapLaserRow.put("laser_pcs_id",laser_pcs_id);
                                lstLaserDocuments.add(mapLaserRow);
                            }
                        }
                    }
                    if (lstLaserDocuments.size() > 0) mongoTemplate.insert(lstLaserDocuments, meLaserTable);
                }
                catch (Exception exLaser){
                    log.warn("解析下发CSV文件:"+exLaser);
                }
            }

            //4.3 插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            String plan_id=CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("plan_id",plan_id);
            mapBigDataRow.put("station_id",station_id);
            mapBigDataRow.put("task_from","EAP");
            mapBigDataRow.put("group_lot_num","");
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("lot_short_num",lot_short_num);
            mapBigDataRow.put("lot_index",1);
            mapBigDataRow.put("plan_lot_count",plan_count);
            mapBigDataRow.put("target_lot_count",plan_count);
            mapBigDataRow.put("port_code","");
            mapBigDataRow.put("material_code",material_code);
            mapBigDataRow.put("pallet_num","");
            mapBigDataRow.put("pallet_type","");
            mapBigDataRow.put("lot_level",lot_version);
            mapBigDataRow.put("panel_length",Double.parseDouble(panel_length));
            mapBigDataRow.put("panel_width",Double.parseDouble(panel_width));
            mapBigDataRow.put("panel_tickness",Double.parseDouble(panel_tickness));
            mapBigDataRow.put("panel_model",panel_model);
            mapBigDataRow.put("inspect_count",InspectCount);
            mapBigDataRow.put("inspect_finish_count",0);
            mapBigDataRow.put("pdb_count",0);
            mapBigDataRow.put("pdb_rule",0);//新增应该直接用0:lastCarryStatus
            mapBigDataRow.put("fp_count",0);
            mapBigDataRow.put("group_lot_status","WAIT");
            mapBigDataRow.put("lot_status","PLAN");
            mapBigDataRow.put("finish_count",0);
            mapBigDataRow.put("finish_ok_count",0);
            mapBigDataRow.put("finish_ng_count",0);
            mapBigDataRow.put("task_error_code",0);
            mapBigDataRow.put("item_info",lot_attr_list_str);
            mapBigDataRow.put("task_start_time",CFuncUtilsSystem.GetNowDateTime(""));
            mapBigDataRow.put("task_end_time","");
            mapBigDataRow.put("task_cost_time",(long)0);
            mapBigDataRow.put("attribute1",attribute1);
            mapBigDataRow.put("attribute2",attribute2);
            mapBigDataRow.put("attribute3","");
            mapBigDataRow.put("face_code",0);
            mapBigDataRow.put("pallet_use_count",0);
            mapBigDataRow.put("user_name",WebUserId);
            mapBigDataRow.put("group_id","");
            mapBigDataRow.put("offline_flag","N");
            mapBigDataRow.put("target_update_count",0);
            mapBigDataRow.put("pnl_infos","");
            mongoTemplate.insert(mapBigDataRow,apsPlanTable);

            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //2.(在线)接受EAP通知下发任务全部完成命令
    public JSONObject LotCommandDownload(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="LotCommandDownload";
        String esbInterfCode="LotCommandDownload";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        long station_id=0L;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String apsPlanTable="a_eap_aps_plan";
        try{
            //新版本逻辑
            if(eapDyInterfCommon.CheckDyVersion(3)){
                return eapDyRecvFlowFuncNew.LotCommandDownload(jsonParas,request,apiRoutePath);
            }

            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0","ProductionInfoDownload","AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0","ProductionInfoDownload","AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();

            //3.批量读取TAG数据
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OneCarMultyLotFlagTag="";
            String OnOffLineTag="";
            String UnLoadLinkFlagTag="";
            String SysModelTag="";
            String WebUserIdTag="";
            String ZanCunCcdFlagTag="";
            //值
            String OneCarMultyLotFlag="";//一批多车模式:0一车一批、1一车多批、2一批多车
            String OnOffLine="";//在线离线
            String UnLoadLinkFlag="";//投收联机模式
            String SysModel="";//本地远程模式
            String WebUserId="";//员工号
            String ZanCunCcdFlag="";//暂存是否提前
            if (aisMonitorModel.equals("AIS-PC")) {
                OneCarMultyLotFlagTag=station_attr+"Ais/AisConfig/OneCarMultyLotFlag";
                OnOffLineTag=station_attr+"Plc/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais/AisConfig/UnLoadLinkFlag";
                SysModelTag=station_attr+"Plc/PlcConfig/SysModel";
                WebUserIdTag=station_attr+"Ais/AisStatus/WebUserId";
                ZanCunCcdFlagTag=station_attr+"Ais/AisConfig/ZanCunCcdFlag";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OneCarMultyLotFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/OneCarMultyLotFlag";
                OnOffLineTag=station_attr+"Plc_"+station_code+"/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/UnLoadLinkFlag";
                SysModelTag=station_attr+"Plc_"+station_code+"/PlcConfig/SysModel";
                WebUserIdTag=station_attr+"Ais_"+station_code+"/AisStatus/WebUserId";
                ZanCunCcdFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/ZanCunCcdFlag";
            }
            else{
                code=-3;
                errorMsg="AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                opCommonFunc.SaveCimMessage(station_id,"0","ProductionInfoDownload","AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String sumReadTags=OneCarMultyLotFlagTag+","+OnOffLineTag+","+UnLoadLinkFlagTag+","+SysModelTag+","+WebUserIdTag+","+ZanCunCcdFlagTag;
            JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,sumReadTags);
            if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                for(int i=0;i<jsonArrayTag.size();i++){
                    JSONObject jbItem=jsonArrayTag.getJSONObject(i);
                    String tag_key=jbItem.getString("tag_key");
                    String tag_value=jbItem.getString("tag_value");
                    if(tag_key.equals(OneCarMultyLotFlagTag)) OneCarMultyLotFlag=tag_value;
                    if(tag_key.equals(OnOffLineTag)) OnOffLine=tag_value;
                    if(tag_key.equals(UnLoadLinkFlagTag)) UnLoadLinkFlag=tag_value;
                    if(tag_key.equals(SysModelTag)) SysModel=tag_value;
                    if(tag_key.equals(WebUserIdTag)) WebUserId=tag_value;
                    if(tag_key.equals(ZanCunCcdFlagTag)) ZanCunCcdFlag=tag_value;
                }
            }

            //3.1 判断是否断网
            if(OneCarMultyLotFlag==null || OneCarMultyLotFlag.equals("") ||
                    OnOffLine==null || OnOffLine.equals("") ||
                    UnLoadLinkFlag==null || UnLoadLinkFlag.equals("") ||
                    SysModel==null || SysModel.equals("")){
                code=-4;
                errorMsg="读取AIS配置参数时,SCADA断网,请重试";
                opCommonFunc.SaveCimMessage(station_id,"0","ProductionInfoDownload","AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.2 若是离线模式不接受任务
            if(!OnOffLine.equals("1")){
                code=-5;
                errorMsg="设备当前为离线模式,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0","ProductionInfoDownload","AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(!SysModel.equals("1")){
                code=-5;
                errorMsg="设备当前为本地模式,AIS拒绝接受远程工单下发完成";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.3 若是收扳机,且是联机模式也拒绝接受任务
            if(station_attr.equals("UnLoad") && UnLoadLinkFlag.equals("1")){
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"仅作提醒,投收联机模式不需EAP下发任务命令");
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message","仅作提醒,投收联机模式不需EAP下发任务命令");
                return jbResult;
            }

            //4.数据解析
            String port_code=jbRecvBody.getString("port_no");//端口号
            String port_code2=jbRecvBody.getString("port_no");
            String port_type=jbRecvBody.getString("port_type");//端口属性[不使用,使用系统的]
            String pallet_num=jbRecvBody.getString("carrier_id");//载具号[不使用,用AIS系统的载具条码做更新]
            String port_command=jbRecvBody.getString("port_command");//命令
            JSONObject lot_infos=jbRecvBody.getJSONObject("lot_infos");
            if(lot_infos==null){
                code=-7;
                errorMsg="lot_infos为null数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0","ProductionInfoDownload","AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            JSONArray lot_attr_list=lot_infos.getJSONArray("lot");//item_id、item_value
            if(lot_attr_list==null || lot_attr_list.size()<=0){
                code=-8;
                errorMsg="lot_infos属性数据为空,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0","ProductionInfoDownload","AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //4.1 如果是放板机则需要判断端口是否正确
            if(station_attr.equals("Load")){
                //判断给出的端口号以及端口类型是否正确
                String sqlPort="select count(1) from a_eap_fmod_station_port " +
                        "where station_id="+station_id+" and port_code='"+port_code+"'";
                Integer portCount=cFuncDbSqlResolve.GetSelectCount(sqlPort);
                if(portCount<=0){
                    code=-6;
                    errorMsg="传递的设备端口号{"+port_code+"}不存在设备基础数据,AIS系统拒绝执行";
                    opCommonFunc.SaveCimMessage(station_id,"0","ProductionInfoDownload","AIS",errorMsg,5);
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
            }
            else port_code="";//收扳机自由选择端口号

            //4.2 启动
            Boolean isNewVersion=eapDyInterfCommon.CheckDyVersion(3);
            Query queryBigData = new Query();
            Update updateBigData = new Update();
            if(port_command.contains("Start")){
                String lot_group_num=CFuncUtilsSystem.GetOnlySign("LG");
                String group_id=CFuncUtilsSystem.CreateUUID(true);

                //获取全部的PNL过期信息,新增过期集合数据 240315 by ZhouJun
                JSONArray pnl_infos=new JSONArray();
                String pnl_infos_str="";
                String first_lot_num="";
                String judgeBigLotFlag=cFuncDbSqlResolve.GetParameterValue("Dy_JudgeBigLotFlag");
                for(int i=0;i<lot_attr_list.size();i++){
                    JSONObject jbItem=lot_attr_list.getJSONObject(i);

                    //新增同一大批次同一层别
                    String lot_num=jbItem.getString("lot_id");
                    if(i==0) first_lot_num=lot_num;
                    else{
                        if(first_lot_num.length()>=18 && lot_num.length()>=18 && judgeBigLotFlag.equals("Y")){
                            String first_lot_num_biglot=first_lot_num.substring(0,10);
                            String first_lot_num_level=first_lot_num.substring(16,18);
                            String compat_lot_num_biglot=lot_num.substring(0,10);
                            String compat_lot_num_level=lot_num.substring(16,18);
                            if(!first_lot_num_biglot.equals(compat_lot_num_biglot)){
                                code=-21;
                                errorMsg="EAP下发工单{"+lot_num+"}与首工单{"+first_lot_num+"}不属于同一大批";
                                opCommonFunc.SaveCimMessage(0L,"0",
                                        "ProductionInfoDownload","AIS",errorMsg,5);
                                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                                jbResult.put("responseParas",responseParas);
                                jbResult.put("successFlag",false);
                                jbResult.put("message",errorMsg);
                                return jbResult;
                            }
                            if(!first_lot_num_level.equals(compat_lot_num_level)){
                                code=-22;
                                errorMsg="EAP下发工单{"+lot_num+"}与首工单{"+first_lot_num+"}不属于同一层次";
                                opCommonFunc.SaveCimMessage(0L,"0",
                                        "ProductionInfoDownload","AIS",errorMsg,5);
                                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                                jbResult.put("responseParas",responseParas);
                                jbResult.put("successFlag",false);
                                jbResult.put("message",errorMsg);
                                return jbResult;
                            }
                        }
                    }

                    if(jbItem.containsKey("pnl_infos")){
                        JSONObject jbItem_pnl_infos=jbItem.getJSONObject("pnl_infos");
                        if(jbItem_pnl_infos!=null){
                            JSONArray pnlItem=jbItem_pnl_infos.getJSONArray("pnl");
                            if(pnlItem!=null && pnlItem.size()>0){
                                for(int j=0;j<pnlItem.size();j++){
                                    JSONObject jbItemPnl=pnlItem.getJSONObject(j);
                                    if(isNewVersion){
                                        if(jbItemPnl!=null && jbItemPnl.containsKey("pnl_id") && jbItemPnl.containsKey("pnl_store_dt")){
                                            JSONObject jbNew=new JSONObject();
                                            jbNew.put("panel_id",jbItemPnl.getString("pnl_id"));
                                            jbNew.put("panel_time",jbItemPnl.getString("pnl_store_dt"));
                                            pnl_infos.add(jbNew);
                                        }
                                    }
                                    else{
                                        if(jbItemPnl!=null && jbItemPnl.containsKey("panel_id") && jbItemPnl.containsKey("panel_time")){
                                            pnl_infos.add(jbItemPnl);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if(pnl_infos!=null && pnl_infos.size()>0) pnl_infos_str=pnl_infos.toString();

                //
                List<String> lot_num_list=new ArrayList<>();
                for(int i=0;i<lot_attr_list.size();i++){
                    JSONObject jbItem=lot_attr_list.getJSONObject(i);
                    String lot_num=jbItem.getString("lot_id");
                    if(!lot_num_list.contains(lot_num)) lot_num_list.add(lot_num);
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                    updateBigData = new Update();
                    updateBigData.set("lot_index", i+1);
                    if(station_attr.equals("Load")){
                        updateBigData.set("port_code", port_code);
                    }
                    updateBigData.set("group_lot_num", lot_group_num);
                    updateBigData.set("group_id", group_id);
                    updateBigData.set("pnl_infos", pnl_infos_str);
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }
                //全部更新后统一修改状态
                String last_group_lot_status="PLAN";
                if(OneCarMultyLotFlag.equals("2") && ZanCunCcdFlag.equals("1") && !station_attr.equals("UnLoad")){
                    last_group_lot_status="WAIT";
                }
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                updateBigData = new Update();
                updateBigData.set("group_lot_status", last_group_lot_status);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

                //判断是否为小板
                String DySmallPanelFlag=cFuncDbSqlResolve.GetParameterValue("Dy_SmallPanel");
                if(DySmallPanelFlag.equals("Y")){
                    String meLaserTable="a_eap_me_laser";
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("lot_num").in(lot_num_list));
                    updateBigData = new Update();
                    updateBigData.set("group_lot_num", lot_group_num);
                    mongoTemplate.updateMulti(queryBigData, updateBigData, meLaserTable);
                    //若是投收联机,则需要将数据同步到收扳机
                    if(station_attr.equals("Load") && UnLoadLinkFlag.equals("1")){
                        eapDyRecvFlowFuncNew.AysnLaserDataToUnLoad(station_id,lot_group_num);
                    }
                }

                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message","接受成功");
                return jbResult;
            }

            //4.3 取消任务
            if(port_command.contains("Cancel")) {
                JSONArray lot_list = new JSONArray();
                for (int i = 0; i < lot_attr_list.size(); i++) {
                    JSONObject jbItem = lot_attr_list.getJSONObject(i);
                    String lot_num = jbItem.getString("lot_id");
                    Integer lot_count = 0;
                    if (isNewVersion) lot_count = jbItem.getInteger("lot_qty");
                    else lot_count = jbItem.getInteger("lot_count");

                    JSONObject jbItem2 = new JSONObject();
                    jbItem2.put("lot_id", lot_num);
                    jbItem2.put("lot_count", lot_count);
                    lot_list.add(jbItem2);

                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                    updateBigData = new Update();
                    updateBigData.set("group_lot_status", "CANCEL");
                    updateBigData.set("lot_status", "CANCEL");
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }

                //上报BCCanel
                if (isNewVersion){
                    eapDySendFlowFunc.CarrierStatusReport(station_code, pallet_num, "CancelBC", lot_list, "0",station_attr,port_code2,"N");
                    if(station_attr.equals("Load")){
                        //发送强制退载具
                        opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                                "Plc","PlcStatus",
                                "AisPort"+Integer.parseInt(port_code2)+"BackRequest",
                                "EAP","1",false);
                    }
                }
                else eapDySendFlowFunc.CarrierStatusReport(station_code, pallet_num, "BCCanel", lot_list, "0",station_attr,port_code2,"N");

                //若是一批多车,且任务不存在
                if (OneCarMultyLotFlag.equals("2")) {
                    String[] group_lot_status_list = new String[]{"WAIT", "PLAN", "WORK"};
                    JSONObject jbItem2 = lot_attr_list.getJSONObject(0);
                    String lot_num2 = jbItem2.getString("lot_id");
                    Integer lot_count2 = jbItem2.getInteger("lot_count");
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num2));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                    long planCount2 = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
                    if (planCount2 <= 0) {
                        //端口状态上报--等方法开发完成
                        Integer left_count = 0;
                        if (station_attr.equals("Load")) left_count = lot_count2;
                        eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "CANE", "N",
                                pallet_num, String.valueOf(left_count), WebUserId, "N", "N","",station_attr);
                        eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "UDRQ", "N",
                                pallet_num, String.valueOf(left_count), WebUserId, "N", "N","",station_attr);
                    }
                }

                responseParas = eapDyInterfCommon.CreateResponseHead01(function_name, request_uuid, "OK", code, "");
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "接受成功");
            }

            if(port_command.contains("Abort")) {
                JSONArray lot_list = new JSONArray();
                for (int i = 0; i < lot_attr_list.size(); i++) {
                    JSONObject jbItem = lot_attr_list.getJSONObject(i);
                    String lot_num = jbItem.getString("lot_id");
                    Integer lot_count = 0;
                    if (isNewVersion) lot_count = jbItem.getInteger("lot_qty");
                    else lot_count = jbItem.getInteger("lot_count");

                    JSONObject jbItem2 = new JSONObject();
                    jbItem2.put("lot_id", lot_num);
                    jbItem2.put("lot_count", lot_count);
                    lot_list.add(jbItem2);

                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    updateBigData = new Update();
                    updateBigData.set("group_lot_status", "ABORT");
                    updateBigData.set("lot_status", "ABORT");
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }

                //上报BCCanel
                if (isNewVersion){
                    eapDySendFlowFunc.CarrierStatusReport(station_code, pallet_num, "CancelBC", lot_list, "0",station_attr,port_code2,"N");
                    if(station_attr.equals("Load")){
                        //发送强制退载具
                        opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                                "Plc","PlcStatus",
                                "AisPort"+Integer.parseInt(port_code2)+"BackRequest",
                                "EAP","1",false);
                    }
                }
                else eapDySendFlowFunc.CarrierStatusReport(station_code, pallet_num, "BCCanel", lot_list, "0",station_attr,port_code2,"N");

                //若是一批多车,且任务不存在
                if (OneCarMultyLotFlag.equals("2")) {
                    String[] group_lot_status_list = new String[]{"WAIT", "PLAN", "WORK"};
                    JSONObject jbItem2 = lot_attr_list.getJSONObject(0);
                    String lot_num2 = jbItem2.getString("lot_id");
                    Integer lot_count2 = jbItem2.getInteger("lot_count");
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num2));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                    long planCount2 = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
                    if (planCount2 <= 0) {
                        //端口状态上报--等方法开发完成
                        Integer left_count = 0;
                        if (station_attr.equals("Load")) left_count = lot_count2;
                        eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "CANE", "N",
                                pallet_num, String.valueOf(left_count), WebUserId, "N", "N","",station_attr);
                        eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "UDRQ", "N",
                                pallet_num, String.valueOf(left_count), WebUserId, "N", "N","",station_attr);
                    }
                }

                responseParas = eapDyInterfCommon.CreateResponseHead01(function_name, request_uuid, "OK", code, "");
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "接受成功");
            }
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //3.(在线)收板机接受EAP下发修正计划
    public JSONObject JobRemoveRecoveryRequest(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="JobRemoveRecoveryRequest";
        String esbInterfCode="JobRemoveRecoveryRequest";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        long station_id=0L;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String apsPlanTable="a_eap_aps_plan";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"' and station_attr='UnLoad'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的收扳机设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();

            //3.批量读取TAG数据
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OnOffLineTag="";
            String UnLoadLinkFlagTag="";
            String SysModelTag="";
            //值
            String OnOffLine="";//在线离线
            String UnLoadLinkFlag="";//投收联机模式
            String SysModel="";//本地远程模式
            if (aisMonitorModel.equals("AIS-PC")) {
                OnOffLineTag=station_attr+"Plc/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais/AisConfig/UnLoadLinkFlag";
                SysModelTag=station_attr+"Plc/PlcConfig/SysModel";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OnOffLineTag=station_attr+"Plc_"+station_code+"/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/UnLoadLinkFlag";
                SysModelTag=station_attr+"Plc_"+station_code+"/PlcConfig/SysModel";
            }
            else{
                code=-3;
                errorMsg="AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String sumReadTags=OnOffLineTag+","+UnLoadLinkFlagTag+","+SysModelTag;
            JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,sumReadTags);
            if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                for(int i=0;i<jsonArrayTag.size();i++){
                    JSONObject jbItem=jsonArrayTag.getJSONObject(i);
                    String tag_key=jbItem.getString("tag_key");
                    String tag_value=jbItem.getString("tag_value");
                    if(tag_key.equals(OnOffLineTag)) OnOffLine=tag_value;
                    if(tag_key.equals(UnLoadLinkFlagTag)) UnLoadLinkFlag=tag_value;
                    if(tag_key.equals(SysModelTag)) SysModel=tag_value;
                }
            }

            //3.1 判断是否断网
            if(OnOffLine==null || OnOffLine.equals("") ||
                    UnLoadLinkFlag==null || UnLoadLinkFlag.equals("") ||
                    SysModel==null || SysModel.equals("")){
                code=-4;
                errorMsg="读取AIS配置参数时,SCADA断网,请重试";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.2 若是离线模式不接受任务
            if(!OnOffLine.equals("1")){
                code=-5;
                errorMsg="设备当前为离线模式,AIS拒绝接受工单数量变更";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(!SysModel.equals("1")){
                code=-5;
                errorMsg="设备当前为本地模式,AIS拒绝接受远程工单下发";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.3 若是收扳机,投收联机模式不需EAP下发工单数量变更
            if(station_attr.equals("UnLoad") && UnLoadLinkFlag.equals("1")){
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"仅作提醒,投收联机模式不需EAP下发工单数量变更");
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message","仅作提醒,投收联机模式不需EAP下发工单数量变更");
                return jbResult;
            }

            //4.数据解析
            String event_type=jbRecvBody.getString("event_type");//Remove和Recovery
            String lot_num=jbRecvBody.getString("lot_id");
            String panel_id=jbRecvBody.getString("panel_id");
            String slot_no=jbRecvBody.getString("slot_no");
            if(!event_type.equals("Remove") && !event_type.equals("Recovery")){
                code=-7;
                errorMsg="下发的event_type={"+event_type+"},只能为Remove或者Recovery,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //4.1 查询当前lot对应的任务数据
            String[] group_lot_status_list=new String[]{"WAIT","PLAN","WORK"};
            Query queryBigData = new Query();
            Update updateBigData = new Update();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(!iteratorBigData.hasNext()){
                code=-8;
                errorMsg="收扳机未下发工单{"+lot_num+"}";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            Document docItemBigData = iteratorBigData.next();
            String group_lot_num=docItemBigData.getString("group_lot_num");
            String plan_id=docItemBigData.getString("plan_id");
            Integer target_lot_count=docItemBigData.getInteger("target_lot_count");
            String group_lot_status=docItemBigData.getString("group_lot_status");
            String port_code=docItemBigData.getString("port_code");
            iteratorBigData.close();
            //4.3 读取当前组总共数量
            Integer sum_target_lot_count=0;
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while(iteratorBigData.hasNext()){
                docItemBigData = iteratorBigData.next();
                Integer target_lot_count2=docItemBigData.getInteger("target_lot_count");
                sum_target_lot_count+=target_lot_count2;
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();

            //4.3 计算数量
            if(event_type.equals("Remove")){
                target_lot_count=target_lot_count-1;
                sum_target_lot_count=sum_target_lot_count-1;
                if(target_lot_count<0) target_lot_count=0;
                if(sum_target_lot_count<0) sum_target_lot_count=0;
            }
            else{
                target_lot_count=target_lot_count+1;
                sum_target_lot_count=sum_target_lot_count+1;
            }

            //4.4 若是WORK状态,先取消任务
            if(sum_target_lot_count<=0 && group_lot_status.equals("WORK")){
                errorMsg=opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Ais","AisStatus","TaskCancelRequest","EAP","1",true);
                if(!errorMsg.equals("")){
                    code=-9;
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
            }

            //4.5 更新数量
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            updateBigData = new Update();
            updateBigData.set("target_lot_count", target_lot_count);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //4.6 判断数量,若总数量<=0,则直接将任务取消掉
            if(sum_target_lot_count<=0){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                updateBigData = new Update();
                updateBigData.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            else{
                if(group_lot_status.equals("WORK")){
                    String sqlPort="select port_index " +
                            "from a_eap_fmod_station_port " +
                            "where station_id="+station_id+" and port_code='"+port_code+"'";
                    List<Map<String, Object>> lstPort=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlPort,false,request,apiRoutePath);
                    String port_index=lstPort.get(0).get("port_index").toString();
                    String tagUpdateLotCountStatus="";
                    String tagUpdateLotCount="";
                    if(aisMonitorModel.equals("AIS-PC")){
                        tagUpdateLotCountStatus="UnLoadPlc/AisReqControl"+port_index+"/UpdateLotCountStatus";
                        tagUpdateLotCount="UnLoadPlc/AisReqControl"+port_index+"/UpdateLotCount";
                    }
                    else if(aisMonitorModel.equals("AIS-SERVER")){
                        tagUpdateLotCountStatus="UnLoadPlc_"+station_code+"/AisReqControl"+port_index+"/UpdateLotCountStatus";
                        tagUpdateLotCount="UnLoadPlc_"+station_code+"/AisReqControl"+port_index+"/UpdateLotCount";
                    }
                    String tagOnlyKeyList=tagUpdateLotCount+","+tagUpdateLotCountStatus;
                    String tagValueList=sum_target_lot_count+"&1";
                    errorMsg= cFuncUtilsCellScada.WriteTagByStation(station_code,station_code,tagOnlyKeyList,tagValueList,true);
                    if(!errorMsg.equals("")){
                        code=-10;
                        opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                        responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                        jbResult.put("responseParas",responseParas);
                        jbResult.put("successFlag",false);
                        jbResult.put("message",errorMsg);
                        return jbResult;
                    }
                }
            }

            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //4.(本地)上游通知下游工单信息(上游通知收扳机接受工单信息)
    public JSONObject BcOffLineLotDownLoad(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="BcOffLineLotDownLoad";
        String esbInterfCode="BcOffLineLotDownLoad";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        long station_id=0L;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String apsPlanTable="a_eap_aps_plan";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断收板工位是否存在
            String sqlStation="select station_code," +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_attr='UnLoad' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="不存在AIS收板设备基础数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();
            station_code=itemListStation.get(0).get("station_code").toString();

            //3.批量读取TAG数据
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OneCarMultyLotFlagTag="";
            String EapLHTParasTag="";
            String OnOffLineTag="";
            String UnLoadLinkFlagTag="";
            String WebUserIdTag="";
            String SysModelTag="";
            //值
            String OneCarMultyLotFlag="";//一批多车模式:0一车一批、1一车多批、2一批多车
            String EapLHTParas="";//EAP接口长宽高参数集合
            String OnOffLine="";//在线离线
            String UnLoadLinkFlag="";//投收联机模式
            String WebUserId="";//员工号
            String SysModel="";//本地远程模式
            if (aisMonitorModel.equals("AIS-PC")) {
                OneCarMultyLotFlagTag=station_attr+"Ais/AisConfig/OneCarMultyLotFlag";
                EapLHTParasTag=station_attr+"Ais/AisConfig/EapLHTParas";
                OnOffLineTag=station_attr+"Plc/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais/AisConfig/UnLoadLinkFlag";
                WebUserIdTag=station_attr+"Ais/AisStatus/WebUserId";
                SysModelTag=station_attr+"Plc/PlcConfig/SysModel";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OneCarMultyLotFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/OneCarMultyLotFlag";
                EapLHTParasTag=station_attr+"Ais_"+station_code+"/AisConfig/EapLHTParas";
                OnOffLineTag=station_attr+"Plc_"+station_code+"/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/UnLoadLinkFlag";
                WebUserIdTag=station_attr+"Ais_"+station_code+"/AisStatus/WebUserId";
                SysModelTag=station_attr+"Plc_"+station_code+"/PlcConfig/SysModel";
            }
            else{
                code=-3;
                errorMsg="AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String sumReadTags=OneCarMultyLotFlagTag+","+EapLHTParasTag+","+OnOffLineTag+","+UnLoadLinkFlagTag+","+WebUserIdTag+","+SysModelTag;
            JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,sumReadTags);
            if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                for(int i=0;i<jsonArrayTag.size();i++){
                    JSONObject jbItem=jsonArrayTag.getJSONObject(i);
                    String tag_key=jbItem.getString("tag_key");
                    String tag_value=jbItem.getString("tag_value");
                    if(tag_key.equals(OneCarMultyLotFlagTag)) OneCarMultyLotFlag=tag_value;
                    if(tag_key.equals(EapLHTParasTag)) EapLHTParas=tag_value;
                    if(tag_key.equals(OnOffLineTag)) OnOffLine=tag_value;
                    if(tag_key.equals(UnLoadLinkFlagTag)) UnLoadLinkFlag=tag_value;
                    if(tag_key.equals(WebUserIdTag)) WebUserId=tag_value;
                    if(tag_key.equals(SysModelTag)) SysModel=tag_value;
                }
            }

            //3.1 判断是否断网
            if(OneCarMultyLotFlag==null || OneCarMultyLotFlag.equals("") ||
                    OnOffLine==null || OnOffLine.equals("") ||
                    UnLoadLinkFlag==null || UnLoadLinkFlag.equals("") ||
                    SysModel==null || SysModel.equals("")){
                code=-4;
                errorMsg="读取AIS配置参数时,SCADA断网,请重试";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.2 若是离线模式不接受任务
            if(!OnOffLine.equals("1")){
                code=-5;
                errorMsg="设备当前为在线模式,AIS拒绝接受离线工单下发";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(SysModel.equals("1")){
                code=-5;
                errorMsg="设备当前为EAP远程模式,AIS拒绝接受上游工单下发";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.3 若是收扳机,且是联机模式也拒绝接受任务
            if(station_attr.equals("UnLoad") && UnLoadLinkFlag.equals("1")){
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"仅作提醒,投收联机模式不需上游下发任务");
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message","仅作提醒,投收联机模式不需上游下发任务");
                return jbResult;
            }

            //3.4 判断EAP接口长宽高参数集合设置是否正确
            String[] panel_attr_list=EapLHTParas.split(",",-1);
            if(panel_attr_list==null || panel_attr_list.length<3){
                code=-6;
                errorMsg="未设置正确AIS配置参数{EAP接口长宽高参数集合}值";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String colname_length=panel_attr_list[0];//板长item_id名称
            String colname_width=panel_attr_list[1];//板宽item_id名称
            String colname_tickness=panel_attr_list[2];//板厚item_id名称

            //4.数据解析
            String lot_num=jbRecvBody.getString("lot_id");//批次号
            String material_code=jbRecvBody.getString("prod_id");//產品代碼
            Integer plan_count=jbRecvBody.getInteger("pnl_count");//基板数量
            String lot_short_num=jbRecvBody.getString("lot_short_id");//批次短代码
            String lot_version="";
            String attribute1="";
            String attribute2="";
            JSONObject lot_infos=jbRecvBody.getJSONObject("item_list");
            if(lot_infos==null){
                code=-7;
                errorMsg="lot_infos为null数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            JSONArray lot_attr_list=lot_infos.getJSONArray("lot");//item_id、item_value
            String lot_attr_list_str="[]";
            if(lot_attr_list==null || lot_attr_list.size()<=0){
                code=-8;
                errorMsg="lot_infos属性数据为空,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            lot_attr_list_str=lot_attr_list.toString();
            String panel_length="0";
            String panel_width="0";
            String panel_tickness="0";
            for(int i=0;i<lot_attr_list.size();i++){
                JSONObject jbItem=lot_attr_list.getJSONObject(i);
                String item_id=jbItem.getString("item_id");
                String item_value=jbItem.getString("item_value");
                if(item_id.equals(colname_length)) panel_length=item_value;
                else if(item_id.equals(colname_width)) panel_width=item_value;
                else if(item_id.equals(colname_tickness)) panel_tickness=item_value;
            }
            if(panel_length==null || panel_length.equals("")) panel_length="0";
            if(panel_width==null || panel_width.equals("")) panel_width="0";
            if(panel_tickness==null || panel_tickness.equals("")) panel_tickness="0";
            if(Float.parseFloat(panel_length)<=0 || Float.parseFloat(panel_width)<=0){
                code=-9;
                errorMsg="EAP下发任务中板长{"+panel_length+"}与板宽{"+panel_width+"}小于等于0,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(lot_num==null || lot_num.equals("") || lot_short_num==null || lot_short_num.equals("") || plan_count==null || plan_count<=0){
                code=-10;
                errorMsg="下发任务中lot_id|lot_short_id|pnl_count参数字段不能为空或者不能为0";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //4.1 判断是否存在重复的lot，若存在则不执行
            String[] group_lot_status_list=new String[]{"WAIT","PLAN","WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            long planCount =  mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
            if(planCount>0){
                String message="仅作提醒,工单{"+lot_num+"}已存在且未完成,上游设备无需重复下发";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,message);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message",message);
                return jbResult;
            }

            //4.3 插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            String plan_id=CFuncUtilsSystem.CreateUUID(true);
            String lot_group_num=CFuncUtilsSystem.GetOnlySign("LG");
            String group_id=CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("plan_id",plan_id);
            mapBigDataRow.put("station_id",station_id);
            mapBigDataRow.put("task_from","UpDevice");
            mapBigDataRow.put("group_lot_num",lot_group_num);
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("lot_short_num",lot_short_num);
            mapBigDataRow.put("lot_index",1);
            mapBigDataRow.put("plan_lot_count",plan_count);
            mapBigDataRow.put("target_lot_count",plan_count);
            mapBigDataRow.put("port_code","");
            mapBigDataRow.put("material_code",material_code);
            mapBigDataRow.put("pallet_num","");
            mapBigDataRow.put("pallet_type","");
            mapBigDataRow.put("lot_level",lot_version);
            mapBigDataRow.put("panel_length",Double.parseDouble(panel_length));
            mapBigDataRow.put("panel_width",Double.parseDouble(panel_width));
            mapBigDataRow.put("panel_tickness",Double.parseDouble(panel_tickness));
            mapBigDataRow.put("panel_model",-1);
            mapBigDataRow.put("inspect_count",0);
            mapBigDataRow.put("inspect_finish_count",0);
            mapBigDataRow.put("pdb_count",0);
            mapBigDataRow.put("pdb_rule",0);
            mapBigDataRow.put("fp_count",0);
            mapBigDataRow.put("group_lot_status","PLAN");
            mapBigDataRow.put("lot_status","PLAN");
            mapBigDataRow.put("finish_count",0);
            mapBigDataRow.put("finish_ok_count",0);
            mapBigDataRow.put("finish_ng_count",0);
            mapBigDataRow.put("task_error_code",0);
            mapBigDataRow.put("item_info",lot_attr_list_str);
            mapBigDataRow.put("task_start_time","");
            mapBigDataRow.put("task_end_time","");
            mapBigDataRow.put("task_cost_time",(long)0);
            mapBigDataRow.put("attribute1",attribute1);
            mapBigDataRow.put("attribute2",attribute2);
            mapBigDataRow.put("attribute3","");
            mapBigDataRow.put("face_code",0);
            mapBigDataRow.put("pallet_use_count",0);
            mapBigDataRow.put("user_name",WebUserId);
            mapBigDataRow.put("group_id",group_id);
            mapBigDataRow.put("offline_flag","N");
            mapBigDataRow.put("target_update_count",0);
            mongoTemplate.insert(mapBigDataRow,apsPlanTable);

            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //5.(本地)结批完成
    public JSONObject LotFinishDownLoad(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="LotFinishDownLoad";
        String esbInterfCode="LotFinishDownLoad";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        long station_id=0L;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String apsPlanTable="a_eap_aps_plan";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select station_code," +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_attr='UnLoad' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="未查找到AIS收扳机设备基础数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();
            station_code=itemListStation.get(0).get("station_code").toString();

            //3.批量读取TAG数据
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OnOffLineTag="";
            String UnLoadLinkFlagTag="";
            String SysModelTag="";
            //值
            String OnOffLine="";//在线离线
            String UnLoadLinkFlag="";//投收联机模式
            String SysModel="";//本地远程模式
            if (aisMonitorModel.equals("AIS-PC")) {
                OnOffLineTag=station_attr+"Plc/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais/AisConfig/UnLoadLinkFlag";
                SysModelTag=station_attr+"Plc/PlcConfig/SysModel";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OnOffLineTag=station_attr+"Plc_"+station_code+"/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/UnLoadLinkFlag";
                SysModelTag=station_attr+"Plc_"+station_code+"/PlcConfig/SysModel";
            }
            else{
                code=-3;
                errorMsg="AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String sumReadTags=OnOffLineTag+","+UnLoadLinkFlagTag+","+SysModelTag;
            JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,sumReadTags);
            if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                for(int i=0;i<jsonArrayTag.size();i++){
                    JSONObject jbItem=jsonArrayTag.getJSONObject(i);
                    String tag_key=jbItem.getString("tag_key");
                    String tag_value=jbItem.getString("tag_value");
                    if(tag_key.equals(OnOffLineTag)) OnOffLine=tag_value;
                    if(tag_key.equals(UnLoadLinkFlagTag)) UnLoadLinkFlag=tag_value;
                    if(tag_key.equals(SysModelTag)) SysModel=tag_value;
                }
            }

            //3.1 判断是否断网
            if(OnOffLine==null || OnOffLine.equals("") ||
                    UnLoadLinkFlag==null || UnLoadLinkFlag.equals("") ||
                    SysModel==null || SysModel.equals("")){
                code=-4;
                errorMsg="读取AIS配置参数时,SCADA断网,请重试";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.2 若是离线模式不接受任务
            if(!OnOffLine.equals("1")){
                code=-5;
                errorMsg="设备当前为离线模式,AIS拒绝接受上游结批信号";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(SysModel.equals("1")){
                code=-5;
                errorMsg="设备当前为EAP远程模式,AIS拒绝接受上游强制结批";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.3 若是收扳机,投收联机模式不需上游强制结批
            if(station_attr.equals("UnLoad") && UnLoadLinkFlag.equals("1")){
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"仅作提醒,投收联机模式不需上游强制结批");
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message","仅作提醒,投收联机模式不需上游强制结批");
                return jbResult;
            }

            //4.数据解析
            String lot_num=jbRecvBody.getString("lot_id");
            Integer lot_count=jbRecvBody.getInteger("lot_count");
            String lot_short_id=jbRecvBody.getString("lot_short_id");

            //4.1 查询当前lot对应的任务数据
            String[] group_lot_status_list=new String[]{"WAIT","PLAN","WORK"};
            Query queryBigData = new Query();
            Update updateBigData = new Update();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(!iteratorBigData.hasNext()){
                code=-8;
                errorMsg="收扳机未下发工单{"+lot_num+"}";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            Document docItemBigData = iteratorBigData.next();
            String group_lot_num=docItemBigData.getString("group_lot_num");
            String group_lot_status=docItemBigData.getString("group_lot_status");
            String port_code=docItemBigData.getString("port_code");
            iteratorBigData.close();

            //4.6 若不是WORK状态强制取消任务
            if(!group_lot_status.equals("WORK")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                updateBigData = new Update();
                updateBigData.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            else{
                String sqlPort="select port_index " +
                        "from a_eap_fmod_station_port " +
                        "where station_id="+station_id+" and port_code='"+port_code+"'";
                List<Map<String, Object>> lstPort=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlPort,false,request,apiRoutePath);
                String port_index=lstPort.get(0).get("port_index").toString();
                String AisPortBackRequestTagCode="AisPort"+port_index+"BackRequest";
                errorMsg=opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Plc","PlcStatus",AisPortBackRequestTagCode,"UpDevice","1",true);
                if(!errorMsg.equals("")){
                    code=-10;
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
            }

            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //6.接受上游设备发送过来的当片信息
    public JSONObject EachPanelDataDownLoad(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="EachPanelDataDownLoad";
        String esbInterfCode="EachPanelDataDownLoad";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        long station_id=0L;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String mePanelQueueTable="a_eap_me_panel_queue";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select station_code," +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_attr='UnLoad' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="未查找到AIS收扳机设备基础数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();
            station_code=itemListStation.get(0).get("station_code").toString();

            //4.数据解析
            String lot_num=jbRecvBody.getString("lot_id");//批次号
            String panel_id=jbRecvBody.getString("panel_id");//板件信息
            String panel_status=jbRecvBody.getString("panel_status");//OK/NG

            //4.1 插入板件信息到队列
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            String panel_queue_id=CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("panel_queue_id",panel_queue_id);
            mapBigDataRow.put("station_id",station_id);
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("panel_barcode",panel_id);
            mapBigDataRow.put("panel_status",panel_status);
            mapBigDataRow.put("use_flag","N");
            mongoTemplate.insert(mapBigDataRow,mePanelQueueTable);

            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //7.(在线)接受EAP下发將產品規格
    public JSONObject ProductionSpecDownload(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="ProductionSpecDownload";
        String esbInterfCode="ProductionSpecDownload";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        long station_id=0L;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String apsProSpecTable="a_eap_aps_pro_spec";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();

            //3.批量读取TAG数据
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");

            String OnOffLineTag="";
            String SysModelTag="";
            //值
            String OnOffLine="";//在线离线
            String SysModel="";//本地远程
            if (aisMonitorModel.equals("AIS-PC")) {
                OnOffLineTag=station_attr+"Plc/PlcConfig/OnOffLine";
                SysModelTag=station_attr+"Plc/PlcConfig/SysModel";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OnOffLineTag=station_attr+"Plc_"+station_code+"/PlcConfig/OnOffLine";
                SysModelTag=station_attr+"Plc_"+station_code+"/PlcConfig/SysModel";
            }
            else{
                code=-3;
                errorMsg="AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String sumReadTags=OnOffLineTag+","+SysModelTag;
            JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,sumReadTags);
            if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                for(int i=0;i<jsonArrayTag.size();i++){
                    JSONObject jbItem=jsonArrayTag.getJSONObject(i);
                    String tag_key=jbItem.getString("tag_key");
                    String tag_value=jbItem.getString("tag_value");
                    if(tag_key.equals(OnOffLineTag)) OnOffLine=tag_value;
                    if(tag_key.equals(SysModelTag)) SysModel=tag_value;
                }
            }

            //3.1 判断是否断网
            if(OnOffLine==null || OnOffLine.equals("") ||
                    SysModel==null || SysModel.equals("")){
                code=-4;
                errorMsg="读取AIS配置参数时,SCADA断网,请重试";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.2 若是离线模式不接受任务
            if(!OnOffLine.equals("1")){
                code=-5;
                errorMsg="设备当前为离线模式,AIS拒绝接受产品规格下发";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(!SysModel.equals("1")){
                code=-5;
                errorMsg="设备当前为本地模式,AIS拒绝接受远程产品规格下发";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //4.数据解析
            String download_type=jbRecvBody.getString("download_type");//下发类型
            String spec_seq=jbRecvBody.getString("spec_seq");//规格序号

            //D:BC请求主动删除
            if(download_type.equals("D")){
                Query queryBigDataDel = new Query();
                queryBigDataDel.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDel.addCriteria(Criteria.where("spec_seq").is(spec_seq));
                long specCountDel =  mongoTemplate.getCollection(apsProSpecTable).countDocuments(queryBigDataDel.getQueryObject());
                if(specCountDel<=0){
                    code=-30;
                    errorMsg="规格序号{"+spec_seq+"}不存在,不允许删除";
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
                else{
                    mongoTemplate.remove(queryBigDataDel,apsProSpecTable);
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS","EAP远程执行规格序号{"+spec_seq+"}删除",5);
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",true);
                    jbResult.put("message","删除成功");
                    return jbResult;
                }
            }

            //U:BC请求主动修改,先删除再新增
            if(download_type.equals("U")){
                Query queryBigDataUpd = new Query();
                queryBigDataUpd.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataUpd.addCriteria(Criteria.where("spec_seq").is(spec_seq));
                long planCountUpd =  mongoTemplate.getCollection(apsProSpecTable).countDocuments(queryBigDataUpd.getQueryObject());
                if(planCountUpd<=0){
                    code=-31;
                    errorMsg="规格序号{"+spec_seq+"}不存在,不允许修改";
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
                else{
                    mongoTemplate.remove(queryBigDataUpd,apsProSpecTable);
                }
            }

            //新增产品规则

            JSONObject spec_infos=jbRecvBody.getJSONObject("spec_infos");//规格详细
            if(spec_infos==null){
                code=-7;
                errorMsg="spec_infos为null数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            JSONArray spec_attr_list=spec_infos.getJSONArray("spec");
            String spec_attr_list_str="[]";
            if(spec_attr_list==null || spec_attr_list.size()<=0){
                code=-8;
                errorMsg="spec_infos属性数据为空,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            spec_attr_list_str=spec_attr_list.toString();

            //4.1 判断是否存在重复的spec_seq，若存在则不执行
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("spec_seq").is(spec_seq));
            long planCount =  mongoTemplate.getCollection(apsProSpecTable).countDocuments(queryBigData.getQueryObject());
            if(planCount>0){
                String message="仅作提醒,规则序号{"+spec_seq+"}已存在,EAP无需重复下发";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,message);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message",message);
                return jbResult;
            }

            //4.3 插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            String spec_id=CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("spec_id",spec_id);
            mapBigDataRow.put("station_id",station_id);
            mapBigDataRow.put("task_from","EAP");
            mapBigDataRow.put("spec_seq",spec_seq);
            mapBigDataRow.put("spec_info",spec_attr_list_str);
            mongoTemplate.insert(mapBigDataRow,apsProSpecTable);

            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //8.(在线)接受EAP下发PNL
    public JSONObject PanelDataDownload(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="PanelDataDownload";
        String esbInterfCode="PanelDataDownload";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        long station_id=0L;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String apsPlanTable="a_eap_aps_plan";
        String apsPlanDTable="a_eap_aps_plan_d";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_attr='UnLoad' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="收扳机工位号不存在AIS设备基础数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());

            //4.数据解析
            String pnl_id=jbRecvBody.getString("pnl_id");
            String lot_id=jbRecvBody.getString("lot_id");
            if(pnl_id==null) pnl_id="";
            if(lot_id==null) lot_id="";
            if(pnl_id.equals("") || lot_id.equals("")){
                code=-3;
                errorMsg="板件ID或者工单号不能为空";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            //获取工单信息
            Query queryBigDataLot = new Query();
            queryBigDataLot.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigDataLot.addCriteria(Criteria.where("lot_num").is(lot_id));
            queryBigDataLot.with(Sort.by(Sort.Direction.DESC,"_id"));
            Map lotMap = mongoTemplate.findOne(queryBigDataLot, Map.class, apsPlanTable);
            if(lotMap==null){
                code=-4;
                errorMsg="未能根据工单号{"+lot_id+"}查询到收扳机工单信息";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String plan_id=lotMap.get("plan_id").toString();
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String plan_d_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRowB = new HashMap<>();
            mapBigDataRowB.put("item_date", item_date);
            mapBigDataRowB.put("item_date_val", item_date_val);
            mapBigDataRowB.put("plan_d_id", plan_d_id);
            mapBigDataRowB.put("plan_id", plan_id);
            mapBigDataRowB.put("panel_barcode", pnl_id);
            mapBigDataRowB.put("panel_attr", "0");
            mapBigDataRowB.put("use_sign", 0);
            mongoTemplate.insert(mapBigDataRowB, apsPlanDTable);

            //返回成功
            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex;
            opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
