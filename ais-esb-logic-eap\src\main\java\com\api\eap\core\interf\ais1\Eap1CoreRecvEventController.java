package com.api.eap.core.interf.ais1;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 投收扳机标准接受事件接口
 * 1.AIS接受远程控制启动与停止
 * 2.AIS接受远程控制HMI文本显示
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/interf")
public class Eap1CoreRecvEventController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private Eap1CoreRecvEventFunc eap1CoreRecvEventFunc;

    //1.AIS接受远程控制启动与停止
    @RequestMapping(value = "/EapCoreInterfControlStop",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapCoreRecvEventControlStop(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/core/interf/EapCoreInterfControlStop";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eap1CoreRecvEventFunc.EapCoreInterfControlStop(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //2.AIS接受远程控制HMI文本显示
    @RequestMapping(value = "/EapCoreInterfRecvHmiShow",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapCoreRecvEventHmiShow(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/core/interf/EapCoreInterfRecvHmiShow";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eap1CoreRecvEventFunc.EapCoreInterfRecvHmiShow(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //3.AIS任务取消(AIS发布)
    @RequestMapping(value = "/EapCoreInterfCancelTask",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapCoreInterfCancelTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/core/interf/EapCoreInterfCancelTask";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eap1CoreRecvEventFunc.EapCoreInterfCancelTask(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //4.AIS通讯状态测试(AIS发布)
    @RequestMapping(value = "/EapCoreInterfConnTest",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapCoreInterfConnTest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/core/interf/EapCoreInterfConnTest";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eap1CoreRecvEventFunc.EapCoreInterfConnTest(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

}
