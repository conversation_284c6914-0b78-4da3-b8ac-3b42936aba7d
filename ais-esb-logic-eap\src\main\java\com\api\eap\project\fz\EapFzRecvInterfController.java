package com.api.eap.project.fz;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.project.glorysoft.EapGloryRecvInterfFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 方正EAP定义接口
 * 1.EAP初始数据请求
 * 2.EAP请求点检数据
 * 3.EAP远程讯息命令
 * 4.EAP主动下发档案路径和批次信息
 * 5.EAP批次数据删除请求
 * 6.EAP调用此接口下发Lot信息
 * 7.Panel信息请求信号返回
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/Fz/interf/recv")
public class EapFzRecvInterfController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private EapFzRecvInterfFunc eapFzRecvInterfFunc;

    //EAP初始数据请求
    @RequestMapping(value = "/InitialDataRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String InitialDataRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/Fz/interf/recv/InitialDataRequest";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapFzRecvInterfFunc.aisInitialDataRequest(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //EAP请求点检数据
    @RequestMapping(value = "/TraceDataRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String TraceDataRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/Fz/interf/recv/TraceDataRequest";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapFzRecvInterfFunc.aisTraceDataRequest(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //EAP远程讯息命令
    @RequestMapping(value = "/CIMMessageCommand", method = {RequestMethod.POST, RequestMethod.GET})
    public String CIMMessageCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/Fz/interf/recv/CIMMessageCommand";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapFzRecvInterfFunc.aisCIMMessageCommand(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //EAP主动下发档案路径和批次信息
    @RequestMapping(value = "/FilePathAndLotInformationDownload", method = {RequestMethod.POST, RequestMethod.GET})
    public String FilePathAndLotInformationDownload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/Fz/interf/recv/FilePathAndLotInformationDownload";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapFzRecvInterfFunc.aisFilePathAndLotInformationDownload(jsonParas, request);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //EAP批次数据删除请求
    @RequestMapping(value = "/DeleteLotInfoDataRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String DeleteLotInfoDataRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/Fz/interf/recv/DeleteLotInfoDataRequest";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapFzRecvInterfFunc.aisDeleteLotInfoDataRequest(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //EAP询问配方名称是否存在
    @RequestMapping(value = "/RecipeNameExistRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String RecipeNameExistRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/Fz/interf/recv/RecipeNameExistRequest";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapFzRecvInterfFunc.aisRecipeNameExistRequest(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //EAP向设备询问当前的配方名及配方参数
    @RequestMapping(value = "/RecipeParameterRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String RecipeParameterRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/Fz/interf/recv/RecipeParameterRequest";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eapFzRecvInterfFunc.aisRecipeParameterRequest(jsonParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }
}
