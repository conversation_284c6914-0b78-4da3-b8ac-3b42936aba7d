package com.api.eap.project.wxjd;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * EAP发送事件数据定义接口
 * 1.UserVerify:锁机密码修改
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/wxjd")
public class EapLockPasswordModification {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    //锁机密码修改
    @RequestMapping(value = "/LockPasswordModification", method = {RequestMethod.POST,RequestMethod.GET})
    public String LockPasswordModification(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/wxjd/LockPasswordModification";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        String meUserTable="a_eap_me_station_user";
        try{
            String LockPasswor=jsonParas.getString("LockPasswor");//密码
            if(LockPasswor!=null && !LockPasswor.equals("")){
                    String sqlUpdParas="update sys_parameter set " +
                            "parameter_val='"+LockPasswor+"' " +
                            "where parameter_code='LockPassword'";
                    cFuncDbSqlExecute.ExecUpdateSql(userId,sqlUpdParas,false,request,apiRoutePath);
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "锁机密码修改异常:"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
