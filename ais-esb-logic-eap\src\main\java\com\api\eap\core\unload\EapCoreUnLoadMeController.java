package com.api.eap.core.unload;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 收板机工艺制造处理逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/unload")
public class EapCoreUnLoadMeController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //查询收板机载具状态用于判断放板机是否可以放板
    @RequestMapping(value = "/EapCoreUnLoadMeAllowLoad", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapCoreUnLoadMeAllowLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/core/unload/EapCoreUnLoadMeAllowLoad";
        String transResult="";
        String errorMsg="";
        String allow_flag="N";
        try{
            String station_code=jsonParas.getString("station_code");//收板机工位号
            String aisMonitorModel=cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String tagPlcExistPallet="";
            if(aisMonitorModel.equals("AIS-PC")){
                tagPlcExistPallet="UnLoadPlc/PlcStatus/PlcExistPallet";
            }
            else if(aisMonitorModel.equals("AIS-SERVER")){
                tagPlcExistPallet="UnLoadPlc_"+station_code+"/PlcStatus/PlcExistPallet";
            }
            else{
                errorMsg="未配置收板机系统参数AIS_MONITOR_MODE";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            JSONArray jsonArray= cFuncUtilsCellScada.ReadTagByStation(station_code,tagPlcExistPallet);
            if(jsonArray!=null && jsonArray.size()>0){
                JSONObject jbItem=jsonArray.getJSONObject(0);
                String tagPlcExistPalletValue=jbItem.getString("tag_value");
                if(tagPlcExistPalletValue.equals("1")) allow_flag="Y";
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,allow_flag,"",0);
        }
        catch (Exception ex){
            errorMsg= "查询收板机载具状态用于判断放板机是否可以放板异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
