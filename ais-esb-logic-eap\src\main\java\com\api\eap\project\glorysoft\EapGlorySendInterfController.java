package com.api.eap.project.glorysoft;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 哥瑞利EAP定义接口
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-10
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/glorysoft/interf/send")
public class EapGlorySendInterfController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private EapGlorySendInterfFunc eapGlorySendInterfFunc;

    //[EAP]设备初始化建立通讯请求上报
    //传递参数:station_code、station_id
    @RequestMapping(value = "/EapGlorySendAreYouThereReply", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendAreYouThereReply(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendAreYouThereReply";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendAreYouThereReply(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]设备初始化建立通讯请求上报接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]设备切换CIM状态上报
    //传递参数:station_code、station_id、cim_value(1EAP模式,0AIS模式)
    @RequestMapping(value = "/EapGlorySendEQPCommunicationStatusReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendEQPCommunicationStatusReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendEQPCommunicationStatusReport";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendEQPCommunicationStatusReport(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]设备切换CIM状态上报接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]设备切换手动模式自动模式
    //传递参数:station_code、station_id、model_value(1自动,0手动)
    @RequestMapping(value = "/EapGlorySendEQPRunningModeReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendEQPRunningModeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendEQPRunningModeReport";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendEQPRunningModeReport(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]设备切换手动模式自动模式上报接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]设备向EAP请求时间
    //传递参数:station_code、station_id
    @RequestMapping(value = "/EapGlorySendEQPDateTimeRequest", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendEQPDateTimeRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendEQPDateTimeRequest";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendEQPDateTimeRequest(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]设备向EAP请求时间接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]设备发生状态改变时调用
    //传递参数:station_code、station_id、device_status(IDLE(待机)、RUN(运行)、DOWN(停机)、PM(保养))
    @RequestMapping(value = "/EapGlorySendEQPStatusChangeReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendEQPStatusChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendEQPStatusChangeReport";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendEQPStatusChangeReport(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]设备发生状态改变时调用接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]设备发生报警时调用
    //传递参数:station_code、station_id、alarm_id、alarm_flag、alarm_value
    @RequestMapping(value = "/EapGlorySendEQPAlarmReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendEQPAlarmReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendEQPAlarmReport";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendEQPAlarmReport(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]设备发生报警时调用接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]取片事件报告
    //传递参数:station_code、station_id、port_code、pallet_num、lot_num、panel_barcode
    @RequestMapping(value = "/EapGlorySendFetchOutJobEventReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendFetchOutJobEventReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendFetchOutJobEventReport";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendFetchOutJobEventReport(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]取片事件报告接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]放片事件报告
    //传递参数:station_code、station_id、port_code、pallet_num、lot_num、panel_barcode
    @RequestMapping(value = "/EapGlorySendStoreInJobEventReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendStoreInJobEventReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendStoreInJobEventReport";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendStoreInJobEventReport(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]放片事件报告接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]收放板机工位状态报告
    //传递参数:
    //port_status(1.空、2.载入完成、3.等待开始命令、4.生产中、5.完工、6.任务取消、7.任务中止、8.卸料、9.首件完成)
    //station_code、station_id、port_code、pallet_num、lot_num、finish_count、finish_panel_list
    @RequestMapping(value = "/EapGlorySendCarrierStatusChangeReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendCarrierStatusChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendCarrierStatusChangeReport";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendCarrierStatusChangeReport(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]收放板机工位状态报告接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]收放板机搬运模式上报
    //传递参数:station_code、station_id、port_code、auto_flag
    @RequestMapping(value = "/EapGlorySendPortTransferModeChangeReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendPortTransferModeChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendPortTransferModeChangeReport";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendPortTransferModeChangeReport(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]收放板机搬运模式上报接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]Panel信息请求
    //传递参数：station_code、station_id、pallet_num、lot_num、panel_barcode
    @RequestMapping(value = "/EapGlorySendPanelInformationRequest", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapGlorySendPanelInformationRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/glorysoft/interf/send/EapGlorySendPanelInformationRequest";
        String transResult="";
        String errorMsg="";
        try{
            String startDate= CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult=eapGlorySendInterfFunc.eapGlorySendPanelInformationRequest(jsonParas);
            Boolean prodFlag=true;
            Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
            String esbInterfCode=jbResult.getString("esbInterfCode");
            String token=jbResult.getString("token");
            String requestParas=jbResult.getString("requestParas");
            String responseParas=jbResult.getString("responseParas");
            Boolean successFlag=jbResult.getBoolean("successFlag");
            String message=jbResult.getString("message");
            String endDate= CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if(isSaveFlag){
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                        successFlag,  message, null);
            }
            if(!successFlag){
                transResult= CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "[EAP]Panel信息请求接口异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
