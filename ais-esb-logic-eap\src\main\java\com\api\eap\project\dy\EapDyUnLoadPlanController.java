package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 定颖放板机定制生产计划处理逻辑
 * 1.放板机Panel校验
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/dy/unload")
public class EapDyUnLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private EapDySendFlowFunc eapDySendFlowFunc;

    //收板机Panel校验
    @RequestMapping(value = "/EapDyUnLoadPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyUnLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/unload/EapDyUnLoadPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断
            //针对条码进行校验
            Integer face_code2_int=0;
            String[] lstPanelBarCode=panel_barcode.split(",",-1);
            if(lstPanelBarCode!=null && lstPanelBarCode.length==2){
                panel_barcode=lstPanelBarCode[0];
                String face_code2=lstPanelBarCode[1];
                if(face_code2.equals("Z")) face_code2_int=1;
                else if(face_code2.equals("F")) face_code2_int=2;
            }
            //若出现多个集合,则直接判断为NoRead
            if(lstPanelBarCode!=null && lstPanelBarCode.length>2){
                panel_barcode="NoRead";
            }

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer eap_face_code=0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";
            String virtu_pallet_num="";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                target_lot_count = docItemBigData.getInteger("target_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                eap_face_code= docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                virtu_pallet_num=pallet_num;
                if(docItemBigData.containsKey("virtu_pallet_num")) virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                iteratorBigData.close();
            }
            face_code=face_code2_int;
            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");
            mapBigDataRow.put("virtu_pallet_num",virtu_pallet_num);

            String SbDoubleFlag = cFuncDbSqlResolve.GetParameterValue("SB_DOUBLE_FLAG");
            String isUseNextLot = "N";//是否使用下一个任务

            if (plan_id.equals("")) {//当未找到任务时直接记录
                panel_ng_code=8;
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 11;
                }
                panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                mapBigDataRow.put("panel_ng_code", panel_ng_code);
                mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num + "," +
                        port_index+ "," +face_code;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            List<String> lot_short_num_list=new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
                String lot_short_num_1=docItemBigData.getString("lot_short_num");
                if(!lot_short_num_1.equals("") && !lot_short_num_list.contains(lot_short_num_1)) lot_short_num_list.add(lot_short_num_1);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                panel_ng_code = 11;
                panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                mapBigDataRow.put("panel_ng_code", panel_ng_code);
                mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num + "," + port_index;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_ng_code = 4;
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_ng_code = 4;
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            if (ng_auto_pass_value.equals("1")) {
                                panel_ng_code = 4;
                            } else {
                                panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            }
                        } else {
                            panel_ng_code = CheckPanelByItemInfo(station_id,plan_id,lstPlanId, panel_barcode, lot_short_num, item_info,
                                    strip_flag, ng_auto_pass_value,face_code,eap_face_code,lot_short_num_list);
                            if (panel_ng_code == 1 && SbDoubleFlag.equals("Y")) {
                                //当前PORT任务与板件不匹配,则执行下一个任务
                                String[] group_lot_status_list = new String[]{"WAIT", "PLAN"};
                                lot_status_list = new String[]{"WAIT","PLAN", "WORK"};
                                queryBigData = new Query();
                                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                                queryBigData.addCriteria(Criteria.where("group_lot_num").ne(group_lot_num));
                                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                                queryBigData.limit(1);
                                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                Document docItemBigData=null;
                                if (iteratorBigData.hasNext()) {
                                    docItemBigData = iteratorBigData.next();
                                    iteratorBigData.close();
                                }
                                if(docItemBigData!=null){
                                    String select_plan_id2 = docItemBigData.get("plan_id").toString();
                                    String select_group_lot_num2 = docItemBigData.get("group_lot_num").toString();
                                    String select_lot_short_num2 = docItemBigData.get("lot_short_num").toString();
                                    String select_item_info2 = docItemBigData.get("item_info").toString();
                                    Integer select_finish_count2 = docItemBigData.getInteger("finish_count");
                                    Integer select_finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                                    Integer select_finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                                    Integer eap_face_code2=docItemBigData.getInteger("face_code");
                                    //处理
                                    lstPlanId = new ArrayList<>();
                                    lot_short_num_list= new ArrayList<>();
                                    queryBigData2 = new Query();
                                    queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
                                    queryBigData2.addCriteria(Criteria.where("group_lot_num").is(select_group_lot_num2));
                                    iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                                            sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                                    while (iteratorBigData.hasNext()) {
                                        Document docItemBigData2 = iteratorBigData.next();
                                        lstPlanId.add(docItemBigData2.getString("plan_id"));
                                        String lot_short_num_1=docItemBigData2.getString("lot_short_num");
                                        if(!lot_short_num_1.equals("") && !lot_short_num_list.contains(lot_short_num_1)) lot_short_num_list.add(lot_short_num_1);
                                    }
                                    if (iteratorBigData.hasNext()) iteratorBigData.close();
                                    Integer panel_ng_code2 = CheckPanelByItemInfo(station_id,select_plan_id2,lstPlanId, panel_barcode, select_lot_short_num2,
                                            select_item_info2, strip_flag, ng_auto_pass_value,face_code,eap_face_code2,lot_short_num_list);
                                    if (panel_ng_code2 != 1) {
                                        panel_ng_code = panel_ng_code2;
                                        plan_id = select_plan_id2;
                                        group_lot_num = select_group_lot_num2;
                                        lot_short_num = select_lot_short_num2;
                                        item_info = select_item_info2;
                                        finish_count = select_finish_count2;
                                        finish_ok_count = select_finish_ok_count2;
                                        finish_ng_count = select_finish_ng_count2;
                                        panel_index = finish_count + 1;
                                        //其他数据
                                        plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                                        task_from=docItemBigData.getString("task_from");
                                        lot_num = docItemBigData.getString("lot_num");
                                        lot_index = docItemBigData.getInteger("lot_index");
                                        material_code = docItemBigData.getString("material_code");
                                        pallet_num = docItemBigData.getString("pallet_num");
                                        pallet_type = docItemBigData.getString("pallet_type");
                                        lot_level = docItemBigData.getString("lot_level");
                                        panel_length = docItemBigData.getDouble("panel_length");
                                        panel_width = docItemBigData.getDouble("panel_width");
                                        panel_tickness = docItemBigData.getDouble("panel_tickness");
                                        virtu_pallet_num=pallet_num;
                                        if(docItemBigData.containsKey("virtu_pallet_num")) virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                                        isUseNextLot = "Y";
                                    }
                                }
                            }
                        }
                    }
                } else {
                    //如果是无Panel模式,则需要进行组合
                    if (!lot_short_num.equals("") && lot_short_num.length() >= 10) {
                        String noPanelLotShortNum = lot_short_num.substring(0, 10);
                        panel_barcode = noPanelLotShortNum + String.format("%03d", panel_index);
                        mapBigDataRow.put("panel_barcode", panel_barcode);
                    } else {
                        panel_barcode = String.format("%03d", panel_index);
                        mapBigDataRow.put("panel_barcode", panel_barcode);
                    }
                }
            }


            if (panel_ng_code == 0) panel_status = "OK";
            else if (panel_ng_code == 4) panel_status = "NG_PASS";
            else panel_status = "NG";
            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);

            if (isUseNextLot.equals("Y")) {
                if (port_code.equals("01")) port_code = "02";
                else port_code = "01";
                if (port_index.equals("1")) port_index = "2";
                else port_index = "1";
            }
            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("panel_index", panel_index);
            //其他数据
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("virtu_pallet_num", virtu_pallet_num);

            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            Update updateBigData = new Update();
            String lot_status = "WORK";
            String lot_status2 = "WORK";

            if (isUseNextLot.equals("N")) {
                if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
                lot_status2 = lot_status;
                if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
                updateBigData.set("lot_status", lot_status);
                if (finish_count == 1) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                if (lot_status.equals("FINISH")) {
                    updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
            }
            else{
                if(finish_ok_count >= plan_lot_count){
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                updateBigData.set("lot_status", lot_status);
                if (finish_count == 1) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
            }

            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);

            //更新数据
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //更新EAP状态
            if(finish_count==1){
                String station_code="";//工位号
                String sqlStation="select station_code " +
                        "from sys_fmod_station " +
                        "where station_id="+station_id+"";
                List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStation,false,request,apiRoutePath);
                if(itemListStation!=null && itemListStation.size()>0){
                    station_code=itemListStation.get(0).get("station_code").toString();
                }
                String writeTags="UnLoadEap/EapStatus/CurrentLotNum,UnLoadEap/EapStatus/CurrentLotCount";
                String tagValues=lot_num+"&"+plan_lot_count;
                cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, writeTags, tagValues, false);
            }

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num + "," +
                    port_index+ "," +face_code;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "收板机Panel校验异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //收板机Panel提前验证,不做存储
    @RequestMapping(value = "/EapDyUnLoadPlanPanelPreCheck", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyUnLoadPlanPanelPreCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/unload/EapDyUnLoadPlanPanelPreCheck";
        String transResult = "";
        String result = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String group_lot_num=jsonParas.getString("group_lot_num");//当前端口作业母批号
            String group_lot_num2=jsonParas.getString("group_lot_num2");//另外一个端口作业母批号
            String task_order_by_up=jsonParas.getString("task_order_by_up");//任务是否来自上游,由当前读码决定

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if(group_lot_num==null) group_lot_num="";
            if(group_lot_num2==null) group_lot_num2="";
            if(task_order_by_up==null || task_order_by_up.equals("")) task_order_by_up="0";
            long station_id_long = Long.parseLong(station_id);
            String lot_num = "";

            Integer face_code2_int=0;
            String[] lstPanelBarCode=panel_barcode.split(",",-1);
            if(lstPanelBarCode!=null && lstPanelBarCode.length==2){
                panel_barcode=lstPanelBarCode[0];
                String face_code2=lstPanelBarCode[1];
                if(face_code2.equals("Z")) face_code2_int=1;
                else if(face_code2.equals("F")) face_code2_int=2;
            }
            //若出现多个集合,则直接判断为NoRead
            if(lstPanelBarCode!=null && lstPanelBarCode.length>2){
                panel_barcode="NoRead";
            }

            //1.若是无panel模式则无需进行扫描
            if (!panel_model_value.equals("1")) {
                result = panel_flag + "," + lot_num + "," + panel_ng_code + "," + panel_status + "," + panel_ng_msg;
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //若任务来自上游,则由当前读码来决定选择那个工单
            if("1".equals(task_order_by_up)){
                if(!panel_barcode.equals("NoRead") && !panel_barcode.equals("")){
                    if("".equals(group_lot_num) || "".equals(group_lot_num2)){
                        String plan_id3="";
                        Query queryBigDataD = new Query();
                        queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                        queryBigDataD.with(Sort.by(Sort.Direction.DESC, "_id"));
                        MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanBTable).find(queryBigDataD.getQueryObject()).
                                sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                        if (iteratorBigDataD.hasNext()){
                            Document docItemBigDataD = iteratorBigDataD.next();
                            plan_id3=docItemBigDataD.getString("plan_id");
                            iteratorBigDataD.close();
                        }
                        if(!"".equals(plan_id3)){
                            String group_lot_num3="";
                            Query queryBigDataMain = new Query();
                            queryBigDataMain.addCriteria(Criteria.where("station_id").is(station_id_long));
                            queryBigDataMain.addCriteria(Criteria.where("plan_id").is(plan_id3));
                            queryBigDataMain.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                            MongoCursor<Document> iteratorBigDataMain = mongoTemplate.getCollection(apsPlanTable).find(queryBigDataMain.getQueryObject()).
                                    sort(queryBigDataMain.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                            if(iteratorBigDataMain.hasNext()){
                                Document docItemBigDataMain = iteratorBigDataMain.next();
                                group_lot_num3=docItemBigDataMain.getString("group_lot_num");
                                iteratorBigDataMain.close();
                            }
                            if(!"".equals(group_lot_num3)){
                                Boolean isNeedUpdStatus=false;
                                if("".equals(group_lot_num)){
                                    group_lot_num=group_lot_num3;
                                    isNeedUpdStatus=true;
                                }
                                else{
                                    if("".equals(group_lot_num2)){
                                        group_lot_num2=group_lot_num3;
                                        isNeedUpdStatus=true;
                                    }
                                }
                                if(isNeedUpdStatus){
                                    queryBigDataMain = new Query();
                                    queryBigDataMain.addCriteria(Criteria.where("station_id").is(station_id_long));
                                    queryBigDataMain.addCriteria(Criteria.where("group_lot_num").is(group_lot_num3));
                                    Update updateBigDataMain = new Update();
                                    updateBigDataMain.set("group_lot_status", "PLAN");
                                    mongoTemplate.updateMulti(queryBigDataMain, updateBigDataMain, apsPlanTable);
                                }
                            }
                        }
                    }
                }
            }

            //判断是否需要上报暂存信息
            String DyUnLoadZcjFlag=cFuncDbSqlResolve.GetParameterValue("Dy_UnLoadZCJ");
            Boolean isP1Factory = eapDyInterfCommon.CheckDyFactory("P1");
            Boolean isNewVersion = eapDyInterfCommon.CheckDyVersion(3);//是否泰国超颖项目
            String SbDoubleFlag = cFuncDbSqlResolve.GetParameterValue("SB_DOUBLE_FLAG");
            Boolean isUnLoadZcj=false;
            if("Y".equals(DyUnLoadZcjFlag)) isUnLoadZcj=true;

            //首先先查询正在WORK的任务
            String group_id = "";
            String plan_id="";
            String material_code_P001 = "";
            String lot_version_P002 = "";
            String lot_num_P003 = "";
            String lot_short_num = "";
            String item_info = "";
            String first_plan_count_P004 = "0";
            String attribute1_P005 = "";
            String attribute2_P006 = "";
            String prod_id = "";
            String prod_version = "";
            String process_code = "";
            Integer panel_index=1;
            Integer eap_face_code=0;
            Integer ng_control=0;
            JSONObject attr_else = null;
            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(lot_status_list));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(
                    new Criteria().andOperator(
                            Criteria.where("$expr").gt(Arrays.asList("$plan_lot_count", "$pallet_use_count"))
                    )
            );
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(!iteratorBigData.hasNext()){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(lot_status_list));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id= docItemBigData.getString("group_id");
                plan_id= docItemBigData.getString("plan_id");
                lot_num=docItemBigData.getString("lot_num");
                material_code_P001 = docItemBigData.getString("material_code");
                lot_version_P002 = docItemBigData.getString("lot_level");
                lot_num_P003=docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                item_info = docItemBigData.getString("item_info");
                first_plan_count_P004 = String.valueOf(docItemBigData.getInteger("plan_lot_count"));
                panel_index=docItemBigData.getInteger("pallet_use_count")+1;
                eap_face_code=docItemBigData.getInteger("face_code");
                attribute1_P005 = docItemBigData.getString("attribute1");
                attribute2_P006 = docItemBigData.getString("attribute2");
                ng_control = docItemBigData.getInteger("panel_model");
                prod_id = material_code_P001;
                prod_version = lot_version_P002;
                process_code = attribute1_P005;
                if (docItemBigData.containsKey("attribute3")) {
                    String attribute_else = docItemBigData.getString("attribute3");
                    if (attribute_else != null && !attribute_else.equals("")) {
                        attr_else = JSONObject.parseObject(attribute_else);
                    }
                }
                iteratorBigData.close();
            }
            if (plan_id.equals("")) {
                panel_ng_code=8;
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 11;
                }
            }
            else{
                //查询List<PlanId>
                List<String> lstPlanId = new ArrayList<>();
                List<String> lot_short_num_list=new ArrayList<>();
                Query queryBigData2 = new Query();
                queryBigData2.addCriteria(Criteria.where("group_id").is(group_id));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                        sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    lstPlanId.add(docItemBigData.getString("plan_id"));
                    String lot_short_num_1=docItemBigData.getString("lot_short_num");
                    if(!lot_short_num_1.equals("") && !lot_short_num_list.contains(lot_short_num_1)) lot_short_num_list.add(lot_short_num_1);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 11;
                }
                else{
                    if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                        if(panel_barcode.equals("NoRead")) panel_ng_code=2;
                        else panel_ng_code=7;
                    }
                    else{
                        //第一次判断
                        panel_ng_code = CheckPanelByItemInfo(station_id,plan_id,lstPlanId, panel_barcode, lot_short_num,
                                item_info, "N", ng_auto_pass_value, face_code2_int,eap_face_code,lot_short_num_list);
                        if (panel_ng_code == 1 && SbDoubleFlag.equals("Y") && !"".equals(group_lot_num2)){
                            queryBigData = new Query();
                            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                            queryBigData.addCriteria(
                                    new Criteria().andOperator(
                                            Criteria.where("$expr").gt(Arrays.asList("$plan_lot_count", "$pallet_use_count"))
                                    )
                            );
                            queryBigData.addCriteria(Criteria.where("group_lot_status").in(lot_status_list));
                            //queryBigData.addCriteria(Criteria.where("group_id").ne(group_id));
                            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num2));
                            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                            if(!iteratorBigData.hasNext()){
                                queryBigData = new Query();
                                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigData.addCriteria(Criteria.where("group_lot_status").in(lot_status_list));
                                //queryBigData.addCriteria(Criteria.where("group_id").ne(group_id));
                                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num2));
                                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                            }
                            Document docItemBigData=null;
                            if (iteratorBigData.hasNext()){
                                docItemBigData = iteratorBigData.next();
                                iteratorBigData.close();
                            }
                            if(docItemBigData!=null){
                                String select_plan_id2 = docItemBigData.getString("plan_id");
                                String select_lot_short_num2 = docItemBigData.getString("lot_short_num");
                                String select_item_info2 = docItemBigData.getString("item_info");
                                Integer eap_face_code2=docItemBigData.getInteger("face_code");
                                String group_id2=docItemBigData.getString("group_id");
                                lstPlanId = new ArrayList<>();
                                lot_short_num_list=new ArrayList<>();
                                queryBigData2 = new Query();
                                queryBigData2.addCriteria(Criteria.where("group_id").is(group_id2));
                                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                                        sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                                while (iteratorBigData.hasNext()) {
                                    Document docItemBigData2 = iteratorBigData.next();
                                    lstPlanId.add(docItemBigData2.getString("plan_id"));
                                    String lot_short_num_1=docItemBigData2.getString("lot_short_num");
                                    if(!lot_short_num_1.equals("") && !lot_short_num_list.contains(lot_short_num_1)) lot_short_num_list.add(lot_short_num_1);
                                }
                                if (iteratorBigData.hasNext()) iteratorBigData.close();
                                //第二次判断
                                Integer panel_ng_code2 = CheckPanelByItemInfo(station_id,select_plan_id2,lstPlanId, panel_barcode,
                                        select_lot_short_num2, select_item_info2, "N", ng_auto_pass_value,
                                        face_code2_int,eap_face_code2,lot_short_num_list);
                                if (panel_ng_code2 != 1){
                                    plan_id=select_plan_id2;
                                    panel_ng_code=panel_ng_code2;
                                    material_code_P001 = docItemBigData.getString("material_code");
                                    lot_version_P002 = docItemBigData.getString("lot_level");
                                    lot_num_P003=docItemBigData.getString("lot_num");
                                    lot_num=docItemBigData.getString("lot_num");
                                    lot_short_num = docItemBigData.getString("lot_short_num");
                                    item_info = docItemBigData.getString("item_info");
                                    first_plan_count_P004 = String.valueOf(docItemBigData.getInteger("plan_lot_count"));
                                    panel_index=docItemBigData.getInteger("pallet_use_count")+1;
                                    attribute1_P005 = docItemBigData.getString("attribute1");
                                    attribute2_P006 = docItemBigData.getString("attribute2");
                                    ng_control = docItemBigData.getInteger("panel_model");
                                    prod_id = material_code_P001;
                                    prod_version = lot_version_P002;
                                    process_code = attribute1_P005;
                                    if (docItemBigData.containsKey("attribute3")) {
                                        String attribute_else = docItemBigData.getString("attribute3");
                                        if (attribute_else != null && !attribute_else.equals("")) {
                                            attr_else = JSONObject.parseObject(attribute_else);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            if(panel_ng_code>0){
                if(panel_ng_code==4) panel_status = "NG_PASS";
                else{
                    if(ng_auto_pass_value.equals("1")) panel_status = "NG_PASS";
                    else panel_status = "NG";
                }
            }
            else panel_status = "OK";
            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);


            //1.首先记录OK统计和NG统计
            if (panel_barcode.equals("NoRead") || panel_barcode.equals("")){
                String ngAllCount = opCommonFunc.ReadCellOneRedisValue(station_code, "UnLoad",
                        "Eap", "EapStatus", "PanelNgCount");
                if (ngAllCount == null || ngAllCount.equals("")) {
                    errorMsg = "读取标签{PanelNgCount}失败";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                Integer ngAllCountInt = Integer.parseInt(ngAllCount) + 1;
                errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, "UnLoad",
                        "Eap", "EapStatus", "PanelNgCount", "AIS",
                        String.valueOf(ngAllCountInt), false);
                if (!errorMsg.equals("")) {
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }
            else{
                String okAllCount = opCommonFunc.ReadCellOneRedisValue(station_code, "UnLoad",
                        "Eap", "EapStatus", "PanelOkCount");
                if (okAllCount == null || okAllCount.equals("")) {
                    errorMsg = "读取标签{PanelOkCount}失败";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                Integer okAllCountInt = Integer.parseInt(okAllCount) + 1;
                errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, "UnLoad",
                        "Eap", "EapStatus", "PanelOkCount", "AIS",
                        String.valueOf(okAllCountInt), false);
                if (!errorMsg.equals("")) {
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }

            //2.若是OK或者NG_PASS更改计数
            if(panel_status.equals("OK") || panel_status.equals("NG_PASS")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                Update updateBigData = new Update();
                updateBigData.set("pallet_use_count", panel_index);
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //3.读码上报
            JSONArray item_attr_list = new JSONArray();
            JSONObject jbAttr = new JSONObject();
            jbAttr.put("item_id", "P001");
            jbAttr.put("item_value", material_code_P001);
            item_attr_list.add(jbAttr);
            jbAttr = new JSONObject();
            jbAttr.put("item_id", "P002");
            jbAttr.put("item_value", lot_version_P002);
            item_attr_list.add(jbAttr);
            jbAttr = new JSONObject();
            jbAttr.put("item_id", "P003");
            jbAttr.put("item_value", lot_num_P003);
            item_attr_list.add(jbAttr);
            jbAttr = new JSONObject();
            jbAttr.put("item_id", "P004");
            jbAttr.put("item_value", first_plan_count_P004);
            item_attr_list.add(jbAttr);
            jbAttr = new JSONObject();
            jbAttr.put("item_id", "P005");
            jbAttr.put("item_value", attribute1_P005);
            item_attr_list.add(jbAttr);
            jbAttr = new JSONObject();
            jbAttr.put("item_id", "P006");
            jbAttr.put("item_value", attribute2_P006);
            item_attr_list.add(jbAttr);
            if (isP1Factory) {
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P007");
                jbAttr.put("item_value", "0");
                item_attr_list.add(jbAttr);
            }
            if(isNewVersion){
                item_attr_list = new JSONArray();
                //判断内容
                String new_panel_status="OK";
                if (panel_ng_code == 0) new_panel_status = "OK";
                else if(panel_ng_code == 2) new_panel_status = "NoRead";
                else if(panel_ng_code == 1) new_panel_status = "Mix";
                else if(panel_ng_code == 8) new_panel_status = "No Mission";
                else if(panel_ng_code == 4) new_panel_status = "No Control";
                else if(panel_ng_code == 5) new_panel_status = "Time Out";
                else if(panel_ng_code == 11) new_panel_status = "Dummy";
                if(lot_num==null || lot_num.equals("")) new_panel_status = "No Mission";
                if (ng_control == 1) new_panel_status = "No Control";
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P001");
                jbAttr.put("item_value", new_panel_status);
                item_attr_list.add(jbAttr);
                //2.面次
                String new_face_code="NoRead";
                if(face_code2_int==1) new_face_code="01";
                if(face_code2_int==2) new_face_code="02";
                if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) new_face_code = "NoRead";
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P002");
                jbAttr.put("item_value", new_face_code);
                item_attr_list.add(jbAttr);
                //3.读码上报方式
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P003");
                jbAttr.put("item_value", "2DReader");
                item_attr_list.add(jbAttr);
            }
            else{
                //黄石定颖新增
                //P011
                String new_panel_status = "1";
                if (panel_ng_code == 0) new_panel_status = "1";
                else if (panel_ng_code == 2) new_panel_status = "2";
                else if (panel_ng_code == 1) new_panel_status = "3";
                else if (panel_ng_code == 8) new_panel_status = "6";
                else if (panel_ng_code == 4) new_panel_status = "4";
                else if (panel_ng_code == 5) new_panel_status = "5";
                else if (panel_ng_code == 7) new_panel_status = "7";
                if (lot_num == null || lot_num.equals("")) new_panel_status = "6";
                if (ng_control == 1) new_panel_status = "4";
                //P012
                String new_up_way = "1";
                //增加到属性组中
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P011");
                jbAttr.put("item_value", new_panel_status);
                item_attr_list.add(jbAttr);
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P012");
                jbAttr.put("item_value", new_up_way);
                item_attr_list.add(jbAttr);
            }
            String slot_no = String.format("%03d", panel_index);
            lot_num=lot_num_P003;
            if(panel_status.equals("NG")){
                if(!isUnLoadZcj){
                    eapDySendFlowFunc.PanelDataUploadReport(station_id_long, station_code, panel_barcode,
                            slot_no, item_attr_list, "N", "N", "01", lot_num, prod_id, prod_version,
                            process_code, attr_else, "", "0", 0,0,"");
                    String zc_code="2";
                    if(panel_ng_code==6) zc_code="3";
                    if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) zc_code="1";
                    AddEachPosition(station_code,panel_barcode,zc_code);
                }
            }
            else{
                eapDySendFlowFunc.PanelDataUploadReport(station_id_long, station_code, panel_barcode,
                        slot_no, item_attr_list, "N", "N", "01", lot_num, prod_id, prod_version,
                        process_code, attr_else, "", "0", 0,ng_control,"");
            }

            //判断是否要传递给下游
            String InLineWayValue=opCommonFunc.ReadCellOneRedisValue(station_code,"UnLoad",
                    "Ais","AisConfig","InLineWay");
            if(InLineWayValue.equals("WEBAPI")){
                eapDySendFlowFunc.EachPanelDataDownLoad(station_code, lot_num, panel_barcode, panel_status,slot_no);
            }
            else if(InLineWayValue.equals("UDP")){
                JSONObject postParas=new JSONObject();
                JSONObject request_head=eapDyInterfCommon.CreateRequestHeader("EachPanelDataDownLoad",station_code);
                postParas.put("request_head",request_head);
                com.alibaba.fastjson2.JSONObject request_body=new com.alibaba.fastjson2.JSONObject();
                request_body.put("lot_id",lot_num);
                request_body.put("panel_id",panel_barcode);
                request_body.put("panel_status",panel_status);
                postParas.put("request_body",request_body);
                String requestParas=postParas.toString();
                opCommonFunc.WriteCellOneTagValue(station_code,"UnLoad",
                        "UdpServer","UdpStatus","UdpSendMsg","AIS",requestParas,false);
            }

            if(panel_status.equals("OK") || panel_status.equals("NG_PASS")){
                panel_ng_code=0;
            }
            result = panel_flag + "," + lot_num + "," + panel_ng_code + "," + panel_status + "," + panel_ng_msg;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "收板机Panel提前验证异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //子方法判断
    private Integer CheckPanelByItemInfo(String station_id,String plan_id,List<String>lstPlanId, String panel_barcode,
                                        String lot_short_num, String item_info, String strip_flag, String ng_auto_pass_value,
                                        Integer face_code,Integer eap_face_code, List<String> lot_short_num_list2) throws Exception {
        Integer panel_ng_code = 0;
        String panel_ng_msg = "";
        String panel_status = "OK";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String mePanelQueueTable="a_eap_me_panel_queue";
        try {
            //判断是否为Strip
            if (strip_flag.equals("Y")) {
                if (item_info != null && !item_info.equals("")) {
                    JSONArray jaStripList = JSONArray.parseArray(item_info);
                    if (jaStripList != null && jaStripList.size() > 0) {
                        panel_status = "NG";
                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        for (int m = 0; m < jaStripList.size(); m++) {
                            JSONObject jbStripItem = jaStripList.getJSONObject(m);
                            String strip_barcode = jbStripItem.getString("strip_barcode");
                            String strip_level = jbStripItem.getString("strip_level");
                            String strip_status = jbStripItem.getString("strip_status");
                            if (strip_barcode.equals(panel_barcode)) {
                                if (strip_status.equals("OK")) {
                                    panel_status = "OK";
                                    panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                }
                                break;
                            }
                        }
                    }
                }
            } else {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                if (pnListCount > 0) {
                    Query queryBigDataD = new Query();
                    queryBigDataD.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                    long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                    if (okCount <= 0) {
                        if (ng_auto_pass_value.equals("1")) {
                            panel_status = "NG_PASS";
                            panel_ng_code = 4;
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            panel_status = "NG";
                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        }
                    }
                }
                if(panel_ng_code==0) {
                    //需要判断简码LIST信息,新增板件判断逻辑,240315 by ZhouJun
                    List<String> lot_short_num_list = lot_short_num_list2;
                    if (item_info != null && !item_info.equals("")) {
                        JSONArray item_info_list = JSONArray.parseArray(item_info);
                        if (item_info_list != null && item_info_list.size() > 0) {
                            int lotShortNumListCount = 0;
                            for (int i = 0; i < item_info_list.size(); i++) {
                                JSONObject jbItem = item_info_list.getJSONObject(i);
                                String item_id = jbItem.getString("item_id");
                                String item_value = jbItem.getString("item_value");
                                if (item_id.equals("S026") && item_value != null && !item_value.equals("") && !item_value.contains("NA")) {
                                    try {
                                        lotShortNumListCount = Integer.parseInt(item_value);
                                    } catch (Exception intError) {
                                        lotShortNumListCount = 0;
                                    }
                                    break;
                                }
                            }
                            if (lotShortNumListCount > 0) {
                                List<String> lotShortColList = new ArrayList<>();
                                for (int i = 1; i <= lotShortNumListCount; i++) {
                                    String SName = "S" + String.format("%03d", 26 + i);
                                    lotShortColList.add(SName);
                                }
                                for (int i = 0; i < item_info_list.size(); i++) {
                                    JSONObject jbItem = item_info_list.getJSONObject(i);
                                    String item_id = jbItem.getString("item_id");
                                    String item_value = jbItem.getString("item_value");
                                    if (lotShortColList.contains(item_id) &&
                                            item_value != null && !item_value.equals("")) {
                                        if (!lot_short_num_list.contains(item_value))
                                            lot_short_num_list.add(item_value);
                                    }
                                }
                            }
                        }
                    }
                    String lot_short_num2=lot_short_num;
                    if(eapDyInterfCommon.CheckDyVersion(3)){
                        if(lot_short_num2.length()>=12){
                            lot_short_num2=lot_short_num2.substring(0,12);
                        }
                    }
                    if (!lot_short_num2.equals("") && !lot_short_num_list.contains(lot_short_num2))
                        lot_short_num_list.add(lot_short_num2);
                    //新增逻辑,简码判断
                    if (lot_short_num_list.size() > 0 && pnListCount<=0) {
                        //默认混批
                        panel_status = "NG";
                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);

                        for (int i = 0; i < lot_short_num_list.size(); i++) {
                            String lot_short_num_item = lot_short_num_list.get(i);
                            if (panel_barcode.startsWith(lot_short_num_item)) {
                                panel_status = "OK";
                                panel_ng_code = 0;
                                panel_ng_msg = "";
                                break;
                            }
                        }
                    }

                    //判断AOI下发数据是否OK
                    String SbPanelFromUp=cFuncDbSqlResolve.GetParameterValue("SbPanelFromUp");
                    String up_panel_status="OK";
                    if(SbPanelFromUp.equals("Y") && panel_ng_code==0){
                        up_panel_status="NG";
                        queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                        queryBigData.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                        queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                        MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(mePanelQueueTable).find(queryBigData.getQueryObject()).
                                sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                        if (iteratorBigData.hasNext()){
                            Document docItemBigData = iteratorBigData.next();
                            up_panel_status=docItemBigData.getString("panel_status");
                            iteratorBigData.close();
                        }
                    }
                    if(up_panel_status.equals("NG")){
                        panel_status = "NG";
                        panel_ng_code=9;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    }
                    //判断面次信息
                    if(eap_face_code>0 && panel_ng_code==0){
                        if(face_code!=eap_face_code){
                            String eap_face_code_str="正面";
                            if(eap_face_code!=1) eap_face_code_str="反面";
                            String face_code_str="正面";
                            if(face_code!=1) face_code_str="反面";

                            panel_status="NG";
                            panel_ng_code=6;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5Hold,6面次错误,7其他定义
                            panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                            //增加弹窗报错以及Alarm报错
                            opCommonFunc.SaveCimMessage(Long.parseLong(station_id),"1","-6","AIS",
                                    "检测到pnl号{"+panel_barcode+"}识别面次为{"+face_code_str+"}与工单要求面次{"+eap_face_code_str+"}不一致,判定为NG",5);
                        }
                    }
                    if (panel_ng_code > 0 && ng_auto_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    }
                }
            }
        } catch (Exception ex) {
            panel_ng_code=99;
            log.error(ex.getMessage());
        }
        return panel_ng_code;
    }

    //增加暂存信息
    @Async
    public void AddEachPosition(String station_code,String panel_barcode,String zc_code) throws Exception{
        try{
            if(!eapDyInterfCommon.CheckDyVersion(3)) return;
            String eachTableName="a_eap_me_eachposition";
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String each_position_id=CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("each_position_id",each_position_id);
            mapBigDataRow.put("station_code",station_code);
            mapBigDataRow.put("panel_barcode",panel_barcode);
            mapBigDataRow.put("zc_code",zc_code);
            mongoTemplate.insert(mapBigDataRow,eachTableName);
            //再查询出来
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            List<Map> panelList = mongoTemplate.find(queryBigData, Map.class, eachTableName);
            JSONArray jaList=new JSONArray();
            if(panelList!=null && panelList.size()>0){
                for(int i=0;i<panelList.size();i++){
                    Map map=panelList.get(i);
                    String slot_no=String.format("%03d", i+1);
                    String temporary_storage_reason=map.get("zc_code").toString();
                    String pnl_id=map.get("panel_barcode").toString();
                    JSONObject jbItem=new JSONObject();
                    jbItem.put("slot_no",slot_no);
                    jbItem.put("temporary_storage_reason",temporary_storage_reason);
                    jbItem.put("pnl_id",pnl_id);
                    jaList.add(jbItem);
                }
            }
            eapDySendFlowFunc.EachPositionReport(station_code, jaList);
        } catch (Exception ex) {
            log.error(ex.getMessage());
        }
    }

    //收板机Panel提前验证,不做存储
    @RequestMapping(value = "/EapDyUnLoadMeLaserSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyUnLoadMeLaserSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/unload/EapDyUnLoadMeLaserSave";
        String transResult = "";
        String errorMsg = "";
        String meLaserTable="a_eap_me_laser";
        try{
            //保存镭射数据
            String group_lot_num=jsonParas.getString("group_lot_num");
            JSONArray laser_list=jsonParas.getJSONArray("laser_list");
            if(group_lot_num!=null && !group_lot_num.equals("") && laser_list!=null && laser_list.size()>0){
                String sqlStation = "select " +
                        "station_id,station_code,COALESCE(station_attr,'') station_attr " +
                        "from sys_fmod_station " +
                        "where enable_flag='Y' and station_attr='UnLoad' " +
                        "order by station_id LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                        false, null, "");
                if(itemListStation!=null && itemListStation.size()>0){
                    Long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
                    Date item_date_laser = CFuncUtilsSystem.GetMongoISODate("");
                    long item_date_val_laser=CFuncUtilsSystem.GetMongoDataValue(item_date_laser);
                    List<Map<String, Object>> lstLaserDocuments = new ArrayList<>();
                    for(int i=0;i<laser_list.size();i++){
                        JSONObject jbItem=laser_list.getJSONObject(i);
                        String laser_id=CFuncUtilsSystem.CreateUUID(true);
                        Map<String, Object> mapLaserRow = new HashMap<>();
                        mapLaserRow.put("item_date",item_date_laser);
                        mapLaserRow.put("item_date_val",item_date_val_laser);
                        mapLaserRow.put("laser_id",laser_id);
                        mapLaserRow.put("station_id",station_id);
                        mapLaserRow.put("group_lot_num",group_lot_num);
                        mapLaserRow.put("lot_num",jbItem.getString("lot_num"));
                        mapLaserRow.put("laser_time",jbItem.getString("laser_time"));
                        mapLaserRow.put("laser_panel_id",jbItem.getString("laser_panel_id"));
                        mapLaserRow.put("laser_barcode",jbItem.getString("laser_barcode"));
                        mapLaserRow.put("laser_array_id",jbItem.getString("laser_array_id"));
                        mapLaserRow.put("laser_pcs_id",jbItem.getString("laser_pcs_id"));
                        lstLaserDocuments.add(mapLaserRow);
                    }
                    if (lstLaserDocuments.size() > 0) mongoTemplate.insert(lstLaserDocuments, meLaserTable);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "Exception:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
