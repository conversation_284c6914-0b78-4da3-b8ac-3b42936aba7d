package com.api.pack.core.recipe;

import com.api.base.IMongoBasicService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class MeRecipeService extends IMongoBasicService<MeRecipe, MeRecipeRepository>
{
    public MeRecipeService(MeRecipeRepository repository)
    {
        super(repository);
    }
}
