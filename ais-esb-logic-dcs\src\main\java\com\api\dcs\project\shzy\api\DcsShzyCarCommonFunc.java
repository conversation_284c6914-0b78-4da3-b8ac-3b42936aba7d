package com.api.dcs.project.shzy.api;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * (上海中冶)天车特殊方法
 * 1.转移调度路线到历史表
 * 2.插入锁定调度任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@Service
@Slf4j
public class DcsShzyCarCommonFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.转移调度路线到历史表
    public void MoveLockCarRouteToHis(String userID, HttpServletRequest request,String apiRoutePath,
                                  String ware_house) throws Exception{
        String meCarTaskHisTable="b_dcs_wms_me_car_task_his";
        String meCarRouteHisTable="b_dcs_wms_me_car_route_his";
        String nowDateTime=CFuncUtilsSystem.GetNowDateTime("");

        //1.先转移锁定调度任务表
        String sqlLockCarTaskSel="select " +
                "COALESCE(a.created_by,'') created_by," +
                "TO_CHAR(a.creation_date, 'yyyy-MM-dd HH:mm:ss') creation_date," +
                "a.car_task_id,a.cycle_code,a.car_code,a.ware_house," +
                "a.task_from,a.task_way,a.task_type,a.task_num," +
                "COALESCE(a.serial_num,'') serial_num," +
                "COALESCE(a.lot_num,'') lot_num," +
                "COALESCE(a.model_type,'') model_type," +
                "a.from_stock_code,a.to_stock_code," +
                "COALESCE(a.m_length,0) m_length," +
                "COALESCE(a.m_width,0) m_width," +
                "COALESCE(a.m_height,0) m_height," +
                "COALESCE(a.m_weight,0) m_weight," +
                "TO_CHAR(a.task_start_date, 'yyyy-MM-dd HH:mm:ss') task_start_date," +
                "TO_CHAR(a.task_end_date, 'yyyy-MM-dd HH:mm:ss') task_end_date," +
                "COALESCE(a.cost_time,0) cost_time," +
                "COALESCE(a.finish_check_gd_flag,'') finish_check_gd_flag," +
                "TO_CHAR(a.finish_check_gd_date, 'yyyy-MM-dd HH:mm:ss') finish_check_gd_date," +
                "COALESCE(a.check_model_flag,'') check_model_flag," +
                "TO_CHAR(a.check_model_date, 'yyyy-MM-dd HH:mm:ss') check_model_date," +
                "COALESCE(a.check_model_result,'') check_model_result," +
                "TO_CHAR(a.tell_start_date, 'yyyy-MM-dd HH:mm:ss') tell_start_date," +
                "TO_CHAR(a.tell_cancel_date, 'yyyy-MM-dd HH:mm:ss') tell_cancel_date," +
                "TO_CHAR(a.tell_stop_date, 'yyyy-MM-dd HH:mm:ss') tell_stop_date," +
                "COALESCE(a.tell_start_by,'') tell_start_by," +
                "COALESCE(a.tell_cancel_by,'') tell_cancel_by," +
                "COALESCE(a.tell_stop_by,'') tell_stop_by," +
                "a.execute_status," +
                "COALESCE(a.execute_error_msg,'') execute_error_msg " +
                "from b_dcs_wms_lock_car_task a " +
                "where a.ware_house='"+ware_house+"' " +
                "order by a.car_task_id";
        List<Map<String, Object>> itemListLockCarTask=cFuncDbSqlExecute.ExecSelectSql(userID,sqlLockCarTaskSel,
                false,request,apiRoutePath);
        if(itemListLockCarTask!=null && itemListLockCarTask.size()>0){
            List<Map<String, Object>> lstDocuments=new ArrayList<>();
            for(Map<String, Object> mapItem : itemListLockCarTask){
                Map<String, Object> mapDataItem=new HashMap<>();
                //先将时间做转换
                String creation_date="";
                String task_start_date="";
                String task_end_date="";
                String finish_check_gd_date="";
                String check_model_date="";
                String tell_start_date="";
                String tell_cancel_date="";
                String tell_stop_date="";
                if(mapItem.containsKey("creation_date") && mapItem.get("creation_date")!=null)
                    creation_date=mapItem.get("creation_date").toString();
                if(mapItem.containsKey("task_end_date") && mapItem.get("task_end_date")!=null)
                    task_end_date=mapItem.get("task_end_date").toString();
                if(mapItem.containsKey("finish_check_gd_date") && mapItem.get("finish_check_gd_date")!=null)
                    finish_check_gd_date=mapItem.get("finish_check_gd_date").toString();
                if(mapItem.containsKey("check_model_date") && mapItem.get("check_model_date")!=null)
                    check_model_date=mapItem.get("check_model_date").toString();
                if(mapItem.containsKey("tell_start_date") && mapItem.get("tell_start_date")!=null)
                    tell_start_date=mapItem.get("tell_start_date").toString();
                if(mapItem.containsKey("tell_cancel_date") && mapItem.get("tell_cancel_date")!=null)
                    tell_cancel_date=mapItem.get("tell_cancel_date").toString();
                if(mapItem.containsKey("tell_stop_date") && mapItem.get("tell_stop_date")!=null)
                    tell_stop_date=mapItem.get("tell_stop_date").toString();

                if(creation_date==null || creation_date.equals("")) creation_date=nowDateTime;
                if(task_start_date==null) task_start_date="";
                if(task_end_date==null) task_end_date="";
                if(finish_check_gd_date==null) finish_check_gd_date="";
                if(check_model_date==null) check_model_date="";
                if(tell_start_date==null) tell_start_date="";
                if(tell_cancel_date==null) tell_cancel_date="";
                if(tell_stop_date==null) tell_stop_date="";
                Date item_date = CFuncUtilsSystem.GetMongoISODate(creation_date);
                long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
                //制作集合
                mapDataItem.put("item_date",item_date);
                mapDataItem.put("item_date_val",item_date_val);
                mapDataItem.put("car_task_id",Long.parseLong(mapItem.get("car_task_id").toString()));
                mapDataItem.put("cycle_code",mapItem.get("cycle_code").toString());
                mapDataItem.put("car_code",mapItem.get("car_code").toString());
                mapDataItem.put("ware_house",mapItem.get("ware_house").toString());
                mapDataItem.put("task_from",mapItem.get("task_from").toString());
                mapDataItem.put("task_way",mapItem.get("task_way").toString());
                mapDataItem.put("task_type",mapItem.get("task_type").toString());
                mapDataItem.put("task_num",mapItem.get("task_num").toString());
                mapDataItem.put("serial_num",mapItem.get("serial_num").toString());
                mapDataItem.put("lot_num",mapItem.get("lot_num").toString());
                mapDataItem.put("material_code",mapItem.get("model_type").toString());
                mapDataItem.put("material_des","");
                mapDataItem.put("model_type",mapItem.get("model_type").toString());
                mapDataItem.put("m_length",Double.parseDouble(mapItem.get("m_length").toString()));
                mapDataItem.put("m_width",Double.parseDouble(mapItem.get("m_width").toString()));
                mapDataItem.put("m_height",Double.parseDouble(mapItem.get("m_height").toString()));
                mapDataItem.put("m_weight",Double.parseDouble(mapItem.get("m_weight").toString()));
                mapDataItem.put("m_texture","");
                mapDataItem.put("from_stock_code",mapItem.get("from_stock_code").toString());
                mapDataItem.put("to_stock_code",mapItem.get("to_stock_code").toString());
                mapDataItem.put("task_start_date",task_start_date);
                mapDataItem.put("task_end_date",task_end_date);
                mapDataItem.put("cost_time",Long.parseLong(mapItem.get("cost_time").toString()));
                mapDataItem.put("finish_check_gd_flag",mapItem.get("finish_check_gd_flag").toString());
                mapDataItem.put("finish_check_gd_date",finish_check_gd_date);
                mapDataItem.put("check_model_flag",mapItem.get("check_model_flag").toString());
                mapDataItem.put("check_model_date",check_model_date);
                mapDataItem.put("check_model_result",mapItem.get("check_model_result").toString());
                mapDataItem.put("tell_start_date",tell_start_date);
                mapDataItem.put("tell_cancel_date",tell_cancel_date);
                mapDataItem.put("tell_stop_date",tell_stop_date);
                mapDataItem.put("tell_start_by",mapItem.get("tell_start_by").toString());
                mapDataItem.put("tell_cancel_by",mapItem.get("tell_cancel_by").toString());
                mapDataItem.put("tell_stop_by",mapItem.get("tell_stop_by").toString());
                mapDataItem.put("execute_status",mapItem.get("execute_status").toString());
                mapDataItem.put("execute_error_msg",mapItem.get("execute_error_msg").toString());
                lstDocuments.add(mapDataItem);
            }
            mongoTemplate.insert(lstDocuments,meCarTaskHisTable);
        }

        //2.转移天车调度路线到历史
        String sqlLockCarRouteSel="select " +
                "COALESCE(created_by,'') created_by," +
                "TO_CHAR(creation_date, 'yyyy-MM-dd HH:mm:ss') creation_date," +
                "car_route_id,ware_house,car_code,car_task_type," +
                "step_code,step_name,location_x,location_y,location_z,step_status," +
                "COALESCE(step_start_limit_id,0) step_start_limit_id," +
                "COALESCE(step_start_limit_step,'') step_start_limit_step," +
                "COALESCE(step_start_limit_status,'') step_start_limit_status," +
                "need_check_gd_flag,need_check_model_flag," +
                "COALESCE(if_tags_list,'') if_tags_list," +
                "COALESCE(if_tags_value,'') if_tags_value," +
                "COALESCE(car_task_id,0) car_task_id," +
                "COALESCE(stock_count,0) stock_count," +
                "COALESCE(check_gd_tag,'') check_gd_tag," +
                "COALESCE(check_gd_value,'') check_gd_value," +
                "COALESCE(check_model_tag,'') check_model_tag," +
                "para_f,para_r,para_a,para_h " +
                "from b_dcs_wms_lock_car_route " +
                "where ware_house='"+ware_house+"' " +
                "order by car_route_id";
        List<Map<String, Object>> itemListLockCarRoute=cFuncDbSqlExecute.ExecSelectSql(userID,sqlLockCarRouteSel,
                false,request,apiRoutePath);
        if(itemListLockCarRoute!=null && itemListLockCarRoute.size()>0){
            List<Map<String, Object>> lstDocuments=new ArrayList<>();
            for(Map<String, Object> mapItem : itemListLockCarRoute){
                Map<String, Object> mapDataItem=new HashMap<>();
                //先将时间做转换
                String creation_date="";
                if(mapItem.containsKey("creation_date") && mapItem.get("creation_date")!=null)
                    creation_date=mapItem.get("creation_date").toString();
                if(creation_date==null || creation_date.equals("")) creation_date=nowDateTime;
                Date item_date = CFuncUtilsSystem.GetMongoISODate(creation_date);
                long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
                //制作集合
                mapDataItem.put("item_date",item_date);
                mapDataItem.put("item_date_val",item_date_val);
                mapDataItem.put("car_route_id",Long.parseLong(mapItem.get("car_route_id").toString()));
                mapDataItem.put("ware_house",mapItem.get("ware_house").toString());
                mapDataItem.put("car_code",mapItem.get("car_code").toString());
                mapDataItem.put("car_task_type",mapItem.get("car_task_type").toString());
                mapDataItem.put("step_code",mapItem.get("step_code").toString());
                mapDataItem.put("step_name",mapItem.get("step_name").toString());
                mapDataItem.put("location_x",Integer.parseInt(mapItem.get("location_x").toString()));
                mapDataItem.put("location_y",Integer.parseInt(mapItem.get("location_y").toString()));
                mapDataItem.put("location_z",Integer.parseInt(mapItem.get("location_z").toString()));
                mapDataItem.put("step_status",mapItem.get("step_status").toString());
                mapDataItem.put("step_start_limit_id",Long.parseLong(mapItem.get("step_start_limit_id").toString()));
                mapDataItem.put("step_start_limit_step",mapItem.get("step_start_limit_step").toString());
                mapDataItem.put("step_start_limit_status",mapItem.get("step_start_limit_status").toString());
                mapDataItem.put("need_check_gd_flag",mapItem.get("need_check_gd_flag").toString());
                mapDataItem.put("need_check_model_flag",mapItem.get("need_check_model_flag").toString());
                mapDataItem.put("if_tags_list",mapItem.get("if_tags_list").toString());
                mapDataItem.put("if_tags_value",mapItem.get("if_tags_value").toString());
                mapDataItem.put("car_task_id",Long.parseLong(mapItem.get("car_task_id").toString()));
                mapDataItem.put("stock_count",Integer.parseInt(mapItem.get("stock_count").toString()));
                mapDataItem.put("check_gd_tag",mapItem.get("check_gd_tag").toString());
                mapDataItem.put("check_gd_value",mapItem.get("check_gd_value").toString());
                mapDataItem.put("check_model_tag",mapItem.get("check_model_tag").toString());
                mapDataItem.put("para_f",Integer.parseInt(mapItem.get("para_f").toString()));
                mapDataItem.put("para_r",Integer.parseInt(mapItem.get("para_r").toString()));
                mapDataItem.put("para_a",Integer.parseInt(mapItem.get("para_a").toString()));
                mapDataItem.put("para_h",Integer.parseInt(mapItem.get("para_h").toString()));
                lstDocuments.add(mapDataItem);
            }
            mongoTemplate.insert(lstDocuments,meCarRouteHisTable);
        }
    }

    //2.插入锁定调度任务
    public Long LockCarTaskIns(String userID,HttpServletRequest request,String apiRoutePath,
                               String cycle_code,String car_code,String ware_house,
                               String task_id,Long model_id,String task_num,
                               String task_from,String task_way,String task_type,
                               String serial_num,String lot_num,String model_type,
                               Long stock_d_id,String from_stock_code,String to_stock_code,
                               Double m_length,Double m_width,Double m_height,Double m_weight) throws Exception{
        Long car_task_id=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_lock_car_task_id_seq",true);
        String nowDateTime=CFuncUtilsSystem.GetNowDateTime("");
        String sqlCarTaskIns="insert into b_dcs_wms_lock_car_task " +
                "(created_by,creation_date,car_task_id,cycle_code,car_code," +
                "ware_house,task_id,model_id,task_num,task_from," +
                "task_way,task_type,serial_num,lot_num,model_type," +
                "stock_d_id,execute_status,from_stock_code,to_stock_code," +
                "m_length,m_width,m_height,m_weight) values " +
                "('"+userID+"','"+nowDateTime+"',"+car_task_id+",'"+cycle_code+"','"+car_code+"'," +
                "'"+ware_house+"','"+task_id+"',"+model_id+",'"+task_num+"','"+task_from+"'," +
                "'"+task_way+"','"+task_type+"','"+serial_num+"','"+lot_num+"','"+model_type+"'," +
                ""+stock_d_id+",'PLAN','"+from_stock_code+"','"+to_stock_code+"'," +
                ""+m_length+","+m_width+","+m_height+","+m_weight+")";
        cFuncDbSqlExecute.ExecUpdateSql(userID,sqlCarTaskIns,false,request,apiRoutePath);
        return car_task_id;
    }
}
