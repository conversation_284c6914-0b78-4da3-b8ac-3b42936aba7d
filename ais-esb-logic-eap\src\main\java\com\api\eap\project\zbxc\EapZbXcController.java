package com.api.eap.project.zbxc;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * （淄博芯材 配板机处理逻辑
 * 1.配扳机扫描验证
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/zbxc/")
public class EapZbXcController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private PlanCommonFunc planCommonFunc;

    //1.放板机Panel校验
    @RequestMapping(value = "/EapCheckAreYouThereReply", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapZbXcPbjPanelCheck(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/zbxc/pbj/EapZbXcPbjPanelCheck";
        String transResult = "";
        String errorMsg = "";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            Query query = new Query();
            query.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            query.limit(1);
            query.addCriteria(Criteria.where("esb_interf_code").is("AisAreYouThereReply"));
            query.addCriteria(Criteria.where("success_flag").is("Y"));
            Document one = mongoTemplate.findOne(query,Document.class,"sys_core_esb_interf_log");
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            Assert.notEmpty(itemListStation, "工位配置异常");
            Map<String, Object> stationInfo = itemListStation.stream().findFirst().get();
            if (one==null) {
                opCommonFunc.WriteCellOneTagValue(station_code, stationInfo.get("station_attr").toString(), "Ais", "AisStatus", "AreYouThereBeat", "", "2", true);
                return CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
            }
            String success_flag = one.getString("success_flag");
            SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmss");
            Date item_date_val = df.parse(one.getLong("item_date_val").toString());
            long currentTime = System.currentTimeMillis();
            long differenceInMilliSeconds = currentTime - item_date_val.getTime();
            // 将毫秒差值转换为秒
            long differenceInSeconds = TimeUnit.MILLISECONDS.toSeconds(differenceInMilliSeconds);
            log.debug("next :{}", one);
            log.debug("当前时间减去上次发送心跳的时间:{}-{}={}", currentTime, item_date_val.getTime(), differenceInSeconds);
            String beatTimeout = cFuncDbSqlResolve.GetParameterValue("EAP_ARE_YOU_THERE_BEAT_TIMEOUT");
            log.debug("beatTimeout :{}", beatTimeout);
            log.debug("是否超出时间:{}", differenceInSeconds > Long.parseLong(StringUtils.isEmpty(beatTimeout) ? "0" : beatTimeout));
            if (differenceInSeconds > Long.parseLong(StringUtils.isEmpty(beatTimeout) ? "0" : beatTimeout) || StringUtils.isEmpty(success_flag)) {
                opCommonFunc.WriteCellOneTagValue(station_code, stationInfo.get("station_attr").toString(), "Ais", "AisStatus", "AreYouThereBeat", "", "2", true);
                return CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
            }
            opCommonFunc.WriteCellOneTagValue(station_code, stationInfo.get("station_attr").toString(), "Ais", "AisStatus", "AreYouThereBeat", "", "1", true);
            return CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception e) {
            errorMsg = "异常:" + e.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);

        }
        return transResult;
    }
}
