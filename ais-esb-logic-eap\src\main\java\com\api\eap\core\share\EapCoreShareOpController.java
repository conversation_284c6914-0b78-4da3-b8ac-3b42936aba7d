package com.api.eap.core.share;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 工位与端口共用对外API接口
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/share")
public class EapCoreShareOpController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //判断员工是否进行了登入
    @RequestMapping(value = "/EapCoreShareOpUserIsCheckIn", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapCoreShareOpUserIsCheckIn(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/core/share/EapCoreShareOpUserIsCheckIn";
        String selectResult="";
        String errorMsg="";
        String meStationUserTable="a_eap_me_station_user";
        String checkInFlag="N";
        try{
            String station_id=jsonParas.getString("station_id");
            Query query = new Query();
            query.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            query.addCriteria(Criteria.where("checkout_flag").is("N"));
            long allCount =  mongoTemplate.getCollection(meStationUserTable).countDocuments(query.getQueryObject());
            if(allCount>0) checkInFlag="Y";
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,null,checkInFlag,"",0);
        }
        catch (Exception ex){
            errorMsg= "判断员工是否进行了登入异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //插入CIM消息
    @RequestMapping(value = "/EapCoreShareOpCimIns", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapCoreShareOpCimIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/core/share/EapCoreShareOpCimIns";
        String transResult="";
        String errorMsg="";
        try{
            String station_code=jsonParas.getString("station_code");
            String cim_code=jsonParas.getString("cim_code");
            String cim_from=jsonParas.getString("cim_from");
            String cim_msg=jsonParas.getString("cim_msg");
            if(station_code==null) station_code="";
            if(cim_code==null) cim_code="";
            if(cim_from==null) cim_from="";
            if(cim_msg==null) cim_msg="";
            //1.查询工位ID
            String sqlStation="select station_id,station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStation,
                    false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                errorMsg="未能根据工位号{"+station_code+"}查找到工位基础信息";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();
            String screen_control="1";
            //2.查询CIM基础信息
            String sqlCim="select cim_msg,screen_control " +
                    "from a_eap_fmod_cim " +
                    "where cim_code='"+cim_code+"' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListCim=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlCim,
                    false,request,apiRoutePath);
            if(itemListCim!=null && itemListCim.size()>0){
                cim_msg=itemListCim.get(0).get("cim_msg").toString();
                screen_control=itemListCim.get(0).get("screen_control").toString();
                if("2".equals(screen_control)){
                    screen_control="1";
                    opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                            "Plc","PlcStatus","AisLightBeer","EAP","1",false);
                }
            }
            opCommonFunc.SaveCimMessage(station_id,screen_control,cim_code,cim_from,cim_msg,5);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "插入CIM消息异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
