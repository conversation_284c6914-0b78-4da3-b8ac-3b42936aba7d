package com.api.mes.project.gx;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.lang.model.util.ElementScanner6;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 处理PACK工艺逻辑
 * 1.自动切换PACK订单
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-24
 */
@RestController
@RequestMapping("/mes/project/gx")
public class MesGxPackMeController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //自动切换到下一个订单
    @Transactional
    @RequestMapping(value = "/MesGxPackMoRecipeAutoSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxPackMoRecipeAutoSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxPackMoRecipeAutoSel";
        String selectResult = "";
        String errorMsg = "";
        try {
            String userName = jsonParas.getString("userID");
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String prod_line_id = jsonParas.getString("prod_line_id");

            //先做删除
            String sqlDelete01 = "delete from c_mes_aps_station_mo where station_id=" + station_id + "";
            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlDelete01, false, request, apiRoutePath);

            String sqlMo = "select mo_id,small_model_type,make_order " + "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " + "and mo_status='LINE_UP' and enable_flag='Y' and mo_sign='AUTO' " + "order by mo_order_by LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMo, false, request, apiRoutePath);
            if (itemListMo == null || itemListMo.size() <= 0) {
                errorMsg = "PACK自动订单已消耗完毕,请补充订单";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            String mo_id_new = itemListMo.get(0).get("mo_id").toString();
            String sqlMoUpdate = "update c_mes_aps_plan_mo set " + "mo_status='CARRY_ON' where mo_id=" + mo_id_new + "";
            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlMoUpdate, false, request, apiRoutePath);

            String mo_id = itemListMo.get(0).get("mo_id").toString();
            String small_model_type = itemListMo.get(0).get("small_model_type").toString();
            String make_order = itemListMo.get(0).get("make_order").toString();

            //更新当前工位选择订单
            String sqlInsert01 = "insert into c_mes_aps_station_mo " + "(station_id,make_order,station_code,mo_id) values " + "(" + station_id + ",'" + make_order + "','" + station_code + "'," + mo_id + ")";
            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlInsert01, false, request, apiRoutePath);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "自动切换到下一个订单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更新PACK自动上线数量
    @Transactional
    @RequestMapping(value = "/MesGxPackMoUpdateOnLineCount", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxPackMoUpdateOnLineCount(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxPackMoUpdateOnLineCount";
        String transResult = "";
        String errorMsg = "";
        try {
            String userName = jsonParas.getString("userID");
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String prod_line_id = jsonParas.getString("prod_line_id");
            String make_order = jsonParas.getString("make_order");
            String sqlMo = "select mo_id,mo_plan_count,mo_online_count " + "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " + "and (mo_status='SELECTED' or mo_status='CARRY_ON') and enable_flag='Y' " + "and make_order='" + make_order + "' " + "and mo_sign='AUTO' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(userName, sqlMo, false, request, apiRoutePath);
            if (itemListMo == null || itemListMo.size() <= 0) {
                errorMsg = "人为修改为订单{" + make_order + "}状态,导致无法增加该订单上线完工数量";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String mo_id = itemListMo.get(0).get("mo_id").toString();
            Integer mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
            Integer mo_online_count = Integer.parseInt(itemListMo.get(0).get("mo_online_count").toString());
            String mo_status = "CARRY_ON";
            if (mo_online_count + 1 >= mo_plan_count) mo_status = "COMPLETE";
            String sqlUpdate = "update c_mes_aps_plan_mo set " + "mo_online_count=mo_online_count+1," + "mo_status='" + mo_status + "' " + "where mo_id=" + mo_id;
            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlUpdate, false, request, apiRoutePath);
            if (mo_status.equals("COMPLETE")) {
                MesGxPackMoRecipeAutoSel(jsonParas, request);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "更新PACK自动上线数量异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


    //记录车辆码和PACK的关系
    @RequestMapping(value = "/MesGxCarCodePackRelIns", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxCarCodePackRelIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxCarCodePackRelIns";
        String transResult = "";
        String errorMsg = "";
        String meBarcodeRelTable = "c_mes_me_car_pack_rel";
        try {
            String car_code = jsonParas.getString("car_code");
            String pack_code = jsonParas.getString("pack_code");
            String make_order = jsonParas.getString("make_order");
            //格式化新增数据
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            //新增
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            Map<String, Object> mapDataItem = new HashMap<>();
            String material_rel_id = CFuncUtilsSystem.CreateUUID(true);
            mapDataItem.put("item_date", item_date);
            mapDataItem.put("item_date_val", item_date_val);
            mapDataItem.put("barcode_rel_id", material_rel_id);
            mapDataItem.put("make_order", make_order);
            mapDataItem.put("car_code", car_code);
            mapDataItem.put("pack_code", pack_code);
            //上传标识
            mapDataItem.put("up_flag", "N");
            mapDataItem.put("up_ng_code", -1);
            mapDataItem.put("up_ng_msg", "");
            mapDataItem.put("enable_flag", "Y");
            lstDocuments.add(mapDataItem);
            mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, meBarcodeRelTable).insert(lstDocuments).execute();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "记录车辆码和PACK的关系异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //查询车辆码和PACK的关系
    @RequestMapping(value = "/MesGxCarCodePackRelSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxCarCodePackRelSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxCarCodePackRelSel";
        String transResult = "";
        String errorMsg = "";
        String meBarcodeRelTable = "c_mes_me_car_pack_rel";
        try {
            String car_code = jsonParas.getString("car_code");
            String make_order = jsonParas.getString("make_order");
            String sqlMo = "select make_order,COALESCE(attribute1,'1') attribute1 from c_mes_aps_plan_mo " +
                    "where make_order='" + make_order + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql("", sqlMo, false, request, apiRoutePath);
            int packCount = 1;
            if (itemListMo != null && itemListMo.size() > 0) {
                packCount = Integer.parseInt(itemListMo.get(0).get("attribute1").toString());
            }
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("car_code").is(car_code));
            long moFlowCount = mongoTemplate.getCollection(meBarcodeRelTable).countDocuments(queryBigData.getQueryObject());
            String bindFinish = "0";
            if (moFlowCount >= packCount) {
                bindFinish = "1";
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, bindFinish, "", 0);
        } catch (Exception ex) {
            errorMsg = "记录车辆码和PACK的关系异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //校验上一个件过站状态-南京国轩：1标识OK，2标识NG
    @RequestMapping(value = "/MesGxPreSerialStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxPreSerialStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxPreSerialStatus";
        String selectResult = "";
        String errorMsg = "";
        String mesMeStationFlow = "c_mes_me_station_flow";
        try {
            String station_code = jsonParas.getString("station_code");
            String serial_num = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "arrive_date"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(mesMeStationFlow).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).limit(1).iterator();
            String preSerialStatus = "1";
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String quality_sign = docItemBigData.getString("quality_sign");
                String leave_date = docItemBigData.getString("leave_date");
                if ((quality_sign.equals("OK") || quality_sign.equals("NG_PASS")) && leave_date != null && !leave_date.equals(""))
                    preSerialStatus = "1";
                else
                    preSerialStatus = "2";
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, preSerialStatus, "", 0);
        } catch (Exception ex) {
            errorMsg = "查询过站异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //过站重码校验-南京国轩：0标识不重码，1标识重码
    @RequestMapping(value = "/MesGxSerialStationFlowSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesGxSerialStationFlowSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/gx/MesGxSerialStationFlowSel";
        String selectResult = "";
        String errorMsg = "";
        String mesMeStationFlow = "c_mes_me_station_flow";
        try {
            String station_code = jsonParas.getString("station_code");
            String serial_num = jsonParas.getString("serial_num");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(mesMeStationFlow).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).limit(1).iterator();
            String hasStationFlowFlag = "0";
            if (iteratorBigData.hasNext()) {
                hasStationFlowFlag = "1";
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, hasStationFlowFlag, "", 0);
        } catch (Exception ex) {
            errorMsg = "查询过站异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}

