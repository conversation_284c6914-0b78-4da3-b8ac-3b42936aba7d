package com.api.eap.project.wxsn;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpStaticElements;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 收板机生产计划处理逻辑
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/wxsn/unload")
public class EapWxsnUnLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //放板机完工同步到收板机
    @RequestMapping(value = "/EapCoreUnLoadPlanLoadFinish", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanLoadFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/unload/EapCoreUnLoadPlanLoadFinish";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String station_code = jsonParas.getString("station_code");//收板机工位
            String sqlStation = "select station_id " +
                    "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            JSONArray load_panel_list = jsonParas.getJSONArray("load_panel_list");
            JSONArray load_lot_list = jsonParas.getJSONArray("load_lot_list");
            String target_group_lot_num = "";
            Integer target_plan_count = 0;
            Integer target_update_count_sum = 0;
            String group_lot_status_now = "";

            String[] group_lot_status = new String[]{"PLAN", "WORK"};
            if (load_lot_list != null && load_lot_list.size() > 0) {
                for (int i = 0; i < load_lot_list.size(); i++) {
                    JSONObject jbItem = load_lot_list.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    if (i == 0) target_group_lot_num = group_lot_num;
                    String lot_num = jbItem.getString("lot_num");
                    Integer finish_count = jbItem.getInteger("finish_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    Integer finish_ng_count = jbItem.getInteger("finish_ng_count");
                    target_plan_count += finish_ok_count;

                    //先查找数据
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        Document docItemBigData = iteratorBigData.next();
                        Integer target_update_count = 0;
                        if (docItemBigData.containsKey("target_update_count")) {
                            target_update_count = docItemBigData.getInteger("target_update_count");
                        }
                        target_update_count_sum += target_update_count;
                        finish_ok_count += target_update_count;
                        group_lot_status_now = docItemBigData.getString("group_lot_status");
                        iteratorBigData.close();
                    }

                    //更新
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    Update updateBigData = new Update();
                    updateBigData.set("target_lot_count", finish_ok_count);
                    updateBigData.set("target_update_count", finish_ok_count);
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }
            }

            Boolean isCancel = false;
            if (target_plan_count <= 0 && target_update_count_sum <= 0) isCancel = true;

            //判断放板数量是否为0
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && isCancel) {

                //判断是否写入点位CANCEL
                if (group_lot_status_now.equals("WORK")) {
                    String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                    String tagTaskCancelRequest = "";
                    if (aisMonitorModel.equals("AIS-PC")) {
                        tagTaskCancelRequest = "UnLoadAis/AisStatus/TaskCancelRequest";
                    } else if (aisMonitorModel.equals("AIS-SERVER")) {
                        tagTaskCancelRequest = "UnLoadAis_" + station_code + "/AisStatus/TaskCancelRequest";
                    } else {
                        OpStaticElements.unLoadPlanCountUpdLock.unlock();
                        errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagTaskCancelRequest, "1", true);
                    if (!errorMsg.equals("")) {
                        OpStaticElements.unLoadPlanCountUpdLock.unlock();
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }

                //再更新
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                Update updateBigData2 = new Update();
                updateBigData2.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }

            //判断是否当前任务正在进行中,若是进行中,则需要把任务数量更新到PLC
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && target_plan_count > 0) {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                String port_code = "";
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    port_code = docItemBigData.getString("port_code");
                    iteratorBigData.close();
                }
                if (port_code != null && !port_code.equals("")) {
                    String sqlPort = "select port_index " +
                            "from a_eap_fmod_station_port " +
                            "where station_id=" + station_id + " and port_code='" + port_code + "'";
                    List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPort, false, request, apiRoutePath);
                    if (lstPort != null && lstPort.size() > 0) {
                        String port_index = lstPort.get(0).get("port_index").toString();
                        String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                        String tagUpdateLotCountStatus = "";
                        String tagUpdateLotCount = "";
                        if (aisMonitorModel.equals("AIS-PC")) {
                            tagUpdateLotCountStatus = "UnLoadPlc/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "UnLoadPlc/AisReqControl" + port_index + "/UpdateLotCount";
                        } else if (aisMonitorModel.equals("AIS-SERVER")) {
                            tagUpdateLotCountStatus = "UnLoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "UnLoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCount";
                        } else {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        String tagOnlyKeyList = tagUpdateLotCount + "," + tagUpdateLotCountStatus;
                        String tagValueList = (target_plan_count + target_update_count_sum) + "&1";
                        errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
                        if (!errorMsg.equals("")) {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                    }
                }
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "放板机完工同步到收板机异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //判断是否存在收板任务
    @RequestMapping(value = "/EapCoreUnLoadPlanExistJudge", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanExistJudge(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/unload/EapCoreUnLoadPlanExistJudge";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    mapBigDataRow.put("other_attribute", docItemBigData.containsKey("other_attribute") ? docItemBigData.getString("other_attribute") : "");
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在收板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //选择端口任务为进行中
    @RequestMapping(value = "/EapCoreUnLoadPlanWorkPlanSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanWorkPlanSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/unload/EapCoreUnLoadPlanWorkPlanSelect";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String group_lot_num = "";
            long station_id_long = Long.parseLong(station_id);

            //先将当前端口正在进行中的任务结束掉
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            Update updateBigData2 = new Update();
            updateBigData2.set("group_lot_status", "CANCEL");
            mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);

            //选择PLAN任务
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                iteratorBigData.close();
            }
            if (!group_lot_num.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                updateBigData.set("port_code", port_code);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "选择端口任务为进行中异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //收板机Panel校验  ----暂时先不做一车多批的判断,这个再想想
    @RequestMapping(value = "/EapCoreUnLoadPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/unload/EapCoreUnLoadPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            Integer code_length = jsonParas.getInteger("code_length");//需要截取的条码长度
            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer fp_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                fp_count = docItemBigData.getInteger("fp_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                iteratorBigData.close();
            }
            plan_lot_count = fp_count > 0 ? fp_count : plan_lot_count;
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));

            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            if (plan_id.equals("")) {//当未找到任务时直接记录
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK";//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK";//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            if (!lot_num.equals("")) {//采用批次号判断
                                String str = panel_barcode.substring(0, code_length);
                                if (!str.equals(lot_num)) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                } else {
                                    panel_status = "OK";
                                    panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                }
                            } else if (!lot_short_num.equals("")) {//采用简码判断
                                if (!panel_barcode.contains(lot_short_num)) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if (panel_status.equals("OK")) {
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if (panelCount > 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_count >= plan_lot_count) lot_status = "FINISH";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            String UsePlcFinishCountFlag = cFuncDbSqlResolve.GetParameterValue("UsePlcFinishCountFlag");
            if(!UsePlcFinishCountFlag.equals("Y")) {
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "DEMO异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更改任务完工状态以及查询完工明细
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateFinishStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/unload/EapCoreUnLoadPlanUpdateFinishStatus";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                String material_code = docItemBigData.getString("material_code");
                String other_attribute = docItemBigData.getString("other_attribute");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("material_code", material_code);
                jbItem2.put("other_attribute", other_attribute);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //更改完工任务状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("task_error_code", task_error_code_int);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //绑定天盖或者载具到任务
    @RequestMapping(value = "/EapCoreUnLoadPlanBindPallet", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanBindPallet(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/unload/EapCoreUnLoadPlanBindPallet";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");
            String pallet_num = jsonParas.getString("pallet_num");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";

            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort == null || lstPort.size() <= 0) {
                errorMsg = "未能根据端口序号{" + port_index + "}查找到端口编号";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String port_code = lstPort.get(0).get("port_code").toString();

            //选择PLAN任务
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //更新
            if (!group_id.equals("")) {
                //计划表更新
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                Update updateBigData = new Update();
                updateBigData.set("pallet_num", pallet_num);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                //过站更新
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                updateBigData = new Update();
                updateBigData.set("pallet_num", pallet_num);
                mongoTemplate.updateMulti(queryBigData, updateBigData, meStationFlowTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "绑定天盖或者载具到任务异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更新板件Tray码
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateTray", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateTray(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/unload/EapCoreUnLoadPlanUpdateTray";
        String transResult = "";
        String errorMsg = "";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String station_flow_id = jsonParas.getString("station_flow_id");
            String tray_barcode = jsonParas.getString("tray_barcode");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            Update updateBigData = new Update();
            updateBigData.set("tray_barcode", tray_barcode);
            mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "更新板件Tray码异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //获取当前正在执行的工单号
    @RequestMapping(value = "/EapCoreUnLoadPlanPortLotSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanPortLotSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/unload/EapCoreUnLoadPlanPortLotSelect";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String group_lot_num = "";
            long ngPanelCount = 0l;
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");

            /*
            //获取当前正在执行的端口号
            String aisMonitorModel=cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String tagPlcWorkPortIndex="";
            if(aisMonitorModel.equals("AIS-PC")){
                tagPlcWorkPortIndex="UnLoadPlc/PlcStatus/PlcWorkPortIndex";
            }
            else if(aisMonitorModel.equals("AIS-SERVER")){
                tagPlcWorkPortIndex="UnLoadPlc_"+station_code+"/PlcStatus/PlcWorkPortIndex";
            }
            else{
                errorMsg="未配置收板机系统参数AIS_MONITOR_MODE";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            JSONArray jsonArray= cFuncUtilsCellScada.ReadTagByStation(station_code,tagPlcWorkPortIndex);
            String port_index="";
            if(jsonArray!=null && jsonArray.size()>0){
                JSONObject jbItem=jsonArray.getJSONObject(0);
                port_index=jbItem.getString("tag_value");
            }
            if(port_index==null || port_index.equals("")){
                errorMsg="收板机PLC掉线,未能读取到当前作业端口序号";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if(port_index.equals("0")) port_index="1";
            //1.根据工位ID与端口序号查找端口号
            String sqlPort="select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id="+station_id+" and port_index="+port_index+"";
            List<Map<String, Object>> lstPort=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlPort,false,request,apiRoutePath);
            if(lstPort==null || lstPort.size()<=0){
                errorMsg= "未能根据PLC给出端口序号{"+port_index+"}查找到端口编号";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String port_code=lstPort.get(0).get("port_code").toString();
             */


            //2.查询当前是否存在进行中的任务
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (lstPlanId.size() > 0) {
                //判断混板数量
                Integer panel_ng_code = 1;
                Query queryBigDataFlow = new Query();
                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigDataFlow.addCriteria(Criteria.where("panel_ng_code").is(panel_ng_code));
                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                ngPanelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
            }
            group_lot_num = group_lot_num + "," + ngPanelCount;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
        } catch (Exception ex) {
            errorMsg = "获取当前正在执行的工单号异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更改任务完工数量
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateFinishCount", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateFinishCount(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/wxsn/unload/EapCoreUnLoadPlanUpdateFinishCount";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String finish_count = jsonParas.getString("finish_count");
            //1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();

            Update updateBigData = new Update();
            updateBigData.set("finish_count", Integer.parseInt(finish_count));
            updateBigData.set("finish_ok_count", Integer.parseInt(finish_count));
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
