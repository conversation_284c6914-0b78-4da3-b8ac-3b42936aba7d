package com.api.dcs.core.wms;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * WMS天车主调度逻辑
 * 1.取消锁定任务以及锁定调度路线
 * 2.根据条件创建天车调度
 * 3.调度任务类型查询以及锁定天车任务
 * 4.判断任务是否执行完成
 * 5.将调度任务转移到HIS表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/wms")
public class DcsCoreCarMainController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsCarCommonFunc dcsCarCommonFunc;
    @Autowired
    private DcsCarMainManualInFunc dcsCarMainManualInFunc;//1.手动天车闯入路线
    @Autowired
    private DcsCarMainStockOutFunc dcsCarMainStockOutFunc;//2.出库上料传输路线
    @Autowired
    private DcsCarMainPwXlFunc dcsCarMainPwXlFunc;//3.抛丸上料传输路线
    @Autowired
    private DcsCarMainPwOutFunc dcsCarMainPwOutFunc;//4.库位到抛丸路线
    @Autowired
    private DcsCarMainStockInFunc dcsCarMainStockInFunc;//5.对中入库路线
    @Autowired
    private DcsCarMainPwInFunc dcsCarMainPwInFunc;//6.抛丸入库流程
    @Autowired
    private DcsCarMainStockDDFunc dcsCarMainStockDDFunc;//7.倒垛路线
    @Autowired
    private DcsCarMainBiRangFunc dcsCarMainBiRangFunc;//8.自动回归远点避让流程

    //1.取消锁定任务以及锁定调度路线
    @RequestMapping(value = "/DcsCoreWmsCarMainCancel", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarMainCancel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/wms/DcsCoreWmsCarMainCancel";
        String transResult="";
        String errorMsg="";
        String wmsCarTaskTable="b_dcs_wms_car_task";
        String userID="-1";
        try{
            //1.获取参数
            String nowDateTime=CFuncUtilsSystem.GetNowDateTime("");
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");
            //2.查询当前锁定调度任务,然后取消对应的任务
            List<String> lstTaskId=new ArrayList<>();
            String sqlLockCarTaskSel="select task_id " +
                    "from b_dcs_wms_lock_car_task " +
                    "where ware_house='"+ware_house+"' " +
                    "order by car_task_id";
            List<Map<String, Object>> itemListLockCarTask=cFuncDbSqlExecute.ExecSelectSql(userID,sqlLockCarTaskSel,
                    false,request,apiRoutePath);
            if(itemListLockCarTask!=null && itemListLockCarTask.size()>0){
                for(Map<String, Object> mapItem : itemListLockCarTask){
                    String task_id=mapItem.get("task_id").toString();
                    lstTaskId.add(task_id);
                }
            }
            if(lstTaskId!=null && lstTaskId.size()>0){
                String[] task_status_list=new String[]{"PLAN","WORK"};
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("task_id").in(lstTaskId));
                queryBigData.addCriteria(Criteria.where("task_status").in(task_status_list));
                Update updateBigData = new Update();
                updateBigData.set("task_status", "CANCEL");
                updateBigData.set("lock_flag", "N");
                updateBigData.set("tell_cancel_date", nowDateTime);
                updateBigData.set("tell_cancel_by", userID);
                mongoTemplate.updateMulti(queryBigData, updateBigData, wmsCarTaskTable);

                //修改当前调度路线任务为CANCEL状态
                String sqlLockCarTaskUpd="update b_dcs_wms_lock_car_task set " +
                        "tell_cancel_date='"+nowDateTime+"'," +
                        "tell_cancel_by='"+userID+"' " +
                        "where ware_house='"+ware_house+"'";
                cFuncDbSqlExecute.ExecUpdateSql(userID,sqlLockCarTaskUpd,false,request,apiRoutePath);
            }
            //3.取消库存明细锁定状态
            String sqlStockUpdate="update b_dcs_wms_me_stock set " +
                    "lock_flag='N' " +
                    "where lock_flag='Y' " +
                    "and stock_id in (select stock_id from b_dcs_wms_fmod_stock where ware_house='"+ware_house+"')";
            cFuncDbSqlExecute.ExecUpdateSql(userID,sqlStockUpdate,false,request,apiRoutePath);
            //4.将调度信息转移到历史
            dcsCarCommonFunc.MoveLockCarRouteToHis(userID,request,apiRoutePath,ware_house);
            //5.删除锁定调度
            dcsCarCommonFunc.DelLockCarRoute(userID,request,apiRoutePath,ware_house);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "取消锁定任务以及锁定调度路线异常:"+ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.根据条件创建天车调度(主天车)
    @Transactional
    @RequestMapping(value = "/DcsCoreWmsCarMainCreate", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarMainCreate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/wms/DcsCoreWmsCarMainCreate";
        String transResult="";
        String errorMsg="";
        String userID="-1";
        try{
            //1.获取参数
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");
            String car_code=jsonParas.getString("car_code");//天车编码
            String auto_birang_flag=jsonParas.getString("auto_birang_flag");//是否需要自动避让
            String manual_car_in_flag=jsonParas.getString("manual_car_in_flag");//手动天车是否开进来
            Integer car_location_x=jsonParas.getInteger("car_location_x");//当前天车坐标

            //2.将调度全部删除
            dcsCarCommonFunc.DelLockCarRoute(userID,request,apiRoutePath,ware_house);

            //3.按照先后顺序依次判断调度路线
            Boolean routePassFlag=false;
            String routeErrorMsg="";
            List<String> lstRouteError=new ArrayList<>();
            String okCreateRouteFlag="N";

            //3.1 手动天车闯入路线
            if(manual_car_in_flag.equals("Y") && auto_birang_flag.equals("Y")){
                Map<String, Object> mapManualInResult=dcsCarMainManualInFunc.CreateTaskRoute(
                        userID,request,apiRoutePath,ware_house,car_code,car_location_x);
                routePassFlag=Boolean.parseBoolean(mapManualInResult.get("passFlag").toString());
                routeErrorMsg=mapManualInResult.get("errorMsg").toString();
                if(!routePassFlag && !routeErrorMsg.equals("")){
                    errorMsg= routeErrorMsg;
                    transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                if(!routePassFlag) okCreateRouteFlag="Y";
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,okCreateRouteFlag,"",0);
                return transResult;
            }

            //3.2 依次对路线顺序进行判断
            String[] lstRouteOrder=new String[]{"StockOut","PwXl","PwOut","StockIn","PwIn","StockDD","BiRang"};
            for(String routeOrder : lstRouteOrder){
                Map<String, Object> mapResult=null;
                switch (routeOrder){
                    case "StockOut":
                        mapResult=dcsCarMainStockOutFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code);
                        break;
                    case "PwXl":
                        mapResult=dcsCarMainPwXlFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code);
                        break;
                    case "PwOut":
                        mapResult=dcsCarMainPwOutFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code);
                        break;
                    case "StockIn":
                        mapResult=dcsCarMainStockInFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code);
                        break;
                    case "PwIn":
                        mapResult=dcsCarMainPwInFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code);
                        break;
                    case "StockDD":
                        mapResult=dcsCarMainStockDDFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code);
                        break;
                    case "BiRang":
                        if(auto_birang_flag.equals("Y")){
                            mapResult=dcsCarMainBiRangFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code,car_location_x);
                        }
                        break;
                }
                if(mapResult!=null){
                    routePassFlag=Boolean.parseBoolean(mapResult.get("passFlag").toString());
                    routeErrorMsg=mapResult.get("errorMsg").toString();
                    if(!routePassFlag && routeErrorMsg.equals("")){
                        okCreateRouteFlag="Y";
                        transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,okCreateRouteFlag,"",0);
                        return transResult;
                    }
                    if(!routePassFlag && !routeErrorMsg.equals("")){
                        lstRouteError.add(routeErrorMsg);
                    }
                }
            }

            //4.判断调度是否存在异常,抛出第一个异常
            if(lstRouteError.size()>0){
                errorMsg=lstRouteError.get(0);
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,okCreateRouteFlag,"",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "根据条件创建天车调度发生异常:"+ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.调度任务类型查询以及锁定天车任务
    @RequestMapping(value = "/DcsCoreWmsCarMainTypeSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarMainTypeSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/wms/DcsCoreWmsCarMainTypeSel";
        String selectResult="";
        String errorMsg="";
        String wmsCarTaskTable="b_dcs_wms_car_task";
        String userID="-1";
        try{
            //1.获取参数
            String nowDateTime=CFuncUtilsSystem.GetNowDateTime("");
            userID=jsonParas.getString("userID");
            String car_code=jsonParas.getString("car_code");

            //2.查询任务
            String sqlLockCarRouteSel="select " +
                    "a.car_task_type," +
                    "COALESCE(b.task_id,'') task_id," +
                    "COALESCE(b.task_num,'') task_num," +
                    "COALESCE(b.task_type,'') task_type," +
                    "COALESCE(b.model_type,'') model_type," +
                    "COALESCE(b.from_stock_code,'') from_stock_code," +
                    "COALESCE(b.to_stock_code,'') to_stock_code " +
                    "from b_dcs_wms_lock_car_route a left join b_dcs_wms_lock_car_task b " +
                    "on a.car_task_id=b.car_task_id " +
                    "where a.car_code='"+car_code+"' " +
                    "order by a.car_route_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(userID,sqlLockCarRouteSel,
                    false,request,apiRoutePath);
            if(itemList!=null && itemList.size()>0){
                String task_id=itemList.get(0).get("task_id").toString();
                if(task_id!=null && !task_id.equals("")){
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("task_id").is(task_id));
                    Update updateBigData = new Update();
                    updateBigData.set("task_status", "WORK");
                    updateBigData.set("lock_flag", "Y");
                    updateBigData.set("tell_start_date", nowDateTime);
                    updateBigData.set("tell_start_by", userID);
                    mongoTemplate.updateFirst(queryBigData, updateBigData, wmsCarTaskTable);
                }
            }
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "调度任务类型查询异常:"+ex.getMessage();
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //4.判断任务是否执行完成
    @RequestMapping(value = "/DcsCoreWmsCarMainJudgeFinish", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarMainJudgeFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/wms/DcsCoreWmsCarMainJudgeFinish";
        String transResult="";
        String errorMsg="";
        String wmsCarTaskTable="b_dcs_wms_car_task";
        String userID="-1";
        String finishFlag="N";
        try{
            //1.获取参数
            String nowDateTime=CFuncUtilsSystem.GetNowDateTime("");
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");

            //2.查询是否有PLAN和WORK状态下的任务
            String sqlLockCarTaskCount="select count(1) from b_dcs_wms_lock_car_task " +
                    "where (execute_status='PLAN' or execute_status='WORK') " +
                    "and ware_house='"+ware_house+"'";
            String sqlLockCarRouteCount="select count(1) from b_dcs_wms_lock_car_route " +
                    "where (step_status='PLAN' or step_status='WORK') " +
                    "and ware_house='"+ware_house+"'";
            Integer lockCarTaskCount=cFuncDbSqlResolve.GetSelectCount(sqlLockCarTaskCount);
            Integer lockCarRouteCount=cFuncDbSqlResolve.GetSelectCount(sqlLockCarRouteCount);
            if(lockCarTaskCount>0 || lockCarRouteCount>0){
                transResult=CFuncUtilsLayUiResut.GetStandJson(true,null,finishFlag,"",0);
                return transResult;
            }

            //3.全部完成则将天车任务进行FINISH
            finishFlag="Y";
            String sqlLockCarTaskSel="select task_id " +
                    "from b_dcs_wms_lock_car_task " +
                    "where ware_house='"+ware_house+"'";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(userID,sqlLockCarTaskSel,
                    false,request,apiRoutePath);
            if(itemList!=null && itemList.size()>0){
                for(Map<String, Object> mapItem : itemList){
                    String task_id=mapItem.get("task_id").toString();
                    if(task_id!=null && !task_id.equals("")){
                        Query queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("task_id").is(task_id));
                        Update updateBigData = new Update();
                        updateBigData.set("task_status", "FINISH");
                        updateBigData.set("lock_flag", "N");
                        updateBigData.set("tell_start_date", nowDateTime);
                        updateBigData.set("tell_start_by", userID);
                        mongoTemplate.updateFirst(queryBigData, updateBigData, wmsCarTaskTable);
                    }
                }
            }
            transResult=CFuncUtilsLayUiResut.GetStandJson(true,null,finishFlag,"",0);
        }
        catch (Exception ex){
            errorMsg= "判断任务是否执行完成发生异常:"+ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.将调度任务转移到HIS表
    @RequestMapping(value = "/DcsCoreWmsCarMainMoveHis", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarMainMoveHis(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/wms/DcsCoreWmsCarMainMoveHis";
        String transResult = "";
        String errorMsg = "";
        String userID="-1";
        try{
            //1.获取参数
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");

            //2.将调度信息转移到历史
            dcsCarCommonFunc.MoveLockCarRouteToHis(userID,request,apiRoutePath,ware_house);

            //3.删除锁定调度
            dcsCarCommonFunc.DelLockCarRoute(userID,request,apiRoutePath,ware_house);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "将调度任务转移到HIS表发生异常:"+ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
