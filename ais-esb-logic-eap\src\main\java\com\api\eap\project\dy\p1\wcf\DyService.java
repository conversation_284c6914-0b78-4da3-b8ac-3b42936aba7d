
package com.api.eap.project.dy.p1.wcf;

import java.net.MalformedURLException;
import java.net.URL;
import javax.xml.namespace.QName;
import javax.xml.ws.Service;
import javax.xml.ws.WebEndpoint;
import javax.xml.ws.WebServiceClient;
import javax.xml.ws.WebServiceException;
import javax.xml.ws.WebServiceFeature;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.3.0-SNAPSHOT
 * Generated source version: 2.2
 * 
 */
@WebServiceClient(name = "DyService", targetNamespace = "http://tempuri.org/", wsdlLocation = "file:/C:/AIS/DyService.wsdl")
public class DyService
    extends Service
{

    private final static URL DYSERVICE_WSDL_LOCATION;
    private final static WebServiceException DYSERVICE_EXCEPTION;
    private final static QName DYSERVICE_QNAME = new QName("http://tempuri.org/", "DyService");

    static {
        URL url = null;
        WebServiceException e = null;
        try {
            url = new URL("file:/C:/AIS/DyService.wsdl");
        } catch (MalformedURLException ex) {
            e = new WebServiceException(ex);
        }
        DYSERVICE_WSDL_LOCATION = url;
        DYSERVICE_EXCEPTION = e;
    }

    public DyService() {
        super(__getWsdlLocation(), DYSERVICE_QNAME);
    }

    public DyService(WebServiceFeature... features) {
        super(__getWsdlLocation(), DYSERVICE_QNAME, features);
    }

    public DyService(URL wsdlLocation) {
        super(wsdlLocation, DYSERVICE_QNAME);
    }

    public DyService(URL wsdlLocation, WebServiceFeature... features) {
        super(wsdlLocation, DYSERVICE_QNAME, features);
    }

    public DyService(URL wsdlLocation, QName serviceName) {
        super(wsdlLocation, serviceName);
    }

    public DyService(URL wsdlLocation, QName serviceName, WebServiceFeature... features) {
        super(wsdlLocation, serviceName, features);
    }

    /**
     * 
     * @return
     *     returns DyServiceSoap
     */
    @WebEndpoint(name = "DyServiceSoap")
    public DyServiceSoap getDyServiceSoap() {
        return super.getPort(new QName("http://tempuri.org/", "DyServiceSoap"), DyServiceSoap.class);
    }

    /**
     * 
     * @param features
     *     A list of {@link WebServiceFeature} to configure on the proxy.  Supported features not in the <code>features</code> parameter will have their default values.
     * @return
     *     returns DyServiceSoap
     */
    @WebEndpoint(name = "DyServiceSoap")
    public DyServiceSoap getDyServiceSoap(WebServiceFeature... features) {
        return super.getPort(new QName("http://tempuri.org/", "DyServiceSoap"), DyServiceSoap.class, features);
    }

    private static URL __getWsdlLocation() {
        if (DYSERVICE_EXCEPTION!= null) {
            throw DYSERVICE_EXCEPTION;
        }
        return DYSERVICE_WSDL_LOCATION;
    }

}
