package com.api.pack.core.plan;

import com.api.base.Const;
import com.api.base.IMongoBasicService;
import com.api.pack.core.board.PCSBoard;
import com.api.pack.core.sort.SortCompareResult;
import com.api.pack.core.sort.SortCompareResultEvent;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;

@Service
@Transactional(rollbackFor = Exception.class)
public class PlanService extends IMongoBasicService<Plan, PlanRepository>
{
    private final MongoTemplate mongoTemplate;

    public PlanService(PlanRepository repository, MongoTemplate mongoTemplate)
    {
        super(repository);
        this.mongoTemplate = mongoTemplate;
    }

    @EventListener
    public void handleSortCompareResultEvent(SortCompareResultEvent event)
    {
        SortCompareResult compareResult = event.getSource();
        if (compareResult == null || compareResult.getPlan() == null)
        {
            return;
        }
        String id = String.valueOf(compareResult.getPlan().getOrDefault("plan_id", ""));
        if (compareResult.isOK())
        {
            int finishOkCount = (int) compareResult.getPlan().getOrDefault("finish_ok_count", 0);
            compareResult.getPlan().put("finish_ok_count", finishOkCount + 1);
            this.incByIdForKey(id, "finish_ok_count");
        }
        else if (compareResult.isNG())
        {
            int finishNgCount = (int) compareResult.getPlan().getOrDefault("finish_ng_count", 0);
            compareResult.getPlan().put("finish_ng_count", finishNgCount + 1);
            this.incByIdForKey(id, "finish_ng_count");
        }
    }

    public void resetOrderStatus()
    {
        Query query = new Query();
        query.addCriteria(Criteria.where("lot_status").is(PlanConst.STATUS_WORK));
        query.addCriteria(Criteria.where("enable_flag").is(Const.FLAG_Y));
        Update update = new Update();
        update.set("lot_status", PlanConst.STATUS_PLAN);
        this.mongoTemplate.updateMulti(query, update, Plan.class);
        // TODO 以前不规范的存储会导致下面这种写法执行后存储一条新的数据，暂时不使用
//        Plan params = new Plan();
//        params.setLotNum(lotNum);
//        params.setLotStatus(PlanConst.STATUS_WORK);
//        this.getRepository().
//                findOne(Example.of(params, ExampleMatcher.matching().withIgnoreNullValues().withIgnorePaths(Const.IGNORE_PATHS))). // 查询
//                ifPresent(plan -> {
//                    plan.setOrderStatus(PlanConst.STATUS_PLAN); // 重置状态
//                    // plan.setTaskStartTime(Const.BLANK); // 重置任务开始时间
//                    this.save(plan); // 保存
//                });
    }

    public Plan getByLotStatus(String status)
    {
        Plan params = new Plan();
        params.setLotStatus(status);
        return this.getRepository().findOne(Example.of(params, ExampleMatcher.matching().withIgnoreNullValues().withIgnorePaths(Const.IGNORE_PATHS))).orElse(null);
    }

    public org.bson.Document getCurrent()
    {
        Document connection = Plan.class.getAnnotation(Document.class);
        String connectionName = "a_pack_aps_plan";
        if (connection != null)
        {
            connectionName = connection.value();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("lot_status").is(PlanConst.STATUS_WORK));
        query.addCriteria(Criteria.where("enable_flag").is(Const.FLAG_Y));
        return this.mongoTemplate.findOne(query, org.bson.Document.class, connectionName);
    }

    public void incByIdForKey(String id, String key)
    {
        Document connection = Plan.class.getAnnotation(Document.class);
        String connectionName = "a_pack_aps_plan";
        if (connection != null)
        {
            connectionName = connection.value();
        }
        Query query = new Query();
        query.addCriteria(Criteria.where("plan_id").is(id));
        Update update = new Update();
        // 原字段值自增1
        update.inc(key, 1);
        mongoTemplate.updateFirst(query, update, connectionName);
    }
}
