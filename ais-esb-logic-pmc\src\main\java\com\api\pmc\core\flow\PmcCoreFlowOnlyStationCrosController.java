package com.api.pmc.core.flow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 仅过站FID流程
 * 1.RFID获取订单
 * 2.记录过站信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/flow")
public class PmcCoreFlowOnlyStationCrosController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;
    @Autowired
    private PmcCoreStationCrosBase pmcCoreStationCrosBase;

    //1.RFID获取订单
    @RequestMapping(value = "/PmcCoreFlowRfidOnlyStationCrosSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreFlowRfidOnlyStationCrosSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/flow/PmcCoreFlowRfidOnlyStationCrosSel";
        String selectResult="";
        String errorMsg="";
        String method="/aisEsbOra/pmc/core/interf/PmcCoreMesRfidMakeOrderSel";
        List<Map<String, Object>> itemListMo=null;
        try{
            String station_code=jsonParas.getString("station_code");//工位
            String prod_line_code=jsonParas.getString("prod_line_code");//产线
            String work_center_code=jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String serial_num=jsonParas.getString("serial_num");//工件编号或RFID(DMS+行 不满20位前面补0)
            String pallet_num=jsonParas.getString("pallet_num");//托盘号（小车编号/滑橇号）
            String interf_flag=jsonParas.getString("interf_flag");//是否调用接口获取订单
            //1、校验工位信息
            String station_des="";//工位描述
            String bg_proceduce_code="";//报工工序号
            String bg_proceduce_des="";//报工工序描述
            String line_section_code="";//产线分段编码(来自快速编码)
            String dx_flag="";//是否为定序工位
            String online_flag="";//是否上线工位
            String show_only_flag="";//是否工位独立显示(Y过站离开是否清空实时工件信息)
            String flow_flag="";//是否根据当前工位实时工件信息通过状态触发流程图
            String flow_taglist="";//触发tagOnlyKeyList(做一个json格式,对应不同的工位状态触发对应的tag点)
            String avi_station="";//AVI推算工位集
            String nj_flag="";//是否下发拧紧参数
            String up_flag="";//过站上传标识(MES,VIN打刻,油液加注,LES)
            String sqlStation="select COALESCE(a.station_des,'') station_des," +
                    "COALESCE(a.bg_proceduce_code,'') bg_proceduce_code, " +
                    "COALESCE(a.bg_proceduce_des,'') bg_proceduce_des, " +
                    "COALESCE(a.line_section_code,'') line_section_code, " +
                    "COALESCE(a.station_attr,'N') dx_flag," +
                    "COALESCE(a.online_flag,'N') online_flag, " +
                    "COALESCE(a.show_only_flag,'N') show_only_flag, " +
                    "COALESCE(a.attribute1,'N') flow_flag, " +
                    "COALESCE(a.attribute2,'') flow_taglist, " +
                    "COALESCE(a.attribute3,'') avi_station, " +
                    "COALESCE(a.attribute4,'N') nj_flag, " +
                    "COALESCE(a.attribute5,'') up_flag " +
                    "from sys_fmod_station a," +
                    "     sys_fmod_prod_line b " +
                    "where a.prod_line_id=b.prod_line_id " +
                    "and a.enable_flag='Y' " +
                    "and b.enable_flag='Y' " +
                    "and a.station_code='"+station_code+"' "+
                    "and b.prod_line_code='"+prod_line_code+"' "+
                    "LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlStation,false,null,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                errorMsg= "产线{"+prod_line_code+"},工位号{"+station_code+"},系统中不存在";
                selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            station_des=itemListStation.get(0).get("station_des").toString();
            bg_proceduce_code=itemListStation.get(0).get("bg_proceduce_code").toString();
            bg_proceduce_des=itemListStation.get(0).get("bg_proceduce_des").toString();
            line_section_code=itemListStation.get(0).get("line_section_code").toString();
            dx_flag=itemListStation.get(0).get("dx_flag").toString();
            online_flag=itemListStation.get(0).get("online_flag").toString();
            show_only_flag=itemListStation.get(0).get("show_only_flag").toString();
            flow_flag=itemListStation.get(0).get("flow_flag").toString();
            flow_taglist=itemListStation.get(0).get("flow_taglist").toString();
            avi_station=itemListStation.get(0).get("avi_station").toString();
            nj_flag=itemListStation.get(0).get("nj_flag").toString();
            up_flag=itemListStation.get(0).get("up_flag").toString();
            //2、获取订单
            String make_order="";//订单号
            String dms="";//DMS号
            String item_project="";//行项目
            String vin="";//vin号
            String small_model_type="";//型号
            String main_material_code="";//物料编号
            String material_color="";//颜色
            String material_size="";//尺寸
            String shaft_proc_num="";//拧紧程序号
            String engine_num="";//发动机
            String driver_way="";//驱动形式
            String publish_number="";//发布顺序号(上位MES)
            if(interf_flag.equals("Y")){
                //3、调用接口获取订单
                if(PmcCoreServer.EsbUrl==null ||
                        PmcCoreServer.EsbUrl.isEmpty()){
                    pmcCoreServerInit.ServerInit();
                }
                JSONObject jsonObjectMoReq = new JSONObject();
                jsonObjectMoReq.put("rfid",serial_num);//工件编号或RFID(DMS+行 不满20位前面补0)
                jsonObjectMoReq.put("workshop",work_center_code);
                jsonObjectMoReq.put("order_state","2");//订单状态(1.已生成2.已发布3.已上线4.拉入5.下线6.拉出)
                JSONObject jsonObjectMoRes= cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl+method, jsonObjectMoReq);
                Integer moCode=jsonObjectMoRes.getInteger("code");
                String msg=jsonObjectMoRes.getString("msg");
                if(moCode!=0) {
                    errorMsg= "工位号{"+station_code+"},RFID{"+serial_num+"},车间{"+work_center_code+"},状态{2},获取上位MES订单异常:"+msg;
                    selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return selectResult;
                }
                JSONArray oraArry=jsonObjectMoRes.getJSONArray("data");
                if(oraArry==null || oraArry.size()<=0) {
                    selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListMo,"","",0);
                    return selectResult;
                }
                JSONObject oraJsonObject = oraArry.getJSONObject(0);
                make_order = oraJsonObject.getString("ORDER_PROD");//生产订单
                dms = oraJsonObject.getString("DMS_NUM");//DMS号
                item_project = oraJsonObject.getString("DMS_ROW");//DMS行项目
                vin = oraJsonObject.getString("VIN");//vin号
                if(StringUtils.isEmpty(vin)){
                    vin="";
                }
                main_material_code = oraJsonObject.getString("MATERIAL_CODE");//物料编码
                if(StringUtils.isEmpty(main_material_code)){
                    main_material_code="";
                }
                small_model_type = oraJsonObject.getString("PLATFORM_MODEL");//型号
                if(StringUtils.isEmpty(small_model_type)){
                    small_model_type="";
                }
                material_color = oraJsonObject.getString("CAR_COLOUR_CODE");//颜色
                if(StringUtils.isEmpty(material_color)){
                    material_color="";
                }
                material_size = oraJsonObject.getString("WIDTH_DIMENSION");//尺寸
                if(StringUtils.isEmpty(material_size)){
                    material_size="";
                }
                engine_num = oraJsonObject.getString("ENGINE_MODEL");//发动机
                if(StringUtils.isEmpty(engine_num)){
                    engine_num="";
                }
                driver_way = oraJsonObject.getString("DRIVE_MODE");//驱动形式
                if(StringUtils.isEmpty(driver_way)){
                    driver_way="";
                }
                publish_number = oraJsonObject.getString("PUBLISH_NUMBER");//发布顺序号
            } else {
                //4、获取线首信息
                String sqlOnline="select COALESCE(make_order,'') make_order," +
                        "COALESCE(dms,'') dms," +
                        "COALESCE(item_project,'') item_project, " +
                        "COALESCE(vin,'') vin, " +
                        "COALESCE(small_model_type,'') small_model_type, " +
                        "COALESCE(main_material_code,'') main_material_code, " +
                        "COALESCE(material_color,'') material_color, " +
                        "COALESCE(material_size,'') material_size, " +
                        "COALESCE(shaft_proc_num,'') shaft_proc_num, " +
                        "COALESCE(engine_num,'') engine_num, " +
                        "COALESCE(driver_way,'') driver_way, " +
                        "COALESCE(publish_number,'') publish_number " +
                        "from d_pmc_me_flow_online " +
                        "where enable_flag='Y' " +
                        "and work_center_code='"+work_center_code+"' "+
                        "and serial_num='"+serial_num+"' "+
                        "LIMIT 1 OFFSET 0";
                List<Map<String,Object>> itemListOnline=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlOnline,false,null,apiRoutePath);
                if(itemListOnline==null || itemListOnline.size()<=0){
                    selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListOnline,"","",0);
                    return selectResult;
                }
                make_order=itemListOnline.get(0).get("make_order").toString();
                dms=itemListOnline.get(0).get("dms").toString();
                item_project=itemListOnline.get(0).get("item_project").toString();
                vin=itemListOnline.get(0).get("vin").toString();
                main_material_code=itemListOnline.get(0).get("main_material_code").toString();
                small_model_type=itemListOnline.get(0).get("small_model_type").toString();
                material_color=itemListOnline.get(0).get("material_color").toString();
                material_size=itemListOnline.get(0).get("material_size").toString();
                shaft_proc_num=itemListOnline.get(0).get("shaft_proc_num").toString();
                engine_num=itemListOnline.get(0).get("engine_num").toString();
                driver_way=itemListOnline.get(0).get("driver_way").toString();
                publish_number=itemListOnline.get(0).get("publish_number").toString();
            }
            //拼接返回值
            itemListMo=new ArrayList<>();
            Map<String, Object> map=new HashMap<>();
            map.put("make_order",make_order);
            map.put("dms",dms);
            map.put("item_project",item_project);
            map.put("serial_num",serial_num);
            map.put("pallet_num",pallet_num);
            map.put("vin",vin);
            map.put("main_material_code",main_material_code);
            map.put("small_model_type",small_model_type);
            map.put("material_color",material_color);
            map.put("material_size",material_size);
            map.put("shaft_proc_num",shaft_proc_num);
            map.put("engine_num",engine_num);
            map.put("driver_way",driver_way);
            map.put("publish_number",publish_number);
            //工位信息
            map.put("station_des",station_des);
            map.put("bg_proceduce_code",bg_proceduce_code);
            map.put("bg_proceduce_des",bg_proceduce_des);
            map.put("line_section_code",line_section_code);
            map.put("dx_flag",dx_flag);
            map.put("online_flag",online_flag);
            map.put("show_only_flag",show_only_flag);
            map.put("flow_flag",flow_flag);
            map.put("flow_taglist",flow_taglist);
            map.put("avi_station",avi_station);
            map.put("nj_flag",nj_flag);
            map.put("up_flag",up_flag);
            itemListMo.add(map);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListMo,"","",0);
        }
        catch (Exception ex){
            errorMsg= "RFID获取订单异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.记录过站信息
    @Transactional
    @RequestMapping(value = "/PmcCoreFlowOnlyStationCrosIns", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreFlowOnlyStationCrosIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/flow/PmcCoreFlowOnlyStationCrosIns";
        String selectResult="";
        String errorMsg="";
        List<Map<String, Object>> itemListMo=null;
        try{
            String station_code=jsonParas.getString("station_code");//工位
            String prod_line_code=jsonParas.getString("prod_line_code");//产线
            String work_center_code=jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            //订单信息
            String make_order=jsonParas.getString("make_order");//订单号
            String dms=jsonParas.getString("dms");//DMS号
            String item_project=jsonParas.getString("item_project");//行项目
            String serial_num=jsonParas.getString("serial_num");//工件编号或RFID(DMS+行 不满20位前面补0)
            String pallet_num=jsonParas.getString("pallet_num");//托盘号
            String vin=jsonParas.getString("vin");//vin号
            String main_material_code=jsonParas.getString("main_material_code");//物料编号
            String small_model_type=jsonParas.getString("small_model_type");//型号
            String material_color=jsonParas.getString("material_color");//颜色
            String material_size=jsonParas.getString("material_size");//尺寸
            String shaft_proc_num=jsonParas.getString("shaft_proc_num");//拧紧程序号
            String engine_num=jsonParas.getString("engine_num");//发动机
            String driver_way=jsonParas.getString("driver_way");//驱动形式
            String station_status=jsonParas.getString("station_status");//工位状态(1正常、2缓存区、3空板过点、4拉入、5拉出)
            //过站时间
            String arrive_date=jsonParas.getString("arrive_date");//到达时间
            //过站状态
            String check_status=jsonParas.getString("check_status");//过站校验状态(OK/NG)
            String check_code=jsonParas.getString("check_code");//过站校验代码
            String check_msg=jsonParas.getString("check_msg");//过站校验描述
            //工位信息
            String station_des = jsonParas.getString("station_des");//工位描述
            String bg_proceduce_code = jsonParas.getString("bg_proceduce_code");//报工工序号
            String bg_proceduce_des = jsonParas.getString("bg_proceduce_des");//报工工序描述
            String line_section_code = jsonParas.getString("line_section_code");//产线分段编码(来自快速编码)
            String up_interf_flag=jsonParas.getString("up_interf_flag");//过站上传标识(MES,VIN打刻,油液加注,LES)
            String flow_taglist=jsonParas.getString("flow_taglist");////触发tagOnlyKeyList(做一个json格式,对应不同的工位状态触发对应的tag点)
            //执行函数
            String staff_id="AIS";//操作者(默认工位号)
            String allow_way="1";//允许信息来源(1AIS流程图过站；2MES过站；3AGV过站)
            String stationFlowId=pmcCoreStationCrosBase.StationCrosIntefTask(request, apiRoutePath, staff_id, allow_way,
                                                                         station_code, prod_line_code, work_center_code,
                                                                         make_order, dms, item_project, serial_num, pallet_num,
                                                                         vin, main_material_code, small_model_type, material_color,
                                                                         material_size, shaft_proc_num, engine_num, driver_way,
                                                                         station_status, arrive_date,
                                                                         check_status, check_code, check_msg,
                                                                         station_des, bg_proceduce_code, bg_proceduce_des,
                                                                         line_section_code, up_interf_flag, flow_taglist);
            //拼接返回值
            itemListMo=new ArrayList<>();
            Map<String, Object> map=new HashMap<>();
            map.put("station_flow_id",stationFlowId);
            itemListMo.add(map);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListMo,"","",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "记录过站信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
