package com.api.dcs.project.shzy.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * 上海中冶地图库位管理公共方法
 * 1.计算占用像素单格
 * 2.锁定地图库位单格
 * 3.填充地图库位单格
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-9
 */
@Service
@Slf4j
public class DcsShzyWmsMapStockFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.计算占用像素单格(旧方法)
    public JSONObject GetMapStockCell_Old(String stock_group_code, Integer m_length,Integer m_width) throws Exception{
        String errorMsg="";
        JSONObject jbMapCell=null;
        try{
            //1.查询库区参数
            String sqlStockGroup="select " +
                    "start_position_x,end_position_x,start_position_y,end_position_y," +
                    "map_cell_length,map_cell_width,x_start_type,x_math_type," +
                    "y_start_type,y_math_type,safe_length,safe_width " +
                    "from b_dcs_wms_map_stock_group " +
                    "where stock_group_code='"+stock_group_code+"'";
            List<Map<String, Object>> itemListStockGroup=cFuncDbSqlExecute.ExecSelectSql("WMS", sqlStockGroup,
                    false,null,"");
            if(itemListStockGroup==null || itemListStockGroup.size()<=0){
                errorMsg="未查询到库区{"+stock_group_code+"}基础信息";
                throw new Exception(errorMsg);
            }
            Integer start_position_x=Integer.parseInt(itemListStockGroup.get(0).get("start_position_x").toString());
            Integer end_position_x=Integer.parseInt(itemListStockGroup.get(0).get("end_position_x").toString());
            Integer start_position_y=Integer.parseInt(itemListStockGroup.get(0).get("start_position_y").toString());
            Integer end_position_y=Integer.parseInt(itemListStockGroup.get(0).get("end_position_y").toString());
            Integer map_cell_length=Integer.parseInt(itemListStockGroup.get(0).get("map_cell_length").toString());
            Integer map_cell_width=Integer.parseInt(itemListStockGroup.get(0).get("map_cell_width").toString());
            String x_start_type=itemListStockGroup.get(0).get("x_start_type").toString();
            String x_math_type=itemListStockGroup.get(0).get("x_math_type").toString();
            String y_start_type=itemListStockGroup.get(0).get("y_start_type").toString();
            String y_math_type=itemListStockGroup.get(0).get("y_math_type").toString();
            Integer safe_length=Integer.parseInt(itemListStockGroup.get(0).get("safe_length").toString());
            Integer safe_width=Integer.parseInt(itemListStockGroup.get(0).get("safe_width").toString());
            if(m_length<=0 || m_width<=0){
                errorMsg="板长或者板宽不能<=0";
                throw new Exception(errorMsg);
            }
            //2.计算板子所占用像素
            Integer use_row_count=(m_width+safe_width)/map_cell_width;
            if((m_width+safe_width)%map_cell_width>0) use_row_count++;
            Integer use_col_count=(m_length+safe_length)/map_cell_length;
            if((m_length+safe_length)%map_cell_length>0) use_col_count++;
            if(use_row_count<=0 || use_col_count<=0){
                errorMsg="板子所占地图像素单格行|列数量不能<=0,检查基础配置信息";
                throw new Exception(errorMsg);
            }
            //2.1 先查询全部具备条件的Rows
            String rowsSel="SELECT cell_row" +
                    " FROM (" +
                    " SELECT cell_row" +
                    " FROM b_dcs_wms_map_stock_cell" +
                    " WHERE stock_group_code='"+stock_group_code+"' AND lock_flag='N' AND fill_flag='N' and enable_flag='Y'" +
                    " ORDER BY cell_row" +
                    " ) AS sub ";
            String rowsCond=" WHERE 1=1 ";
            for(int rowIndex=1;rowIndex<use_row_count;rowIndex++){
                rowsCond+=" AND EXISTS (" +
                        " SELECT 1" +
                        " FROM b_dcs_wms_map_stock_cell" +
                        " WHERE stock_group_code='"+stock_group_code+"' AND lock_flag='N' AND fill_flag='N' and enable_flag='Y'" +
                        " AND cell_row = sub.cell_row+"+rowIndex+" ) ";
            }
            rowsSel+=rowsCond;
            rowsSel="SELECT DISTINCT cell_row FROM ("+rowsSel+") as last order by cell_row ";
            if(y_start_type.equals("UP")){//从上到下:即Rows为从大到小排序
                rowsSel+="desc";
            }
            List<Map<String, Object>> itemListRows=cFuncDbSqlExecute.ExecSelectSql("WMS", rowsSel,
                    false,null,"");
            if(itemListRows==null || itemListRows.size()<=0){
                errorMsg="未查询到库区{"+stock_group_code+"}剩余行空间";
                throw new Exception(errorMsg);
            }
            //2.2 查询全部具备条件的Cols
            List<Map<String, Object>> itemListCols=null;
            for(int rowIndex=0;rowIndex<itemListRows.size()-1;rowIndex++){
                Integer cell_row=Integer.parseInt(itemListRows.get(rowIndex).get("cell_row").toString());
                String colsSel="SELECT cell_col" +
                        " FROM (" +
                        " SELECT cell_col" +
                        " FROM b_dcs_wms_map_stock_cell" +
                        " WHERE stock_group_code='"+stock_group_code+"' AND lock_flag='N' AND fill_flag='N' and enable_flag='Y'" +
                        " AND cell_row="+cell_row+"" +
                        " ORDER BY cell_col" +
                        ") AS sub ";
                String colsCond=" WHERE 1=1 ";
                for(int i_row_index=0;i_row_index<use_row_count;i_row_index++){
                    for(int colIndex=1;colIndex<use_col_count;colIndex++){
                        colsCond+=" AND EXISTS (" +
                                " SELECT 1" +
                                " FROM b_dcs_wms_map_stock_cell" +
                                " WHERE stock_group_code='"+stock_group_code+"' AND lock_flag='N' AND fill_flag='N' and enable_flag='Y'" +
                                " AND cell_row="+(cell_row+i_row_index)+"" +
                                " AND cell_col = sub.cell_col+"+colIndex+" )";
                    }
                }
                colsSel+=colsCond;
                colsSel="SELECT DISTINCT cell_col FROM ("+colsSel+") as last order by cell_col ";
                if(x_start_type.equals("RIGHT")){//从右到左:即Cols为从大到小排序
                    colsSel+="desc";
                }
                colsSel+=" LIMIT 1 OFFSET 0";
                itemListCols=cFuncDbSqlExecute.ExecSelectSql("WMS", colsSel, false,null,"");
                if(itemListCols!=null && itemListCols.size()>0){
                    break;
                }
            }
            if(itemListCols==null || itemListCols.size()<=0){
                errorMsg="未查询到库区{"+stock_group_code+"}剩余列空间";
                throw new Exception(errorMsg);
            }
            //计算出开始行,结束行,开始列,结束列
            Integer start_cell_row=Integer.parseInt(itemListRows.get(0).get("cell_row").toString());
            Integer end_cell_row=Integer.parseInt(itemListRows.get(0).get("cell_row").toString())+(use_row_count-1);
            Integer start_cell_col=Integer.parseInt(itemListCols.get(0).get("cell_col").toString());
            Integer end_cell_col=Integer.parseInt(itemListCols.get(0).get("cell_col").toString())+(use_col_count-1);
            Integer position_x=0;
            Integer position_y=0;
            if(x_math_type.equals("PLUS")){//增加方式
                Integer cell_start_x=start_position_x+(start_cell_col-1)*map_cell_length;
                position_x=cell_start_x+m_length/2;
            }
            else{
                Integer cell_start_x=start_position_x-(start_cell_col-1)*map_cell_length;
                position_x=cell_start_x-m_length/2;
            }
            if(y_math_type.equals("PLUS")){
                Integer cell_start_y=start_position_y+(start_cell_row-1)*map_cell_width;
                position_y=cell_start_y+m_width/2;
            }
            else{
                Integer cell_start_y=start_position_y-(start_cell_row-1)*map_cell_width;
                position_y=cell_start_y-m_width/2;
            }
            jbMapCell=new JSONObject();
            jbMapCell.put("start_cell_row",start_cell_row);
            jbMapCell.put("end_cell_row",end_cell_row);
            jbMapCell.put("start_cell_col",start_cell_col);
            jbMapCell.put("end_cell_col",end_cell_col);
            jbMapCell.put("position_x",position_x);
            jbMapCell.put("position_y",position_y);
        }
        catch (Exception ex){
            errorMsg="获取库位存储位置异常:"+ex;
            throw new Exception(errorMsg);
        }
        return jbMapCell;
    }

    //1.计算占用像素单格(新方法)
    public JSONObject GetMapStockCell(String stock_group_code, Integer m_length,Integer m_width,String temp_cell_flag) throws Exception{
        String errorMsg="";
        JSONObject jbMapCell=null;
        try{
            //1.查询库区参数
            String sqlStockGroup="select " +
                    "start_position_x,end_position_x,start_position_y,end_position_y," +
                    "map_cell_length,map_cell_width,x_start_type,x_math_type," +
                    "y_start_type,y_math_type,safe_length,safe_width " +
                    "from b_dcs_wms_map_stock_group " +
                    "where stock_group_code='"+stock_group_code+"'";
            List<Map<String, Object>> itemListStockGroup=cFuncDbSqlExecute.ExecSelectSql("WMS", sqlStockGroup,
                    false,null,"");
            if(itemListStockGroup==null || itemListStockGroup.size()<=0){
                errorMsg="未查询到库区{"+stock_group_code+"}基础信息";
                throw new Exception(errorMsg);
            }
            Integer start_position_x=Integer.parseInt(itemListStockGroup.get(0).get("start_position_x").toString());
            Integer end_position_x=Integer.parseInt(itemListStockGroup.get(0).get("end_position_x").toString());
            Integer start_position_y=Integer.parseInt(itemListStockGroup.get(0).get("start_position_y").toString());
            Integer end_position_y=Integer.parseInt(itemListStockGroup.get(0).get("end_position_y").toString());
            Integer map_cell_length=Integer.parseInt(itemListStockGroup.get(0).get("map_cell_length").toString());
            Integer map_cell_width=Integer.parseInt(itemListStockGroup.get(0).get("map_cell_width").toString());
            String x_start_type=itemListStockGroup.get(0).get("x_start_type").toString();
            String x_math_type=itemListStockGroup.get(0).get("x_math_type").toString();
            String y_start_type=itemListStockGroup.get(0).get("y_start_type").toString();
            String y_math_type=itemListStockGroup.get(0).get("y_math_type").toString();
            Integer safe_length=Integer.parseInt(itemListStockGroup.get(0).get("safe_length").toString());
            Integer safe_width=Integer.parseInt(itemListStockGroup.get(0).get("safe_width").toString());
            if(m_length<=0 || m_width<=0){
                errorMsg="板长或者板宽不能<=0";
                throw new Exception(errorMsg);
            }
            //2.计算板子所占用像素
            Integer use_row_count=(m_width+safe_width)/map_cell_width;
            if((m_width+safe_width)%map_cell_width>0) use_row_count++;
            Integer use_col_count=(m_length+safe_length)/map_cell_length;
            if((m_length+safe_length)%map_cell_length>0) use_col_count++;
            if(use_row_count<=0 || use_col_count<=0){
                errorMsg="板子所占地图像素单格行|列数量不能<=0,检查基础配置信息";
                throw new Exception(errorMsg);
            }
            //3 查询全部的地图像素信息
            String sqlMapCell="select cell_row,cell_col " +
                    "from b_dcs_wms_map_stock_cell " +
                    "where stock_group_code='"+stock_group_code+"' and lock_flag='N' and fill_flag='N' and enable_flag='Y' " +
                    "and temp_cell_flag='"+temp_cell_flag+"' " +
                    "order by cell_row,cell_col";
            List<Map<String, Object>> itemListMapCell=cFuncDbSqlExecute.ExecSelectSql("WMS", sqlMapCell,
                    false,null,"");
            if(itemListMapCell==null || itemListMapCell.size()<(use_row_count*use_col_count)){
                errorMsg="未查询到库区{"+stock_group_code+"}剩余单格空间";
                throw new Exception(errorMsg);
            }
            //3.1 遍历得到行与列哈希表
            Integer old_cell_row=0;
            List<Integer> lstRow=new ArrayList<>();
            Map<Integer, List<Integer>> mapHash=new HashMap<>();
            List<Integer> lstCol=new ArrayList<>();
            for(int i=0;i<itemListMapCell.size();i++){
                Map<String, Object> mapItem=itemListMapCell.get(i);
                Integer cell_row=Integer.parseInt(mapItem.get("cell_row").toString());
                Integer cell_col=Integer.parseInt(mapItem.get("cell_col").toString());
                if(!cell_row.equals(old_cell_row)){
                    old_cell_row=cell_row;
                    lstRow.add(cell_row);
                    lstCol=new ArrayList<>();
                }
                lstCol.add(cell_col);
                mapHash.put(cell_row,lstCol);
            }
            itemListMapCell=null;//释放
            //3.2 判断行那些是满足条件的,将不满足条件的剔除掉
            List<Integer> lstOkRow=new ArrayList<>();
            for(int i=0;i<lstRow.size();i++){
                Integer cell_row=lstRow.get(i);
                Boolean isOk=true;
                for(int rowIndex=1;rowIndex<use_row_count;rowIndex++){
                    Integer judge_cell_row=cell_row+rowIndex;
                    if(!lstRow.contains(judge_cell_row)){
                        isOk=false;
                        break;
                    }
                }
                if(isOk) lstOkRow.add(cell_row);
            }
            lstRow=null;//释放
            if(lstOkRow.size()<=0){
                errorMsg="未查询到库区{"+stock_group_code+"}剩余单格行空间";
                throw new Exception(errorMsg);
            }
            //3.3 查找合适的行和列
            Integer start_cell_row=0;
            Integer start_cell_col=0;
            if(y_start_type.equals("UP")){//从上到下
                for(int i=lstOkRow.size()-1;i>=0;i--){
                    Integer cell_row=lstOkRow.get(i);
                    Boolean isOk=true;
                    List<Integer> compareLstCol=new ArrayList<>();
                    for(int rowIndex=0;rowIndex<use_row_count;rowIndex++){
                        Integer cell_row_index=cell_row+rowIndex;
                        if(!mapHash.containsKey(cell_row_index)){
                            isOk=false;
                            break;
                        }
                        List<Integer> lstColItem=mapHash.get(cell_row_index);
                        if(lstColItem.size()<use_col_count){
                            isOk=false;
                            break;
                        }
                        if(compareLstCol.size()<=0) compareLstCol=lstColItem;
                        else{
                            if(compareLstCol.size()>lstColItem.size()) compareLstCol=lstColItem;
                        }
                    }
                    if(!isOk) continue;
                    isOk=false;
                    if(x_start_type.equals("RIGHT")){
                        for(int j=compareLstCol.size()-1;j>=0;j--){
                            Integer cell_col=compareLstCol.get(j);
                            cell_col=cell_col-use_col_count+1;
                            if(cell_col<=0) break;
                            Boolean isOk2=true;
                            for(int rowIndex=0;rowIndex<use_row_count;rowIndex++){
                                Integer cell_row_index=cell_row+rowIndex;
                                List<Integer> lstColItem=mapHash.get(cell_row_index);
                                for(int colIndex=0;colIndex<use_col_count;colIndex++){
                                    Integer judge_cell_col=cell_col+colIndex;
                                    if(!lstColItem.contains(judge_cell_col)){
                                        isOk2=false;
                                        break;
                                    }
                                }
                                if(!isOk2) break;
                            }
                            if(isOk2){
                                isOk=true;
                                start_cell_row=cell_row;
                                start_cell_col=cell_col;
                                break;
                            }
                        }
                    }
                    else{//从左到右
                        for(int j=0;j<compareLstCol.size();j++){
                            Integer cell_col=compareLstCol.get(j);
                            Boolean isOk2=true;
                            for(int rowIndex=0;rowIndex<use_row_count;rowIndex++){
                                Integer cell_row_index=cell_row+rowIndex;
                                List<Integer> lstColItem=mapHash.get(cell_row_index);
                                for(int colIndex=0;colIndex<use_col_count;colIndex++){
                                    Integer judge_cell_col=cell_col+colIndex;
                                    if(!lstColItem.contains(judge_cell_col)){
                                        isOk2=false;
                                        break;
                                    }
                                }
                                if(!isOk2) break;
                            }
                            if(isOk2){
                                isOk=true;
                                start_cell_row=cell_row;
                                start_cell_col=cell_col;
                                break;
                            }
                        }
                    }
                    if(isOk) break;
                }
            }
            else{//从下到上
                for(int i=0;i<lstOkRow.size();i++){
                    Integer cell_row=lstOkRow.get(i);
                    Boolean isOk=true;
                    List<Integer> compareLstCol=new ArrayList<>();
                    for(int rowIndex=0;rowIndex<use_row_count;rowIndex++){
                        Integer cell_row_index=cell_row+rowIndex;
                        if(!mapHash.containsKey(cell_row_index)){
                            isOk=false;
                            break;
                        }
                        List<Integer> lstColItem=mapHash.get(cell_row_index);
                        if(lstColItem.size()<use_col_count){
                            isOk=false;
                            break;
                        }
                        if(compareLstCol.size()<=0) compareLstCol=lstColItem;
                        else{
                            if(compareLstCol.size()>lstColItem.size()) compareLstCol=lstColItem;
                        }
                    }
                    if(!isOk) continue;
                    isOk=false;
                    if(x_start_type.equals("RIGHT")){
                        for(int j=compareLstCol.size()-1;j>=0;j--){
                            Integer cell_col=compareLstCol.get(j);
                            cell_col=cell_col-use_col_count+1;
                            if(cell_col<=0) break;
                            Boolean isOk2=true;
                            for(int rowIndex=0;rowIndex<use_row_count;rowIndex++){
                                Integer cell_row_index=cell_row+rowIndex;
                                List<Integer> lstColItem=mapHash.get(cell_row_index);
                                for(int colIndex=0;colIndex<use_col_count;colIndex++){
                                    Integer judge_cell_col=cell_col+colIndex;
                                    if(!lstColItem.contains(judge_cell_col)){
                                        isOk2=false;
                                        break;
                                    }
                                }
                                if(!isOk2) break;
                            }
                            if(isOk2){
                                isOk=true;
                                start_cell_row=cell_row;
                                start_cell_col=cell_col;
                                break;
                            }
                        }
                    }
                    else{//从左到右
                        for(int j=0;j<compareLstCol.size();j++){
                            Integer cell_col=compareLstCol.get(j);
                            Boolean isOk2=true;
                            for(int rowIndex=0;rowIndex<use_row_count;rowIndex++){
                                Integer cell_row_index=cell_row+rowIndex;
                                List<Integer> lstColItem=mapHash.get(cell_row_index);
                                for(int colIndex=0;colIndex<use_col_count;colIndex++){
                                    Integer judge_cell_col=cell_col+colIndex;
                                    if(!lstColItem.contains(judge_cell_col)){
                                        isOk2=false;
                                        break;
                                    }
                                }
                                if(!isOk2) break;
                            }
                            if(isOk2){
                                isOk=true;
                                start_cell_row=cell_row;
                                start_cell_col=cell_col;
                                break;
                            }
                        }
                    }
                    if(isOk) break;
                }
            }
            //3.4 判断是否得到单格
            if(start_cell_row<=0 || start_cell_col<=0){
                errorMsg="未查询到库区{"+stock_group_code+"}剩余单格空间";
                throw new Exception(errorMsg);
            }
            //计算出开始行,结束行,开始列,结束列
            Integer end_cell_row=start_cell_row+(use_row_count-1);
            Integer end_cell_col=start_cell_col+(use_col_count-1);
            Integer position_x=0;
            Integer position_y=0;
            if(x_math_type.equals("PLUS")){//增加方式
                Integer cell_start_x=start_position_x+(start_cell_col-1)*map_cell_length;
                position_x=cell_start_x+m_length/2;
            }
            else{
                Integer cell_start_x=start_position_x-(start_cell_col-1)*map_cell_length;
                position_x=cell_start_x-m_length/2;
            }
            if(y_math_type.equals("PLUS")){
                Integer cell_start_y=start_position_y+(start_cell_row-1)*map_cell_width;
                position_y=cell_start_y+m_width/2;
            }
            else{
                Integer cell_start_y=start_position_y-(start_cell_row-1)*map_cell_width;
                position_y=cell_start_y-m_width/2;
            }
            jbMapCell=new JSONObject();
            jbMapCell.put("start_cell_row",start_cell_row);
            jbMapCell.put("end_cell_row",end_cell_row);
            jbMapCell.put("start_cell_col",start_cell_col);
            jbMapCell.put("end_cell_col",end_cell_col);
            jbMapCell.put("position_x",position_x);
            jbMapCell.put("position_y",position_y);
        }
        catch (Exception ex){
            errorMsg="获取库位存储位置异常:"+ex;
            throw new Exception(errorMsg);
        }
        return jbMapCell;
    }

    //2.锁定地图库位单格
    public void LockMapStockCell(String user_name,String task_num,String stock_group_code,
                                 Integer start_cell_row,Integer end_cell_row,
                                 Integer start_cell_col,Integer end_cell_col,
                                 String lock_flag) throws Exception{
        String lock_task_num=task_num;
        if(!lock_flag.equals("Y")) lock_task_num="";
        String sqlUpdate="update b_dcs_wms_map_stock_cell set " +
                "last_updated_by='"+user_name+"'," +
                "last_update_date='"+ CFuncUtilsSystem.GetNowDateTime("") +"'," +
                "lock_flag='"+lock_flag+"'," +
                "lock_task_num='"+lock_task_num+"' " +
                "where stock_group_code='"+stock_group_code+"' " +
                "and cell_row>="+start_cell_row+" and cell_row<="+end_cell_row+" " +
                "and cell_col>="+start_cell_col+" and cell_col<="+end_cell_col+"";
        cFuncDbSqlExecute.ExecUpdateSql(user_name,sqlUpdate,false,null,"");
    }

    //3.填充地图库位单格
    public void FillMapStockCell(String user_name,String task_num,String stock_group_code,
                                 Integer start_cell_row,Integer end_cell_row,
                                 Integer start_cell_col,Integer end_cell_col,
                                 String fill_flag) throws Exception{
        String fill_task_num=task_num;
        if(!fill_flag.equals("Y")) fill_task_num="";
        String sqlUpdate="update b_dcs_wms_map_stock_cell set " +
                "last_updated_by='"+user_name+"'," +
                "last_update_date='"+ CFuncUtilsSystem.GetNowDateTime("") +"'," +
                "fill_flag='"+fill_flag+"'," +
                "fill_task_num='"+fill_task_num+"' " +
                "where stock_group_code='"+stock_group_code+"' " +
                "and cell_row>="+start_cell_row+" and cell_row<="+end_cell_row+" " +
                "and cell_col>="+start_cell_col+" and cell_col<="+end_cell_col+"";
        if(!fill_flag.equals("Y")){
            sqlUpdate="update b_dcs_wms_map_stock_cell set " +
                    "last_updated_by='"+user_name+"'," +
                    "last_update_date='"+ CFuncUtilsSystem.GetNowDateTime("") +"'," +
                    "lock_flag='N'," +
                    "lock_task_num=''," +
                    "fill_flag='"+fill_flag+"'," +
                    "fill_task_num='"+fill_task_num+"' " +
                    "where stock_group_code='"+stock_group_code+"' " +
                    "and cell_row>="+start_cell_row+" and cell_row<="+end_cell_row+" " +
                    "and cell_col>="+start_cell_col+" and cell_col<="+end_cell_col+"";
        }
        cFuncDbSqlExecute.ExecUpdateSql(user_name,sqlUpdate,false,null,"");
    }

    //4.入库存储
    @Transactional
    public void MapStockIn(JSONObject jbRow) throws Exception{
        String errorMsg="";
        try{
            Long stock_id=jbRow.getLong("stock_id");
            String stock_group_code=jbRow.getString("stock_group_code");
            String stock_code=jbRow.getString("stock_code");
            Integer m_length=jbRow.getInteger("m_length");
            Integer m_width=jbRow.getInteger("m_width");
            Integer m_thickness=jbRow.getInteger("m_thickness");
            Integer start_cell_row=jbRow.getInteger("start_cell_row");
            Integer end_cell_row=jbRow.getInteger("end_cell_row");
            Integer start_cell_col=jbRow.getInteger("start_cell_col");
            Integer end_cell_col=jbRow.getInteger("end_cell_col");
            Integer position_x=jbRow.getInteger("position_x");
            Integer position_y=jbRow.getInteger("position_y");
            Integer position_z=jbRow.getInteger("position_z");
            String task_from=jbRow.getString("task_from");
            String task_num=jbRow.getString("task_num");
            String material_code=jbRow.getString("material_code");
            String serial_num=jbRow.getString("serial_num");
            String lot_num=jbRow.getString("lot_num");
            String task_way=jbRow.getString("task_way");
            String task_type=jbRow.getString("task_type");
            String car_code=jbRow.getString("car_code");
            String kit_task_num=jbRow.getString("kit_task_num");
            String kit_structure_no=jbRow.getString("kit_structure_no");
            String kit_material_type=jbRow.getString("kit_material_type");
            String kit_flag=jbRow.getString("kit_flag");
            String temp_flag=jbRow.getString("temp_flag");
            if(stock_id==null) stock_id=0L;
            if(stock_group_code==null) stock_group_code="";
            if(stock_code==null) stock_code="";
            if(m_length==null) m_length=0;
            if(m_width==null) m_width=0;
            if(m_thickness==null) m_thickness=0;
            if(start_cell_row==null) start_cell_row=0;
            if(end_cell_row==null) end_cell_row=0;
            if(start_cell_col==null) start_cell_col=0;
            if(end_cell_col==null) end_cell_col=0;
            if(position_x==null) position_x=0;
            if(position_y==null) position_y=0;
            if(position_z==null) position_z=0;
            if(task_from==null) task_from="";
            if(task_num==null) task_num="";
            if(material_code==null) material_code="";
            if(serial_num==null) serial_num="";
            if(lot_num==null) lot_num="";
            if(task_way==null) task_way="";
            if(task_type==null) task_type="";
            if(car_code==null) car_code="";
            if(kit_task_num==null) kit_task_num="";
            if(kit_structure_no==null) kit_structure_no="";
            if(kit_material_type==null) kit_material_type="";
            if(kit_flag==null) kit_flag="";
            if(temp_flag==null || temp_flag.equals("")) temp_flag="N";
            Long stock_id2=stock_id;
            Integer stock_count=1;
            String full_flag="N";
            if(stock_id<=0){
                stock_id2=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_map_me_stock_seq",true);
            }
            else{
                //查询库存信息
                String sqlStockCount="select count(1) " +
                        "from b_dcs_wms_map_me_stock_d " +
                        "where stock_id="+stock_id;
                stock_count=cFuncDbSqlResolve.GetSelectCount(sqlStockCount)+1;
            }
            String sqlStockGroup="select " +
                    "ware_house,stock_max_height " +
                    "from b_dcs_wms_map_stock_group " +
                    "where stock_group_code='"+stock_group_code+"'";
            List<Map<String, Object>> itemListStockGroup=cFuncDbSqlExecute.ExecSelectSql("WMS", sqlStockGroup,
                    false,null,"");
            if(itemListStockGroup==null || itemListStockGroup.size()<=0){
                errorMsg="未查询到库区{"+stock_group_code+"}基础信息";
                throw new Exception(errorMsg);
            }
            String now=CFuncUtilsSystem.GetNowDateTime("");
            String ware_house=itemListStockGroup.get(0).get("ware_house").toString();
            Integer stock_max_height=Integer.parseInt(itemListStockGroup.get(0).get("stock_max_height").toString());
            if(stock_max_height<=position_z) full_flag="Y";
            if(stock_id<=0){
                String sqlInsert="insert into b_dcs_wms_map_me_stock " +
                        "(created_by,creation_date,stock_id,ware_house,stock_group_code," +
                        "stock_code,stock_count,m_length,m_width," +
                        "start_cell_row,end_cell_row,start_cell_col,end_cell_col," +
                        "position_x,position_y,stock_status,full_flag,lock_flag,temp_flag) values " +
                        "('WMS','"+now+"',"+stock_id2+",'"+ware_house+"','"+stock_group_code+"'," +
                        "'"+stock_code+"',"+stock_count+","+m_length+","+m_width+"," +
                        ""+start_cell_row+","+end_cell_row+","+start_cell_col+","+end_cell_col+"," +
                        ""+position_x+","+position_y+",'NORMAL','"+full_flag+"','N','"+temp_flag+"')";
                cFuncDbSqlExecute.ExecUpdateSql("WMS",sqlInsert,false,null,"");
                //更新MapCell标识
                String sqlUpdate="update b_dcs_wms_map_stock_cell set " +
                        "last_updated_by='WMS'," +
                        "last_update_date='"+ now +"'," +
                        "lock_flag='N'," +
                        "lock_task_num=''," +
                        "fill_flag='Y'," +
                        "fill_task_num='"+task_num+"' " +
                        "where stock_group_code='"+stock_group_code+"' " +
                        "and cell_row>="+start_cell_row+" and cell_row<="+end_cell_row+" " +
                        "and cell_col>="+start_cell_col+" and cell_col<="+end_cell_col+"";
                cFuncDbSqlExecute.ExecUpdateSql("WMS",sqlUpdate,false,null,"");
            }
            else{
                String sqlUpdate="update b_dcs_wms_map_me_stock set " +
                        "last_updated_by='WMS'," +
                        "last_update_date='"+now+"'," +
                        "stock_count="+stock_count+"," +
                        "full_flag='"+full_flag+"'," +
                        "lock_flag='N' " +
                        "where stock_id="+stock_id;
                cFuncDbSqlExecute.ExecUpdateSql("WMS",sqlUpdate,false,null,"");
            }
            //插入明细
            Long stock_d_id=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_map_me_stock_d_seq",true);
            String sqlInsertD="insert into b_dcs_wms_map_me_stock_d " +
                    "(created_by,creation_date,stock_d_id,stock_id," +
                    "task_from,task_num,material_code,serial_num,lot_num," +
                    "stock_index,location_z,lock_flag,task_way," +
                    "task_type,stock_d_time,car_code,m_thickness," +
                    "kit_task_num,kit_structure_no,kit_material_type,kit_flag) values " +
                    "('WMS','"+now+"',"+stock_d_id+","+stock_id2+"," +
                    "'"+task_from+"','"+task_num+"','"+material_code+"','"+serial_num+"','"+lot_num+"'," +
                    ""+stock_count+","+position_z+",'N','"+task_way+"'," +
                    "'"+task_type+"','"+now+"','"+car_code+"',"+m_thickness+"," +
                    "'"+kit_task_num+"','"+kit_structure_no+"','"+kit_material_type+"','"+kit_flag+"')";
            cFuncDbSqlExecute.ExecUpdateSql("WMS",sqlInsertD,false,null,"");
            //记录事件
            JSONObject mapParas=jbRow;
            mapParas.put("stock_id",stock_id2);
            mapParas.put("stock_code",stock_code);
            mapParas.put("stock_way",task_way);
            mapParas.put("from_stock_code","FJ");
            mapParas.put("to_stock_code",stock_code);
            mapParas.put("to_location_x",position_x);
            mapParas.put("to_location_y",position_y);
            mapParas.put("to_location_z",position_z);
            mapParas.put("event_status","KW_EVENT");
            mapParas.put("alarm_flag","N");
            StockEventIns(mapParas);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg="存储库存信息异常:"+ex;
            throw new Exception(errorMsg);
        }
    }

    //5.更新库存
    public void UpdateKwStockCount(String userID,Boolean isAdd,String car_code,Map<String, Object> mapParas) throws Exception{
        //1.获取参数
        String stock_code=mapParas.get("stock_code").toString();

        //2.根据库位查询
        String sqlStockSel="select " +
                "stock_id,stock_count," +
                "start_cell_row,end_cell_row,start_cell_col,end_cell_col,stock_group_code " +
                "from b_dcs_wms_map_me_stock " +
                "where stock_code='"+stock_code+"' " +
                "order by stock_id LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql(userID,sqlStockSel,
                false,null,"");
        if(itemListStock==null || itemListStock.size()<=0){
            throw new Exception("未能根据库位{"+stock_code+"}查找到库位基础信息");
        }
        String stock_id=itemListStock.get(0).get("stock_id").toString();
        Integer stock_count=Integer.parseInt(itemListStock.get(0).get("stock_count").toString());
        Integer start_cell_row=Integer.parseInt(itemListStock.get(0).get("start_cell_row").toString());
        Integer end_cell_row=Integer.parseInt(itemListStock.get(0).get("end_cell_row").toString());
        Integer start_cell_col=Integer.parseInt(itemListStock.get(0).get("start_cell_col").toString());
        Integer end_cell_col=Integer.parseInt(itemListStock.get(0).get("end_cell_col").toString());
        String stock_group_code=itemListStock.get(0).get("stock_group_code").toString();
        String sqlStockGroup="select " +
                "stock_max_height " +
                "from b_dcs_wms_map_stock_group " +
                "where stock_group_code='"+stock_group_code+"'";
        List<Map<String, Object>> itemListStockGroup=cFuncDbSqlExecute.ExecSelectSql("WMS", sqlStockGroup,
                false,null,"");
        if(itemListStockGroup==null || itemListStockGroup.size()<=0){
            throw new Exception("未查询到库区{"+stock_group_code+"}基础信息");
        }
        Integer stock_max_height=Integer.parseInt(itemListStockGroup.get(0).get("stock_max_height").toString());
        if(stock_count<0) stock_count=0;
        if(isAdd) stock_count++;
        else stock_count--;
        if(stock_count<0) stock_count=0;

        //3.库存增加或者减少
        String sqlStockUpd="";
        if(isAdd){
            Integer location_z=Integer.parseInt(mapParas.get("location_z").toString());
            if(location_z>=stock_max_height){
                sqlStockUpd="update b_dcs_wms_map_me_stock set " +
                        "last_updated_by='WMS'," +
                        "last_update_date='"+CFuncUtilsSystem.GetNowDateTime("")+"'," +
                        "lock_flag='N'," +
                        "stock_count="+stock_count+"," +
                        "full_flag='Y' " +
                        "where stock_id="+stock_id;
            }
            else{
                sqlStockUpd="update b_dcs_wms_map_me_stock set " +
                        "last_updated_by='WMS'," +
                        "last_update_date='"+CFuncUtilsSystem.GetNowDateTime("")+"'," +
                        "lock_flag='N'," +
                        "stock_count="+stock_count+" " +
                        "where stock_id="+stock_id;
            }
        }
        else{
            if(stock_count>0){
                sqlStockUpd="update b_dcs_wms_map_me_stock set " +
                        "last_updated_by='WMS'," +
                        "last_update_date='"+CFuncUtilsSystem.GetNowDateTime("")+"'," +
                        "lock_flag='N'," +
                        "stock_count="+stock_count+" " +
                        "where stock_id="+stock_id;
            }
            else{
                sqlStockUpd="delete from b_dcs_wms_map_me_stock " +
                        "where stock_id="+stock_id;
            }
        }
        cFuncDbSqlExecute.ExecUpdateSql(userID,sqlStockUpd,false,null,"");
        //4.修改明细
        String sqlMeStockUpd="";
        if(isAdd){
            String nowDateTime= CFuncUtilsSystem.GetNowDateTime("");
            Long stock_d_id=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_map_me_stock_d_seq",true);
            String task_num=mapParas.get("task_num").toString();
            String task_from=mapParas.get("task_from").toString();
            String serial_num=mapParas.get("serial_num").toString();
            String lot_num=mapParas.get("lot_num").toString();
            String material_code=mapParas.get("model_type").toString();
            String location_z=mapParas.get("location_z").toString();
            String task_way=mapParas.get("task_way").toString();
            String task_type=mapParas.get("task_type").toString();
            String m_thickness_str=mapParas.get("m_height").toString();
            BigDecimal bigDecimal = new BigDecimal(m_thickness_str);
            Integer m_thickness=bigDecimal.intValue();
            String kit_task_num=mapParas.containsKey("kit_task_num") ? mapParas.get("kit_task_num").toString() : "";
            String kit_structure_no=mapParas.containsKey("kit_structure_no") ? mapParas.get("kit_structure_no").toString() : "";
            String kit_material_type=mapParas.containsKey("kit_material_type") ? mapParas.get("kit_material_type").toString() : "";
            String kit_flag=mapParas.containsKey("kit_flag") ? mapParas.get("kit_flag").toString() : "N";
            if(kit_task_num==null) kit_task_num="";
            if(kit_structure_no==null) kit_structure_no="";
            if(kit_material_type==null) kit_material_type="";
            if(kit_flag==null) kit_flag="N";
            Integer stock_index=stock_count;
            sqlMeStockUpd="insert into b_dcs_wms_map_me_stock_d " +
                    "(created_by,creation_date,stock_d_id,stock_id," +
                    "task_from,task_num,material_code,serial_num,lot_num," +
                    "stock_index,location_z,lock_flag,task_way," +
                    "task_type,stock_d_time,car_code,m_thickness," +
                    "kit_task_num,kit_structure_no,kit_material_type,kit_flag) values " +
                    "('WMS','"+nowDateTime+"',"+stock_d_id+","+stock_id+"," +
                    "'"+task_from+"','"+task_num+"','"+material_code+"','"+serial_num+"','"+lot_num+"'," +
                    ""+stock_index+","+location_z+",'N','"+task_way+"'," +
                    "'"+task_type+"','"+nowDateTime+"','"+car_code+"',"+m_thickness+"," +
                    "'"+kit_task_num+"','"+kit_structure_no+"','"+kit_material_type+"','"+kit_flag+"')";
        }
        else{
            sqlMeStockUpd="delete from b_dcs_wms_map_me_stock_d " +
                    "where stock_d_id in " +
                    "(select stock_d_id from b_dcs_wms_map_me_stock_d " +
                    "where stock_id="+stock_id+" " +
                    "order by stock_d_id desc LIMIT 1 OFFSET 0)";
        }
        cFuncDbSqlExecute.ExecUpdateSql(userID,sqlMeStockUpd,false,null,"");
        //5.若库存为0,则需要解锁单格
        if(!isAdd && stock_count<=0){
            FillMapStockCell("WMS","",stock_group_code,
                    start_cell_row,end_cell_row,start_cell_col,end_cell_col,"N");
        }
        //6.若是新增库位且数量=1则需要填充单格子
        if(isAdd && stock_count==1){
            String task_num=mapParas.get("task_num").toString();
            String sqlUpdate="update b_dcs_wms_map_stock_cell set " +
                    "last_updated_by='WMS'," +
                    "last_update_date='"+ CFuncUtilsSystem.GetNowDateTime("") +"'," +
                    "lock_flag='N'," +
                    "lock_task_num=''," +
                    "fill_flag='Y'," +
                    "fill_task_num='"+task_num+"' " +
                    "where stock_group_code='"+stock_group_code+"' " +
                    "and cell_row>="+start_cell_row+" and cell_row<="+end_cell_row+" " +
                    "and cell_col>="+start_cell_col+" and cell_col<="+end_cell_col+"";
            cFuncDbSqlExecute.ExecUpdateSql("WMS",sqlUpdate,false,null,"");
        }
    }

    //6.增加库位事件
    public void StockEventIns(JSONObject mapParas) throws Exception{
        String meStockEventTable="b_dcs_wms_me_stock_event";
        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
        String stock_event_id=CFuncUtilsSystem.CreateUUID(true);
        Map<String, Object> mapBigDataRow=new HashMap<>();
        mapBigDataRow.put("item_date",item_date);
        mapBigDataRow.put("item_date_val",item_date_val);
        mapBigDataRow.put("stock_event_id",stock_event_id);
        mapBigDataRow.put("stock_id",Long.parseLong(mapParas.getString("stock_id")));
        mapBigDataRow.put("car_code",mapParas.getString("car_code")==null ? "":mapParas.getString("car_code"));
        mapBigDataRow.put("stock_code",mapParas.getString("stock_code")==null ? "":mapParas.getString("stock_code"));
        mapBigDataRow.put("stock_way",mapParas.getString("stock_way")==null ? "":mapParas.getString("stock_way"));
        mapBigDataRow.put("task_way",mapParas.getString("task_way")==null ? "":mapParas.getString("task_way"));
        mapBigDataRow.put("task_type",mapParas.getString("task_type")==null ? "":mapParas.getString("task_type"));
        mapBigDataRow.put("task_from",mapParas.getString("task_from")==null ? "":mapParas.getString("task_from"));
        mapBigDataRow.put("task_num",mapParas.getString("task_num")==null ? "":mapParas.getString("task_num"));
        mapBigDataRow.put("serial_num",mapParas.getString("serial_num")==null ? "":mapParas.getString("serial_num"));
        mapBigDataRow.put("lot_num",mapParas.getString("lot_num")==null ? "":mapParas.getString("lot_num"));
        mapBigDataRow.put("model_type",mapParas.getString("model_type")==null ? "":mapParas.getString("model_type"));
        mapBigDataRow.put("material_code",mapParas.getString("material_code")==null ? "":mapParas.getString("material_code"));
        mapBigDataRow.put("material_des",mapParas.getString("material_des")==null ? "":mapParas.getString("material_des"));
        mapBigDataRow.put("material_draw",mapParas.getString("material_draw")==null ? "":mapParas.getString("material_draw"));
        mapBigDataRow.put("m_length",Double.parseDouble(mapParas.getString("m_length")==null ? "0":mapParas.getString("m_length")));
        mapBigDataRow.put("m_width",Double.parseDouble(mapParas.getString("m_width")==null ? "0":mapParas.getString("m_width")));
        mapBigDataRow.put("m_height",Double.parseDouble(mapParas.getString("m_height")==null ? "0":mapParas.getString("m_height")));
        mapBigDataRow.put("m_weight",Double.parseDouble(mapParas.getString("m_weight")==null ? "0":mapParas.getString("m_weight")));
        mapBigDataRow.put("m_texture",mapParas.getString("m_texture")==null ? "0":mapParas.getString("m_texture"));
        mapBigDataRow.put("from_stock_code",mapParas.getString("from_stock_code")==null ? "":mapParas.getString("from_stock_code"));
        mapBigDataRow.put("from_location_x",mapParas.getString("from_location_x")==null ? "0":mapParas.getString("from_location_x"));
        mapBigDataRow.put("from_location_y",mapParas.getString("from_location_y")==null ? "0":mapParas.getString("from_location_y"));
        mapBigDataRow.put("from_location_z",mapParas.getString("from_location_z")==null ? "0":mapParas.getString("from_location_z"));
        mapBigDataRow.put("to_stock_code",mapParas.getString("to_stock_code")==null ? "":mapParas.getString("to_stock_code"));
        mapBigDataRow.put("to_location_x",mapParas.getString("to_location_x")==null ? "0":mapParas.getString("to_location_x"));
        mapBigDataRow.put("to_location_y",mapParas.getString("to_location_y")==null ? "0":mapParas.getString("to_location_y"));
        mapBigDataRow.put("to_location_z",mapParas.getString("to_location_z")==null ? "0":mapParas.getString("to_location_z"));
        mapBigDataRow.put("event_status",mapParas.getString("event_status")==null ? "":mapParas.getString("event_status"));
        mapBigDataRow.put("alarm_flag",mapParas.getString("alarm_flag")==null ? "":mapParas.getString("alarm_flag"));
        mapBigDataRow.put("up_flag","N");
        mapBigDataRow.put("up_code","");
        mapBigDataRow.put("up_msg","");
        mongoTemplate.insert(mapBigDataRow,meStockEventTable);
    }

    //7.查找出库库位以及相关信息
    public Map<String, Object> GetKwStockOut(String ware_house,String project_code,String material_code) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlStockSel="select " +
                "a.stock_id,a.stock_code,a.position_x,a.position_y,b.location_z," +
                "a.start_cell_row,a.end_cell_row,a.start_cell_col,a.end_cell_col," +
                "a.stock_count,a.m_length,a.m_width,b.m_thickness,b.stock_d_id " +
                "from b_dcs_wms_map_me_stock a inner join b_dcs_wms_map_me_stock_d b " +
                "on a.stock_id=b.stock_id " +
                "where a.stock_status='NORMAL' and a.lock_flag='N' " +
                "and a.ware_house='"+ware_house+"' " +
                "and a.temp_flag='N' " +
                "and b.material_code='"+material_code+"' " +
                "and b.lot_num='"+project_code+"' " +
                "and a.stock_count>0 " +
                "order by a.stock_group_code,a.start_cell_row,a.start_cell_col,b.stock_index desc " +
                "LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                false,null,"");
        if(itemListStock!=null && itemListStock.size()>0){
            mapItem=itemListStock.get(0);
        }
        return mapItem;
    }

    //8.查找临时库位
    public Map<String, Object> GetTempKwStock(String ware_house,String stock_group_code) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlStockSel="select " +
                "a.stock_id,a.stock_code,a.position_x,a.position_y,b.location_z," +
                "a.start_cell_row,a.end_cell_row,a.start_cell_col,a.end_cell_col," +
                "a.stock_count,a.m_length,a.m_width,b.m_thickness,b.stock_d_id," +
                "b.task_num,b.material_code," +
                "COALESCE(b.serial_num,'') serial_num," +
                "COALESCE(b.lot_num,'') lot_num," +
                "COALESCE(b.kit_task_num,'') kit_task_num," +
                "COALESCE(b.kit_structure_no,'') kit_structure_no," +
                "COALESCE(b.kit_material_type,'') kit_material_type," +
                "COALESCE(b.kit_flag,'N') kit_flag " +
                "from b_dcs_wms_map_me_stock a inner join b_dcs_wms_map_me_stock_d b " +
                "on a.stock_id=b.stock_id " +
                "where a.stock_status='NORMAL' and a.lock_flag='N' " +
                "and a.ware_house='"+ware_house+"' " +
                "and a.stock_group_code='"+stock_group_code+"' " +
                "and a.temp_flag='Y' " +
                "and a.stock_count>0 " +
                "order by a.stock_group_code,a.start_cell_row,a.start_cell_col,b.stock_index desc " +
                "LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStockSel,
                false,null,"");
        if(itemListStock!=null && itemListStock.size()>0){
            mapItem=itemListStock.get(0);
        }
        return mapItem;
    }

    //9.查找入库库位
    public Map<String, Object> GetKwStockIn(String ware_house,String stock_group_code,
                                            String project_code,String material_code,String temp_cell_flag,
                                            Integer material_length,Integer material_width,Integer material_thickness,
                                            HttpServletRequest request,String apiRoutePath) throws Exception{
        Map<String, Object> mapItem=null;
        String errorMsg="";
        //1.查询是否有现成库存进行存储
        String sqlStockGroup="select " +
                "start_position_z,stock_max_height,dd_way,x_math_type,y_math_type " +
                "from b_dcs_wms_map_stock_group " +
                "where stock_group_code='"+stock_group_code+"'";
        List<Map<String, Object>> itemListStockGroup=cFuncDbSqlExecute.ExecSelectSql("WMS", sqlStockGroup,
                false,request,apiRoutePath);
        if(itemListStockGroup==null || itemListStockGroup.size()<=0){
            errorMsg="未能根据库区{"+stock_group_code+"}查询基础设置信息";
            throw new Exception(errorMsg);
        }
        Integer start_position_z=Integer.parseInt(itemListStockGroup.get(0).get("start_position_z").toString());
        Integer stock_max_height=Integer.parseInt(itemListStockGroup.get(0).get("stock_max_height").toString());
        String dd_way=itemListStockGroup.get(0).get("dd_way").toString();
        String sqlStock="";
        if(dd_way.equals("MATERIAL")){//按照物料类型
            sqlStock="select " +
                    "a.stock_id,a.stock_code,a.position_x,a.position_y,b.location_z," +
                    "a.start_cell_row,a.end_cell_row,a.start_cell_col,a.end_cell_col," +
                    "a.stock_count,b.stock_d_id " +
                    "from b_dcs_wms_map_me_stock a inner join b_dcs_wms_map_me_stock_d b " +
                    "on a.stock_id=b.stock_id " +
                    "where a.stock_status='NORMAL' and a.full_flag='N' and a.lock_flag='N' " +
                    "and a.stock_group_code='"+stock_group_code+"' " +
                    "and a.temp_flag='"+temp_cell_flag+"' " +
                    "and b.material_code='"+material_code+"' " +
                    "and b.lot_num='"+project_code+"' " +
                    "order by a.start_cell_row,a.start_cell_col,b.stock_index desc " +
                    "LIMIT 1 OFFSET 0";
        }
        else{
            sqlStock="select " +
                    "a.stock_id,a.stock_code,a.position_x,a.position_y,b.location_z," +
                    "a.start_cell_row,a.end_cell_row,a.start_cell_col,a.end_cell_col," +
                    "a.stock_count,b.stock_d_id " +
                    "from b_dcs_wms_map_me_stock a inner join b_dcs_wms_map_me_stock_d b " +
                    "on a.stock_id=b.stock_id " +
                    "where a.stock_status='NORMAL' and a.full_flag='N' and a.lock_flag='N' " +
                    "and a.stock_group_code='"+stock_group_code+"' " +
                    "and a.temp_flag='"+temp_cell_flag+"' " +
                    "and a.m_length="+material_length+" and a.m_width="+material_width+" " +
                    "order by a.start_cell_row,a.start_cell_col,b.stock_index desc " +
                    "LIMIT 1 OFFSET 0";
        }
        Long stock_id=0L;
        String stock_code="";
        Integer position_x=0;
        Integer position_y=0;
        Integer location_z=0;
        Integer start_cell_row=0;
        Integer end_cell_row=0;
        Integer start_cell_col=0;
        Integer end_cell_col=0;
        Long stock_d_id=0L;
        Integer stock_count=0;
        List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("WMS", sqlStock,
                false,request,apiRoutePath);
        if(itemListStock!=null && itemListStock.size()>0){
            stock_id=Long.parseLong(itemListStock.get(0).get("stock_id").toString());
            stock_code=itemListStock.get(0).get("stock_code").toString();
            position_x=Integer.parseInt(itemListStock.get(0).get("position_x").toString());
            position_y=Integer.parseInt(itemListStock.get(0).get("position_y").toString());
            location_z=Integer.parseInt(itemListStock.get(0).get("location_z").toString());
            start_cell_row=Integer.parseInt(itemListStock.get(0).get("start_cell_row").toString());
            end_cell_row=Integer.parseInt(itemListStock.get(0).get("end_cell_row").toString());
            start_cell_col=Integer.parseInt(itemListStock.get(0).get("start_cell_col").toString());
            end_cell_col=Integer.parseInt(itemListStock.get(0).get("end_cell_col").toString());
            stock_d_id=Long.parseLong(itemListStock.get(0).get("stock_d_id").toString());
            stock_count=Integer.parseInt(itemListStock.get(0).get("stock_count").toString());
            location_z=location_z+material_thickness;
        }
        else{
            JSONObject jbMapCell=GetMapStockCell(stock_group_code,material_length,material_width,temp_cell_flag);
            if(jbMapCell==null){
                return mapItem;
            }
            start_cell_row=jbMapCell.getInteger("start_cell_row");
            end_cell_row=jbMapCell.getInteger("end_cell_row");
            start_cell_col=jbMapCell.getInteger("start_cell_col");
            end_cell_col=jbMapCell.getInteger("end_cell_col");
            stock_code=stock_group_code+"-"+
                    "R"+start_cell_row+"C"+start_cell_col+"-"+
                    "R"+end_cell_row+"C"+end_cell_col;
            position_x=jbMapCell.getInteger("position_x");
            position_y=jbMapCell.getInteger("position_y");
            location_z=start_position_z+material_thickness;
        }
        mapItem=new HashMap<>();
        mapItem.put("stock_id",stock_id);
        mapItem.put("stock_code",stock_code);
        mapItem.put("position_x",position_x);
        mapItem.put("position_y",position_y);
        mapItem.put("location_z",location_z);
        mapItem.put("start_cell_row",start_cell_row);
        mapItem.put("end_cell_row",end_cell_row);
        mapItem.put("start_cell_col",start_cell_col);
        mapItem.put("end_cell_col",end_cell_col);
        mapItem.put("stock_count",stock_count);
        mapItem.put("stock_d_id",stock_d_id);
        return mapItem;
    }
}
