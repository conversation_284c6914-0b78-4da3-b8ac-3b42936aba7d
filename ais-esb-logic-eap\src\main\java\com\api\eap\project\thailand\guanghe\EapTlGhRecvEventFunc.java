package com.api.eap.project.thailand.guanghe;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * (泰国广合)EAP接受事件功能函数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class EapTlGhRecvEventFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private EapTlGhInterfCommon eapTlGhInterfCommon;

    //1.HMI报警信息下发
    public JSONObject CIMMessageCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String esbInterfCode="CIMMessageCommand";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        Integer code=200;
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            responseParas= eapTlGhInterfCommon.CreateResponseHead01(code,"成功");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=500;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas= eapTlGhInterfCommon.CreateResponseHead01(code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //2.EAP验证配方参数结果命令
    public JSONObject RecipeValidationResultCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String esbInterfCode="RecipeValidationResultCommand";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        Integer code=200;
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            responseParas= eapTlGhInterfCommon.CreateResponseHead01(code,"成功");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=500;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas= eapTlGhInterfCommon.CreateResponseHead01(code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
