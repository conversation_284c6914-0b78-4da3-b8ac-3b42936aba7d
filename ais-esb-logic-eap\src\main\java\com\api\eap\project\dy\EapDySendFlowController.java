package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 定颖EAP发送流程数据定义接口
 * 1.PortStatusChangeReport:[接口]端口状态发生变化推送到EAP
 * 2.CarrierIDReport:[接口]载具扫描上报验证
 * 3.CarrierStatusReport:[接口]开始/结束/取消/终止投板（收板）时上报
 * 4.CCDDataReport:[接口]CCD读码上报
 * 5.FetchOutReport:[接口]每放一片板需要上报（针对放板机）
 * 6.StoreInReport:[接口]每收一片板需要上报（针对收板机）
 * 7.JobCountReport:[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
 * 8.PanelDataUploadReport:[接口]每片Panel收放台車事件上報
 * 9.WIPTrackingReport:[接口]每一lot完板上报
 * 10.CarrierDataUploadReport:[接口]最后全部任务完板后上报
 * 11.JobDataCreateModifyReport:[接口]人工操作界面拿走一片板子(手工录入)
 * 12.JobRemoveRecoveryReport:[接口]人工操作界面放回板子(手工录入)
 * 13.EachPositionReport:[接口]暂存位置信息动态上报
 * 14.EachPanelDataDownLoad:[上下游接口]当片信息(上游传递给下游)
 * 15.BcOffLineLotDownLoad:[上下游接口]BC离线工单信息下发
 * 16.BcOffLineLoginInDownLoad:[上下游接口]BC离线同步登入
 * 17.LotFinishDownLoad:[上下游接口]传递到下游设备结批信息
 * 18.ProductionModeDownLoad:[上下游接口]首件状态下发
 * 19.HeartBeatCheckDownLoad:[上下游接口]检测下游心跳状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/dy/interf/send")
public class EapDySendFlowController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDySendFlowFunc eapDySendFlowFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;

    //1.[接口]端口状态发生变化推送到EAP
    @RequestMapping(value = "/PortStatusChangeReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyPortStatusChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/PortStatusChangeReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String port_status = jsonParas.getString("port_status");//UDCM、LDRQ、WAIT、PROC、ABOT、CANE、PREN、UDRQ、
            String pallet_num = jsonParas.getString("pallet_num");
            Integer left_count = jsonParas.getInteger("left_count");
            String eqp_job_type = jsonParas.getString("eqp_job_type");//FCL、ECL、ECR、FCU、FCR、ECRFCL、FCUECL
            if (eqp_job_type == null) eqp_job_type = "";

            if (pallet_num == null) pallet_num = "";
            if (left_count == null) left_count = 0;

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String allow_sb_emptypallet_flag = "N";
            String user_name = "";
            String port_ng_flag = "N";
            String pallet_reload_flag = "N";
            if (port_status.equals("LDRQ")) pallet_reload_flag = "Y";
            if (port_status.equals("UDCM") || port_status.equals("LDRQ")) {
                pallet_num = "";
                left_count = 0;
            }
            if(port_status.equals("LDCM")){
                if(station_attr.equals("Load")) eqp_job_type="FCL";
                else eqp_job_type="ECL";
            }

            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //3.判断是否为WAIT状态,若为WAIT状态,判断是否参数要求控制有无PANEL
            if (port_status.equals("WAIT") || port_status.equals("WaitProc")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                Integer panel_model = -1;
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    panel_model = docItemBigData.getInteger("panel_model");
                    iteratorBigData.close();
                }
                if (panel_model >= 0) {
                    //判断是否为P1,若是P1控制有无Panel模式
                    Boolean isP1Factory=eapDyInterfCommon.CheckDyFactory("P1");
                    if(isP1Factory){
                        errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr,
                                "Plc", "PlcConfig", "PanelModel", "EAP",
                                String.valueOf(panel_model), true);
                    }
                    else{
                        if(eapDyInterfCommon.CheckDyVersion(3)){
                            errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr,
                                    "Ais", "AisConfig", "NgPanelPassFlag", "EAP",
                                    String.valueOf(panel_model), true);
                        }
                        else{
                            String panel_model_new="1";
                            if(panel_model>0) panel_model_new="0";
                            errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr,
                                    "Plc", "PlcConfig", "PanelModel", "EAP",
                                    panel_model_new, true);
                        }
                    }
                    if (!errorMsg.equals("")) {
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
            }

            //端口状态上报
            eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, port_status, allow_sb_emptypallet_flag, pallet_num, String.valueOf(left_count), user_name, port_ng_flag, pallet_reload_flag,eqp_job_type,station_attr);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "端口状态上报EAP异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]载具扫描上报验证
    @RequestMapping(value = "/CarrierIDReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCarrierIDReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/CarrierIDReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String mePalletQueueTable = "a_eap_me_pallet_queue";
        try {
            String result = "OK";
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String production_mode = jsonParas.getString("production_mode");//制程分类
            String ccdNo = jsonParas.getString("ccd_no");
            if (production_mode == null || production_mode.equals("")) production_mode = "Default";
            String pre_pallet_scan = jsonParas.getString("pre_pallet_scan");//是否提前扫描载具
            if (pre_pallet_scan == null || pre_pallet_scan.equals("")) pre_pallet_scan = "N";
            String read_type = jsonParas.getString("read_type");
            String eqp_job_type = jsonParas.getString("eqp_job_type");//FCL、ECL、ECR、FCU、FCR、ECRFCL、FCUECL
            if (eqp_job_type == null) eqp_job_type = "";
            Boolean isNewVersion=eapDyInterfCommon.CheckDyVersion(3);

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String user_name = "";

            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //查询是否为提前扫描
            String ZanCunCcdFlag = "0";
            if (station_attr.equals("Load")) {
                ZanCunCcdFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr, "Ais", "AisConfig", "ZanCunCcdFlag");
                if (ZanCunCcdFlag == null || ZanCunCcdFlag.equals("")) {
                    errorMsg = "读取点位{ZanCunCcdFlag}失败";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }

            //3.若是来自暂存,但是非扫描工位
            if (ZanCunCcdFlag.equals("1") && !pre_pallet_scan.equals("Y")) {
                //1.查找载具是否最后一批状态
                Integer rtn_fuder = -1;
                String lot_num = "";
                String i_id = "";
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("pallet_num").is(pallet_num));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(mePalletQueueTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    i_id = docItemBigData.getObjectId("_id").toString();
                    lot_num = docItemBigData.getString("lot_num");
                    rtn_fuder = docItemBigData.getInteger("rtn_fuder");//1最后一车标识,0不是最后一车标识
                    iteratorBigData.close();
                }
                if (rtn_fuder >= 0 && !lot_num.equals("")) {
                    queryBigData = new Query();
                    String[] group_lot_status_list2 = new String[]{"WAIT", "PLAN", "WORK"};
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list2));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    Update updateBigData2 = new Update();
                    updateBigData2.set("pdb_rule", rtn_fuder);
                    mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
                }
                //成功后发送LDCM
                String eqp_job_type2="FCL";
                if(station_attr.equals("UnLoad")) eqp_job_type2="ECL";
                if(!isNewVersion){
                    eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "LDCM", "N", pallet_num, "0", user_name, "N", "N",eqp_job_type2,station_attr);
                }
                //删除数据
                if (!i_id.equals("")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("_id").lt(new ObjectId(i_id)));
                    mongoTemplate.remove(queryBigData, mePalletQueueTable);
                }

                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //4.提前扫描，需要判断是否最后一车
            if (ZanCunCcdFlag.equals("1") && pre_pallet_scan.equals("Y")) {
                String old_lot_num = "";
                String new_lot_num = "";
                String first_scan_flag = "N";
                Integer old_rtn_fuder = 0;
                String read_type2 = "B";
                //4.1 读取当前批次号
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(mePalletQueueTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    old_lot_num = docItemBigData.getString("lot_num");
                    old_rtn_fuder = docItemBigData.getInteger("rtn_fuder");//1最后一车标识,0不是最后一车标识
                    iteratorBigData.close();
                }
                if (old_rtn_fuder == 1) {
                    first_scan_flag = "Y";
                    read_type2 = "S";
                } else {
                    if (old_lot_num.equals("")) {
                        first_scan_flag = "Y";
                        read_type2 = "S";
                    }
                }

                //
                String lot_infos_strs="";
                JSONObject response_body = eapDySendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, station_attr, 0, production_mode, "N", null, first_scan_flag, read_type2);
                if (response_body == null) {
                    result = "NG";
                } else {
                    Integer interf_rtn_fuder = 0;
                    //先获取参数中的rtn_fuder值
                    if (response_body.containsKey("rtn_fuder")) {
                        try {
                            interf_rtn_fuder = response_body.getInteger("rtn_fuder");
                            if (interf_rtn_fuder == null) interf_rtn_fuder = 0;
                        } catch (Exception ex) {
                        }
                    }

                    //获取到lot_id信息
                    if (response_body.containsKey("lot_infos")) {
                        JSONObject lot_infos = response_body.getJSONObject("lot_infos");
                        if (lot_infos != null) {
                            if (lot_infos.containsKey("lot")) {
                                JSONArray lot_list = lot_infos.getJSONArray("lot");
                                if (lot_list != null && lot_list.size() > 0) {
                                    JSONObject first_lot_obj = lot_list.getJSONObject(0);
                                    if (first_lot_obj != null && first_lot_obj.containsKey("lot_id")) {
                                        new_lot_num = first_lot_obj.getString("lot_id");
                                    }
                                    if(isNewVersion){
                                        JSONArray new_lot_list=new JSONArray();
                                        for(int i=0;i<lot_list.size();i++){
                                            JSONObject jbItemAttr=lot_list.getJSONObject(i);
                                            String lot_id=jbItemAttr.getString("lot_id");
                                            String lot_qty=jbItemAttr.getString("lot_qty");
                                            JSONObject jbItemAttrNew=new JSONObject();
                                            jbItemAttrNew.put("lot_id",lot_id);
                                            jbItemAttrNew.put("lot_count",lot_qty);
                                            new_lot_list.add(jbItemAttrNew);
                                        }
                                        lot_infos_strs=new_lot_list.toString();
                                    }
                                }
                            }
                        }
                    }

                    if (!first_scan_flag.equals("Y")) {
                        if (!old_lot_num.equals(new_lot_num)) {
                            result = "NG";
                            //记录到CIM消息
                            String cimMessage = "当前载具{" + pallet_num + "}对应工单{" + new_lot_num + "}与当前作业工单{" + old_lot_num + "}不一致,不允许上机";
                            opCommonFunc.SaveCimMessage(station_id_long, "1", "-1", "EAP", cimMessage,5);
                        }
                    }

                    if (result.equals("OK")) {
                        //新版本需要上报LDCM
                        if(isNewVersion){
                            String eqp_job_type2="FCL";
                            if(station_attr.equals("UnLoad")) eqp_job_type2="ECL";
                            eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "LDCM", "N", pallet_num, "0", user_name, "N", "N",eqp_job_type2,station_attr);
                        }
                        //增加载具队列数据
                        Map<String, Object> mapPalletQueueRow = new HashMap<>();
                        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                        mapPalletQueueRow.put("item_date", item_date);
                        mapPalletQueueRow.put("item_date_val", item_date_val);
                        mapPalletQueueRow.put("pallet_queue_id", CFuncUtilsSystem.CreateUUID(true));
                        mapPalletQueueRow.put("station_id", station_id_long);
                        mapPalletQueueRow.put("lot_num", new_lot_num);
                        mapPalletQueueRow.put("pallet_num", pallet_num);
                        mapPalletQueueRow.put("rtn_fuder", interf_rtn_fuder);
                        mongoTemplate.insert(mapPalletQueueRow, mePalletQueueTable);
                        //返回result
                        if(isNewVersion){
                            if(first_scan_flag.equals("Y")){
                                result="OK|"+lot_infos_strs;
                            }
                            else{
                                result="PASS|"+lot_infos_strs;
                            }
                        }
                    }
                }
                //CCDDataReport:[接口]CCD读码上报
                eapDySendFlowFunc.CCDDataReport(station_code, pallet_num, ccdNo);

                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //其他
            String lot_infos_strs="";
            JSONObject response_body2 = eapDySendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, station_attr, 0, production_mode, "N", null, "Y", read_type);
            if (response_body2 == null) result = "NG";
            else{
                if(isNewVersion){
                    if (response_body2.containsKey("lot_infos")) {
                        JSONObject lot_infos = response_body2.getJSONObject("lot_infos");
                        if (lot_infos != null) {
                            if (lot_infos.containsKey("lot")) {
                                JSONArray lot_list = lot_infos.getJSONArray("lot");
                                if (lot_list != null && lot_list.size() > 0) {
                                    JSONArray new_lot_list=new JSONArray();
                                    for(int i=0;i<lot_list.size();i++){
                                        JSONObject jbItemAttr=lot_list.getJSONObject(i);
                                        String lot_id=jbItemAttr.getString("lot_id");
                                        String lot_qty=jbItemAttr.getString("lot_qty");
                                        JSONObject jbItemAttrNew=new JSONObject();
                                        jbItemAttrNew.put("lot_id",lot_id);
                                        jbItemAttrNew.put("lot_count",lot_qty);
                                        new_lot_list.add(jbItemAttrNew);
                                    }
                                    lot_infos_strs=new_lot_list.toString();
                                    result=result+"|"+lot_infos_strs;
                                }
                            }
                        }
                    }
                }
            }

            //成功后发送LDCM
            String eqp_job_type3="FCL";
            if(station_attr.equals("UnLoad")) eqp_job_type3="ECL";
            eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "LDCM", "N", pallet_num, "0", user_name, "N", "N",eqp_job_type3,station_attr);

            //1.CCDDataReport:[接口]CCD读码上报
            eapDySendFlowFunc.CCDDataReport(station_code, pallet_num, ccdNo);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "载具扫描上报验证异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]载具扫描上报验证(归批机)
    @RequestMapping(value = "/CarrierIDReport2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCarrierIDReport2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/CarrierIDReport2";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String result = "OK";
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String production_mode = jsonParas.getString("production_mode");//制程分类
            String ccdNo = jsonParas.getString("ccd_no");
            if (production_mode == null || production_mode.equals("")) production_mode = "Default";
            String pre_pallet_scan = jsonParas.getString("pre_pallet_scan");//是否提前扫描载具
            if (pre_pallet_scan == null || pre_pallet_scan.equals("")) pre_pallet_scan = "N";
            String read_type = jsonParas.getString("read_type");
            String eqp_job_type = jsonParas.getString("eqp_job_type");//FCL、ECL、ECR、FCU、FCR、ECRFCL、FCUECL
            if (eqp_job_type == null) eqp_job_type = "";

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();

            JSONObject response_body2 = eapDySendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, "Load", 0, production_mode, "N", null, "Y", read_type);
            if (response_body2 == null) result = "NG";

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "载具扫描上报验证异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.[接口]开始/结束/取消/终止上报
    @RequestMapping(value = "/CarrierStatusReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCarrierStatusReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/CarrierStatusReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String task_status = jsonParas.getString("task_status");
            String pallet_num = jsonParas.getString("pallet_num");
            String lot_list_str = jsonParas.getString("lot_list");
            String group_lot_num=jsonParas.getString("group_lot_num");
            String manual_wip_flag=jsonParas.getString("manual_wip_flag");
            if(group_lot_num==null) group_lot_num="";
            if(manual_wip_flag==null) manual_wip_flag="";
            String sqlStation = "select station_id," + "COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, request, apiRoutePath);
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            Long station_id_long=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            JSONArray lot_list = JSONArray.parseArray(lot_list_str);
            //若是WaitProc则需要做映射
            Integer plan_lot_count=0;
            Integer finish_count=0;
            Integer finish_ok_count=0;
            if(task_status.equals("WaitProc")){
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                Integer panel_model = -1;
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    panel_model = docItemBigData.getInteger("panel_model");
                    plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                    finish_count=docItemBigData.getInteger("finish_count");
                    finish_ok_count=docItemBigData.getInteger("finish_ok_count");
                    iteratorBigData.close();
                }
                if (panel_model >= 0) {
                    //判断是否为P1,若是P1控制有无Panel模式
                    Boolean isP1Factory=eapDyInterfCommon.CheckDyFactory("P1");
                    if(isP1Factory){
                        errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr,
                                "Plc", "PlcConfig", "PanelModel", "EAP",
                                String.valueOf(panel_model), true);
                    }
                    else{
                        errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr,
                                "Ais", "AisConfig", "NgPanelPassFlag", "EAP",
                                String.valueOf(panel_model), true);
                    }
                    if (!errorMsg.equals("")) {
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
            }
            //针对WaitStart处理,需要查询数量
            if(task_status.equals("WaitStart")){
                if (lot_list != null && lot_list.size() > 0){
                    JSONArray lot_list_new = new JSONArray();
                    for (int i = 0; i < lot_list.size(); i++) {
                        JSONObject jbNew = lot_list.getJSONObject(i);
                        String lot_id = jbNew.getString("lot_id");
                        String lot_count = jbNew.getString("lot_count");
                        if(!lot_id.equals("") && (lot_count==null || lot_count.equals("") || lot_count.equals("0"))){
                            Query queryBigData = new Query();
                            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_id));
                            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(
                                    queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                                    noCursorTimeout(true).batchSize(1).iterator();
                            if(iteratorBigData.hasNext()){
                                Document docItemBigData = iteratorBigData.next();
                                lot_count=String.valueOf(docItemBigData.getInteger("plan_lot_count"));
                                iteratorBigData.close();
                            }
                        }
                        JSONObject jbNew2 = new JSONObject();
                        jbNew2.put("lot_id", lot_id);
                        jbNew2.put("lot_count", lot_count);
                        lot_list_new.add(jbNew2);
                    }
                    lot_list=lot_list_new;
                }
            }
            //针对新版本进行修正完成数量
            if(eapDyInterfCommon.CheckDyVersion(3)){
                if(station_attr.equals("Load")){
                    String OneCarMultyLotFlag= opCommonFunc.ReadCellOneRedisValue(station_code,station_attr,
                            "Ais","AisConfig","OneCarMultyLotFlag");
                    if("2".equals(OneCarMultyLotFlag)){
                        if(task_status.equals("WaitProc")){
                            Integer left_count=plan_lot_count-finish_ok_count;
                            if(left_count<0) left_count=0;
                            if(lot_list!=null && lot_list.size()>0){
                                JSONArray lot_list_new=new JSONArray();
                                for(int i=0;i<lot_list.size();i++){
                                    JSONObject jb=lot_list.getJSONObject(i);
                                    jb.put("lot_count",left_count);
                                    lot_list_new.add(jb);
                                }
                                lot_list=lot_list_new;
                            }
                        }
                    }
                }
            }
            eapDySendFlowFunc.CarrierStatusReport(station_code, pallet_num, task_status, lot_list, "0",
                    station_attr, port_code,manual_wip_flag);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "开始/结束/取消/终止上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.[接口]开始上报(归批机)
    @RequestMapping(value = "/CarrierStatusReport2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCarrierStatusReport2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/CarrierStatusReport2";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String task_status = jsonParas.getString("task_status");
            String pallet_num = jsonParas.getString("pallet_num");
            String sqlStation = "select station_id," + "COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, request, apiRoutePath);
            Long station_id_long=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            //第一片上报Process
            JSONArray lot_list = new JSONArray();
            String group_id="";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if(!group_id.equals("")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                    String lot_num2 = docItemBigData.getString("lot_num");
                    Integer left_count = 0;
                    left_count = plan_lot_count - 1;
                    if (left_count < 0) left_count = 0;
                    JSONObject jbItem = new JSONObject();
                    jbItem.put("lot_id", lot_num2);
                    jbItem.put("lot_count", left_count);
                    lot_list.add(jbItem);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            eapDySendFlowFunc.CarrierStatusReport(station_code, pallet_num, task_status, lot_list, "0", "Load", port_code,"N");
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "开始/结束/取消/终止上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.综合下游下发任务以及其他任务相关信息
    @RequestMapping(value = "/DownDeviceTaskInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyDownDeviceTaskInfo(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/DownDeviceTaskInfo";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        MongoCursor<Document> iteratorBigData = null;
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String sys_model = jsonParas.getString("sys_model");
            String group_lot_num = jsonParas.getString("group_lot_num");
            //远程模式不需要下发到下游
            if (sys_model.equals("1")) {
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String lot_num = docItemBigData.getString("lot_num");
                String lot_short_num = docItemBigData.getString("lot_short_num");
                String material_code = docItemBigData.getString("material_code");
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                String item_info = docItemBigData.getString("item_info");
                JSONArray ja = new JSONArray();
                if (item_info != null && !item_info.equals("")) {
                    ja = JSONArray.parseArray(item_info);
                }
                eapDySendFlowFunc.BcOffLineLotDownLoad(station_code, lot_num, material_code, plan_lot_count, lot_short_num, ja);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            try {
                if (iteratorBigData != null) {
                    if (iteratorBigData.hasNext()) iteratorBigData.close();
                }
            } catch (Exception exx) {
            }
            errorMsg = "综合下游下发任务信息异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.EAP综合读码上报
    @RequestMapping(value = "/PanelResultReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyPanelResultReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/PanelResultReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        MongoCursor<Document> iteratorBigData = null;
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String panel_result = jsonParas.getString("panel_result");
            String ccdNo = jsonParas.getString("ccdNo");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String onff_line = jsonParas.getString("onff_line");//在线离线
            String panel_model = jsonParas.getString("panel_model");//有无Panel模式
            String inspect_flag = jsonParas.getString("inspect_flag");//是否首件
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String[] panelListParas = panel_result.split(",", -1);
            String station_flow_id = panelListParas[0];
            String lot_num = panelListParas[1];
            Integer panel_index = Integer.parseInt(panelListParas[3]);
            String panel_barcode = panelListParas[4];
            Integer panel_ng_code = Integer.parseInt(panelListParas[5]);//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5Hold,6其他定义
            Integer inspect_finish_count = Integer.parseInt(panelListParas[6]);
            String lot_status = panelListParas[8];
            String group_lot_num = panelListParas[9];
            String first_panel_flag="N";
            String panel_staus="";
            String slot_no = String.format("%03d", panel_index);
            String prod_id = "";
            String prod_version = "";
            String process_code = "";
            JSONObject attr_else = null;
            String eqp_job_type = jsonParas.getString("eqp_job_type");//FCL、ECL、ECR、FCU、FCR、ECRFCL、FCUECL
            String manual_wip_flag=jsonParas.getString("manual_wip_flag");//是否人工补报
            if (eqp_job_type == null) eqp_job_type = "";
            if (manual_wip_flag == null) manual_wip_flag = "";
            String DySmallPanelFlag=cFuncDbSqlResolve.GetParameterValue("Dy_SmallPanel");
            String virtu_pallet_num=pallet_num;
            String yk_result="OK";

            //新增HoldingTime参数
            String holding_time = "";
            String over_times = "0";//超时时间
            Integer hold_result = 0;
            String face_code="0";
            Integer ng_control=-1;//0正常模式,1强制放行
            if (panel_ng_code == 0) hold_result = 1;
            else if (panel_ng_code == 1) hold_result = 3;
            else if (panel_ng_code == 2) hold_result = 2;
            else if (panel_ng_code == 3) hold_result = 3;
            else if (panel_ng_code == 4) hold_result = 4;
            else if (panel_ng_code == 5) hold_result = 5;
            if (onff_line.equals("0")) {
                hold_result = 6;
            }
            if(station_attr.equals("Load")){
                if (panelListParas.length >= 11) holding_time = panelListParas[10];
                if (panelListParas.length >= 12) over_times = panelListParas[11];
                if (panelListParas.length >= 13) face_code = panelListParas[12];
                if(panelListParas.length>=14) first_panel_flag=panelListParas[13];
                if(panelListParas.length>=15) panel_staus=panelListParas[14];
            }
            if(station_attr.equals("UnLoad")){
                if (panelListParas.length >= 11){
                    if(!panelListParas[10].equals("0")){
                        port_code = "0"+panelListParas[10];
                    }
                }
                if (panelListParas.length >= 12) face_code = panelListParas[11];
                if (panelListParas.length >= 13) panel_staus = panelListParas[12];
                if("Y".equals(DySmallPanelFlag)){
                    if (panelListParas.length >= 14) yk_result = panelListParas[13];
                    if(!"OK".equals(yk_result)) yk_result="NG";
                }
            }

            if (panel_model.equals("1")) {
                if (panel_barcode == null || panel_barcode.equals("") || panel_barcode.equals("FFFFFFFF"))
                    panel_barcode = "NoRead";
            }
            //根据母批查询信息
            Integer sum_plan_count = 0;
            Integer sum_finish_count = 0;
            Integer sum_finish_ok_count = 0;
            String material_code_P001 = "";
            String lot_version_P002 = "";
            String lot_num_P003 = "";
            String first_plan_count_P004 = "0";
            String attribute1_P005 = "";
            String attribute2_P006 = "";
            String user_name = "";
            String item_info="";
            JSONArray lot_list = new JSONArray();
            Boolean is_exist_virtu_pallet=false;

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                String lot_num2 = docItemBigData.getString("lot_num");
                sum_plan_count += plan_lot_count;
                sum_finish_count += finish_count;
                sum_finish_ok_count += finish_ok_count;
                if (lot_num2.equals(lot_num)) {
                    material_code_P001 = docItemBigData.getString("material_code");
                    lot_version_P002 = docItemBigData.getString("lot_level");
                    lot_num_P003 = lot_num;
                    first_plan_count_P004 = String.valueOf(plan_lot_count);
                    attribute1_P005 = docItemBigData.getString("attribute1");
                    attribute2_P006 = docItemBigData.getString("attribute2");
                    item_info = docItemBigData.getString("item_info");
                    prod_id = material_code_P001;
                    prod_version = lot_version_P002;
                    process_code = attribute1_P005;
                    if (docItemBigData.containsKey("attribute3")) {
                        String attribute_else = docItemBigData.getString("attribute3");
                        if (attribute_else != null && !attribute_else.equals("")) {
                            attr_else = JSONObject.parseObject(attribute_else);
                        }
                    }
                    pallet_num = docItemBigData.getString("pallet_num");
                    ng_control=docItemBigData.getInteger("panel_model");
                    virtu_pallet_num=pallet_num;
                    if(docItemBigData.containsKey("virtu_pallet_num")){
                        is_exist_virtu_pallet=true;
                        virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                    }
                }
                Integer left_count = 0;
                if (station_attr.equals("Load")) {
                    left_count = plan_lot_count - finish_ok_count;
                    if (left_count < 0) left_count = 0;
                } else {
                    left_count = finish_ok_count;
                }
                JSONObject jbItem = new JSONObject();
                jbItem.put("lot_id", lot_num2);
                jbItem.put("lot_count", left_count);
                lot_list.add(jbItem);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前用户
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //远程模式下上报EAP
            if (sys_model.equals("1") || sys_model.equals("0")) {
                Integer left_count = 0;
                if (station_attr.equals("Load")) {
                    left_count = sum_plan_count - sum_finish_ok_count;
                    if (left_count < 0) left_count = 0;
                } else {
                    left_count = sum_finish_ok_count;
                }

                if(DySmallPanelFlag.equals("Y")){
                    if(first_panel_flag.equals("Y") && panel_index==1 && !manual_wip_flag.equals("Y") && station_attr.equals("Load")){
                        eapDySendFlowFunc.PortStatusChangeReportAsync(station_code, port_code, station_attr, "PROC", "N", pallet_num, String.valueOf(left_count), user_name, "N", "N",eqp_job_type);
                        eapDySendFlowFunc.CarrierStatusReportAsync(station_code, pallet_num, "Process", lot_list, "0", station_attr, port_code,"N");
                    }
                    else{
                        if (panel_index == 1 && !manual_wip_flag.equals("Y") && station_attr.equals("UnLoad")) {
                            if(panel_ng_code==0 || panel_ng_code==4 || panel_staus.equals("OK") || panel_staus.equals("NG_PASS")){
                                if(panel_ng_code!=11 && panel_ng_code!=30 && panel_ng_code!=8){
                                    eapDySendFlowFunc.PortStatusChangeReportAsync(station_code, port_code, station_attr, "PROC", "N", pallet_num, String.valueOf(left_count), user_name, "N", "N",eqp_job_type);
                                    eapDySendFlowFunc.CarrierStatusReportAsync(station_code, pallet_num, "Process", lot_list, "0", station_attr, port_code,"N");
                                }
                            }
                        }
                    }
                }
                else{
                    //【必须】若是第一片则必须上报PROC和开始投收板事件
                    if (sum_finish_ok_count == 1 && !manual_wip_flag.equals("Y")) {
                        if(panel_ng_code==0 || panel_ng_code==4 || panel_staus.equals("OK") || panel_staus.equals("NG_PASS")){
                            if(panel_ng_code!=11 && panel_ng_code!=30 && panel_ng_code!=8){
                                eapDySendFlowFunc.PortStatusChangeReportAsync(station_code, port_code, station_attr, "PROC", "N", pallet_num, String.valueOf(left_count), user_name, "N", "N",eqp_job_type);
                                eapDySendFlowFunc.CarrierStatusReportAsync(station_code, pallet_num, "Process", lot_list, "0", station_attr, port_code,"N");
                            }
                        }
                    } else {
                        //若是一批多车,需要判断是否第一次载具码,若是也需要进行PROC|Process上报
                        if (OneCarMultyLotFlag.equals("2") && !manual_wip_flag.equals("Y")) {
                            if(panel_ng_code==0 || panel_ng_code==4 || panel_staus.equals("OK") || panel_staus.equals("NG_PASS")){
                                if(panel_ng_code!=11 && panel_ng_code!=30 && panel_ng_code!=8){
                                    long pnListCount=0l;
                                    String[] panel_status_list=new String[]{"OK","NG_PASS"};
                                    queryBigData = new Query();
                                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                    queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                                    queryBigData.addCriteria(Criteria.where("panel_status").in(panel_status_list));
                                    if(is_exist_virtu_pallet && !"".equals(virtu_pallet_num)){
                                        queryBigData.addCriteria(Criteria.where("virtu_pallet_num").is(virtu_pallet_num));
                                        pnListCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                                    }
                                    else{
                                        if (pallet_num != null && !pallet_num.equals("") && !pallet_num.equals("NoRead")){
                                            queryBigData.addCriteria(Criteria.where("pallet_num").is(pallet_num));
                                            pnListCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                                        }
                                    }
                                    if (pnListCount == 1) {
                                        eapDySendFlowFunc.PortStatusChangeReportAsync(station_code, port_code, station_attr, "PROC", "N", pallet_num, String.valueOf(left_count), user_name, "N", "N",eqp_job_type);
                                        eapDySendFlowFunc.CarrierStatusReportAsync(station_code, pallet_num, "Process", lot_list, "0", station_attr, port_code,"N");
                                    }
                                }
                            }
                        }
                    }
                }

                if(!manual_wip_flag.equals("Y")){
                    //1.CCDDataReport:[接口]CCD读码上报
                    eapDySendFlowFunc.CCDDataReport(station_code, panel_barcode, ccdNo);
                    //2.FetchOutReport:[接口]每放一片板需要上报（针对放板机）
                    //3.StoreInReport:[接口]每收一片板需要上报（针对收板机）
                    if (station_attr.equals("Load")) {
                        eapDySendFlowFunc.FetchOutReport(station_code, panel_barcode, port_code, slot_no, lot_num);
                    } else {
                        eapDySendFlowFunc.StoreInReport(station_code, panel_barcode, port_code, slot_no, lot_num);
                    }
                    //4.JobCountReport:[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
                    eapDySendFlowFunc.JobCountReport(station_code, left_count, port_code,station_attr);
                }
            }

            //上报读码结果用于EAP统计读码率
            if (panel_model.equals("1")) {
                //5.PanelDataUploadReport:[接口]每片Panel收放台車事件上報
                String local_flag = "Y";
                if (sys_model.equals("1")) local_flag = "N";
                String offline_flag = "Y";
                if (onff_line.equals("1")) offline_flag = "N";

                //上传属性
                JSONArray item_attr_list = new JSONArray();
                JSONObject jbAttr = new JSONObject();
                jbAttr.put("item_id", "P001");
                jbAttr.put("item_value", material_code_P001);
                item_attr_list.add(jbAttr);
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P002");
                jbAttr.put("item_value", lot_version_P002);
                item_attr_list.add(jbAttr);
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P003");
                jbAttr.put("item_value", lot_num_P003);
                item_attr_list.add(jbAttr);
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P004");
                jbAttr.put("item_value", first_plan_count_P004);
                item_attr_list.add(jbAttr);
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P005");
                jbAttr.put("item_value", attribute1_P005);
                item_attr_list.add(jbAttr);
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P006");
                jbAttr.put("item_value", attribute2_P006);
                item_attr_list.add(jbAttr);

                Boolean isP1Factory = eapDyInterfCommon.CheckDyFactory("P1");
                Boolean isNewVersion = eapDyInterfCommon.CheckDyVersion(3);//是否泰国超颖项目
                if (isP1Factory) {
                    //增加判定结果
                    jbAttr = new JSONObject();
                    jbAttr.put("item_id", "P007");
                    jbAttr.put("item_value", String.valueOf(hold_result));
                    item_attr_list.add(jbAttr);
                }
                else{
                    if(isNewVersion){
                        item_attr_list = new JSONArray();
                        //1.读码判定结果
                        //0为正常,1为混板,2为读码失败板,3重码,4强制越过,5Hold,6其他定义
                        String new_panel_status="OK";
                        if (panel_ng_code == 0) new_panel_status = "OK";
                        else if(panel_ng_code == 2) new_panel_status = "NoRead";
                        else if(panel_ng_code == 1) new_panel_status = "Mix";
                        else if(panel_ng_code == 8) new_panel_status = "No Mission";
                        else if(panel_ng_code == 4) new_panel_status = "No Control";
                        else if(panel_ng_code == 5) new_panel_status = "Time Out";
                        else if(panel_ng_code == 11) new_panel_status = "Dummy";
                        if(lot_num==null || lot_num.equals("")) new_panel_status = "No Mission";
                        if(ng_control==1) new_panel_status = "No Control";
                        jbAttr = new JSONObject();
                        jbAttr.put("item_id", "P001");
                        jbAttr.put("item_value", new_panel_status);
                        item_attr_list.add(jbAttr);
                        //2.面次
                        String new_face_code="NoRead";
                        if(face_code.equals("1")) new_face_code="01";
                        if(face_code.equals("2")) new_face_code="02";
                        if(panel_barcode.equals("") || panel_barcode.equals("NoRead")) new_face_code="NoRead";
                        if(DySmallPanelFlag.equals("Y")) new_face_code="01";//小板默认正面面次
                        jbAttr = new JSONObject();
                        jbAttr.put("item_id", "P002");
                        jbAttr.put("item_value", new_face_code);
                        item_attr_list.add(jbAttr);
                        //3.读码上报方式
                        jbAttr = new JSONObject();
                        jbAttr.put("item_id", "P003");
                        if(manual_wip_flag.equals("Y")){
                            jbAttr.put("item_value", "OPReader");
                        }
                        else{
                            jbAttr.put("item_value", "2DReader");
                        }
                        item_attr_list.add(jbAttr);
                        if("Y".equals(DySmallPanelFlag) && station_attr.equals("UnLoad")){
                            jbAttr = new JSONObject();
                            jbAttr.put("item_id", "P004");
                            jbAttr.put("item_value", yk_result);
                            item_attr_list.add(jbAttr);
                        }
                    }
                    else{
                        if (!DySmallPanelFlag.equals("Y")){
                            //黄石超颖逻辑
                            //P011
                            String new_panel_status = "1";
                            if (panel_ng_code == 0) new_panel_status = "1";
                            else if (panel_ng_code == 2) new_panel_status = "2";
                            else if (panel_ng_code == 1) new_panel_status = "3";
                            else if (panel_ng_code == 8) new_panel_status = "6";
                            else if (panel_ng_code == 4) new_panel_status = "4";
                            else if (panel_ng_code == 5) new_panel_status = "5";
                            else if (panel_ng_code == 7) new_panel_status = "7";
                            if (lot_num == null || lot_num.equals("")) new_panel_status = "6";
                            if (ng_control == 1) new_panel_status = "4";
                            //P012
                            String new_up_way = "1";
                            if (manual_wip_flag.equals("Y")) new_up_way = "2";
                            //增加到属性组中
                            jbAttr = new JSONObject();
                            jbAttr.put("item_id", "P011");
                            jbAttr.put("item_value", new_panel_status);
                            item_attr_list.add(jbAttr);
                            jbAttr = new JSONObject();
                            jbAttr.put("item_id", "P012");
                            jbAttr.put("item_value", new_up_way);
                            item_attr_list.add(jbAttr);
                        }
                    }
                }
                //若是收扳机且提前上报,则不需要处理
                Boolean isUpPanel=true;
                if(station_attr.equals("UnLoad") && !manual_wip_flag.equals("Y")){
                    try{
                        String ZanCunCcdFlag= opCommonFunc.ReadCellOneRedisValue(station_code,station_attr,
                                "Ais","AisConfig","ZanCunCcdFlag");
                        if("1".equals(ZanCunCcdFlag)){
                            isUpPanel=false;
                        }
                    }
                    catch (Exception exx2){}
                }
                if(isUpPanel){
                    eapDySendFlowFunc.PanelDataUploadReport(Long.parseLong(station_id), station_code, panel_barcode, slot_no,
                            item_attr_list, offline_flag, local_flag, ccdNo, lot_num, prod_id, prod_version, process_code,
                            attr_else, holding_time, over_times, hold_result,ng_control,item_info);
                }
            }

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            try {
                if (iteratorBigData != null) {
                    if (iteratorBigData.hasNext()) iteratorBigData.close();
                }
            } catch (Exception exx) {
            }
            errorMsg = "综合读码上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //6.板件传递到下游综合
    @RequestMapping(value = "/DownDevicePanelReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyDownDevicePanelReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/DownDevicePanelReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String panel_result = jsonParas.getString("panel_result");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String onff_line = jsonParas.getString("onff_line");//在线离线
            String panel_model = jsonParas.getString("panel_model");//有无Panel模式
            String inspect_flag = jsonParas.getString("inspect_flag");//是否首件
            String[] panelListParas = panel_result.split(",", -1);
            String station_flow_id = panelListParas[0];
            String lot_num = panelListParas[1];
            Integer panel_index = Integer.parseInt(panelListParas[3]);
            String panel_barcode = panelListParas[4];
            Integer panel_ng_code = Integer.parseInt(panelListParas[5]);
            Integer inspect_finish_count = Integer.parseInt(panelListParas[6]);
            String lot_status = panelListParas[8];
            String group_lot_num = panelListParas[9];
            String slot_no = String.format("%03d", panel_index);
            String panel_status = "OK";
            //0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (panel_ng_code == 4) panel_status = "NG_PASS";
            else {
                if (panel_ng_code > 0) panel_status = "NG";
            }
            if (panel_model.equals("1")) {
                if (panel_barcode == null || panel_barcode.equals("") || panel_barcode.equals("FFFFFFFF"))
                    panel_barcode = "NoRead";
            }
            if(eapDyInterfCommon.CheckDyVersion(3)){
                String panel_staus="";
                panel_status="2";
                if(station_attr.equals("Load")){
                    if(panelListParas.length>=15) panel_staus=panelListParas[14];
                }
                if(station_attr.equals("UnLoad")){
                    if (panelListParas.length >= 13) panel_staus = panelListParas[12];
                }
                if("NG_PASS".equals(panel_staus)) panel_status="6";
                else{
                    if(panel_ng_code==0) panel_status="0";
                    else if(panel_ng_code==4) panel_status="6";
                    else if(panel_ng_code==2 || panel_ng_code==7) panel_status="1";
                }
            }
            eapDySendFlowFunc.EachPanelDataDownLoad(station_code, lot_num, panel_barcode, panel_status,slot_no);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "板件传递到下游综合异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //7.EAP子批次完板上报
    @RequestMapping(value = "/SubLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDySubLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/SubLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        MongoCursor<Document> iteratorBigData = null;
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String panel_result = jsonParas.getString("panel_result");
            String offline_flag = jsonParas.getString("offline_flag");//在线、离线模式
            if (offline_flag == null) offline_flag = "N";
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String prod_mode = jsonParas.getString("prod_mode");
            String nw_value = jsonParas.getString("nw_value");//内外层代码
            String short_count = jsonParas.getString("short_count");//少片数量
            if (short_count == null || short_count.equals("")) short_count = "0";
            String noread_count = jsonParas.getString("noread_count");//NoRead数量
            if (noread_count == null || noread_count.equals("")) noread_count = "0";
            String[] panelListParas = panel_result.split(",", -1);
            String station_flow_id = panelListParas[0];
            String lot_num = panelListParas[1];
            String lot_status = panelListParas[8];
            String group_lot_num = panelListParas[9];
            String local_flag = "Y";
            if (sys_model.equals("1")) local_flag = "N";
            //判断是否为定颖新版本,最后统一上报
            String wip_flag=jsonParas.getString("wip_flag");
            String m_wip_lot_num=jsonParas.getString("m_wip_lot_num");
            String manual_wip_flag=jsonParas.getString("manual_wip_flag");
            if(wip_flag==null) wip_flag="";
            if(m_wip_lot_num==null) m_wip_lot_num="";
            if(manual_wip_flag==null) manual_wip_flag="";
            Boolean isNewVersion=eapDyInterfCommon.CheckDyVersion(3);
            if(!wip_flag.equals("Y")){
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OnOffLineTag = "";
            String PanelModelTag = "";
            String NgPanelPassFlagTag = "";
            String OnOffLine = "";//在线离线
            String PanelModel="";//有无panel
            String NgPanelPassFlag="";//是否NG强制PASS
            if (aisMonitorModel.equals("AIS-PC")) {
                OnOffLineTag = station_attr + "Plc/PlcConfig/OnOffLine";
                PanelModelTag= station_attr + "Plc/PlcConfig/PanelModel";
                NgPanelPassFlagTag= station_attr + "Ais/AisConfig/NgPanelPassFlag";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OnOffLineTag = station_attr + "Plc_" + station_code + "/PlcConfig/OnOffLine";
                PanelModelTag = station_attr + "Plc_" + station_code + "/PlcConfig/PanelModel";
                NgPanelPassFlagTag = station_attr + "Ais_" + station_code + "/AisConfig/NgPanelPassFlag";
            } else {
                errorMsg = "AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code,
                    OnOffLineTag+","+PanelModelTag+","+NgPanelPassFlagTag);
            if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                for (int i = 0; i < jsonArrayTag.size(); i++) {
                    JSONObject jbItem = jsonArrayTag.getJSONObject(i);
                    String tag_key = jbItem.getString("tag_key");
                    String tag_value = jbItem.getString("tag_value");
                    if (tag_key.equals(OnOffLineTag)) OnOffLine = tag_value;
                    if (tag_key.equals(PanelModelTag)) PanelModel = tag_value;
                    if (tag_key.equals(NgPanelPassFlagTag)) NgPanelPassFlag = tag_value;
                }
            }
            if (!OnOffLine.equals("1")) offline_flag = "Y";
            else offline_flag = "N";

            //新增一批多车多级
            String out_code = jsonParas.getString("out_code");
            if (out_code == null || out_code.equals("")) out_code = "-1";

            //若是载具转换机
            String station_attr2=jsonParas.getString("station_attr2");
            if(station_attr2!=null && !"".equals(station_attr2)) station_attr=station_attr2;

            //根据母批查询信息
            Integer sum_plan_count = 0;
            Integer sum_finish_count = 0;
            Integer sum_finish_ok_count = 0;
            String material_code_P001 = "";
            String lot_version_P002 = "";
            Integer first_plan_count_P004 = 0;
            Integer lot_finish_count = 0;
            Integer lot_finish_count2 = 0;
            String lot_short_num = "";
            String attribute1_P005 = "";
            String attribute2_P006 = "";
            String user_name = "";
            String dept_id = "";
            String shift_id = "";
            String task_start_time = CFuncUtilsSystem.GetNowDateTime("");
            String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
            String item_info = "";
            JSONArray item_attr_list = null;
            String splitseq = "0";
            String lastSplit = "1";
            if (prod_mode == null || prod_mode.equals("")) prod_mode = "0";
            if (nw_value == null || nw_value.equals("")) nw_value = "0";
            JSONObject attr_else = null;
            Double panel_length=0D;
            Double panel_width=0D;
            Double panel_tickness=0D;
            Integer panel_model=-1;
            String task_from="EAP";

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                String lot_num2 = docItemBigData.getString("lot_num");
                sum_plan_count += plan_lot_count;
                sum_finish_count += finish_count;
                sum_finish_ok_count += finish_ok_count;
                if (lot_num2.equals(lot_num)) {
                    task_from=docItemBigData.getString("task_from");
                    material_code_P001 = docItemBigData.getString("material_code");
                    lot_version_P002 = docItemBigData.getString("lot_level");
                    first_plan_count_P004 = plan_lot_count;
                    lot_finish_count = finish_ok_count;
                    lot_finish_count2=finish_count;
                    lot_short_num = docItemBigData.getString("lot_short_num");
                    item_info = docItemBigData.getString("item_info");
                    attribute1_P005 = docItemBigData.getString("attribute1");
                    attribute2_P006 = docItemBigData.getString("attribute2");
                    panel_length=docItemBigData.getDouble("panel_length");
                    panel_width=docItemBigData.getDouble("panel_width");
                    panel_tickness=docItemBigData.getDouble("panel_tickness");
                    panel_model=docItemBigData.getInteger("panel_model");
                    if (docItemBigData.containsKey("attribute3")) {
                        String attribute_else = docItemBigData.getString("attribute3");
                        if (attribute_else != null && !attribute_else.equals("")) {
                            attr_else = JSONObject.parseObject(attribute_else);
                        }
                    }
                    pallet_num = docItemBigData.getString("pallet_num");
                    task_start_time = docItemBigData.getString("task_start_time");
                    task_end_time = docItemBigData.getString("task_end_time");
                    if (task_start_time == null || task_start_time.equals("")) {
                        task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    }
                    if (task_end_time == null || task_end_time.equals("")) {
                        task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    }
                    //判断结束时间是否大于开始时间,若是则开始时间设定等于结束时间,这是因为可能没有放一块板子
                    if (CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time) < 0) {
                        task_start_time = task_end_time;
                    }
                }
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前用户
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                dept_id = docItemBigData.getString("dept_id");
                shift_id = docItemBigData.getString("shift_id");
                iteratorBigData.close();
            }

            JSONObject jsonObject = eapDyInterfCommon.GetParamsMapping();
            Boolean isP1Factory = eapDyInterfCommon.CheckDyFactory("P1");
            Integer wip_noread_count=0;
            Integer wip_short_count=0;
            String DySmallPanelFlag=cFuncDbSqlResolve.GetParameterValue("Dy_SmallPanel");
            if(!isNewVersion){
                if(DySmallPanelFlag.equals("Y")){
                    //获取S001-S005值
                    String S001="";
                    String S002="";
                    String S003="";
                    String S004="";
                    String S005="";
                    String S006="";
                    if (item_info != null && !item_info.equals("")){
                        JSONArray item_info_list = JSONArray.parseArray(item_info);
                        if (item_info_list != null && item_info_list.size() > 0){
                            for (int i = 0; i < item_info_list.size(); i++){
                                JSONObject jbItem = item_info_list.getJSONObject(i);
                                String item_id = jbItem.getString("item_id");
                                String item_value = jbItem.getString("item_value");
                                if(item_id.equals("S001")) S001=item_value;
                                else if(item_id.equals("S002")) S002=item_value;
                                else if(item_id.equals("S003")) S003=item_value;
                                else if(item_id.equals("S004")) S004=item_value;
                                else if(item_id.equals("S005")) S005=item_value;
                                else if(item_id.equals("S006")) S006=item_value;
                            }
                        }
                    }
                    //查询NoRead数量
                    String[] lstNoReadPanel=new String[]{"","NoRead"};
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("panel_barcode").in(lstNoReadPanel));
                    queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                    long noReadCount2 = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                    //判断少片数量
                    Integer shortCount2=first_plan_count_P004-lot_finish_count-(int)noReadCount2;
                    if(shortCount2<0) shortCount2=0;
                    //判断结束批次类型
                    String end_lot_type="3";
                    if(first_plan_count_P004==lot_finish_count) end_lot_type="1";
                    else if(lot_finish_count<=0) end_lot_type="4";
                    Integer out_code_int=Integer.parseInt(out_code);
                    if(out_code_int>=4){
                        end_lot_type="5";
                        if(lot_finish_count2<=0) end_lot_type="4";
                    }
                    if(manual_wip_flag.equals("Y")) end_lot_type="6";
                    //组合数据
                    item_attr_list = new JSONArray();
                    //Ship-PNL尺寸(徑)
                    JSONObject jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T001");
                    jbAttr.put("item_value", S001);
                    item_attr_list.add(jbAttr);
                    //Ship-PNL尺寸(緯)
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T002");
                    jbAttr.put("item_value", S002);
                    item_attr_list.add(jbAttr);
                    //成品板厚
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T003");
                    jbAttr.put("item_value", S003);
                    item_attr_list.add(jbAttr);
                    //開啓讀碼標志位
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T004");
                    jbAttr.put("item_value", S004);
                    item_attr_list.add(jbAttr);
                    //讀碼比對資料路徑
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T005");
                    jbAttr.put("item_value", S005);
                    item_attr_list.add(jbAttr);
                    //NoRead數量
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T006");
                    jbAttr.put("item_value", String.valueOf(noReadCount2));
                    item_attr_list.add(jbAttr);
                    //少片數量
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T007");
                    jbAttr.put("item_value", String.valueOf(shortCount2));
                    item_attr_list.add(jbAttr);
                    //結批類型
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T008");
                    jbAttr.put("item_value", end_lot_type);
                    item_attr_list.add(jbAttr);
                    //2D数量
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T009");
                    jbAttr.put("item_value", S006);
                    item_attr_list.add(jbAttr);
                }
                else{
                    //3.处理Item_Info
                    if (item_info != null && !item_info.equals("")) {
                        if (!isP1Factory) {
                            for (int j = 1; j <= 200; j++) {
                                String SSource = "S" + String.format("%03d", j);
                                String TTarget = "T" + String.format("%03d", j + 1);
                                if (jsonObject != null && jsonObject.containsKey(TTarget)) {
                                    SSource = jsonObject.getString(TTarget);
                                }
                                item_info = item_info.replace(SSource, TTarget);
                            }
                        }
                        item_attr_list = JSONArray.parseArray(item_info);
                        if (!isP1Factory) {
                            if (nw_value == null || nw_value.equals("")) nw_value = "0";
                            JSONObject nw_obj = new JSONObject();
                            nw_obj.put("item_id", "T001");
                            nw_obj.put("item_value", nw_value);
                            item_attr_list.add(nw_obj);
                        }
                    }
                    if (!isP1Factory) {
                        if (item_attr_list == null) item_attr_list = new JSONArray();
                        for (int k = item_attr_list.size() - 1; k >= 0; k--) {
                            JSONObject jbItemAttr = item_attr_list.getJSONObject(k);
                            String item_id = jbItemAttr.getString("item_id");
                            if (item_id.equals("T030") || item_id.equals("T031") || item_id.equals("T032")) {
                                item_attr_list.remove(k);
                            }
                        }
                        JSONObject T030 = new JSONObject();
                        T030.put("item_id", "T030");
                        T030.put("item_value", noread_count);
                        item_attr_list.add(T030);
                        JSONObject T031 = new JSONObject();
                        T031.put("item_id", "T031");
                        T031.put("item_value", short_count);
                        item_attr_list.add(T031);

                        //新增逻辑
                        String end_lot_type = "3";
                        if (first_plan_count_P004 == lot_finish_count) end_lot_type = "1";
                        else if (lot_finish_count <= 0) end_lot_type = "4";
                        Integer out_code_int = Integer.parseInt(out_code);
                        if (out_code_int >= 4)
                        {
                            end_lot_type = "5";
                            if (lot_finish_count2 <= 0) end_lot_type = "4";
                        }
                        if (!m_wip_lot_num.equals("")) end_lot_type = "5";
                        if (out_code_int >= 4)
                        {
                            if (lot_finish_count2 <= 0) end_lot_type = "4";
                        }
                        if (manual_wip_flag.equals("Y")) end_lot_type = "6";
                        //2.NoRead数量
                        Integer last_noread_count = 0;
                        Integer last_short_count = 0;
                        if (station_attr.equals("Load"))
                        {
                            Integer[] lstNoReadPanelCode = new Integer[]{2, 7};
                            queryBigData = new Query();
                            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                            queryBigData.addCriteria(Criteria.where("panel_ng_code").in(lstNoReadPanelCode));
                            queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                            long noReadCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                            //查询NoRead为NG的数量
                            queryBigData = new Query();
                            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                            queryBigData.addCriteria(Criteria.where("panel_ng_code").in(lstNoReadPanelCode));
                            queryBigData.addCriteria(Criteria.where("panel_status").is("NG"));
                            queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                            long ngNoReadCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                            last_noread_count = (int) noReadCount;
                            last_short_count = first_plan_count_P004 - lot_finish_count - (int) ngNoReadCount;
                            if (!m_wip_lot_num.equals(""))
                            {
                                if (m_wip_lot_num.equals(lot_num))
                                {
                                    last_noread_count = Integer.parseInt(noread_count);
                                    last_short_count = Integer.parseInt(short_count);
                                }
                            }
                        }
                        else
                        {
                            Integer[] lstNoReadPanelCode = new Integer[]{2, 7};
                            queryBigData = new Query();
                            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                            queryBigData.addCriteria(Criteria.where("panel_ng_code").in(lstNoReadPanelCode));
                            queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                            long noReadCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                            last_noread_count = (int) noReadCount;
                            last_short_count = first_plan_count_P004 - lot_finish_count;
                        }
                        Integer new_short_count = last_short_count;
                        if (new_short_count < 0) new_short_count = 0;
                        wip_noread_count = last_noread_count;
                        wip_short_count = new_short_count;
                        if (wip_short_count > 0 && station_attr.equals("UnLoad") && !manual_wip_flag.equals("Y") && lot_finish_count2 > 0)
                        {
                            end_lot_type = "5";
                        }
                        JSONObject T032 = new JSONObject();
                        T032.put("item_id", "T032");
                        T032.put("item_value", end_lot_type);
                        item_attr_list.add(T032);
                    }
                }
            }
            else{
                //泰国定颖
                item_attr_list = new JSONArray();
                //1.结批类型:1：计数到达；2：结批信息；3：完工信号；4：取消任務；5：人员手动；6：掃碼補報
                String end_lot_type="3";
                if(first_plan_count_P004==lot_finish_count) end_lot_type="1";
                else if(lot_finish_count<=0) end_lot_type="4";
                Integer out_code_int=Integer.parseInt(out_code);
                if(out_code_int>=4){
                    end_lot_type="5";
                    if(lot_finish_count2<=0) end_lot_type="4";
                }
                if(!m_wip_lot_num.equals("")) end_lot_type="5";
                if(out_code_int>=4){
                    if(lot_finish_count2<=0) end_lot_type="4";
                }
                if(manual_wip_flag.equals("Y")) end_lot_type="6";
                //2.NoRead数量
                Integer last_noread_count=0;
                Integer last_short_count=0;
                if(station_attr.equals("Load")){
                    Integer[] lstNoReadPanelCode=new Integer[]{2,7};
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("panel_ng_code").in(lstNoReadPanelCode));
                    queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                    long noReadCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                    //查询NoRead为NG的数量
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("panel_ng_code").in(lstNoReadPanelCode));
                    queryBigData.addCriteria(Criteria.where("panel_status").is("NG"));
                    queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                    long ngNoReadCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                    last_noread_count=(int)noReadCount;
                    last_short_count=first_plan_count_P004-lot_finish_count-(int)ngNoReadCount;
                    if(!m_wip_lot_num.equals("")){
                        if(m_wip_lot_num.equals(lot_num)){
                            last_noread_count=Integer.parseInt(noread_count);
                            last_short_count=Integer.parseInt(short_count);
                        }
                    }
                }
                else{
                    Integer[] lstNoReadPanelCode=new Integer[]{2,7};
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("panel_ng_code").in(lstNoReadPanelCode));
                    queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                    long noReadCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                    last_noread_count=(int)noReadCount;
                    last_short_count=first_plan_count_P004-lot_finish_count;
                }
                Integer new_short_count=last_short_count;
                if(new_short_count<0) new_short_count=0;
                wip_noread_count=last_noread_count;
                wip_short_count=new_short_count;
                if(wip_short_count>0 && station_attr.equals("UnLoad") && !manual_wip_flag.equals("Y") && lot_finish_count2>0){
                    end_lot_type="5";
                }
                //类型
                JSONObject jbAttr=new JSONObject();
                jbAttr.put("item_id", "T001");
                jbAttr.put("item_value", end_lot_type);
                item_attr_list.add(jbAttr);
                //NoRead数量
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T002");
                jbAttr.put("item_value", String.valueOf(last_noread_count));
                item_attr_list.add(jbAttr);
                //3.少片数量
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T003");
                jbAttr.put("item_value", String.valueOf(new_short_count));
                item_attr_list.add(jbAttr);
                //4.板长
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T004");
                jbAttr.put("item_value", String.format("%.3f", panel_length));
                item_attr_list.add(jbAttr);
                //5.板宽
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T005");
                jbAttr.put("item_value", String.format("%.3f", panel_width));
                item_attr_list.add(jbAttr);
                //6.板厚
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T006");
                jbAttr.put("item_value", String.format("%.3f", panel_tickness));
                item_attr_list.add(jbAttr);
                //7.读码NG管控标志
                String new_panel_model="1";
                if(panel_model==1) new_panel_model="2";
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T007");
                jbAttr.put("item_value", String.valueOf(new_panel_model));
                item_attr_list.add(jbAttr);
                //8.其他需要做映射方法
                if("AIS".equals(task_from)){
                    String dy_paramsMapping_ais=cFuncDbSqlResolve.GetParameterValue("Dy_ParamsMapping_Ais");
                    if(!"".equals(dy_paramsMapping_ais)){
                        jsonObject=JSONObject.parseObject(dy_paramsMapping_ais);
                        if (jsonObject != null){
                            Set<String> keys=jsonObject.keySet();
                            JSONArray jaAttrTemp=null;
                            if (item_info != null && !item_info.equals("")){
                                jaAttrTemp=JSONArray.parseArray(item_info);
                            }
                            for(String key : keys){
                                String oriKey=jsonObject.getString(key);
                                String keyValue=oriKey;
                                if(jaAttrTemp!=null && jaAttrTemp.size()>0 && !oriKey.equals("") && oriKey.startsWith("S")){
                                    for(int k=0;k<jaAttrTemp.size();k++){
                                        JSONObject jbItemTemp=jaAttrTemp.getJSONObject(k);
                                        String item_id2=jbItemTemp.getString("item_id");
                                        String item_value2=jbItemTemp.getString("item_value");
                                        if(item_id2.equals(oriKey)){
                                            Double item_value3=0D;
                                            try{
                                                item_value3=Double.parseDouble(item_value2);
                                            }
                                            catch (Exception exChange){}
                                            keyValue=String.format("%.3f", item_value3);//若是S开头则需要给格式
                                            break;
                                        }
                                    }
                                }
                                jbAttr=new JSONObject();
                                jbAttr.put("item_id", key);
                                jbAttr.put("item_value", keyValue);
                                item_attr_list.add(jbAttr);
                            }
                        }
                    }
                }
                else{
                    if (jsonObject != null){
                        Set<String> keys=jsonObject.keySet();
                        JSONArray jaAttrTemp=null;
                        if (item_info != null && !item_info.equals("")){
                            jaAttrTemp=JSONArray.parseArray(item_info);
                        }
                        for(String key : keys){
                            String oriKey=jsonObject.getString(key);
                            String keyValue=oriKey;
                            if(jaAttrTemp!=null && jaAttrTemp.size()>0 && !oriKey.equals("")){
                                for(int k=0;k<jaAttrTemp.size();k++){
                                    JSONObject jbItemTemp=jaAttrTemp.getJSONObject(k);
                                    String item_id2=jbItemTemp.getString("item_id");
                                    String item_value2=jbItemTemp.getString("item_value");
                                    if(item_id2.equals(oriKey)){
                                        keyValue=item_value2;
                                        break;
                                    }
                                }
                            }
                            jbAttr=new JSONObject();
                            jbAttr.put("item_id", key);
                            jbAttr.put("item_value", keyValue);
                            item_attr_list.add(jbAttr);
                        }
                    }
                }
            }

            //用于DataUpLoad参数
            Integer lot_left_count = 0;
            JSONArray carr_infos = new JSONArray();
            JSONObject jbCarr = new JSONObject();
            JSONObject jbLot = new JSONObject();
            if (!eapDyInterfCommon.CheckDyVersion(3)) {
                lot_left_count = first_plan_count_P004;
            } else {
                if (station_attr.equals("Load")) {
                    lot_left_count = first_plan_count_P004 - lot_finish_count;
                    if (lot_left_count < 0) lot_left_count = 0;
                } else {
                    lot_left_count = lot_finish_count;
                }
            }
            jbLot.put("lot_id", lot_num);
            jbLot.put("lot_count", lot_left_count);
            jbCarr.put("lot", jbLot);
            JSONObject pnl_infos = new JSONObject();
            JSONArray jaSlot = new JSONArray();
            //查询明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(100).iterator();
            Boolean isManualOutQtyZero=true;
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String panel_barcode = docItemBigData.getString("panel_barcode");
                Integer panel_index = docItemBigData.getInteger("panel_index");
                String manual_judge_code= docItemBigData.getString("manual_judge_code");
                if("1".equals(manual_judge_code)) isManualOutQtyZero=false;
                JSONObject jbDetail = new JSONObject();
                jbDetail.put("slot_no", String.format("%03d", panel_index));
                jbDetail.put("panel_id", panel_barcode);
                jaSlot.add(jbDetail);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            pnl_infos.put("slot", jaSlot);
            jbCarr.put("pnl_infos", pnl_infos);
            carr_infos.add(jbCarr);

            //判断是否发送WIP数据
            Boolean isSendWip = true;
            if (OneCarMultyLotFlag.equals("2")) {
                Integer out_code_int = Integer.parseInt(out_code);
                if (out_code_int > 0 && out_code_int < 4) {
                    if (sum_plan_count > sum_finish_ok_count) isSendWip = false;
                }
            }

            //判断outQty是否=0
            if(!isNewVersion && isManualOutQtyZero && manual_wip_flag.equals("Y") && station_attr.equals("UnLoad")){
                lot_finish_count=0;
            }

            //上报WIP【异步】
            if (isSendWip) {
                if("Y".equals(DySmallPanelFlag) && station_attr.equals("Load")){
                    //lot_finish_count默认等于first_plan_count_P004
                    eapDySendFlowFunc.WIPTrackingReport(Long.parseLong(station_id), station_code, user_name, dept_id, shift_id,
                            task_start_time, task_end_time, lot_num, sum_plan_count, first_plan_count_P004, first_plan_count_P004,
                            material_code_P001, lot_short_num, lot_version_P002, prod_mode, attribute1_P005, attribute2_P006,
                            item_attr_list, offline_flag, local_flag, attr_else);
                }
                else{
                    eapDySendFlowFunc.WIPTrackingReport(Long.parseLong(station_id), station_code, user_name, dept_id, shift_id,
                            task_start_time, task_end_time, lot_num, sum_plan_count, first_plan_count_P004, lot_finish_count,
                            material_code_P001, lot_short_num, lot_version_P002, prod_mode, attribute1_P005, attribute2_P006,
                            item_attr_list, offline_flag, local_flag, attr_else);
                }
                //判断是否存在NoRead数量,若存在则等待认为确认补充
                if(wip_short_count>0 && station_attr.equals("UnLoad") &&
                        !manual_wip_flag.equals("Y") && lot_finish_count2>0){
                    if(PanelModel.equals("1") && NgPanelPassFlag.equals("0")){
                        queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                        queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                        queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                        Update updateBigData = new Update();
                        updateBigData.set("manual_wip_flag", "Y");
                        updateBigData.set("wip_noread_count", wip_noread_count);
                        updateBigData.set("wip_short_count", wip_short_count);
                        mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                    }
                }
                //删除镭射数据
                if(station_attr.equals("Load")){
                    mongoTemplate.dropCollection("a_eap_me_laser");
                }
                else{
                    String meLaserTable="a_eap_me_laser";
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    mongoTemplate.remove(queryBigData,meLaserTable);
                }
            }
            //上报DataUpLoad【异步】
            eapDySendFlowFunc.CarrierDataUploadReport(Long.parseLong(station_id), station_code, pallet_num, "0", splitseq, lastSplit, carr_infos, offline_flag, local_flag);

            //若是小板完板则需要将finish_count变更为plan_count
            if("Y".equals(DySmallPanelFlag) && station_attr.equals("Load")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                Update updateBigData = new Update();
                updateBigData.set("finish_count", first_plan_count_P004);
                updateBigData.set("finish_ok_count", first_plan_count_P004);
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            try {
                if (iteratorBigData != null) {
                    if (iteratorBigData.hasNext()) iteratorBigData.close();
                }
            } catch (Exception exx) {
            }
            errorMsg = "EAP子批次完板上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //8.通知下游批次结束
    @RequestMapping(value = "/DownDeviceSubLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyDownDeviceSubLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/DownDeviceSubLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String panel_result = jsonParas.getString("panel_result");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String[] panelListParas = panel_result.split(",", -1);
            String station_flow_id = panelListParas[0];
            String lot_num = panelListParas[1];
            String lot_status = panelListParas[8];
            String group_lot_num = panelListParas[9];

            //根据批次查询信息
            Integer lot_finish_count = 0;
            String lot_short_num = "";

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lot_finish_count = docItemBigData.getInteger("finish_ok_count");
                if (station_attr.equals("UnLoad")) {
                    pallet_num = docItemBigData.getString("pallet_num");
                }
                iteratorBigData.close();
            }

            //通知下游结批信号
            eapDySendFlowFunc.LotFinishDownLoad(station_code, lot_num, lot_finish_count, lot_short_num,port_code);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "通知下游批次结束异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //9.EAP综合母批完板上报
    @RequestMapping(value = "/GroupLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyGroupLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/GroupLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String prod_mode = jsonParas.getString("prod_mode");
            String nw_value = jsonParas.getString("nw_value");//内外层代码
            String lot_list = jsonParas.getString("lot_list");
            String out_code = jsonParas.getString("out_code");
            String local_flag = "Y";
            if (sys_model.equals("1")) local_flag = "N";
            if (out_code == null || out_code.equals("")) out_code = "-1";
            //针对lot_list进行处理
            JSONArray lotArray = JSONArray.parseArray(lot_list);
            if (lotArray != null && lotArray.size() > 0) {
                for (int i = 0; i < lotArray.size(); i++) {
                    JSONObject jbItem = lotArray.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    String lot_num = jbItem.getString("lot_num");
                    String pallet_num = jbItem.getString("pallet_num");
                    Integer plan_lot_count = jbItem.getInteger("plan_lot_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    Integer finish_count = jbItem.getInteger("finish_count");
                    JSONObject jsonParas2 = jsonParas;
                    jsonParas2.put("wip_flag","Y");
                    String panel_result = "," + lot_num + "," + pallet_num + ",0,,0,0,,WORK," + group_lot_num;
                    jsonParas2.put("panel_result", panel_result);
                    jsonParas2.put("pallet_num", pallet_num);
                    EapDySubLotFinishReport(jsonParas2, request);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "EAP综合母批完板上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //10.母批完板通知下游批次结束
    @RequestMapping(value = "/DownDeviceGroupLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyDownDeviceGroupLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/DownDeviceGroupLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        try {
            JSONObject jsonParas2 = jsonParas;
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String lot_list = jsonParas.getString("lot_list");
            String local_flag = "Y";
            if (sys_model.equals("1")) local_flag = "N";
            //针对lot_list进行处理
            JSONArray lotArray = JSONArray.parseArray(lot_list);
            if (lotArray != null && lotArray.size() > 0) {
                for (int i = 0; i < lotArray.size(); i++) {
                    JSONObject jbItem = lotArray.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    String lot_num = jbItem.getString("lot_num");
                    String pallet_num = jbItem.getString("pallet_num");
                    Integer plan_lot_count = jbItem.getInteger("plan_lot_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    if (plan_lot_count > finish_ok_count) {
                        //说明未完成工单,需要执行WIP动作
                        String panel_result = "," + lot_num + "," + pallet_num + ",0,,0,0,,WORK," + group_lot_num;
                        jsonParas2.put("panel_result", panel_result);
                        jsonParas2.put("pallet_num", pallet_num);
                        EapDyDownDeviceSubLotFinishReport(jsonParas2, request);
                    }
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "母批完板通知下游批次结束异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //暂存每片上报
    @RequestMapping(value = "/EachPositionReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyEachPositionReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/EachPositionReport";
        String transResult = "";
        String errorMsg = "";
        try {
            JSONObject jsonParas2 = jsonParas;
            String station_code = jsonParas.getString("station_code");
            JSONArray jaList = jsonParas.getJSONArray("panel_list");
            eapDySendFlowFunc.EachPositionReport(station_code, jaList);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "暂存每片上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //AGV与MGV模式切换时请求
    @RequestMapping(value = "/DockingModeChangeRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyDockingModeChangeRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/DockingModeChangeRequest";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String dock_mode = jsonParas.getString("dock_mode");
            eapDySendFlowFunc.DockingModeChangeRequest(station_code, port_code, dock_mode);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AGV与MGV模式切换时请求异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //投收扳机上下载具任务
    @RequestMapping(value = "/PortRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyPortRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/PortRequest";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String job_type = jsonParas.getString("job_type");
            String pallet_num = jsonParas.getString("pallet_num");
            eapDySendFlowFunc.PortRequest(station_code, port_code, station_attr, job_type, pallet_num);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "投收扳机上下载具任务异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //暂存机片数删除
    @RequestMapping(value = "/EachPositionDelReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyEachPositionDelReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/EachPositionDelReport";
        String transResult = "";
        String errorMsg = "";
        String eachTableName="a_eap_me_eachposition";
        try {
            String station_attr=jsonParas.getString("station_attr");
            String sqlStation="select station_id," +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_attr='"+station_attr+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlStation,false,request,apiRoutePath);
            String station_code=itemListStation.get(0).get("station_code").toString();
            //
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(eachTableName).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            String each_position_id="";
            if (iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                each_position_id=docItemBigData.getString("each_position_id");
                iteratorBigData.close();
            }
            if(!each_position_id.equals("")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("each_position_id").is(each_position_id));
                mongoTemplate.remove(queryBigData, eachTableName);
            }
            //再查询出来
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            List<Map> panelList = mongoTemplate.find(queryBigData, Map.class, eachTableName);
            JSONArray jaList=new JSONArray();
            if(panelList!=null && panelList.size()>0){
                for(int i=0;i<panelList.size();i++){
                    Map map=panelList.get(i);
                    String slot_no=String.format("%03d", i+1);
                    String temporary_storage_reason=map.get("zc_code").toString();
                    String pnl_id=map.get("panel_barcode").toString();
                    JSONObject jbItem=new JSONObject();
                    jbItem.put("slot_no",slot_no);
                    jbItem.put("temporary_storage_reason",temporary_storage_reason);
                    jbItem.put("pnl_id",pnl_id);
                    jaList.add(jbItem);
                }
            }
            eapDySendFlowFunc.EachPositionReport(station_code, jaList);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "暂存每片上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
