package com.api.mes.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 1.从mes获取电芯原始数据
 * 2.保存电芯原始数据到c_mes_me_dx_original表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-18
 */
@RestController
@Slf4j
@RequestMapping("/mes/interf/core")
public class MesCoreInterfOriginalDxController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //1.从MES查询电芯原始数据
    @RequestMapping(value = "/MesInterfCoreOriginalDxSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesInterfCoreOriginalDxSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/interf/core/MesInterfCoreOriginalDxSelect";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = selectOriginalDxData(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        log.debug("MesInterfCoreOriginalDxSelect:{}", responseParas);
        //记录日志
        cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        return responseParas;
    }

    //查询电芯原始数据
    private JSONObject selectOriginalDxData(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "MesInterfCoreOriginalDxSelect";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject jbResponse = null;
        JSONObject jsonObjectReq = null;
        try {
            int prod_line_id = jsonParas.getInteger("prod_line_id");
            String dx_barcode = jsonParas.getString("dx_barcode");
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url," + "COALESCE(esb_interf_des,'') esb_interf_des " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //2.调用接口
            jsonObjectReq = new JSONObject();
            jsonObjectReq.put("tenantID", prod_line_id);
            jsonObjectReq.put("dxCode", dx_barcode);
            requestParas = jsonObjectReq.toString();
            JSONObject jsonObjectMes = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, jsonObjectReq);

            responseParas = jsonObjectMes.toString();
            String code = jsonObjectMes.getString("code");
            String backMsg = jsonObjectMes.getString("errorMsg");
            if (!code.equals("200")) {
                errorMsg = esbInterfCode + "接口返回失败：" + backMsg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //上传成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "获取成功");


        } catch (Exception ex) {
            errorMsg = "MES获取电芯基础数据接口发生未知异常:" + ex.getMessage();
            log.error("error:{},request:{}", errorMsg, jsonObjectReq);
            jbResponse = new JSONObject();
            jbResponse.put("response_time", CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
            jbResponse.put("response_attr", "");
            jbResponse.put("code", -99);
            jbResponse.put("msg", errorMsg);
            jbResult.put("requestParas", requestParas);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }


    //2.保存电芯原始数据
    @Transactional
    @RequestMapping(value = "/MesCoreOriginalDxSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreOriginalDxSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/interf/core/MesCoreOriginalDxSave";
        String tranResult = "";
        String errorMsg = "";
        String dxOriginalTable = "c_mes_me_dx_original";
        try {
            String dx_barcode = jsonParas.getString("dx_barcode");
            String dx_data = jsonParas.getJSONObject("dx_data").toJSONString();
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
            Query query = new Query();
            long allCount = mongoTemplate.getCollection(dxOriginalTable).countDocuments(query.getQueryObject());
            Integer dx_index = (int) allCount + 1;
            JSONObject dxObject = JSONObject.parseObject(dx_data);
            String dx_ori_gear = dxObject.getString("RFD001");//电芯档位
            String dx_ori_capacity = dxObject.getString("RFR0008");//电芯容量4
            String dx_ori_ocv4_pressure = dxObject.getString("ROCV40008");//电芯V4电压
            String dx_ori_ocr4_air = dxObject.getString("ROCV40009");//电芯V4电阻
            String dx_ori_k_value = dxObject.getString("ROCV40011");//电芯K值
            String dx_ori_thickness = dxObject.getString("RBJ015");//电芯厚度
            String dx_ori_ocv4_time = dxObject.getString("ROCV40007");//0CV4时间
            String dx_ori_ocv5_pressure = dxObject.getString("RBJ002"); //电芯V5电压
            String dx_ori_glue_pressure = dxObject.getString("RBJ003"); //包胶壳压
            String dx_ori_dcir = dxObject.getString("RBJ004");//电芯V5内阻
            String dx_ori_technology = "";
            if ("JZGX".equals(projectCode)) {
                dx_ori_technology = dxObject.getString("RFR012");//分容预测工艺
            }
            String data_export_by = dxObject.getString("6000002");//租户ID
            //金寨
            //容量
            if (dxObject.containsKey("RFR005")) {
                dx_ori_capacity = dxObject.getString("RFR005");
            }
            //电压
            if (dxObject.containsKey("ROCV4002")) {
                dx_ori_ocv4_pressure = dxObject.getString("ROCV4002");
            }
            //电阻
            if (dxObject.containsKey("ROCV4004")) {
                dx_ori_ocr4_air = dxObject.getString("ROCV4004");
            }
            //k值
            if (dxObject.containsKey("ROCV4005")) {
                dx_ori_k_value = dxObject.getString("ROCV4005");
            }
            if (dxObject.containsKey("ROCV4007")) {
                dx_ori_ocv4_time = dxObject.getString("ROCV4007");
            }
            //唐山国轩0727改造
            if (dxObject.containsKey("TOTAL_END_CAP")) {
                dx_ori_capacity = dxObject.getString("TOTAL_END_CAP");
            }
            if (dxObject.containsKey("OCV4")) {
                dx_ori_ocv4_pressure = dxObject.getString("OCV4");
            }
            if (dxObject.containsKey("OCR4")) {
                dx_ori_ocr4_air = dxObject.getString("OCR4");
            }
            if (dxObject.containsKey("K_VALUE")) {
                dx_ori_k_value = dxObject.getString("K_VALUE");
            }
            if (dxObject.containsKey("TEST_TIME")) {
                dx_ori_ocv4_time = dxObject.getString("TEST_TIME");
            }
            if (dxObject.containsKey("C_THI")) {
                dx_ori_thickness = dxObject.getString("C_THI");
            }
            if (dxObject.containsKey("RFR012") && "JZGX".equals(projectCode)) {
                dx_ori_technology = dxObject.getString("RFR012");
            }

            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("dx_original_id", CFuncUtilsSystem.CreateUUID(true));
            mapBigDataRow.put("data_from", "GXMES");
            mapBigDataRow.put("data_export_by", data_export_by);
            mapBigDataRow.put("dx_barcode", dx_barcode);
            mapBigDataRow.put("dx_gear", dx_ori_gear == null ? 0 : Integer.parseInt(dx_ori_gear));//档位
            mapBigDataRow.put("dx_ori_batch", dx_barcode.substring(12,17));//批次号  13~17位
            mapBigDataRow.put("dx_ori_capacity", dx_ori_capacity == null ? 0d : Math.abs(Double.parseDouble(dx_ori_capacity)));//容量
            mapBigDataRow.put("dx_ori_ocv4_pressure", dx_ori_ocv4_pressure == null ? 0d : Double.parseDouble(dx_ori_ocv4_pressure));//电压
            mapBigDataRow.put("dx_ori_ocr4_air", dx_ori_ocr4_air == null ? 0d : Double.parseDouble(dx_ori_ocr4_air));//内阻
            mapBigDataRow.put("dx_ori_k_value", dx_ori_k_value == null ? 0d : Double.parseDouble(dx_ori_k_value));//K值
            mapBigDataRow.put("dx_ori_dcir", dx_ori_dcir == null ? 0d : Double.parseDouble(dx_ori_dcir));//Dcir内阻-电芯V5内阻
            mapBigDataRow.put("dx_ori_thickness", dx_ori_thickness == null ? 0d : Double.parseDouble(dx_ori_thickness));//厚度
            mapBigDataRow.put("dx_ori_ocv4_time", dx_ori_ocv4_time);//ocv4时间
            mapBigDataRow.put("dx_ori_ocv5_pressure", dx_ori_ocv5_pressure == null ? 0d : Double.parseDouble(dx_ori_ocv5_pressure));//电芯V5电压
            mapBigDataRow.put("dx_ori_glue_pressure", dx_ori_glue_pressure == null ? 0d : Double.parseDouble(dx_ori_glue_pressure));//包胶壳压
            if ("JZGX".equals(projectCode)) {
                mapBigDataRow.put("dx_ori_technology", dx_ori_technology);//分容预测工艺
            }
            lstDocuments.add(mapBigDataRow);
            if (lstDocuments.size() > 0) {
                mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED, dxOriginalTable).insert(lstDocuments).execute();
            }
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, String.valueOf(dx_index), "", 0);
        } catch (Exception ex) {
            errorMsg = "保存电芯原始数据发生异常" + ex.getMessage();
            tranResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }


}
