package com.api.dcs.core.interf.fj;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 分拣接受流程对外接口
 * 1.接受分拣解析结果
 * 2.分拣即时结果上报
 * 3.接受分拣完成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/interf/fj/recv")
public class DcsCoreFjRecvController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private DcsCoreFjRecvFunc dcsCoreFjRecvFunc;
    @Autowired
    private DcsInterfCommon dcsInterfCommon;

    //1.接受分拣解析结果
    @RequestMapping(value = "/DcsCoreFjRecvTaskResolve", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreFjRecvTaskResolve(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/interf/fj/recv/DcsCoreFjRecvTaskResolve";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsCoreFjRecvFunc.FjRecvTaskResolve(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String moID = jbResult.getString("moID");
        Integer code = jbResult.getInteger("code");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        String resolve_start_time = jbResult.getString("resolve_start_time");
        String resolve_end_time = jbResult.getString("resolve_end_time");
        String startDate2 = startDate;
        String endDate2 = endDate;
        if (resolve_start_time != null && !resolve_start_time.equals("") && resolve_end_time != null && !resolve_end_time.equals("")) {
            startDate2 = resolve_start_time;
            endDate2 = resolve_end_time;
        }
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
            //记录任务事件
            if (moID != null && !moID.equals("")) {
                dcsInterfCommon.InsertApsTaskEvent(moID, token, esbInterfCode, "接受分拣解析结果", responseParas, requestParas, code, message, successFlag, startDate2, endDate2, "");
            }
        }
        return responseParas;
    }

    //2.分拣即时结果上报
    @RequestMapping(value = "/DcsCoreFjRecvProcResult", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreFjRecvProcResult(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/interf/fj/recv/DcsCoreFjRecvProcResult";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsCoreFjRecvFunc.FjRecvProcResult(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //3.接受分拣完成
    @RequestMapping(value = "/DcsCoreFjRecvTaskFinish", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreFjRecvTaskFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/interf/fj/recv/DcsCoreFjRecvTaskFinish";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsCoreFjRecvFunc.FjRecvTaskFinish(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String moID = jbResult.getString("moID");
        Integer code = jbResult.getInteger("code");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
            //记录任务事件
            if (moID != null && !moID.equals("")) {
                dcsInterfCommon.InsertApsTaskEvent(moID, token, esbInterfCode, "接受分拣完成", responseParas, requestParas, code, message, successFlag, startDate, endDate, "");
            }
        }
        return responseParas;
    }
}
