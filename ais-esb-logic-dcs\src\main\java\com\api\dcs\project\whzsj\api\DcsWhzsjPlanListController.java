package com.api.dcs.project.whzsj.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 到货清单逻辑
 * 1.根据钢板唯一识别码查询到货清单
 *
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/whzsj/wms")
public class DcsWhzsjPlanListController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.根据钢板唯一识别码查询到货清单
    @RequestMapping(value = "/DcsWhzsjPlanListBySerialNumSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsWhzsjPlanListBySerialNumSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/whzsj/wms/DcsWhzsjPlanListBySerialNumSel";
        String selectResult="";
        String errorMsg="";
        String userID="-1";
        try{
            //1.获取参数
            String nowDateTime=CFuncUtilsSystem.GetNowDateTime("");
            userID=jsonParas.getString("userID");
            String serial_num=jsonParas.getString("serial_num");//序列号(钢板唯一识别码)
            String plan_status=jsonParas.getString("plan_status");//计划状态(PLAN、WORK、FINISH、CANCEL)

            //2.查询到货清单
            String sqlLockCarRouteSel="select " +
                    "b.list_num," +
                    "COALESCE(a.project_code,'') project_code," +
                    "COALESCE(a.block_code,'') block_code," +
                    "COALESCE(a.lot_num,'') lot_num," +
                    "COALESCE(a.serial_num,'') serial_num," +
                    "COALESCE(a.steel_seal_code,'') steel_seal_code," +
                    "COALESCE(a.material_code,'') material_code," +
                    "COALESCE(a.m_texture,'') m_texture," +
                    "COALESCE(a.ship_class,'') ship_class," +
                    "COALESCE(a.model_type,'') model_type," +
                    "COALESCE(a.m_length,0) m_length," +
                    "COALESCE(a.m_width,0) m_width," +
                    "COALESCE(a.m_height,0) m_height," +
                    "COALESCE(a.m_weight,0) m_weight" +
                    "from b_dcs_wms_plan_list_d a," +
                    "     b_dcs_wms_plan_list b " +
                    "where a.plan_list_id=b.plan_list_id " +
                    "and b.enable_flag='Y' " +
                    "and b.plan_status='"+plan_status+"' " +
                    "and a.serial_num='"+serial_num+"' " +
                    "order by a.plan_list_d_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(userID,sqlLockCarRouteSel,
                    false,request,apiRoutePath);
            if(itemList!=null && itemList.size()>0){
                //String task_id=itemList.get(0).get("task_id").toString();
            }
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "根据钢板唯一识别码查询到货清单查询异常:"+ex.getMessage();
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
