package com.api.eap.project.thailand.guanghe;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * (泰国广合)EAP发送事件功能函数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class EapTlGhSendEventFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private EapTlGhInterfCommon eapTlGhInterfCommon;
    @Autowired
    private EapTlGhSendEventSubFunc eapTlGhSendEventSubFunc;
}
