package com.api.pmc.core.screen;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 大屏设置IFRMAE接口
 * 1.大屏IFRMAE信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/screen")
public class PmcCoreScreenController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    //1.大屏IFRMAE信息
    @RequestMapping(value = "/PmcCoreScreenSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreScreenSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/screen/PmcCoreScreenSel";
        String selectResult="";
        String errorMsg="";
        try{
            String screen_set_code=jsonParas.getString("screen_set_code");//大屏编码
            //获取大屏
            String sqlScreen="select COALESCE(screen_set_type,'') screen_set_type," +
                    "COALESCE(screen_set_code,'') screen_set_code," +
                    "COALESCE(screen_set_des,'') screen_set_des," +
                    "COALESCE(screen_set_url,'') screen_set_url," +
                    "COALESCE(screen_set_url_des,'') screen_set_url_des," +
                    "COALESCE(order_num,0) order_num," +
                    "COALESCE(switch_time,-1) switch_time " +
                    "from d_pmc_fmod_screen_set " +
                    "where enable_flag='Y' " +
                    "and screen_set_code='"+screen_set_code+ "' " +
                    "order by order_num ";
            List<Map<String, Object>> itemListScreen=cFuncDbSqlExecute.ExecSelectSql(screen_set_code,sqlScreen,false,request,apiRoutePath);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemListScreen,"","",0);
        }
        catch (Exception ex){
            errorMsg= "大屏IFRMAE信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}

