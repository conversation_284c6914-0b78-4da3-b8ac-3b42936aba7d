package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定颖EAP发送事件功能函数
 * 1.UserVerify:用户登入登出验证
 * 2.UserLoginRequest:人员登入登出事件
 * 3.EQPInfoVerify:检测与EAP通讯并且返回EAP时间由应用进行更新本地时间
 * 4.AlarmReport:上报报警信息到EAP
 * 5.RealStatusReport:设备状态上报到EAP
 * 6.StatusChangeReport:只要报警与状态发生任何变化都需要进行上报到EAP
 * 7.ParamVerify:查询工单信息
 * 8.UtilityReport:上报三率
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapCjMasterSendEventFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private EapCjMasterSendEventSubFunc eapCjMasterSendEventSubFunc;

    //1.用户登入登出验证
    public JSONObject UserVerify(Boolean checkOut,String station_code,String user_id) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapCjMasterSendEventSubFunc.UserVerify(checkOut,station_code,user_id);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
        JSONObject jbUserInfo=jbResult.getJSONObject("responseBody");
        return jbUserInfo;
    }

    //2.人员登入登出事件
    @Async
    public void UserLoginRequest(Boolean checkOut,String station_code,String user_id,String dept_id,
                                       String shift_id,String nick_name) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapCjMasterSendEventSubFunc.UserLoginRequest(checkOut,station_code,user_id,dept_id,shift_id,nick_name);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //3.检测与EAP通讯并且返回EAP时间由应用进行更新本地时间
    public String EQPInfoVerify(String station_code) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapCjMasterSendEventSubFunc.EQPInfoVerify(station_code);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
        String nowDateTime=jbResult.getJSONObject("responseBody").getString("now");
        return nowDateTime;
    }

    //4.上报报警信息到EAP
    @Async
    public void AlarmReport(Long station_id,String station_code,String reset_flag,String warn_flag,
                                String alarm_id,String alarm_desc,String local_flag,String status_code,
                                String offline_flag) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapCjMasterSendEventSubFunc.AlarmReport(station_id,station_code,reset_flag,warn_flag,
                alarm_id,alarm_desc,local_flag,status_code,offline_flag);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //5.设备状态上报到EAP
    @Async
    public void RealStatusReport(String station_code,String local_flag,String auto_flag,
                                 String stop_flag,String repair_flag,String hold_flag,
                                 String status_code,String reset_flag,String warn_flag,String alarm_id,
                                 String short_bettery_flag,String power_on_time,String idle_delay_time,
                                 String current_lot_count,String history_lot_count,String red_light_flag,
                                 String yellow_light_flag,String green_light_flag,String blue_light_flag,
                                 String user_name,String lot_num,String eap_offline_flag,String plc_offline_flag,
                                 String prod_mode,String alarm_desc,String offline_flag) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");

        JSONObject jbResult= eapCjMasterSendEventSubFunc.RealStatusReport(station_code,local_flag,auto_flag,
                stop_flag,repair_flag,hold_flag,status_code,reset_flag,warn_flag,alarm_id,
                short_bettery_flag,power_on_time,idle_delay_time,current_lot_count,history_lot_count,red_light_flag,
                yellow_light_flag,green_light_flag,blue_light_flag,user_name,lot_num,eap_offline_flag,plc_offline_flag,
                prod_mode,alarm_desc,offline_flag);

        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //6.只要报警与状态发生任何变化都需要进行上报到EAP
    @Async
    public void StatusChangeReport(Long station_id,String station_code,String auto_flag,
                                   String status_code,String alarm_id,String local_flag,
                                   String alarm_desc,String offline_flag) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapCjMasterSendEventSubFunc.StatusChangeReport(station_id,station_code,auto_flag,status_code,
                alarm_id,local_flag,alarm_desc,offline_flag);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //7.查询工单信息
    public List<Map<String, Object>> ParamVerify(String station_code,String production_mode,String lot_num) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapCjMasterSendEventSubFunc.ParamVerify(station_code,production_mode,lot_num);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
        JSONObject jbLotInfo=jbResult.getJSONObject("responseBody");
        List<Map<String, Object>> itemList=new ArrayList<>();
        String lot_id=jbLotInfo.getString("lot_id");
        String process_code=jbLotInfo.getString("process_code");
        String use_in_name=jbLotInfo.getString("use_in_name");
        String lot_short_id=jbLotInfo.getString("lot_short_id");
        String pnl_count=jbLotInfo.getString("pnl_count");
        String prod_ver=jbLotInfo.getString("prod_ver");
        JSONObject lot_infos=jbLotInfo.getJSONObject("lot_infos");
        Map<String, Object> map=new HashMap<>();
        map.put("lot_id",lot_id);
        map.put("process_code",process_code);
        map.put("use_in_name",use_in_name);
        map.put("lot_short_id",lot_short_id);
        map.put("pnl_count",pnl_count);
        map.put("prod_ver",prod_ver);
        map.put("lot_infos",lot_infos);
        itemList.add(map);
        return itemList;
    }

    //8.三率上报
    public void UtilityReport(Long station_id, String station_code, String shfit_id, JSONArray item_list,
                              String report_dt,String offline_flag, String local_flag) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapCjMasterSendEventSubFunc.UtilityReport(station_id,station_code,shfit_id,item_list,report_dt,offline_flag,local_flag);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }

    //CimMode模式变化后事件上报
    public String CimModeChangeReport(Long station_id,String station_code,String cim_model,String user_id) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapCjMasterSendEventSubFunc.CimModeChangeReport(station_id,station_code,cim_model,user_id);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        String cimPassword=jbResult.containsKey("cimPassword") ? jbResult.getString("cimPassword") : "";
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
        return cimPassword;
    }


}
