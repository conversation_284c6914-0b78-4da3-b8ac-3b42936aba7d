package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 载具转换机端口2数据存储
 * 1.放板机Panel校验
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@Service
@Slf4j
public class EapDyZjZhLoadPlanFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private EapDySendEventFunc eapDySendEventFunc;
    @Autowired EapDySendFlowFunc eapDySendFlowFunc;

    //载具转换端口2存储
    @Async
    public void EapDyZjZhLoadPlanPanelCheckAndSave(JSONObject jsonParas) throws Exception{
        String apsPlanTable="a_eap_aps_plan";
        String meStationFlowTable="a_eap_me_station_flow";
        try{
            String station_id=jsonParas.getString("station_id");
            String station_code=jsonParas.getString("station_code");
            String group_lot_num=jsonParas.getString("group_lot_num");
            String panel_barcode=jsonParas.getString("panel_barcode");
            String tray_barcode=jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value=jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value=jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value=jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value=jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批,2为一批多车
            String inspect_flag=jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag=jsonParas.getString("dummy_flag");//是否为Dummy板
            String face_code=jsonParas.getString("face_code");//存储A面B面,1:A,2:B
            Integer panel_ng_code=jsonParas.getInteger("panel_ng_code");
            String panel_ng_msg=jsonParas.getString("panel_ng_msg");
            String panel_status=jsonParas.getString("panel_status");
            String user_name=jsonParas.getString("user_name");
            String ccd_no=jsonParas.getString("ccd_no");

            String panel_flag="N";
            if(group_lot_num==null) group_lot_num="";
            if(panel_barcode==null) panel_barcode="";
            if(tray_barcode==null) tray_barcode="";
            if(ng_auto_pass_value==null) ng_auto_pass_value="";
            if(ng_manual_pass_value==null) ng_manual_pass_value="";
            if(panel_model_value==null) panel_model_value="";
            if(panel_model_value.equals("1")) panel_flag="Y";
            if(onecar_multybatch_value==null) onecar_multybatch_value="";
            if(inspect_flag==null || inspect_flag.equals("")) inspect_flag="N";
            if(dummy_flag==null || dummy_flag.equals("")) dummy_flag="N";
            if(face_code==null || face_code.equals("")) face_code="0";
            if(panel_ng_code==null) panel_ng_code=0;
            if(panel_ng_msg==null) panel_ng_msg="";
            if(panel_status==null || panel_status.equals("")) panel_status="OK";
            if(user_name==null) user_name="";
            if(ccd_no==null || ccd_no.equals("")) ccd_no="01";
            long station_id_long=Long.parseLong(station_id);
            Integer face_code_int=Integer.parseInt(face_code);
            group_lot_num=group_lot_num+"-02";

            //查询任务信息
            String plan_id="";
            String task_from="";
            String lot_num="";
            String lot_short_num="";
            Integer lot_index=1;
            String port_code="02";
            String material_code="";
            String pallet_num="";
            String pallet_type="";
            String lot_level="";
            Integer fp_index=0;
            Double panel_length=0d;
            Double panel_width=0d;
            Double panel_tickness=0d;
            Integer plan_lot_count=0;
            Integer finish_count=0;
            Integer finish_ok_count=0;
            Integer finish_ng_count=0;
            Integer panel_index=0;
            String virtu_pallet_num="";

            String[] lot_status_list=new String[]{"PLAN","WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(!iteratorBigData.hasNext()){
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                plan_id=docItemBigData.getString("plan_id");
                task_from=docItemBigData.getString("task_from");
                lot_num=docItemBigData.getString("lot_num");
                lot_short_num=docItemBigData.getString("lot_short_num");
                lot_index=docItemBigData.getInteger("lot_index");
                port_code=docItemBigData.getString("port_code");
                material_code=docItemBigData.getString("material_code");
                pallet_num=docItemBigData.getString("pallet_num");
                pallet_type=docItemBigData.getString("pallet_type");
                lot_level=docItemBigData.getString("lot_level");
                panel_length=docItemBigData.getDouble("panel_length");
                panel_width=docItemBigData.getDouble("panel_width");
                panel_tickness=docItemBigData.getDouble("panel_tickness");
                plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                finish_count=docItemBigData.getInteger("finish_count");
                finish_ok_count=docItemBigData.getInteger("finish_ok_count");
                finish_ng_count=docItemBigData.getInteger("finish_ng_count");
                virtu_pallet_num=pallet_num;
                if(docItemBigData.containsKey("virtu_pallet_num")) virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                iteratorBigData.close();
            }
            panel_index=finish_ok_count+1;
            String slot_no = String.format("%03d", panel_index);
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id=CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("station_flow_id",station_flow_id);
            mapBigDataRow.put("station_id",station_id_long);
            mapBigDataRow.put("plan_id",plan_id);
            mapBigDataRow.put("task_from",task_from);
            mapBigDataRow.put("group_lot_num",group_lot_num);
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("lot_short_num",lot_short_num);
            mapBigDataRow.put("lot_index",lot_index);
            mapBigDataRow.put("port_code",port_code);
            mapBigDataRow.put("material_code",material_code);
            mapBigDataRow.put("pallet_num",pallet_num);
            mapBigDataRow.put("pallet_type",pallet_type);
            mapBigDataRow.put("lot_level",lot_level);
            mapBigDataRow.put("fp_index",fp_index);
            mapBigDataRow.put("panel_barcode",panel_barcode);
            mapBigDataRow.put("panel_length",panel_length);
            mapBigDataRow.put("panel_width",panel_width);
            mapBigDataRow.put("panel_tickness",panel_tickness);
            mapBigDataRow.put("panel_index",panel_index);
            mapBigDataRow.put("panel_status",panel_status);
            mapBigDataRow.put("panel_ng_code",panel_ng_code);
            mapBigDataRow.put("panel_ng_msg",panel_ng_msg);
            mapBigDataRow.put("inspect_flag",inspect_flag);
            mapBigDataRow.put("dummy_flag",dummy_flag);
            mapBigDataRow.put("manual_judge_code","0");
            mapBigDataRow.put("panel_flag",panel_flag);
            mapBigDataRow.put("user_name",user_name);
            mapBigDataRow.put("eap_flag","N");
            mapBigDataRow.put("tray_barcode",tray_barcode);
            mapBigDataRow.put("face_code",face_code_int);
            mapBigDataRow.put("offline_flag","N");
            mapBigDataRow.put("virtu_pallet_num",virtu_pallet_num);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow,meStationFlowTable);
            //更新完工数量
            finish_count++;
            if(panel_status.equals("NG"))  finish_ng_count++;
            else finish_ok_count++;
            String lot_status="WORK";
            if(finish_ok_count>=plan_lot_count) lot_status="FINISH";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if(finish_count==1){
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if(lot_status.equals("FINISH")){
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if(!"Y".equals(dummy_flag)){
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //执行JobCount以及StoreIn等事件
            //1.查询累计数据
            if(!"Y".equals(dummy_flag)){
                Integer sum_finish_ok_count=0;
                JSONArray lot_list = new JSONArray();
                Boolean is_exist_virtu_pallet=false;
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    sum_finish_ok_count += docItemBigData.getInteger("finish_ok_count");
                    JSONObject jbItem = new JSONObject();
                    jbItem.put("lot_id", docItemBigData.getString("lot_num"));
                    jbItem.put("lot_count", docItemBigData.getInteger("finish_ok_count"));
                    lot_list.add(jbItem);
                    virtu_pallet_num=pallet_num;
                    if(docItemBigData.containsKey("virtu_pallet_num")){
                        is_exist_virtu_pallet=true;
                        virtu_pallet_num=docItemBigData.getString("virtu_pallet_num");
                    }
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
                Integer left_count = sum_finish_ok_count;
                //【必须】若是第一片则必须上报PROC和开始投收板事件
                if (sum_finish_ok_count == 1) {
                    if(panel_ng_code==0 || panel_ng_code==4 || panel_status.equals("OK") || panel_status.equals("NG_PASS")){
                        if(panel_ng_code!=11 && panel_ng_code!=30 && panel_ng_code!=8){
                            eapDySendFlowFunc.ZjZhPortStatusChangeReportAsync(station_code, port_code, "Load", "PROC", "N", pallet_num, String.valueOf(left_count), user_name, "N", "N","ECL","UnLoad");
                            eapDySendFlowFunc.ZjZhCarrierStatusReportAsync(station_code, pallet_num, "Process", lot_list, "0", "Load", port_code,"N");
                        }
                    }
                } else {
                    //若是一批多车,需要判断是否第一次载具码,若是也需要进行PROC|Process上报
                    if (onecar_multybatch_value.equals("2")) {
                        if(panel_ng_code==0 || panel_ng_code==4 || panel_status.equals("OK") || panel_status.equals("NG_PASS")){
                            if(panel_ng_code!=11 && panel_ng_code!=30 && panel_ng_code!=8){
                                long pnListCount=0l;
                                String[] panel_status_list=new String[]{"OK","NG_PASS"};
                                queryBigData = new Query();
                                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                                queryBigData.addCriteria(Criteria.where("panel_status").in(panel_status_list));
                                if(is_exist_virtu_pallet && !"".equals(virtu_pallet_num)){
                                    queryBigData.addCriteria(Criteria.where("virtu_pallet_num").is(virtu_pallet_num));
                                    pnListCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                                }
                                else{
                                    if (pallet_num != null && !pallet_num.equals("") && !pallet_num.equals("NoRead")){
                                        queryBigData.addCriteria(Criteria.where("pallet_num").is(pallet_num));
                                        pnListCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                                    }
                                }
                                if (pnListCount == 1) {
                                    eapDySendFlowFunc.ZjZhPortStatusChangeReportAsync(station_code, port_code, "Load", "PROC", "N", pallet_num, String.valueOf(left_count), user_name, "N", "N","ECL","UnLoad");
                                    eapDySendFlowFunc.ZjZhCarrierStatusReportAsync(station_code, pallet_num, "Process", lot_list, "0", "Load", port_code,"N");
                                }
                            }
                        }
                    }
                }
                //CCD
                eapDySendFlowFunc.CCDDataReport(station_code, panel_barcode, ccd_no);
                eapDySendFlowFunc.StoreInReport(station_code, panel_barcode, port_code, slot_no, lot_num);
                eapDySendFlowFunc.JobCountReport(station_code, left_count, port_code,"UnLoad");
            }
        }
        catch (Exception ex){
            log.warn("EapDyZjZhLoadPlanPanelCheckAndSave:"+ex);
        }
    }
}
