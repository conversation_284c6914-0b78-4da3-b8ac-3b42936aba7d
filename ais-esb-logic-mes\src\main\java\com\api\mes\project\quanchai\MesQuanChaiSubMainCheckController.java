package com.api.mes.project.quanchai;

import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@RestController
@Slf4j
@RequestMapping("/mes/project/quanchai")
public class MesQuanChaiSubMainCheckController {
    @Autowired
    private MongoTemplate mongoTemplate;

    //分线绑定到总线信息存储
    @RequestMapping(value = "/MesQuanChaiMeMaterialRelIns", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesQuanChaiMeMaterialRelIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/project/quanchai/MesQuanChaiMeMaterialRelIns";
        String transResult="";
        String errorMsg="";
        String meMaterialRelTable="c_mes_me_material_rel";
        try{
            String prod_line_code=jsonParas.getString("prod_line_code");
            String small_model_type_main=jsonParas.getString("small_model_type_main");
            String model_code_main=jsonParas.getString("model_code_main");
            String serial_num_main=jsonParas.getString("serial_num_main");
            String small_model_type_sub=jsonParas.getString("small_model_type_sub");
            String model_code_sub=jsonParas.getString("model_code_sub");
            String serial_num_sub=jsonParas.getString("serial_num_sub");
            //格式化新增数据
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            //新增
            List<Map<String, Object>> lstDocuments=new ArrayList<>();
            Map<String, Object> mapDataItem=new HashMap<>();
            String material_rel_id=CFuncUtilsSystem.CreateUUID(true);
            mapDataItem.put("item_date",item_date);
            mapDataItem.put("item_date_val",item_date_val);
            mapDataItem.put("material_rel_id",material_rel_id);
            mapDataItem.put("main_prod_line_code",prod_line_code);
            mapDataItem.put("main_serial_num",serial_num_main);
            mapDataItem.put("main_material_type",small_model_type_main);
            //分线
            mapDataItem.put("sub_prod_line_code",prod_line_code);
            mapDataItem.put("sub_serial_num",serial_num_sub);
            mapDataItem.put("sub_material_type",small_model_type_sub);
            //上传标识
            mapDataItem.put("up_flag","N");
            mapDataItem.put("up_ng_code",-1);
            mapDataItem.put("up_ng_msg","");
            mapDataItem.put("enable_flag","Y");
            lstDocuments.add(mapDataItem);
            mongoTemplate.bulkOps(BulkOperations.BulkMode.UNORDERED,meMaterialRelTable).insert(lstDocuments).execute();
            transResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "分线绑定到总线信息存储异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
