package com.api.pack.core.board;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.context.MessageSource;
import org.springframework.util.ObjectUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.Locale;

/**
 * <p>
 * 板件前后比较器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
public class BoardF2BComparator extends BoardDefaultComparator implements BoardComparator
{
    public void compare(SETBoard front, SETBoard back, String[] properties, MessageSource messageSource)
    {
        if (front == null)
        {
            String err = getPackFormatMessage(BoardConst.SET_FRONT_CONTENT, messageSource);
            throw new BoardCompareException(err);
        }
        if (back == null)
        {
            String err = getPackFormatMessage(BoardConst.SET_BACK_CONTENT, messageSource);
            throw new BoardCompareException(err);
        }
        try
        {
            for (String property : properties)
            {
                if (BoardConst.BOARD_PROPERTY_REPAIR_LABEL.equals(property)) {  //不校验双面维修标签是否一致
                    continue;
                }
                if (BoardConst.BOARD_PROPERTY_LEVEL.equals(property)){  //不校验双面板件等级是否一致
                    continue;
                }
                Field frontField = front.getClass().getDeclaredField(property);
                String propertyName = property;
                ApiModelProperty annotation = frontField.getAnnotation(ApiModelProperty.class);
                if (annotation != null)
                {
                    propertyName = annotation.value();
                }
                if (!Modifier.isPublic(frontField.getModifiers()))
                {
                    frontField.setAccessible(true);
                }
                Object frontValue = frontField.get(front);
                Field backField = back.getClass().getDeclaredField(property);
                if (!Modifier.isPublic(backField.getModifiers()))
                {
                    backField.setAccessible(true);
                }
                Object backValue = backField.get(back);
                if ((ObjectUtils.isEmpty(frontValue) && ObjectUtils.isEmpty(backValue)) // NULL
                        || (isNC(frontValue) || isNC(backValue)) // NC
                        || (frontValue != null && frontValue.equals(backValue)) // EQ
                )
                {
                    continue;
                }
                String err = getPackF2BNotMatchMessage(front.getBoardCategory(), propertyName, frontValue, backValue, messageSource);
                throw new BoardCompareException(err, BoardConst.STATUS_NG, BoardConst.CODE_NG,  BoardConst.SORT_BOARD_F2B);
            }
        }
        catch (BoardCompareException ex)
        {
            throw ex;
        }
        catch (Throwable ex)
        {
            String compareRuleName = messageSource.getMessage(BoardConst.BOARD_COMPARE_MESSAGES_PACK_COMPARE_RULES_F2B, null, Locale.getDefault());
            String err = messageSource.getMessage(BoardConst.BOARD_COMPARE_MESSAGES_PACK_NOT_MATCH_ALL, new Object[]{BoardConst.BLANK, compareRuleName}, Locale.getDefault());
            throw new BoardCompareException(err, BoardConst.STATUS_NG, BoardConst.CODE_NG);
        }
    }

    private String getPackFormatMessage(String param1, MessageSource messageSource)
    {
        return messageSource.getMessage(BoardConst.BOARD_COMPARE_MESSAGES_PACK_FORMAT_ERROR, new Object[]{param1}, Locale.getDefault());
    }

    private String getPackF2BNotMatchMessage(String param1, String param2, Object param3, Object param4, MessageSource messageSource)
    {
        return messageSource.getMessage(BoardConst.BOARD_COMPARE_MESSAGES_PACK_COMPARE_RULES_F2B_NOT_MATCH, new Object[]{param1, param2, String.valueOf(param3), String.valueOf(param4)}, Locale.getDefault());
    }
}
