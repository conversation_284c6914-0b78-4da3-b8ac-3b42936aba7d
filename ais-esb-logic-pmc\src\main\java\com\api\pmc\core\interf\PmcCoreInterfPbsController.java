package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * PBS接口
 * 1.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfPbsController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //1.PBS数据回传
    @RequestMapping(value = "/PmcCorePbsReportStation", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCorePbsReportStation(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/interf/PmcCorePbsReportStation";
        String selectResult="";
        String errorMsg="";
        try{
            String data_from_sys=jsonParas.getString("data_from_sys");//来源系统，待定义
            /*
            String vin=jsonParas.getString("vin");//VIN号
            String shebeibh=jsonParas.getString("shebeibh");//设备编号
            String dh=jsonParas.getString("dh");//图号
            String shedingjzl=jsonParas.getString("shedingjzl");//设定加注量
            String shijijzl=jsonParas.getString("shijijzl");//实际加注量
            String shedingyl=jsonParas.getString("shedingyl");//设定压力
            String shijiyl=jsonParas.getString("shijiyl");//实际压力
            String shedingczkz=jsonParas.getString("shedingczkz");//设定粗抽真空值
            String shijiczkz=jsonParas.getString("shijiczkz");//实际粗抽真空值
            String shedingxzkz=jsonParas.getString("shedingxzkz");//设定细抽真空值
            String shijixzkz=jsonParas.getString("shijixzkz");//实际细抽真空值
            String zuoyesj=jsonParas.getString("zuoyesj");//作业时间
            String jiazhujp=jsonParas.getString("jiazhujp");//加注节拍
            String gongyicsly=jsonParas.getString("gongyicsly");//工艺参数来源
            String panding=jsonParas.getString("panding");//加注结果判定
            */

            log.info("PBS数据回传:"+jsonParas.toString());

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "PBS数据回传异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
