package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 定颖放板机定制生产计划处理逻辑
 * 1.放板机Panel校验
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-19
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/cj/event")
public class EapCjEventController {
    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @RequestMapping(value = "/InOrOutPanelEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String InOrOutPanelEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        try {
            CjInOrOutPnlEvent customEvent = new CjInOrOutPnlEvent(this, jsonParas);
            applicationEventPublisher.publishEvent(customEvent);
        } catch (Exception e) {
            return CFuncUtilsLayUiResut.GetErrorJson("出现未知异常");
        }
        return CFuncUtilsLayUiResut.GetStandJson(true, null, "", "处理成功", 0);
    }
}
