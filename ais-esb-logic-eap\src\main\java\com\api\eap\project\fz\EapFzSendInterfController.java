package com.api.eap.project.fz;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.project.glorysoft.EapGlorySendInterfFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 方正EAP定义接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-06
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/fz/interf/send")
public class EapFzSendInterfController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private EapFzSendInterfFunc eapFzSendInterfFunc;

    //[EAP]设备初始化建立通讯请求上报
    @RequestMapping(value = "/EapAreYouThereRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapAreYouThereRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fz/interf/send/EapAreYouThereRequest";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapFzSendInterfFunc.eapAreYouThereRequest(jsonParas);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "[EAP]设备初始化建立通讯请求上报接口异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]设备基本信息上报-定时
    @RequestMapping(value = "/EapEquipmentInformationReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapEquipmentInformationReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fz/interf/send/EapEquipmentInformationReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapFzSendInterfFunc.eapEquipmentInformationReport(jsonParas);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "[EAP]设备切换CIM状态上报接口异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]设备发生状态改变时调用
    @RequestMapping(value = "/EapEquipmentStatusReportChangeReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapEquipmentStatusReportChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fz/interf/send/EapEquipmentStatusReportChangeReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapFzSendInterfFunc.eapEquipmentStatusReportChangeReport(jsonParas);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "[EAP]设备发生状态改变时调用接口异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]设备发生报警时调用
    @RequestMapping(value = "/EapAlarmWarningDataReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapAlarmWarningDataReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fz/interf/send/EapAlarmWarningDataReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapFzSendInterfFunc.eapAlarmWarningDataReport(jsonParas);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "[EAP]设备发生报警时调用接口异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]制程资料报告
    @RequestMapping(value = "/EapProcessDataReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapProcessDataReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fz/interf/send/EapProcessDataReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapFzSendInterfFunc.eapProcessDataReport(jsonParas);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "[EAP]取片事件报告接口异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]员工上下岗报告
    @RequestMapping(value = "/EapUserLoginLogoutReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapUserLoginLogoutReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fz/interf/send/EapUserLoginLogoutReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapFzSendInterfFunc.eapUserLoginLogoutReport(jsonParas);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "[EAP]放片事件报告接口异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //[EAP]档案路径和参数信息请求
    @RequestMapping(value = "/EapFilePathAndParameterRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapFilePathAndParameterRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fz/interf/send/EapFilePathAndParameterRequest";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapFzSendInterfFunc.eapFilePathAndParameterRequest(jsonParas, request);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "[EAP]收放板机工位状态报告接口异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


    //[EAP]配方参数变更上报
    @RequestMapping(value = "/EapRecipeParameterRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapRecipeParameterRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/fz/interf/send/EapRecipeParameterRequest";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = eapFzSendInterfFunc.eapRecipeParameterRequest(jsonParas);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "[EAP]收放板机工位状态报告接口异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
