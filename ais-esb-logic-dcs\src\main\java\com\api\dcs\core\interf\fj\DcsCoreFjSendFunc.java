package com.api.dcs.core.interf.fj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * DCS分拣发送流程功能函数
 * 1.发送分拣任务
 * 2.通知工位开始分拣
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@Slf4j
public class DcsCoreFjSendFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private DcsCoreFjSendSubFunc dcsCoreFjSendSubFunc;
    @Autowired
    private DcsInterfCommon dcsInterfCommon;
    @Resource
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Resource
    private CFuncUtilsRest cFuncUtilsRest;

    //1.发送分拣任务
    public void FjSendTask(String userID, String mo_id_list, JSONArray task_list) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsCoreFjSendSubFunc.FjSendTask(task_list);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        Integer code = jbResult.getInteger("code");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
            //记录事件
            String[] lstMoIds = mo_id_list.split(",", -1);
            for (String mo_id : lstMoIds) {
                dcsInterfCommon.InsertApsTaskEvent(mo_id, userID, esbInterfCode, "发送分拣任务", requestParas, responseParas, code, message, successFlag, startDate, endDate, "");
            }
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    //2.通知工位开始分拣
    public void FjSendTaskStart(String station_code, String mo_id, String task_num, String is_frame) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsCoreFjSendSubFunc.FjSendTaskStart(station_code, task_num, is_frame);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        Integer code = jbResult.getInteger("code");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
            //记录事件
            dcsInterfCommon.InsertApsTaskEvent(mo_id, station_code, esbInterfCode, "通知工位开始分拣", requestParas, responseParas, code, message, successFlag, startDate, endDate, "");
        }
        if (!successFlag) {
            throw new Exception(message);
        }
    }

    /**
     * 接口描述：切割分拣完成报工；
     * 报工接口 在G15工位的流程图增加一个步骤 生产任务结束报工MOM
     * task_num任务号 读取到当前工位的任务传入接口
     * cut_count切割总数量 为当前任务解析的数量
     * real_weight钢板重量 根据当前任务号查询b_dcs_aps_task表是哪个钢板型号 根据钢板型号去查询
     * b_dcs_fmod_model表得到m_weight重量传入到这个字段；
     * 根据任务号去b_dcs_aps_task表查询 task_num=’task_num’ 得到mo_id
     * 根据mo_id去查询b_dcs_aps_task_resolve分拣解析表 mo_id=‘mo_id’得到总数量count 传入对应的数量数字到cut_count
     * part_material_num 零件物料号 根据任务号去b_dcs_aps_task表查询 task_num=’task_num’
     * 得到mo_id 再去查询b_dcs_me_sort_result分拣结果表的material_num字段 根据material_num进行group分组一下看这个任务下面有
     * 几个不一样的物料号 每一个物料号下面有几个零件 就是零件数量 然后遍历传入list里面的sort_num
     */
    public JSONObject SortingCompletedReportMom(JSONObject request) throws Exception {
        String esbInterfCode = "SortingCompletedReportMom";
        String errorMsg = "";
        JSONObject response = new JSONObject();

        try {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            return cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, request);
        } catch (Exception e) {
            errorMsg = "接口发生未知异常:" + e.getMessage();
            response.put("code", -1);
            response.put("msg", errorMsg);
        }
        return response;
    }
}
