package com.api.pack.core.recipe;

import com.alibaba.fastjson.JSONObject;
import com.api.base.IMybatisBasic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 配方
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-24
 */
@ApiModel(value = "Recipe", description = "配方")
@TableName("a_pack_fmod_recipe")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class Recipe extends IMybatisBasic
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "配方ID")
    @TableId(value = "recipe_id")
    private Long id;

    @ApiModelProperty(value = "料号(型号)")
    @TableField(value = "model_type")
    private String modelType;

    @ApiModelProperty(value = "料号(型号)版本")
    @TableField(value = "model_version")
    private String modelVersion;

    @ApiModelProperty(value = "SET类型:无码,单面,双面")
    @TableField(value = "array_type")
    private String arrayType;

    @ApiModelProperty(value = "PCS类型:无码,单面,双面")
    @TableField(value = "bd_type")
    private String bdType;

    @ApiModelProperty(value = "SET条码大小写规则:无规则,大写,小写")
    @TableField(value = "array_case")
    private String arrayCase;

    @ApiModelProperty(value = "PCS条码大小写规则:无规则,大写,小写")
    @TableField(value = "bd_case")
    private String bdCase;

    @ApiModelProperty(value = "长,单位毫米")
    @TableField(value = "m_length")
    private Double mLength;

    @ApiModelProperty(value = "宽,单位毫米")
    @TableField(value = "m_width")
    private Double mWidth;

    @ApiModelProperty(value = "厚,单位毫米")
    @TableField(value = "m_tickness")
    private Double mThickness;

    @ApiModelProperty(value = "重量,单位克")
    @TableField(value = "m_weight")
    private Double mWeight;

    @ApiModelProperty(value = "SET条码长度")
    @TableField(value = "array_length")
    private Integer arrayLength;

    @ApiModelProperty(value = "PCS条码长度")
    @TableField(value = "bd_length")
    private Integer bdLength;

    @ApiModelProperty(value = "截取规则集")
    @TableField(value = "split_rules")
    private String splitRules;

    @ApiModelProperty(value = "分选截取比对规则ID")
    @TableField(value = "sort_split_rule_id")
    private Long sortSplitRuleId;

    public Recipe(JSONObject data)
    {
        this.id = data.getLongValue("recipe_id");
        this.modelType = data.getString("model_type");
        String[] ss = this.modelType.split("-");
        this.modelVersion = "01";
        if (ss.length >= 2)
        {
            this.modelVersion = ss[1];
        }
        this.mLength = data.getDoubleValue("m_length");
        this.mWidth = data.getDoubleValue("m_width");
        this.mThickness = data.getDoubleValue("m_tickness");
        this.mWeight = data.getDoubleValue("m_weight");
        // SET条码长度默认15
        this.arrayLength = 15;
        this.bdLength = 0;
        // SET类型默认双面
        this.arrayType = "Double";
        // PCS类型默认无码
        this.bdType = "None";
        this.arrayCase = "Normal";
        this.bdCase = "Normal";
        // SET板件SET码截取镭射批次规则从左边第3位开始截取4个字符 array_lot_split_rule
        // SET板件SET码截取周期规则从左边第7位开始截取4个字符 array_cycle_split_rule
        // SET板件字符截取周期规则从左边第7位开始截取4个字符 array_dc_split_rule
        // SET板件字符截取镭射批次规则从左边第3位开始截取4个字符 array_ln_split_rule
        /*
            {
                "array_set_split_rule": "",
                "array_lot_split_rule": "Left|3|4",
                "array_cycle_split_rule": "Left|7|4",
                "array_dc_split_rule": "Left|7|4",
                "array_ln_split_rule": "Left|3|4",
                "bd_set_split_rule": "",
                "bd_lot_split_rule": "",
                "bd_cycle_split_rule": "",
                "bd_dc_split_rule": "",
                "bd_ln_split_rule": "",
                "bd_index_split_rule": ""
            }
         */
        this.splitRules = "{\"array_set_split_rule\":\"\",\"array_lot_split_rule\":\"Left|3|4\",\"array_cycle_split_rule\":\"Left|7|4\",\"array_dc_split_rule\":\"Left|7|4\",\"array_ln_split_rule\":\"Left|3|4\",\"bd_set_split_rule\":\"\",\"bd_lot_split_rule\":\"\",\"bd_cycle_split_rule\":\"\",\"bd_dc_split_rule\":\"\",\"bd_ln_split_rule\":\"\",\"bd_index_split_rule\":\"\"}";
        this.sortSplitRuleId = 1L;
    }
}
