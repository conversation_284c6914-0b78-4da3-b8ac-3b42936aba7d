package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 检测接口
 * 1.整体检测结果回传
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfJcController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //1.整体检测结果回传
    @RequestMapping(value = "/PmcCoreJcReportStation", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreJcReportStation(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/interf/PmcCoreJcReportStation";
        String selectResult="";
        String errorMsg="";
        String userName="-1";
        try{
            log.info("整体检测结果回传:"+jsonParas.toString());

            String request_uuid=jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time=jsonParas.getString("request_time");//请求时间
            String request_attr=jsonParas.getString("request_attr");//预留请求属性
            String data_from_sys=jsonParas.getString("data_from_sys");//来源系统，待定义
            //循环
            String vin="";//VIN号
            String test_duration="";//整体检测时间
            String test_times="";//整体检测次数
            String test_judge="";//整体检测结果
            JSONArray jzArray = jsonParas.getJSONArray("list");
            if(jzArray != null && jzArray.size()>0) {
                for (int i = 0; i < jzArray.size(); i++) {
                    JSONObject jzObject = jzArray.getJSONObject(i);
                    vin=jzObject.getString("vin");//VIN号
                    test_duration=jzObject.getString("test_duration");//整体检测时间
                    test_times=jzObject.getString("test_times");//整体检测次数
                    test_judge=jzObject.getString("test_judge");//整体检测结果

                    /*
                    String insertSql="insert into d_pmc_me_station_quality_fill (vin,shebeibh,dh," +
                            "shedingjzl,shijijzl,shedingyl,shijiyl," +
                            "shedingczkz,shijiczkz,shedingxzkz,shijixzkz," +
                            "shedingzkjl,shijizkjl,shedingzyjl,shijizyjl," +
                            "zuoyesj,jiazhujp,gongyicsly,panding " +
                            ") values (" + "'"+vin+"','"+shebeibh+"','"+dh+"',"+
                            shedingjzl+","+shijijzl+","+shedingyl+","+shijiyl+","+
                            shedingczkz+","+shijiczkz+","+shedingxzkz+","+shijixzkz+","+
                            shedingzkjl+","+shijizkjl+","+shedingzyjl+","+shijizyjl+",'"+
                            zuoyesj+"',"+jiazhujp+","+gongyicsly+","+panding +")";
                    cFuncDbSqlExecute.ExecUpdateSql(userName,insertSql,true,request,apiRoutePath);
                     */
                }
            }

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "整体检测结果回传异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
