package com.api.pmc.project.bq;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 北汽车间大屏数据接口
 * 1.总装/焊装车间车辆位置、图片url、信息
 * 2.总装/焊装车间实时生产数据
 * 3.PBS区/底盘喷涂线根据serial_num获取车辆信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/project/bq")
public class PmcBqWorkshopScreenDataController {
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncDbSqlMapper cFuncDbSqlMapper;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.总装/焊装车间车辆位置、图片url、信息
    @RequestMapping(value = "/PmcBqWorkshopCarInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqWorkshopCarInfo(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        String work_center_code = jsonParas.getString("work_center_code");
        String prod_line_code = jsonParas.getString("prod_line_code");
        try {
            String sql = "select sta.prod_line_code,fs.line_section_code,sta.station_code,sta.vin,sta.dms,sta.make_order," +
                    "sta.small_model_type,img.car_color_des,img.car_image_body,img.car_image_head_left,img.car_image_head_right," +
                    "img.car_model_des,stt.white_car_adjust from d_pmc_me_station_status sta " +
                    " left join d_pmc_fmod_car_image img on sta.small_model_type = img.car_type and sta.material_color = img.car_color" +
                    " left join sys_fmod_station fs on sta.station_code = fs.station_code " +
                    " left join d_pmc_me_flow_online stt ON sta.make_order = stt.make_order " +
                    " where sta.work_center_code = '" + work_center_code + "' and sta.station_status = '1' and sta.make_order <> ''";
            if (prod_line_code != null && !prod_line_code.equals("")) {
                sql += " and sta.prod_line_code='" + prod_line_code + "' ";
            }
            List<Map<String, Object>> itemList = cFuncDbSqlMapper.ExecSelectSql(sql);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "总装/焊装车间车辆信息查询异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.总装/焊装车间实时生产数据
    @RequestMapping(value = "/PmcBqWorkshopRealTimeData", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqWorkshopRealTimeData(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        try {
            String prod_line_codes = jsonParas.getString("prod_line_codes");
            String[] codeList = prod_line_codes.split(",");
            StringBuilder sql = new StringBuilder("select prod_line_code,today_plan,current_online,current_offline,jph_actual  from d_pmc_fmod_screen_set_content" +
                    " where prod_line_code in (");
            for (int i = 0; i < codeList.length; i++) {
                sql.append("'" + codeList[i] + "'");
                if (i < codeList.length - 1) {
                    sql.append(",");
                }
            }
            sql.append(")");
            List<Map<String, Object>> itemList = cFuncDbSqlMapper.ExecSelectSql(sql.toString());
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "总装/焊装车间车辆信息查询异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //3.PBS区/底盘喷涂线根据serial_num获取车辆信息
    @RequestMapping(value = "/PmcBqCarInfoBySerialNum", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqCarInfoBySerialNum(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        try {
            String serial_num = jsonParas.getString("serial_num");
            String sql = "select fo.station_code,fo.vin,fo.dms,fo.make_order,fo.small_model_type,img.car_color_des from d_pmc_me_flow_online fo\n" +
                    "left join sys_fmod_station fs on fo.station_code = fs.station_code\n" +
                    "left join d_pmc_fmod_car_image img on fo.small_model_type = img.car_type and fo.material_color = img.car_color\n" +
                    "where serial_num = '" + serial_num + "'";
            List<Map<String, Object>> itemList = cFuncDbSqlMapper.ExecSelectSql(sql);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);

        } catch (Exception ex) {
            errorMsg = "PBS区/底盘喷涂线根据serial_num获取车辆信息异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 4.根据加注机设备编号获取加注机状态
     *
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/PmcBqFillDeviceStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqFillDeviceStatus(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        try {
            String shebeibhList = jsonParas.getString("shebeibhList");
            Aggregation aggregation = Aggregation.newAggregation(
                    Aggregation.match(Criteria.where("shebeibh").in(shebeibhList.split(","))),
                    Aggregation.sort(Sort.by(Sort.Direction.DESC, "_id")),
                    Aggregation.group("shebeibh").first("$$ROOT").as("latestData")
            );
            AggregationResults<Map> aggregationResults = mongoTemplate.aggregate(aggregation, "d_pmc_me_fill_device_status", Map.class);

            List<Map<String, Object>> itemList = new ArrayList<>();
            for (Map map : aggregationResults.getMappedResults()) {
                Map<String, Object> ldMap = (Map<String, Object>) map.get("latestData");
                Map<String, Object> dMap = new HashMap<>();
                dMap.put("shebeibh", map.get("_id"));
                dMap.put("device_status", ldMap.get("device_status"));
                itemList.add(dMap);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 1);
        } catch (Exception ex) {
            errorMsg = "根据加注机设备编号获取加注机状态信息异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }


    /**
     * 5.查询PBS区车辆位置、图片url
     *
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/PmcBqPbsAreaCarInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqPbsAreaCarInfo(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        try {
            String cell_ip = jsonParas.getString("cell_ip");
            List<Map<String, Object>> tagList = cFuncDbSqlMapper.ExecSelectSql("select cli.client_code,gp.tag_group_code,tag.tag_code from scada_tag tag " +
                    " left join scada_tag_group gp on tag.tag_group_id = gp.tag_group_id" +
                    " left join scada_client cli on gp.client_id = cli.client_id" +
                    " where gp.tag_group_code in ('PBS_CarPos1', 'PBS_CarPos2')");
            JSONArray readTagList = new JSONArray();
            for (Map<String, Object> map : tagList) {
                JSONObject readTag = new JSONObject();
                String tagKey = map.get("client_code").toString() + "/" + map.get("tag_group_code").toString() + "/" + map.get("tag_code").toString();
                map.put("tag_key", tagKey);
                readTag.put("tag_key", tagKey);
                readTagList.add(readTag);
            }

            JSONObject tagResult = cFuncUtilsRest.PostJaBackJb(cell_ip + "/cell/core/scada/CoreScadaReadTag", readTagList);
            if (tagResult == null && !"0".equals(tagResult.getString("code"))) {
                return CFuncUtilsLayUiResut.GetErrorJson("点位数据获取失败");
            }

            JSONArray inposList = new JSONArray();
            JSONArray rfidList = new JSONArray();
            StringJoiner rfids = new StringJoiner(",");
            JSONArray data = tagResult.getJSONArray("data");
            for (int i = 0; i < data.size(); i++) {
                JSONObject obj = data.getJSONObject(i);
                String tag_key = obj.getString("tag_key");
                String tag_value = obj.getString("tag_value");
                if (tag_key.contains("_Inpos") && !StringUtils.isEmpty(tag_value)) {
                    inposList.add(obj);
                } else if (tag_key.contains("_RFID") && !StringUtils.isEmpty(tag_value)) {
                    rfidList.add(obj);
                    rfids.add(tag_value);
                }
            }

            // 根据rfid获取上位系统车辆信息
            JSONArray workOrderList = new JSONArray();
            if (!StringUtils.isEmpty(rfids.toString())) {
                JSONObject params = new JSONObject();
                params.put("rfids", rfids.toString());
                params.put("workshop", "ZA");
                //JSONObject woResult = cFuncUtilsRest.PostJbBackJb("http://127.0.0.1:9091/pmc/project/bq/PmcBqExtendWorkOrderByRfids",  params);
                JSONObject woResult = cFuncUtilsRest.PostJbBackJb("http://10.140.4.15:9090/aisEsbOra/pmc/project/bq/PmcBqExtendWorkOrderByRfids", params);
                if (woResult != null && "0".equals(woResult.getString("code"))) {
                    workOrderList = woResult.getJSONArray("data");
                }
            }


            // 根据车型和车身颜色获取车辆图片
            List<Map<String, Object>> imgList = cFuncDbSqlMapper.ExecSelectSql("select car_type,car_color,car_image_head_right from d_pmc_fmod_car_image");
            Map<String, String> imgMap = imgList.stream().collect(Collectors.toMap((s) -> s.get("car_type").toString() + s.get("car_color").toString(), (s) -> s.get("car_image_head_right").toString()));
            if (!CollectionUtils.isEmpty(workOrderList)) {
                for (int i = 0; i < workOrderList.size(); i++) {
                    JSONObject wo = workOrderList.getJSONObject(i);
                    String car_image_head_right = imgMap.get(wo.getString("PLATFORM_MODEL") + wo.getString("CAR_COLOUR_CODE"));
                    wo.put("car_image_head_right", car_image_head_right);
                }
            }

            // 组装数据返回前端
            Map<String, Map> workMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(workOrderList)) {
                List<Map> woList = workOrderList.toJavaList(Map.class);
                workMap = woList.stream().collect(Collectors.toMap(map -> (String) map.get("RFID"), map -> map));
            }
            for (int i = 0; i < rfidList.size(); i++) {
                JSONObject obj = rfidList.getJSONObject(i);
                String tag_value = obj.getString("tag_value");
                Map wo = workMap.get(tag_value);
                if (wo != null) {
                    obj.put("workshop", wo.get("WORKSHOP"));
                    obj.put("rfid", wo.get("RFID"));
                    obj.put("vin", wo.get("VIN"));
                    obj.put("order_prod", wo.get("ORDER_PROD"));
                    obj.put("platform_model", wo.get("PLATFORM_MODEL"));
                    obj.put("dms_num", wo.get("DMS_NUM"));
                    obj.put("car_colour_description", wo.get("CAR_COLOUR_DESCRIPTION"));
                    obj.put("car_image_head_right", wo.get("car_image_head_right"));
                }
            }

            JSONObject result = new JSONObject();
            result.put("code", "0");
            result.put("inposList", inposList);
            result.put("rfidList", rfidList);
            selectResult = result.toString();
        } catch (Exception ex) {
            errorMsg = "查询PBS区车辆位置、图片url信息异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 6.初始获取点位数据
     *
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/initGetTagData", method = {RequestMethod.POST, RequestMethod.GET})
    public String initGetTagData(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        try {
            String cell_ip = jsonParas.getString("cell_ip");
            String client_code = jsonParas.getString("client_code");
            String tag_group_code = jsonParas.getString("tag_group_code");
            String sql = "select cli.client_code,gp.tag_group_code,tag.tag_code from scada_tag tag " +
                    " left join scada_tag_group gp on tag.tag_group_id = gp.tag_group_id" +
                    " left join scada_client cli on gp.client_id = cli.client_id" +
                    " where 1=1";
            if (!StringUtils.isEmpty(tag_group_code)) {
                StringBuilder sb = new StringBuilder();
                sb.append(" and gp.tag_group_code in (");
                String[] codeList = tag_group_code.split(",");
                for (int i = 0; i < codeList.length; i++) {
                    sb.append("'" + codeList[i] + "'");
                    if (i < codeList.length - 1) {
                        sb.append(",");
                    }
                }
                sb.append(")");
                sql += sb.toString();
            }
            if (!StringUtils.isEmpty(client_code)) {
                sql += " and cli.client_code = '" + client_code + "'";
            }

            List<Map<String, Object>> tagList = cFuncDbSqlMapper.ExecSelectSql(sql);

            JSONArray readTagList = new JSONArray();
            for (Map<String, Object> map : tagList) {
                JSONObject readTag = new JSONObject();
                String tagKey = map.get("client_code").toString() + "/" + map.get("tag_group_code").toString() + "/" + map.get("tag_code").toString();
                map.put("tag_key", tagKey);
                readTag.put("tag_key", tagKey);
                readTagList.add(readTag);
            }

            JSONObject tagResult = cFuncUtilsRest.PostJaBackJb(cell_ip + "/cell/core/scada/CoreScadaReadTag", readTagList);
            if (tagResult == null && !"0".equals(tagResult.getString("code"))) {
                return CFuncUtilsLayUiResut.GetErrorJson("点位数据获取失败");
            }

            JSONObject result = new JSONObject();
            result.put("code", "0");
            result.put("data", tagResult.getJSONArray("data"));
            selectResult = result.toString();
        } catch (Exception ex) {
            errorMsg = "初始获取点位数据异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 7.查询底盘喷涂区车辆位置
     *
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/PmcBqDbptAreaCarInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqDbptAreaCarInfo(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        try {
            String cell_ip = jsonParas.getString("cell_ip");
            List<Map<String, Object>> tagList = cFuncDbSqlMapper.ExecSelectSql("select cli.client_code,gp.tag_group_code,tag.tag_code from scada_tag tag " +
                    " left join scada_tag_group gp on tag.tag_group_id = gp.tag_group_id" +
                    " left join scada_client cli on gp.client_id = cli.client_id" +
                    " where cli.client_code = 'PMC_DT_Status' and tag.tag_code like '%_RB_PRY_NUM'");
            JSONArray readTagList = new JSONArray();
            for (Map<String, Object> map : tagList) {
                JSONObject readTag = new JSONObject();
                String tagKey = map.get("client_code").toString() + "/" + map.get("tag_group_code").toString() + "/" + map.get("tag_code").toString();
                map.put("tag_key", tagKey);
                readTag.put("tag_key", tagKey);
                readTagList.add(readTag);
            }

            JSONObject tagResult = cFuncUtilsRest.PostJaBackJb(cell_ip + "/cell/core/scada/CoreScadaReadTag", readTagList);
            if (tagResult == null && !"0".equals(tagResult.getString("code"))) {
                return CFuncUtilsLayUiResut.GetErrorJson("点位数据获取失败");
            }

            JSONArray carList = tagResult.getJSONArray("data");
            List<String> palletNumList = new ArrayList<>();
            for (int i = 0; i < carList.size(); i++) {
                JSONObject obj = carList.getJSONObject(i);
                String tag_value = obj.getString("tag_value");
                if (!StringUtils.isEmpty(tag_value)) palletNumList.add(tag_value);
            }

            // 根据滑橇号查询过站数据
            Map<String, Map> moMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(palletNumList)) {
                StringBuilder sb = new StringBuilder();
                for (int i = 0; i < palletNumList.size(); i++) {
                    sb.append("'" + palletNumList.get(i) + "'");
                    if (i < palletNumList.size() - 1) {
                        sb.append(",");
                    }
                }

                List<Map<String, Object>> moList = cFuncDbSqlMapper.ExecSelectSql("SELECT DISTINCT ON (pallet_num) pallet_num,f1.make_order,dms,vin,small_model_type,material_color" +
                        " FROM d_pmc_me_station_flow f1" +
                        " left join (select make_order from d_pmc_me_station_flow where station_code = 'ZADT020') f2 on f1.make_order = f2.make_order" +
                        " WHERE f1.pallet_num IN (" + sb.toString() + ") and f1.station_code = 'ZADT010' and f2.make_order is null" +
                        " ORDER BY pallet_num, station_flow_id DESC;");
                moMap = moList.stream().collect(Collectors.toMap(map -> (String) map.get("pallet_num"), map -> map));
            }

            // 组装数据返回前端
            for (int i = 0; i < carList.size(); i++) {
                JSONObject obj = carList.getJSONObject(i);
                String tag_value = obj.getString("tag_value");
                Map wo = moMap.get(tag_value);
                if (wo != null) {
                    obj.put("pallet_num", wo.get("pallet_num"));
                    obj.put("vin", wo.get("vin"));
                    obj.put("make_order", wo.get("make_order"));
                    obj.put("small_model_type", wo.get("small_model_type"));
                    obj.put("dms", wo.get("dms"));
                    obj.put("material_color", wo.get("material_color"));
                }
            }

            JSONObject result = new JSONObject();
            result.put("code", "0");
            result.put("carList", carList);
            selectResult = result.toString();
        } catch (Exception ex) {
            errorMsg = "查询底盘喷涂区车辆位置异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }


    /**
     * 7.查询WBS库区车辆位置
     *
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/PmcBqWbsAreaCarInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqWbsAreaCarInfo(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        try {
            String fastcode_group_code = jsonParas.getString("fastcode_group_code");
            List<Map<String, Object>> stationList = cFuncDbSqlMapper.ExecSelectSql("select sf.fastcode_code from sys_fastcode sf left join sys_fastcode_group sfg on sf.fastcode_group_id = sfg.fastcode_group_id " +
                    " where sfg.fastcode_group_code = '" + fastcode_group_code + "'" +
                    " order by sf.fastcode_order");
            if (CollectionUtils.isEmpty(stationList) || stationList.size() != 2) {
                return CFuncUtilsLayUiResut.GetErrorJson("未配置【" + fastcode_group_code + "】上下线工位");
            }
            String wbsUpLineStation = (String) stationList.get(0).get("fastcode_code");
            String wbsDownLineStation = (String) stationList.get(1).get("fastcode_code");
            List<Map<String, Object>> itemList = cFuncDbSqlMapper.ExecSelectSql("select * from (SELECT DISTINCT ON ( df.make_order ) df.station_code," +
                    "df.creation_date, df.make_order,df.dms,df.vin,df.small_model_type,img.car_image_body,img.car_image_head_left," +
                    "img.car_image_head_right,img.car_color_des FROM d_pmc_me_station_flow df LEFT JOIN " +
                    "( SELECT make_order, station_code FROM  d_pmc_me_station_flow WHERE station_code = '" + wbsDownLineStation + "' and check_status = 'OK') AS subquery " +
                    "ON df.make_order = subquery.make_order LEFT JOIN d_pmc_fmod_car_image img ON df.small_model_type = img.car_type  " +
                    " AND df.material_color = img.car_color WHERE df.station_code = '" + wbsUpLineStation + "'  " +
                    "AND subquery.make_order IS NULL AND df.check_status = 'OK' ORDER BY df.make_order) catch  " +
                    "ORDER BY catch.creation_date ");
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", itemList.size());
        } catch (Exception ex) {
            errorMsg = "查询WBS库区车辆位置异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 8.查询焊装车间产线车辆位置
     *
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/PmcBqHaProdLineCarInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcBqHaProdLineCarInfo(@RequestBody JSONObject jsonParas) {
        String selectResult = "";
        String errorMsg = "";
        String work_center_code = jsonParas.getString("work_center_code");
        String prod_line_code = jsonParas.getString("prod_line_code");
        try {
            String sql = "select sta.prod_line_code,fs.line_section_code,sta.station_code,sta.vin,sta.dms,sta.make_order,sta.small_model_type,img.car_color_des,img.car_image_body,img.car_image_head_left,img.car_image_head_right,fo.white_car_adjust,fo.right_front_door,fo.left_front_door,fo.vpp_s,fo.bbl_s,fo.bbr_s,fo.bbf_s,fo.bok_s,fo.y26,fo.bed_s,fo.bor_s\n" +
                    "from d_pmc_me_station_status sta\n" +
                    " left join d_pmc_fmod_car_image img on sta.small_model_type = img.car_type and sta.material_color = img.car_color\n" +
                    " left join sys_fmod_station fs on sta.station_code = fs.station_code\n" +
                    " left join d_pmc_me_flow_online fo on sta.make_order = fo.make_order" +
                    " where sta.work_center_code = '" + work_center_code + "' and sta.station_status = '1' and sta.make_order <> ''";
            if (prod_line_code != null && !prod_line_code.equals("")) {
                sql += " and sta.prod_line_code='" + prod_line_code + "' ";
            }
            List<Map<String, Object>> itemList = cFuncDbSqlMapper.ExecSelectSql(sql);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "焊装车间车辆信息查询异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}

