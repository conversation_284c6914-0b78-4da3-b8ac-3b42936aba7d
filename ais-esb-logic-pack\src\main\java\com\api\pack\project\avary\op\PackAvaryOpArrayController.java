package com.api.pack.project.avary.op;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.pack.core.ccd.CCDLblBarcodeResultMessage;
import com.api.pack.core.fmod.FmodRecipeConst;
import com.api.pack.core.fmod.FmodRecipeShipAddress;
import com.api.pack.core.fmod.FmodRecipeShipAddressDetail;
import com.api.pack.core.fmod.FmodRecipeShipAddressService;
import com.api.pack.core.pile.Pile;
import com.api.pack.core.pile.PileService;
import com.api.pack.core.plan.PlanService;
import com.api.system.FastCodeGroup;
import com.api.system.FastCodeGroupService;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Array逻辑处理
 * 1.线扫判断与存储
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@RestController
@RequestMapping("/pack/project/avary/op")
@Slf4j
public class PackAvaryOpArrayController
{
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PackAvaryOpArrayFunc opArrayFunc;
    @Autowired
    private PlanService planService;
    @Autowired
    private FmodRecipeShipAddressService fmodRecipeShipAddressService;
    @Autowired
    private FastCodeGroupService fastCodeGroupService;
    @Autowired
    private PileService pileService;

    //1.线扫判断与存储
    @RequestMapping(value = "/ArraySave", produces = "application/json;charset=utf-8", method = {
            RequestMethod.POST, RequestMethod.GET
    })
    public String ArraySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/avary/op/ArraySave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_pack_aps_plan";
        String result = "";
        try
        {
            //计时开始
            long l1 = System.currentTimeMillis();

            String user_name = "";
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            JSONObject ccd_data = jsonParas.getJSONObject("ccd_data");

            //1.获取当前WORK订单信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            JSONObject jbPlan = mongoTemplate.findOne(queryBigData, JSONObject.class, apsPlanTable);
            if (jbPlan == null || jbPlan.isEmpty())
            {
                errorMsg = "当前未下发任务,无法执行线扫判断,请先选择任务进行下发";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String plan_id = jbPlan.getString("plan_id");
            int finish_ok_count = jbPlan.getInteger("finish_ok_count");
            int finish_ng_count = jbPlan.getInteger("finish_ng_count");

            //2.获取当前用户信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            JSONObject user = mongoTemplate.findOne(queryBigData, JSONObject.class, meUserTable);
            if (user != null)
            {
                user_name = user.getString("user_name");
            }

            //3.获取配方信息
            String recipe_paras = jbPlan.getString("recipe_paras");
            JSONObject jbRecipe = JSONObject.parseObject(recipe_paras);

            //4.查询当前分选条件
            String sqlSort = "select " + "sort_code,sort_name," + "COALESCE(sort_value,'') sort_value " + "from a_pack_fmod_sort " + "where enable_flag='Y' and sort_flag='Y' order by sort_index";
            List<Map<String, Object>> itemListSort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlSort, false, request, apiRoutePath);
            JSONObject jbSort = new JSONObject();
            if (itemListSort != null && itemListSort.size() > 0)
            {
                for (Map<String, Object> mapItem : itemListSort)
                {
                    String sort_code = mapItem.get("sort_code").toString();
                    String sort_value = mapItem.get("sort_value").toString();
                    jbSort.put(sort_code, sort_value);
                }
            }

            //5.解析获取原始数据
            JSONObject jbCcdResolveResult = opArrayFunc.ResolveCcdResult(user_name, jbPlan, jbRecipe, jbSort, ccd_data);
            Map<String, Object> mapRowArray = jbCcdResolveResult.getJSONObject("array");

            //6.更新订单完工数量
            String array_status = mapRowArray.get("array_status").toString();
            if (array_status.equals("OK"))
            {
                finish_ok_count = finish_ok_count + 1;
            }
            else
            {
                finish_ng_count = finish_ng_count + 1;
            }
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            Update updateBigData = new Update();
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //计时结束
            long l2 = System.currentTimeMillis();
            long cost_time = l2 - l1;

            //7.返回参数
            JSONObject jbResult = new JSONObject();
            jbResult.put("array_id", mapRowArray.get("array_id").toString());
            jbResult.put("array_barcode", mapRowArray.get("array_barcode").toString());
            jbResult.put("board_sn", mapRowArray.get("board_sn").toString());
            jbResult.put("deposit_position", Integer.parseInt(mapRowArray.get("deposit_position").toString()));
            jbResult.put("board_result", Integer.parseInt(mapRowArray.get("board_result").toString()));
            jbResult.put("board_turn", Integer.parseInt(mapRowArray.get("board_turn").toString()));
            jbResult.put("cost_time", cost_time + "ms");
            jbResult.put("xout_act_num", Integer.parseInt(mapRowArray.get("xout_act_num").toString()));
            jbResult.put("array_index", Integer.parseInt(mapRowArray.get("array_index").toString()));
            jbResult.put("array_level", mapRowArray.get("array_level").toString());
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("finish_ng_count", finish_ng_count);
            jbResult.put("array_ng_msg", mapRowArray.get("array_ng_msg"));

            Map<String, Object> compareResult = jbCcdResolveResult.getJSONObject("compareResult");
            if (compareResult != null)
            {
                for (String key : compareResult.keySet())
                {
                    jbResult.put(key, compareResult.get(key));
                }
            }
            result = jbResult.toString();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        }
        catch (Exception ex)
        {
            errorMsg = "线扫判断与存储发生异常:" + ex.getMessage();
            log.error(errorMsg, ex);
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.PC向CCD发送当前工作中工单的出货地信息
    @RequestMapping(value = "/ShipAddressSel", produces = "application/json;charset=utf-8", method = {
            RequestMethod.POST, RequestMethod.GET
    })
    public String ShipAddressSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/avary/op/ShipAddressSel";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_pack_aps_plan";
        String result = "";
        try
        {
            Document plan = null;//当前工作中的订单
            String shipAddress = "";//当前工作订单的出货地
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            MongoCursor<Document> iterator = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            if (iterator.hasNext())
            {
                plan = iterator.next();
                shipAddress = plan.getString("ship_address");
                iterator.close();
            }
            result = shipAddress;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
            return transResult;
        }
        catch (Exception ex)
        {
            errorMsg = "PC向CCD发送当前工作中工单的出货地信息发生异常：" + ex.getMessage();
            log.error(errorMsg, ex.getMessage());
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.校验CCD发送的出货地数据
    @RequestMapping(value = "/ShipAddressResultAnalysis", produces = "application/json;charset=utf-8", method = {
            RequestMethod.POST, RequestMethod.GET
    })
    public String ShipAddressResultAnalysis(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/avary/op/ShipAddressResultAnalysis";
        String transResult = "";
        String errorMsg = "";
        String result = "";
        try
        {
            boolean bSucc = Boolean.TRUE;
            String ccd_data = jsonParas.getString("ccd_data");
            // 解析CCD数据
            CCDLblBarcodeResultMessage ccdMessage = CCDLblBarcodeResultMessage.fromJSON(ccd_data);
            Map<Integer, String> ccdLblBarcodeResultMessageContentItemMapping = ccdMessage.getCCDLblBarcodeResultMessageContentItemMapping();
            // 获取当前工作中的订单/计划
            Document plan = planService.getCurrent();
            String shipAddressName = plan.getString("ship_address");
            // 获取快速编码组中的MES字段
            FastCodeGroup mesField = FastCodeGroup.byCode(FmodRecipeConst.FCG_CODES_MES_FIELD, fastCodeGroupService);
            // 获取出货地信息
            FmodRecipeShipAddress shipAddress = FmodRecipeShipAddress.byName(shipAddressName, fmodRecipeShipAddressService);
            Map<Integer, FmodRecipeShipAddressDetail> detailsMapping = shipAddress.getDetailsMapping(mesField);
            for (Integer index : ccdLblBarcodeResultMessageContentItemMapping.keySet())
            {
                String lblBarcode = ccdLblBarcodeResultMessageContentItemMapping.get(index);
                FmodRecipeShipAddressDetail detail = detailsMapping.get(index);
                if (detail != null)
                {
                    String src = detail.splitData(lblBarcode);
                    String tar = null;
                    if (FmodRecipeConst.FCG_CODES_MES_FROM_0.equals(detail.getMesFrom()))
                    {
                        // MES订单
                        tar = String.valueOf(plan.get(detail.getMesFieldValue()));
                        //TODO 临时解决 2025/01/08
                        if (FmodRecipeConst.MES_FIELD_DC.equals(detail.getMesFieldValue()))
                        {
                            int srcLen = src.length();
                            int tarLen = tar.length();
                            if (srcLen > tarLen)
                            {
                                tar = String.format("%0" + srcLen + "d", Integer.parseInt(tar));
                            }
                        }
                    }
                    else if (FmodRecipeConst.FCG_CODES_MES_FROM_1.equals(detail.getMesFrom()))
                    {
                        // MES标签
                        String lotNum = plan.getString("lot_num");
                        Pile pile = this.pileService.findLastOne(lotNum);
                        tar = pile.getPileBarcode();
                    }
                    if (tar != null)
                    {
                        tar = detail.splitMesData(tar);
                        // 比对校验
                        switch (detail.getSplitCompareFunc())
                        {
                            case FmodRecipeConst.FCG_CODES_SPLIT_COMPARE_FUNC_0:
                                if (!src.contains(tar))
                                {
                                    bSucc = Boolean.FALSE;
                                }
                                break;
                            case FmodRecipeConst.FCG_CODES_SPLIT_COMPARE_FUNC_1:
                                if (!src.equals(tar))
                                {
                                    bSucc = Boolean.FALSE;
                                }
                                break;
                            case FmodRecipeConst.FCG_CODES_SPLIT_COMPARE_FUNC_2:
                                try
                                {
                                    if (Long.parseLong(src) <= Long.parseLong(tar))
                                    {
                                        bSucc = Boolean.FALSE;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    bSucc = Boolean.FALSE;
                                }
                                break;
                            case FmodRecipeConst.FCG_CODES_SPLIT_COMPARE_FUNC_3:
                                try
                                {
                                    if (Long.parseLong(src) >= Long.parseLong(tar))
                                    {
                                        bSucc = Boolean.FALSE;
                                    }
                                }
                                catch (Exception ex)
                                {
                                    bSucc = Boolean.FALSE;
                                }
                                break;
                            default:
                                throw new Exception("出货地信息校验失败，未知的比对函数值：" + detail.getSplitCompareFunc());
                        }
                    }
                    // 如果校验失败，跳出循环
                    if (!bSucc)
                    {
                        break;
                    }
                }
            }
            //7.返回参数
            JSONObject jbResult = new JSONObject();
            jbResult.put("bSucc", Boolean.toString(bSucc));
            result = jbResult.toString();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        }
        catch (Exception ex)
        {
            errorMsg = "校验CCD发送的出货地数据发生异常：" + ex.getMessage();
            log.error(errorMsg, ex.getMessage());
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
