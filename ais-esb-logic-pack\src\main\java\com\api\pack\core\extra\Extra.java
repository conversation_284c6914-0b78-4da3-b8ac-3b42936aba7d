package com.api.pack.core.extra;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.api.base.IMybatisBasic;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 额外参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("a_pack_extra")
public class Extra extends IMybatisBasic implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @TableId(value = "extra_id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "编码")
    @TableField(value = "extra_code")
    private String code;

    @ApiModelProperty(value = "值")
    @TableField(value = "extra_value")
    private String value;

    @ApiModelProperty(value = "描述")
    @TableField(value = "extra_desc")
    private String desc;
}
