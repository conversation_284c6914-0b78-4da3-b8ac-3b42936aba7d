package com.api.pmc.core.interf;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * EMS接口
 * 1.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfEmsController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //EMS过站上报
    @RequestMapping(value = "/PmcCoreEmsReportStation", method = {RequestMethod.GET})
    public String PmcCoreEmsReportStation(HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/interf/PmcCoreEmsReportStation";
        String selectResult="";
        String errorMsg="";
        try{
            String stationCode=request.getParameter("stationCode");//工位号
            String agvNum=request.getParameter("agvNum");//AGV编号
            String makeOrder=request.getParameter("makeOrder");//订单号
            String dms=request.getParameter("dms");//DMS号

            log.info("EMS过站上报:[stationCode|agvNum|makeOrder|dms]"+stationCode+"|"+agvNum+"|"+makeOrder+"|"+dms);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "EMS过站上报异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
