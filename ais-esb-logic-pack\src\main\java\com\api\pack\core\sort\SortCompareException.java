package com.api.pack.core.sort;

import lombok.EqualsAndHashCode;

/**
 * <p>
 * 分选比较异常类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-29
 */
@EqualsAndHashCode(callSuper = true)
public class SortCompareException extends SortException
{
    public SortCompareException(String message)
    {
        super(message);
    }

    public SortCompareException(String message, int code)
    {
        super(message, code);
    }
}
