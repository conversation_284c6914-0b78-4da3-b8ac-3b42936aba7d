package com.api.pmc.project.bq;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 北汽分装工位屏数据接口
 * 1.零件分装线工位屏数据
 * 2.轮胎分装线工位屏数据
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-29
 */
@RestController
@Slf4j
@RequestMapping("/pmc/project/bq")
public class PmcBqFzStationScreenDataController {
    @Autowired
    private CFuncDbSqlMapper cFuncDbSqlMapper;

    /**
     * 零件分装线工位屏数据
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/PmcBqPartStationScreenData", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcBqPartStationScreenData(@RequestBody JSONObject jsonParas){
        String selectResult="";
        String errorMsg="";
        String station_code = jsonParas.getString("station_code");
        try{
            List<Map<String,Object>> statusList = cFuncDbSqlMapper.ExecSelectSql("select make_order from d_pmc_me_station_status where station_code = '"+station_code+"' and make_order <> ''");
            String makeOrder = "";
            if(!CollectionUtils.isEmpty(statusList)){
                makeOrder = (String) statusList.get(0).get("make_order");
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime now =  LocalDateTime.now();
            LocalDateTime startOfDay = now.with(LocalTime.MIN);
            LocalDateTime endOfDay = now.with(LocalTime.MAX);

            List<Map<String, Object>> itemList=cFuncDbSqlMapper.ExecSelectSql("SELECT mo.work_status,mo.make_order,fo.vin,fo.main_material_code FROM d_pmc_me_station_mo mo left join d_pmc_me_flow_online fo on mo.make_order = fo.make_order" +
                    //" where mo.creation_date >= '"+formatter.format(startOfDay)+"' and mo.creation_date <= '"+formatter.format(endOfDay)+"' and mo.station_code = '"+station_code+"'\n" +
                    " where mo.station_code = '"+station_code+"'" +
                    " and mo_work_order >= (SELECT coalesce(mo_work_order,0) FROM d_pmc_me_station_mo WHERE station_code = '"+station_code+"' and work_status = 'FINISH' ORDER BY mo_work_order desc LIMIT 1)" +
                    " order by mo.mo_work_order limit 7");
            // 记录最后一个完成订单的数据下标
            // int lastFinishIndex = 0;
            // 设置序号
            for (int i = 0; i < itemList.size(); i++) {
                Map<String, Object> map = itemList.get(i);
                String work_status = (String) map.get("work_status");
                String make_order = (String) map.get("make_order");
                map.put("seq", String.format("%03d", (i+1)));
                if("FINISH".equals(work_status)){
                    // lastFinishIndex = i;
                    map.put("work_status_name","已完成");
                } else if("PLAN".equals(work_status)){
                    map.put("work_status_name","等待中");
                }
                if("PLAN".equals(work_status) && make_order.equals(makeOrder)){
                    map.put("work_status_name","进行中");
                }
            }
//            if(!CollectionUtils.isEmpty(itemList)){
//                int toIndex = (lastFinishIndex + 7) > (itemList.size() -1) ? (itemList.size() - 1) : (lastFinishIndex + 7);
//                itemList = itemList.subList(lastFinishIndex, toIndex);
//            }
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",itemList.size());
        }
        catch (Exception ex){
            errorMsg= "零件分装线工位屏数据查询异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 轮胎分装线工位屏订单数据
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/PmcBqTyreStationScreenLeftData", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcBqTyreStationScreenLeftData(@RequestBody JSONObject jsonParas){
        String selectResult="";
        String errorMsg="";
        String station_code = jsonParas.getString("station_code");
        try{
            JSONObject result = new JSONObject();
            List<Map<String,Object>> statusList = cFuncDbSqlMapper.ExecSelectSql("select make_order,dms,item_project from d_pmc_me_station_status where station_code = '"+station_code+"' and make_order <> ''");
            String makeOrder = "";
            if(!CollectionUtils.isEmpty(statusList)){
                makeOrder = (String) statusList.get(0).get("make_order");
            }

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime now =  LocalDateTime.now();
            LocalDateTime startOfDay = now.with(LocalTime.MIN);
            LocalDateTime endOfDay = now.with(LocalTime.MAX);

            List<Map<String, Object>> itemList=cFuncDbSqlMapper.ExecSelectSql("SELECT fo.sale_make_order,mo.work_status,mo.make_order,fo.vin,fo.main_material_code FROM d_pmc_me_station_mo mo left join d_pmc_me_flow_online fo on mo.make_order = fo.make_order" +
                    //" where mo.creation_date >= '"+formatter.format(startOfDay)+"' and mo.creation_date <= '"+formatter.format(endOfDay)+"' and mo.station_code = '"+station_code+"'\n" +
                    " where mo.station_code = '"+station_code+"'" +
                    " and mo_work_order >= (SELECT coalesce(mo_work_order,0) FROM d_pmc_me_station_mo WHERE station_code = '"+station_code+"' and work_status = 'FINISH' ORDER BY mo_work_order desc LIMIT 1)" +
                    " order by mo.mo_work_order limit 15");
            // 记录最后一个完成订单的数据下标
            // int lastFinishIndex = 0;
            // 设置序号
            for (int i = 0; i < itemList.size(); i++) {
                Map<String, Object> map = itemList.get(i);
                String work_status = (String) map.get("work_status");
                String make_order = (String) map.get("make_order");
                String sale_make_order = (String) map.get("sale_make_order");
                map.put("seq", String.format("%03d", (i+1)));
                if("FINISH".equals(work_status)){
                    // lastFinishIndex = i;
                    map.put("work_status_name","已完成");
                } else if("PLAN".equals(work_status)){
                    map.put("work_status_name","等待中");
                }
                if("PLAN".equals(work_status) && make_order.equals(makeOrder)){
                    map.put("work_status_name","进行中");
                }

                map.put("make_order", sale_make_order);
            }
//            if(!CollectionUtils.isEmpty(itemList)){
//                int toIndex = (lastFinishIndex + 7) > (itemList.size() -1) ? (itemList.size() - 1) : (lastFinishIndex + 7);
//                itemList = itemList.subList(lastFinishIndex, toIndex);
//            }
            result.put("orderList", itemList);
            result.put("code", 0);
            selectResult=result.toString();
        }
        catch (Exception ex){
            errorMsg= "轮胎分装线工位屏数据查询异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 轮胎分装线工位屏轮胎数据
     * @param jsonParas
     * @return
     */
    @RequestMapping(value = "/PmcBqTyreStationScreenRightData", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcBqTyreStationScreenRightData(@RequestBody JSONObject jsonParas){
        String selectResult="";
        String errorMsg="";
        String station_code = jsonParas.getString("station_code");
        try{
            JSONObject result = new JSONObject();
            List<Map<String,Object>> statusList = cFuncDbSqlMapper.ExecSelectSql("select make_order,dms,item_project from d_pmc_me_station_status where station_code = '"+station_code+"' and make_order <> ''");
            String makeOrder = "";
            if(!CollectionUtils.isEmpty(statusList)){
                makeOrder = (String) statusList.get(0).get("make_order");
                String dms = (String) statusList.get(0).get("dms");
                String item_project = (String) statusList.get(0).get("item_project");
                String feature_value_desc = "";
                List<Map<String,Object>> featureList =  cFuncDbSqlMapper.ExecSelectSql("select feature_value_desc from d_pmc_fmod_feature where dms = '"+dms+"' and item_project = '"+item_project+"' and feature = 'Y29'");
                if(!CollectionUtils.isEmpty(featureList)) feature_value_desc = (String) featureList.get(0).get("feature_value_desc");

                List<Map<String,Object>> tyreList =  cFuncDbSqlMapper.ExecSelectSql("select sort,order_prod,tyre_material_code,tyre_name,tyre_number,checked_flag,checked_result from d_pmc_fmod_tyre_sort where order_prod = '"+makeOrder+"' and enable_flag = 'Y' order by sort::numeric");
                for (int i = 0; i < tyreList.size(); i++) {
                    Map<String, Object> map = tyreList.get(i);
                    int sort = MapUtils.getIntValue(map, "sort");
                    map.put("seq", String.format("%02d", sort));
                    map.put("feature_value_desc", feature_value_desc);
                }
                result.put("tyreList", tyreList);
            }
            result.put("code", 0);
            selectResult=result.toString();
        }
        catch (Exception ex){
            errorMsg= "轮胎分装线工位屏数据查询异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}

