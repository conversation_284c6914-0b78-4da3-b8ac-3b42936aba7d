package com.api.dcs.project.fjrm.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * WMS接受流程功能函数
 * 1.W10A1 废料调运计划(暂时不用)
 *
 * 4.W10B1 物料请求总计划
 * 5.W10B2 物料请求总计划确认
 *
 * 6.W10C1 化学成分报文
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
@Slf4j
public class DcsFjrmWmsRecvFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private DcsFjrmInterfCommon dcsFjrmInterfCommon;

    //1.W10A1 废料调运计划(暂时不用)
    public JSONObject RecvW10a1Task(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "RecvW10a1Task";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            //1.接受参数
            String Plan_ID=jsonParas.getString("Plan_ID");//唯一ID(GUID)
            String Source=jsonParas.getString("Source");//e.g. 码头编号, WMS
            String Destination=jsonParas.getString("Destination");//e.g. 码头编号, WMS
            String PositionCode=jsonParas.getString("PositionCode");//如果指定料位，则指定调运到具体料位
            String Smelt_ID=jsonParas.getString("Smelt_ID");//炉次号
            String Material_Code=jsonParas.getString("Material_Code");//物料名称代码，MES提供清单
            String Grade=jsonParas.getString("Grade");//如果是废料，提供合金
            String Lot_No=jsonParas.getString("Lot_No");//部分物料比如再生锭有批次号
            String Weight=jsonParas.getString("Weight");//重量(kg)
            String Spare=jsonParas.getString("Spare");//废料框号
            //创建返回信息
            responseParas = dcsFjrmInterfCommon.CreateResponse(200, "OK");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "W10A1接收成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.W10B1 物料请求总计划
    public JSONObject RecvW10b1Task(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "RecvW10b1Task";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String userID="MES";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.接受参数
            String Batch_ID=jsonParas.getString("Batch_ID");//唯一ID
            String Smelt_ID=jsonParas.getString("Smelt_ID");//炉次号
            String Material_Code=jsonParas.getString("Material_Code");//物料名称代码，MES提供清单
            String Grade=jsonParas.getString("Grade");//如果不为空，按合金牌号在库存中查询
            String Lot_No=jsonParas.getString("Lot_No");//部分物料比如再生锭有批次号，如果不为空，按批次号在库存中查询
            String Weight=jsonParas.getString("Weight");//总重量(KG)
            String Spare=jsonParas.getString("Spare");//出口编号,TrainOut2,TrainOut4,TrainOut6,TrainOut8
            String Material_Shape_Code=jsonParas.getString("Material_Shape_Code");//废料形态代码，双方约定
            String Composition_Error_Code=jsonParas.getString("Composition_Error_Code");//成分异常代码，由WMS维护
            String ErrorMin=jsonParas.getString("ErrorMin");//化学成分异常最小值(10-5%)
            String ErrorMax=jsonParas.getString("ErrorMax");//化学成分异常最大值

            //2.判断参数信息
            if(Batch_ID.equals("")){
                errorMsg="任务编号不能为空";
                responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(Material_Code.equals("")){
                errorMsg="物料名称不能为空";
                responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(Weight.equals("")){
                errorMsg="总重量不能为空";
                responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            Integer WeightNum = Integer.valueOf(Weight);
            if(WeightNum<=0){
                errorMsg="总重量不能<=0";
                responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //3.判断任务是否存在
            String sqlIntefTaskSel="select task_id, task_status " +
                    "from b_dcs_wms_intef_task " +
                    "where task_num='"+Batch_ID+"' ";
            List<Map<String, Object>> itemListIntefTask=cFuncDbSqlExecute.ExecSelectSql(userID,sqlIntefTaskSel,
                    false,request,apiRoutePath);
            if(itemListIntefTask!=null && itemListIntefTask.size()>0){
                String task_id=itemListIntefTask.get(0).get("task_id").toString();
                String task_status=itemListIntefTask.get(0).get("task_status").toString();
                //判断：是否已执行
                if(!"PLAN".equals(task_status)){
                    errorMsg="当前任务号："+Batch_ID+"，状态是："+task_status+"，不能修改信息";
                    responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                //存在，修改
                String sqlUpdIntefTask="update b_dcs_wms_intef_task set " +
                        "last_updated_by='MES'," +
                        "last_update_date='"+ CFuncUtilsSystem.GetNowDateTime("") +"'," +
                        "lot_num='"+Lot_No+"', " +
                        "material_code='"+Material_Code+"', " +
                        "width="+Weight+", " +
                        "error_min="+ErrorMin+", " +
                        "error_max="+ErrorMax+" " +
                        "where task_id="+task_id+" ";
                cFuncDbSqlExecute.ExecUpdateSql(userID,sqlUpdIntefTask,false,request,apiRoutePath);
            }
            else {
                //不存在，新增
                String taskNum = Batch_ID;//task_num
                String taskFrom="MES";//任务来源
                String task_way="AUTO";//任务方式(自动/半自动/手动)
                String task_type="FULL_OUT_TASK";//任务类型：FULL_OUT_TASK 满框出库
                String ware_house="";//库区域
                String from_stock_code="";//起始库位
                String to_stock_code="";//目标库位
                //任务状态 PROD_TASK_STATUS：PLAN-计划、WORK-进行中、FINISH-完成、CANCEL-取消
                String task_status="PLAN";
                String wharf_code="";//码头编码
                String waste_box_code=""; //废料框编码
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                long taskIdSeq = cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_intef_task_id_seq", false);
                String insertSql = "INSERT INTO b_dcs_wms_intef_task (created_by, creation_date, " +
                        "task_id, task_num, task_from, task_way, task_type, ware_house, " +
                        "from_stock_code, to_stock_code, lot_num, material_code, width, " +
                        "error_min, error_max, task_order, task_status, wharf_code, waste_box_code) " +
                        "VALUES ('" + userID + "','" + nowDateTime + "','" + taskIdSeq + "','" +
                        taskNum + "','" +
                        taskFrom + "','" +
                        task_way + "','" +
                        task_type + "','" +
                        ware_house + "','" +
                        from_stock_code + "','" +
                        to_stock_code + "','" +
                        Lot_No + "','" +
                        Material_Code + "'," +
                        Weight + "," +
                        ErrorMin + "," +
                        ErrorMax + "," +
                        taskIdSeq + ",'" +
                        task_status + "','" +
                        wharf_code + "','" +
                        waste_box_code + "')";
                cFuncDbSqlExecute.ExecUpdateSql(userID, insertSql, true, request, apiRoutePath);
            }
            //创建返回信息
            responseParas = dcsFjrmInterfCommon.CreateResponse(200, "OK");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "W10B1接收成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //5.W10B2 物料请求总计划确认
    public JSONObject RecvW10b2Task(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "RecvW10b2Task";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String userID="MES";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.接受参数
            String Batch_ID=jsonParas.getString("Batch_ID");//唯一ID
            String Is_Check=jsonParas.getString("Is_Check");//e.g:Y 确认，N，不同意，MES将重新发送批次计划

            //2.判断参数信息
            if(Batch_ID.equals("")){
                errorMsg="任务编号不能为空";
                responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //3.判断任务是否存在
            String sqlIntefTaskSel="select task_id, task_status " +
                    "from b_dcs_wms_intef_task " +
                    "where task_num='"+Batch_ID+"' ";
            List<Map<String, Object>> itemListIntefTask=cFuncDbSqlExecute.ExecSelectSql(userID,sqlIntefTaskSel,
                    false,request,apiRoutePath);
            if(itemListIntefTask==null || itemListIntefTask.size()<=0) {
                errorMsg="任务编号："+Batch_ID+"，不存在";
                responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if(itemListIntefTask!=null && itemListIntefTask.size()>0){
                String task_id=itemListIntefTask.get(0).get("task_id").toString();
                String task_status=itemListIntefTask.get(0).get("task_status").toString();
                //状态：不是 PLAN、CANCEL 报错
                if(!"PLAN".equals(task_status) && !"CANCEL".equals(task_status)){
                    errorMsg="任务编号当前状态："+task_status+"，不允许更改";
                    responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }

                if("N".equals(Is_Check)){
                    task_status="CANCEL";
                } else {
                    task_status="PLAN";
                }
                //存在，修改
                String sqlUpdIntefTask="update b_dcs_wms_intef_task set " +
                        "last_updated_by='MES'," +
                        "last_update_date='"+ CFuncUtilsSystem.GetNowDateTime("") +"'," +
                        "task_status='"+task_status+"' " +
                        "where task_id="+task_id+" ";
                cFuncDbSqlExecute.ExecUpdateSql(userID,sqlUpdIntefTask,false,request,apiRoutePath);
            }
            //创建返回信息
            responseParas = dcsFjrmInterfCommon.CreateResponse(200, "OK");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "W10B2接收成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //6.W10C1 化学成分报文
    public JSONObject RecvW10c1Task(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "RecvW10c1Task";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String userID="MES";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.接受参数
            String Lot_No=jsonParas.getString("Lot_No");//唯一ID(批次号)
            String Si=jsonParas.getString("Si");//10-5%
            String Fe=jsonParas.getString("Fe");//10-5%
            String Cu=jsonParas.getString("Cu");//10-5%
            String Mn=jsonParas.getString("Mn");//10-5%
            String Mg=jsonParas.getString("Mg");//10-5%
            String Ni=jsonParas.getString("Ni");//10-5%
            String Zn=jsonParas.getString("Zn");//10-5%
            String Ti=jsonParas.getString("Ti");//10-5%
            String Cr=jsonParas.getString("Cr");//10-5%
            String Na=jsonParas.getString("Na");//10-5%
            String Ca=jsonParas.getString("Ca");//10-5%
            if (Si == null ||"".equals(Si)) Si = "0";
            if (Fe == null ||"".equals(Fe)) Fe = "0";
            if (Cu == null ||"".equals(Cu)) Cu = "0";
            if (Mn == null ||"".equals(Mn)) Mn = "0";
            if (Mg == null ||"".equals(Mg)) Mg = "0";
            if (Ni == null ||"".equals(Ni)) Ni = "0";
            if (Zn == null ||"".equals(Zn)) Zn = "0";
            if (Ti == null ||"".equals(Ti)) Ti = "0";
            if (Cr == null ||"".equals(Cr)) Cr = "0";
            if (Na == null ||"".equals(Na)) Na = "0";
            if (Ca == null ||"".equals(Ca)) Ca = "0";

            //2.判断参数信息
            if(Lot_No.equals("")){
                errorMsg="批次号不能为空";
                responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //3.判断化学成分是否存在
            String sqlChemistrySel="select chemistry_id " +
                    "from b_dcs_wms_chemistry " +
                    "where lot_num='"+Lot_No+"' ";
            List<Map<String, Object>> itemListChemistry=cFuncDbSqlExecute.ExecSelectSql(userID,sqlChemistrySel,
                    false,request,apiRoutePath);
            if(itemListChemistry!=null && itemListChemistry.size()>0){
                String chemistry_id=itemListChemistry.get(0).get("chemistry_id").toString();
                //判断：是否已执行
                String sqlUpdIntefTask="update b_dcs_wms_chemistry set " +
                        "last_updated_by='MES'," +
                        "last_update_date='"+ CFuncUtilsSystem.GetNowDateTime("") +"'," +
                        "lot_num='"+Lot_No+"', " +
                        "si="+Si+", " +
                        "fe="+Fe+", " +
                        "cu="+Cu+", " +
                        "mn="+Mn+", " +
                        "mg="+Mg+", " +
                        "ni="+Ni+", " +
                        "zn="+Zn+", " +
                        "ti="+Ti+", " +
                        "cr="+Cr+", " +
                        "na="+Na+", " +
                        "ca="+Ca+" " +
                        "where chemistry_id="+chemistry_id+" ";
                cFuncDbSqlExecute.ExecUpdateSql(userID,sqlUpdIntefTask,false,request,apiRoutePath);
            }
            else {
                //不存在，新增
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                long chemistryIdSeq = cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_chemistry_id_seq", false);
                String insertSql = "INSERT INTO b_dcs_wms_chemistry (created_by, creation_date, " +
                        "chemistry_id, lot_num, " +
                        "si, fe, cu, mn, mg,  " +
                        "ni, zn, ti, cr, na, ca, enable_flag) " +
                        "VALUES ('" + userID + "','" + nowDateTime + "','" + chemistryIdSeq + "','" +
                        Lot_No + "'," +
                        Si + "," +
                        Fe + "," +
                        Cu + "," +
                        Mn + "," +
                        Mg + "," +
                        Ni + "," +
                        Zn + "," +
                        Ti + "," +
                        Cr + "," +
                        Na + "," +
                        Ca + ",'Y')";
                cFuncDbSqlExecute.ExecUpdateSql(userID, insertSql, true, request, apiRoutePath);
            }
            //创建返回信息
            responseParas = dcsFjrmInterfCommon.CreateResponse(200, "OK");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "W10C1接收成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = dcsFjrmInterfCommon.CreateResponse(500, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }






}
