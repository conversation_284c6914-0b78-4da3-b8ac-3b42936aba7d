package com.api.mes.project.byd;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mongodb.client.MongoCursor;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 1.物料扫描按照扫描规则处理
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-20
 */
@RestController
@Slf4j
@RequestMapping("/mes/project/byd")
public class MesBydMeMaterialController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.物料扫描验证
    @Transactional
    @RequestMapping(value = "/MesMeMaterialScan", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesMeMaterialScan(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/project/byd/MesMeMaterialScan";
        String tranResult="";
        String errorMsg="";
        String userName="-1";
        String flowTableName="c_mes_me_station_flow";
        String materialTableName="c_mes_me_station_material";
        try{
            userName=jsonParas.getString("user_name");
            String station_code=jsonParas.getString("station_code");
            String barcode=jsonParas.getString("barcode");
            if(barcode==null || barcode.equals("")){
                errorMsg="扫描条码不能为空";
                tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            //1.判断当前工序是否为物料扫描工序,如果不是给与提示
            String sqlCrPdure="select proceduce_id,proceduce_type " +
                    "from c_mes_me_cr_pdure " +
                    "where station_code='"+station_code+"' and proceduce_status='PLAN' " +
                    "order by work_order_by LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListCrPdure=cFuncDbSqlExecute.ExecSelectSql(userName,sqlCrPdure,false,request,apiRoutePath);
            String proceduce_id="";
            String proceduce_type="";
            String make_order="";
            String small_model_type="";
            String station_flow_id="";
            if(itemListCrPdure!=null && itemListCrPdure.size()>0){
                proceduce_id=itemListCrPdure.get(0).get("proceduce_id").toString();
                proceduce_type=itemListCrPdure.get(0).get("proceduce_type").toString();
            }
            if(proceduce_type==null || !proceduce_type.equals("MATERIAL_SCAN")){
                errorMsg="当前不是扫描工序,不能在作业指导页面进行物料扫描";
                tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            //2.获取订单
            String sqlMis="select COALESCE(make_order,'') make_order," +
                    "COALESCE(small_model_type,'') small_model_type " +
                    "from c_mes_me_station_mis " +
                    "where station_code='"+station_code+"'";
            List<Map<String, Object>> itemListMis=cFuncDbSqlExecute.ExecSelectSql(userName,sqlMis,false,request,apiRoutePath);
            if(itemListMis!=null && itemListMis.size()>0){
                make_order=itemListMis.get(0).get("make_order").toString();
                small_model_type=itemListMis.get(0).get("small_model_type").toString();
            }
            if(make_order==null || make_order.equals("")){
                errorMsg="未找到当前工位订单号,此时不能进行扫描";
                tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            //3.根据订单查找到物料扫描规则
            String sqlMoRecipe="select e.prod_line_code," +
                    "d.material_rule_type,d.material_rule_code,d.special_flag," +
                    "COALESCE(d.special_func_name,'') special_func_name," +
                    "d.check_len_flag,COALESCE(d.len_value,0) len_value," +
                    "d.check_code_flag,COALESCE(d.check_code_way,'') check_code_way," +
                    "COALESCE(d.code_start_index,0) code_start_index,COALESCE(d.code_end_index,0) code_end_index," +
                    "d.check_count_flag,COALESCE(d.check_count_way,'') check_count_way," +
                    "COALESCE(d.count_start_index,0) count_start_index,COALESCE(d.count_end_index,0) count_end_index," +
                    "d.check_batch_flag,COALESCE(d.check_batch_way,'') check_batch_way," +
                    "COALESCE(d.batch_start_index,0) batch_start_index,COALESCE(d.batch_end_index,0) batch_end_index," +
                    "d.check_supplier_flag,COALESCE(d.check_supplier_way,'') check_supplier_way," +
                    "COALESCE(d.supplier_start_index,0) supplier_start_index,COALESCE(d.supplier_end_index,0) supplier_end_index," +
                    "d.check_model_flag,COALESCE(d.check_model_way,'') check_model_way," +
                    "COALESCE(d.model_start_index,0) model_start_index,COALESCE(d.model_end_index,0) model_end_index " +
                    "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " +
                    "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " +
                    "on b.recipe_id=c.recipe_id inner join c_mes_fmod_recipe_material_rule d " +
                    "on c.recipe_id=d.recipe_id inner join sys_fmod_prod_line e " +
                    "on a.prod_line_id=e.prod_line_id inner join sys_fmod_station f " +
                    "on e.prod_line_id=f.prod_line_id " +
                    "where a.make_order='"+make_order+"' and f.station_code='"+station_code+"' " +
                    "and c.recipe_type='SCANRULE' and d.enable_flag='Y' " +
                    "order by d.material_rule_type,d.material_rule_code";
            List<Map<String, Object>> itemListRecipe=cFuncDbSqlExecute.ExecSelectSql(userName,sqlMoRecipe,false,request,apiRoutePath);
            if(itemListRecipe==null || itemListRecipe.size()<=0){
                errorMsg="未找到当前工位订单号{"+make_order+"},查找到物料扫描规则配方";
                tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String prod_line_code=itemListRecipe.get(0).get("prod_line_code").toString();

            //4.查询当前过站信息ID--不需要对过站进行判断
            /*Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(flowTableName).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                station_flow_id=docItemBigData.getString("station_flow_id");
            }
            if(station_flow_id==null || station_flow_id.equals("")){
                errorMsg="当前工位当前工件未保存过站信息,在没保存过站信息前不允许扫描物料,否则无法保存物料追溯信息";
                tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }*/

            //5.查询当前工序BOM批次规则代码与精准追溯代码
            List<String> lstExactRule=new ArrayList<>();
            List<String> lstBatchRule=new ArrayList<>();
            String sqlPdureBom="select " +
                    "COALESCE(material_rule_exact,'') material_rule_exact," +
                    "COALESCE(material_rule_card,'') material_rule_card " +
                    "from c_mes_me_cr_pdure_bom " +
                    "where proceduce_id="+proceduce_id+" and station_code='"+station_code+"'";
            List<Map<String, Object>> itemListBom=cFuncDbSqlExecute.ExecSelectSql(userName,sqlPdureBom,false,request,apiRoutePath);
            if(itemListBom==null || itemListBom.size()<=0){
                errorMsg="当前工位当前订单{"+make_order+"}不需要进行物料扫描";
                tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            for(Map<String, Object> map : itemListBom){
                String material_rule_exact=map.get("material_rule_exact").toString();
                String material_rule_card=map.get("material_rule_card").toString();
                if(material_rule_exact!=null && !material_rule_exact.equals("")){
                    if(!lstExactRule.contains(material_rule_exact)) lstExactRule.add(material_rule_exact);
                }
                else{
                    if(material_rule_card!=null && !material_rule_card.equals("")){
                        if(!lstBatchRule.contains(material_rule_card)) lstBatchRule.add(material_rule_card);
                    }
                }
            }
            //6.将规则打包成HASH表,便于快速定位查询
            Map<String, Object> mapExactRule=new HashMap<>();
            Map<String, Object> mapBatchRule=new HashMap<>();
            for(Map<String, Object> map:itemListRecipe){
                String material_rule_type=map.get("material_rule_type").toString();
                String material_rule_code=map.get("material_rule_code").toString();
                if(material_rule_type.equals("EXACT")){
                    if(!mapExactRule.containsKey(material_rule_code) && lstExactRule.contains(material_rule_code)){
                        mapExactRule.put(material_rule_code,map);
                    }
                }
                else if(material_rule_type.equals("BATCH")){
                    if(!mapBatchRule.containsKey(material_rule_code) && lstBatchRule.contains(material_rule_code)){
                        mapBatchRule.put(material_rule_code,map);
                    }
                }
            }
            //7.判断是否需要进行扫描
            if(mapExactRule.size()<=0 && mapBatchRule.size()<=0){
                errorMsg="当前工位当前订单{"+make_order+"}未设置精准追溯物料规则或者批次追溯物料规则";
                tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String material_code="";
            String material_batch="";
            String material_supplier="";
            String supplier_des="";
            String lable_barcode="";
            String exact_barcode="";
            Integer barcode_count=0;
            String material_type="";
            //8.先对精准追溯进行判断
            Iterator<String> iterator=mapExactRule.keySet().iterator();
            while (iterator.hasNext()){
                String material_rule_code=iterator.next();
                Map<String, Object> mapItem=(Map<String, Object>)mapExactRule.get(material_rule_code);
                JSONObject jbResult=ResolveBarCodeRule(barcode,mapItem,small_model_type);
                if(jbResult!=null){
                    material_code=jbResult.getString("material_code");
                    material_batch=jbResult.getString("material_batch");
                    material_supplier=jbResult.getString("material_supplier");
                    exact_barcode=barcode;
                    material_type="精准追溯物料";
                    //判断物料号是否存在,若不存在继续判断
                    String sqlExistM="select count(1) " +
                            "from c_mes_me_cr_pdure_bom " +
                            "where proceduce_id="+proceduce_id+" and station_code='"+station_code+"' " +
                            "and material_code='"+material_code+"' ";
                    Integer existCount=cFuncDbSqlResolve.GetSelectCount(sqlExistM);
                    if(existCount>0) break;
                }
            }
            if(exact_barcode.equals("")){//代表不是精追溯
                iterator=mapBatchRule.keySet().iterator();
                while (iterator.hasNext()){
                    String material_rule_code=iterator.next();
                    Map<String, Object> mapItem=(Map<String, Object>)mapBatchRule.get(material_rule_code);
                    JSONObject jbResult=ResolveBarCodeRule(barcode,mapItem,small_model_type);
                    if(jbResult!=null){
                        material_code=jbResult.getString("material_code");
                        material_batch=jbResult.getString("material_batch");
                        material_supplier=jbResult.getString("material_supplier");
                        barcode_count=jbResult.getInteger("barcode_count");
                        lable_barcode=barcode;
                        material_type="批次物料";
                        //判断物料号是否存在,若不存在继续判断
                        String sqlExistM="select count(1) " +
                                "from c_mes_me_cr_pdure_bom " +
                                "where proceduce_id="+proceduce_id+" and station_code='"+station_code+"' " +
                                "and material_code='"+material_code+"' ";
                        Integer existCount=cFuncDbSqlResolve.GetSelectCount(sqlExistM);
                        if(existCount>0) break;
                    }
                }
            }
            if(material_code==null || material_code.equals("")){
                errorMsg="该物料条码{"+barcode+"}不符合设定配方的条码解析规则,请工艺人员确认物料规则";
                tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            //查询供应商描述
            String sqlSuppier="select supplier_des " +
                    "from c_mes_fmod_supplier " +
                    "where supplier_code='"+material_supplier+"' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListsqlSuppier=cFuncDbSqlExecute.ExecSelectSql(userName,sqlSuppier,false,request,apiRoutePath);
            if(itemListsqlSuppier!=null && itemListsqlSuppier.size()>0){
                supplier_des=itemListsqlSuppier.get(0).get("supplier_des").toString();
            }
            //判断BOM是否存在
            String sqlMaterial="select " +
                    "material_id,material_code,material_des,usage," +
                    "COALESCE(material_uom,'') material_uom,COALESCE(material_attr,'') material_attr," +
                    "main_material_flag,verify_flag,batch_flag,stock_flag," +
                    "COALESCE(bom_version,'') bom_version,fchong_flag,verify_finish_flag,batch_finish_flag " +
                    "from c_mes_me_cr_pdure_bom " +
                    "where proceduce_id="+proceduce_id+" and station_code='"+station_code+"' " +
                    "and material_code='"+material_code+"' ";
            List<Map<String, Object>> itemListMaterial=cFuncDbSqlExecute.ExecSelectSql(userName,sqlMaterial,false,request,apiRoutePath);
            if(itemListMaterial==null  || itemListMaterial.size()<=0){
                errorMsg="该物料条码{"+barcode+"},解析出来的物料号{"+material_code+"},属于物料类型{"+material_type+"}不在订单{"+make_order+"}物料BOM中";
                tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return tranResult;
            }
            String material_id="";
            String fchong_flag="";
            for(Map<String, Object> map : itemListMaterial){
                String verify_finish_flag=map.get("verify_finish_flag").toString();
                String verify_flag=map.get("verify_flag").toString();
                fchong_flag=map.get("fchong_flag").toString();
                if(verify_flag.equals("Y")){
                    if(verify_finish_flag.equals("Y")) continue;
                    material_id=map.get("material_id").toString();
                    break;
                }
            }
            //1.先对精准追溯进行UPDATE
            if(exact_barcode!=null && !exact_barcode.equals("")){
                //判断是否重复扫码
                if(fchong_flag.equals("Y")){
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
                    queryBigData.addCriteria(Criteria.where("exact_barcode").is(exact_barcode));
                    long exactCount=mongoTemplate.getCollection(materialTableName).countDocuments(queryBigData.getQueryObject());
                    if(exactCount>0){
                        errorMsg="扫描精追物料条码{"+exact_barcode+"}已绑定工件编码,配方设定该物料不允许重复绑定,若需要扫描成功该物料需要进入物料解绑页面进行解绑";
                        tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return tranResult;
                    }
                }
                String sqlUpdate="update c_mes_me_cr_pdure_bom set " +
                        "last_updated_by='"+userName+"'," +
                        "last_update_date='"+CFuncUtilsSystem.GetNowDateTime("")+"'," +
                        "verify_finish_flag='Y'," +
                        "batch_finish_flag='Y'," +
                        "material_batch='"+material_batch+"'," +
                        "material_supplier='"+material_supplier+"'," +
                        "supplier_des='"+supplier_des+"'," +
                        "lable_barcode=''," +
                        "exact_barcode='"+exact_barcode+"' " +
                        "where material_code='"+material_code+"'";
                if(!material_id.equals("")){
                    sqlUpdate="update c_mes_me_cr_pdure_bom set " +
                            "last_updated_by='"+userName+"'," +
                            "last_update_date='"+CFuncUtilsSystem.GetNowDateTime("")+"'," +
                            "verify_finish_flag='Y'," +
                            "batch_finish_flag='Y'," +
                            "material_batch='"+material_batch+"'," +
                            "material_supplier='"+material_supplier+"'," +
                            "supplier_des='"+supplier_des+"'," +
                            "exact_barcode='"+exact_barcode+"' " +
                            "where material_id="+material_id;
                }
                cFuncDbSqlExecute.ExecUpdateSql(userName,sqlUpdate,true,request,apiRoutePath);
            }
            else{
                String sqlUpdate="update c_mes_me_cr_pdure_bom set " +
                        "last_updated_by='"+userName+"'," +
                        "last_update_date='"+CFuncUtilsSystem.GetNowDateTime("")+"'," +
                        "batch_finish_flag='Y'," +
                        "material_batch='"+material_batch+"'," +
                        "material_supplier='"+material_supplier+"'," +
                        "supplier_des='"+supplier_des+"'," +
                        "lable_barcode='"+lable_barcode+"' " +
                        "where material_code='"+material_code+"'";
                cFuncDbSqlExecute.ExecUpdateSql(userName,sqlUpdate,true,request,apiRoutePath);
                //更新批次原始表
                String sqlMaterialStockCount="select count(1) " +
                        "from c_mes_me_station_stock " +
                        "where station_code='"+station_code+"' and material_code='"+material_code+"'";
                Integer count=cFuncDbSqlResolve.GetSelectCount(sqlMaterialStockCount);
                if(count>0){
                    String sqlUpdate01="update c_mes_me_station_stock set " +
                            "last_updated_by='"+userName+"'," +
                            "last_update_date='"+CFuncUtilsSystem.GetNowDateTime("")+"'," +
                            "stock_count=stock_count+"+barcode_count+"," +
                            "material_batch='"+material_batch+"'," +
                            "material_supplier='"+material_supplier+"'," +
                            "supplier_des='"+supplier_des+"'," +
                            "lable_barcode='"+lable_barcode+"' " +
                            "where station_code='"+station_code+"' and material_code='"+material_code+"'";
                    cFuncDbSqlExecute.ExecUpdateSql(userName,sqlUpdate01,false,request,apiRoutePath);
                }
                else{
                    long stock_id=cFuncDbSqlResolve.GetIncreaseID("c_mes_me_station_stock_id_seq",false);
                    String sqlInsert01="insert into c_mes_me_station_stock " +
                            "(created_by,creation_date,stock_id,prod_line_code,station_code," +
                            "material_code,stock_count,material_batch,material_supplier," +
                            "supplier_des,lable_barcode,min_count,lable_left_count) values " +
                            "('"+userName+"','"+CFuncUtilsSystem.GetNowDateTime("")+"',"+stock_id+"," +
                            "'"+prod_line_code+"','"+station_code+"','"+material_code+"',"+barcode_count+"," +
                            "'"+material_batch+"','"+material_supplier+"','"+supplier_des+"','"+lable_barcode+"',0,0)";
                    cFuncDbSqlExecute.ExecUpdateSql(userName,sqlInsert01,false,request,apiRoutePath);
                }
            }
            tranResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "物料扫描验证发生异常"+ex.getMessage();
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }

    //按照规则解析条码
    private JSONObject ResolveBarCodeRule(String barCode,Map<String, Object> mapItem,String small_model_type) throws Exception{
        JSONObject jbResult=null;
        try{
            String material_code="";
            String material_batch="";
            String material_supplier="";
            Integer barcode_count=1;
            String material_model="";

            String material_rule_code=mapItem.get("material_rule_code").toString();
            String special_flag=mapItem.get("special_flag").toString();
            String special_func_name=mapItem.get("special_func_name").toString();
            String check_len_flag=mapItem.get("check_len_flag").toString();
            Integer len_value=Integer.parseInt(mapItem.get("len_value").toString());
            String check_code_flag=mapItem.get("check_code_flag").toString();
            String check_code_way=mapItem.get("check_code_way").toString();
            Integer code_start_index=Integer.parseInt(mapItem.get("code_start_index").toString());
            Integer code_end_index=Integer.parseInt(mapItem.get("code_end_index").toString());
            String check_count_flag=mapItem.get("check_count_flag").toString();
            String check_count_way=mapItem.get("check_count_way").toString();
            Integer count_start_index=Integer.parseInt(mapItem.get("count_start_index").toString());
            Integer count_end_index=Integer.parseInt(mapItem.get("count_end_index").toString());
            String check_batch_flag=mapItem.get("check_batch_flag").toString();
            String check_batch_way=mapItem.get("check_batch_way").toString();
            Integer batch_start_index=Integer.parseInt(mapItem.get("batch_start_index").toString());
            Integer batch_end_index=Integer.parseInt(mapItem.get("batch_end_index").toString());
            String check_supplier_flag=mapItem.get("check_supplier_flag").toString();
            String check_supplier_way=mapItem.get("check_supplier_way").toString();
            Integer supplier_start_index=Integer.parseInt(mapItem.get("supplier_start_index").toString());
            Integer supplier_end_index=Integer.parseInt(mapItem.get("supplier_end_index").toString());
            String check_model_flag=mapItem.get("check_model_flag").toString();
            String check_model_way=mapItem.get("check_model_way").toString();
            Integer model_start_index=Integer.parseInt(mapItem.get("model_start_index").toString());
            Integer model_end_index=Integer.parseInt(mapItem.get("model_end_index").toString());
            if(special_flag.equals("Y")){
                throw new Exception("该项目未定制化物料扫描规则,请工艺人员按照标准物料扫码规则定义配方");
            }
            //长度校验
            if(check_len_flag.equals("Y")){
                if(barCode.length()!=len_value) return jbResult;
            }
            //解析物料号
            if(check_code_flag.equals("Y")){
                if(code_start_index>=code_end_index){
                    throw new Exception("物料扫码规则{"+material_rule_code+"},物料号起始位{"+code_start_index+"}不能大于等于解析终止位{"+code_end_index+"}");
                }
                if(barCode.length()<code_end_index) return jbResult;
                material_code=barCode.substring(code_start_index,code_end_index);
                if(check_code_way.equals("LTRIM")) material_code=material_code.replaceAll("^[　 ]+", "");
                else if(check_code_way.equals("RTRIM")) material_code=material_code.replaceAll("[　 ]+$", "");
                else if(check_code_way.equals("TRIM")) material_code=material_code.trim();
                else if(check_code_way.equals("LZERO")) material_code=material_code.replaceAll("^(0+)", "");
                else if(check_code_way.equals("RZERO")) material_code=material_code.replaceAll("0*$", "");
                else if(check_code_way.equals("ZERO")) material_code=material_code.replaceAll("^(0+)", "").replaceAll("0*$", "");
            }
            //解析物料数量
            if(check_count_flag.equals("Y")){
                if(count_start_index>=count_end_index){
                    throw new Exception("物料扫码规则{"+material_rule_code+"},物料数量起始位{"+code_start_index+"}不能大于等于解析终止位{"+code_end_index+"}");
                }
                if(barCode.length()<count_end_index) return jbResult;
                String tempMaterialCount=barCode.substring(count_start_index,count_end_index);
                if(check_count_way.equals("LTRIM")) tempMaterialCount=tempMaterialCount.replaceAll("^[　 ]+", "");
                else if(check_count_way.equals("RTRIM")) tempMaterialCount=tempMaterialCount.replaceAll("[　 ]+$", "");
                else if(check_count_way.equals("TRIM")) tempMaterialCount=tempMaterialCount.trim();
                else if(check_count_way.equals("LZERO")) tempMaterialCount=tempMaterialCount.replaceAll("^(0+)", "");
                else if(check_count_way.equals("RZERO")) tempMaterialCount=tempMaterialCount.replaceAll("0*$", "");
                else if(check_count_way.equals("ZERO")) tempMaterialCount=tempMaterialCount.replaceAll("^(0+)", "").replaceAll("0*$", "");
                barcode_count=Integer.parseInt(tempMaterialCount);
            }
            //解析物料批次
            if(check_batch_flag.equals("Y")){
                if(batch_start_index>=batch_end_index){
                    throw new Exception("物料扫码规则{"+material_rule_code+"},物料批次起始位{"+code_start_index+"}不能大于等于解析终止位{"+code_end_index+"}");
                }
                if(barCode.length()<batch_end_index) return jbResult;
                material_batch=barCode.substring(batch_start_index,batch_end_index);
                if(check_batch_way.equals("LTRIM")) material_batch=material_batch.replaceAll("^[　 ]+", "");
                else if(check_batch_way.equals("RTRIM")) material_batch=material_batch.replaceAll("[　 ]+$", "");
                else if(check_batch_way.equals("TRIM")) material_batch=material_batch.trim();
                else if(check_batch_way.equals("LZERO")) material_batch=material_batch.replaceAll("^(0+)", "");
                else if(check_batch_way.equals("RZERO")) material_batch=material_batch.replaceAll("0*$", "");
                else if(check_batch_way.equals("ZERO")) material_batch=material_batch.replaceAll("^(0+)", "").replaceAll("0*$", "");
            }
            //解析出供应商代码
            if(check_supplier_flag.equals("Y")){
                if(supplier_start_index>=supplier_end_index){
                    throw new Exception("物料扫码规则{"+material_rule_code+"},物料供应商代码起始位{"+code_start_index+"}不能大于等于解析终止位{"+code_end_index+"}");
                }
                if(barCode.length()<supplier_end_index) return jbResult;
                material_supplier=barCode.substring(supplier_start_index,supplier_end_index);
                if(check_supplier_way.equals("LTRIM")) material_supplier=material_supplier.replaceAll("^[　 ]+", "");
                else if(check_supplier_way.equals("RTRIM")) material_supplier=material_supplier.replaceAll("[　 ]+$", "");
                else if(check_supplier_way.equals("TRIM")) material_supplier=material_supplier.trim();
                else if(check_supplier_way.equals("LZERO")) material_supplier=material_supplier.replaceAll("^(0+)", "");
                else if(check_supplier_way.equals("RZERO")) material_supplier=material_supplier.replaceAll("0*$", "");
                else if(check_supplier_way.equals("ZERO")) material_supplier=material_supplier.replaceAll("^(0+)", "").replaceAll("0*$", "");
            }
            //解析出物料型号
            if(check_model_flag.equals("Y")){
                if(model_start_index>=model_end_index){
                    throw new Exception("物料扫码规则{"+material_rule_code+"},物料机型起始位{"+code_start_index+"}不能大于等于解析终止位{"+code_end_index+"}");
                }
                if(barCode.length()<supplier_end_index) return jbResult;
                material_model=barCode.substring(model_start_index,model_end_index);
                if(check_model_way.equals("LTRIM")) material_model=material_model.replaceAll("^[　 ]+", "");
                else if(check_model_way.equals("RTRIM")) material_model=material_model.replaceAll("[　 ]+$", "");
                else if(check_model_way.equals("TRIM")) material_model=material_model.trim();
                else if(check_model_way.equals("LZERO")) material_model=material_model.replaceAll("^(0+)", "");
                else if(check_model_way.equals("RZERO")) material_model=material_model.replaceAll("0*$", "");
                else if(check_model_way.equals("ZERO")) material_model=material_model.replaceAll("^(0+)", "").replaceAll("0*$", "");
                if(!small_model_type.contains(material_model)){
                    throw new Exception("物料扫码规则{"+material_rule_code+"},物料机型解析数据{"+material_model+"}与条码内要求机型{"+small_model_type+"}不匹配");
                }
            }
            if(!material_code.equals("")) {
                jbResult=new JSONObject();
                jbResult.put("material_code",material_code);
                jbResult.put("material_batch",material_batch);
                jbResult.put("material_supplier",material_supplier);
                jbResult.put("barcode_count",barcode_count);
            }
        }
        catch (Exception ex){
            throw ex;
        }
        return jbResult;
    }
}
