package com.api.dcs.core.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * DCS接口公共方法
 * 1.创建返回
 * 2.存储任务事件
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@Service
@Slf4j
public class DcsInterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //1.创建返回
    public String CreateResponse(String response_uuid,Integer code, String message) throws Exception{
        JSONObject response_head=new JSONObject();
        response_head.put("response_uuid",response_uuid);
        response_head.put("response_time", CFuncUtilsSystem.GetNowDateTime(""));
        response_head.put("code",code);
        response_head.put("msg",message);
        return response_head.toString();
    }

    //2.存储任务事件
    @Async
    public void InsertApsTaskEvent(String mo_id,String user_name,String event_type,String event_name,
                                   String send_data,String recv_data,Integer event_code,String event_msg,
                                   Boolean successFlag,String startDate,String endDate,String event_remarks){
        JSONObject jbRequest = new JSONObject();
        jbRequest.put("startDate", startDate);
        jbRequest.put("endDate", endDate);
        jbRequest.put("esbInterfCode", event_type);
        jbRequest.put("requestParas", recv_data);
        jbRequest.put("responseParas", send_data);
        jbRequest.put("successFlag", successFlag);
        jbRequest.put("code", event_code);
        jbRequest.put("message", event_msg);
        String tableName = "b_dcs_aps_task_event";
        String event_status="OK";
        if(!successFlag) event_status="NG";
        try{
            String start_date = startDate;
            long cost_time = CFuncUtilsSystem.GetDiffMsTimes(startDate, endDate);
            if (startDate == null || startDate.equals("")) {
                start_date = CFuncUtilsSystem.GetNowDateTime("");
            }
            Date item_date = CFuncUtilsSystem.GetMongoISODate(startDate);
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String mo_event_id=CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapDataItem = new HashMap<>();
            mapDataItem.put("item_date",item_date);
            mapDataItem.put("item_date_val",item_date_val);
            mapDataItem.put("mo_event_id",mo_event_id);
            mapDataItem.put("mo_id",mo_id);
            mapDataItem.put("user_name",user_name);
            mapDataItem.put("event_type",event_type);
            mapDataItem.put("event_name",event_name);
            mapDataItem.put("send_data",send_data);
            mapDataItem.put("recv_data",recv_data);
            mapDataItem.put("event_code",event_code);
            mapDataItem.put("event_msg",event_msg);
            mapDataItem.put("event_status",event_status);
            mapDataItem.put("start_date",start_date);
            mapDataItem.put("end_date",endDate);
            mapDataItem.put("cost_times",cost_time);
            mapDataItem.put("event_remarks",event_remarks);
            mongoTemplate.insert(mapDataItem, tableName);
        }
        catch (Exception ex) {
            log.warn("存储任务事件异常:" + ex.getMessage()+",参数信息为"+jbRequest.toString()+"");
        }
    }
}
