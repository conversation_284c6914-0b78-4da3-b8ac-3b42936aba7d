package com.api.eap.project.dayuan;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.utils.CFuncUtilsLayUiResut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * EapDyWorkOrderController
 * 工单信息查询
 * 1.工单查询
 * 2.工单生产过程工艺参数查询
 * <AUTHOR>
 * @date 2023-02-20 16:49
 */
@RestController
@RequestMapping("/eap/project/dayuan/interf")
public class EapDyWorkOrderController {

    @Autowired
    private CFuncDbSqlMapper cFuncDbSqlMapper;

    //1.工单信息查询
    @RequestMapping(value = "/EapDyLastWorkOrderSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyLastWorkOrderSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String selectResult="";
        String errorMsg="";
        try{
            //查询
            String sql="select material_no,batch_no from a_eap_me_work_order order by work_order_id desc limit 1";
            List<Map<String, Object>> itemList=cFuncDbSqlMapper.ExecSelectSql(sql);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",1);
        }
        catch (Exception ex){
            errorMsg= "查询工单数据发生异常"+ex.getMessage();
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;

    }
}
