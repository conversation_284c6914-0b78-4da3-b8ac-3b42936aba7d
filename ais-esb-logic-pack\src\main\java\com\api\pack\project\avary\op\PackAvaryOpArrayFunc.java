package com.api.pack.project.avary.op;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pack.core.board.*;
import com.api.pack.core.ccd.CCDScanMessage;
import com.api.pack.core.sort.SortConst;
import com.api.pack.core.sort.SortSplitRule;
import com.api.pack.core.sort.SortSplitRuleService;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.context.MessageSource;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 * Array方法
 * 1.获取Array与BD解析数据
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@Slf4j
@Service
public class PackAvaryOpArrayFunc
{
    private final MongoTemplate mongoTemplate;

    private final MessageSource messageSource;

    private final SortSplitRuleService sortSplitRuleService;
    // formatter:off
    private final ExecutorService defaultThreadPoolExecutor = new ThreadPoolExecutor(
            3,
            10,
            60,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(10000),
            new ThreadFactoryBuilder().setNameFormat("default-executor-%d").build(),
            (r, executor) -> log.error("system pool is full! ")
    );
    // formatter:on
    private final BoardDefaultComparator boardComparator = new BoardDefaultComparator();
    private final BoardF2BComparator boardF2BComparator = new BoardF2BComparator();
    private final BoardXNPComparator boardXNPComparator = new BoardXNPComparator();
    private final Comparator directionComparator = new DirectionComparator();

    public PackAvaryOpArrayFunc(MongoTemplate mongoTemplate, MessageSource messageSource, SortSplitRuleService sortSplitRuleService)
    {
        this.mongoTemplate = mongoTemplate;
        this.messageSource = messageSource;
        this.sortSplitRuleService = sortSplitRuleService;
    }

    //根据规则获取解析结果
    private JSONObject getSplitResult(String strName, String str, String ruleName, String rule)
    {
        String split_result = "";
        String split_error = "";
        JSONObject jbResult = new JSONObject();
        jbResult.put("split_result", "");
        jbResult.put("split_error", "");
        if (rule != null && (rule.startsWith("Left") || rule.startsWith("Right")))
        {
            String[] ruleParas = rule.split("\\|", -1);
            String direct = ruleParas[0];
            Integer start = Integer.parseInt(ruleParas[1]);
            Integer length = Integer.parseInt(ruleParas[2]);
            if (length <= 0)
            {
                split_error = "{" + strName + "}根据规则名称{" + ruleName + "}规则方法{" + rule + "}截取字符失败:{截取设置长度" + length + "必须大于0}";
                jbResult.put("split_error", split_error);
                return jbResult;
            }
            if (direct.equals("Left"))
            {
                if (str.length() < start + length)
                {
                    split_error = "{" + strName + "}根据规则名称{" + ruleName + "}规则方法{" + rule + "}截取字符失败:{字符截止位" + (start + length) + "超出界限}";
                    jbResult.put("split_error", split_error);
                    return jbResult;
                }
                split_result = str.substring(start, start + length);
                jbResult.put("split_result", split_result);
            }
            else
            {
                Integer start2 = str.length() - start - 1;
                if (start2 < 0)
                {
                    split_error = "{" + strName + "}根据规则名称{" + ruleName + "}规则方法{" + rule + "}截取字符失败:{起始位置" + start + "超出界限}";
                    jbResult.put("split_error", split_error);
                    return jbResult;
                }
                if (str.length() < start2 + length)
                {
                    split_error = "{" + strName + "}根据规则名称{" + ruleName + "}规则方法{" + rule + "}截取字符失败:{字符截止位" + (start2 + length) + "超出界限}";
                    jbResult.put("split_error", split_error);
                    return jbResult;
                }
                split_result = str.substring(start2, start2 + length);
                jbResult.put("split_result", split_result);
            }
        }
        return jbResult;
    }

    //判断Level等级
    private Integer getLevelInt(String level) throws Exception
    {
        int levelInt = 0;
        if (BoardConst.VALUE_NC.equals(level))
        {
            levelInt = 0;
        }
        else if (level.equals("A"))
        {
            levelInt = 1;
        }
        else if (level.equals("B"))
        {
            levelInt = 2;
        }
        else if (level.equals("C"))
        {
            levelInt = 3;
        }
        else if (level.equals("D"))
        {
            levelInt = 4;
        }
        else if (level.equals("E"))
        {
            levelInt = 5;
        }
        else
        {
            levelInt = 6;
        }
        return levelInt;
    }

    //获取用于存储的SET条码
    //完成分选条件：正反面码比对
    private JSONObject getSaveArrayBarCode(JSONObject jbSort, String array_type, String array_barcode_front, String array_barcode_back) throws Exception
    {
        String array_barcode = "";
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        String use_front_flag = "Y";
        JSONObject jbResult = new JSONObject();
        if (array_type.equals("None"))
        {//无码
            array_barcode = "";
        }
        else if (array_type.equals("Single"))
        {//单面
            if (!array_barcode_front.equals("@NC@") && !array_barcode_front.equals("@NULL@"))
            {
                array_barcode = array_barcode_front;
            }
            else
            {
                if (!array_barcode_back.equals("@NC@") && !array_barcode_back.equals("@NULL@"))
                {
                    array_barcode = array_barcode_back;
                    use_front_flag = "N";
                }
            }
            if (array_barcode.equals("@NC@") || array_barcode.equals("@NULL@"))
            {
                array_barcode = "NoRead";
                array_status = "NG";
                array_ng_code = -1;
                array_ng_msg = "厂内码读取失败";
                use_front_flag = "Y";
            }
        }
        else
        {//双面
            if (!array_barcode_front.equals("@NC@") && !array_barcode_front.equals("@NULL@"))
            {
                array_barcode = array_barcode_front;
            }
            else
            {
                if (!array_barcode_back.equals("@NC@") && !array_barcode_back.equals("@NULL@"))
                {
                    array_barcode = array_barcode_back;
                }
            }
            if (array_barcode.equals("@NC@") || array_barcode.equals("@NULL@"))
            {
                array_barcode = "NoRead";
                array_status = "NG";
                array_ng_code = -1;
                array_ng_msg = "厂内码读取失败";
            }
            else
            {
                if (!array_barcode_front.equals(array_barcode_back))
                {
                    //若正面和反面条码不一致,则选择正面条码作为存储
                    array_barcode = array_barcode_front;
                }
                if (jbSort.containsKey("FrontAndBackSort"))
                {
                    if (!array_barcode_front.equals(array_barcode_back))
                    {
                        array_status = "NG";
                        array_ng_code = -2;
                        array_ng_msg = "正面厂内码{" + array_barcode_front + "}与反面厂内码{" + array_barcode_back + "}不一致";
                    }
                }
            }
        }
        jbResult.put("array_barcode", array_barcode);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        jbResult.put("use_front_flag", use_front_flag);//是否使用正面
        return jbResult;
    }

    //获取用于存储的SET等级
    //完成分选条件：SET等级判定
    private JSONObject getSaveArrayLevel(JSONObject jbSort, String array_type, String use_front_flag, String array_level_front, String array_level_back) throws Exception
    {
        String array_level = "";
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        if (array_type.equals("None"))
        {//无码
            array_level = "";
        }
        else if (array_type.equals("Single"))
        {//单面
            if (use_front_flag.equals("Y"))
            {
                array_level = array_level_front;
            }
            else
            {
                array_level = array_level_back;
            }
            Integer array_level_int = getLevelInt(array_level);
            if (array_level_int >= 6)
            {
                array_level = "F";
            }
        }
        else
        {
            if (use_front_flag.equals("Y"))
            {
                array_level = array_level_front;
                Integer array_level_a = getLevelInt(array_level_front);
                if (array_level_a >= 6)
                {
                    array_level = "F";
                }
                Integer array_level_b = getLevelInt(array_level_back);
                if (array_level_b > array_level_a)
                {
                    array_level = array_level_back;
                    if (array_level_b >= 6)
                    {
                        array_level = "F";
                    }
                }
            }
        }
        if (!array_level.equals(""))
        {
            if (jbSort.containsKey("SetLevelSort"))
            {
                String stand_array_level = jbSort.getString("SetLevelSort");
                Integer array_level_now = getLevelInt(array_level);
                Integer array_level_stand = getLevelInt(stand_array_level);
                if (array_level_now > array_level_stand)
                {
                    array_status = "NG";
                    array_ng_code = -3;
                    array_ng_msg = "SET二维码等级{" + array_level + "}低于设定等级{" + stand_array_level + "}";
                }
            }
        }
        jbResult.put("array_level", array_level);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //用于比对mapping结果
    private JSONObject getSaveArrayMappingResult(JSONObject jbSort, String setFrontQrc, String setBackQrc, JSONArray bd_list_front, JSONArray bd_list_back) throws Exception
    {
        String array_status = "NG";
        Integer array_ng_code = 1;
        String array_ng_msg = "mapping比对失败";
        JSONArray bd_list_front_last = bd_list_front;  //正面数据
        JSONArray bd_list_back_last = bd_list_back;  //反面数据
        StringBuilder xout_count_front = new StringBuilder();
        StringBuilder xout_count_back = new StringBuilder();
        for (int i = 0; i < bd_list_front_last.size(); i++)
        {
            JSONObject jbPcsItemFront = bd_list_front_last.getJSONObject(i);
            JSONObject jbPcsItemBack = bd_list_back_last.getJSONObject(i);
            Boolean xout_flag_front = jbPcsItemFront.getBoolean("IsXout");
            Boolean xout_flag_back = jbPcsItemBack.getBoolean("IsXout");
            //判断正反面PCS的XOUT_FLAG，如果XoutFlag=true，那么值为1，反之为0
            if (xout_flag_front)
            {
                xout_count_front.append("1");
            }
            else
            {
                xout_count_front.append("0");
            }
            if (xout_flag_back)
            {
                xout_count_back.append("1");
            }
            else
            {
                xout_count_back.append("0");
            }
        }
        JSONObject jbResult = new JSONObject();
        String xout_count_front_str = xout_count_front.toString();
        String xout_count_back_str = xout_count_back.toString();
        Query query = new Query();
        query.addCriteria(Criteria.where("enable_flag").is("Y"));
        query.addCriteria(Criteria.where("barcode").is(setFrontQrc));
        // 增加排序筛选最新登记且最新上传的一笔
        query.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
        query.with(Sort.by(Sort.Direction.DESC, "upload_date_val"));
        query.fields().include("bad_mark");
        Document badMarkList = mongoTemplate.findOne(query, Document.class, "a_pack_strip_inspect_data");
        if (jbSort.containsKey(BoardConst.SORT_BOARD_MAPPING))
        {
            //判断厂内码是否在mes下发的mapping中
            if (badMarkList != null && !badMarkList.isEmpty())
            {
                if (setFrontQrc.equals(setBackQrc))
                {
                    if (badMarkList.getString("bad_mark").equals(xout_count_front_str) && badMarkList.getString("bad_mark").equals(xout_count_back_str))
                    {
                        array_status = "OK";
                        array_ng_code = 0;
                        array_ng_msg = "";
                    }
                }
                else
                {
                    array_ng_msg = "厂内码与反面厂内码不一致";
                }
            }
            else
            {
                array_ng_msg = "厂内码在下载mapping中不存在";
            }
        }
        else
        {
            array_status = "OK";
            array_ng_code = 0;
            array_ng_msg = "";
        }
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        jbResult.put("xout_count_front", xout_count_front_str);
        jbResult.put("xout_count_back", xout_count_back_str);
        jbResult.put("bad_mark", badMarkList != null && !badMarkList.isEmpty() ? badMarkList.getString("bad_mark") : "");
        return jbResult;
    }

    //获取用于存储的SET的BD数量
    private JSONObject getSaveArrayBdCount(String bd_type, Integer array_bd_count_front, Integer array_bd_count_back) throws Exception
    {
        Integer array_bd_count = 0;
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        if (array_bd_count_front >= array_bd_count_back)
        {
            array_bd_count = array_bd_count_front;
        }
        else
        {
            array_bd_count = array_bd_count_back;
        }
        if (bd_type.equals("Double"))
        {
            if (array_bd_count_front != array_bd_count_back)
            {
                array_status = "NG";
                array_ng_code = -5;
                array_ng_msg = "正面PCS数量{" + array_bd_count_front + "}不等于反面PCS数量{" + array_bd_count_back + "}";
            }
        }
        if (!bd_type.equals("None"))
        {
            if (array_bd_count <= 0)
            {
                array_status = "NG";
                array_ng_code = -6;
                array_ng_msg = "线扫反馈SET里面的PCS数量<=0";
            }
        }
        jbResult.put("array_bd_count", array_bd_count);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET的旋转方向
    private JSONObject getSaveArrayBoardTurn(String use_front_flag, Integer board_turn_front, Integer board_turn_back) throws Exception
    {
        Integer board_turn = 2;
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        if (use_front_flag.equals("Y"))
        {
            if (board_turn_front > 0)
            {
                board_turn = 1;
            }
        }
        else
        {
            if (board_turn_back > 0)
            {
                board_turn = 1;
            }
        }
        jbResult.put("board_turn", board_turn);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //获取用于存储的SET的XOUT实际数量
    private JSONObject getSaveArrayXoutCount(String bd_type, String xout_flag, Integer xout_set_num, Integer xout_act_num_front, Integer xout_act_num_back) throws Exception
    {
        Integer xout_act_num = 0;
        String array_status = "OK";
        int array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        if (xout_act_num_front >= xout_act_num_back)
        {
            xout_act_num = xout_act_num_front;
        }
        else
        {
            xout_act_num = xout_act_num_back;
        }
        if (xout_act_num > xout_set_num && xout_flag.equals("Y"))
        {
            array_status = "NG";
            array_ng_code = -7;
            array_ng_msg = "X位实际数量{" + xout_act_num + "}大于设定X位数量{" + xout_set_num + "}";
        }
        if (bd_type.equals("Double"))
        {
            if (!xout_act_num_front.equals(xout_act_num_back) && xout_flag.equals("Y"))
            {
                array_status = "NG";
                array_ng_code = -8;
                array_ng_msg = "正面X位数量{" + xout_act_num_front + "}不等于反面X位数量{" + xout_act_num_back + "}";
            }
        }
        jbResult.put("xout_act_num", xout_act_num);
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //对Array合格性进行校验
    private JSONObject checkArrayResult(JSONObject jbRecipe, JSONObject jbSort, String array_type, Map<String, Object> mapRowArray, List<SortSplitRule.Item> sortRules) throws Exception
    {
        String dataType = "SET";
        Map<String, JSONObject> panelMap = new HashMap<>();
        if (mapRowArray.containsKey("array_front_info"))
        {
            JSONObject front = JSON.parseObject((String) mapRowArray.get("array_front_info"));
            panelMap.put("front", front);
        }
        if (mapRowArray.containsKey("array_back_info"))
        {
            JSONObject back = JSON.parseObject((String) mapRowArray.get("array_back_info"));
            panelMap.put("back", back);
        }
        // 比对校验SET modified by jay-y 2024/04/24
        for (String orient : panelMap.keySet())
        {
            this.compare(dataType, orient, panelMap.get(orient), null, jbSort, array_type, mapRowArray, sortRules, false);
        }

        String meArrayTable = "a_pack_me_array";
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        JSONObject jbResult = new JSONObject();
        String array_barcode = mapRowArray.get("array_barcode").toString();
        // 条码长度比对
        if (jbSort.containsKey("BarLengthSort") && !array_type.equals("None"))
        {
            Integer array_length = jbRecipe.getInteger("array_length");
            if (array_length > 0)
            {
                if (array_barcode.length() != array_length)
                {
                    jbResult.put("array_status", "NG");
                    jbResult.put("array_ng_code", -28);
                    jbResult.put("array_ng_msg", "厂内码长度{" + array_barcode.length() + "}与配方规定长度{" + array_length + "}不等");
                    return jbResult;
                }
            }
        }
        // 条码大小写比对
        if (jbSort.containsKey("BarCaseSort") && !array_type.equals("None"))
        {
            String array_case = jbRecipe.getString("array_case");
            if (array_case.equals("Upper") || array_case.equals("Lower"))
            {
                if (array_case.equals("Upper"))
                {
                    boolean isAllUpper = array_barcode.matches("[A-Z\\d]+");
                    if (!isAllUpper)
                    {
                        jbResult.put("array_status", "NG");
                        jbResult.put("array_ng_code", -29);
                        jbResult.put("array_ng_msg", "厂内码{" + array_barcode + "}与配方要求全大写不符合");
                        return jbResult;
                    }
                }
                else
                {
                    boolean isAllLower = array_barcode.matches("[a-z\\d]+");
                    if (!isAllLower)
                    {
                        jbResult.put("array_status", "NG");
                        jbResult.put("array_ng_code", -30);
                        jbResult.put("array_ng_msg", "厂内码{" + array_barcode + "}与配方要求全小写不符合");
                        return jbResult;
                    }
                }
            }
        }
        // SET重码对比
        if (jbSort.containsKey("MultiCodeSort") && !array_type.equals("None"))
        {
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.addCriteria(Criteria.where("unbind_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
            queryBigData.addCriteria(Criteria.where("array_barcode").is(array_barcode));
            long arrayCount = mongoTemplate.getCollection(meArrayTable).countDocuments(queryBigData.getQueryObject());
            if (arrayCount > 0)
            {
                jbResult.put("array_status", "NG");
                jbResult.put("array_ng_code", -31);
                jbResult.put("array_ng_msg", "厂内码{" + array_barcode + "}重复,请先解绑厂内码");
                return jbResult;
            }
        }
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        return jbResult;
    }

    //获取PCS存储信息以及PCS影响SET分选解析
    private JSONObject checkBdResult(String bd_type, JSONObject jbRecipe, JSONObject jbSort, Map<String, Object> mapRowArray, JSONArray bd_list_front, JSONArray bd_list_back, List<SortSplitRule.Item> pcsSortRules) throws Exception
    {
        String setDataType = "SET";
        Map<String, JSONObject> setPanelMap = new HashMap<>();
        Object front = mapRowArray.get("array_front_info");
        Object back = mapRowArray.get("array_back_info");
        if (!ObjectUtils.isEmpty(front))
        {
            setPanelMap.put("front", JSON.parseObject((String) mapRowArray.get("array_front_info")));
        }
        if (!ObjectUtils.isEmpty(back))
        {
            setPanelMap.put("back", JSON.parseObject((String) mapRowArray.get("array_back_info")));
        }

        String meBdTable = "a_pack_me_bd";
        String array_status = "OK";
        Integer array_ng_code = 0;
        String array_ng_msg = "";
        Boolean use_front_flag = true;
        String array_id = mapRowArray.get("array_id").toString();
        String array_barcode = mapRowArray.get("array_barcode").toString();
        Integer pcs_level_int = 0;
        String pcs_level = "";
        if (jbSort.containsKey("PcsLevelSort"))
        {
            pcs_level = jbSort.getString("PcsLevelSort");
            pcs_level_int = getLevelInt(pcs_level);
        }
        Boolean isCheckBdIndex = false;
        Integer bd_index_stand = 0;
        Integer bd_index_start = jbRecipe.getInteger("bd_index_start");
        Integer bd_index_incre = jbRecipe.getInteger("bd_index_incre");
        if (jbSort.containsKey("PcsIndexSort"))
        {
            if (bd_index_start >= 0 && bd_index_incre >= 1)
            {
                isCheckBdIndex = true;
            }
        }

        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
        JSONObject jbResult = new JSONObject();
        List<Map<String, Object>> bdRowsList = new ArrayList<>();//BD解析结果

        if (bd_list_front == null || bd_list_front.size() <= 0)
        {
            jbResult.put("array_status", "NG");
            jbResult.put("array_ng_code", -51);
            jbResult.put("array_ng_msg", "线扫返回正面PCS集合为空");
            return jbResult;
        }
        if (bd_list_back == null || bd_list_back.size() <= 0)
        {
            jbResult.put("array_status", "NG");
            jbResult.put("array_ng_code", -52);
            jbResult.put("array_ng_msg", "线扫返回反面PCS集合为空");
            return jbResult;
        }
        if (bd_list_front.size() != bd_list_back.size())
        {
            jbResult.put("array_status", "NG");
            jbResult.put("array_ng_code", -53);
            jbResult.put("array_ng_msg", "线扫返回正面PCS集合数量{" + bd_list_front.size() + "}不等于反面PCS集合数量{" + bd_list_back.size() + "}");
            return jbResult;
        }
        //判断使用正面还是反面作为存储与判断依据
        if (bd_type.equals("Single"))
        {//单面
            JSONObject jbPcsItemFrontFirst = bd_list_front.getJSONObject(0);
            String bd_barcode_front_first = jbPcsItemFrontFirst.getString("PcsQRC");
            if (bd_barcode_front_first.equals("@NC@"))
            {
                use_front_flag = false;
            }
        }
        JSONArray bd_list_front_last = bd_list_front;
        JSONArray bd_list_back_last = bd_list_back;
        if (!use_front_flag)
        {
            bd_list_front_last = bd_list_back;
            bd_list_back_last = bd_list_front;
        }
        String dataType = "PCS";
        Map<String, JSONObject> panelMap = new HashMap<>();
        //循环对PCS数据进行判断
        List<String> lstPcsBarCode = new ArrayList<>();
        for (int i = 0; i < bd_list_front_last.size(); i++)
        {
            JSONObject jbPcsItemFront = bd_list_front_last.getJSONObject(i);
            panelMap.put("front", jbPcsItemFront);
            JSONObject jbPcsItemBack = bd_list_back_last.getJSONObject(i);
            panelMap.put("back", jbPcsItemBack);
            String bd_id = CFuncUtilsSystem.CreateUUID(true);
            Integer bd_index = i + 1;
            String bd_barcode = "";
            String bd_level = "";
            String bd_mark = "";
            String xout_flag_bd = "N";
            String bd_status = "OK";
            int bd_ng_code = 0;
            String bd_ng_msg = "";
            if (isCheckBdIndex)
            {
                if (i == 0)
                {
                    bd_index_stand = bd_index_start;
                }
                else
                {
                    bd_index_stand = bd_index_stand + bd_index_incre;
                }
            }
            //正面信息
            String bd_barcode_front = jbPcsItemFront.getString("PcsQRC");
            String bd_level_front = jbPcsItemFront.getString("PcsQRCLevel");
            String bd_mark_front = jbPcsItemFront.getString("DirMarkChkRtl");
            Boolean xout_flag_front = jbPcsItemFront.getBoolean("IsXout");
            if (bd_barcode_front.equals("@NC@") || bd_barcode_front.equals("@NULL@") || bd_barcode_front.equals(""))
            {
                bd_barcode_front = "NoRead";
            }
            if (bd_mark_front.equals("@NC@") || bd_mark_front.equals(""))
            {
                bd_mark_front = "";
            }
            Integer bd_level_front_int = getLevelInt(bd_level_front);
            if (bd_level_front_int >= 6)
            {
                bd_level_front = "F";
            }
            //反面信息
            String bd_barcode_back = jbPcsItemBack.getString("PcsQRC");
            String bd_level_back = jbPcsItemBack.getString("PcsQRCLevel");
            String bd_mark_back = jbPcsItemBack.getString("DirMarkChkRtl");
            Boolean xout_flag_back = jbPcsItemBack.getBoolean("IsXout");
            if (bd_barcode_back.equals("@NC@") || bd_barcode_back.equals("@NULL@") || bd_barcode_back.equals(""))
            {
                bd_barcode_back = "NoRead";
            }
            if (bd_mark_back.equals("@NC@") || bd_mark_back.equals(""))
            {
                bd_mark_back = "";
            }
            Integer bd_level_back_int = getLevelInt(bd_level_back);
            if (bd_level_back_int >= 6)
            {
                bd_level_back = "F";
            }
            if (bd_type.equals("Single"))
            {//单面
                bd_barcode = bd_barcode_front;
                bd_mark = bd_mark_front;
                bd_level = bd_level_front;
            }
            if (bd_type.equals("Double"))
            {//双面
                bd_barcode = "NoRead";
                if (!bd_barcode_front.equals("NoRead"))
                {
                    bd_barcode = bd_barcode_front;
                }
                else
                {
                    if (!bd_barcode_back.equals("NoRead"))
                    {
                        bd_barcode = bd_barcode_back;
                    }
                }
                if (bd_mark_front.equals("NG") || bd_mark_back.equals("NG"))
                {
                    bd_mark = "NG";
                }
                if (bd_mark_front.equals("OK") && bd_mark_back.equals("OK"))
                {
                    bd_mark = "OK";
                }
                bd_level = bd_level_front;
                if (bd_level_back_int > bd_level_front_int)
                {
                    bd_level = bd_level_back;
                }
            }
            if (xout_flag_front || xout_flag_back)
            {
                //1.正反面X位判断
                xout_flag_bd = "Y";
                if (xout_flag_front && !xout_flag_back)
                {
                    if (array_status.equals("OK"))
                    {
                        array_status = "NG";
                        array_ng_code = -54;
                        array_ng_msg = "PCS序号{" + bd_index + "}正面画X,反面未画X";
                    }
                    bd_status = "NG";
                    bd_ng_code = -1;
                    bd_ng_msg = "正面画X,反面未画X";
                }
                else if (!xout_flag_front)
                {
                    if (array_status.equals("OK"))
                    {
                        array_status = "NG";
                        array_ng_code = -55;
                        array_ng_msg = "PCS序号{" + bd_index + "}反面画X,正面未画X";
                    }
                    bd_status = "NG";
                    bd_ng_code = -2;
                    bd_ng_msg = "反面画X,正面未画X";
                }
            }
            else
            {
                //2.条码逻辑判断
                if (bd_type.equals("Double") && jbSort.containsKey("FrontAndBackSort"))
                {//双面
                    if (!bd_barcode_front.equals(bd_barcode_back))
                    {
                        if (array_status.equals("OK"))
                        {
                            array_status = "NG";
                            array_ng_code = -56;
                            array_ng_msg = "PCS序号{" + bd_index + "},正面PCS条码为{" + bd_barcode_front + "},反面PCS条码为{" + bd_barcode_back + "},二者不一致";
                        }
                        bd_status = "NG";
                        bd_ng_code = -3;
                        bd_ng_msg = "正面PCS条码为{" + bd_barcode_front + "},反面PCS条码为{" + bd_barcode_back + "},二者不一致";
                    }
                }
                if (!bd_type.equals("None"))
                {
                    if (bd_status.equals("OK"))
                    {
                        if (bd_mark.equals("NG"))
                        {
                            if (array_status.equals("OK"))
                            {
                                array_status = "NG";
                                array_ng_code = -57;
                                array_ng_msg = "PCS序号{" + bd_index + "},检测光学点为NG";
                            }
                            bd_status = "NG";
                            bd_ng_code = -4;
                            bd_ng_msg = "检测光学点为NG";
                        }
                        if (pcs_level_int > 0 && bd_status.equals("OK"))
                        {
                            Integer bd_level_int = getLevelInt(bd_level);
                            if (bd_level_int > pcs_level_int)
                            {
                                if (array_status.equals("OK"))
                                {
                                    array_status = "NG";
                                    array_ng_code = -58;
                                    array_ng_msg = "PCS序号{" + bd_index + "},PCS二维码等级{" + bd_level + "}大于设定等级{" + pcs_level + "}";
                                }
                                bd_status = "NG";
                                bd_ng_code = -5;
                                bd_ng_msg = "PCS二维码等级{" + bd_level + "}大于设定等级{" + pcs_level + "}";
                            }
                        }
                        if (bd_barcode.equals("NoRead"))
                        {
                            if (array_status.equals("OK"))
                            {
                                array_status = "NG";
                                array_ng_code = -59;
                                array_ng_msg = "PCS序号{" + bd_index + "},PCS条码读取失败";
                            }
                            if (bd_status.equals("OK"))
                            {
                                bd_status = "NG";
                                bd_ng_code = -6;
                                bd_ng_msg = "条码读取失败";
                            }
                        }
                        else
                        {
                            //验证重码
                            if (jbSort.containsKey("MultiCodeSort"))
                            {
                                if (lstPcsBarCode.contains(bd_barcode))
                                {
                                    if (array_status.equals("OK"))
                                    {
                                        array_status = "NG";
                                        array_ng_code = -60;
                                        array_ng_msg = "PCS序号{" + bd_index + "},当前板件中存在相同的PCS条码{" + bd_barcode + "}";
                                    }
                                    if (bd_status.equals("OK"))
                                    {
                                        bd_status = "NG";
                                        bd_ng_code = -7;
                                        bd_ng_msg = "当前板件中存在相同的PCS条码{" + bd_barcode + "}";
                                    }
                                }
                                else
                                {
                                    lstPcsBarCode.add(bd_barcode);
                                }
                                if (bd_status.equals("OK"))
                                {
                                    Query queryBigData = new Query();
                                    queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                                    queryBigData.addCriteria(Criteria.where("array_status").is("OK"));
                                    queryBigData.addCriteria(Criteria.where("bd_barcode").is(bd_barcode));
                                    long arrayCount = mongoTemplate.getCollection(meBdTable).countDocuments(queryBigData.getQueryObject());
                                    if (arrayCount > 0)
                                    {
                                        if (array_status.equals("OK"))
                                        {
                                            array_status = "NG";
                                            array_ng_code = -61;
                                            array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}重码,请先解绑";
                                        }
                                        bd_status = "NG";
                                        bd_ng_code = -8;
                                        bd_ng_msg = "PCS条码{" + bd_barcode + "}重码,请先解绑";
                                    }
                                }
                            }
                            //条码长度比对
                            if (jbSort.containsKey("BarLengthSort") && bd_status.equals("OK"))
                            {
                                Integer bd_length = jbRecipe.getInteger("bd_length");
                                if (bd_length > 0)
                                {
                                    if (bd_barcode.length() != bd_length)
                                    {
                                        if (array_status.equals("OK"))
                                        {
                                            array_status = "NG";
                                            array_ng_code = -66;
                                            array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}条码长度{" + bd_barcode.length() + "}不等于设定长度{" + bd_length + "}";
                                        }
                                        bd_status = "NG";
                                        bd_ng_code = -13;
                                        bd_ng_msg = "PCS条码{" + bd_barcode + "}条码长度{" + bd_barcode.length() + "}不等于设定长度{" + bd_length + "}";
                                    }
                                }
                            }
                            //条码大小比对
                            if (jbSort.containsKey("BarCaseSort") && bd_status.equals("OK"))
                            {
                                String bd_case = jbRecipe.getString("bd_case");
                                if (bd_case.equals("Upper") || bd_case.equals("Lower"))
                                {
                                    if (bd_case.equals("Upper"))
                                    {
                                        boolean isAllUpper = bd_barcode.matches("[A-Z\\d]+");
                                        if (!isAllUpper)
                                        {
                                            if (array_status.equals("OK"))
                                            {
                                                array_status = "NG";
                                                array_ng_code = -67;
                                                array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}不全为大写";
                                            }
                                            bd_status = "NG";
                                            bd_ng_code = -14;
                                            bd_ng_msg = "PCS条码{" + bd_barcode + "}不全为大写";
                                        }
                                    }
                                    else
                                    {
                                        boolean isAllLower = bd_barcode.matches("[a-z\\d]+");
                                        if (!isAllLower)
                                        {
                                            if (array_status.equals("OK"))
                                            {
                                                array_status = "NG";
                                                array_ng_code = -68;
                                                array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}不全为小写";
                                            }
                                            bd_status = "NG";
                                            bd_ng_code = -15;
                                            bd_ng_msg = "PCS条码{" + bd_barcode + "}不全为小写";
                                        }
                                    }
                                }
                            }
                            //PCS序号比对
                            if (isCheckBdIndex)
                            {
                                String bd_index_split_rule = jbRecipe.getString("bd_index_split_rule");
                                JSONObject jbSplitResult = getSplitResult("PCS条码", bd_barcode, "PCS截取序号", bd_index_split_rule);
                                String split_result = jbSplitResult.getString("split_result");
                                String split_error = jbSplitResult.getString("split_error");
                                if (!split_error.equals(""))
                                {
                                    if (array_status.equals("OK"))
                                    {
                                        array_status = "NG";
                                        array_ng_code = -69;
                                        array_ng_msg = "PCS序号{" + bd_index + "}," + split_error;
                                    }
                                    if (bd_status.equals("OK"))
                                    {
                                        bd_status = "NG";
                                        bd_ng_code = -16;
                                        bd_ng_msg = split_error;
                                    }
                                }
                                else
                                {
                                    if (!split_result.equals(""))
                                    {
                                        try
                                        {
                                            int bd_index_now = Integer.parseInt(split_result);
                                            if (bd_index_now != bd_index_stand)
                                            {
                                                if (array_status.equals("OK"))
                                                {
                                                    array_status = "NG";
                                                    array_ng_code = -70;
                                                    array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}截取序号{" + bd_index_now + "}不等于要求序号{" + bd_index_stand + "}";
                                                }
                                                if (bd_status.equals("OK"))
                                                {
                                                    bd_status = "NG";
                                                    bd_ng_code = -17;
                                                    bd_ng_msg = "PCS条码{" + bd_barcode + "}截取序号{" + bd_index_now + "}不等于要求序号{" + bd_index_stand + "}";
                                                }
                                            }
                                        }
                                        catch (Exception convError)
                                        {
                                            if (array_status.equals("OK"))
                                            {
                                                array_status = "NG";
                                                array_ng_code = -71;
                                                array_ng_msg = "PCS序号{" + bd_index + "},PCS条码{" + bd_barcode + "}截取序号{" + split_result + "}转换Int类型失败";
                                            }
                                            if (bd_status.equals("OK"))
                                            {
                                                bd_status = "NG";
                                                bd_ng_code = -18;
                                                bd_ng_msg = "PCS条码{" + bd_barcode + "}截取序号{" + split_result + "}转换Int类型失败";
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                // 比对验证PCS modified by jay-y 2024/04/24
                CompareResult compareResult = null;
                for (String orient : panelMap.keySet())
                {
                    JSONObject panel = panelMap.get(orient);
                    JSONObject setPanel = setPanelMap.get(orient);
                    Integer pcsNum = Integer.parseInt(panel.getString("PcsNum"));
                    compareResult = this.compare(dataType, orient, panel, pcsNum, jbSort, bd_type, mapRowArray, pcsSortRules, true);
                    if (compareResult != null && compareResult.isNG())
                    {
                        break;
                    }
                    compareResult = this.compare(dataType, orient, panel, pcsNum, setDataType, orient, setPanel, null, jbSort, bd_type, pcsSortRules, true);
                    if (compareResult != null && compareResult.isNG())
                    {
                        break;
                    }
                }
                if (compareResult != null && compareResult.isNG())
                {
                    array_status = "NG";
                    array_ng_code = -1;
                    array_ng_msg = compareResult.getNgMsg();
                    if (bd_status.equals("OK"))
                    {
                        bd_status = "NG";
                        bd_ng_code = -9;
                        bd_ng_msg = compareResult.getNgMsg();
                    }
                }
            }
            //创建保存集合
            Map<String, Object> mapBdItem = new HashMap<>();
            mapBdItem.put("item_date", item_date);
            mapBdItem.put("item_date_val", item_date_val);
            mapBdItem.put("bd_id", bd_id);
            mapBdItem.put("array_id", array_id);
            mapBdItem.put("array_barcode", array_barcode);
            mapBdItem.put("array_status", "");
            mapBdItem.put("bd_barcode", bd_barcode);
            mapBdItem.put("bd_index", bd_index);
            mapBdItem.put("bd_level", bd_level);
            mapBdItem.put("bd_mark", bd_mark);
            mapBdItem.put("xout_flag", xout_flag_bd);
            mapBdItem.put("bd_status", bd_status);
            mapBdItem.put("bd_ng_code", bd_ng_code);
            mapBdItem.put("bd_ng_msg", bd_ng_msg);
            // PCS状态为OK时的PCS记录置为有效
            mapBdItem.put("enable_flag", "OK".equals(bd_status) ? "Y" : "N");
            bdRowsList.add(mapBdItem);
        }
        jbResult.put("array_status", array_status);
        jbResult.put("array_ng_code", array_ng_code);
        jbResult.put("array_ng_msg", array_ng_msg);
        jbResult.put("bd", bdRowsList);
        return jbResult;
    }

    //3.获取Array与BD解析数据
    public JSONObject ResolveCcdResult(String user_name, JSONObject jbPlan, JSONObject jbRecipe, JSONObject jbSort, JSONObject ccdData) throws Exception
    {
        JSONObject jbCcdResolveResult = new JSONObject();
        Map<String, Object> mapRowArray = new HashMap<>();// Array解析结果
        List<Map<String, Object>> bdRowsList = new ArrayList<>();// BD解析结果

        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
        String array_id = CFuncUtilsSystem.CreateUUID(true);
        String array_type = jbPlan.getString("array_type");
        String bd_type = jbPlan.getString("bd_type");
        int board_result = 0;
        int deposit_position = 2;
        boolean isMultiCode = false; // 代表是否重码
        String xout_flag = "N";
        int xout_set_num = 0;
        String array_barcode = "";
        String array_level = "";
        String array_mark = "";
        Integer array_bd_count = 0;
        Integer board_turn = 2;
        Integer xout_act_num = 0;
        String split_lot = "";
        String split_model = "";
        String array_status = "OK";

        // 初始化规则集 added by jay-y 2024/05/22
        Long sortSplitRuleId = jbRecipe.getLong("sort_split_rule_id");
        SortSplitRule splitRule = SortSplitRule.byId(sortSplitRuleId, sortSplitRuleService);
        List<SortSplitRule.Item> setSortRules = new LinkedList<>();
        List<SortSplitRule.Item> pcsSortRules = new LinkedList<>();
        if (splitRule != null && !ObjectUtils.isEmpty(splitRule.getItems()))
        {
            for (SortSplitRule.Item item : splitRule.getItems())
            {
                if (SortConst.BOARD_CATEGORY_SET.equals(item.getBoardType()))
                {
                    setSortRules.add(item);
                }
                else if (SortConst.BOARD_CATEGORY_PCS.equals(item.getBoardType()))
                {
                    pcsSortRules.add(item);
                }
            }
        }
        JSONObject mappingResult = null;
        // 获取CCD数据
        CCDScanMessage ccdMessage = new CCDScanMessage(user_name, array_id, jbPlan, jbRecipe, jbSort, ccdData);
        try
        {
            for (String k : jbPlan.keySet())
            {
                mapRowArray.put(k, jbPlan.get(k));
            }
            String cyclePeriod = String.valueOf(jbPlan.get("cycle_period"));
            mapRowArray.remove("_id");
            mapRowArray.put("item_date", item_date);
            mapRowArray.put("item_date_val", item_date_val);
            mapRowArray.put("array_id", array_id);
            mapRowArray.putIfAbsent("pile_barcode", "");
            mapRowArray.putIfAbsent("array_barcode", "");
            mapRowArray.putIfAbsent("lot_num", "");
            mapRowArray.put("array_index", jbPlan.getInteger("finish_ok_count") + 1);
            mapRowArray.putIfAbsent("board_sn", "");
            mapRowArray.putIfAbsent("array_level", "");
            mapRowArray.putIfAbsent("array_mark", "");
            mapRowArray.put("array_bd_count", 0);
            mapRowArray.put("board_result", 0);
            mapRowArray.put("board_turn", 2);
            mapRowArray.putIfAbsent("array_ng_code", "");
            mapRowArray.putIfAbsent("array_ng_msg", "");
            mapRowArray.put("xout_flag", xout_flag);
            mapRowArray.put("xout_set_num", xout_set_num);
            mapRowArray.put("xout_act_num", xout_act_num);
            mapRowArray.put("array_status", array_status);
            mapRowArray.putIfAbsent("array_ng_code", 0);
            mapRowArray.putIfAbsent("array_ng_msg", "");
            mapRowArray.putIfAbsent("array_front_info", "");
            mapRowArray.putIfAbsent("array_back_info", "");
            mapRowArray.put("user_name", user_name);
            mapRowArray.putIfAbsent("task_type", "");
            mapRowArray.putIfAbsent("model_type", "");
            mapRowArray.putIfAbsent("model_version", "");
            mapRowArray.putIfAbsent("array_type", "");
            mapRowArray.putIfAbsent("bd_type", "");
            mapRowArray.put("m_length", jbPlan.getDouble("m_length"));
            mapRowArray.put("m_width", jbPlan.getDouble("m_width"));
            mapRowArray.put("m_tickness", jbPlan.getDouble("m_tickness"));
            mapRowArray.put("m_weight", jbPlan.getDouble("m_weight"));
            mapRowArray.put("cycle_period", cyclePeriod);
            mapRowArray.putIfAbsent("split_lot", "");
            mapRowArray.putIfAbsent("split_model", "");
            mapRowArray.putIfAbsent("up_flag", "N");
            mapRowArray.putIfAbsent("up_ng_code", 0);
            mapRowArray.putIfAbsent("up_ng_msg", "");
            mapRowArray.putIfAbsent("pile_use_flag", "N");
            mapRowArray.putIfAbsent("enable_flag", "Y");
            mapRowArray.putIfAbsent("unbind_flag", "N");
            mapRowArray.putIfAbsent("unbind_user", "");
            mapRowArray.putIfAbsent("unbind_time", "");
            mapRowArray.putIfAbsent("unbind_way", "");
            mapRowArray.putIfAbsent("batch_no", ""); // 批号
            mapRowArray.putIfAbsent("laser_batch_no", ""); // 镭射批号
            mapRowArray.putIfAbsent("typesetting_no", ""); // 排版数
            mapRowArray.putIfAbsent("customer_mn", ""); // 客户料号
            mapRowArray.putIfAbsent("ul_code", ""); // UL号

            // 解析CCD数据，目前架构为一次一片，正反面都存在数据
            JSONArray jaCcdData = ccdData.getJSONArray("Content");
            JSONObject jbFirst = jaCcdData.getJSONObject(0);
            JSONObject SetFrontContent = jbFirst.getJSONObject("SetFrontContent");//正面
            JSONObject SetBackContent = jbFirst.getJSONObject("SetBackContent");//反面

            // Array基本存储
            mapRowArray.put("board_sn", SortConst.convertValue(ccdMessage.getOrigin().getBoardSn()));
            mapRowArray.put("array_front_info", ccdMessage.getOrigin().getMultiAspectFront());
            mapRowArray.put("array_back_info", ccdMessage.getOrigin().getMultiAspectBack());
            mapRowArray.put("array_barcode", SortConst.convertValue(ccdMessage.getOrigin().getBoardBarcode()));
            mapRowArray.put("array_level", SortConst.convertValue(ccdMessage.getOrigin().getBoardLevel()));
            mapRowArray.put("array_mark", SortConst.convertValue(ccdMessage.getOrigin().getBoardMark()));
            mapRowArray.put("array_bd_count", ccdMessage.getOrigin().getBoardLayoutNumber());
            mapRowArray.put("board_turn", ccdMessage.getOrigin().getBoardTurn());
            mapRowArray.put("xout_act_num", ccdMessage.getOrigin().getXoutActualNumber());

            // 正面数据
            String array_barcode_front = SetFrontContent.getString("SetQRC");
            String array_level_front = SetFrontContent.getString("SetQRCLevel");
//            String array_mark_front = SetFrontContent.getString("DirMarkChkRtl");
            Integer array_bd_count_front = SetFrontContent.getInteger("LayoutQty");
            Integer board_turn_front = SetFrontContent.getInteger("Direction");
            Integer xout_act_num_front = SetFrontContent.getInteger("XoutQty");
            JSONArray bd_list_front = SetFrontContent.getJSONArray("PcsMsgList");

            // 反面数据
            String array_barcode_back = SetBackContent.getString("SetQRC");
            String array_level_back = SetBackContent.getString("SetQRCLevel");
//            String array_mark_back = SetBackContent.getString("DirMarkChkRtl");
            Integer array_bd_count_back = SetBackContent.getInteger("LayoutQty");
            Integer board_turn_back = SetBackContent.getInteger("Direction");
            Integer xout_act_num_back = SetBackContent.getInteger("XoutQty");
            JSONArray bd_list_back = SetBackContent.getJSONArray("PcsMsgList");

            // 双面板件，正反面数据比对
            if (ccdMessage.getOrigin().isDouble())
            {
                this.boardF2BComparator.compare(ccdMessage.getFront(), ccdMessage.getBack(), BoardConst.SET_BOARD_INITIAL_PROPERTIES, messageSource);
            }

            // 获取用于存储的SET条码
            JSONObject jbResolveResult = getSaveArrayBarCode(jbSort, array_type, array_barcode_front, array_barcode_back);
            String use_front_flag = jbResolveResult.getString("use_front_flag");//使用正面Y,使用反面N
            array_barcode = jbResolveResult.getString("array_barcode");
            String array_status_result = jbResolveResult.getString("array_status");
            Integer array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            String array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }

            //获取用于比对mapping结果
            mappingResult = getSaveArrayMappingResult(jbSort, array_barcode_front, array_barcode_back, bd_list_front, bd_list_back);
            array_status_result = mappingResult.getString("array_status");
            array_ng_code_result = mappingResult.getInteger("array_ng_code");
            array_ng_msg_result = mappingResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }

            // 比对板件方向
            SortRule boardDirectionSort = new SortRule("板件方向", BoardConst.SORT_BOARD_DIRECTION, "boardDirection", false);
            this.compare(ccdMessage, this.directionComparator, boardDirectionSort);
            String boardDirectionStr = SortConst.convertValue(ccdMessage.getOrigin().getBoardDirection());
            boardDirectionStr = Const.BLANK.equals(boardDirectionStr) ? "0" : boardDirectionStr;
            int boardDirection = Integer.parseInt(boardDirectionStr);
            if (boardDirection > 0)
            {
                ccdMessage.getOrigin().setBoardTurn(BoardConst.TURN_1);
                mapRowArray.put("board_turn", BoardConst.TURN_1);
            }

            // 比较SET中的XOUT实际PCS数量和板件PCS数量
            ccdMessage.getOrigin().compareWithXoutActualNumberAndBoardLayoutNumber();

            // 比对维修标签
            try
            {
                ccdMessage.getFront().checkValue(boardComparator, BoardConst.BOARD_PROPERTY_REPAIR_LABEL, Boolean.FALSE, messageSource);
                ccdMessage.getBack().checkValue(boardComparator, BoardConst.BOARD_PROPERTY_REPAIR_LABEL, Boolean.FALSE, messageSource);
            }
            catch (BoardCompareException ex)
            {
                ex.setMessage("该板件已贴维修标签");
                throw ex;
            }

            // 比对FlowError
            ccdMessage.getFront().checkValue(boardComparator, BoardConst.BOARD_PROPERTY_FLOW_ERROR, Boolean.FALSE, messageSource);
            ccdMessage.getBack().checkValue(boardComparator, BoardConst.BOARD_PROPERTY_FLOW_ERROR, Boolean.FALSE, messageSource);

            // 判断当前设置为几X
            if (jbSort.containsKey("XoutSort"))
            {
                xout_set_num = Integer.parseInt(jbSort.getString("XoutSort").replaceAll("X", ""));
                if (xout_set_num < 0)
                {
                    xout_set_num = 0;
                }
                if (xout_set_num > 0)
                {
                    xout_flag = "Y";
                }
            }
            mapRowArray.put("xout_set_num", xout_set_num);

            // 获取用于存储的SET等级
            jbResolveResult = getSaveArrayLevel(jbSort, array_type, use_front_flag, array_level_front, array_level_back);
            array_level = jbResolveResult.getString("array_level");
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            // 获取用于存储的SET的BD数量
            jbResolveResult = getSaveArrayBdCount(bd_type, array_bd_count_front, array_bd_count_back);
            array_bd_count = jbResolveResult.getInteger("array_bd_count");
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            // 获取用于存储的SET的旋转方向
            jbResolveResult = getSaveArrayBoardTurn(use_front_flag, board_turn_front, board_turn_back);
            board_turn = jbResolveResult.getInteger("board_turn");
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            // 获取用于存储的SET的画X数量
            jbResolveResult = getSaveArrayXoutCount(bd_type, xout_flag, xout_set_num, xout_act_num_front, xout_act_num_back);
            xout_act_num = jbResolveResult.getInteger("xout_act_num");
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG") && array_status.equals("OK"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
            }
            // Array基本存储
            mapRowArray.put("array_barcode", array_barcode);
            mapRowArray.put("array_level", array_level);
            mapRowArray.put("array_mark", array_mark);
            mapRowArray.put("array_bd_count", array_bd_count);
            mapRowArray.put("board_turn", board_turn);
            mapRowArray.put("xout_act_num", xout_act_num);

            // 针对SET条码进行分选条件解析
            if (array_status.equals("OK"))
            {
                jbResolveResult = checkArrayResult(jbRecipe, jbSort, array_type, mapRowArray, setSortRules);
                array_status_result = jbResolveResult.getString("array_status");
                array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
                array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
                if (array_status_result.equals("NG"))
                {
                    array_status = array_status_result;
                    mapRowArray.put("array_status", array_status_result);
                    mapRowArray.put("array_ng_code", array_ng_code_result);
                    mapRowArray.put("array_ng_msg", array_ng_msg_result);
                    if (array_ng_code_result == -31)
                    {
                        isMultiCode = true;
                    }
                }
                if (jbResolveResult.containsKey("split_lot"))
                {
                    split_lot = jbResolveResult.getString("split_lot");
                    mapRowArray.put("split_lot", split_lot);
                }
                if (jbResolveResult.containsKey("split_model"))
                {
                    split_model = jbResolveResult.getString("split_model");
                    mapRowArray.put("split_model", split_model);
                }
            }
            if (BoardConst.STATUS_OK.equals(array_status) && ccdMessage.getOrigin().getMultiAspect() != null)
            {
                for (String key : ccdMessage.getOrigin().getMultiAspect().keySet())
                {
                    SETBoard setBoard = ccdMessage.getOrigin().getMultiAspect().get(key);
                    if (jbSort.containsKey(BoardConst.SORT_CODE_XOUT_NP))
                    {
                        String compareRuleName = messageSource.getMessage(BoardConst.BOARD_COMPARE_MESSAGES_PACK_COMPARE_RULES_XNP, null, Locale.getDefault());
                        String compareRuleValue = jbSort.getString(BoardConst.SORT_CODE_XOUT_NP);
                        BoardCompareResult boardXNPCompareResult = setBoard.compareByBoardXNPComparator(boardXNPComparator, compareRuleName, compareRuleValue, messageSource);
                        if (boardXNPCompareResult != null)
                        {
                            if (boardXNPCompareResult.getPosition() != null)
                            {
                                mapRowArray.put("deposit_position", boardXNPCompareResult.getPosition());
                            }
                            if (boardXNPCompareResult.isOK() && !BoardConst.DEPOSIT_POSITION_OK.equals(boardXNPCompareResult.getPosition()) // 没分配到OK仓位
                            )
                            {
                                mapRowArray.put("xout_flag", BoardConst.FLAG_Y);
                                if (!ccdMessage.getOrigin().isXout())
                                {
                                    ccdMessage.getOrigin().setXoutFlag(BoardConst.FLAG_Y);
                                }
                            }
                            else if (boardXNPCompareResult.isNG())
                            {
                                array_status = boardXNPCompareResult.getStatus();
                                mapRowArray.put("array_status", boardXNPCompareResult.getStatus());
                                mapRowArray.put("array_ng_code", boardXNPCompareResult.getCode());
                                mapRowArray.put("array_ng_msg", boardXNPCompareResult.getMsg());
                                break;
                            }
                        }
                    }
                }
            }
            // 检测光学点值是否为OK
            BoardCompareResult frontCheckOpticalPointValueResult = ccdMessage.getFront().checkValue(boardComparator, BoardConst.BOARD_PROPERTY_MARK, BoardConst.OK, messageSource, Boolean.TRUE);
            boolean frontCheckOpticalPointValueResultIsNG = frontCheckOpticalPointValueResult != null && frontCheckOpticalPointValueResult.isNG();
            BoardCompareResult backCheckOpticalPointValueResult = ccdMessage.getBack().checkValue(boardComparator, BoardConst.BOARD_PROPERTY_MARK, BoardConst.OK, messageSource, Boolean.TRUE);
            boolean backCheckOpticalPointValueResultIsNG = backCheckOpticalPointValueResult != null && backCheckOpticalPointValueResult.isNG();
            if ((frontCheckOpticalPointValueResultIsNG // 正面检测结果为NG
                    || backCheckOpticalPointValueResultIsNG) // 反面检测结果为NG
                    && !ccdMessage.getOrigin().isNone() // 非无码板件
                    && !ccdMessage.getOrigin().isXout() // 非XOUT板件
            )
            {
                String msg = frontCheckOpticalPointValueResultIsNG ? frontCheckOpticalPointValueResult.getMsg() : backCheckOpticalPointValueResult.getMsg();
                String status = frontCheckOpticalPointValueResultIsNG ? frontCheckOpticalPointValueResult.getStatus() : backCheckOpticalPointValueResult.getStatus();
                int code = frontCheckOpticalPointValueResultIsNG ? frontCheckOpticalPointValueResult.getCode() : backCheckOpticalPointValueResult.getCode();
                throw new BoardCompareException(msg, status, code);
            }
            // 获取PCS存储信息以及PCS影响SET分选解析
            jbResolveResult = checkBdResult(bd_type, jbRecipe, jbSort, mapRowArray, bd_list_front, bd_list_back, pcsSortRules);
            array_status_result = jbResolveResult.getString("array_status");
            array_ng_code_result = jbResolveResult.getInteger("array_ng_code");
            array_ng_msg_result = jbResolveResult.getString("array_ng_msg");
            if (array_status_result.equals("NG"))
            {
                array_status = array_status_result;
                mapRowArray.put("array_status", array_status_result);
                mapRowArray.put("array_ng_code", array_ng_code_result);
                mapRowArray.put("array_ng_msg", array_ng_msg_result);
                if (array_ng_code_result == -60 || array_ng_code_result == -61)
                {
                    isMultiCode = true;
                }
            }
            if (jbResolveResult.containsKey("bd"))
            {
                bdRowsList = (List<Map<String, Object>>) jbResolveResult.get("bd");
                for (Map<String, Object> bdMapItem : bdRowsList)
                {
                    bdMapItem.put("array_status", array_status);
                    // SET和PCS状态同时为OK时的PCS记录才置为有效
                    if ("NG".equals(array_status))
                    {
                        bdMapItem.put("enable_flag", "N");
                    }
                }
            }
        }
        catch (Exception e)
        {
            e.printStackTrace();
            if (e instanceof BoardCompareException)
            {
                BoardCompareException compareException = (BoardCompareException) e;
                array_status = compareException.getStatus();
                mapRowArray.put("array_status", compareException.getStatus());
                mapRowArray.put("array_ng_code", compareException.getCode());
                mapRowArray.put("array_ng_msg", compareException.getMessage());
            }
        }
        finally
        {
            //7.综合判断board_result和deposit_position
            board_result = 3;
            if (isMultiCode)
            {
                board_result = 4;
            }
            else
            {
                if (array_status.equals("OK"))
                {
                    board_result = 1;
                    // 实际数量为0时输出判断结果为OK，实际数量大于0小于等于设定数量时为XOUT，大于设定数量时NG
                    if (xout_flag.equals("Y") && xout_act_num > 0 && xout_act_num <= xout_set_num)
                    {
                        board_result = 2;
                    }
                }
            }
            if (mapRowArray.containsKey("xout_flag"))
            {
                String isXout = (String) mapRowArray.get("xout_flag");
                if (BoardConst.STATUS_OK.equals(array_status) && BoardConst.FLAG_Y.equals(isXout))
                {
                    board_result = 2;
                }
            }
            else
            {
                mapRowArray.put("xout_flag", xout_flag);
            }
            if (array_status.equals("OK"))
            {
                deposit_position = 1;
                mapRowArray.putIfAbsent("deposit_position", deposit_position);
            }
            else
            {
                mapRowArray.put("enable_flag", BoardConst.FLAG_N);
                ccdMessage.getOrigin().setEnableFlag(BoardConst.FLAG_N);
                mapRowArray.put("deposit_position", deposit_position);
            }
            mapRowArray.put("board_result", board_result);
            ccdMessage.getOrigin().setBoardResult(board_result);
            ccdMessage.getOrigin().setDepositPosition((Integer) mapRowArray.get("deposit_position"));
            //返回数据
            jbCcdResolveResult.put("array", mapRowArray);
            jbCcdResolveResult.put("bd", bdRowsList);
//            jbCcdResolveResult.put("array", ccdMessage.getOrigin());
//            jbCcdResolveResult.put("bd", ccdMessage.getOrigin().getChildrenBoards());
            this.completeCompareResult(jbCcdResolveResult, ccdMessage, jbPlan, mappingResult);
            mongoTemplateInsert(bdRowsList, mapRowArray);
        }
        return jbCcdResolveResult;
    }

    public void mongoTemplateInsert(List<Map<String, Object>> bdRowsList, Map<String, Object> mapRowArray)
    {
        mongoTemplate.insert(mapRowArray, "a_pack_me_array");
        this.defaultThreadPoolExecutor.execute(() -> {
            try
            {
                if (bdRowsList != null && bdRowsList.size() > 0)
                {
                    mongoTemplate.insert(bdRowsList, "a_pack_me_bd");
                }
            }
            catch (Exception e)
            {
                log.error("Save error: " + e.getMessage(), e);
            }
        });
    }

    private void completeCompareResult(Map<String, Object> jbCcdResolveResult, CCDScanMessage message, JSONObject plan, JSONObject mappingResult)
    {
        Map<String, Object> compareResult = new HashMap<>();
        SETBoard origin = message.getOrigin();
        SETBoard front = message.getFront();
        SETBoard back = message.getBack();
        String dstPartNumber = plan.getString("model_type"); // MES-料号
        String dstDateCode = String.valueOf(plan.get("cycle_period")); // MES-周期
        String dstLotNo = plan.getString("certify_lot"); // MES-验证批号
        List<String> okPartNumbers = new ArrayList<>();
        List<String> ngPartNumbers = new ArrayList<>();
        List<String> okDateCodes = new ArrayList<>();
        List<String> ngDateCodes = new ArrayList<>();
        List<String> okLotNums = new ArrayList<>();
        List<String> ngLotNums = new ArrayList<>();
        for (String key : origin.getMultiAspect().keySet())
        {
            SETBoard setBoard = origin.getMultiAspect().get(key);
            Object partNumberObj = JSONPath.eval(setBoard.getBoardChar(), BoardConst.PART_NUMBER);
            Object dateCodeObj = JSONPath.eval(setBoard.getBoardChar(), BoardConst.DATE_CODE);
            if (!BoardConst.VALUE_NC.equals(partNumberObj))
            {
                if (dstPartNumber.equals(partNumberObj))
                {
                    okPartNumbers.add((String) partNumberObj);
                }
                else
                {
                    ngPartNumbers.add((String) partNumberObj);
                }
            }
            if (!BoardConst.VALUE_NC.equals(dateCodeObj))
            {
                if (dstDateCode.equals(dateCodeObj))
                {
                    okDateCodes.add((String) dateCodeObj);
                }
                else
                {
                    ngDateCodes.add((String) dateCodeObj);
                }
            }
            Object lotNumObj = JSONPath.eval(setBoard.getBoardChar(), BoardConst.LOT_NUM);
            if (!BoardConst.VALUE_NC.equals(lotNumObj))
            {
                if (dstLotNo.equals(lotNumObj))
                {
                    okLotNums.add((String) lotNumObj);
                }
                else
                {
                    ngLotNums.add((String) lotNumObj);
                }
            }
            for (PCSBoard pcsBoard : front.getChildrenBoards())
            {
                partNumberObj = JSONPath.eval(pcsBoard.getBoardChar(), BoardConst.PART_NUMBER);
                if (!BoardConst.VALUE_NC.equals(partNumberObj))
                {
                    if (dstPartNumber.equals(partNumberObj))
                    {
                        okPartNumbers.add((String) partNumberObj);
                    }
                    else
                    {
                        ngPartNumbers.add((String) partNumberObj);
                    }
                }
                dateCodeObj = JSONPath.eval(pcsBoard.getBoardChar(), BoardConst.DATE_CODE);
                if (!BoardConst.VALUE_NC.equals(dateCodeObj))
                {
                    if (dstDateCode.equals(dateCodeObj))
                    {
                        okDateCodes.add((String) dateCodeObj);
                    }
                    else
                    {
                        ngDateCodes.add((String) dateCodeObj);
                    }
                }
                lotNumObj = JSONPath.eval(pcsBoard.getBoardChar(), BoardConst.LOT_NUM);
                if (!BoardConst.VALUE_NC.equals(lotNumObj))
                {
                    if (dstLotNo.equals(lotNumObj))
                    {
                        okLotNums.add((String) lotNumObj);
                    }
                    else
                    {
                        ngLotNums.add((String) lotNumObj);
                    }
                }
            }
        }
        compareResult.put("dst_part_number", dstPartNumber);
        if (ngPartNumbers.size() > 0)
        {
            compareResult.put("src_part_number", ngPartNumbers.get(0));
            compareResult.put("ModelSort", "NG");
        }
        else
        {
            compareResult.put("src_part_number", okPartNumbers.size() > 0 ? okPartNumbers.get(0) : BoardConst.VALUE_NC);
            compareResult.put("ModelSort", "OK");
        }
        compareResult.put("dst_date_code", dstDateCode);
        if (ngDateCodes.size() > 0)
        {
            compareResult.put("src_date_code", ngDateCodes.get(0));
            compareResult.put("CyclePeriodSort", "NG");
        }
        else
        {
            compareResult.put("src_date_code", okDateCodes.size() > 0 ? okDateCodes.get(0) : BoardConst.VALUE_NC);
            compareResult.put("CyclePeriodSort", "OK");
        }
        compareResult.put("dst_lot_num", dstLotNo);
        if (ngLotNums.size() > 0)
        {
            compareResult.put("src_lot_num", ngLotNums.get(0));
            compareResult.put("BatchNumSort", "NG");
        }
        else
        {
            compareResult.put("src_lot_num", okLotNums.size() > 0 ? okLotNums.get(0) : null);
            compareResult.put("BatchNumSort", "OK");
        }
        // mapping填充
        if (mappingResult != null)
        {
            String xoutCountFront = mappingResult.getString("xout_count_front");
            String xoutCountBack = mappingResult.getString("xout_count_back");
            String badMark = mappingResult.getString("bad_mark");
            String srcMappingResult = xoutCountFront.equals(xoutCountBack) ? xoutCountFront : (xoutCountFront + "/" + xoutCountBack);
            compareResult.put("src_mapping_result", srcMappingResult);
            compareResult.put("dst_mapping_result", badMark);
            compareResult.put("MappingSort", ObjectUtils.isEmpty(badMark) || xoutCountFront.equals(xoutCountBack) && xoutCountFront.equals(badMark) ? "OK" : "NG");
        }
        // 客户2D1、2D2填充
        String srcCustomer2D1 = front.getCustomerQRC1() != null && !BoardConst.VALUE_NC.equals(front.getCustomerQRC1()) // CCD-客户2D1
                ? front.getCustomerQRC1() : back.getCustomerQRC1() != null && !BoardConst.VALUE_NC.equals(back.getCustomerQRC1()) //
                ? back.getCustomerQRC1() : front.getCustomerQRC1();
        String srcCustomer2D2 = front.getCustomerQRC2() != null && !BoardConst.VALUE_NC.equals(front.getCustomerQRC2()) // CCD-客户2D2
                ? front.getCustomerQRC2() : back.getCustomerQRC2() != null && !BoardConst.VALUE_NC.equals(back.getCustomerQRC2()) //
                ? back.getCustomerQRC2() : front.getCustomerQRC2();
        compareResult.put("cus_2D1", srcCustomer2D1);
        compareResult.put("cus_2D2", srcCustomer2D2);
        jbCcdResolveResult.put("compareResult", compareResult);
    }

    private boolean isSkipCompare(String srcDataType, JSONObject srcPanel, JSONObject jbSort, String rowType, SortSplitRule.Item sortRule)
    {
        if (jbSort.containsKey(sortRule.getCode()))
        {
            String sortVal = jbSort.getString(sortRule.getCode());
            boolean checkDataType = false;
            if (sortVal != null)
            {
                String[] dateTypes = sortVal.split(",");
                checkDataType = !ObjectUtils.isEmpty(sortVal) && !"ALL".equals(sortVal) && dateTypes.length > 0 && !Arrays.asList(dateTypes).contains(srcDataType); // 如果规则要求是有码板件，然后指定数据板件类型不为空且不为ALL且与规则要求板件类型不匹配，则跳过
            }
            boolean hasQR = sortRule.getRequiredBarcode() != null && sortRule.getRequiredBarcode() && "None".equals(rowType); // 如果规则要求是有码板件，然后是无码板件，则跳过
            // 周期或批次比对时，CustomerQRC1和CustomerQRC2相等时，则跳过 added by jay-y 2024/05/22
            boolean isCycleOrBatch = (sortRule.getCode().endsWith(BoardConst.SORT_CYCLE_PERIOD)  // 周期比对
                    || sortRule.getCode().endsWith(BoardConst.SORT_BATCH_NUMBER)) // 批次比对
                    && !ObjectUtils.isEmpty(srcPanel) // 源板件不为空
                    && srcPanel.getString(BoardConst.CCD_CONTENT_SET_CUSTOMER_QRC1) != null && srcPanel.getString(BoardConst.CCD_CONTENT_SET_CUSTOMER_QRC1).equals(srcPanel.getString(BoardConst.CCD_CONTENT_SET_CUSTOMER_QRC2)) // CustomerQRC1和CustomerQRC2相等
                    ;
            return hasQR || checkDataType || isCycleOrBatch;
        }
        return true;
    }

    private CompareResult compare(String srcDataType, String srcOrient, JSONObject srcPanel, Integer srcIndex, String dstDataType, String dstOrient, JSONObject dstPanel, Integer dstIndex, JSONObject jbSort, String rowType, List<SortSplitRule.Item> sortRules, boolean needResult)
    {
        Comparator comparator = new DefaultComparator();
        for (SortSplitRule.Item sortRule : sortRules)
        {
            if (isSkipCompare(srcDataType, srcPanel, jbSort, rowType, sortRule))
            {
                continue;
            }
            CompareResult compareResult = this.compare(srcDataType, srcOrient, srcPanel, srcIndex, dstDataType, dstOrient, dstPanel, dstIndex, sortRule, comparator);
            if (compareResult != null && compareResult.isNG())
            {
                if (!needResult)
                {
                    throw new BoardCompareException(compareResult.getNgMsg(), compareResult.getStatus(), compareResult.getNgCode(), sortRule.getCode());
                }
                return compareResult;
            }
        }
        return null;
    }

    private CompareResult compare(String srcDataType, String srcOrient, JSONObject srcPanel, Integer srcIndex, JSONObject jbSort, String rowType, Map<String, Object> plan, List<SortSplitRule.Item> sortRules, boolean needResult)
    {
        Comparator comparator = new DefaultComparator();
        for (SortSplitRule.Item sortRule : sortRules)
        {
            if (isSkipCompare(srcDataType, srcPanel, jbSort, rowType, sortRule))
            {
                continue;
            }
            CompareResult compareResult = this.compare(srcDataType, srcOrient, srcPanel, srcIndex, plan, sortRule, comparator);
            if (compareResult != null && compareResult.isNG())
            {
                if (!needResult)
                {
                    throw new BoardCompareException(compareResult.getNgMsg(), compareResult.getStatus(), compareResult.getNgCode(), sortRule.getCode());
                }
                return compareResult;
            }
        }
        return null;
    }

    private CompareResult compare(String dataType, String orient, JSONObject panel, Integer index, Map<String, Object> plan, SortSplitRule.Item sortRule, Comparator comparator)
    {
        String name = sortRule.getName();
        CompareData compareData = index != null ? new CompareData(dataType, orient, panel, sortRule.getSrcKey(), index) : new CompareData(dataType, orient, panel, sortRule.getSrcKey());
        SplitRule splitRule = new SplitRule(name, sortRule.getSrcSplitIndex(), sortRule.getSrcSplitLength(), sortRule.getSrcSplitReverse());
        Object planValue = plan.get(sortRule.getDstKey());
        if (planValue == null || Const.NULL.equals(planValue))
        {
            return null;
        }
        CompareRule compareRule = new CompareRule(name, String.valueOf(planValue));
        return comparator.compare(compareData, splitRule, compareRule);
    }

    private CompareResult compare(String dataType, String orient, JSONObject srcPanel, Integer srcIndex, String dstDataType, String dstOrient, JSONObject dstPanel, Integer dstIndex, SortSplitRule.Item sortRule, Comparator comparator)
    {
        String name = sortRule.getName();
        CompareData srcCompareData = srcIndex != null ? new CompareData(dataType, orient, srcPanel, sortRule.getSrcKey(), srcIndex) : new CompareData(dataType, orient, srcPanel, sortRule.getSrcKey());
        SplitRule srcSplitRule = new SplitRule(name, sortRule.getSrcSplitIndex(), sortRule.getSrcSplitLength(), sortRule.getSrcSplitReverse());
        CompareData dstCompareData = dstIndex != null ? new CompareData(dstDataType, dstOrient, dstPanel, sortRule.getDstKey(), dstIndex) : new CompareData(dstDataType, dstOrient, dstPanel, sortRule.getDstKey());
        if (dstCompareData.getValue() == null || Const.NULL.equals(dstCompareData.getValue()))
        {
            return null;
        }
        SplitRule dstSplitRule = new SplitRule(name, sortRule.getDstSplitIndex(), sortRule.getDstSplitLength(), sortRule.getDstSplitReverse());
        CompareRule compareRule = new CompareRule(name);
        return comparator.compare(srcCompareData, srcSplitRule, dstCompareData, dstSplitRule, compareRule);
    }

    private void compare(CCDScanMessage ccdMessage, Comparator comparator, SortRule sortRule)
    {
        if (!ccdMessage.isReady())
        {
            throw new BoardCompareException("CCD数据不完整，无法进行比对");
        }
        List<CompareData> data = new LinkedList<>();
        String boardType = ccdMessage.getOrigin().getBoardType();
        SETBoard front = ccdMessage.getOrigin().getMultiAspect().get(BoardConst.ORIENT_FRONT);
        SETBoard back = ccdMessage.getOrigin().getMultiAspect().get(BoardConst.ORIENT_BACK);
        boolean useDouble = BoardConst.DOUBLE.equals(boardType) && front.isValid() && back.isValid();
        boolean useFront = BoardConst.SINGLE.equals(boardType) && front.isValid();
        boolean useBack = BoardConst.SINGLE.equals(boardType) && back.isValid();
        SplitRule splitRule = sortRule.getSplitRule(ccdMessage.getRecipe());
        if (useFront)
        {
            data.add(new CompareData(front, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
        }
        else if (useBack)
        {
            data.add(new CompareData(back, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
        }
        else if (useDouble)
        {
            data.add(new CompareData(front, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
            data.add(new CompareData(back, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
        }
        else if (!sortRule.isRequiredQR())
        {
            data.add(new CompareData(front, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
            data.add(new CompareData(back, sortRule.getCompareDataValueKey(), sortRule.isRequiredQR()));
        }
        for (CompareData d : data)
        {
            comparator.compare(d, splitRule, sortRule);
        }
    }

    @Data
    @AllArgsConstructor
    public static class SortRule
    {
        private String name;
        private String code;
        private Object value;
        private String compareDataValueKey;
        private String splitRuleValueKey;
        private String compareRuleTargetKey;
        private String dstCompareDataValueKey;
        private String dstSplitRuleValueKey;
        private boolean requiredQR;

        private SplitRule splitRule;
        private SplitRule dstSplitRule;

        public SortRule(String name, String code, Object value, String srcCompareDataValueKey, String srcSplitRuleValueKey, String dstCompareDataValueKey, String dstSplitRuleValueKey, String compareRuleTargetKey, boolean requiredQR)
        {
            this.name = name;
            this.code = code;
            this.value = value;
            this.compareDataValueKey = srcCompareDataValueKey;
            this.splitRuleValueKey = srcSplitRuleValueKey;
            this.dstCompareDataValueKey = dstCompareDataValueKey;
            this.dstSplitRuleValueKey = dstSplitRuleValueKey;
            this.compareRuleTargetKey = compareRuleTargetKey;
            this.requiredQR = requiredQR;
        }

        public SortRule(String name, String code, String compareDataValueKey, boolean requiredQR)
        {
            this(name, code, null, compareDataValueKey, null, null, null, null, requiredQR);
        }

        public SortRule(String name, String code, boolean requiredQR)
        {
            this(name, code, null, null, null, null, null, null, requiredQR);
        }

        public SplitRule getSplitRule(JSONObject recipe)
        {
            if (this.splitRuleValueKey != null)
            {
                String sortCode = recipe.getString(this.splitRuleValueKey);
                if (sortCode != null)
                {
                    this.splitRule = new SplitRule(this.name + "截取规则", sortCode);
                }
            }
            return this.splitRule;
        }

        public SplitRule getDstSplitRule(JSONObject recipe)
        {
            if (this.dstSplitRuleValueKey != null)
            {
                String sortCode = recipe.getString(this.dstSplitRuleValueKey);
                if (sortCode != null)
                {
                    this.dstSplitRule = new SplitRule(this.name + "截取规则", sortCode);
                }
            }
            return this.dstSplitRule;
        }
    }

    @Data
    @AllArgsConstructor
    public static class SplitRule
    {
        private String name;
        private String value;
        private int start;
        private int length;
        private Boolean reverse;

        public SplitRule(String name, int start, int length, Boolean reverse)
        {
            this.name = name;
            this.start = start;
            this.length = length;
            this.reverse = reverse;
        }

        public SplitRule(String name, String value)
        {
            this.name = name;
            if (!ObjectUtils.isEmpty(value))
            {
                this.value = value;
                String[] arr = value.split("\\|", -1);
                if (arr.length == 3)
                {
                    if (arr[0].equals("Left"))
                    {
                        this.reverse = false;
                    }
                    else if (arr[0].equals("Right"))
                    {
                        this.reverse = true;
                    }
                    this.start = Integer.parseInt(arr[1]);
                    this.length = Integer.parseInt(arr[2]);
                }
            }
        }

        public Result process(String dataName, String dataValue)
        {
            if (this.reverse == null)
            {
                return new Result(null, dataValue);
            }
            if (this.length == 0)
            {
                return new Result(null, dataValue);
            }
            else if (this.length < 0)
            {
                String err = String.format("%s根据规则名称{%s}规则方法{%s}截取字符失败:{截取设置长度%d必须大于0}", dataName, this.name, this.value, this.length);
                return new Result(err, null);
            }
            int dataLength = dataValue.length();
            int actualStart = this.start;
            int splitDataLength = this.start + this.length;
            if (this.reverse)
            {
                dataValue = new StringBuilder(dataValue).reverse().toString();
            }
            if (splitDataLength > dataLength)
            {
                String err = String.format("%s根据规则名称{%s}规则方法{%s}截取字符失败:{字符截止位%d超出界限}", dataName, this.name, this.value, splitDataLength);
                return new Result(err, null);
            }
            dataValue = dataValue.substring(actualStart, actualStart + this.length);
            if (this.reverse)
            {
                dataValue = new StringBuilder(dataValue).reverse().toString();
            }
            return new Result(null, dataValue);
        }


        @Data
        @AllArgsConstructor
        public static class Result
        {
            private String error;
            private String value;

            public boolean isOk()
            {
                return this.error == null;
            }

            public boolean isError()
            {
                return !this.isOk();
            }
        }
    }

    @Data
    @AllArgsConstructor
    public static class CompareRule
    {
        private String name;
        private Object origin;
        private Object target;

        public CompareRule(String name, String target)
        {
            this(name, null, target);
        }

        public CompareRule(String name)
        {
            this(name, null, null);
        }

        public Result process(String dataName, String errMessage, boolean isEquals)
        {
            String err = String.format("%s未检测到%s信息或字符%s信息或二维码%s信息", dataName, this.name, this.name, this.name);
            if (ObjectUtils.isEmpty(this.origin) || BoardConst.VALUE_NULL.equals(this.origin))
            {
                return new Result(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            boolean skip = this.origin.equals(BoardConst.VALUE_NC);
            if (this.origin instanceof String && !(this.target instanceof String))
            {
                this.target = String.valueOf(this.target);
            }
            //TODO 临时解决 2025/01/07
            if (this.name.contains("周期") && this.target != null)
            {
                int srcLen = ((String) this.origin).length();
                int tarLen = ((String) this.target).length();
                if (srcLen > tarLen)
                {
                    this.target = String.format("%0" + srcLen + "d", Integer.parseInt((String) this.target));
                }
            }
            if (!skip && !this.origin.equals(this.target) && !isEquals)
            {
                if (ObjectUtils.isEmpty(this.target))
                {
                    return new Result(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
                }
                err = !ObjectUtils.isEmpty(errMessage) ? errMessage : String.format("%s字符%s{%s}信息不等于设定%s{%s}信息", dataName, this.name, this.origin, this.name, this.target);
                return new Result(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            else if (!skip && this.origin.equals(this.target) && isEquals)
            {
                err = !ObjectUtils.isEmpty(errMessage) ? errMessage : String.format("%s检测到%s{%s}信息", dataName, this.name, this.origin);
                return new Result(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            return new Result(BoardConst.STATUS_OK, BoardConst.CODE_OK, BoardConst.BLANK);
        }

        public Result process(String dataName, String errMessage)
        {
            return this.process(dataName, errMessage, false);
        }

        public Result process(String dataName, boolean isEquals)
        {
            return this.process(dataName, null, isEquals);
        }

        public Result process(String dataName)
        {
            return this.process(dataName, null, false);
        }

        public static class Result extends CompareResult
        {
            public Result(String status, int ngCode, String ngMsg)
            {
                super(status, ngCode, ngMsg);
            }
        }
    }

    @Data
    public static class CompareData
    {
        private String name;
        private String type;
        private String orient;
        private Object origin;
        private String path;
        private Integer index;
        private String value;

        public CompareData(Board board, String path, boolean isChar)
        {
            if (board != null)
            {
                this.name = board.getBoardName();
                this.type = board.getBoardCategory();
                this.orient = board.getBoardOrient();
                this.path = path;
                if (isChar)
                {
                    this.origin = board.getBoardChar();
                    this.resolve();
                }
                else
                {
                    this.origin = board;
                    // Java反射获取对象属性值
                    Field field = ReflectionUtils.findField(board.getClass(), path);
                    if (field != null)
                    {
                        ReflectionUtils.makeAccessible(field);
                        this.value = String.valueOf(ReflectionUtils.getField(field, board));
                        this.index = board.getBoardIndex();
                    }
                }
            }
        }

        public CompareData(Board board, String property)
        {
            this(board, property, false);
        }

        public CompareData(String type, String orient, Object origin, String path, Integer index, String value)
        {
            this.type = type;
            this.orient = orient;
            this.origin = origin;
            this.path = path;
            this.index = index;
            String dataType = this.type + (!ObjectUtils.isEmpty(this.index) ? "[" + this.index + "]" : BoardConst.BLANK);
            String dataOrient = "back".equals(this.orient) ? "反" : "正";
            this.name = String.format("%s板%s面", dataType, dataOrient);
            if (ObjectUtils.isEmpty(value))
            {
                this.resolve();
            }
            else
            {
                this.value = value;
            }
        }

        public CompareData(String type, String orient, Object origin, String path, Integer index)
        {
            this(type, orient, origin, path, index, null);
        }

        public CompareData(String type, String orient, Object origin, String path)
        {
            this(type, orient, origin, path, null, null);
        }

        private void resolve()
        {
            if (ObjectUtils.isEmpty(this.origin) || ObjectUtils.isEmpty(this.path))
            {
                this.value = BoardConst.BLANK;
                return;
            }
            try
            {
                if (this.origin instanceof String)
                {
                    this.origin = JSON.parse((String) this.origin);
                }
                this.value = String.valueOf(JSONPath.eval(this.origin, this.path));
            }
            catch (Exception ex)
            {
                log.error("解析JSON数据异常", ex);
            }
        }

        public boolean isNULL()
        {
            return BoardConst.VALUE_NULL.equals(this.value);
        }

        public boolean isNC()
        {
            return BoardConst.VALUE_NC.equals(this.value);
        }
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    public static class CompareResult extends BoardCompareResult
    {
        private String status;

        @JsonProperty("ng_code")
        private int ngCode;

        @JsonProperty("ng_msg")
        private String ngMsg;

        @JsonProperty("deposit_position")
        private Integer depositPosition;

        public CompareResult(String status, int ngCode, String ngMsg)
        {
            super(status, ngCode, ngMsg, null);
            this.status = status;
            this.ngCode = ngCode;
            this.ngMsg = ngMsg;
        }

        @JsonIgnore
        @JSONField(serialize = false, deserialize = false)
        public boolean isOK()
        {
            return BoardConst.STATUS_OK.equals(this.status);
        }

        @JsonIgnore
        @JSONField(serialize = false, deserialize = false)
        public boolean isNG()
        {
            return BoardConst.STATUS_NG.equals(this.status);
        }

        public JSONObject toJSONObject()
        {
            JSONObject result = new JSONObject();
            result.put("array_status", this.status);
            result.put("array_ng_code", this.ngCode);
            result.put("array_ng_msg", this.ngMsg);
            if (this.depositPosition != null)
            {
                result.put("deposit_position", this.depositPosition);
            }
            return result;
        }
    }

    public interface Comparator
    {
        default void compare(CompareData data, SplitRule splitRule, SortRule sortRule) throws BoardCompareException
        {
            String dataName = data.getName();
            String dataValue = data.getValue();
            if (splitRule != null)
            {
                SplitRule.Result splitRuleResult = splitRule.process(dataName, dataValue);
                if (splitRuleResult.isError())
                {
                    throw new BoardCompareException(splitRuleResult.getError());
                }
                dataValue = splitRuleResult.getValue();
            }
            CompareRule rule = new CompareRule(sortRule.getName(), dataValue, sortRule.getValue());
            CompareResult result = rule.process(dataName, true);
            if (result.isNG())
            {
                throw new BoardCompareException(result.getNgMsg());
            }
        }

        default CompareResult compare(CompareData data, CompareRule compareRule)
        {
            if (data.isNC())
            {
                return new CompareResult(BoardConst.STATUS_OK, BoardConst.CODE_OK, BoardConst.BLANK);
            }
            if (data.isNULL())
            {
                String err = String.format("%s未检测到字符%s信息或二维码%s信息", data.getName(), compareRule.getName(), compareRule.getName());
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            if (!ObjectUtils.isEmpty(compareRule.getOrigin()))
            {
                if (compareRule.getOrigin().equals(data.getValue()))
                {
                    return new CompareResult(BoardConst.STATUS_OK, BoardConst.CODE_OK, BoardConst.BLANK);
                }
                String err = String.format("%s%s信息与%s信息不一致", data.getName(), compareRule.getName(), compareRule.getName());
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
            }
            return null;
        }

        default CompareResult compare(CompareData data, SplitRule splitRule, CompareRule compareRule)
        {
            CompareResult compareResult = this.compare(data, compareRule);
            if (compareResult != null)
            {
                return compareResult;
            }
            String dataValue = data.getValue();
            String dataName = data.getName();
            SplitRule.Result splitRuleResult = splitRule.process(dataName, dataValue);
            if (splitRuleResult.isError())
            {
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, splitRuleResult.getError());
            }
            dataValue = splitRuleResult.getValue();
            compareRule.setOrigin(dataValue);
            return compareRule.process(dataName);
        }

        default CompareResult compare(CompareData srcData, SplitRule srcSplitRule, CompareData dstData, SplitRule dstSplitRule, CompareRule compareRule)
        {
            CompareResult srcCompareResult = this.compare(srcData, compareRule);
            if (srcCompareResult != null)
            {
                return srcCompareResult;
            }
            CompareResult dstCompareResult = this.compare(dstData, compareRule);
            if (dstCompareResult != null)
            {
                return dstCompareResult;
            }
            String srcDataValue = srcData.getValue();
            String srcDataName = srcData.getName();
            SplitRule.Result srcSplitRuleResult = srcSplitRule.process(srcDataName, srcDataValue);
            if (srcSplitRuleResult.isError())
            {
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, srcSplitRuleResult.getError());
            }
            srcDataValue = srcSplitRuleResult.getValue();
            compareRule.setOrigin(srcDataValue);
            String dstDataValue = dstData.getValue();
            String dstDataName = dstData.getName();
            SplitRule.Result dstSplitRuleResult = dstSplitRule.process(dstDataName, dstDataValue);
            if (dstSplitRuleResult.isError())
            {
                return new CompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, dstSplitRuleResult.getError());
            }
            dstDataValue = dstSplitRuleResult.getValue();
            String ruleName = compareRule.getName();
            compareRule.setOrigin(srcDataValue);
            compareRule.setTarget(dstDataValue);
            String errMessage = String.format("%s%s信息{%s}与%s%s信息{%s}不一致", srcDataName, ruleName, srcDataValue, dstDataName, ruleName, dstDataValue);
            return compareRule.process(dstDataName, errMessage);
        }
    }

    @Data
    @AllArgsConstructor
    public static class DefaultComparator implements Comparator
    {
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    @AllArgsConstructor
    public static class DirectionComparator extends DefaultComparator implements Comparator
    {
        @Override
        public void compare(CompareData data, SplitRule splitRule, SortRule sortRule) throws BoardCompareException
        {
            String dataName = data.getName();
            String dataValue = data.getValue();
            if (splitRule != null)
            {
                SplitRule.Result splitRuleResult = splitRule.process(dataName, dataValue);
                if (splitRuleResult.isError())
                {
                    throw new BoardCompareException(splitRuleResult.getError());
                }
                dataValue = splitRuleResult.getValue();
            }
            // SET正面（或反面）初定位识别异常请前往CCD系统查看
            CompareRule rule1 = new CompareRule(sortRule.getName(), dataValue, BoardConst.VALUE_NULL_1);
            String rule1Msg = String.format("%s定位训练异常，请前往CCD系统查看", dataName);
            if (rule1.process(dataName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new BoardCompareException(rule1Msg, BoardConst.SORT_BOARD_DIRECTION);
            }
            // SET正面（或反面）板件放反导致异常
            CompareRule rule2 = new CompareRule(sortRule.getName(), dataValue, BoardConst.VALUE_NULL_2);
            String rule2Msg = String.format("%s产品特征异常，请前往CCD系统查看", dataName);
            if (rule2.process(dataName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new BoardCompareException(rule2Msg, BoardConst.SORT_BOARD_DIRECTION);
            }
            // SET正面（或反面）产品训练异常
            CompareRule rule3 = new CompareRule(sortRule.getName(), dataValue, BoardConst.VALUE_NULL_3);
            String rule3Msg = String.format("%s产品训练异常，请前往CCD系统查看", dataName);
            if (rule3.process(dataName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new BoardCompareException(rule3Msg, BoardConst.SORT_BOARD_DIRECTION);
            }
            // CCD手动NG过板
            CompareRule rule4 = new CompareRule(sortRule.getName(), dataValue, BoardConst.VALUE_NULL_4);
            String rule4Msg = "CCD手动释放NG板";
            if (rule4.process(dataName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new BoardCompareException(rule4Msg, BoardConst.SORT_BOARD_DIRECTION);
            }
        }
    }
}
