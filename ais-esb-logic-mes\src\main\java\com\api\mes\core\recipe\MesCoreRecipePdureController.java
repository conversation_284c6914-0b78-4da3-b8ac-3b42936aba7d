package com.api.mes.core.recipe;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 1.创建作业工序临时表
 * 2.更新作业工序临时表工序状态
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-25
 */
@RestController
@Slf4j
@RequestMapping("/mes/core/recipe")
public class MesCoreRecipePdureController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;

    //创建作业工序临时表(包括工序、BOM、质量)
    @Transactional
    @RequestMapping(value = "/MesCoreRecipePdureWorkCreate", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkCreate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipePdureWorkCreate";
        String transResult = "";
        String errorMsg = "";
        String make_order = "";
        String station_code = "";
        try {
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("");
            String prod_line_id = jsonParas.getString("prod_line_id");//产线ID
            String prod_line_code = jsonParas.getString("prod_line_code");//产线CODE
            String station_id = jsonParas.getString("station_id");//工位ID
            station_code = jsonParas.getString("station_code");//工位号
            make_order = jsonParas.getString("make_order");//订单号
            //1.先删除临时表
            String delte01 = "delete from c_mes_me_cr_pdure " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            String delte02 = "delete from c_mes_me_cr_pdure_bom " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            String delte03 = "delete from c_mes_me_cr_pdure_quality " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte01, false, request, apiRoutePath);
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte02, false, request, apiRoutePath);
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte03, false, request, apiRoutePath);
            //2.查询工艺路线配方
            String sqlRecipe = "select c.recipe_id " +
                    "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " +
                    "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " +
                    "on b.recipe_id=c.recipe_id " +
                    "where a.prod_line_id=" + prod_line_id + " and a.make_order='" + make_order + "' " +
                    "and c.recipe_type='PROCESS' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlRecipe,
                    false, request, apiRoutePath);
            if (itemListRecipe == null || itemListRecipe.size() <= 0) {
                errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}未找到工艺路线,请检查是否修改了正在生产的订单或者删除了工艺路线";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String recipe_id = itemListRecipe.get(0).get("recipe_id").toString();
            //3.根据工位号以及RECIPE_ID查找到工艺路线组
            String sqlCraftRoute = "select a.craft_route_id " +
                    "from c_mes_fmod_recipe_cr a inner join c_mes_fmod_recipe_cr_main b " +
                    "on a.craft_route_main_id=b.craft_route_main_id " +
                    "where a.enable_flag='Y' and b.enable_flag='Y' " +
                    "and a.station_id=" + station_id + " and b.recipe_id=" + recipe_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListCraftRoute = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlCraftRoute,
                    false, request, apiRoutePath);
            if (itemListCraftRoute != null && itemListCraftRoute.size() > 0) {
                String craft_route_id = itemListCraftRoute.get(0).get("craft_route_id").toString();
                //3.1 增加工序临时表
                String sqlInsertPdure = "insert into c_mes_me_cr_pdure " +
                        "(created_by,creation_date,proceduce_id,prod_line_code,station_code," +
                        "proceduce_code,proceduce_des,proceduce_type,proceduce_attr,work_order_by," +
                        "data_move_flag,allow_jump_flag,pic_name,pic_path,proceduce_status) " +
                        "select " +
                        "'" + station_code + "' as created_by," +
                        "'" + nowDateTime + "' as creation_date," +
                        "proceduce_id," +
                        "'" + prod_line_code + "' as prod_line_code," +
                        "'" + station_code + "' as station_code," +
                        "proceduce_code,proceduce_des,proceduce_type," +
                        "COALESCE(proceduce_attr,'') proceduce_attr," +
                        "work_order_by,data_move_flag,allow_jump_flag," +
                        "COALESCE(pic_name,'') pic_name," +
                        "COALESCE(pic_path,'') pic_path," +
                        "'PLAN' as proceduce_status " +
                        "from c_mes_fmod_recipe_cr_pdure " +
                        "where craft_route_id=" + craft_route_id + " and enable_flag='Y' " +
                        "order by work_order_by";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertPdure, false, request, apiRoutePath);
                //3.2 增加物料BOM临时表
                String sqlInsertBom = "insert into c_mes_me_cr_pdure_bom " +
                        "(created_by,creation_date,material_id,proceduce_id,prod_line_code,station_code," +
                        "material_code,material_des,draw_code,usage,material_uom," +
                        "material_attr,material_cost,remarks,max_count,min_count," +
                        "rack_code,rack_max_count,rack_min_count,box_type,box_set," +
                        "material_index,material_gg,main_material_flag,verify_flag," +
                        "batch_flag,material_rule_exact,material_rule_card,stock_flag," +
                        "bom_version,verify_finish_flag,batch_finish_flag," +
                        "stock_count,material_batch,material_supplier,supplier_des," +
                        "lable_barcode,exact_barcode,fchong_flag) " +
                        "select " +
                        "'" + station_code + "' as created_by," +
                        "'" + nowDateTime + "' as creation_date," +
                        "a.material_id,b.proceduce_id," +
                        "'" + prod_line_code + "' as prod_line_code," +
                        "'" + station_code + "' as station_code," +
                        "a.material_code,a.material_des," +
                        "COALESCE(a.draw_code,'') draw_code,a.usage," +
                        "COALESCE(a.material_uom,'') material_uom," +
                        "COALESCE(a.material_attr,'') material_attr," +
                        "COALESCE(a.material_cost,0) material_cost," +
                        "COALESCE(a.remarks,'') remarks," +
                        "COALESCE(a.max_count,0) max_count," +
                        "COALESCE(a.min_count,0) min_count," +
                        "COALESCE(a.rack_code,'') rack_code," +
                        "COALESCE(a.rack_max_count,0) rack_max_count," +
                        "COALESCE(a.rack_min_count,0) rack_min_count," +
                        "COALESCE(a.box_type,'') box_type," +
                        "COALESCE(a.box_set,0) box_set," +
                        "COALESCE(a.material_index,1) material_index," +
                        "COALESCE(a.material_gg,'') material_gg," +
                        "a.main_material_flag,a.verify_flag,a.batch_flag," +
                        "COALESCE(a.material_rule_exact,'') material_rule_exact," +
                        "COALESCE(a.material_rule_card,'') material_rule_card," +
                        "a.stock_flag,COALESCE(a.bom_version,'') bom_version," +
                        "(case when a.verify_flag='Y' then 'N' else 'Y' end) as verify_finish_flag," +
                        "(case when (a.batch_flag='Y' and (c.material_batch is null or c.material_batch='')) then 'N' else 'Y' end) as batch_finish_flag," +
                        "COALESCE(c.stock_count,0) stock_count," +
                        "COALESCE(c.material_batch,'') material_batch," +
                        "COALESCE(c.material_supplier,'') material_supplier," +
                        "COALESCE(c.supplier_des,'') supplier_des," +
                        "COALESCE(c.lable_barcode,'') lable_barcode," +
                        "'' as exact_barcode,a.fchong_flag " +
                        "from c_mes_fmod_recipe_cr_pdure_bom a inner join c_mes_fmod_recipe_cr_pdure b " +
                        "on a.proceduce_id=b.proceduce_id " +
                        "left join c_mes_me_station_stock c " +
                        "on a.material_code=c.material_code and c.prod_line_code='" + prod_line_code + "' and c.station_code='" + station_code + "' " +
                        "where b.proceduce_type='MATERIAL_SCAN' and b.craft_route_id=" + craft_route_id + " " +
                        "and b.enable_flag='Y' and a.enable_flag='Y' " +
                        "order by b.proceduce_id,a.material_id";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertBom, false, request, apiRoutePath);
                //3.3 增加质量数据临时表
                String sqlInsertQuality = "insert into c_mes_me_cr_pdure_quality " +
                        "(created_by,creation_date,quality_id,proceduce_id,prod_line_code,station_code," +
                        "quality_from,tag_id,tag_quality_sign_id,group_order,group_name,tag_col_order," +
                        "tag_col_inner_order,location_x,location_y,bolt_code,torque,progm_num," +
                        "control_type,control_width,control_height,quality_for,tag_des,tag_uom," +
                        "theory_value,down_limit,upper_limit,quality_status,tag_value,quality_save_flag) " +
                        "select " +
                        "'" + station_code + "' as created_by," +
                        "'" + nowDateTime + "' as creation_date," +
                        "a.quality_id,b.proceduce_id," +
                        "'" + prod_line_code + "' as prod_line_code," +
                        "'" + station_code + "' as station_code," +
                        "a.quality_from,COALESCE(a.tag_id,0) tag_id,COALESCE(a.tag_quality_sign_id,0) tag_quality_sign_id," +
                        "a.group_order,a.group_name," +
                        "COALESCE(a.tag_col_order,1) tag_col_order," +
                        "COALESCE(a.tag_col_inner_order,1) tag_col_inner_order," +
                        "a.location_x,a.location_y," +
                        "COALESCE(a.bolt_code,0) bolt_code," +
                        "COALESCE(a.torque,'') torque," +
                        "COALESCE(a.progm_num,0) progm_num," +
                        "a.control_type,a.control_width,a.control_height,a.quality_for,a.tag_des," +
                        "COALESCE(a.tag_uom,'') tag_uom," +
                        "COALESCE(a.theory_value,'') theory_value," +
                        "COALESCE(a.down_limit,'') down_limit," +
                        "COALESCE(a.upper_limit,'') upper_limit," +
                        "'WAIT' as quality_status,'' as tag_value,a.quality_save_flag " +
                        "from c_mes_fmod_recipe_cr_pdure_quality a inner join c_mes_fmod_recipe_cr_pdure b " +
                        "on a.proceduce_id=b.proceduce_id " +
                        "where (b.proceduce_type='AUTO_QUALITY' or b.proceduce_type='SHAFT_QUALITY') and b.craft_route_id=" + craft_route_id + " " +
                        "and b.enable_flag='Y' and a.enable_flag='Y' " +
                        "order by b.proceduce_id,a.group_order,a.tag_col_order,a.tag_col_inner_order";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertQuality, false, request, apiRoutePath);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}创建工序作业表发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //创建作业工序临时表(包括工序、BOM、质量)--其中BOM来源于机型BOM(c_mes_fmod_small_model_bom)
    @Transactional
    @RequestMapping(value = "/MesCoreRecipePdureWorkCreate2", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkCreate2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = " mes/core/recipe/MesCoreRecipePdureWorkCreate2";
        String transResult = "";
        String errorMsg = "";
        String make_order = "";
        String station_code = "";
        try {
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("");
            String prod_line_id = jsonParas.getString("prod_line_id");//产线ID
            String prod_line_code = jsonParas.getString("prod_line_code");//产线CODE
            String station_id = jsonParas.getString("station_id");//工位ID
            station_code = jsonParas.getString("station_code");//工位号
            make_order = jsonParas.getString("make_order");//订单号
            //1.先删除临时表
            String delte01 = "delete from c_mes_me_cr_pdure " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            String delte02 = "delete from c_mes_me_cr_pdure_bom " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            String delte03 = "delete from c_mes_me_cr_pdure_quality " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte01, false, request, apiRoutePath);
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte02, false, request, apiRoutePath);
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte03, false, request, apiRoutePath);
            //2.查询工艺路线配方
            String sqlRecipe = "select c.recipe_id,a.small_model_type " +
                    "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " +
                    "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " +
                    "on b.recipe_id=c.recipe_id " +
                    "where a.prod_line_id=" + prod_line_id + " and a.make_order='" + make_order + "' " +
                    "and c.recipe_type='PROCESS' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlRecipe,
                    false, request, apiRoutePath);
            if (itemListRecipe == null || itemListRecipe.size() <= 0) {
                errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}未找到工艺路线,请检查是否修改了正在生产的订单或者删除了工艺路线";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String recipe_id = itemListRecipe.get(0).get("recipe_id").toString();
            String small_model_type = itemListRecipe.get(0).get("small_model_type").toString();
            //3.根据工位号以及RECIPE_ID查找到工艺路线组
            String sqlCraftRoute = "select a.craft_route_id " +
                    "from c_mes_fmod_recipe_cr a inner join c_mes_fmod_recipe_cr_main b " +
                    "on a.craft_route_main_id=b.craft_route_main_id " +
                    "where a.enable_flag='Y' and b.enable_flag='Y' " +
                    "and a.station_id=" + station_id + " and b.recipe_id=" + recipe_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListCraftRoute = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlCraftRoute,
                    false, request, apiRoutePath);

            if (itemListCraftRoute != null && itemListCraftRoute.size() > 0) {
                String craft_route_id = itemListCraftRoute.get(0).get("craft_route_id").toString();
                //3.1 增加工序临时表
                String sqlInsertPdure = "insert into c_mes_me_cr_pdure " +
                        "(created_by,creation_date,proceduce_id,prod_line_code,station_code," +
                        "proceduce_code,proceduce_des,proceduce_type,proceduce_attr,work_order_by," +
                        "data_move_flag,allow_jump_flag,pic_name,pic_path,proceduce_status) " +
                        "select " +
                        "'" + station_code + "' as created_by," +
                        "'" + nowDateTime + "' as creation_date," +
                        "proceduce_id," +
                        "'" + prod_line_code + "' as prod_line_code," +
                        "'" + station_code + "' as station_code," +
                        "proceduce_code,proceduce_des,proceduce_type," +
                        "COALESCE(proceduce_attr,'') proceduce_attr," +
                        "work_order_by,data_move_flag,allow_jump_flag," +
                        "COALESCE(pic_name,'') pic_name," +
                        "COALESCE(pic_path,'') pic_path," +
                        "'PLAN' as proceduce_status " +
                        "from c_mes_fmod_recipe_cr_pdure " +
                        "where craft_route_id=" + craft_route_id + " and enable_flag='Y' " +
                        "order by work_order_by";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertPdure, false, request, apiRoutePath);
                //3.2 增加物料BOM临时表
                String sqlPdureMSelect = "select b.proceduce_id " +
                        "from c_mes_fmod_recipe_cr_pdure b " +
                        "where b.proceduce_type='MATERIAL_SCAN' and b.craft_route_id=" + craft_route_id + " " +
                        "and b.enable_flag='Y' LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListMPdure = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPdureMSelect,
                        false, request, apiRoutePath);
                if (itemListMPdure != null && itemListMPdure.size() > 0) {
                    String proceduce_id_m = itemListMPdure.get(0).get("proceduce_id").toString();
                    String sqlInsertBom = "insert into c_mes_me_cr_pdure_bom " +
                            "(created_by,creation_date,material_id,proceduce_id,prod_line_code,station_code," +
                            "material_code,material_des,draw_code,usage,material_uom," +
                            "material_attr,material_cost,remarks,max_count,min_count," +
                            "rack_code,rack_max_count,rack_min_count,box_type,box_set," +
                            "material_index,material_gg,main_material_flag,verify_flag," +
                            "batch_flag,material_rule_exact,material_rule_card,stock_flag," +
                            "bom_version,verify_finish_flag,batch_finish_flag," +
                            "stock_count,material_batch,material_supplier,supplier_des," +
                            "lable_barcode,exact_barcode,fchong_flag) " +
                            "select " +
                            "'" + station_code + "' as created_by," +
                            "'" + nowDateTime + "' as creation_date," +
                            "a.material_id," + proceduce_id_m + " as proceduce_id," +
                            "'" + prod_line_code + "' as prod_line_code," +
                            "'" + station_code + "' as station_code," +
                            "a.material_code,a.material_des," +
                            "COALESCE(a.draw_code,'') draw_code,a.usage," +
                            "COALESCE(a.material_uom,'') material_uom," +
                            "COALESCE(a.material_attr,'') material_attr," +
                            "COALESCE(a.material_cost,0) material_cost," +
                            "COALESCE(a.remarks,'') remarks," +
                            "COALESCE(a.max_count,0) max_count," +
                            "COALESCE(a.min_count,0) min_count," +
                            "COALESCE(a.rack_code,'') rack_code," +
                            "COALESCE(a.rack_max_count,0) rack_max_count," +
                            "COALESCE(a.rack_min_count,0) rack_min_count," +
                            "COALESCE(a.box_type,'') box_type," +
                            "COALESCE(a.box_set,0) box_set," +
                            "COALESCE(a.material_index,1) material_index," +
                            "COALESCE(a.material_gg,'') material_gg," +
                            "a.main_material_flag,a.verify_flag,a.batch_flag," +
                            "COALESCE(a.material_rule_exact,'') material_rule_exact," +
                            "COALESCE(a.material_rule_card,'') material_rule_card," +
                            "a.stock_flag,COALESCE(a.bom_version,'') bom_version," +
                            "(case when a.verify_flag='Y' then 'N' else 'Y' end) as verify_finish_flag," +
                            "(case when (a.batch_flag='Y' and (c.material_batch is null or c.material_batch='')) then 'N' else 'Y' end) as batch_finish_flag," +
                            "COALESCE(c.stock_count,0) stock_count," +
                            "COALESCE(c.material_batch,'') material_batch," +
                            "COALESCE(c.material_supplier,'') material_supplier," +
                            "COALESCE(c.supplier_des,'') supplier_des," +
                            "COALESCE(c.lable_barcode,'') lable_barcode," +
                            "'' as exact_barcode,a.fchong_flag " +
                            "from c_mes_fmod_small_model_bom a inner join c_mes_fmod_small_model b " +
                            "on a.small_type_id=b.small_type_id " +
                            "left join c_mes_me_station_stock c " +
                            "on a.material_code=c.material_code and c.prod_line_code='" + prod_line_code + "' and c.station_code='" + station_code + "' " +
                            "where b.small_model_type='" + small_model_type + "' and b.prod_line_id=" + prod_line_id + " " +
                            "and a.station_id=" + station_id + " " +
                            "and b.enable_flag='Y' and a.enable_flag='Y' " +
                            "order by a.material_id";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertBom, false, request, apiRoutePath);
                }
                //3.3 增加质量数据临时表
                String sqlInsertQuality = "insert into c_mes_me_cr_pdure_quality " +
                        "(created_by,creation_date,quality_id,proceduce_id,prod_line_code,station_code," +
                        "quality_from,tag_id,tag_quality_sign_id,group_order,group_name,tag_col_order," +
                        "tag_col_inner_order,location_x,location_y,bolt_code,torque,progm_num," +
                        "control_type,control_width,control_height,quality_for,tag_des,tag_uom," +
                        "theory_value,down_limit,upper_limit,quality_status,tag_value,quality_save_flag) " +
                        "select " +
                        "'" + station_code + "' as created_by," +
                        "'" + nowDateTime + "' as creation_date," +
                        "a.quality_id,b.proceduce_id," +
                        "'" + prod_line_code + "' as prod_line_code," +
                        "'" + station_code + "' as station_code," +
                        "a.quality_from,COALESCE(a.tag_id,0) tag_id,COALESCE(a.tag_quality_sign_id,0) tag_quality_sign_id," +
                        "a.group_order,a.group_name," +
                        "COALESCE(a.tag_col_order,1) tag_col_order," +
                        "COALESCE(a.tag_col_inner_order,1) tag_col_inner_order," +
                        "a.location_x,a.location_y," +
                        "COALESCE(a.bolt_code,0) bolt_code," +
                        "COALESCE(a.torque,'') torque," +
                        "COALESCE(a.progm_num,0) progm_num," +
                        "a.control_type,a.control_width,a.control_height,a.quality_for,a.tag_des," +
                        "COALESCE(a.tag_uom,'') tag_uom," +
                        "COALESCE(a.theory_value,'') theory_value," +
                        "COALESCE(a.down_limit,'') down_limit," +
                        "COALESCE(a.upper_limit,'') upper_limit," +
                        "'' as quality_status,'' as tag_value,a.quality_save_flag " +
                        "from c_mes_fmod_recipe_cr_pdure_quality a inner join c_mes_fmod_recipe_cr_pdure b " +
                        "on a.proceduce_id=b.proceduce_id " +
                        "where (b.proceduce_type='AUTO_QUALITY' or b.proceduce_type='SHAFT_QUALITY') and b.craft_route_id=" + craft_route_id + " " +
                        "and b.enable_flag='Y' and a.enable_flag='Y' " +
                        "order by b.proceduce_id,a.group_order,a.tag_col_order,a.tag_col_inner_order";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertQuality, false, request, apiRoutePath);

                //1.根据模组配方ID查找LIMIT项目 双环创建工序时候往
                if ("SH".equals(projectCode)) {
                    //查询主物料不用扫码批次验证
                    String sqlMaterial = "SELECT COALESCE(model_bom.material_code,'') " +
                            "material_code ,COALESCE(material_rule.code_start_index,0) code_start_index, " +
                            "model_bom.verify_flag FROM c_mes_fmod_small_model_bom model_bom " +
                            "JOIN c_mes_fmod_recipe_material_rule material_rule ON  " +
                            "model_bom.material_rule_exact = material_rule.material_rule_code " +
                            " WHERE model_bom.station_id = '"+station_id+"'";
                    List<Map<String, Object>> itemListMaterial = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMaterial,
                            false, request, apiRoutePath);
                    if (itemListMaterial != null && itemListMaterial.size() > 0) {
                        String sqlUpdateMaterialBom = "update c_mes_me_cr_pdure_bom set " +
                                "verify_finish_flag='N'," +
                                "batch_finish_flag='N' " +
                                "where station_code='" + station_code + "' " ;
                        cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdateMaterialBom, false, request, apiRoutePath);
                    }
                    String sqlBatch = "SELECT batch.station_code,batch.material_code,batch.batch_code,batch.batch_count,batch.switch_count,batch.used_count,pdure_batch.usage FROM " +
                            "c_mes_me_batch batch JOIN c_mes_me_cr_pdure_bom pdure_batch" +
                            " ON batch.station_code = pdure_batch.station_code AND batch.material_code = pdure_batch.material_code " +
                            "WHERE batch.enable_flag = 'Y' and batch.station_code = '" + station_code + "' and batch_flag='Y' and verify_flag='N' " +
                            "AND batch.batch_id IN ( SELECT MIN ( batch_id ) AS batch_id FROM c_mes_me_batch  WHERE station_code = '" + station_code + "' GROUP BY material_code ) ";
                    List<Map<String, Object>> itemListBatch = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlBatch,
                            false, request, apiRoutePath);
                    if (itemListBatch != null && itemListBatch.size() > 0) {
                        for (int i = 0; i < itemListBatch.size(); i++) {
                            String batch_code = itemListBatch.get(i).get("batch_code").toString();
                            String material_code = itemListBatch.get(i).get("material_code").toString();
                            Integer used_count = Integer.parseInt(itemListBatch.get(i).get("used_count").toString());
                            Integer batch_count = Integer.parseInt(itemListBatch.get(i).get("batch_count").toString());
                            Integer switch_count = Integer.parseInt(itemListBatch.get(i).get("switch_count").toString());
                            if (batch_count <= used_count) continue;
                            int usage = Integer.parseInt(itemListBatch.get(i).get("usage").toString());
                            int countTotal = used_count + usage;
                            //如果查询出一个满足条件，就不在进入  //判断写入到scada点位里面去
                            //1.判断总数量（例如500）-使用数量（490） 报警数量10 就写入scada
                            //2.判断总数量（例如500）-使用数量（510） 报警数量10  超出总数量 就写入scada
                            if (((batch_count - used_count) <= switch_count) || (batch_count - used_count) < 0) {
                                errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, station_code + "/MesStatus/MesS_BoltCode", "1", true);
                            }
                            //判断当前的总数量 等于 使用数量 就把这条数据的有效标识打成N
                            if (batch_count == countTotal) {
                                String sqlUpdateModelBom = "update c_mes_me_batch set " +
                                        "enable_flag='N' " +
                                        "where material_code='" + material_code + "' " +
                                        "and station_id='" + station_id + "'";
                                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdateModelBom, false, request, apiRoutePath);
                            }
                            String sqlUpdatePdureBom = "update c_mes_me_cr_pdure_bom set " +
                                    "material_batch='" + batch_code + "'," +
                                    "verify_finish_flag='Y'," +
                                    "batch_finish_flag='Y' " +
                                    "where material_code='" + material_code + "' " +
                                    "and station_code='" + station_code + "'";


                            String sqlUpdateBatch = "update c_mes_me_batch set " +
                                    "used_count='" + countTotal + "' " +
                                    "where material_code='" + material_code + "' " +
                                    "and station_code='" + station_code + "' " +
                                    "AND batch_id IN ( SELECT MIN ( batch_id ) AS batch_id FROM c_mes_me_batch GROUP BY material_code )";
                            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdatePdureBom, false, request, apiRoutePath);
                            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdateBatch, false, request, apiRoutePath);
                        }
                    }
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}创建工序作业表发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //创建作业工序临时表(包括工序、BOM、质量)--其中BOM来源于机型BOM(c_mes_fmod_small_model_bom)
    @Transactional
    @RequestMapping(value = "/MesCoreRecipePdureWorkCreate3", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkCreate3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = " mes/core/recipe/MesCoreRecipePdureWorkCreate3";
        String transResult = "";
        String errorMsg = "";
        String make_orders = "";
        String station_code = "";
        try {
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("");
            String prod_line_id = jsonParas.getString("prod_line_id");//产线ID
            String prod_line_code = jsonParas.getString("prod_line_code");//产线CODE
            String station_id = jsonParas.getString("station_id");//工位ID
            station_code = jsonParas.getString("station_code");//工位号
            make_orders = jsonParas.getString("make_orders");//订单号
            //1.先删除临时表
            String delte01 = "delete from c_mes_me_cr_pdure " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            String delte02 = "delete from c_mes_me_cr_pdure_bom " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            String delte03 = "delete from c_mes_me_cr_pdure_quality " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte01, false, request, apiRoutePath);
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte02, false, request, apiRoutePath);
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte03, false, request, apiRoutePath);
            String[] orders = make_orders.split(",");
            List<String> flag = new ArrayList<String>();
            List<String> materialIds = new ArrayList<String>();
            for (int i = 0; i < orders.length; i++) {
                //2.查询工艺路线配方
                String sqlRecipe = "select c.recipe_id,a.small_model_type " +
                        "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " +
                        "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " +
                        "on b.recipe_id=c.recipe_id " +
                        "where a.prod_line_id=" + prod_line_id + " and a.make_order ='" + orders[i] + "' " +
                        "and c.recipe_type='PROCESS' LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlRecipe,
                        false, request, apiRoutePath);
                if (itemListRecipe == null || itemListRecipe.size() <= 0) {
                    errorMsg = "当前工位号{" + station_code + "},按照订单{" + orders[i] + "}未找到工艺路线,请检查是否修改了正在生产的订单或者删除了工艺路线";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                String recipe_id = itemListRecipe.get(0).get("recipe_id").toString();
                String small_model_type = itemListRecipe.get(0).get("small_model_type").toString();
                //3.根据工位号以及RECIPE_ID查找到工艺路线组
                String sqlCraftRoute = "select a.craft_route_id " +
                        "from c_mes_fmod_recipe_cr a inner join c_mes_fmod_recipe_cr_main b " +
                        "on a.craft_route_main_id=b.craft_route_main_id " +
                        "where a.enable_flag='Y' and b.enable_flag='Y' " +
                        "and a.station_id=" + station_id + " and b.recipe_id=" + recipe_id + " LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListCraftRoute = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlCraftRoute,
                        false, request, apiRoutePath);
                if (itemListCraftRoute != null && itemListCraftRoute.size() > 0) {
                    String craft_route_id = itemListCraftRoute.get(0).get("craft_route_id").toString();
                    //3.1 增加工序临时表
                    if (!flag.contains(craft_route_id)) {
                        String sqlInsertPdure = "insert into c_mes_me_cr_pdure " +
                                "(created_by,creation_date,proceduce_id,prod_line_code,station_code," +
                                "proceduce_code,proceduce_des,proceduce_type,proceduce_attr,work_order_by," +
                                "data_move_flag,allow_jump_flag,pic_name,pic_path,proceduce_status) " +
                                "select " +
                                "'" + station_code + "' as created_by," +
                                "'" + nowDateTime + "' as creation_date," +
                                "proceduce_id," +
                                "'" + prod_line_code + "' as prod_line_code," +
                                "'" + station_code + "' as station_code," +
                                "proceduce_code,proceduce_des,proceduce_type," +
                                "COALESCE(proceduce_attr,'') proceduce_attr," +
                                "work_order_by,data_move_flag,allow_jump_flag," +
                                "COALESCE(pic_name,'') pic_name," +
                                "COALESCE(pic_path,'') pic_path," +
                                "'PLAN' as proceduce_status " +
                                "from c_mes_fmod_recipe_cr_pdure " +
                                "where craft_route_id=" + craft_route_id + " and enable_flag='Y' " +
                                "order by work_order_by";
                        cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertPdure, false, request, apiRoutePath);
                        flag.add(craft_route_id);
                    }
                    //3.2 增加物料BOM临时表
                    String sqlPdureMSelect = "select b.proceduce_id " +
                            "from c_mes_fmod_recipe_cr_pdure b " +
                            "where b.proceduce_type='MATERIAL_SCAN' and b.craft_route_id=" + craft_route_id + " " +
                            "and b.enable_flag='Y' LIMIT 1 OFFSET 0";
                    List<Map<String, Object>> itemListMPdure = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPdureMSelect,
                            false, request, apiRoutePath);
                    if (itemListMPdure != null && itemListMPdure.size() > 0) {
                        String proceduce_id_m = itemListMPdure.get(0).get("proceduce_id").toString();
                        String selectBomSql = "select " +
                                "'" + station_code + "' as created_by," +
                                "'" + nowDateTime + "' as creation_date," +
                                "a.material_id," + proceduce_id_m + " as proceduce_id," +
                                "'" + prod_line_code + "' as prod_line_code," +
                                "'" + station_code + "' as station_code," +
                                "a.material_code,a.material_des," +
                                "COALESCE(a.draw_code,'') draw_code,a.usage," +
                                "COALESCE(a.material_uom,'') material_uom," +
                                "COALESCE(a.material_attr,'') material_attr," +
                                "COALESCE(a.material_cost,0) material_cost," +
                                "COALESCE(a.remarks,'') remarks," +
                                "COALESCE(a.max_count,0) max_count," +
                                "COALESCE(a.min_count,0) min_count," +
                                "COALESCE(a.rack_code,'') rack_code," +
                                "COALESCE(a.rack_max_count,0) rack_max_count," +
                                "COALESCE(a.rack_min_count,0) rack_min_count," +
                                "COALESCE(a.box_type,'') box_type," +
                                "COALESCE(a.box_set,0) box_set," +
                                "COALESCE(a.material_index,1) material_index," +
                                "COALESCE(a.material_gg,'') material_gg," +
                                "a.main_material_flag,a.verify_flag,a.batch_flag," +
                                "COALESCE(a.material_rule_exact,'') material_rule_exact," +
                                "COALESCE(a.material_rule_card,'') material_rule_card," +
                                "a.stock_flag,COALESCE(a.bom_version,'') bom_version," +
                                "(case when a.verify_flag='Y' then 'N' else 'Y' end) as verify_finish_flag," +
                                "(case when (a.batch_flag='Y' and (c.material_batch is null or c.material_batch='')) then 'N' else 'Y' end) as batch_finish_flag," +
                                "COALESCE(c.stock_count,0) stock_count," +
                                "COALESCE(c.material_batch,'') material_batch," +
                                "COALESCE(c.material_supplier,'') material_supplier," +
                                "COALESCE(c.supplier_des,'') supplier_des," +
                                "COALESCE(c.lable_barcode,'') lable_barcode," +
                                "'' as exact_barcode,a.fchong_flag " +
                                "from c_mes_fmod_small_model_bom a inner join c_mes_fmod_small_model b " +
                                "on a.small_type_id=b.small_type_id " +
                                "left join c_mes_me_station_stock c " +
                                "on a.material_code=c.material_code and c.prod_line_code='" + prod_line_code + "' and c.station_code='" + station_code + "' " +
                                "where b.small_model_type='" + small_model_type + "' and b.prod_line_id=" + prod_line_id + " " +
                                "and a.station_id=" + station_id + " " +
                                "and b.enable_flag='Y' and a.enable_flag='Y' " +
                                "order by a.material_id";
                        List<Map<String, Object>> maps = cFuncDbSqlExecute.ExecSelectSql(station_code, selectBomSql, false, request, apiRoutePath);
                        if (CollectionUtils.isEmpty(maps)) {
                            log.error("查询不到bom信息");
                            continue;
                        }
                        Map<String, Object> bom = maps.get(0);
                        String materialId = maps.stream().map(m -> m.get("material_id").toString()).findFirst().get();
                        if (materialIds.contains(materialId)) {
                            //update
                            String sqlUpdateBom = "update c_mes_me_cr_pdure_bom set   usage=usage+" + bom.get("usage") + " where material_id=" + materialId;
                            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdateBom, false, request, apiRoutePath);
                        } else {
                            String sqlInsertBom = "insert into c_mes_me_cr_pdure_bom " +
                                    "(created_by,creation_date,material_id,proceduce_id,prod_line_code,station_code," +
                                    "material_code,material_des,draw_code,usage,material_uom," +
                                    "material_attr,material_cost,remarks,max_count,min_count," +
                                    "rack_code,rack_max_count,rack_min_count,box_type,box_set," +
                                    "material_index,material_gg,main_material_flag,verify_flag," +
                                    "batch_flag,material_rule_exact,material_rule_card,stock_flag," +
                                    "bom_version,verify_finish_flag,batch_finish_flag," +
                                    "stock_count,material_batch,material_supplier,supplier_des," +
                                    "lable_barcode,exact_barcode,fchong_flag) " +
                                    "select " +
                                    "'" + station_code + "' as created_by," +
                                    "'" + nowDateTime + "' as creation_date," +
                                    "a.material_id," + proceduce_id_m + " as proceduce_id," +
                                    "'" + prod_line_code + "' as prod_line_code," +
                                    "'" + station_code + "' as station_code," +
                                    "a.material_code,a.material_des," +
                                    "COALESCE(a.draw_code,'') draw_code,a.usage," +
                                    "COALESCE(a.material_uom,'') material_uom," +
                                    "COALESCE(a.material_attr,'') material_attr," +
                                    "COALESCE(a.material_cost,0) material_cost," +
                                    "COALESCE(a.remarks,'') remarks," +
                                    "COALESCE(a.max_count,0) max_count," +
                                    "COALESCE(a.min_count,0) min_count," +
                                    "COALESCE(a.rack_code,'') rack_code," +
                                    "COALESCE(a.rack_max_count,0) rack_max_count," +
                                    "COALESCE(a.rack_min_count,0) rack_min_count," +
                                    "COALESCE(a.box_type,'') box_type," +
                                    "COALESCE(a.box_set,0) box_set," +
                                    "COALESCE(a.material_index,1) material_index," +
                                    "COALESCE(a.material_gg,'') material_gg," +
                                    "a.main_material_flag,a.verify_flag,a.batch_flag," +
                                    "COALESCE(a.material_rule_exact,'') material_rule_exact," +
                                    "COALESCE(a.material_rule_card,'') material_rule_card," +
                                    "a.stock_flag,COALESCE(a.bom_version,'') bom_version," +
                                    "(case when a.verify_flag='Y' then 'N' else 'Y' end) as verify_finish_flag," +
                                    "(case when (a.batch_flag='Y' and (c.material_batch is null or c.material_batch='')) then 'N' else 'Y' end) as batch_finish_flag," +
                                    "COALESCE(c.stock_count,0) stock_count," +
                                    "COALESCE(c.material_batch,'') material_batch," +
                                    "COALESCE(c.material_supplier,'') material_supplier," +
                                    "COALESCE(c.supplier_des,'') supplier_des," +
                                    "COALESCE(c.lable_barcode,'') lable_barcode," +
                                    "'' as exact_barcode,a.fchong_flag " +
                                    "from c_mes_fmod_small_model_bom a inner join c_mes_fmod_small_model b " +
                                    "on a.small_type_id=b.small_type_id " +
                                    "left join c_mes_me_station_stock c " +
                                    "on a.material_code=c.material_code and c.prod_line_code='" + prod_line_code + "' and c.station_code='" + station_code + "' " +
                                    "where b.small_model_type='" + small_model_type + "' and b.prod_line_id=" + prod_line_id + " " +
                                    "and a.station_id=" + station_id + " " +
                                    "and b.enable_flag='Y' and a.enable_flag='Y' " +
                                    "order by a.material_id";
                            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertBom, false, request, apiRoutePath);
                            materialIds.add(materialId);
                        }
                    }
                    //3.3 增加质量数据临时表
                    String sqlInsertQuality = "insert into c_mes_me_cr_pdure_quality " +
                            "(created_by,creation_date,quality_id,proceduce_id,prod_line_code,station_code," +
                            "quality_from,tag_id,tag_quality_sign_id,group_order,group_name,tag_col_order," +
                            "tag_col_inner_order,location_x,location_y,bolt_code,torque,progm_num," +
                            "control_type,control_width,control_height,quality_for,tag_des,tag_uom," +
                            "theory_value,down_limit,upper_limit,quality_status,tag_value,quality_save_flag) " +
                            "select " +
                            "'" + station_code + "' as created_by," +
                            "'" + nowDateTime + "' as creation_date," +
                            "a.quality_id,b.proceduce_id," +
                            "'" + prod_line_code + "' as prod_line_code," +
                            "'" + station_code + "' as station_code," +
                            "a.quality_from,COALESCE(a.tag_id,0) tag_id,COALESCE(a.tag_quality_sign_id,0) tag_quality_sign_id," +
                            "a.group_order,a.group_name," +
                            "COALESCE(a.tag_col_order,1) tag_col_order," +
                            "COALESCE(a.tag_col_inner_order,1) tag_col_inner_order," +
                            "a.location_x,a.location_y," +
                            "COALESCE(a.bolt_code,0) bolt_code," +
                            "COALESCE(a.torque,'') torque," +
                            "COALESCE(a.progm_num,0) progm_num," +
                            "a.control_type,a.control_width,a.control_height,a.quality_for,a.tag_des," +
                            "COALESCE(a.tag_uom,'') tag_uom," +
                            "COALESCE(a.theory_value,'') theory_value," +
                            "COALESCE(a.down_limit,'') down_limit," +
                            "COALESCE(a.upper_limit,'') upper_limit," +
                            "'' as quality_status,'' as tag_value,a.quality_save_flag " +
                            "from c_mes_fmod_recipe_cr_pdure_quality a inner join c_mes_fmod_recipe_cr_pdure b " +
                            "on a.proceduce_id=b.proceduce_id " +
                            "where (b.proceduce_type='AUTO_QUALITY' or b.proceduce_type='SHAFT_QUALITY') and b.craft_route_id=" + craft_route_id + " " +
                            "and b.enable_flag='Y' and a.enable_flag='Y' " +
                            "order by b.proceduce_id,a.group_order,a.tag_col_order,a.tag_col_inner_order";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertQuality, false, request, apiRoutePath);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_orders + "}创建工序作业表发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //创建作业工序临时表(包括工序、BOM、质量) 根据金寨、唐山国轩，程序号为0时不生成c_mes_me_cr_pdure_bom
    @Transactional
    @RequestMapping(value = "/MesCoreRecipePdureWorkCreate4", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkCreate4(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipePdureWorkCreate";
        String transResult = "";
        String errorMsg = "";
        String make_order = "";
        String station_code = "";
        try {
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("");
            String prod_line_id = jsonParas.getString("prod_line_id");//产线ID
            String prod_line_code = jsonParas.getString("prod_line_code");//产线CODE
            String station_id = jsonParas.getString("station_id");//工位ID
            station_code = jsonParas.getString("station_code");//工位号
            make_order = jsonParas.getString("make_order");//订单号
            //1.先删除临时表
            String delte01 = "delete from c_mes_me_cr_pdure " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            String delte02 = "delete from c_mes_me_cr_pdure_bom " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            String delte03 = "delete from c_mes_me_cr_pdure_quality " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte01, false, request, apiRoutePath);
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte02, false, request, apiRoutePath);
            cFuncDbSqlExecute.ExecUpdateSql(station_code, delte03, false, request, apiRoutePath);
            //2.查询工艺路线配方
            String sqlRecipe = "select c.recipe_id " +
                    "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " +
                    "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " +
                    "on b.recipe_id=c.recipe_id " +
                    "where a.prod_line_id=" + prod_line_id + " and a.make_order='" + make_order + "' " +
                    "and c.recipe_type='PROCESS' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlRecipe,
                    false, request, apiRoutePath);
            if (itemListRecipe == null || itemListRecipe.size() <= 0) {
                errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}未找到工艺路线,请检查是否修改了正在生产的订单或者删除了工艺路线";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String recipe_id = itemListRecipe.get(0).get("recipe_id").toString();
            //3.根据工位号以及RECIPE_ID查找到工艺路线组
            String sqlCraftRoute = "select a.craft_route_id " +
                    "from c_mes_fmod_recipe_cr a inner join c_mes_fmod_recipe_cr_main b " +
                    "on a.craft_route_main_id=b.craft_route_main_id " +
                    "where a.enable_flag='Y' and b.enable_flag='Y' " +
                    "and a.station_id=" + station_id + " and b.recipe_id=" + recipe_id + " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListCraftRoute = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlCraftRoute,
                    false, request, apiRoutePath);
            if (itemListCraftRoute != null && itemListCraftRoute.size() > 0) {
                String craft_route_id = itemListCraftRoute.get(0).get("craft_route_id").toString();
                //3.1 增加工序临时表
                String sqlInsertPdure = "insert into c_mes_me_cr_pdure " +
                        "(created_by,creation_date,proceduce_id,prod_line_code,station_code," +
                        "proceduce_code,proceduce_des,proceduce_type,proceduce_attr,work_order_by," +
                        "data_move_flag,allow_jump_flag,pic_name,pic_path,proceduce_status) " +
                        "select " +
                        "'" + station_code + "' as created_by," +
                        "'" + nowDateTime + "' as creation_date," +
                        "proceduce_id," +
                        "'" + prod_line_code + "' as prod_line_code," +
                        "'" + station_code + "' as station_code," +
                        "proceduce_code,proceduce_des,proceduce_type," +
                        "COALESCE(proceduce_attr,'') proceduce_attr," +
                        "work_order_by,data_move_flag,allow_jump_flag," +
                        "COALESCE(pic_name,'') pic_name," +
                        "COALESCE(pic_path,'') pic_path," +
                        "'PLAN' as proceduce_status " +
                        "from c_mes_fmod_recipe_cr_pdure " +
                        "where craft_route_id=" + craft_route_id + " and enable_flag='Y' " +
                        "order by work_order_by";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertPdure, false, request, apiRoutePath);
                //3.2 增加物料BOM临时表
                String sqlInsertBom = "insert into c_mes_me_cr_pdure_bom " +
                        "(created_by,creation_date,material_id,proceduce_id,prod_line_code,station_code," +
                        "material_code,material_des,draw_code,usage,material_uom," +
                        "material_attr,material_cost,remarks,max_count,min_count," +
                        "rack_code,rack_max_count,rack_min_count,box_type,box_set," +
                        "material_index,material_gg,main_material_flag,verify_flag," +
                        "batch_flag,material_rule_exact,material_rule_card,stock_flag," +
                        "bom_version,verify_finish_flag,batch_finish_flag," +
                        "stock_count,material_batch,material_supplier,supplier_des," +
                        "lable_barcode,exact_barcode,fchong_flag) " +
                        "select " +
                        "'" + station_code + "' as created_by," +
                        "'" + nowDateTime + "' as creation_date," +
                        "a.material_id,b.proceduce_id," +
                        "'" + prod_line_code + "' as prod_line_code," +
                        "'" + station_code + "' as station_code," +
                        "a.material_code,a.material_des," +
                        "COALESCE(a.draw_code,'') draw_code,a.usage," +
                        "COALESCE(a.material_uom,'') material_uom," +
                        "COALESCE(a.material_attr,'') material_attr," +
                        "COALESCE(a.material_cost,0) material_cost," +
                        "COALESCE(a.remarks,'') remarks," +
                        "COALESCE(a.max_count,0) max_count," +
                        "COALESCE(a.min_count,0) min_count," +
                        "COALESCE(a.rack_code,'') rack_code," +
                        "COALESCE(a.rack_max_count,0) rack_max_count," +
                        "COALESCE(a.rack_min_count,0) rack_min_count," +
                        "COALESCE(a.box_type,'') box_type," +
                        "COALESCE(a.box_set,0) box_set," +
                        "COALESCE(a.material_index,1) material_index," +
                        "COALESCE(a.material_gg,'') material_gg," +
                        "a.main_material_flag,a.verify_flag,a.batch_flag," +
                        "COALESCE(a.material_rule_exact,'') material_rule_exact," +
                        "COALESCE(a.material_rule_card,'') material_rule_card," +
                        "a.stock_flag,COALESCE(a.bom_version,'') bom_version," +
                        "(case when a.verify_flag='Y' then 'N' else 'Y' end) as verify_finish_flag," +
                        "(case when (a.batch_flag='Y' and (c.material_batch is null or c.material_batch='')) then 'N' else 'Y' end) as batch_finish_flag," +
                        "COALESCE(c.stock_count,0) stock_count," +
                        "COALESCE(c.material_batch,'') material_batch," +
                        "COALESCE(c.material_supplier,'') material_supplier," +
                        "COALESCE(c.supplier_des,'') supplier_des," +
                        "COALESCE(c.lable_barcode,'') lable_barcode," +
                        "'' as exact_barcode,a.fchong_flag " +
                        "from c_mes_fmod_recipe_cr_pdure_bom a inner join c_mes_fmod_recipe_cr_pdure b " +
                        "on a.proceduce_id=b.proceduce_id " +
                        "left join c_mes_me_station_stock c " +
                        "on a.material_code=c.material_code and c.prod_line_code='" + prod_line_code + "' and c.station_code='" + station_code + "' " +
                        "where b.proceduce_type='MATERIAL_SCAN' and b.craft_route_id=" + craft_route_id + " " +
                        "and b.enable_flag='Y' and a.enable_flag='Y' " +
                        "order by b.proceduce_id,a.material_id";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertBom, false, request, apiRoutePath);
                //3.3 增加质量数据临时表
                String sqlInsertQuality = "insert into c_mes_me_cr_pdure_quality " +
                        "(created_by,creation_date,quality_id,proceduce_id,prod_line_code,station_code," +
                        "quality_from,tag_id,tag_quality_sign_id,group_order,group_name,tag_col_order," +
                        "tag_col_inner_order,location_x,location_y,bolt_code,torque,progm_num," +
                        "control_type,control_width,control_height,quality_for,tag_des,tag_uom," +
                        "theory_value,down_limit,upper_limit,quality_status,tag_value,quality_save_flag) " +
                        "select " +
                        "'" + station_code + "' as created_by," +
                        "'" + nowDateTime + "' as creation_date," +
                        "a.quality_id,b.proceduce_id," +
                        "'" + prod_line_code + "' as prod_line_code," +
                        "'" + station_code + "' as station_code," +
                        "a.quality_from,COALESCE(a.tag_id,0) tag_id,COALESCE(a.tag_quality_sign_id,0) tag_quality_sign_id," +
                        "a.group_order,a.group_name," +
                        "COALESCE(a.tag_col_order,1) tag_col_order," +
                        "COALESCE(a.tag_col_inner_order,1) tag_col_inner_order," +
                        "a.location_x,a.location_y," +
                        "COALESCE(a.bolt_code,0) bolt_code," +
                        "COALESCE(a.torque,'') torque," +
                        "COALESCE(a.progm_num,0) progm_num," +
                        "a.control_type,a.control_width,a.control_height,a.quality_for,a.tag_des," +
                        "COALESCE(a.tag_uom,'') tag_uom," +
                        "COALESCE(a.theory_value,'') theory_value," +
                        "COALESCE(a.down_limit,'') down_limit," +
                        "COALESCE(a.upper_limit,'') upper_limit," +
                        "'WAIT' as quality_status,'' as tag_value,a.quality_save_flag " +
                        "from c_mes_fmod_recipe_cr_pdure_quality a inner join c_mes_fmod_recipe_cr_pdure b " +
                        "on a.proceduce_id=b.proceduce_id " +
                        "where (b.proceduce_type='AUTO_QUALITY' or b.proceduce_type='SHAFT_QUALITY') and b.craft_route_id=" + craft_route_id + " " +
                        "and b.enable_flag='Y' and a.enable_flag='Y'  AND COALESCE ( a.progm_num, 0 ) != 0" +
                        "order by b.proceduce_id,a.group_order,a.tag_col_order,a.tag_col_inner_order";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertQuality, false, request, apiRoutePath);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}创建工序作业表发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //查询当前工位作业工序
    @RequestMapping(value = "/MesCoreRecipePdureWorkSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipePdureWorkSelect";
        String selectResult = "";
        String errorMsg = "";
        String station_code = "";
        try {
            String prod_line_code = jsonParas.getString("prod_line_code");//产线CODE
            station_code = jsonParas.getString("station_code");//工位号
            String sqlPdure = "select proceduce_id,proceduce_code,proceduce_des,proceduce_type," +
                    "COALESCE(proceduce_attr,'') proceduce_attr " +
                    "from c_mes_me_cr_pdure " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "' " +
                    "and proceduce_status='PLAN' " +
                    "order by work_order_by LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListPdure = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPdure,
                    false, request, apiRoutePath);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListPdure, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "查询当前工位{" + station_code + "}作业工序异常:" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //查询当前工位作业工序
    @RequestMapping(value = "/MesCoreRecipePdureWorkSelect2", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkSelect2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipePdureWorkSelect2";
        String selectResult = "";
        String errorMsg = "";
        String station_code = "";
        try {
            String prod_line_code = jsonParas.getString("prod_line_code");//产线CODE
            station_code = jsonParas.getString("station_code");//工位号
            String sqlPdure = "select proceduce_id,proceduce_code,proceduce_des,proceduce_type," +
                    "COALESCE(proceduce_attr,'') proceduce_attr " +
                    "from c_mes_me_cr_pdure " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "' " +
                    "and proceduce_status='PLAN' " +
                    "order by work_order_by";
            List<Map<String, Object>> itemListPdure = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPdure,
                    false, request, apiRoutePath);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListPdure, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "查询当前工位{" + station_code + "}作业工序异常:" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    /**
     * 查询当前工位作业工序 並分組
     *
     * @param jsonParas
     * @param request
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/MesCoreRecipePdureWorkSelect3", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkSelect3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipePdureWorkSelect3";
        String selectResult = "";
        String errorMsg = "";
        String station_code = "";
        try {
            String prod_line_code = jsonParas.getString("prod_line_code");//产线CODE
            station_code = jsonParas.getString("station_code");//工位号
            String sqlPdure = "select proceduce_id,proceduce_code,proceduce_des,proceduce_type," +
                    "COALESCE(proceduce_attr,'') proceduce_attr " +
                    "from c_mes_me_cr_pdure " +
                    "where prod_line_code='" + prod_line_code + "' and station_code='" + station_code + "' " +
                    "and proceduce_status='PLAN' " +
                    "order by work_order_by";
            List<Map<String, Object>> itemListPdure = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPdure,
                    false, request, apiRoutePath);
            Map<Object, List<Map<String, Object>>> collect = itemListPdure.stream().collect(Collectors.groupingBy(g -> {
                if (!g.containsKey("proceduce_type") || StringUtils.isEmpty(g.get("proceduce_type"))) {
                    return "";
                }
                return g.get("proceduce_type");
            }));
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, JSONObject.toJSONString(collect), "", 0);
        } catch (Exception ex) {
            errorMsg = "查询当前工位{" + station_code + "}作业工序异常:" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更改当前工位作业工序状态
    @Transactional
    @RequestMapping(value = "/MesCoreRecipePdureWorkUpdate", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkUpdate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipePdureWorkUpdate";
        String transResult = "";
        String errorMsg = "";
        String station_code = "";
        String proceduce_id = "";
        String proceduce_code = "";
        String proceduce_status = "";
        String proceduce_type = "";
        String station_flow_id_list = "";
        try {
            station_code = jsonParas.getString("station_code");
            proceduce_id = jsonParas.getString("proceduce_id");
            proceduce_code = jsonParas.getString("proceduce_code");
            proceduce_status = jsonParas.getString("proceduce_status");
            proceduce_type = jsonParas.getString("proceduce_type");
            station_flow_id_list = jsonParas.getString("station_flow_id_list");
            String serial_num = jsonParas.getString("serial_num");
            String prod_line_code = jsonParas.getString("prod_line_code");
            //判断当前工序是否为物料扫描工序,若是物料扫描工序则需要存储物料数据
            if (proceduce_status.equals("FINISH") && proceduce_type.equals("MATERIAL_SCAN")) {
                if (station_flow_id_list != null && !station_flow_id_list.equals("")) {
                    String[] lstStationFlowIds = station_flow_id_list.split(",", -1);
                    if (lstStationFlowIds != null && lstStationFlowIds.length > 0) {
                        String sqlMaterialScan = "select " +
                                "material_code,material_des,usage," +
                                "COALESCE(material_uom,'') material_uom,COALESCE(material_attr,'') material_attr," +
                                "COALESCE(material_batch,'') material_batch,COALESCE(material_supplier,'') material_supplier," +
                                "COALESCE(supplier_des,'') supplier_des," +
                                "COALESCE(lable_barcode,'') lable_barcode,COALESCE(exact_barcode,'') exact_barcode," +
                                "main_material_flag,verify_flag,batch_flag," +
                                "COALESCE(bom_version,'') bom_version " +
                                "from c_mes_me_cr_pdure_bom " +
                                "where proceduce_id=" + proceduce_id + " and station_code='" + station_code + "' " +
                                "order by material_id";
                        List<Map<String, Object>> itemListBom = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMaterialScan,
                                false, request, apiRoutePath);
                        List<Map<String, Object>> lstMDocuments = new ArrayList<>();
                        if (itemListBom != null && itemListBom.size() > 0) {
                            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                            String trace_d_time = CFuncUtilsSystem.GetNowDateTime("");
                            for (Map<String, Object> map : itemListBom) {
                                for (int i = 0; i < lstStationFlowIds.length; i++) {
                                    Map<String, Object> mapMDataItem = new HashMap<>();
                                    String station_flow_id = lstStationFlowIds[i];
                                    String material_trace_id = CFuncUtilsSystem.CreateUUID(true);
                                    mapMDataItem.put("item_date", item_date);
                                    mapMDataItem.put("item_date_val", item_date_val);
                                    mapMDataItem.put("material_trace_id", material_trace_id);
                                    mapMDataItem.put("station_flow_id", station_flow_id);
                                    mapMDataItem.put("prod_line_code", prod_line_code);
                                    mapMDataItem.put("station_code", station_code);
                                    mapMDataItem.put("serial_num", serial_num);
                                    mapMDataItem.put("material_code", map.get("material_code").toString());
                                    mapMDataItem.put("material_des", map.get("material_des").toString());
                                    mapMDataItem.put("use_count", Integer.parseInt(map.get("usage").toString()));
                                    mapMDataItem.put("material_uom", map.get("material_uom").toString());
                                    mapMDataItem.put("material_attr", map.get("material_attr").toString());
                                    mapMDataItem.put("material_batch", map.get("material_batch").toString());
                                    mapMDataItem.put("material_supplier", map.get("material_supplier").toString());
                                    mapMDataItem.put("supplier_des", map.get("supplier_des").toString());
                                    mapMDataItem.put("lable_barcode", map.get("lable_barcode").toString());
                                    mapMDataItem.put("exact_barcode", map.get("exact_barcode").toString());
                                    mapMDataItem.put("main_material_flag", map.get("main_material_flag").toString());
                                    mapMDataItem.put("verify_flag", map.get("verify_flag").toString());
                                    mapMDataItem.put("batch_flag", map.get("batch_flag").toString());
                                    mapMDataItem.put("bom_version", map.get("bom_version").toString());
                                    mapMDataItem.put("trace_d_time", trace_d_time);
                                    lstMDocuments.add(mapMDataItem);
                                }
                            }
                            if (lstMDocuments.size() > 0) {
                                mongoTemplate.insert(lstMDocuments, "c_mes_me_station_material");
                            }
                        }
                    }
                }
            }

            String sqlUpdate = "update c_mes_me_cr_pdure set " +
                    "last_updated_by='" + station_code + "'," +
                    "last_update_date='" + CFuncUtilsSystem.GetNowDateTime("") + "'," +
                    "proceduce_status='" + proceduce_status + "' " +
                    "where proceduce_id=" + proceduce_id;
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdate, false, request, apiRoutePath);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "更改当前工位{" + station_code + "},工序{" + proceduce_code + "},状态为{" + proceduce_status + "}异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更改当前工位作业工序状态
    @Transactional
    @RequestMapping(value = "/MesCoreRecipePdureWorkUpdate2", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkUpdate2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipePdureWorkUpdate2";
        String transResult = "";
        String errorMsg = "";
        String station_code = "";
        String proceduce_id = "";
        String proceduce_code = "";
        String proceduce_status = "";
        String proceduce_type = "";
        String station_flow_id_list = "";
        try {
            station_code = jsonParas.getString("station_code");
            proceduce_id = jsonParas.getString("proceduce_id");
            proceduce_code = jsonParas.getString("proceduce_code");
            proceduce_status = jsonParas.getString("proceduce_status");
            proceduce_type = jsonParas.getString("proceduce_type");
            station_flow_id_list = jsonParas.getString("station_flow_id_list");
            String[] serialNums = jsonParas.getString("serial_num").split(",");
            String prod_line_code = jsonParas.getString("prod_line_code");
            //判断当前工序是否为物料扫描工序,若是物料扫描工序则需要存储物料数据
            if (proceduce_status.equals("FINISH") && proceduce_type.equals("MATERIAL_SCAN")) {
                String[] lstStationFlowIds = station_flow_id_list.split(",", -1);
                //查询online
                Query query = new Query();
                query.addCriteria(Criteria.where("serial_num").in(serialNums));
                List<Document> documents = mongoTemplate.find(query, Document.class, "c_mes_me_online_flow");
                Map<String, List<Document>> groupBySerialNum = documents.stream().collect(Collectors.groupingBy(g -> g.getString("serial_num")));
                List<String> smallModelTypes = documents.stream().map(m -> "'" + m.getString("small_model_type") + "'").distinct().collect(Collectors.toList());
                String smallModelType = smallModelTypes.toString().replace("[", " ").replace("]", " ");
                String sqlMaterial = "SELECT smallModel.small_model_type,pbom.material_code, pbom.material_des, pbom.usage, COALESCE ( pbom.material_uom, '' ) material_uom, COALESCE ( pbom.material_attr, '' ) material_attr,COALESCE ( pbom.material_batch, '' ) material_batch,COALESCE ( pbom.material_supplier, '' ) material_supplier,COALESCE ( pbom.supplier_des, '' ) supplier_des,COALESCE ( pbom.lable_barcode, '' ) lable_barcode,COALESCE ( pbom.exact_barcode, '' ) exact_barcode, pbom.main_material_flag, pbom.verify_flag, pbom.batch_flag,COALESCE ( pbom.bom_version, '' ) bom_version, pbom.material_id FROM c_mes_me_cr_pdure_bom as pbom left join c_mes_fmod_small_model_bom mbom on pbom.material_id=mbom.material_id left join c_mes_fmod_small_model smallModel on mbom.small_type_id=smallModel.small_type_id WHERE smallModel.small_model_type IN (" + smallModelType + ") ORDER BY pbom.material_id";
                List<Map<String, Object>> stationBoms = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMaterial,
                        false, request, apiRoutePath);
                Map<Object, List<Map<String, Object>>> stationBomGroupBySmallModelType = stationBoms.stream().collect(Collectors.groupingBy(g -> g.get("small_model_type")));
                List<Map<String, Object>> lstMDocuments = new ArrayList<>();
                log.debug("工位bom查询信息：{}", stationBoms);
                if (CollectionUtils.isEmpty(stationBoms)) {
                    return CFuncUtilsLayUiResut.GetErrorJson("工位bom信息为空");
                }
                Set<Object> keys = stationBomGroupBySmallModelType.keySet();
                Map<String, Map<String, Object>> stationBomMap = new HashMap<>();
                for (Object key : keys) {
                    Map<String, Object> stringObjectMap = stationBomGroupBySmallModelType.get(key).get(0);
                    stringObjectMap.put("exact_barcode_index_flag", 0);
                    stationBomMap.put(key.toString(), stringObjectMap);
                }
                for (int i = 0; i < serialNums.length; i++) {
                    Document document = groupBySerialNum.get(serialNums[i]).get(0);
                    Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                    long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                    String trace_d_time = CFuncUtilsSystem.GetNowDateTime("");
                    Map<String, Object> stationBom = stationBomMap.get(document.getString("small_model_type"));
                    Integer exact_barcode_index_flag = (Integer) stationBom.get("exact_barcode_index_flag");
                    String exact_barcode = stationBom.get("exact_barcode").toString().split(",")[exact_barcode_index_flag];
                    Map<String, Object> mapMDataItem = new HashMap<>();
                    String station_flow_id = lstStationFlowIds[i];
                    String material_trace_id = CFuncUtilsSystem.CreateUUID(true);
                    mapMDataItem.put("item_date", item_date);
                    mapMDataItem.put("item_date_val", item_date_val);
                    mapMDataItem.put("material_trace_id", material_trace_id);
                    mapMDataItem.put("station_flow_id", station_flow_id);
                    mapMDataItem.put("prod_line_code", prod_line_code);
                    mapMDataItem.put("station_code", station_code);
                    mapMDataItem.put("serial_num", document.getString("serial_num"));
                    mapMDataItem.put("material_code", stationBom.get("material_code").toString());
                    mapMDataItem.put("material_des", stationBom.get("material_des").toString());
                    mapMDataItem.put("use_count", Integer.parseInt(stationBom.get("usage").toString()));
                    mapMDataItem.put("material_uom", stationBom.get("material_uom").toString());
                    mapMDataItem.put("material_attr", stationBom.get("material_attr").toString());
                    mapMDataItem.put("material_batch", stationBom.get("material_batch").toString());
                    mapMDataItem.put("material_supplier", stationBom.get("material_supplier").toString());
                    mapMDataItem.put("supplier_des", stationBom.get("supplier_des").toString());
                    mapMDataItem.put("lable_barcode", stationBom.get("lable_barcode").toString());
                    mapMDataItem.put("exact_barcode", exact_barcode);
                    mapMDataItem.put("main_material_flag", stationBom.get("main_material_flag").toString());
                    mapMDataItem.put("verify_flag", stationBom.get("verify_flag").toString());
                    mapMDataItem.put("batch_flag", stationBom.get("batch_flag").toString());
                    mapMDataItem.put("bom_version", stationBom.get("bom_version").toString());
                    mapMDataItem.put("trace_d_time", trace_d_time);
                    stationBom.put("exact_barcode_index_flag", ++exact_barcode_index_flag);
                    stationBomMap.put(document.getString("small_model_type"), stationBom);
                    lstMDocuments.add(mapMDataItem);
                }
                if (lstMDocuments.size() > 0) {
                    mongoTemplate.insert(lstMDocuments, "c_mes_me_station_material");
                }
            }
            String sqlUpdate = "update c_mes_me_cr_pdure set " +
                    "last_updated_by='" + station_code + "'," +
                    "last_update_date='" + CFuncUtilsSystem.GetNowDateTime("") + "'," +
                    "proceduce_status='" + proceduce_status + "' " +
                    "where proceduce_id=" + proceduce_id;
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdate, false, request, apiRoutePath);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "更改当前工位{" + station_code + "},工序{" + proceduce_code + "},状态为{" + proceduce_status + "}异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更改当前工位作业工序状态
    @Transactional
    @RequestMapping(value = "/MesCoreRecipePdureWorkUpdate3", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipePdureWorkUpdate3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipePdureWorkUpdate3";
        String transResult = "";
        String errorMsg = "";
        String station_code = "";
        String proceduce_id = "";
        String proceduce_code = "";
        String proceduce_status = "";
        String proceduce_type = "";
        String station_flow_id_list = "";
        try {
            station_code = jsonParas.getString("station_code");
            proceduce_id = jsonParas.getString("proceduce_id");
            proceduce_code = jsonParas.getString("proceduce_code");
            proceduce_status = jsonParas.getString("proceduce_status");
            proceduce_type = jsonParas.getString("proceduce_type");
            station_flow_id_list = jsonParas.getString("station_flow_id_list");
            String[] serialNums = jsonParas.getString("serial_num").split(",");
            String prod_line_code = jsonParas.getString("prod_line_code");
            //判断当前工序是否为物料扫描工序,若是物料扫描工序则需要存储物料数据
            if (proceduce_status.equals("FINISH") && proceduce_type.equals("MATERIAL_SCAN")) {
                String[] lstStationFlowIds = station_flow_id_list.split(",", -1);
                //查询online
                Query query = new Query();
                query.addCriteria(Criteria.where("serial_num").in(serialNums));
                List<Document> documents = mongoTemplate.find(query, Document.class, "c_mes_me_online_flow");
                Map<String, List<Document>> groupBySerialNum = documents.stream().collect(Collectors.groupingBy(g -> g.getString("serial_num")));
                List<String> smallModelTypes = documents.stream().map(m -> "'" + m.getString("small_model_type") + "'").distinct().collect(Collectors.toList());
                String smallModelType = smallModelTypes.toString().replace("[", " ").replace("]", " ");
                String sqlMaterial = "SELECT smallModel.small_model_type,pbom.material_code, pbom.material_des, pbom.usage, COALESCE ( pbom.material_uom, '' ) material_uom, COALESCE ( pbom.material_attr, '' ) material_attr,COALESCE ( pbom.material_batch, '' ) material_batch,COALESCE ( pbom.material_supplier, '' ) material_supplier,COALESCE ( pbom.supplier_des, '' ) supplier_des,COALESCE ( pbom.lable_barcode, '' ) lable_barcode,COALESCE ( pbom.exact_barcode, '' ) exact_barcode, pbom.main_material_flag, pbom.verify_flag, pbom.batch_flag,COALESCE ( pbom.bom_version, '' ) bom_version, pbom.material_id FROM c_mes_me_cr_pdure_bom as pbom left join c_mes_fmod_small_model_bom mbom on pbom.material_id=mbom.material_id left join c_mes_fmod_small_model smallModel on mbom.small_type_id=smallModel.small_type_id WHERE smallModel.small_model_type IN (" + smallModelType + ") ORDER BY pbom.material_id";
                List<Map<String, Object>> stationBoms = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMaterial,
                        false, request, apiRoutePath);
                Map<Object, List<Map<String, Object>>> stationBomGroupBySmallModelType = stationBoms.stream().collect(Collectors.groupingBy(g -> g.get("small_model_type")));
                List<Map<String, Object>> lstMDocuments = new ArrayList<>();
                if (CollectionUtils.isEmpty(stationBoms)) {
                    return CFuncUtilsLayUiResut.GetErrorJson("工位bom信息为空");
                }
                Set<Object> keys = stationBomGroupBySmallModelType.keySet();
                Map<String, Map<String, Object>> stationBomMap = new HashMap<>();
                for (Object key : keys) {
                    Map<String, Object> stringObjectMap = stationBomGroupBySmallModelType.get(key).get(0);
                    stringObjectMap.put("exact_barcode_index_flag", 0);
                    stationBomMap.put(key.toString(), stringObjectMap);
                }
                for (int i = 0; i < serialNums.length; i++) {
                    Document document = groupBySerialNum.get(serialNums[i]).get(0);
                    Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                    long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                    String trace_d_time = CFuncUtilsSystem.GetNowDateTime("");
                    Map<String, Object> stationBom = stationBomMap.get(document.getString("small_model_type"));
                    Integer exact_barcode_index_flag = (Integer) stationBom.get("exact_barcode_index_flag");
                    String exact_barcode = stationBom.get("exact_barcode").toString().split(",")[exact_barcode_index_flag];
                    Map<String, Object> mapMDataItem = new HashMap<>();
                    String station_flow_id = lstStationFlowIds[i];
                    String material_trace_id = CFuncUtilsSystem.CreateUUID(true);
                    mapMDataItem.put("item_date", item_date);
                    mapMDataItem.put("item_date_val", item_date_val);
                    mapMDataItem.put("material_trace_id", material_trace_id);
                    mapMDataItem.put("station_flow_id", station_flow_id);
                    mapMDataItem.put("prod_line_code", prod_line_code);
                    mapMDataItem.put("station_code", station_code);
                    mapMDataItem.put("serial_num", document.getString("serial_num"));
                    mapMDataItem.put("material_code", stationBom.get("material_code").toString());
                    mapMDataItem.put("material_des", stationBom.get("material_des").toString());
                    mapMDataItem.put("use_count", Integer.parseInt(stationBom.get("usage").toString()));
                    mapMDataItem.put("material_uom", stationBom.get("material_uom").toString());
                    mapMDataItem.put("material_attr", stationBom.get("material_attr").toString());
                    mapMDataItem.put("material_batch", stationBom.get("material_batch").toString());
                    mapMDataItem.put("material_supplier", stationBom.get("material_supplier").toString());
                    mapMDataItem.put("supplier_des", stationBom.get("supplier_des").toString());
                    mapMDataItem.put("lable_barcode", stationBom.get("lable_barcode").toString());
                    mapMDataItem.put("exact_barcode", exact_barcode);
                    mapMDataItem.put("main_material_flag", stationBom.get("main_material_flag").toString());
                    mapMDataItem.put("verify_flag", stationBom.get("verify_flag").toString());
                    mapMDataItem.put("batch_flag", stationBom.get("batch_flag").toString());
                    mapMDataItem.put("bom_version", stationBom.get("bom_version").toString());
                    mapMDataItem.put("trace_d_time", trace_d_time);
                    stationBom.put("exact_barcode_index_flag", ++exact_barcode_index_flag);
                    stationBomMap.put(document.getString("small_model_type"), stationBom);
                    lstMDocuments.add(mapMDataItem);
                }
                if (lstMDocuments.size() > 0) {
                    mongoTemplate.insert(lstMDocuments, "c_mes_me_station_material");
                }
            }
            String sqlUpdate = "update c_mes_me_cr_pdure set " +
                    "last_updated_by='" + station_code + "'," +
                    "last_update_date='" + CFuncUtilsSystem.GetNowDateTime("") + "'," +
                    "proceduce_status='" + proceduce_status + "' " +
                    "where proceduce_id in(" + proceduce_id + ")";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdate, false, request, apiRoutePath);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "更改当前工位{" + station_code + "},工序{" + proceduce_code + "},状态为{" + proceduce_status + "}异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
