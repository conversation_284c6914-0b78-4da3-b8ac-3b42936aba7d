package com.api.pmc.core.flow;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 线首基础类
 * 1.线首保存
 * </p>
 *
 * <AUTHOR>
 * @since 2021-4-7
 */
@Service
@Slf4j
public class PmcCoreOnlineBase {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.线首保存
    public void OnlineIntefTask(HttpServletRequest request,String apiRoutePath,
                             String work_center_code, String prod_line_code,String station_code,
                             String serial_num, String pallet_num,
                             String make_order, String dms, String item_project, String vin, String small_model_type,
                             String main_material_code, String material_color, String material_size, String shaft_proc_num,
                             String engine_num, String driver_way, String publish_number,String online_flag, String dx_flag,
                             String white_car_adjust,String right_front_door,String left_front_door,String vpp_s,
                             String bbl_s,String bbr_s,String bbf_s,String bok_s,String y26,String bed_s,String bor_s) throws Exception {
        try{
            //默认值
            String nowDateTime= CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //工位状态：1正常 才认为是正常首件上线
            //1、新增线首
            long flowOnlineId=cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_flow_online_id_seq",true);
            String mo_work_order=String.valueOf(flowOnlineId);//生产订单顺序,针对订单进行排序
            String sqlOnlineIns="insert into d_pmc_me_flow_online " +
                    "(created_by,creation_date,flow_online_id," +
                    "work_center_code,prod_line_code,station_code,make_order," +
                    "serial_num,dms,item_project,vin,small_model_type," +
                    "main_material_code,material_color,material_size,shaft_proc_num," +
                    "staff_id,pallet_num,engine_num,driver_way,publish_number," +
                    "repair_flag,enable_flag," +
                    "white_car_adjust,right_front_door,left_front_door,vpp_s," +
                    "bbl_s,bbr_s,bbf_s,bok_s,y26,bed_s,bor_s) values " +
                    "('admin','"+nowDateTime+"',"+flowOnlineId+",'"+
                    work_center_code+"','"+prod_line_code+"','"+station_code+"','"+make_order+"','"+
                    serial_num+"','"+dms+"','"+item_project+"','"+vin+"','"+small_model_type+"','"+
                    main_material_code+"','"+material_color+"','"+material_size+"','"+shaft_proc_num+"',"+
                    "'AIS','"+pallet_num+"','"+engine_num+"','"+driver_way+"','"+publish_number+"','N','Y','" +
                    white_car_adjust+"','"+right_front_door+"','"+left_front_door+"','"+vpp_s+"','"+
                    bbl_s+"','"+bbr_s+"','"+bbf_s+"','"+bok_s+"','"+y26+"','"+bed_s+"','"+bor_s+"')";
            cFuncDbSqlExecute.ExecUpdateSql(station_code,sqlOnlineIns,false,request,apiRoutePath);
            //2、生成工位生产订单
            if(online_flag.equals("Y") &&
                    dx_flag.equals("Y")){//上线、定序工位
                String sqlDxStation="select COALESCE(station_code,'N') station_code " +
                        "from sys_fmod_station a," +
                        "     sys_fmod_prod_line b " +
                        "where a.prod_line_id=b.prod_line_id " +
                        "and a.enable_flag='Y' " +
                        "and b.enable_flag='Y' " +
                        "and a.station_attr='Y' " +
                        "and a.station_code<>'"+station_code+"' "+
                        "and b.work_center_code='"+work_center_code+"' " +
                        "order by a.prod_line_id,a.station_order ";
                List<Map<String,Object>> itemListDxStation=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlDxStation,false,null,apiRoutePath);
                if(itemListDxStation!=null && itemListDxStation.size()>0){
                    for(Map<String, Object> mapDxStation : itemListDxStation){
                        long stationMoId=cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_station_mo_id_seq",true);
                        String stationCodeDx=mapDxStation.get("station_code").toString();
                        String sqlDxStationIns="insert into d_pmc_me_station_mo " +
                                "(created_by,creation_date,station_mo_id," +
                                "prod_line_code,station_code,make_order," +
                                "dms,item_project,mo_work_order," +
                                "work_status,set_sign) values " +
                                "('admin','"+nowDateTime+"',"+stationMoId+",'"+
                                prod_line_code+"','"+stationCodeDx+"','"+make_order+"','"+
                                dms+"','"+item_project+"',"+mo_work_order+","+
                                "'PLAN','NONE')";
                        cFuncDbSqlExecute.ExecUpdateSql(station_code,sqlDxStationIns,false,request,apiRoutePath);
                    }
                }
            }
        }
        catch (Exception ex){
            throw new Exception("线首保存失败:"+ex.getMessage());
        }
    }

}
