package com.api.config;

import com.alicp.jetcache.Cache;
import com.alicp.jetcache.embedded.CaffeineCacheBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
public class JetCacheConfig {
    @Bean(name = "jetCache")
    public Cache jetCache() {
        return CaffeineCacheBuilder.createCaffeineCacheBuilder().limit(1000) // 缓存最大条数
                .expireAfterWrite(1, TimeUnit.DAYS) // 写入后10分钟过期
                .build();
    }
}
