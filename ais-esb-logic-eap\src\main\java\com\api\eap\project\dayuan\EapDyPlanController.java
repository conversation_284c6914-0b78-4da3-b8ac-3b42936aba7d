package com.api.eap.project.dayuan;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlMapper;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 工单信息查询
 * 1.工单查询
 * 2.工单生产过程工艺参数查询
 * <AUTHOR>
 * @date 2023-08-2 16:49
 */
@RestController
@RequestMapping("/eap/project/dayuan/plan")
public class EapDyPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;

    //保存任务信息到数据库
    @RequestMapping(value = "/EapDySharePlanSave", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDySharePlanSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dayuan/plan/EapDySharePlanSave";
        String transResult="";
        String errorMsg="";
        String meUserTable="a_eap_me_station_user";
        String apsPlanTable="a_eap_aps_plan";
        String apsPlanBTable="a_eap_aps_plan_d";
        try{
            String station_code=jsonParas.getString("station_code");
            String sqlStation="select station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station where station_code='"+station_code+"'";
            List<Map<String, Object>> lstStation=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlStation,false,request,apiRoutePath);
            if(lstStation==null || lstStation.size()<=0){
                errorMsg="未能根据工位号{"+station_code+"}查找到工位配置信息";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id=lstStation.get(0).get("station_id").toString();
            String station_attr=lstStation.get(0).get("station_attr").toString();
            JSONArray plan_list=jsonParas.getJSONArray("plan_list");
            if(plan_list==null || plan_list.size()<=0){
                errorMsg="保存任务信息不能为空集合";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String final_lot_group_status="PLAN";

            //查询当前登入者
            String user_name="";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                user_name=docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstPlanDocuments=new ArrayList<>();
            List<Map<String, Object>> lstPlanBDocuments=new ArrayList<>();
            String group_id=CFuncUtilsSystem.CreateUUID(true);
            for(int i=0;i<plan_list.size();i++){
                JSONObject jbItem=plan_list.getJSONObject(i);
                Map<String, Object> mapBigDataRow=new HashMap<>();
                String plan_id=CFuncUtilsSystem.CreateUUID(true);
                String port_code=jbItem.getString("port_code")==null ? "":jbItem.getString("port_code");
                String group_lot_num=jbItem.getString("group_lot_num");

                //判断是否存在,若存在PLAN和WORK状态的则不执行存储
                String[] group_lot_status=new String[]{"PLAN","WORK"};
                Query queryCount=new Query();
                queryCount.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryCount.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                long groupLotCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryCount.getQueryObject());
                if(groupLotCount>0) continue;

                //存储
                mapBigDataRow.put("item_date",item_date);
                mapBigDataRow.put("item_date_val",item_date_val);
                mapBigDataRow.put("plan_id",plan_id);
                mapBigDataRow.put("station_id",Long.parseLong(station_id));
                mapBigDataRow.put("task_from",jbItem.getString("task_from")==null ? "EAP":jbItem.getString("task_from"));
                mapBigDataRow.put("group_lot_num",jbItem.getString("group_lot_num")==null ? "":jbItem.getString("group_lot_num"));
                mapBigDataRow.put("lot_num",jbItem.getString("lot_num")==null ? "":jbItem.getString("lot_num"));
                mapBigDataRow.put("lot_short_num",jbItem.getString("lot_short_num")==null ? "":jbItem.getString("lot_short_num"));
                mapBigDataRow.put("lot_index",jbItem.getInteger("lot_index")==null ? 1:jbItem.getInteger("lot_index"));
                mapBigDataRow.put("plan_lot_count",jbItem.getInteger("plan_lot_count")==null ? 0:jbItem.getInteger("plan_lot_count"));
                mapBigDataRow.put("target_lot_count",jbItem.getInteger("target_lot_count")==null ? 0:jbItem.getInteger("target_lot_count"));
                mapBigDataRow.put("port_code",port_code);
                mapBigDataRow.put("material_code",jbItem.getString("material_code")==null ? "":jbItem.getString("material_code"));
                mapBigDataRow.put("pallet_num",jbItem.getString("pallet_num")==null ? "":jbItem.getString("pallet_num"));
                mapBigDataRow.put("pallet_type",jbItem.getString("pallet_type")==null ? "":jbItem.getString("pallet_type"));
                mapBigDataRow.put("lot_level",jbItem.getString("lot_level")==null ? "":jbItem.getString("lot_level"));
                mapBigDataRow.put("panel_length",jbItem.getDouble("panel_length")==null ? 0d:jbItem.getDouble("panel_length"));
                mapBigDataRow.put("panel_width",jbItem.getDouble("panel_width")==null ? 0d:jbItem.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness",jbItem.getDouble("panel_tickness")==null ? 0d:jbItem.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_model",jbItem.getInteger("panel_model")==null ? -1:jbItem.getInteger("panel_model"));
                mapBigDataRow.put("inspect_count",jbItem.getInteger("inspect_count")==null ? 0:jbItem.getInteger("inspect_count"));
                mapBigDataRow.put("inspect_finish_count",0);
                mapBigDataRow.put("pdb_count",jbItem.getInteger("pdb_count")==null ? 0:jbItem.getInteger("pdb_count"));
                mapBigDataRow.put("pdb_rule",jbItem.getInteger("pdb_rule")==null ? 0:jbItem.getInteger("pdb_rule"));
                mapBigDataRow.put("fp_count",jbItem.getInteger("fp_count")==null ? 0:jbItem.getInteger("fp_count"));
                mapBigDataRow.put("group_lot_status",final_lot_group_status);
                mapBigDataRow.put("lot_status","PLAN");
                mapBigDataRow.put("finish_count",0);
                mapBigDataRow.put("finish_ok_count",0);
                mapBigDataRow.put("finish_ng_count",0);
                mapBigDataRow.put("task_error_code",0);
                mapBigDataRow.put("item_info",jbItem.getString("item_info")==null ? "":jbItem.getString("item_info"));
                mapBigDataRow.put("task_start_time","");
                mapBigDataRow.put("task_end_time","");
                mapBigDataRow.put("task_cost_time",(long)0);
                mapBigDataRow.put("attribute1",jbItem.getString("attribute1")==null ? "":jbItem.getString("attribute1"));
                mapBigDataRow.put("attribute2",jbItem.getString("attribute2")==null ? "":jbItem.getString("attribute2"));
                mapBigDataRow.put("attribute3",jbItem.getString("attribute3")==null ? "":jbItem.getString("attribute3"));
                mapBigDataRow.put("face_code",jbItem.getInteger("face_code")==null ? 0:jbItem.getInteger("face_code"));
                mapBigDataRow.put("pallet_use_count",jbItem.getInteger("pallet_use_count")==null ? 0:jbItem.getInteger("pallet_use_count"));
                mapBigDataRow.put("user_name",user_name);
                mapBigDataRow.put("group_id",group_id);
                mapBigDataRow.put("offline_flag","N");
                mapBigDataRow.put("target_update_count",0);//当作上线数量

                lstPlanDocuments.add(mapBigDataRow);
                String panel_list=jbItem.getString("panel_list")==null ? "":jbItem.getString("panel_list");
                String[] panelList=panel_list.split(",",-1);
                if(!panel_list.equals("")){
                    if(panelList!=null && panelList.length>0){
                        for(int j=0;j<panelList.length;j++){
                            String panel_barcode=panelList[j];
                            String plan_d_id=CFuncUtilsSystem.CreateUUID(true);
                            Map<String, Object> mapBigDataRowB=new HashMap<>();
                            mapBigDataRowB.put("item_date",item_date);
                            mapBigDataRowB.put("item_date_val",item_date_val);
                            mapBigDataRowB.put("plan_d_id",plan_d_id);
                            mapBigDataRowB.put("plan_id",plan_id);
                            mapBigDataRowB.put("panel_barcode",panel_barcode);
                            lstPlanBDocuments.add(mapBigDataRowB);
                        }
                    }
                }
            }
            if(lstPlanBDocuments.size()>0) mongoTemplate.insert(lstPlanBDocuments,apsPlanBTable);
            mongoTemplate.insert(lstPlanDocuments,apsPlanTable);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "保存任务信息到数据库异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //查询计划中或者进行中的任务
    @RequestMapping(value = "/EapDyPlanWorkSelect", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyPlanWorkSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dayuan/plan/EapDyPlanWorkSelect";
        String selectResult="";
        String errorMsg="";
        String apsPlanTable="a_eap_aps_plan";
        try{
            String station_id=jsonParas.getString("station_id");
            long station_id_long=Long.parseLong(station_id);
            String group_id="";
            List<Map<String, Object>> itemList=new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));//PLAN
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                group_id=docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            else{
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if(iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    group_id=docItemBigData.getString("group_id");
                    iteratorBigData.close();
                }
            }
            if(!group_id.equals("")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while(iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow=new HashMap<>();
                    mapBigDataRow.put("task_from",docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num",docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num",docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num",docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index",docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count",docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count",docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code",docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num",docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type",docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level",docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length",docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width",docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness",docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model",docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count",docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count",docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule",docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count",docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info",docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1",docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2",docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3",docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code",docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count",docItemBigData.getInteger("pallet_use_count"));
                    itemList.add(mapBigDataRow);
                }
                if(iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "判断是否存在放板任务异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //Panel校验(入和出)
    @RequestMapping(value = "/EapDyPlanPanelCheckAndSave", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dayuan/plan/EapDyPlanPanelCheckAndSave";
        String transResult="";
        String errorMsg="";
        String meUserTable="a_eap_me_station_user";
        String apsPlanTable="a_eap_aps_plan";
        String apsPlanDTable="a_eap_aps_plan_d";
        String meStationFlowTable="a_eap_me_station_flow";
        String result="";
        try{
            String station_id=jsonParas.getString("station_id");
            String group_lot_num="";
            String panel_barcode=jsonParas.getString("panel_barcode");
            String tray_barcode=jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value=jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value=jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value=jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value=jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value=jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code=jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String inspect_flag=jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag=jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag=jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code=jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String in_out_type=jsonParas.getString("in_out_type");//IN代表进入,OUT代表出

            //防止null数据
            String panel_flag="N";
            Integer panel_ng_code=0;
            String panel_ng_msg="";
            String panel_status="OK";
            String user_name="";
            if(panel_barcode==null) panel_barcode="";
            if(panel_barcode.equals("FFFFFFFF")) panel_barcode="NoRead";
            if(tray_barcode==null) tray_barcode="";
            if(ng_auto_pass_value==null) ng_auto_pass_value="";
            if(ng_manual_pass_value==null) ng_manual_pass_value="";
            if(panel_model_value==null) panel_model_value="";
            if(panel_model_value.equals("1")) panel_flag="Y";
            if(onecar_multybatch_value==null) onecar_multybatch_value="";
            if(onecar_multybatch_check_value==null) onecar_multybatch_check_value="";
            if(manual_judge_code==null || manual_judge_code.equals("")) manual_judge_code="0";
            if(inspect_flag==null || inspect_flag.equals("")) inspect_flag="N";
            if(dummy_flag==null || dummy_flag.equals("")) dummy_flag="N";
            if(eap_flag==null || eap_flag.equals("")) eap_flag="N";
            if(eap_ng_code==null || eap_ng_code.equals("")) eap_ng_code="0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义

            long station_id_long=Long.parseLong(station_id);

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                user_name=docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //获取当前任务
            String[] group_lot_status=new String[]{"PLAN","WORK"};
            List<String> lstPlanId=new ArrayList<>();
            //2.获取当前计划中或者执行中的子任务
            String plan_id="";
            String task_from="";
            String lot_num="";
            String lot_short_num="";
            Integer lot_index=1;
            String port_code="";
            String material_code="";
            String pallet_num="";
            String pallet_type="";
            String lot_level="";
            Integer fp_index=0;
            Double panel_length=0d;
            Double panel_width=0d;
            Double panel_tickness=0d;
            Integer plan_lot_count=0;
            Integer finish_count=0;
            Integer finish_ok_count=0;
            Integer finish_ng_count=0;
            Integer face_code=0;
            Integer pallet_use_count=0;
            Integer inspect_finish_count=0;
            Integer panel_index=0;
            Integer target_update_count=0;

            if(in_out_type.equals("IN")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(2).iterator();
                while(iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    String group_lot_status2=docItemBigData.getString("group_lot_status");
                    Integer plan_lot_count2=docItemBigData.getInteger("plan_lot_count");
                    Integer target_update_count2=docItemBigData.getInteger("target_update_count");
                    if(plan_lot_count2>target_update_count2){
                        group_lot_num=docItemBigData.getString("group_lot_num");
                        lstPlanId.add(docItemBigData.getString("plan_id"));
                        //判断是否为PLAN状态,若为PLAN状态则进行UPDATE
                        if(group_lot_status2.equals("PLAN")){
                            Query queryBigData2 = new Query();
                            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
                            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                            Update updateBigData2 = new Update();
                            updateBigData2.set("group_lot_status", "WORK");
                            mongoTemplate.updateMulti(queryBigData2, updateBigData2, apsPlanTable);
                        }
                        //获取参数
                        plan_id=docItemBigData.getString("plan_id");
                        task_from=docItemBigData.getString("task_from");
                        lot_num=docItemBigData.getString("lot_num");
                        lot_short_num=docItemBigData.getString("lot_short_num");
                        lot_index=docItemBigData.getInteger("lot_index");
                        port_code=docItemBigData.getString("port_code");
                        material_code=docItemBigData.getString("material_code");
                        pallet_num=docItemBigData.getString("pallet_num");
                        pallet_type=docItemBigData.getString("pallet_type");
                        lot_level=docItemBigData.getString("lot_level");
                        panel_length=docItemBigData.getDouble("panel_length");
                        panel_width=docItemBigData.getDouble("panel_width");
                        panel_tickness=docItemBigData.getDouble("panel_tickness");
                        plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                        finish_count=docItemBigData.getInteger("finish_count");
                        finish_ok_count=docItemBigData.getInteger("finish_ok_count");
                        finish_ng_count=docItemBigData.getInteger("finish_ng_count");
                        face_code=docItemBigData.getInteger("face_code");
                        pallet_use_count=docItemBigData.getInteger("pallet_use_count");
                        inspect_finish_count=docItemBigData.getInteger("inspect_finish_count");
                        target_update_count=docItemBigData.getInteger("target_update_count");
                        iteratorBigData.close();
                        break;
                    }
                }
            }
            else{
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(2).iterator();
                while (iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    Integer plan_lot_count2=docItemBigData.getInteger("plan_lot_count");
                    Integer finish_count2=docItemBigData.getInteger("finish_count");
                    if(plan_lot_count2>finish_count2){
                        group_lot_num=docItemBigData.getString("group_lot_num");
                        lstPlanId.add(docItemBigData.getString("plan_id"));
                        //获取参数
                        plan_id=docItemBigData.getString("plan_id");
                        task_from=docItemBigData.getString("task_from");
                        lot_num=docItemBigData.getString("lot_num");
                        lot_short_num=docItemBigData.getString("lot_short_num");
                        lot_index=docItemBigData.getInteger("lot_index");
                        port_code=docItemBigData.getString("port_code");
                        material_code=docItemBigData.getString("material_code");
                        pallet_num=docItemBigData.getString("pallet_num");
                        pallet_type=docItemBigData.getString("pallet_type");
                        lot_level=docItemBigData.getString("lot_level");
                        panel_length=docItemBigData.getDouble("panel_length");
                        panel_width=docItemBigData.getDouble("panel_width");
                        panel_tickness=docItemBigData.getDouble("panel_tickness");
                        plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                        finish_count=docItemBigData.getInteger("finish_count");
                        finish_ok_count=docItemBigData.getInteger("finish_ok_count");
                        finish_ng_count=docItemBigData.getInteger("finish_ng_count");
                        face_code=docItemBigData.getInteger("face_code");
                        pallet_use_count=docItemBigData.getInteger("pallet_use_count");
                        inspect_finish_count=docItemBigData.getInteger("inspect_finish_count");
                        target_update_count=docItemBigData.getInteger("target_update_count");
                        iteratorBigData.close();
                        break;
                    }
                }
            }

            //若无工单,则返回空值
            if(group_lot_num.equals("")){
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
                return transResult;
            }

            String station_flow_id=CFuncUtilsSystem.CreateUUID(true);
            //板件出
            if(!in_out_type.equals("IN")){
                finish_count=finish_count+1;
                String lot_status="WORK";
                if(finish_count>=plan_lot_count) lot_status="FINISH";
                Update updateBigData = new Update();
                updateBigData.set("lot_status", lot_status);
                updateBigData.set("group_lot_status", lot_status);
                updateBigData.set("finish_count", finish_count);
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                result=station_flow_id+","+lot_num+","+pallet_num+","+ finish_count+","+panel_barcode+","+
                        panel_ng_code+","+inspect_finish_count+","+tray_barcode+","+plan_lot_count+","+lot_status;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
                return transResult;
            }

            //板件进
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            panel_index=target_update_count+1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("station_flow_id",station_flow_id);
            mapBigDataRow.put("station_id",station_id_long);
            mapBigDataRow.put("plan_id",plan_id);
            mapBigDataRow.put("task_from",task_from);
            mapBigDataRow.put("group_lot_num",group_lot_num);
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("lot_short_num",lot_short_num);
            mapBigDataRow.put("lot_index",lot_index);
            mapBigDataRow.put("port_code",port_code);
            mapBigDataRow.put("material_code",material_code);
            mapBigDataRow.put("pallet_num",pallet_num);
            mapBigDataRow.put("pallet_type",pallet_type);
            mapBigDataRow.put("lot_level",lot_level);
            mapBigDataRow.put("fp_index",fp_index);
            mapBigDataRow.put("panel_barcode",panel_barcode);
            mapBigDataRow.put("panel_length",panel_length);
            mapBigDataRow.put("panel_width",panel_width);
            mapBigDataRow.put("panel_tickness",panel_tickness);
            mapBigDataRow.put("panel_index",panel_index);
            mapBigDataRow.put("panel_status",panel_status);
            mapBigDataRow.put("panel_ng_code",panel_ng_code);
            mapBigDataRow.put("panel_ng_msg",panel_ng_msg);
            mapBigDataRow.put("inspect_flag",inspect_flag);
            mapBigDataRow.put("dummy_flag",dummy_flag);
            mapBigDataRow.put("manual_judge_code",manual_judge_code);
            mapBigDataRow.put("panel_flag",panel_flag);
            mapBigDataRow.put("user_name",user_name);
            mapBigDataRow.put("eap_flag",eap_flag);
            mapBigDataRow.put("tray_barcode",tray_barcode);
            mapBigDataRow.put("face_code",face_code);
            mapBigDataRow.put("offline_flag","N");

            //4.若为Dummy板则直接先存储
            if(dummy_flag.equals("Y")){
                result=station_flow_id+","+lot_num+","+pallet_num+","+ panel_index+","+panel_barcode+","+
                        panel_ng_code+","+inspect_finish_count+","+tray_barcode+","+plan_lot_count+",WORK";//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow,meStationFlowTable);
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if(eap_flag.equals("Y")){
                panel_ng_code=Integer.parseInt(eap_ng_code);
                if(panel_ng_code!=0){
                    panel_status="NG";
                    panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if((ng_auto_pass_value.equals("1")  || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")){
                    panel_status="NG_PASS";
                    panel_ng_code=4;
                    panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else{
                //6.1 有panel模式
                if(panel_model_value.equals("1")){
                    if(ng_manual_pass_value.equals("1")){
                        panel_status="NG_PASS";
                        panel_ng_code=4;
                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                    }
                    else {
                        if(panel_barcode.equals("NoRead") || panel_barcode.equals("")){
                            panel_status="NG";
                            panel_ng_code=2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                        }
                        else{
                            long pnListCount =  mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                            if(pnListCount>0){
                                Query queryBigDataD = new Query();
                                queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                long okCount=mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                if(okCount<=0){
                                    if(ng_auto_pass_value.equals("1")){
                                        panel_status="NG_PASS";
                                        panel_ng_code=4;
                                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                    else{
                                        panel_status="NG";
                                        panel_ng_code=1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                            else{
                                if(!lot_short_num.equals("")){//采用简码判断
                                    if(!panel_barcode.contains(lot_short_num)){
                                        if(ng_auto_pass_value.equals("1")){
                                            panel_status="NG_PASS";
                                            panel_ng_code=4;
                                            panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                        else {
                                            panel_status="NG";
                                            panel_ng_code=1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                            panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if(panel_status.equals("OK")){
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                long panelCount=mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if(panelCount>0){
                                    if(ng_auto_pass_value.equals("1")){
                                        panel_status="NG_PASS";
                                        panel_ng_code=4;
                                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                    else{
                                        panel_status="NG";
                                        panel_ng_code=3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg=planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status",panel_status);
            mapBigDataRow.put("panel_ng_code",panel_ng_code);
            mapBigDataRow.put("panel_ng_msg",panel_ng_msg);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow,meStationFlowTable);
            //更新完工数量
            target_update_count++;
            if(panel_status.equals("NG"))  finish_ng_count++;
            else finish_ok_count++;
            String lot_status="WORK";
            String isInFinish="WORK";
            if(target_update_count>=plan_lot_count) isInFinish="FINISH";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("target_update_count", target_update_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            //更新首检数量
            if(inspect_flag.equals("Y")){//首检必须要全部合格件,否则再次从0开始
                if(panel_status.equals("OK") || panel_status.equals("NG_PASS")) inspect_finish_count++;
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            //返回数据
            result=station_flow_id+","+lot_num+","+pallet_num+","+panel_index+","+panel_barcode+","+
                    panel_ng_code+","+inspect_finish_count+","+tray_barcode+","+plan_lot_count+","+isInFinish;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
        }
        catch (Exception ex){
            errorMsg= "异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //工单结束
    @RequestMapping(value = "/EapDyPlanUpdateFinishStatus", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyPlanUpdateFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dayuan/EapDyPlanUpdateFinishStatus";
        String selectResult="";
        String errorMsg="";
        String apsPlanTable="a_eap_aps_plan";
        String meStationFlowTable="a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try{
            String group_lot_num=jsonParas.getString("group_lot_num");
            List<String> lstPlanId=new ArrayList<>();
            String station_id=jsonParas.getString("station_id");
            String port_code=jsonParas.getString("port_code");
            String task_error_code=jsonParas.getString("task_error_code");
            Integer task_error_code_int=Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList=new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            JSONArray jaLotFinish=new JSONArray();
            while(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                group_lot_num=docItemBigData.getString("group_lot_num");
                String plan_id=docItemBigData.getString("plan_id");
                String lot_num2=docItemBigData.getString("lot_num");
                Integer plan_lot_count2=docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2=docItemBigData.getInteger("target_lot_count");
                Integer finish_count2=docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2=docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2=docItemBigData.getInteger("finish_ng_count");
                JSONObject jbItem2=new JSONObject();
                jbItem2.put("group_lot_num",group_lot_num);
                jbItem2.put("plan_id",plan_id);
                jbItem2.put("lot_num",lot_num2);
                jbItem2.put("plan_lot_count",plan_lot_count2);
                jbItem2.put("target_lot_count",target_lot_count2);
                jbItem2.put("finish_count",finish_count2);
                jbItem2.put("finish_ok_count",finish_ok_count2);
                jbItem2.put("finish_ng_count",finish_ng_count2);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();
            if(group_lot_num==null || group_lot_num.equals("")){
                selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow=new HashMap<>();
                String item_date=sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date",item_date);
                mapBigDataRow.put("task_from",docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num",docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num",docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num",docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index",docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code",docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num",docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type",docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level",docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode",docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length",docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width",docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness",docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index",docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status",docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code",docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg",docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag",docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code",docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag",docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name",docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag",docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode",docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code",docItemBigData.getInteger("face_code"));
                itemList.add(mapBigDataRow);
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();

            //更改完工任务状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("task_error_code", task_error_code_int);
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,jaLotFinish.toString(),"",0);
        }
        catch (Exception ex){
            errorMsg= "更改任务完工状态以及查询完工明细异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
