package com.api.eap.project.thailand.dycj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.dy.EapDyInterfCommon;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 泰国定颖(持久)EAP发送流程数据定义接口
 * 1.CarrierIDReport:[接口]载具扫描上报验证
 * 2.CarrierStatusReport:[接口]开始/结束/取消/终止投板（收板）时上报
 * 3.JobCountReport:[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
 * 4.WIPTrackingReport:[接口]每一lot完板上报
 * 5.EachPanelDataDownLoad:[上下游接口]当片信息(上游传递给下游)
 * 6.BcOffLineLotDownLoad:[上下游接口]BC离线工单信息下发
 * 7.BcOffLineLoginInDownLoad:[上下游接口]BC离线同步登入
 * 8.LotFinishDownLoad:[上下游接口]传递到下游设备结批信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/dycj/interf/send")
public class EapDyCjSendFlowController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyCjSendFlowFunc eapDyCjSendFlowFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;

    //1.[接口]载具扫描上报验证
    @RequestMapping(value = "/CarrierIDReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCjCarrierIDReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dycj/interf/send/CarrierIDReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        try {
            String result = "OK";
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String production_mode = jsonParas.getString("production_mode");//制程分类
            if (production_mode == null || production_mode.equals("")) production_mode = "Default";
            String read_type = jsonParas.getString("read_type");

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();

            JSONObject response_body = eapDyCjSendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, station_attr, 0, production_mode, "N", null, "Y", read_type);
            if (response_body == null) result = "NG";
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "载具扫描上报验证异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]开始/结束/取消/终止上报
    @RequestMapping(value = "/CarrierStatusReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCjCarrierStatusReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dycj/interf/send/CarrierStatusReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String task_status = jsonParas.getString("task_status");
            String pallet_num = jsonParas.getString("pallet_num");
            String lot_list_str = jsonParas.getString("lot_list");
            String group_lot_num=jsonParas.getString("group_lot_num");
            String manual_wip_flag=jsonParas.getString("manual_wip_flag");
            if(group_lot_num==null) group_lot_num="";
            if(manual_wip_flag==null) manual_wip_flag="";
            String sqlStation = "select station_id," + "COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, request, apiRoutePath);
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            Long station_id_long=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            JSONArray lot_list = JSONArray.parseArray(lot_list_str);
            //若是WaitProc则需要做映射
            if(task_status.equals("WaitProc")){
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                Integer panel_model = -1;
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    panel_model = docItemBigData.getInteger("panel_model");
                    iteratorBigData.close();
                }
                if (panel_model >= 0) {
                    errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr,
                            "Ais", "AisConfig", "NgPanelPassFlag", "EAP",
                            String.valueOf(panel_model), true);
                    if (!errorMsg.equals("")) {
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
            }
            eapDyCjSendFlowFunc.CarrierStatusReport(station_code, pallet_num, task_status, lot_list, "0",
                    station_attr, port_code,manual_wip_flag);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "开始/结束/取消/终止上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.综合下游下发任务以及其他任务相关信息
    @RequestMapping(value = "/DownDeviceTaskInfo", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCjDownDeviceTaskInfo(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dycj/interf/send/DownDeviceTaskInfo";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        MongoCursor<Document> iteratorBigData = null;
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String sys_model = jsonParas.getString("sys_model");
            String group_lot_num = jsonParas.getString("group_lot_num");
            //远程模式不需要下发到下游
            if (sys_model.equals("3")) {
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String lot_num = docItemBigData.getString("lot_num");
                String lot_short_num = docItemBigData.getString("lot_short_num");
                String material_code = docItemBigData.getString("material_code");
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                String item_info = docItemBigData.getString("item_info");
                JSONArray ja = new JSONArray();
                if (item_info != null && !item_info.equals("")) {
                    ja = JSONArray.parseArray(item_info);
                }
                eapDyCjSendFlowFunc.BcOffLineLotDownLoad(station_code, lot_num, material_code, plan_lot_count, lot_short_num, ja);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            try {
                if (iteratorBigData != null) {
                    if (iteratorBigData.hasNext()) iteratorBigData.close();
                }
            } catch (Exception exx) {
            }
            errorMsg = "综合下游下发任务信息异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.EAP子批次完板上报
    @RequestMapping(value = "/SubLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCjSubLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dycj/interf/send/SubLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        MongoCursor<Document> iteratorBigData = null;
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String lot_num= jsonParas.getString("lot_num");
            String group_lot_num= jsonParas.getString("group_lot_num");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");
            String prod_mode = jsonParas.getString("prod_mode");
            String nw_value = jsonParas.getString("nw_value");//内外层代码
            String short_count = jsonParas.getString("short_count");//少片数量
            if (short_count == null || short_count.equals("")) short_count = "0";
            String noread_count = jsonParas.getString("noread_count");//NoRead数量
            if (noread_count == null || noread_count.equals("")) noread_count = "0";
            String offline_flag = "N";
            String local_flag = "Y";
            if (sys_model.equals("3")) local_flag = "N";
            if(sys_model.equals("0") || sys_model.equals("1")) offline_flag="Y";
            //判断是否为定颖新版本,最后统一上报
            String wip_flag=jsonParas.getString("wip_flag");
            String m_wip_lot_num=jsonParas.getString("m_wip_lot_num");
            String manual_wip_flag=jsonParas.getString("manual_wip_flag");
            if(wip_flag==null) wip_flag="";
            if(m_wip_lot_num==null) m_wip_lot_num="";
            if(manual_wip_flag==null) manual_wip_flag="";
            Boolean isNewVersion=eapDyInterfCommon.CheckDyVersion(3);
            if(isNewVersion && !wip_flag.equals("Y")){
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }
            //新增一批多车多级
            String out_code = jsonParas.getString("out_code");
            if (out_code == null || out_code.equals("")) out_code = "-1";

            //根据母批查询信息
            String pallet_num="";
            Integer sum_plan_count = 0;
            Integer sum_finish_count = 0;
            Integer sum_finish_ok_count = 0;
            String material_code_P001 = "";
            String lot_version_P002 = "";
            Integer first_plan_count_P004 = 0;
            Integer lot_finish_count = 0;
            Integer lot_finish_count2 = 0;
            String lot_short_num = "";
            String attribute1_P005 = "";
            String attribute2_P006 = "";
            String user_name = "";
            String dept_id = "";
            String shift_id = "";
            String task_start_time = CFuncUtilsSystem.GetNowDateTime("");
            String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
            String item_info = "";
            JSONArray item_attr_list = null;
            String splitseq = "0";
            String lastSplit = "1";
            if (prod_mode == null || prod_mode.equals("")) prod_mode = "0";
            if (nw_value == null || nw_value.equals("")) nw_value = "0";
            JSONObject attr_else = null;
            Double panel_length=0D;
            Double panel_width=0D;
            Double panel_tickness=0D;
            Integer panel_model=-1;
            String task_from="EAP";

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                String lot_num2 = docItemBigData.getString("lot_num");
                sum_plan_count += plan_lot_count;
                sum_finish_count += finish_count;
                sum_finish_ok_count += finish_ok_count;
                if (lot_num2.equals(lot_num)) {
                    task_from=docItemBigData.getString("task_from");
                    material_code_P001 = docItemBigData.getString("material_code");
                    lot_version_P002 = docItemBigData.getString("lot_level");
                    first_plan_count_P004 = plan_lot_count;
                    lot_finish_count = finish_ok_count;
                    lot_finish_count2=finish_count;
                    lot_short_num = docItemBigData.getString("lot_short_num");
                    item_info = docItemBigData.getString("item_info");
                    attribute1_P005 = docItemBigData.getString("attribute1");
                    attribute2_P006 = docItemBigData.getString("attribute2");
                    panel_length=docItemBigData.getDouble("panel_length");
                    panel_width=docItemBigData.getDouble("panel_width");
                    panel_tickness=docItemBigData.getDouble("panel_tickness");
                    panel_model=docItemBigData.getInteger("panel_model");
                    if (docItemBigData.containsKey("attribute3")) {
                        String attribute_else = docItemBigData.getString("attribute3");
                        if (attribute_else != null && !attribute_else.equals("")) {
                            attr_else = JSONObject.parseObject(attribute_else);
                        }
                    }
                    pallet_num = docItemBigData.getString("pallet_num");
                    task_start_time = docItemBigData.getString("task_start_time");
                    task_end_time = docItemBigData.getString("task_end_time");
                    if (task_start_time == null || task_start_time.equals("")) {
                        task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    }
                    if (task_end_time == null || task_end_time.equals("")) {
                        task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    }
                    //判断结束时间是否大于开始时间,若是则开始时间设定等于结束时间,这是因为可能没有放一块板子
                    if (CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time) < 0) {
                        task_start_time = task_end_time;
                    }
                }
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前用户
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                dept_id = docItemBigData.getString("dept_id");
                shift_id = docItemBigData.getString("shift_id");
                iteratorBigData.close();
            }

            JSONObject jsonObject = eapDyInterfCommon.GetParamsMapping();
            Boolean isP1Factory = eapDyInterfCommon.CheckDyFactory("P1");
            Integer wip_noread_count=0;
            Integer wip_short_count=0;
            if(!isNewVersion){
                //3.处理Item_Info
                if (item_info != null && !item_info.equals("")) {
                    if (!isP1Factory) {
                        for (int j = 1; j <= 200; j++) {
                            String SSource = "S" + String.format("%03d", j);
                            String TTarget = "T" + String.format("%03d", j + 1);
                            if (jsonObject != null && jsonObject.containsKey(TTarget)) {
                                SSource = jsonObject.getString(TTarget);
                            }
                            item_info = item_info.replace(SSource, TTarget);
                        }
                    }
                    item_attr_list = JSONArray.parseArray(item_info);
                    if (!isP1Factory) {
                        if (nw_value == null || nw_value.equals("")) nw_value = "0";
                        JSONObject nw_obj = new JSONObject();
                        nw_obj.put("item_id", "T001");
                        nw_obj.put("item_value", nw_value);
                        item_attr_list.add(nw_obj);
                    }
                }
                if (!isP1Factory) {
                    if (item_attr_list == null) item_attr_list = new JSONArray();
                    for (int k = item_attr_list.size() - 1; k >= 0; k--) {
                        JSONObject jbItemAttr = item_attr_list.getJSONObject(k);
                        String item_id = jbItemAttr.getString("item_id");
                        if (item_id.equals("T030") || item_id.equals("T031")) {
                            item_attr_list.remove(k);
                        }
                    }
                    JSONObject T030 = new JSONObject();
                    T030.put("item_id", "T030");
                    T030.put("item_value", noread_count);
                    item_attr_list.add(T030);
                    JSONObject T031 = new JSONObject();
                    T031.put("item_id", "T031");
                    T031.put("item_value", short_count);
                    item_attr_list.add(T031);
                }
            }
            else{
                //泰国定颖
                item_attr_list = new JSONArray();
                //1.结批类型:1：计数到达；2：结批信息；3：完工信号；4：取消任務；5：人员手动；6：掃碼補報
                String end_lot_type="3";
                if(first_plan_count_P004==lot_finish_count) end_lot_type="1";
                else if(lot_finish_count<=0) end_lot_type="4";
                Integer out_code_int=Integer.parseInt(out_code);
                if(out_code_int>=4){
                    end_lot_type="5";
                    if(lot_finish_count2<=0) end_lot_type="4";
                }
                if(!m_wip_lot_num.equals("")) end_lot_type="5";
                if(out_code_int>=4){
                    if(lot_finish_count2<=0) end_lot_type="4";
                }
                if(manual_wip_flag.equals("Y")) end_lot_type="6";
                //2.NoRead数量
                Integer last_noread_count=0;
                Integer last_short_count=0;
                if(station_attr.equals("Load")){
                    Integer[] lstNoReadPanelCode=new Integer[]{2,7};
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("panel_ng_code").in(lstNoReadPanelCode));
                    queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                    long noReadCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                    //查询NoRead为NG的数量
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("panel_ng_code").in(lstNoReadPanelCode));
                    queryBigData.addCriteria(Criteria.where("panel_status").is("NG"));
                    queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                    long ngNoReadCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                    last_noread_count=(int)noReadCount;
                    last_short_count=first_plan_count_P004-lot_finish_count-(int)ngNoReadCount;
                    if(!m_wip_lot_num.equals("")){
                        if(m_wip_lot_num.equals(lot_num)){
                            last_noread_count=Integer.parseInt(noread_count);
                            last_short_count=Integer.parseInt(short_count);
                        }
                    }
                }
                else{
                    Integer[] lstNoReadPanelCode=new Integer[]{2,7};
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("panel_ng_code").in(lstNoReadPanelCode));
                    queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                    long noReadCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                    last_noread_count=(int)noReadCount;
                    last_short_count=first_plan_count_P004-lot_finish_count;
                }
                Integer new_short_count=last_short_count;
                if(new_short_count<0) new_short_count=0;
                wip_noread_count=last_noread_count;
                wip_short_count=new_short_count;
                if(wip_short_count>0 && station_attr.equals("UnLoad") && !manual_wip_flag.equals("Y") && lot_finish_count2>0){
                    end_lot_type="5";
                }
                //类型
                JSONObject jbAttr=new JSONObject();
                jbAttr.put("item_id", "T001");
                jbAttr.put("item_value", end_lot_type);
                item_attr_list.add(jbAttr);
                //NoRead数量
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T002");
                jbAttr.put("item_value", String.valueOf(last_noread_count));
                item_attr_list.add(jbAttr);
                //3.少片数量
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T003");
                jbAttr.put("item_value", String.valueOf(new_short_count));
                item_attr_list.add(jbAttr);
                //4.板长
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T004");
                jbAttr.put("item_value", String.format("%.3f", panel_length));
                item_attr_list.add(jbAttr);
                //5.板宽
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T005");
                jbAttr.put("item_value", String.format("%.3f", panel_width));
                item_attr_list.add(jbAttr);
                //6.板厚
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T006");
                jbAttr.put("item_value", String.format("%.3f", panel_tickness));
                item_attr_list.add(jbAttr);
                //7.读码NG管控标志
                String new_panel_model="1";
                if(panel_model==1) new_panel_model="2";
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T007");
                jbAttr.put("item_value", String.valueOf(new_panel_model));
                item_attr_list.add(jbAttr);
                //8.其他需要做映射方法
                if("AIS".equals(task_from)){
                    String dy_paramsMapping_ais=cFuncDbSqlResolve.GetParameterValue("Dy_ParamsMapping_Ais");
                    if(!"".equals(dy_paramsMapping_ais)){
                        jsonObject=JSONObject.parseObject(dy_paramsMapping_ais);
                        if (jsonObject != null){
                            Set<String> keys=jsonObject.keySet();
                            JSONArray jaAttrTemp=null;
                            if (item_info != null && !item_info.equals("")){
                                jaAttrTemp=JSONArray.parseArray(item_info);
                            }
                            for(String key : keys){
                                String oriKey=jsonObject.getString(key);
                                String keyValue=oriKey;
                                if(jaAttrTemp!=null && jaAttrTemp.size()>0 && !oriKey.equals("") && oriKey.startsWith("S")){
                                    for(int k=0;k<jaAttrTemp.size();k++){
                                        JSONObject jbItemTemp=jaAttrTemp.getJSONObject(k);
                                        String item_id2=jbItemTemp.getString("item_id");
                                        String item_value2=jbItemTemp.getString("item_value");
                                        if(item_id2.equals(oriKey)){
                                            Double item_value3=0D;
                                            try{
                                                item_value3=Double.parseDouble(item_value2);
                                            }
                                            catch (Exception exChange){}
                                            keyValue=String.format("%.3f", item_value3);//若是S开头则需要给格式
                                            break;
                                        }
                                    }
                                }
                                jbAttr=new JSONObject();
                                jbAttr.put("item_id", key);
                                jbAttr.put("item_value", keyValue);
                                item_attr_list.add(jbAttr);
                            }
                        }
                    }
                }
                else{
                    if (jsonObject != null){
                        Set<String> keys=jsonObject.keySet();
                        JSONArray jaAttrTemp=null;
                        if (item_info != null && !item_info.equals("")){
                            jaAttrTemp=JSONArray.parseArray(item_info);
                        }
                        for(String key : keys){
                            String oriKey=jsonObject.getString(key);
                            String keyValue=oriKey;
                            if(jaAttrTemp!=null && jaAttrTemp.size()>0 && !oriKey.equals("")){
                                for(int k=0;k<jaAttrTemp.size();k++){
                                    JSONObject jbItemTemp=jaAttrTemp.getJSONObject(k);
                                    String item_id2=jbItemTemp.getString("item_id");
                                    String item_value2=jbItemTemp.getString("item_value");
                                    if(item_id2.equals(oriKey)){
                                        keyValue=item_value2;
                                        break;
                                    }
                                }
                            }
                            jbAttr=new JSONObject();
                            jbAttr.put("item_id", key);
                            jbAttr.put("item_value", keyValue);
                            item_attr_list.add(jbAttr);
                        }
                    }
                }
            }

            //用于DataUpLoad参数
            Integer lot_left_count = 0;
            JSONArray carr_infos = new JSONArray();
            JSONObject jbCarr = new JSONObject();
            JSONObject jbLot = new JSONObject();
            if (!eapDyInterfCommon.CheckDyVersion(3)) {
                lot_left_count = first_plan_count_P004;
            } else {
                if (station_attr.equals("Load")) {
                    lot_left_count = first_plan_count_P004 - lot_finish_count;
                    if (lot_left_count < 0) lot_left_count = 0;
                } else {
                    lot_left_count = lot_finish_count;
                }
            }
            jbLot.put("lot_id", lot_num);
            jbLot.put("lot_count", lot_left_count);
            jbCarr.put("lot", jbLot);
            JSONObject pnl_infos = new JSONObject();
            JSONArray jaSlot = new JSONArray();
            //查询明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(100).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                String panel_barcode = docItemBigData.getString("panel_barcode");
                Integer panel_index = docItemBigData.getInteger("panel_index");
                JSONObject jbDetail = new JSONObject();
                jbDetail.put("slot_no", String.format("%03d", panel_index));
                jbDetail.put("panel_id", panel_barcode);
                jaSlot.add(jbDetail);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            pnl_infos.put("slot", jaSlot);
            jbCarr.put("pnl_infos", pnl_infos);
            carr_infos.add(jbCarr);

            //判断是否发送WIP数据
            Boolean isSendWip = true;
            if (OneCarMultyLotFlag.equals("2")) {
                Integer out_code_int = Integer.parseInt(out_code);
                if (out_code_int > 0 && out_code_int < 4) {
                    if (sum_plan_count > sum_finish_ok_count) isSendWip = false;
                }
            }

            //上报WIP【异步】
            if (isSendWip) {
                eapDyCjSendFlowFunc.WIPTrackingReport(Long.parseLong(station_id), station_code, user_name, dept_id, shift_id,
                        task_start_time, task_end_time, lot_num, sum_plan_count, first_plan_count_P004, lot_finish_count,
                        material_code_P001, lot_short_num, lot_version_P002, prod_mode, attribute1_P005, attribute2_P006,
                        item_attr_list, offline_flag, local_flag, attr_else);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            try {
                if (iteratorBigData != null) {
                    if (iteratorBigData.hasNext()) iteratorBigData.close();
                }
            } catch (Exception exx) {
            }
            errorMsg = "EAP子批次完板上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.通知下游批次结束
    @RequestMapping(value = "/DownDeviceSubLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCjDownDeviceSubLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dycj/interf/send/DownDeviceSubLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String lot_num = jsonParas.getString("lot_num");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)

            //根据批次查询信息
            Integer lot_finish_count = 0;
            String lot_short_num = "";

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lot_finish_count = docItemBigData.getInteger("finish_ok_count");
                iteratorBigData.close();
            }

            //通知下游结批信号
            eapDyCjSendFlowFunc.LotFinishDownLoad(station_code, lot_num, lot_finish_count, lot_short_num);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "通知下游批次结束异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //9.EAP综合母批完板上报
    @RequestMapping(value = "/GroupLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCjGroupLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dycj/interf/send/GroupLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String prod_mode = jsonParas.getString("prod_mode");
            String nw_value = jsonParas.getString("nw_value");//内外层代码
            String lot_list = jsonParas.getString("lot_list");
            String out_code = jsonParas.getString("out_code");
            String local_flag = "Y";
            if (sys_model.equals("1")) local_flag = "N";
            if (out_code == null || out_code.equals("")) out_code = "-1";
            //针对lot_list进行处理
            JSONArray lotArray = JSONArray.parseArray(lot_list);
            if (lotArray != null && lotArray.size() > 0) {
                for (int i = 0; i < lotArray.size(); i++) {
                    JSONObject jbItem = lotArray.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    String lot_num = jbItem.getString("lot_num");
                    String pallet_num = jbItem.getString("pallet_num");
                    Integer plan_lot_count = jbItem.getInteger("plan_lot_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    Integer finish_count = jbItem.getInteger("finish_count");
                    if(!eapDyInterfCommon.CheckDyVersion(3)){
                        if (plan_lot_count > finish_ok_count) {
                            JSONObject jsonParas2 = jsonParas;
                            //说明未完成工单,需要执行WIP动作
                            String panel_result = "," + lot_num + "," + pallet_num + ",0,,0,0,,WORK," + group_lot_num;
                            jsonParas2.put("panel_result", panel_result);
                            jsonParas2.put("pallet_num", pallet_num);
                            EapDyCjSubLotFinishReport(jsonParas2, request);
                        }
                    }
                    else{
                        JSONObject jsonParas2 = jsonParas;
                        jsonParas2.put("wip_flag","Y");
                        String panel_result = "," + lot_num + "," + pallet_num + ",0,,0,0,,WORK," + group_lot_num;
                        jsonParas2.put("panel_result", panel_result);
                        jsonParas2.put("pallet_num", pallet_num);
                        EapDyCjSubLotFinishReport(jsonParas2, request);
                    }
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "EAP综合母批完板上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //10.母批完板通知下游批次结束
    @RequestMapping(value = "/DownDeviceGroupLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCjDownDeviceGroupLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dycj/interf/send/DownDeviceGroupLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        try {
            JSONObject jsonParas2 = jsonParas;
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String station_attr = jsonParas.getString("station_attr");
            String port_code = jsonParas.getString("port_code");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String lot_list = jsonParas.getString("lot_list");
            String local_flag = "Y";
            if (sys_model.equals("1")) local_flag = "N";
            //针对lot_list进行处理
            JSONArray lotArray = JSONArray.parseArray(lot_list);
            if (lotArray != null && lotArray.size() > 0) {
                for (int i = 0; i < lotArray.size(); i++) {
                    JSONObject jbItem = lotArray.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    String lot_num = jbItem.getString("lot_num");
                    String pallet_num = jbItem.getString("pallet_num");
                    Integer plan_lot_count = jbItem.getInteger("plan_lot_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    if (plan_lot_count > finish_ok_count) {
                        //说明未完成工单,需要执行WIP动作
                        String panel_result = "," + lot_num + "," + pallet_num + ",0,,0,0,,WORK," + group_lot_num;
                        jsonParas2.put("panel_result", panel_result);
                        jsonParas2.put("pallet_num", pallet_num);
                        EapDyCjDownDeviceSubLotFinishReport(jsonParas2, request);
                    }
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "母批完板通知下游批次结束异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //机台内数量上报
    @RequestMapping(value = "/JobCountReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyCjJobCountReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dycj/interf/send/JobCountReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_attr=jsonParas.getString("station_attr");
            Integer job_count=jsonParas.getInteger("job_count");
            if(station_attr==null) station_attr="";
            if(job_count==null) job_count=0;

            //查询工位信息
            String sqlStation = "select station_id,station_code," +
                    "COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_attr='" + station_attr + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            String station_code=itemListStation.get(0).get("station_code").toString();

            eapDyCjSendFlowFunc.JobCountReport(station_code,job_count,"01");
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "机台内数量上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
