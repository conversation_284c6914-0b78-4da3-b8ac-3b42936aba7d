package com.api.pack.core.pile;

import com.api.base.Const;
import com.api.base.IMongoBasicService;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pack.core.board.BoardConst;
import org.springframework.context.event.EventListener;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;

@Service
@Transactional(rollbackFor = Exception.class)
public class PileService extends IMongoBasicService<Pile, PileRepository>
{
    private final MongoTemplate mongoTemplate;

    public PileService(PileRepository repository, MongoTemplate mongoTemplate)
    {
        super(repository);
        this.mongoTemplate = mongoTemplate;
    }

    @EventListener
    public void handlePileRemoveEvent(PileRemoveEvent event)
    {
        Pile pile = event.getSource();
        if (pile.getId() != null)
        {
            pile.setEnableFlag(Const.FLAG_N);
            this.getRepository().save(pile);
        }
    }

    @Transactional(readOnly = true)
    public Pile findLastOne(String lotNum)
    {
        Query query = new Query();
        query.addCriteria(Criteria.where("lot_num").is(lotNum));
        query.addCriteria(Criteria.where("enable_flag").is(Const.FLAG_Y));
        query.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
        return this.mongoTemplate.findOne(query, Pile.class);
    }

    @Transactional(readOnly = true)
    public Pile findOneByPileBarcode(String pileBarcode)
    {
        return this.findOneByPileBarcodeAndEnableFlag(pileBarcode, Const.FLAG_Y);
    }

    @Transactional(readOnly = true)
    public Pile findOneByPileBarcodeAndEnableFlag(String pileBarcode, String enableFlag)
    {
        Query query = new Query();
        query.addCriteria(Criteria.where("pile_barcode").is(pileBarcode));
        if (enableFlag != null && !enableFlag.isEmpty())
        {
            query.addCriteria(Criteria.where(Const.PROPERTY_ENABLE_FLAG).is(enableFlag));
        }
        query.with(Sort.by(Sort.Direction.DESC, Const.PROPERTY_ITEM_DATE_VAL));
        return this.mongoTemplate.findOne(query, Pile.class);
    }

    public void unbindByPileBarcode(String stationCode, String pileBarcode)
    {
        Query pileQuery = new Query();
        pileQuery.addCriteria(Criteria.where("pile_barcode").is(pileBarcode));
        pileQuery.addCriteria(Criteria.where("enable_flag").is(BoardConst.FLAG_Y));
        Update pileUpdate = new Update();
        pileUpdate.set("enable_flag", BoardConst.FLAG_N);
        pileUpdate.set("unbind_flag", BoardConst.FLAG_Y);
        pileUpdate.set("unbind_user", stationCode);
        pileUpdate.set("unbind_time", CFuncUtilsSystem.GetNowDateTime(null));
        pileUpdate.set("unbind_way", "RCS");
        mongoTemplate.updateMulti(pileQuery, pileUpdate, mongoTemplate.getCollectionName(Pile.class));
    }

    public void updateCustomBarcodeByPileBarcode(String customBarcode, String pileBarcode)
    {
        LocalDateTime now = LocalDateTime.now();
        Date itemDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        Long itemDateVal = Long.parseLong(now.format(DateTimeFormatter.ofPattern(Const.DATE_FORMAT_DEFAULT)));
        Query query = new Query();
        query.addCriteria(Criteria.where("pile_barcode").is(pileBarcode));
        query.addCriteria(Criteria.where(Const.PROPERTY_ENABLE_FLAG).is(Const.FLAG_Y));
        Update update = new Update();
        update.set(Const.PROPERTY_ITEM_DATE, itemDate);
        update.set(Const.PROPERTY_ITEM_DATE_VAL, itemDateVal);
        update.set("custom_barcode", customBarcode);
        this.mongoTemplate.updateFirst(query, update, Pile.class);
    }

    public List<Pile> findAllByPlanIdOrderByPileIndexAsc(String planId)
    {
        return this.getRepository().findAllByPlanIdAndEnableFlagOrderByPileIndexAsc(planId, Const.FLAG_Y);
    }
}
