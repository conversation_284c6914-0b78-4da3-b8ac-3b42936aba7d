package com.api.pmc.core.andon;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 安灯报表接口
 * 1.产线安灯按钮信息
 * 2.产线安灯工位牌信息
 * 3.产线安灯事件信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/andon")
public class PmcCoreAndonReportController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.产线安灯按钮信息
    @RequestMapping(value = "/PmcCoreAndonBtnReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreAndonBtnReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/andon/PmcCoreAndonBtnReport";
        String selectResult="";
        String errorMsg="";
        try{
            String prod_line_code=jsonParas.getString("prod_line_code");//产线
            String work_center_code=jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String prod_line_type=jsonParas.getString("prod_line_type");//产线(工位分段，例如：NS1、NS2)
            //返回结果集合
            List<Map<String, Object>> andonResultList=new ArrayList<>();
            Map<String, Object> andonResultItem=new HashMap<>();
            //1、获取系统参数
            String prodLineTypeValue="";
            if(prod_line_type!=null && !prod_line_type.equals("")){
                prodLineTypeValue=cFuncDbSqlResolve.GetParameterValue(prod_line_type);
            }
            //2、获取产线
            String prod_line_id="";//产线ID
            if(prod_line_code!=null && !prod_line_code.equals("")){
                String sqlProdLine="select COALESCE(prod_line_id,0) prod_line_id " +
                        "from sys_fmod_prod_line " +
                        "where enable_flag='Y' "+
                        "and prod_line_code='"+prod_line_code+ "' ";
                if(work_center_code!=null && !work_center_code.equals("")){
                    sqlProdLine+="and work_center_code='"+work_center_code+ "' ";
                }
                List<Map<String, Object>> itemListProdLine=cFuncDbSqlExecute.ExecSelectSql(prod_line_code,sqlProdLine,false,request,apiRoutePath);
                if(itemListProdLine==null || itemListProdLine.size()<=0){
                    andonResultItem.put("andon_btn_list","");//安灯按钮集合
                    andonResultList.add(andonResultItem);
                    selectResult=CFuncUtilsLayUiResut.GetStandJson(true,andonResultList,"","",0);
                    return selectResult;
                }
                prod_line_id=itemListProdLine.get(0).get("prod_line_id").toString();
            }
            //3、获取安灯按钮
            String sqlAndonBtn="select COALESCE(b.station_code,'') station_code," +
                    "COALESCE(b.station_des,'') station_des," +
                    "COALESCE(b.station_order,0) station_order," +
                    "COALESCE(a.andon_type,'') andon_type," +
                    "COALESCE(a.andon_des,'') andon_des," +
                    "COALESCE(a.andon_color,'') andon_color," +
                    "COALESCE(a.btn_level,0) btn_level " +
                    "from d_pmc_fmod_andon_btn a," +
                    "     sys_fmod_station b " +
                    "where a.station_id=b.station_id " +
                    "and b.enable_flag='Y' " +
                    " and a.enable_flag='Y' " +
                    " and a.andon_btn_status='1' ";
            if(prod_line_id!=null && !prod_line_id.equals("")){
                sqlAndonBtn+="and b.prod_line_id="+prod_line_id +" ";
            }
            if(work_center_code!=null && !work_center_code.equals("")){
                sqlAndonBtn+="and exists (select 1 from sys_fmod_prod_line c " +
                        "where c.prod_line_id=b.prod_line_id " +
                        "and c.enable_flag='Y' " +
                        "and c.work_center_code='"+work_center_code+ "') ";
            }
            if(prodLineTypeValue!=null && !prodLineTypeValue.equals("")){
                sqlAndonBtn+=" and a.station_id in ("+prodLineTypeValue+") ";
            }
            sqlAndonBtn+=" order by a.btn_level desc";
            List<Map<String, Object>> itemListAndonBtn=cFuncDbSqlExecute.ExecSelectSql(prod_line_code,sqlAndonBtn,false,request,apiRoutePath);
            andonResultItem.put("andon_btn_list",itemListAndonBtn);//安灯按钮集合

            andonResultList.add(andonResultItem);
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,andonResultList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "产线安灯按钮信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //2.产线安灯工位牌信息
    @RequestMapping(value = "/PmcCoreAndonCardReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreAndonCardReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/andon/PmcCoreAndonCardReport";
        String selectResult="";
        String errorMsg="";
        try{
            String prod_line_code=jsonParas.getString("prod_line_code");//产线
            String work_center_code=jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String prod_line_type=jsonParas.getString("prod_line_type");//产线(工位分段，例如：NS1、NS2)
            //返回结果集合
            List<Map<String, Object>> andonResultList=new ArrayList<>();
            Map<String, Object> andonResultItem=new HashMap<>();
            //1、获取系统参数
            String prodLineTypeValue="";
            if(prod_line_type!=null && !prod_line_type.equals("")){
                prodLineTypeValue=cFuncDbSqlResolve.GetParameterValue(prod_line_type);
            }
            //2、获取产线
            String prod_line_id="";//产线ID
            String prod_line_des="";//产线描述
            if(prod_line_code!=null && !prod_line_code.equals("")){
                String sqlProdLine="select COALESCE(prod_line_id,0) prod_line_id," +
                        "COALESCE(prod_line_des,'') prod_line_des " +
                        "from sys_fmod_prod_line " +
                        "where enable_flag='Y' "+
                        "and prod_line_code='"+prod_line_code+ "' ";
                if(work_center_code!=null && !work_center_code.equals("")){
                    sqlProdLine+="and work_center_code='"+work_center_code+ "' ";
                }
                List<Map<String, Object>> itemListProdLine=cFuncDbSqlExecute.ExecSelectSql(prod_line_code,sqlProdLine,false,request,apiRoutePath);
                if(itemListProdLine==null || itemListProdLine.size()<=0){
                    andonResultItem.put("andon_card_list","");//安灯工位牌集合
                    andonResultList.add(andonResultItem);
                    selectResult=CFuncUtilsLayUiResut.GetStandJson(true,andonResultList,"","",0);
                    return selectResult;
                }
                prod_line_id=itemListProdLine.get(0).get("prod_line_id").toString();
                prod_line_des=itemListProdLine.get(0).get("prod_line_des").toString();
            }
            //3、获取安灯工位牌
            String sqlAndonCard="select COALESCE(b.station_code,'') station_code," +
                    "COALESCE(b.station_des,'') station_des," +
                    "COALESCE(b.station_order,0) station_order," +
                    "COALESCE(a.andon_card_code,'') andon_card_code," +
                    "COALESCE(a.andon_card_type,'') andon_card_type," +
                    "COALESCE(a.andon_card_color,'') andon_card_color " +
                    "from d_pmc_fmod_andon_card a," +
                    "     sys_fmod_station b " +
                    "where a.station_id=b.station_id " +
                    "and b.enable_flag='Y' " +
                    " and a.enable_flag='Y' " +
                    " and a.andon_card_status='1' ";
            if(prod_line_id!=null && !prod_line_id.equals("")){
                sqlAndonCard+="and b.prod_line_id="+prod_line_id + " ";
            }
            if(work_center_code!=null && !work_center_code.equals("")){
                sqlAndonCard+="and exists (select 1 from sys_fmod_prod_line c " +
                        "where c.prod_line_id=b.prod_line_id " +
                        "and c.enable_flag='Y' " +
                        "and c.work_center_code='"+work_center_code+ "') ";
            }
            if(prodLineTypeValue!=null && !prodLineTypeValue.equals("")){
                sqlAndonCard+=" and a.station_id in ("+prodLineTypeValue+") ";
            }
            sqlAndonCard+=" order by b.station_order ";
            List<Map<String, Object>> itemListAndonCard=cFuncDbSqlExecute.ExecSelectSql(prod_line_code,sqlAndonCard,false,request,apiRoutePath);
            andonResultItem.put("andon_card_list",itemListAndonCard);//安灯工位牌集合
            //计算起始位置(内饰)
            if(prod_line_type!=null && prod_line_type.equals("NS2")){
                andonResultItem.put("prod_line_des","内饰2线");//产线描述
                andonResultItem.put("rownum",34);//开始行
            } else if(prod_line_type!=null && prod_line_type.equals("NS1")){
                andonResultItem.put("prod_line_des","内饰1线");//产线描述
                andonResultItem.put("rownum",1);//开始行
            }
            else {
                andonResultItem.put("prod_line_des",prod_line_des);//产线描述
                andonResultItem.put("rownum",1);//开始行
            }

            andonResultList.add(andonResultItem);
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,andonResultList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "产线安灯工位牌信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //3.产线安灯事件信息
    @RequestMapping(value = "/PmcCoreAndonEventReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreAndonEventReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/andon/PmcCoreAndonEventReport";
        String selectResult="";
        String errorMsg="";
        try{
            String prod_line_code=jsonParas.getString("prod_line_code");//产线
            String work_center_code=jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String prod_line_type=jsonParas.getString("prod_line_type");//产线(工位分段，例如：NS1、NS2)
            //返回结果集合
            List<Map<String, Object>> andonResultList=new ArrayList<>();
            Map<String, Object> andonResultItem=new HashMap<>();
            //1、获取系统参数
            String prodLineTypeValue="";
            if(prod_line_type!=null && !prod_line_type.equals("")){
                prodLineTypeValue=cFuncDbSqlResolve.GetParameterValue(prod_line_type);
            }
            //2、获取安灯事件
            String sqlAndonEvent="select COALESCE(a.prod_line_code,'') prod_line_code," +
                    "COALESCE(a.station_code,'') station_code," +
                    "COALESCE(a.station_des,'') station_des," +
                    "COALESCE(b.station_order,0) station_order," +
                    "COALESCE(a.andon_type,'') andon_type," +
                    "COALESCE(a.andon_des,'') andon_des," +
                    "COALESCE(a.andon_limit_time,0) andon_limit_time," +
                    "COALESCE(a.happen_date,'') happen_date," +
                    "COALESCE(a.reset_date,'') reset_date," +
                    "COALESCE(a.cost_time,0) cost_time," +
                    "COALESCE(a.over_time_flag,'N') over_time_flag " +
                    "from d_pmc_me_andon_event a, " +
                    "     sys_fmod_station b " +
                    "where a.station_code=b.station_code " +
                    "and b.enable_flag='Y' " +
                    "and a.enable_flag='Y' " +
                    "and a.reset_flag='N' " +
                    "and a.andon_type!='99' ";//99表示停线
            if(prod_line_code!=null && !prod_line_code.equals("")){
                sqlAndonEvent+="and a.prod_line_code='"+prod_line_code+ "' ";
            }
            if(work_center_code!=null && !work_center_code.equals("")){
                sqlAndonEvent+="and a.work_center_code='"+work_center_code+ "' ";
            }
            if(prodLineTypeValue!=null && !prodLineTypeValue.equals("")){
                sqlAndonEvent+=" and b.station_id in ("+prodLineTypeValue+") ";
            }
            List<Map<String, Object>> itemListAndonEvent=cFuncDbSqlExecute.ExecSelectSql(prod_line_code,sqlAndonEvent,false,request,apiRoutePath);
            andonResultItem.put("andon_event_list",itemListAndonEvent);//安灯事件集合

            andonResultList.add(andonResultItem);
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,andonResultList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "产线安灯事件信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}

