package com.api.pack.project.tripod.op;

import com.alibaba.fastjson.JSONObject;
import com.api.base.Const;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.pack.core.board.BoardConst;
import com.api.pack.core.board.PCSBoardService;
import com.api.pack.core.board.SETBoard;
import com.api.pack.core.board.SETBoardService;
import com.api.pack.core.pile.PileService;
import com.api.pack.core.sort.SortConst;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <p>
 * Array逻辑处理
 * 1.线扫判断与存储
 * 2.更新上传标识
 * 3.更新板件状态
 * 4.根据pile_barcode和array_id解绑板件
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-20
 */
@RestController
@RequestMapping("/pack/project/tripod/op")
@Slf4j
public class PackTripodOpArrayController
{
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private PackTripodOpArrayFuncOfYN opArrayFuncOfYN;

    @Autowired
    private SETBoardService setBoardService;

    @Autowired
    private PCSBoardService pcsBoardService;

    @Autowired
    private PileService pileService;

    //1.线扫判断与存储
    @RequestMapping(value = "/ArraySave", produces = "application/json;charset=utf-8", method = {
            RequestMethod.POST, RequestMethod.GET
    })
    public String ArraySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/project/tripod/op/ArraySave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_pack_aps_plan";
        String meArrayTable = "a_pack_me_array";
        String meBdTable = "a_pack_me_bd";
        String result = "";
        try
        {
            //计时开始
            long l1 = System.currentTimeMillis();
            String user_name = "";
            String lang = jsonParas.getString("lang");
            if (!ObjectUtils.isEmpty(lang))
            {
                Locale.setDefault(Locale.forLanguageTag(lang));
            }
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            JSONObject ccd_data = jsonParas.getJSONObject("ccd_data");
            //1.获取当前WORK订单信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext())
            {
                errorMsg = "当前未下发任务,无法执行线扫判断,请先选择任务进行下发";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Document docItemBigData = iteratorBigData.next();
            String lotNum = docItemBigData.getString("lot_num"); // 批次号
            String plantOrderNum = docItemBigData.getString("plant_order_num"); // 工厂订单号
            String plantCode = docItemBigData.getString("plant_code"); // 工厂代码/厂别
            String cyclePeriod = String.valueOf(docItemBigData.get("cycle_period")); // 周期
            JSONObject jbPlan = new JSONObject();
            String plan_id = docItemBigData.getString("plan_id");
            Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
            Integer finish_ng_count = docItemBigData.getInteger("finish_ng_count");
            String modelVersion = String.valueOf(docItemBigData.get("model_version"));
            Double mLength = new BigDecimal(String.valueOf(docItemBigData.getDouble("m_length"))).doubleValue();
            Double mWidth = new BigDecimal(String.valueOf(docItemBigData.getDouble("m_width"))).doubleValue();
            Double mTickness = new BigDecimal(String.valueOf(docItemBigData.getDouble("m_tickness"))).doubleValue();
            Double mWeight = new BigDecimal(String.valueOf(docItemBigData.getDouble("m_weight"))).doubleValue();
            jbPlan.put("plan_id", plan_id);
            jbPlan.put("lot_num", lotNum);
            jbPlan.put("plant_order_num", plantOrderNum);
            jbPlan.put("task_type", docItemBigData.getString("task_type"));
            jbPlan.put("model_type", docItemBigData.getString("model_type"));
            jbPlan.put("model_version", modelVersion);
            jbPlan.put("array_type", docItemBigData.getString("array_type"));
            jbPlan.put("bd_type", docItemBigData.getString("bd_type"));
            jbPlan.put("m_length", mLength);
            jbPlan.put("m_width", mWidth);
            jbPlan.put("m_tickness", mTickness);
            jbPlan.put("m_weight", mWeight);
            jbPlan.put("cycle_period", cyclePeriod);
            jbPlan.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
            jbPlan.put("finish_ok_count", finish_ok_count);
            jbPlan.put("finish_ng_count", finish_ng_count);

            // 新增比对数据 added by jay-y 2024/04/20
            jbPlan.put("batch_no", docItemBigData.get("batch_no")); // 批号
            jbPlan.put("laser_batch_no", docItemBigData.get("laser_batch_no")); // 镭射批号
            jbPlan.put("typesetting_no", docItemBigData.get("typesetting_no")); // 排版数
            jbPlan.put("customer_mn", docItemBigData.get("customer_mn")); // 客户料号
            jbPlan.put("ul_code", docItemBigData.get("ul_code")); // UL号

            String recipe_paras = docItemBigData.getString("recipe_paras");
            iteratorBigData.close();

            //2.获取当前用户信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext())
            {
                docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //3.获取配方信息
            JSONObject jbRecipe = JSONObject.parseObject(recipe_paras);

            //4.查询当前分选条件
            String sqlSort = "select " + "sort_code,sort_name," + "COALESCE(sort_value,'') sort_value " + "from a_pack_fmod_sort " + "where enable_flag='Y' and sort_flag='Y' order by sort_index";
            List<Map<String, Object>> itemListSort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlSort, false, request, apiRoutePath);
            JSONObject jbSort = new JSONObject();
            if (itemListSort != null && itemListSort.size() > 0)
            {
                for (Map<String, Object> mapItem : itemListSort)
                {
                    String sort_code = mapItem.get("sort_code").toString();
                    String sort_value = mapItem.get("sort_value").toString();
                    jbSort.put(sort_code, sort_value);
                }
            }

            //5.解析获取原始数据
            JSONObject jbCcdResolveResult = opArrayFuncOfYN.ResolveCcdResult(user_name, jbPlan, jbRecipe, jbSort, ccd_data);
            Map<String, Object> mapRowArray = (Map<String, Object>) jbCcdResolveResult.get("array");
            List<Map<String, Object>> bdRowsList = (List<Map<String, Object>>) jbCcdResolveResult.get("bd");
            mongoTemplate.insert(mapRowArray, meArrayTable);
            if (bdRowsList != null && bdRowsList.size() > 0)
            {
                mongoTemplate.insert(bdRowsList, meBdTable);
            }

            //6.更新订单完工数量
            String array_status = mapRowArray.get("array_status").toString();
            if (array_status.equals("OK"))
            {
                finish_ok_count = finish_ok_count + 1;
            }
            else
            {
                finish_ng_count = finish_ng_count + 1;
            }
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            Update updateBigData = new Update();
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //计时结束
            long l2 = System.currentTimeMillis();
            long cost_time = l2 - l1;

            //7.返回参数
            JSONObject jbResult = new JSONObject();
            jbResult.put("array_id", mapRowArray.get("array_id").toString());
            jbResult.put("array_barcode", mapRowArray.get("array_barcode").toString());
            jbResult.put("board_sn", mapRowArray.get("board_sn").toString());
            jbResult.put("deposit_position", Integer.parseInt(mapRowArray.get("deposit_position").toString()));
            jbResult.put("board_result", Integer.parseInt(mapRowArray.get("board_result").toString()));
            jbResult.put("board_turn", Integer.parseInt(mapRowArray.get("board_turn").toString()));
            jbResult.put("cost_time", cost_time + "ms");
            jbResult.put("xout_act_num", Integer.parseInt(mapRowArray.get("xout_act_num").toString()));
            jbResult.put("array_index", Integer.parseInt(mapRowArray.get("array_index").toString()));
            jbResult.put("array_level", mapRowArray.get("array_level").toString());
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("finish_ng_count", finish_ng_count);
            jbResult.put("array_ng_msg", mapRowArray.get("array_ng_msg").toString());
            jbResult.put("lot_num", lotNum);
            jbResult.put("plant_code", plantCode);
            jbResult.put("cycle_period", cyclePeriod);
            jbResult.put("array_bd_count", mapRowArray.get("array_bd_count").toString());
            // 新增"读取时长比对"规则 added by jay-y 2024/09/23
            jbResult.put("sort_rules_read_duration", jbSort.get(SortConst.READ_DURATION));
            result = jbResult.toString();

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        }
        catch (Exception ex)
        {
            ex.printStackTrace();
            errorMsg = "线扫判断与存储发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.更新上传标识
    @RequestMapping(value = "/UpdateBoardUpFlag", produces = "application/json;charset=utf-8", method = {
            RequestMethod.POST, RequestMethod.GET
    })
    public String UpdateBoardUpFlag(@RequestBody JSONObject jsonParas, HttpServletRequest request)
    {
        String apiRoutePath = "pack/project/tripod/op/UpdateBoardUpFlag";
        String transResult = "";
        String errorMsg = "";
        String meArrayTable = "a_pack_me_array";
        try
        {
            //1.获取参数
            String pileBarcode = jsonParas.getString("pile_barcode");
            String eapPileBarcode = jsonParas.getString("eap_pile_barcode");
            String arrayId = jsonParas.getString("array_id");
            Boolean isLast = jsonParas.getBoolean("is_last");

            //2.更新上传标识（根据打包记录）
            Query query = new Query();
            query.addCriteria(Criteria.where("pile_barcode").is(pileBarcode));
            if (!ObjectUtils.isEmpty(arrayId))
            {
                query.addCriteria(Criteria.where("array_id").is(arrayId));
            }
            query.addCriteria(Criteria.where("up_flag").is(BoardConst.FLAG_N));
            query.addCriteria(Criteria.where(Const.PROPERTY_ENABLE_FLAG).is(BoardConst.FLAG_Y));
            Update update = new Update();
            update.set("up_flag", BoardConst.FLAG_Y);
            mongoTemplate.updateMulti(query, update, meArrayTable);
            if (isLast)
            {
                //3.更新包记录（记录EAP打包条码）
                pileService.updateCustomBarcodeByPileBarcode(eapPileBarcode, pileBarcode);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        }
        catch (Exception ex)
        {
            errorMsg = "更新上传标识发生异常:" + ex.getMessage();
            log.error(errorMsg);
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.更新板件状态
    @RequestMapping(value = "/UpdateBoardStatus", produces = "application/json;charset=utf-8", method = {
            RequestMethod.POST, RequestMethod.GET
    })
    public String UpdateBoardStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request)
    {
        String apiRoutePath = "pack/project/tripod/op/UpdateBoardStatus";
        String transResult = "";
        String errorMsg = "";
        String meArrayTable = "a_pack_me_array";
        try
        {
            //1.获取参数
            String arrayId = jsonParas.getString("array_id");
            String arrayStatus = jsonParas.getString("array_status");
            String arrayNgMsg = jsonParas.getString("array_ng_msg");
            String inParas = jsonParas.getString("in_paras");

            //2.更新板件状态
            Query query = new Query();
            query.addCriteria(Criteria.where("array_id").is(arrayId));
            query.addCriteria(Criteria.where("enable_flag").is("Y"));
            Update update = new Update();
            update.set("array_status", arrayStatus);
            if (BoardConst.NG.equals(arrayStatus))
            {
                update.set("array_ng_code", -99);
                if (!ObjectUtils.isEmpty(arrayNgMsg))
                {
                    update.set("array_ng_msg", arrayNgMsg);
                }
            }
            else if (BoardConst.OK.equals(arrayStatus))
            {
                update.set("array_ng_code", 0);
                update.set("array_ng_msg", "");
            }
            if (inParas != null)
            {
                JSONObject jbInParas = JSONObject.parseObject(inParas);
                if (jbInParas != null && !jbInParas.isEmpty())
                {
                    for (String key : jbInParas.keySet())
                    {
                        update.set(key, jbInParas.get(key));
                    }
                }
            }
            mongoTemplate.updateFirst(query, update, meArrayTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        }
        catch (Exception ex)
        {
            errorMsg = "更新板件状态发生异常:" + ex.getMessage();
            log.error(errorMsg);
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.根据array_id解绑
    @RequestMapping(value = "/UnbindByArrayId", produces = "application/json;charset=utf-8", method = {
            RequestMethod.POST, RequestMethod.GET
    })
    public String UnbindByArrayId(@RequestBody JSONObject jsonParas, HttpServletRequest request)
    {
        String apiRoutePath = "pack/project/tripod/op/UnbindByArrayId";
        String transResult = "";
        String errorMsg = "";
        try
        {
            String stationCode = jsonParas.getString("station_code"); // 工站代码
            String arrayId = jsonParas.getString("array_id");
            SETBoard set = this.setBoardService.findOneByBoardIdAndBoardStatus(arrayId, BoardConst.OK);
            if (set == null || ObjectUtils.isEmpty(set.getBoardBarcode()))
            {
                errorMsg = "未找到有效板件[" + arrayId + "]";
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", errorMsg, 0);
                return transResult;
            }
            String boardBarcode = set.getBoardBarcode();
            this.setBoardService.unbindByBoardBarcode(boardBarcode);
            this.pcsBoardService.disableByParentBoardBarcode(boardBarcode);

            if (!ObjectUtils.isEmpty(set.getPileBarcode()))
            {
                long count = this.setBoardService.countAllByPileBarcodeAndBoardStatus(set.getPileBarcode(), BoardConst.OK, "pile_use_flag", BoardConst.FLAG_Y);
                // 当pile中无绑定中set时，更新a_pack_me_pile（包装打包记录表：主要unbind_flag=>Y、unbind相关信息）
                if (count <= 0)
                {
                    this.pileService.unbindByPileBarcode(stationCode, set.getPileBarcode());
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        }
        catch (Exception ex)
        {
            errorMsg = "解绑板件发生异常:" + ex.getMessage();
            log.error(errorMsg);
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
