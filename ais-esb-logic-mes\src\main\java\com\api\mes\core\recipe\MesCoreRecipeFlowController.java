package com.api.mes.core.recipe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Array;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <p>
 * 1.保存过站信息
 * 2.更新过站信息的离开时间
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-25
 */
@RestController
@Slf4j
@RequestMapping("/mes/core/recipe")
public class MesCoreRecipeFlowController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private MesCoreRecipeFunc mesCoreRecipeFunc;

    //保存过站信息
    @RequestMapping(value = "/MesCoreStationFlowSave", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreStationFlowSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreStationFlowSave";
        String transResult = "";
        String errorMsg = "";
        String station_code = "";
        String serial_num = "";
        String meOnlineFlowTable = "c_mes_me_online_flow";
        String meStationFlowTable = "c_mes_me_station_flow";
        String meStationRecipeTable = "c_mes_me_recipe_data";
        try {
            String now_date_str = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd");//获取当前日期,用于计算班次
            String create_date = CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(create_date);
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);

            String prod_line_code = jsonParas.getString("prod_line_code");
            station_code = jsonParas.getString("station_code");//工位号
            serial_num = jsonParas.getString("serial_num");//工件编号
            String container_num = jsonParas.getString("container_num");//托盘号
            if (container_num == null) container_num = "";
            String repair_flag = jsonParas.getString("repair_flag");//是否返修
            if (repair_flag == null || repair_flag.equals("")) repair_flag = "N";
            String location_num_str = jsonParas.getString("location_num");//位置号,针对一些特殊场景
            if (location_num_str == null || location_num_str.equals("")) location_num_str = "0";
            Integer location_num = Integer.parseInt(location_num_str);
            String arrive_date = jsonParas.getString("arrive_date");//到达时间
            String leave_date = jsonParas.getString("leave_date");//离开时间
            Long cost_time = jsonParas.getLong("cost_time");//消耗时间
            String quality_sign = jsonParas.getString("quality_sign");//合格标志
            String check_online_flag = jsonParas.getString("check_online_flag");//是否检查线首数据
            if (leave_date == null) leave_date = "";
            if (cost_time == null) cost_time = 0L;
            if (quality_sign == null || quality_sign.equals("")) quality_sign = "OK";
            if (check_online_flag == null || check_online_flag.equals("")) check_online_flag = "Y";

            //1.根据工位号查询工位信息
            String sqlStation = "select sfs.station_des,sfs.online_flag,sfs.offline_flag,sfs.prod_line_id," +
                    "COALESCE(sfs.bg_proceduce_code,'') bg_proceduce_code," +
                    "COALESCE(sfs.bg_proceduce_des,'') bg_proceduce_des," +
                    "COALESCE(sfs.data_collect_type,'SCADA') data_collect_type," +
                    "COALESCE(sfs.product_type,'') product_type," +
                    "COALESCE(sfs.beat_times,0) beat_times " +
                    "from sys_fmod_station sfs inner join sys_fmod_prod_line sfpl " +
                    "on sfs.prod_line_id=sfpl.prod_line_id " +
                    "where sfpl.prod_line_code='" + prod_line_code + "' " +
                    "and sfs.station_code='" + station_code + "' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "当前工位号{" + station_code + "},保存工件编号{" + serial_num + "},未能根据工位号查找到工位基础数据,请先建模";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String prod_line_id = itemListStation.get(0).get("prod_line_id").toString();
            String station_des = itemListStation.get(0).get("station_des").toString();
            String online_flag = itemListStation.get(0).get("online_flag").toString();
            String offline_flag = itemListStation.get(0).get("offline_flag").toString();
            String bg_proceduce_code = itemListStation.get(0).get("bg_proceduce_code").toString();
            String bg_proceduce_des = itemListStation.get(0).get("bg_proceduce_des").toString();
            String data_collect_type = itemListStation.get(0).get("data_collect_type").toString();
            String product_type = itemListStation.get(0).get("product_type").toString();
            Integer beat_times = Integer.parseInt(itemListStation.get(0).get("beat_times").toString());
            String staff_id = "admin";
            String shif_code = "";
            String shif_des = "";
            String make_order = "";
            String product_batch = "";
            String mo_custom_code = "";
            String mo_custom_des = "";
            String small_model_type = "";
            Integer model_code = 0;
            String print_barcode = "";
            String online_date = CFuncUtilsSystem.GetNowDateTime("");
            Integer mo_plan_count = 0;
            Integer mo_finish_count = 0;
            String serial_status = "正常件";
            if (repair_flag.equals("Y")) serial_status = "返修件";
            //2.查询当前工位登入者
            String sqlLogin = "select COALESCE(user_code,'') user_code " +
                    "from sys_login " +
                    "where station_code='" + station_code + "' and out_flag='N' " +
                    "order by login_id desc LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListLogin = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlLogin,
                    false, request, apiRoutePath);
            if (itemListLogin != null && itemListLogin.size() > 0) {
                staff_id = itemListLogin.get(0).get("user_code").toString();
            }
            //3.查询当前班次
            String sqlShift = "select shift_code,shift_name,shift_start_time,shift_end_time " +
                    "from sys_fmod_shift " +
                    "where prod_line_id=" + prod_line_id + " and enable_flag='Y' " +
                    "order by shift_order";
            List<Map<String, Object>> itemListShift = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlShift,
                    false, request, apiRoutePath);
            if (itemListShift != null && itemListShift.size() > 0) {
                for (Map<String, Object> map : itemListShift) {
                    String shift_start_time = map.get("shift_start_time").toString();
                    String shift_end_time = map.get("shift_end_time").toString();
                    String shift_start_date_str = now_date_str + " " + shift_start_time;
                    String shift_end_date_str = now_date_str + " " + shift_end_time;
                    Date shift_start_date = CFuncUtilsSystem.GetMongoISODate(shift_start_date_str);
                    Date shift_end_date = CFuncUtilsSystem.GetMongoISODate(shift_end_date_str);
                    if (shift_start_date.compareTo(shift_end_date) > 0) {//开始时间在结束时间之前,代表跨班次
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(shift_end_date);
                        cal.add(Calendar.DATE, 1);
                        shift_end_date = cal.getTime();
                    }
                    if (item_date.compareTo(shift_start_date) >= 0 && item_date.compareTo(shift_end_date) <= 0) {
                        shif_code = map.get("shift_code").toString();
                        shif_des = map.get("shift_name").toString();
                        break;
                    }
                }
            }
            //4.查询线首数据
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meOnlineFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            SimpleDateFormat sdf1 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                online_date = sdf1.format(docItemBigData.getDate("item_date"));
                make_order = docItemBigData.getString("make_order");
                product_batch = docItemBigData.getString("product_batch");
                small_model_type = docItemBigData.getString("small_model_type");
                model_code = docItemBigData.getInteger("model_code");
                mo_custom_code = docItemBigData.getString("mo_custom_code");
                mo_custom_des = docItemBigData.getString("mo_custom_des");
                print_barcode = docItemBigData.getString("print_barcode");
                break;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            //判断线首是否存在数据
            if (check_online_flag.equals("Y")) {
                if (make_order == null || make_order.equals("")) {
                    errorMsg = "当前工位号{" + station_code + "},未能根据工件编号{" + serial_num + "}查找到线首数据,该工件未上线过";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }
            //查询订单计划数量
            String sqlMo = "select mo_plan_count " +
                    "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " +
                    "and make_order='" + make_order + "'";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo,
                    false, request, apiRoutePath);
            if (itemListMo != null && itemListMo.size() > 0) {
                mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
            }
            //查询过站订单数据量
            mo_finish_count = mesCoreRecipeFunc.GetStationMoFinishCount(prod_line_code,station_code,make_order);
            //5.先禁用之前过站信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            Update updateBigData = new Update();
            updateBigData.set("enable_flag", "N");
            mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);
            //6.禁用配方数据
            Boolean isUpdateRecipe = true;
            String recipeDataNoUpdateOps = cFuncDbSqlResolve.GetParameterValue("RecipeDataNoUpdateOps");
            if (recipeDataNoUpdateOps != null && !recipeDataNoUpdateOps.equals("")) {
                String[] lstOpsRecipe = recipeDataNoUpdateOps.split(",", -1);
                if (lstOpsRecipe != null && lstOpsRecipe.length > 0) {
                    if (Arrays.asList(lstOpsRecipe).contains(station_code)) isUpdateRecipe = false;
                }
            }
            if (isUpdateRecipe) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
                queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
                queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                updateBigData = new Update();
                updateBigData.set("enable_flag", "N");
                mongoTemplate.updateMulti(queryBigData, updateBigData, meStationRecipeTable);
            }
            //7.保存过站数据
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("prod_line_code", prod_line_code);
            mapBigDataRow.put("station_code", station_code);
            mapBigDataRow.put("station_des", station_des);
            mapBigDataRow.put("proceduce_code", bg_proceduce_code);
            mapBigDataRow.put("proceduce_des", bg_proceduce_des);
            mapBigDataRow.put("serial_type", product_type);
            mapBigDataRow.put("serial_num", serial_num);
            mapBigDataRow.put("container_num", container_num);
            mapBigDataRow.put("staff_id", staff_id);
            mapBigDataRow.put("make_order", make_order);
            mapBigDataRow.put("product_batch", product_batch);
            mapBigDataRow.put("mo_custom_code", mo_custom_code);
            mapBigDataRow.put("mo_custom_des", mo_custom_des);
            mapBigDataRow.put("small_model_type", small_model_type);
            mapBigDataRow.put("model_code", model_code);
            mapBigDataRow.put("beat_times", beat_times);
            mapBigDataRow.put("progm_num", 0);
            mapBigDataRow.put("quality_sign", quality_sign);
            mapBigDataRow.put("data_collect_way", data_collect_type);
            mapBigDataRow.put("location_num", location_num);
            mapBigDataRow.put("pass_way", "");
            mapBigDataRow.put("pass_by", "");
            mapBigDataRow.put("pass_date", "");
            mapBigDataRow.put("pass_remarks", "");
            mapBigDataRow.put("repair_flag", repair_flag);
            mapBigDataRow.put("repair_count", 0);
            mapBigDataRow.put("arrive_date", arrive_date);
            mapBigDataRow.put("leave_date", leave_date);
            mapBigDataRow.put("cost_time", cost_time);
            mapBigDataRow.put("shif_code", shif_code);
            mapBigDataRow.put("shif_des", shif_des);
            mapBigDataRow.put("print_barcode", print_barcode);
            mapBigDataRow.put("online_flag", online_flag);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("up_flag", "N");
            mapBigDataRow.put("up_ng_code", 0);
            mapBigDataRow.put("up_ng_msg", "");
            mapBigDataRow.put("enable_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //增加计数
            mesCoreRecipeFunc.AddStationMoFinishCount(prod_line_code,station_code,make_order,1);

            //保存MIS信息
            String sqlDeleteMis = "delete from c_mes_me_station_mis where station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlDeleteMis, false, request, apiRoutePath);
            long serial_stay_time = CFuncUtilsSystem.GetDiffMsTimes(online_date, CFuncUtilsSystem.GetNowDateTime(""));
            serial_stay_time = serial_stay_time / 1000;
            String sqlInsertMis = "insert into c_mes_me_station_mis " +
                    "(user_name,arrive_date,station_code,serial_num," +
                    "make_order,product_batch," +
                    "small_model_type,model_code,mo_plan_count,mo_finish_count," +
                    "serial_status,online_time,serial_stay_time,ideal_beats,actual_beats," +
                    "shift_code,shift_des) values " +
                    "('" + staff_id + "','" + CFuncUtilsSystem.GetNowDateTime("") + "','" + station_code + "','" + serial_num + "'," +
                    "'" + make_order + "','" + product_batch + "'," +
                    "'" + small_model_type + "'," + model_code + "," + mo_plan_count + "," + mo_finish_count + "," +
                    "'" + serial_status + "','" + online_date + "'," + serial_stay_time + "," + beat_times + "," + beat_times + "," +
                    "'" + shif_code + "','" + shif_des + "')";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertMis, false, request, apiRoutePath);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, station_flow_id, "", 0);
        } catch (Exception ex) {
            errorMsg = "当前工位号{" + station_code + "},保存工件编号{" + serial_num + "},过站信息发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //保存过站信息[针对无线首数据,订单数据来源于c_mes_aps_station_mo]
    @RequestMapping(value = "/MesCoreStationFlowSave2", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreStationFlowSave2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreStationFlowSave2";
        String transResult = "";
        String errorMsg = "";
        String station_code = "";
        String serial_num = "";
        String meStationFlowTable = "c_mes_me_station_flow";
        String meStationRecipeTable = "c_mes_me_recipe_data";
        try {
            String now_date_str = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd");//获取当前日期,用于计算班次
            String create_date = CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(create_date);
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);

            String prod_line_code = jsonParas.getString("prod_line_code");
            station_code = jsonParas.getString("station_code");//工位号
            serial_num = jsonParas.getString("serial_num");//工件编号
            String container_num = jsonParas.getString("container_num");//托盘号
            if (container_num == null) container_num = "";
            String repair_flag = jsonParas.getString("repair_flag");//是否返修
            if (repair_flag == null || repair_flag.equals("")) repair_flag = "N";
            String location_num_str = jsonParas.getString("location_num");//位置号,针对一些特殊场景
            if (location_num_str == null || location_num_str.equals("")) location_num_str = "0";
            Integer location_num = Integer.parseInt(location_num_str);
            String arrive_date = jsonParas.getString("arrive_date");//到达时间
            String quality_sign = jsonParas.getString("quality_sign");//合格标志
            if (quality_sign == null || quality_sign.equals("")) quality_sign = "OK";

            //1.根据工位号查询工位信息
            String sqlStation = "select sfs.station_des,sfs.online_flag,sfs.offline_flag,sfs.prod_line_id," +
                    "COALESCE(sfs.bg_proceduce_code,'') bg_proceduce_code," +
                    "COALESCE(sfs.bg_proceduce_des,'') bg_proceduce_des," +
                    "COALESCE(sfs.data_collect_type,'SCADA') data_collect_type," +
                    "COALESCE(sfs.product_type,'') product_type," +
                    "COALESCE(sfs.beat_times,0) beat_times," +
                    "COALESCE(sfs.online_station_code,'') online_station_code " +
                    "from sys_fmod_station sfs inner join sys_fmod_prod_line sfpl " +
                    "on sfs.prod_line_id=sfpl.prod_line_id " +
                    "where sfpl.prod_line_code='" + prod_line_code + "' " +
                    "and sfs.station_code='" + station_code + "' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "当前工位号{" + station_code + "},保存工件编号{" + serial_num + "},未能根据工位号查找到工位基础数据,请先建模";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String prod_line_id = itemListStation.get(0).get("prod_line_id").toString();
            String station_des = itemListStation.get(0).get("station_des").toString();
            String online_flag = itemListStation.get(0).get("online_flag").toString();
            String offline_flag = itemListStation.get(0).get("offline_flag").toString();
            String bg_proceduce_code = itemListStation.get(0).get("bg_proceduce_code").toString();
            String bg_proceduce_des = itemListStation.get(0).get("bg_proceduce_des").toString();
            String data_collect_type = itemListStation.get(0).get("data_collect_type").toString();
            String product_type = itemListStation.get(0).get("product_type").toString();
            Integer beat_times = Integer.parseInt(itemListStation.get(0).get("beat_times").toString());
            String online_station_code = itemListStation.get(0).get("online_station_code").toString();
            String staff_id = "admin";
            String shif_code = "";
            String shif_des = "";
            String make_order = "";
            String product_batch = "";
            String mo_custom_code = "";
            String mo_custom_des = "";
            String small_model_type = "";
            Integer model_code = 0;
            String print_barcode = "";
            String online_date = CFuncUtilsSystem.GetNowDateTime("");
            Integer mo_plan_count = 0;
            Integer mo_finish_count = 0;
            String serial_status = "正常件";
            if (repair_flag.equals("Y")) serial_status = "返修件";
            //2.查询当前工位登入者
            String sqlLogin = "select COALESCE(user_code,'') user_code " +
                    "from sys_login " +
                    "where station_code='" + station_code + "' and out_flag='N' " +
                    "order by login_id desc LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListLogin = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlLogin,
                    false, request, apiRoutePath);
            if (itemListLogin != null && itemListLogin.size() > 0) {
                staff_id = itemListLogin.get(0).get("user_code").toString();
            }
            //3.查询当前班次
            String sqlShift = "select shift_code,shift_name,shift_start_time,shift_end_time " +
                    "from sys_fmod_shift " +
                    "where prod_line_id=" + prod_line_id + " and enable_flag='Y' " +
                    "order by shift_order";
            List<Map<String, Object>> itemListShift = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlShift,
                    false, request, apiRoutePath);
            if (itemListShift != null && itemListShift.size() > 0) {
                for (Map<String, Object> map : itemListShift) {
                    String shift_start_time = map.get("shift_start_time").toString();
                    String shift_end_time = map.get("shift_end_time").toString();
                    String shift_start_date_str = now_date_str + " " + shift_start_time;
                    String shift_end_date_str = now_date_str + " " + shift_end_time;
                    Date shift_start_date = CFuncUtilsSystem.GetMongoISODate(shift_start_date_str);
                    Date shift_end_date = CFuncUtilsSystem.GetMongoISODate(shift_end_date_str);
                    if (shift_start_date.compareTo(shift_end_date) > 0) {//开始时间在结束时间之前,代表跨班次
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(shift_end_date);
                        cal.add(Calendar.DATE, 1);
                        shift_end_date = cal.getTime();
                    }
                    if (item_date.compareTo(shift_start_date) >= 0 && item_date.compareTo(shift_end_date) <= 0) {
                        shif_code = map.get("shift_code").toString();
                        shif_des = map.get("shift_name").toString();
                        break;
                    }
                }
            }
            //查询,但是不做强制校验
            String sqlMo = "select b.make_order," +
                    "COALESCE(b.product_batch,'') product_batch,b.small_model_type,c.model_code," +
                    "COALESCE(b.mo_custom_code,'') mo_custom_code," +
                    "COALESCE(b.mo_custom_des,'') mo_custom_des,b.mo_plan_count " +
                    "from c_mes_aps_station_mo a inner join c_mes_aps_plan_mo b " +
                    "on a.mo_id=b.mo_id " +
                    "inner join c_mes_fmod_small_model c  " +
                    "on b.prod_line_id=c.prod_line_id and b.small_model_type=c.small_model_type " +
                    "where a.station_code='" + online_station_code + "' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, false, request, apiRoutePath);
            online_date = CFuncUtilsSystem.GetNowDateTime("");
            if (itemListMo != null && itemListMo.size() > 0) {
                make_order = itemListMo.get(0).get("make_order").toString();
                product_batch = itemListMo.get(0).get("product_batch").toString();
                small_model_type = itemListMo.get(0).get("small_model_type").toString();
                model_code = Integer.parseInt(itemListMo.get(0).get("model_code").toString());
                mo_custom_code = itemListMo.get(0).get("mo_custom_code").toString();
                mo_custom_des = itemListMo.get(0).get("mo_custom_des").toString();
                print_barcode = "";
                mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
            }
            //查询过站订单数据量
            mo_finish_count = mesCoreRecipeFunc.GetStationMoFinishCount(prod_line_code,station_code,make_order);
            //5.先禁用之前过站信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            Update updateBigData = new Update();
            updateBigData.set("enable_flag", "N");
            mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);
            //6.禁用配方数据
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            updateBigData = new Update();
            updateBigData.set("enable_flag", "N");
            mongoTemplate.updateMulti(queryBigData, updateBigData, meStationRecipeTable);
            //7.保存过站数据
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("prod_line_code", prod_line_code);
            mapBigDataRow.put("station_code", station_code);
            mapBigDataRow.put("station_des", station_des);
            mapBigDataRow.put("proceduce_code", bg_proceduce_code);
            mapBigDataRow.put("proceduce_des", bg_proceduce_des);
            mapBigDataRow.put("serial_type", product_type);
            mapBigDataRow.put("serial_num", serial_num);
            mapBigDataRow.put("container_num", container_num);
            mapBigDataRow.put("staff_id", staff_id);
            mapBigDataRow.put("make_order", make_order);
            mapBigDataRow.put("product_batch", product_batch);
            mapBigDataRow.put("mo_custom_code", mo_custom_code);
            mapBigDataRow.put("mo_custom_des", mo_custom_des);
            mapBigDataRow.put("small_model_type", small_model_type);
            mapBigDataRow.put("model_code", model_code);
            mapBigDataRow.put("beat_times", beat_times);
            mapBigDataRow.put("progm_num", 0);
            mapBigDataRow.put("quality_sign", quality_sign);
            mapBigDataRow.put("data_collect_way", data_collect_type);
            mapBigDataRow.put("location_num", location_num);
            mapBigDataRow.put("pass_way", "");
            mapBigDataRow.put("pass_by", "");
            mapBigDataRow.put("pass_date", "");
            mapBigDataRow.put("pass_remarks", "");
            mapBigDataRow.put("repair_flag", repair_flag);
            mapBigDataRow.put("repair_count", 0);
            mapBigDataRow.put("arrive_date", arrive_date);
            mapBigDataRow.put("leave_date", "");
            mapBigDataRow.put("cost_time", (long) 0);
            mapBigDataRow.put("shif_code", shif_code);
            mapBigDataRow.put("shif_des", shif_des);
            mapBigDataRow.put("print_barcode", print_barcode);
            mapBigDataRow.put("online_flag", online_flag);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("up_flag", "N");
            mapBigDataRow.put("up_ng_code", 0);
            mapBigDataRow.put("up_ng_msg", "");
            mapBigDataRow.put("enable_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //增加计数
            mesCoreRecipeFunc.AddStationMoFinishCount(prod_line_code,station_code,make_order,1);

            //保存MIS信息
            String sqlDeleteMis = "delete from c_mes_me_station_mis where station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlDeleteMis, false, request, apiRoutePath);
            long serial_stay_time = CFuncUtilsSystem.GetDiffMsTimes(online_date, CFuncUtilsSystem.GetNowDateTime(""));
            serial_stay_time = serial_stay_time / 1000;
            String sqlInsertMis = "insert into c_mes_me_station_mis " +
                    "(user_name,arrive_date,station_code,serial_num," +
                    "make_order,product_batch," +
                    "small_model_type,model_code,mo_plan_count,mo_finish_count," +
                    "serial_status,online_time,serial_stay_time,ideal_beats,actual_beats," +
                    "shift_code,shift_des) values " +
                    "('" + staff_id + "','" + CFuncUtilsSystem.GetNowDateTime("") + "','" + station_code + "','" + serial_num + "'," +
                    "'" + make_order + "','" + product_batch + "'," +
                    "'" + small_model_type + "'," + model_code + "," + mo_plan_count + "," + mo_finish_count + "," +
                    "'" + serial_status + "','" + online_date + "'," + serial_stay_time + "," + beat_times + "," + beat_times + "," +
                    "'" + shif_code + "','" + shif_des + "')";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertMis, false, request, apiRoutePath);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, station_flow_id, "", 0);
        } catch (Exception ex) {
            errorMsg = "当前工位号{" + station_code + "},保存工件编号{" + serial_num + "},过站信息发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


    //保存过站信息[针对唐山/金寨国轩项目，电芯上线（一次有多个电芯）或替换电芯工位]
    @RequestMapping(value = "/MesCoreStationFlowSave3", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreStationFlowSave3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreStationFlowSave3";
        String transResult = "";
        String errorMsg = "";
        String station_code = "";
        String dx_barcode_list = "";
        String station_flow_id_list = "";
        String meStationFlowTable = "c_mes_me_station_flow";
        String meStationRecipeTable = "c_mes_me_recipe_data";
        try {
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
            String now_date_str = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd");//获取当前日期,用于计算班次
            String create_date = CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(create_date);
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);

            String prod_line_code = jsonParas.getString("prod_line_code");
            station_code = jsonParas.getString("station_code");//工位号
            dx_barcode_list = jsonParas.getString("dx_barcode_list");//工件编号列表
            String dx_barcode_result = jsonParas.getString("dx_barcode_result");//工件合格标识列表，1合格；其他不合格
            String container_num = jsonParas.getString("container_num");//托盘号
            if (container_num == null) container_num = "";
            String repair_flag = jsonParas.getString("repair_flag");//是否返修
            if (repair_flag == null || repair_flag.equals("")) repair_flag = "N";
            String location_num_str = jsonParas.getString("location_num");//位置号,针对一些特殊场景
            if (location_num_str == null || location_num_str.equals("")) location_num_str = "0";
            Integer location_num = Integer.parseInt(location_num_str);
            String arrive_date = jsonParas.getString("arrive_date");//到达时间

            //1.根据工位号查询工位信息
            String sqlStation = "select sfs.station_des,sfs.online_flag,sfs.offline_flag,sfs.prod_line_id," +
                    "COALESCE(sfs.bg_proceduce_code,'') bg_proceduce_code," +
                    "COALESCE(sfs.bg_proceduce_des,'') bg_proceduce_des," +
                    "COALESCE(sfs.data_collect_type,'SCADA') data_collect_type," +
                    "COALESCE(sfs.product_type,'') product_type," +
                    "COALESCE(sfs.beat_times,0) beat_times," +
                    "COALESCE(sfs.online_station_code,'') online_station_code " +
                    "from sys_fmod_station sfs inner join sys_fmod_prod_line sfpl " +
                    "on sfs.prod_line_id=sfpl.prod_line_id " +
                    "where sfpl.prod_line_code='" + prod_line_code + "' " +
                    "and sfs.station_code='" + station_code + "' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "当前工位号{" + station_code + "},未能根据工位号查找到工位基础数据,请先建模";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String prod_line_id = itemListStation.get(0).get("prod_line_id").toString();
            String station_des = itemListStation.get(0).get("station_des").toString();
            String online_flag = itemListStation.get(0).get("online_flag").toString();
            String offline_flag = itemListStation.get(0).get("offline_flag").toString();
            String bg_proceduce_code = itemListStation.get(0).get("bg_proceduce_code").toString();
            String bg_proceduce_des = itemListStation.get(0).get("bg_proceduce_des").toString();
            String data_collect_type = itemListStation.get(0).get("data_collect_type").toString();
            String product_type = itemListStation.get(0).get("product_type").toString();
            Integer beat_times = Integer.parseInt(itemListStation.get(0).get("beat_times").toString());
            String online_station_code = itemListStation.get(0).get("online_station_code").toString();
            String staff_id = "admin";
            String shif_code = "";
            String shif_des = "";
            String make_order = "";
            String product_batch = "";
            String mo_custom_code = "";
            String mo_custom_des = "";
            String small_model_type = "";
            Integer model_code = 0;
            String print_barcode = "";
            String online_date = CFuncUtilsSystem.GetNowDateTime("");
            Integer mo_plan_count = 0;
            Integer mo_finish_count = 0;
            String serial_status = "正常件";
            if (repair_flag.equals("Y")) serial_status = "返修件";
            //2.查询当前工位登入者
            String sqlLogin = "select COALESCE(user_code,'') user_code " +
                    "from sys_login " +
                    "where station_code='" + station_code + "' and out_flag='N' " +
                    "order by login_id desc LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListLogin = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlLogin,
                    false, request, apiRoutePath);
            if (itemListLogin != null && itemListLogin.size() > 0) {
                staff_id = itemListLogin.get(0).get("user_code").toString();
            }
            //3.查询当前班次
            String sqlShift = "select shift_code,shift_name,shift_start_time,shift_end_time " +
                    "from sys_fmod_shift " +
                    "where prod_line_id=" + prod_line_id + " and enable_flag='Y' " +
                    "order by shift_order";
            List<Map<String, Object>> itemListShift = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlShift,
                    false, request, apiRoutePath);
            if (itemListShift != null && itemListShift.size() > 0) {
                for (Map<String, Object> map : itemListShift) {
                    String shift_start_time = map.get("shift_start_time").toString();
                    String shift_end_time = map.get("shift_end_time").toString();
                    String shift_start_date_str = now_date_str + " " + shift_start_time;
                    String shift_end_date_str = now_date_str + " " + shift_end_time;
                    Date shift_start_date = CFuncUtilsSystem.GetMongoISODate(shift_start_date_str);
                    Date shift_end_date = CFuncUtilsSystem.GetMongoISODate(shift_end_date_str);
                    if (shift_start_date.compareTo(shift_end_date) > 0) {//开始时间在结束时间之前,代表跨班次
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(shift_end_date);
                        cal.add(Calendar.DATE, 1);
                        shift_end_date = cal.getTime();
                    }
                    if (item_date.compareTo(shift_start_date) >= 0 && item_date.compareTo(shift_end_date) <= 0) {
                        shif_code = map.get("shift_code").toString();
                        shif_des = map.get("shift_name").toString();
                        break;
                    }
                }
            }
            //查询,但是不做强制校验
            String sqlMo = "select b.make_order," +
                    "COALESCE(b.product_batch,'') product_batch,b.small_model_type,c.model_code," +
                    "COALESCE(b.mo_custom_code,'') mo_custom_code," +
                    "COALESCE(b.mo_custom_des,'') mo_custom_des,b.mo_plan_count " +
                    "from c_mes_aps_station_mo a inner join c_mes_aps_plan_mo b " +
                    "on a.mo_id=b.mo_id " +
                    "inner join c_mes_fmod_small_model c  " +
                    "on b.prod_line_id=c.prod_line_id and b.small_model_type=c.small_model_type " +
                    "where a.station_code='" + online_station_code + "' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo, false, request, apiRoutePath);
            online_date = CFuncUtilsSystem.GetNowDateTime("");
            if (itemListMo != null && itemListMo.size() > 0) {
                make_order = itemListMo.get(0).get("make_order").toString();
                product_batch = itemListMo.get(0).get("product_batch").toString();
                small_model_type = itemListMo.get(0).get("small_model_type").toString();
                model_code = Integer.parseInt(itemListMo.get(0).get("model_code").toString());
                mo_custom_code = itemListMo.get(0).get("mo_custom_code").toString();
                mo_custom_des = itemListMo.get(0).get("mo_custom_des").toString();
                print_barcode = "";
                mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
            }
            //查询过站订单数据量
            Query queryBigData = new Query();
            mo_finish_count = mesCoreRecipeFunc.GetStationMoFinishCount(prod_line_code,station_code,make_order);
            String[] serial_num_list = dx_barcode_list.split(",");
            String[] quality_sign_list = dx_barcode_result.split(",");
            for (int i = 0; i < serial_num_list.length; i++) {
                String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
                if (station_flow_id_list.equals("")) station_flow_id_list = station_flow_id;
                else station_flow_id_list = station_flow_id_list + "," + station_flow_id;
                String serial_num = serial_num_list[i];
                String barcode_result = quality_sign_list[i];
                String quality_sign = "OK";
                if (!barcode_result.equals("1")) quality_sign = "NG";
                //5.先禁用之前过站信息
                Update updateBigData = new Update();

                //判断  是否是唐山国轩   并且是OP1051(1线人工替换电芯) 或者是OP1052(2线人工替换电芯) 不进行更新，直接插入数据，其他工位逻辑不变
                if(!("TSGX".equals(projectCode) && ("OP1051".equals(station_code) || "OP1052".equals(station_code)))) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                    queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
                    queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
                    queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                    updateBigData.set("enable_flag", "N");
                    mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);
                }
                //6.禁用配方数据
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
                queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
                queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                updateBigData = new Update();
                updateBigData.set("enable_flag", "N");
                mongoTemplate.updateMulti(queryBigData, updateBigData, meStationRecipeTable);
                //7.保存过站数据
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("station_flow_id", station_flow_id);
                mapBigDataRow.put("prod_line_code", prod_line_code);
                mapBigDataRow.put("station_code", station_code);
                mapBigDataRow.put("station_des", station_des);
                mapBigDataRow.put("proceduce_code", bg_proceduce_code);
                mapBigDataRow.put("proceduce_des", bg_proceduce_des);
                mapBigDataRow.put("serial_type", product_type);
                mapBigDataRow.put("serial_num", serial_num);
                mapBigDataRow.put("container_num", container_num);
                mapBigDataRow.put("staff_id", staff_id);
                mapBigDataRow.put("make_order", make_order);
                mapBigDataRow.put("product_batch", product_batch);
                mapBigDataRow.put("mo_custom_code", mo_custom_code);
                mapBigDataRow.put("mo_custom_des", mo_custom_des);
                mapBigDataRow.put("small_model_type", small_model_type);
                mapBigDataRow.put("model_code", model_code);
                mapBigDataRow.put("beat_times", beat_times);
                mapBigDataRow.put("progm_num", 0);
                mapBigDataRow.put("quality_sign", quality_sign);
                mapBigDataRow.put("data_collect_way", data_collect_type);
                mapBigDataRow.put("location_num", location_num);
                mapBigDataRow.put("pass_way", "");
                mapBigDataRow.put("pass_by", "");
                mapBigDataRow.put("pass_date", "");
                mapBigDataRow.put("pass_remarks", "");
                mapBigDataRow.put("repair_flag", repair_flag);
                mapBigDataRow.put("repair_count", 0);
                mapBigDataRow.put("arrive_date", arrive_date);
                mapBigDataRow.put("leave_date", "");
                mapBigDataRow.put("cost_time", (long) 0);
                mapBigDataRow.put("shif_code", shif_code);
                mapBigDataRow.put("shif_des", shif_des);
                mapBigDataRow.put("print_barcode", print_barcode);
                mapBigDataRow.put("online_flag", online_flag);
                mapBigDataRow.put("offline_flag", offline_flag);
                mapBigDataRow.put("up_flag", "N");
                mapBigDataRow.put("up_ng_code", 0);
                mapBigDataRow.put("up_ng_msg", "");
                mapBigDataRow.put("enable_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            }
            //增加计数
            if(serial_num_list!=null || serial_num_list.length>0){
                mesCoreRecipeFunc.AddStationMoFinishCount(prod_line_code,station_code,make_order,serial_num_list.length);
            }

            //保存MIS信息
            String sqlDeleteMis = "delete from c_mes_me_station_mis where station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlDeleteMis, false, request, apiRoutePath);
            long serial_stay_time = CFuncUtilsSystem.GetDiffMsTimes(online_date, CFuncUtilsSystem.GetNowDateTime(""));
            serial_stay_time = serial_stay_time / 1000;
            String sqlInsertMis = "insert into c_mes_me_station_mis " +
                    "(user_name,arrive_date,station_code,serial_num," +
                    "make_order,product_batch," +
                    "small_model_type,model_code,mo_plan_count,mo_finish_count," +
                    "serial_status,online_time,serial_stay_time,ideal_beats,actual_beats," +
                    "shift_code,shift_des) values " +
                    "('" + staff_id + "','" + CFuncUtilsSystem.GetNowDateTime("") + "','" + station_code + "','" + serial_num_list[0] + "'," +
                    "'" + make_order + "','" + product_batch + "'," +
                    "'" + small_model_type + "'," + model_code + "," + mo_plan_count + "," + mo_finish_count + "," +
                    "'" + serial_status + "','" + online_date + "'," + serial_stay_time + "," + beat_times + "," + beat_times + "," +
                    "'" + shif_code + "','" + shif_des + "')";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertMis, false, request, apiRoutePath);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, station_flow_id_list, "", 0);
        } catch (Exception ex) {
            errorMsg = "当前工位号{" + station_code + "},保存工件编号{" + dx_barcode_list + "},过站信息发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
    //保存过站信息
    @RequestMapping(value = "/MesCoreStationFlowSave4", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreStationFlowSave4(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreStationFlowSave";
        String transResult = "";
        String errorMsg = "";
        String station_code = "";
        String serial_num = "";
        String meStationFlowTable = "c_mes_me_station_flow";
        String meStationRecipeTable = "c_mes_me_recipe_data";
        try {
            String now_date_str = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd");//获取当前日期,用于计算班次
            String create_date = CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(create_date);
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);

            String prod_line_code = jsonParas.getString("prod_line_code");
            station_code = jsonParas.getString("station_code");//工位号
            serial_num = jsonParas.getString("serial_num");//工件编号
            String container_num = jsonParas.getString("container_num");//托盘号
            if (container_num == null) container_num = "";
            String repair_flag = jsonParas.getString("repair_flag");//是否返修
            if (repair_flag == null || repair_flag.equals("")) repair_flag = "N";
            String location_num_str = jsonParas.getString("location_num");//位置号,针对一些特殊场景
            if (location_num_str == null || location_num_str.equals("")) location_num_str = "0";
            Integer location_num = Integer.parseInt(location_num_str);
            String arrive_date = jsonParas.getString("arrive_date");//到达时间
            String leave_date = jsonParas.getString("leave_date");//离开时间
            Long cost_time = jsonParas.getLong("cost_time");//消耗时间
            String quality_sign = jsonParas.getString("quality_sign");//合格标志
            String check_online_flag = jsonParas.getString("check_online_flag");//是否检查线首数据
            if (leave_date == null) leave_date = "";
            if (cost_time == null) cost_time = 0L;
            if (quality_sign == null || quality_sign.equals("")) quality_sign = "OK";
            if (check_online_flag == null || check_online_flag.equals("")) check_online_flag = "Y";

            //1.根据工位号查询工位信息
            String sqlStation = "select sfs.station_des,sfs.online_flag,sfs.offline_flag,sfs.prod_line_id," +
                    "COALESCE(sfs.bg_proceduce_code,'') bg_proceduce_code," +
                    "COALESCE(sfs.bg_proceduce_des,'') bg_proceduce_des," +
                    "COALESCE(sfs.data_collect_type,'SCADA') data_collect_type," +
                    "COALESCE(sfs.product_type,'') product_type," +
                    "COALESCE(sfs.beat_times,0) beat_times " +
                    "from sys_fmod_station sfs inner join sys_fmod_prod_line sfpl " +
                    "on sfs.prod_line_id=sfpl.prod_line_id " +
                    "where sfpl.prod_line_code='" + prod_line_code + "' " +
                    "and sfs.station_code='" + station_code + "' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "当前工位号{" + station_code + "},保存工件编号{" + serial_num + "},未能根据工位号查找到工位基础数据,请先建模";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String prod_line_id = itemListStation.get(0).get("prod_line_id").toString();
            String station_des = itemListStation.get(0).get("station_des").toString();
            String online_flag = itemListStation.get(0).get("online_flag").toString();
            String offline_flag = itemListStation.get(0).get("offline_flag").toString();
            String bg_proceduce_code = itemListStation.get(0).get("bg_proceduce_code").toString();
            String bg_proceduce_des = itemListStation.get(0).get("bg_proceduce_des").toString();
            String data_collect_type = itemListStation.get(0).get("data_collect_type").toString();
            String product_type = itemListStation.get(0).get("product_type").toString();
            Integer beat_times = Integer.parseInt(itemListStation.get(0).get("beat_times").toString());
            String staff_id = "admin";
            String shif_code = "";
            String shif_des = "";
            String make_order = jsonParas.getString("make_order");
            String product_batch = jsonParas.getString("product_batch");
            String mo_custom_code = jsonParas.getString("mo_custom_code");
            String mo_custom_des = jsonParas.getString("mo_custom_des");
            String small_model_type = jsonParas.getString("small_model_type");
            Integer model_code = Integer.parseInt(jsonParas.getString("model_code"));
            String print_barcode = "";
            String online_date = CFuncUtilsSystem.GetNowDateTime("");
            Integer mo_plan_count = 0;
            Integer mo_finish_count = 0;
            String serial_status = "正常件";
            if (repair_flag.equals("Y")) serial_status = "返修件";
            //2.查询当前工位登入者
            String sqlLogin = "select COALESCE(user_code,'') user_code " +
                    "from sys_login " +
                    "where station_code='" + station_code + "' and out_flag='N' " +
                    "order by login_id desc LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListLogin = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlLogin,
                    false, request, apiRoutePath);
            if (itemListLogin != null && itemListLogin.size() > 0) {
                staff_id = itemListLogin.get(0).get("user_code").toString();
            }
            //3.查询当前班次
            String sqlShift = "select shift_code,shift_name,shift_start_time,shift_end_time " +
                    "from sys_fmod_shift " +
                    "where prod_line_id=" + prod_line_id + " and enable_flag='Y' " +
                    "order by shift_order";
            List<Map<String, Object>> itemListShift = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlShift,
                    false, request, apiRoutePath);
            if (itemListShift != null && itemListShift.size() > 0) {
                for (Map<String, Object> map : itemListShift) {
                    String shift_start_time = map.get("shift_start_time").toString();
                    String shift_end_time = map.get("shift_end_time").toString();
                    String shift_start_date_str = now_date_str + " " + shift_start_time;
                    String shift_end_date_str = now_date_str + " " + shift_end_time;
                    Date shift_start_date = CFuncUtilsSystem.GetMongoISODate(shift_start_date_str);
                    Date shift_end_date = CFuncUtilsSystem.GetMongoISODate(shift_end_date_str);
                    if (shift_start_date.compareTo(shift_end_date) > 0) {//开始时间在结束时间之前,代表跨班次
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(shift_end_date);
                        cal.add(Calendar.DATE, 1);
                        shift_end_date = cal.getTime();
                    }
                    if (item_date.compareTo(shift_start_date) >= 0 && item_date.compareTo(shift_end_date) <= 0) {
                        shif_code = map.get("shift_code").toString();
                        shif_des = map.get("shift_name").toString();
                        break;
                    }
                }
            }
            Query queryBigData = new Query();
            //判断线首是否存在数据
            if (check_online_flag.equals("Y")) {
                if (make_order == null || make_order.equals("")) {
                    errorMsg = "当前工位号{" + station_code + "},未能根据工件编号{" + serial_num + "}查找到线首数据,该工件未上线过";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }
            //查询订单计划数量
            String sqlMo = "select mo_plan_count " +
                    "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " +
                    "and make_order='" + make_order + "'";
            List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo,
                    false, request, apiRoutePath);
            if (itemListMo != null && itemListMo.size() > 0) {
                mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
            }
            //查询过站订单数据量
            mo_finish_count = mesCoreRecipeFunc.GetStationMoFinishCount(prod_line_code,station_code,make_order);
            //5.先禁用之前过站信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            Update updateBigData = new Update();
            updateBigData.set("enable_flag", "N");
            mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);
            //6.禁用配方数据
            Boolean isUpdateRecipe = true;
            String recipeDataNoUpdateOps = cFuncDbSqlResolve.GetParameterValue("RecipeDataNoUpdateOps");
            if (recipeDataNoUpdateOps != null && !recipeDataNoUpdateOps.equals("")) {
                String[] lstOpsRecipe = recipeDataNoUpdateOps.split(",", -1);
                if (lstOpsRecipe != null && lstOpsRecipe.length > 0) {
                    if (Arrays.asList(lstOpsRecipe).contains(station_code)) isUpdateRecipe = false;
                }
            }
            if (isUpdateRecipe) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
                queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
                queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                updateBigData = new Update();
                updateBigData.set("enable_flag", "N");
                mongoTemplate.updateMulti(queryBigData, updateBigData, meStationRecipeTable);
            }
            //7.保存过站数据
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("prod_line_code", prod_line_code);
            mapBigDataRow.put("station_code", station_code);
            mapBigDataRow.put("station_des", station_des);
            mapBigDataRow.put("proceduce_code", bg_proceduce_code);
            mapBigDataRow.put("proceduce_des", bg_proceduce_des);
            mapBigDataRow.put("serial_type", product_type);
            mapBigDataRow.put("serial_num", serial_num);
            mapBigDataRow.put("container_num", container_num);
            mapBigDataRow.put("staff_id", staff_id);
            mapBigDataRow.put("make_order", make_order);
            mapBigDataRow.put("product_batch", product_batch);
            mapBigDataRow.put("mo_custom_code", mo_custom_code);
            mapBigDataRow.put("mo_custom_des", mo_custom_des);
            mapBigDataRow.put("small_model_type", small_model_type);
            mapBigDataRow.put("model_code", model_code);
            mapBigDataRow.put("beat_times", beat_times);
            mapBigDataRow.put("progm_num", 0);
            mapBigDataRow.put("quality_sign", quality_sign);
            mapBigDataRow.put("data_collect_way", data_collect_type);
            mapBigDataRow.put("location_num", location_num);
            mapBigDataRow.put("pass_way", "");
            mapBigDataRow.put("pass_by", "");
            mapBigDataRow.put("pass_date", "");
            mapBigDataRow.put("pass_remarks", "");
            mapBigDataRow.put("repair_flag", repair_flag);
            mapBigDataRow.put("repair_count", 0);
            mapBigDataRow.put("arrive_date", arrive_date);
            mapBigDataRow.put("leave_date", leave_date);
            mapBigDataRow.put("cost_time", cost_time);
            mapBigDataRow.put("shif_code", shif_code);
            mapBigDataRow.put("shif_des", shif_des);
            mapBigDataRow.put("print_barcode", print_barcode);
            mapBigDataRow.put("online_flag", online_flag);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("up_flag", "N");
            mapBigDataRow.put("up_ng_code", 0);
            mapBigDataRow.put("up_ng_msg", "");
            mapBigDataRow.put("enable_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //增加计数
            mesCoreRecipeFunc.AddStationMoFinishCount(prod_line_code,station_code,make_order,1);

            //保存MIS信息
            String sqlDeleteMis = "delete from c_mes_me_station_mis where station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlDeleteMis, false, request, apiRoutePath);
            long serial_stay_time = CFuncUtilsSystem.GetDiffMsTimes(online_date, CFuncUtilsSystem.GetNowDateTime(""));
            serial_stay_time = serial_stay_time / 1000;
            String sqlInsertMis = "insert into c_mes_me_station_mis " +
                    "(user_name,arrive_date,station_code,serial_num," +
                    "make_order,product_batch," +
                    "small_model_type,model_code,mo_plan_count,mo_finish_count," +
                    "serial_status,online_time,serial_stay_time,ideal_beats,actual_beats," +
                    "shift_code,shift_des) values " +
                    "('" + staff_id + "','" + CFuncUtilsSystem.GetNowDateTime("") + "','" + station_code + "','" + serial_num + "'," +
                    "'" + make_order + "','" + product_batch + "'," +
                    "'" + small_model_type + "'," + model_code + "," + mo_plan_count + "," + mo_finish_count + "," +
                    "'" + serial_status + "','" + online_date + "'," + 0 + "," + beat_times + "," + beat_times + "," +
                    "'" + shif_code + "','" + shif_des + "')";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertMis, false, request, apiRoutePath);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, station_flow_id, "", 0);
        } catch (Exception ex) {
            errorMsg = "当前工位号{" + station_code + "},保存工件编号{" + serial_num + "},过站信息发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
    //保存过站信息
    @RequestMapping(value = "/MesCoreStationFlowSave05", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreStationFlowSave5(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreStationFlowSave05";
        String transResult = "";
        String errorMsg = "";
        String station_code = "";
        String serial_nums = "";
        String on_line_flow = "";
        String meStationFlowTable = "c_mes_me_station_flow";
        String meStationRecipeTable = "c_mes_me_recipe_data";
        try {
            String now_date_str = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd");//获取当前日期,用于计算班次
            String create_date = CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(create_date);
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_ids = "";

            String prod_line_code = jsonParas.getString("prod_line_code");
            station_code = jsonParas.getString("station_code");//工位号
            serial_nums = jsonParas.getString("serial_num");//工件编号
            on_line_flow = jsonParas.getString("on_line_flow");//工件编号
            String container_num = jsonParas.getString("container_num");//托盘号
            if (container_num == null) container_num = "";
            String repair_flag = jsonParas.getString("repair_flag");//是否返修
            if (repair_flag == null || repair_flag.equals("")) repair_flag = "N";
            String location_num_str = jsonParas.getString("location_num");//位置号,针对一些特殊场景
            if (location_num_str == null || location_num_str.equals("")) location_num_str = "0";
            Integer location_num = Integer.parseInt(location_num_str);
            String arrive_date = jsonParas.getString("arrive_date");//到达时间
            String leave_date = jsonParas.getString("leave_date");//离开时间
            Long cost_time = jsonParas.getLong("cost_time");//消耗时间
            String quality_sign = jsonParas.getString("quality_sign");//合格标志
            String check_online_flag = jsonParas.getString("check_online_flag");//是否检查线首数据
            if (leave_date == null) leave_date = "";
            if (cost_time == null) cost_time = 0L;
            if (quality_sign == null || quality_sign.equals("")) quality_sign = "OK";
            if (check_online_flag == null || check_online_flag.equals("")) check_online_flag = "Y";

            //1.根据工位号查询工位信息
            String sqlStation = "select sfs.station_des,sfs.online_flag,sfs.offline_flag,sfs.prod_line_id," +
                    "COALESCE(sfs.bg_proceduce_code,'') bg_proceduce_code," +
                    "COALESCE(sfs.bg_proceduce_des,'') bg_proceduce_des," +
                    "COALESCE(sfs.data_collect_type,'SCADA') data_collect_type," +
                    "COALESCE(sfs.product_type,'') product_type," +
                    "COALESCE(sfs.beat_times,0) beat_times " +
                    "from sys_fmod_station sfs inner join sys_fmod_prod_line sfpl " +
                    "on sfs.prod_line_id=sfpl.prod_line_id " +
                    "where sfpl.prod_line_code='" + prod_line_code + "' " +
                    "and sfs.station_code='" + station_code + "' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "当前工位号{" + station_code + "},保存工件编号{" + serial_nums + "},未能根据工位号查找到工位基础数据,请先建模";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String prod_line_id = itemListStation.get(0).get("prod_line_id").toString();
            String station_des = itemListStation.get(0).get("station_des").toString();
            String online_flag = itemListStation.get(0).get("online_flag").toString();
            String offline_flag = itemListStation.get(0).get("offline_flag").toString();
            String bg_proceduce_code = itemListStation.get(0).get("bg_proceduce_code").toString();
            String bg_proceduce_des = itemListStation.get(0).get("bg_proceduce_des").toString();
            String data_collect_type = itemListStation.get(0).get("data_collect_type").toString();
            String product_type = itemListStation.get(0).get("product_type").toString();
            Integer beat_times = Integer.parseInt(itemListStation.get(0).get("beat_times").toString());
            String staff_id = "admin";
            String shif_code = "";
            String shif_des = "";
            String make_order = "";
            String product_batch = "";
            String mo_custom_code = "";
            String mo_custom_des = "";
            String small_model_type = "";
            Integer model_code = 0;
            String print_barcode = "";
            String online_date = CFuncUtilsSystem.GetNowDateTime("");
            Integer mo_plan_count = 0;
            Integer mo_finish_count = 0;
            String serial_status = "正常件";
            if (repair_flag.equals("Y")) serial_status = "返修件";
            //2.查询当前工位登入者
            String sqlLogin = "select COALESCE(user_code,'') user_code " +
                    "from sys_login " +
                    "where station_code='" + station_code + "' and out_flag='N' " +
                    "order by login_id desc LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListLogin = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlLogin,
                    false, request, apiRoutePath);
            if (itemListLogin != null && itemListLogin.size() > 0) {
                staff_id = itemListLogin.get(0).get("user_code").toString();
            }
            //3.查询当前班次
            String sqlShift = "select shift_code,shift_name,shift_start_time,shift_end_time " +
                    "from sys_fmod_shift " +
                    "where prod_line_id=" + prod_line_id + " and enable_flag='Y' " +
                    "order by shift_order";
            List<Map<String, Object>> itemListShift = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlShift,
                    false, request, apiRoutePath);
            if (itemListShift != null && itemListShift.size() > 0) {
                for (Map<String, Object> map : itemListShift) {
                    String shift_start_time = map.get("shift_start_time").toString();
                    String shift_end_time = map.get("shift_end_time").toString();
                    String shift_start_date_str = now_date_str + " " + shift_start_time;
                    String shift_end_date_str = now_date_str + " " + shift_end_time;
                    Date shift_start_date = CFuncUtilsSystem.GetMongoISODate(shift_start_date_str);
                    Date shift_end_date = CFuncUtilsSystem.GetMongoISODate(shift_end_date_str);
                    if (shift_start_date.compareTo(shift_end_date) > 0) {//开始时间在结束时间之前,代表跨班次
                        Calendar cal = Calendar.getInstance();
                        cal.setTime(shift_end_date);
                        cal.add(Calendar.DATE, 1);
                        shift_end_date = cal.getTime();
                    }
                    if (item_date.compareTo(shift_start_date) >= 0 && item_date.compareTo(shift_end_date) <= 0) {
                        shif_code = map.get("shift_code").toString();
                        shif_des = map.get("shift_name").toString();
                        break;
                    }
                }
            }
            //5.先禁用之前过站信息
            JSONArray objects = JSONArray.parseArray(on_line_flow);
            for (int i = 0; i < objects.size(); i++) {
                JSONObject jsonObject = objects.getJSONObject(i);
                make_order = jsonObject.getString("make_order");
                product_batch = jsonObject.getString("product_batch");
                mo_custom_code = jsonObject.getString("mo_custom_code");
                mo_custom_des = jsonObject.getString("mo_custom_des");
                small_model_type = jsonObject.getString("small_model_type");
                model_code = Integer.parseInt(jsonObject.getString("model_code"));
                String serial_num = jsonObject.getString("serial_num");
                Query queryBigData = new Query();
                //判断线首是否存在数据
                if (check_online_flag.equals("Y")) {
                    if (make_order == null || make_order.equals("")) {
                        errorMsg = "当前工位号{" + station_code + "},未能根据工件编号{" + serial_num + "}查找到线首数据,该工件未上线过";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
                //查询订单计划数量
                String sqlMo = "select mo_plan_count " +
                        "from c_mes_aps_plan_mo where prod_line_id=" + prod_line_id + " " +
                        "and make_order='" + make_order + "'";
                List<Map<String, Object>> itemListMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMo,
                        false, request, apiRoutePath);
                if (itemListMo != null && itemListMo.size() > 0) {
                    mo_plan_count = Integer.parseInt(itemListMo.get(0).get("mo_plan_count").toString());
                }
                //查询过站订单数据量
                mo_finish_count = mesCoreRecipeFunc.GetStationMoFinishCount(prod_line_code,station_code,make_order);
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
                queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
                queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                Update updateBigData = new Update();
                updateBigData.set("enable_flag", "N");
                mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);
                //6.禁用配方数据
                Boolean isUpdateRecipe = true;
                String recipeDataNoUpdateOps = cFuncDbSqlResolve.GetParameterValue("RecipeDataNoUpdateOps");
                if (recipeDataNoUpdateOps != null && !recipeDataNoUpdateOps.equals("")) {
                    String[] lstOpsRecipe = recipeDataNoUpdateOps.split(",", -1);
                    if (lstOpsRecipe != null && lstOpsRecipe.length > 0) {
                        if (Arrays.asList(lstOpsRecipe).contains(station_code)) isUpdateRecipe = false;
                    }
                }
                if (isUpdateRecipe) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                    queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
                    queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
                    queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                    updateBigData = new Update();
                    updateBigData.set("enable_flag", "N");
                    mongoTemplate.updateMulti(queryBigData, updateBigData, meStationRecipeTable);
                }
               String  station_flow_id= CFuncUtilsSystem.CreateUUID(true);
                station_flow_ids+= StringUtils.isEmpty(station_flow_ids)?station_flow_id:","+station_flow_id;
                //7.保存过站数据
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("station_flow_id", station_flow_id);
                mapBigDataRow.put("prod_line_code", prod_line_code);
                mapBigDataRow.put("station_code", station_code);
                mapBigDataRow.put("station_des", station_des);
                mapBigDataRow.put("proceduce_code", bg_proceduce_code);
                mapBigDataRow.put("proceduce_des", bg_proceduce_des);
                mapBigDataRow.put("serial_type", product_type);
                mapBigDataRow.put("serial_num", serial_num);
                mapBigDataRow.put("container_num", container_num);
                mapBigDataRow.put("staff_id", staff_id);
                mapBigDataRow.put("make_order", make_order);
                mapBigDataRow.put("product_batch", product_batch);
                mapBigDataRow.put("mo_custom_code", mo_custom_code);
                mapBigDataRow.put("mo_custom_des", mo_custom_des);
                mapBigDataRow.put("small_model_type", small_model_type);
                mapBigDataRow.put("model_code", model_code);
                mapBigDataRow.put("beat_times", beat_times);
                mapBigDataRow.put("progm_num", 0);
                mapBigDataRow.put("quality_sign", quality_sign);
                mapBigDataRow.put("data_collect_way", data_collect_type);
                mapBigDataRow.put("location_num", location_num);
                mapBigDataRow.put("pass_way", "");
                mapBigDataRow.put("pass_by", "");
                mapBigDataRow.put("pass_date", "");
                mapBigDataRow.put("pass_remarks", "");
                mapBigDataRow.put("repair_flag", repair_flag);
                mapBigDataRow.put("repair_count", 0);
                mapBigDataRow.put("arrive_date", arrive_date);
                mapBigDataRow.put("leave_date", leave_date);
                mapBigDataRow.put("cost_time", cost_time);
                mapBigDataRow.put("shif_code", shif_code);
                mapBigDataRow.put("shif_des", shif_des);
                mapBigDataRow.put("print_barcode", print_barcode);
                mapBigDataRow.put("online_flag", online_flag);
                mapBigDataRow.put("offline_flag", offline_flag);
                mapBigDataRow.put("up_flag", "N");
                mapBigDataRow.put("up_ng_code", 0);
                mapBigDataRow.put("up_ng_msg", "");
                mapBigDataRow.put("enable_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                //增加计数
                mesCoreRecipeFunc.AddStationMoFinishCount(prod_line_code,station_code,make_order,1);

                //保存MIS信息
                String sqlDeleteMis = "delete from c_mes_me_station_mis where station_code='" + station_code + "'";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlDeleteMis, false, request, apiRoutePath);
                long serial_stay_time = CFuncUtilsSystem.GetDiffMsTimes(online_date, CFuncUtilsSystem.GetNowDateTime(""));
                serial_stay_time = serial_stay_time / 1000;
                String sqlInsertMis = "insert into c_mes_me_station_mis " +
                        "(user_name,arrive_date,station_code,serial_num," +
                        "make_order,product_batch," +
                        "small_model_type,model_code,mo_plan_count,mo_finish_count," +
                        "serial_status,online_time,serial_stay_time,ideal_beats,actual_beats," +
                        "shift_code,shift_des) values " +
                        "('" + staff_id + "','" + CFuncUtilsSystem.GetNowDateTime("") + "','" + station_code + "','" + serial_num + "'," +
                        "'" + make_order + "','" + product_batch + "'," +
                        "'" + small_model_type + "'," + model_code + "," + mo_plan_count + "," + mo_finish_count + "," +
                        "'" + serial_status + "','" + online_date + "'," + 0 + "," + beat_times + "," + beat_times + "," +
                        "'" + shif_code + "','" + shif_des + "')";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsertMis, false, request, apiRoutePath);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, station_flow_ids, "", 0);
        } catch (Exception ex) {
            errorMsg = "当前工位号{" + station_code + "},保存工件编号{" + serial_nums + "},过站信息发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
    //更改过站离开时间
    @RequestMapping(value = "/MesCoreStationFlowUpdateLeave", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreStationFlowUpdateLeave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreStationFlowUpdateLeave";
        String transResult = "";
        String errorMsg = "";
        String station_code = "";
        String station_flow_id_list = "";
        String meStationFlowTable = "c_mes_me_station_flow";
        try {
            station_code = jsonParas.getString("station_code");//工位号
            station_flow_id_list = jsonParas.getString("station_flow_id_list");//过站ID集合
            String leave_date = jsonParas.getString("leave_date");//离开时间
            if (leave_date == null || leave_date.equals("")) leave_date = CFuncUtilsSystem.GetNowDateTime("");
            Long cost_time = jsonParas.getLong("cost_time");
            if (cost_time == null) cost_time = (long) 0;
            String[] lstStationFlowId = station_flow_id_list.split(",", -1);
            if (lstStationFlowId == null || lstStationFlowId.length <= 0 || station_flow_id_list == null || station_flow_id_list.equals("")) {
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").in(lstStationFlowId));
            Update updateBigData = new Update();
            updateBigData.set("leave_date", leave_date);
            updateBigData.set("cost_time", cost_time);
            mongoTemplate.updateMulti(queryBigData, updateBigData, meStationFlowTable);
            //更新完成数量
            String sqlUpdateCount = "update c_mes_me_station_mis set " +
                    "mo_finish_count=mo_finish_count+1 " +
                    "where station_code='" + station_code + "'";
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdateCount, false, request, apiRoutePath);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "当前工位号{" + station_code + "},更新过站ID集合{" + station_flow_id_list + "}离开时间异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
