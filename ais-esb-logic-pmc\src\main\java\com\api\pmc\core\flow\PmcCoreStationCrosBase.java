package com.api.pmc.core.flow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 过站基础类
 * 1.记录过站信息
 * </p>
 *
 * <AUTHOR>
 * @since 2021-4-7
 */
@Service
@Slf4j
public class PmcCoreStationCrosBase {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //1.记录过站信息
    public String StationCrosIntefTask(HttpServletRequest request, String apiRoutePath, String staff_id, String allow_way,
                                       String station_code, String prod_line_code, String work_center_code,
                                       String make_order, String dms, String item_project,
                                       String serial_num, String pallet_num,
                                       String vin, String main_material_code, String small_model_type,
                                       String material_color, String material_size,
                                       String shaft_proc_num, String engine_num, String driver_way,
                                       String station_status, String arrive_date,
                                       String check_status, String check_code, String check_msg,
                                       String station_des, String bg_proceduce_code, String bg_proceduce_des,
                                       String line_section_code, String up_interf_flag, String flow_taglist) throws Exception {
        String id = "";//返回ID
        try{
            //默认值
            String nowDateTime=CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //1、写点位
            if(station_status.equals("1") && check_status.equals("OK") &&
                    flow_taglist!=null && !flow_taglist.equals("")) {
                JSONArray jsonArray = JSONArray.parseArray(flow_taglist);
                String tag_list="";
                String tag_value_list="";
                String tag_value_list_clear="";
                String tag_station_code="";
                String client_code="";
                for(int i=0; i<jsonArray.size(); i++) {
                    JSONObject object = jsonArray.getJSONObject(i);
                    if(i==0){
                        client_code=object.getString("tagkey").split("/")[0];
                    }
                    tag_list+=object.getString("tagkey")+","+
                            object.getString("make_order")+","+
                            object.getString("vin")+",";
                    tag_value_list+="1&"+make_order+"&"+vin+"&";
                    tag_value_list_clear+="0&&&";
                }
                tag_list=tag_list.substring(0,tag_list.length()-1);
                tag_value_list=tag_value_list.substring(0,tag_value_list.length()-1);
                tag_value_list_clear=tag_value_list_clear.substring(0,tag_value_list_clear.length()-1);
                //查询 实例对应的工位
                String sqlClient="select COALESCE(station_code,'') station_code  " +
                        "from scada_client " +
                        "where enable_flag='Y' " +
                        "and client_code ='"+client_code+"' ";
                List<Map<String,Object>> itemListClient=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlClient,false,null,apiRoutePath);
                if(itemListClient!=null && itemListClient.size()>0){
                    tag_station_code=itemListClient.get(0).get("station_code").toString();
                }
                //清空
                log.info("【过站上报】拧紧清空写入【"+tag_station_code+"】,点位【"+tag_list+"】,值【"+tag_value_list+"】");
                String writeMsgClear=cFuncUtilsCellScada.WriteTagByStation(tag_station_code,tag_station_code,tag_list,tag_value_list_clear,true);
                log.info("【过站上报】拧紧清空写入点位结果信息："+writeMsgClear);

                //写值
                log.info("【过站上报】拧紧写入【"+tag_station_code+"】,点位【"+tag_list+"】,值【"+tag_value_list+"】");
                String writeMsg=cFuncUtilsCellScada.WriteTagByStation(tag_station_code,tag_station_code,tag_list,tag_value_list,true);
                log.info("【过站上报】拧紧写入点位结果信息："+writeMsg);
            }
            //2、生成过站
            String serial_type="1";//条码类型(1生成的流水条码;2RFID)
            String repair_flag="N";//返修标识
            String quality_sign="";//总合格标志
            String data_collect_way="WebApi";//数据采集方式(Plc、WebApi、Files)
            String shif_code="";//班次代码
            String shif_des="";//班次描述
            String set_sign="NORMAL";//拉入或者拉出过站点（SET_IN、SET_OUT、NORMAL）
            String flow_stauts_code="0";//过站状态(默认0)
            String assembly_finish_flag="N";//是否校验合件完成(默认N)
            String mes_avi_flag="N";//是否上传MES
            String vin_avi_flag="N";//是否上传VIN打刻
            String jz_avi_flag="N";//是否上传油液加注
            String les_avi_flag="N";//是否上传LES
            if(up_interf_flag!=null && !up_interf_flag.equals("")) {
                String[] aviFlagArray=up_interf_flag.split(",");
                mes_avi_flag=aviFlagArray[0];
                vin_avi_flag=aviFlagArray[1];
                jz_avi_flag=aviFlagArray[2];
                les_avi_flag=aviFlagArray[3];
            }
            long stationFlowId=cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_station_flow_id_seq",true);
            id = String.valueOf(stationFlowId);
            String sqlDxStationIns="insert into d_pmc_me_station_flow " +
                    "(created_by,creation_date,station_flow_id," +
                    "work_center_code,prod_line_code,station_code,station_des,proceduce_code,proceduce_des," +
                    "serial_type,serial_num,pallet_num,staff_id,make_order,dms,item_project," +
                    "vin,small_model_type,main_material_code,material_color,material_size,shaft_proc_num,repair_flag," +
                    "quality_sign,data_collect_way,arrive_date,shif_code,shif_des,set_sign," +
                    "check_status,check_code,check_msg,flow_stauts_code,assembly_finish_flag," +
                    "mes_avi_flag,vin_avi_flag,jz_avi_flag,les_avi_flag) values " +
                    "('"+station_code+"','"+nowDateTime+"',"+stationFlowId+",'"+
                    work_center_code+"','"+prod_line_code+"','"+station_code+"','"+station_des+"','"+bg_proceduce_code+"','"+bg_proceduce_des+"','"+
                    serial_type+"','"+serial_num+"','"+pallet_num+"','"+staff_id+"','"+make_order+"','"+dms+"','"+item_project+"','"+
                    vin+"','"+small_model_type+"','"+main_material_code+"','"+material_color+"','"+material_size+"','"+shaft_proc_num+"','"+repair_flag+"','"+
                    quality_sign+"','"+data_collect_way+"','"+arrive_date+"','"+shif_code+"','"+shif_des+"','"+set_sign+"','"+
                    check_status+"',"+check_code+",'"+check_msg+"',"+flow_stauts_code+",'"+assembly_finish_flag+"','"+
                    mes_avi_flag+"','"+vin_avi_flag+"','"+jz_avi_flag+"','"+les_avi_flag+"')";
            cFuncDbSqlExecute.ExecUpdateSql(station_code,sqlDxStationIns,true,request,apiRoutePath);
            //3、生成当前工位实时工件信息
            if(check_status.equals("OK")) {
                String empty_ban_flag = "N";//是否为空板
                if (station_status.equals("3")) {
                    empty_ban_flag = "Y";
                }
                String status_way = "1";//状态计算方式(1过点实际/2推算/3采集)
                //4、获取当前工位订单 当前工位实时工件信息
                String station_status_id = "";//当前工位实时工件信息
                String sqlStationStatus = "select COALESCE(station_status_id,0) station_status_id " +
                        "from d_pmc_me_station_status " +
                        "where station_code='" + station_code + "' " +
                        "and prod_line_code='" + prod_line_code + "' " +
                        "and work_center_code='" + work_center_code + "' " +
                        "and allow_way='"+allow_way+"' ";
                List<Map<String, Object>> itemListStationStatus = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationStatus, true, request, apiRoutePath);
                if (itemListStationStatus != null && itemListStationStatus.size() > 0) {
                    station_status_id = itemListStationStatus.get(0).get("station_status_id").toString();
                    //4、清空 当前订单
                    String updStationStatus1 = "update d_pmc_me_station_status set " +
                            "last_updated_by='" + station_code + "'," +
                            "last_update_date='" + nowDateTime + "'," +
                            "station_status='0', " +
                            "empty_ban_flag='N', " +
                            "serial_num='', " +
                            "pallet_num='', " +
                            "staff_id='', " +
                            "make_order='', " +
                            "dms='', " +
                            "item_project='', " +
                            "vin='', " +
                            "small_model_type='', " +
                            "main_material_code='', " +
                            "material_color='', " +
                            "material_size='', " +
                            "shaft_proc_num='', " +
                            "quality_sign='', " +
                            "set_sign='', " +
                            "check_status='', " +
                            "check_code=-1, " +
                            "check_msg='', " +
                            "engine_num='', " +
                            "driver_way='', " +
                            "status_way='' " +
                            "where station_code<>'" + station_code + "' " +
                            "and make_order='" + make_order + "' " +
                            "and line_section_code='" + line_section_code + "' " +
                            "and work_center_code='" + work_center_code + "' ";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, updStationStatus1, true, request, apiRoutePath);
                    //5、修改订单信息
                    String updStationStatus2 = "update d_pmc_me_station_status set " +
                            "last_updated_by='" + station_code + "'," +
                            "last_update_date='" + nowDateTime + "'," +
                            "station_status='" + station_status + "', " +
                            "empty_ban_flag='" + empty_ban_flag + "', " +
                            "serial_num='" + serial_num + "', " +
                            "pallet_num='" + pallet_num + "', " +
                            "staff_id='" + staff_id + "', " +
                            "make_order='" + make_order + "', " +
                            "dms='" + dms + "', " +
                            "item_project='" + item_project + "', " +
                            "vin='" + vin + "', " +
                            "small_model_type='" + small_model_type + "', " +
                            "main_material_code='" + main_material_code + "', " +
                            "material_color='" + material_color + "', " +
                            "material_size='" + material_size + "', " +
                            "shaft_proc_num='" + shaft_proc_num + "', " +
                            "quality_sign='" + quality_sign + "', " +
                            "set_sign='" + set_sign + "', " +
                            "check_status='" + check_status + "', " +
                            "check_code=" + check_code + ", " +
                            "check_msg='" + check_msg + "', " +
                            "engine_num='" + engine_num + "', " +
                            "driver_way='" + driver_way + "', " +
                            "status_way='" + status_way + "' " +
                            " where station_status_id=" + station_status_id;
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, updStationStatus2, true, request, apiRoutePath);
                }
            }
            return id;
        }
        catch (Exception ex){
            throw new Exception("记录过站信息失败:"+ex.getMessage());
        }
    }

}
