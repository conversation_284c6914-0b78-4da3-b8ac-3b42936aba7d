package com.api.eap.project.dy.p1.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.dy.p1.wcf.RequestHead;
import com.api.eap.project.dy.p1.wcf.ResponseHead;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <p>
 * 定颖P1-EAP接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-08
 */
@Service
@Slf4j
public class EapDyP1InterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //返回request_head
    public RequestHead CreateRequestHeader(String function_name, String station_code) throws Exception{
        String nowDateTime=CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss.SSS");
        String trxId=station_code+nowDateTime+function_name;
        RequestHead requestHead=new RequestHead();
        requestHead.setEqpId(station_code);
        requestHead.setTimeStamp(nowDateTime);
        requestHead.setTrxId(trxId);
        return requestHead;
    }

    //返回request_head02方法
    public RequestHead CreateRequestHeader02(String function_name, String station_code) throws Exception{
        String nowDateTime=CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss.SSS");
        String trxId=station_code+nowDateTime;
        RequestHead requestHead=new RequestHead();
        requestHead.setEqpId(station_code);
        requestHead.setTimeStamp(nowDateTime);
        requestHead.setTrxId(trxId);
        return requestHead;
    }

    //检查返回结果是否正确
    public void CheckResponseHeader(ResponseHead responseHead) throws Exception{
        String errorMsg="";
        try{
            String result=responseHead.getResult();
            String rtn_code=responseHead.getRtnCode();
            String rtn_msg=responseHead.getRtnMsg();
            if(!result.equals("OK")){
                errorMsg=rtn_code+"@"+rtn_msg;
                throw new Exception(errorMsg);
            }
        }
        catch (Exception ex){
            throw ex;
        }
    }
}
