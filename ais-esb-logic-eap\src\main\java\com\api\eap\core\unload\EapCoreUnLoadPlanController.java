package com.api.eap.core.unload;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.OpStaticElements;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 收板机生产计划处理逻辑
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/unload")
public class EapCoreUnLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //放板机完工同步到收板机
    @RequestMapping(value = "/EapCoreUnLoadPlanLoadFinish", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanLoadFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanLoadFinish";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            //判断是否同步放扳机完工数量
            String updLoadFinishCount = cFuncDbSqlResolve.GetParameterValue("Upd_LoadFinishCount");
            if (updLoadFinishCount.equals("N")) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }

            String station_code = jsonParas.getString("station_code");//收板机工位
            String sqlStation = "select station_id " +
                    "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            JSONArray load_panel_list = jsonParas.getJSONArray("load_panel_list");
            JSONArray load_lot_list = jsonParas.getJSONArray("load_lot_list");
            String target_group_lot_num = "";
            Integer target_plan_count = 0;
            Integer target_update_count_sum = 0;
            String group_lot_status_now = "";

            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            if (load_lot_list != null && load_lot_list.size() > 0) {
                for (int i = 0; i < load_lot_list.size(); i++) {
                    JSONObject jbItem = load_lot_list.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    if (i == 0) target_group_lot_num = group_lot_num;
                    String lot_num = jbItem.getString("lot_num");
                    Integer finish_count = jbItem.getInteger("finish_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    Integer finish_ng_count = jbItem.getInteger("finish_ng_count");
                    target_plan_count += finish_ok_count;

                    //先查找数据
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        Document docItemBigData = iteratorBigData.next();
                        Integer target_update_count = 0;
                        if (docItemBigData.containsKey("target_update_count")) {
                            target_update_count = docItemBigData.getInteger("target_update_count");
                        }
                        target_update_count_sum += target_update_count;
                        finish_ok_count += target_update_count;
                        group_lot_status_now = docItemBigData.getString("group_lot_status");
                        iteratorBigData.close();
                    }

                    //更新
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    Update updateBigData = new Update();
                    updateBigData.set("target_lot_count", finish_ok_count);
                    updateBigData.set("target_update_count", finish_ok_count);
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }
            }

            Boolean isCancel = false;
            if (target_plan_count <= 0 && target_update_count_sum <= 0) isCancel = true;

            //判断放板数量是否为0
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && isCancel) {

                //判断是否写入点位CANCEL
                if (group_lot_status_now.equals("WORK")) {
                    String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                    String tagTaskCancelRequest = "";
                    if (aisMonitorModel.equals("AIS-PC")) {
                        tagTaskCancelRequest = "UnLoadAis/AisStatus/TaskCancelRequest";
                    } else if (aisMonitorModel.equals("AIS-SERVER")) {
                        tagTaskCancelRequest = "UnLoadAis_" + station_code + "/AisStatus/TaskCancelRequest";
                    } else {
                        OpStaticElements.unLoadPlanCountUpdLock.unlock();
                        errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagTaskCancelRequest, "1", true);
                    if (!errorMsg.equals("")) {
                        OpStaticElements.unLoadPlanCountUpdLock.unlock();
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }

                //再更新
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                Update updateBigData2 = new Update();
                updateBigData2.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }

            //判断是否当前任务正在进行中,若是进行中,则需要把任务数量更新到PLC
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && target_plan_count > 0) {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                String port_code = "";
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    port_code = docItemBigData.getString("port_code");
                    iteratorBigData.close();
                }
                if (port_code != null && !port_code.equals("")) {
                    String sqlPort = "select port_index " +
                            "from a_eap_fmod_station_port " +
                            "where station_id=" + station_id + " and port_code='" + port_code + "'";
                    List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPort, false, request, apiRoutePath);
                    if (lstPort != null && lstPort.size() > 0) {
                        String port_index = lstPort.get(0).get("port_index").toString();
                        String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                        String tagUpdateLotCountStatus = "";
                        String tagUpdateLotCount = "";
                        if (aisMonitorModel.equals("AIS-PC")) {
                            tagUpdateLotCountStatus = "UnLoadPlc/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "UnLoadPlc/AisReqControl" + port_index + "/UpdateLotCount";
                        } else if (aisMonitorModel.equals("AIS-SERVER")) {
                            tagUpdateLotCountStatus = "UnLoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "UnLoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCount";
                        } else {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        String tagOnlyKeyList = tagUpdateLotCount + "," + tagUpdateLotCountStatus;
                        String tagValueList = (target_plan_count + target_update_count_sum) + "&1";
                        errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
                        if (!errorMsg.equals("")) {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                    }
                }
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "放板机完工同步到收板机异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //放板机完工同步到收板机,一体机，用放板机点位
    @RequestMapping(value = "/EapCoreUnLoadPlanLoadFinish02", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanLoadFinish02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanLoadFinish02";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String station_code = jsonParas.getString("station_code");//收板机工位
            String sqlStation = "select station_id " +
                    "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            JSONArray load_panel_list = jsonParas.getJSONArray("load_panel_list");
            JSONArray load_lot_list = jsonParas.getJSONArray("load_lot_list");
            String target_group_lot_num = "";
            Integer target_plan_count = 0;
            Integer target_update_count_sum = 0;
            String group_lot_status_now = "";

            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            if (load_lot_list != null && load_lot_list.size() > 0) {
                for (int i = 0; i < load_lot_list.size(); i++) {
                    JSONObject jbItem = load_lot_list.getJSONObject(i);
                    String group_lot_num = jbItem.getString("group_lot_num");
                    if (i == 0) target_group_lot_num = group_lot_num;
                    String lot_num = jbItem.getString("lot_num");
                    Integer finish_count = jbItem.getInteger("finish_count");
                    Integer finish_ok_count = jbItem.getInteger("finish_ok_count");
                    Integer finish_ng_count = jbItem.getInteger("finish_ng_count");
                    target_plan_count += finish_ok_count;

                    //先查找数据
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        Document docItemBigData = iteratorBigData.next();
                        Integer target_update_count = 0;
                        if (docItemBigData.containsKey("target_update_count")) {
                            target_update_count = docItemBigData.getInteger("target_update_count");
                        }
                        target_update_count_sum += target_update_count;
                        finish_ok_count += target_update_count;
                        group_lot_status_now = docItemBigData.getString("group_lot_status");
                        iteratorBigData.close();
                    }

                    //更新
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                    queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                    queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                    Update updateBigData = new Update();
                    updateBigData.set("target_lot_count", finish_ok_count);
                    updateBigData.set("target_update_count", finish_ok_count);
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                }
            }

            Boolean isCancel = false;
            if (target_plan_count <= 0 && target_update_count_sum <= 0) isCancel = true;

            //判断放板数量是否为0
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && isCancel) {

                //判断是否写入点位CANCEL
                if (group_lot_status_now.equals("WORK")) {
                    String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                    String tagTaskCancelRequest = "";
                    if (aisMonitorModel.equals("AIS-PC")) {
                        tagTaskCancelRequest = "LoadAis/AisStatus/TaskCancelRequest";
                    } else if (aisMonitorModel.equals("AIS-SERVER")) {
                        tagTaskCancelRequest = "LoadAis_" + station_code + "/AisStatus/TaskCancelRequest";
                    } else {
                        OpStaticElements.unLoadPlanCountUpdLock.unlock();
                        errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                    errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagTaskCancelRequest, "1", true);
                    if (!errorMsg.equals("")) {
                        OpStaticElements.unLoadPlanCountUpdLock.unlock();
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }

                //再更新
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                Update updateBigData2 = new Update();
                updateBigData2.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
                return transResult;
            }

            //判断是否当前任务正在进行中,若是进行中,则需要把任务数量更新到PLC
            if (target_group_lot_num != null && !target_group_lot_num.equals("") && target_plan_count > 0) {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(target_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                String port_code = "";
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    port_code = docItemBigData.getString("port_code");
                    iteratorBigData.close();
                }
                if (port_code != null && !port_code.equals("")) {
                    String sqlPort = "select port_index " +
                            "from a_eap_fmod_station_port " +
                            "where station_id=" + station_id + " and port_code='" + port_code + "'";
                    List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPort, false, request, apiRoutePath);
                    if (lstPort != null && lstPort.size() > 0) {
                        String port_index = lstPort.get(0).get("port_index").toString();
                        String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                        String tagUpdateLotCountStatus = "";
                        String tagUpdateLotCount = "";
                        if (aisMonitorModel.equals("AIS-PC")) {
                            tagUpdateLotCountStatus = "LoadPlc/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "LoadPlc/AisReqControl" + port_index + "/UpdateLotCount";
                        } else if (aisMonitorModel.equals("AIS-SERVER")) {
                            tagUpdateLotCountStatus = "LoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCountStatus";
                            tagUpdateLotCount = "LoadPlc_" + station_code + "/AisReqControl" + port_index + "/UpdateLotCount";
                        } else {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                        String tagOnlyKeyList = tagUpdateLotCount + "," + tagUpdateLotCountStatus;
                        String tagValueList = (target_plan_count + target_update_count_sum) + "&1";
                        errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
                        if (!errorMsg.equals("")) {
                            OpStaticElements.unLoadPlanCountUpdLock.unlock();
                            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return transResult;
                        }
                    }
                }
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "放板机完工同步到收板机异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //判断是否存在收板任务
    @RequestMapping(value = "/EapCoreUnLoadPlanExistJudge", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanExistJudge(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanExistJudge";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在收板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //判断是否存在收板任务,integratedFlag(一体机标识)，一体机收板机任务attribute1=Y
    @RequestMapping(value = "/EapCoreUnLoadPlanExistJudge02", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanExistJudge02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanExistJudge02";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String integratedFlag = jsonParas.getString("integratedFlag");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            if (integratedFlag.equals("Y")) {
                queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));
            }
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                if (integratedFlag.equals("Y")) {
                    queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));
                }
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在收板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //判断是否存在收板任务-(方法3)
    @RequestMapping(value = "/EapCoreUnLoadPlanExistJudgeM3", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanExistJudgeM3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanExistJudgeM3";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            long station_id = jsonParas.getLong("station_id");
            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            //查询
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                itemList.add(docItemBigData);
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在收板任务M2异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //判断是否存在收板任务-(方法4)
    @RequestMapping(value = "/EapCoreUnLoadPlanExistJudgeM4", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanExistJudgeM4(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanExistJudgeM4";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            long station_id = jsonParas.getLong("station_id");
            //查询
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                itemList.add(docItemBigData);
            }
            if (iteratorBigData.hasNext())
                iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在收板任务M2异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //判断是否存在收板任务-(方法2:针对端口和多任务模式)
    @RequestMapping(value = "/EapCoreUnLoadPlanExistJudgeM2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanExistJudgeM2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanExistJudgeM2";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String pallet_num = jsonParas.getString("pallet_num");
            if (pallet_num == null) pallet_num = "";
            long station_id_long = Long.parseLong(station_id);

            //先读取是否为一批多车模式
            //1.查询工位信息,并且针对一车多批不做处理
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //查询
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = null;
            MongoCursor<Document> iteratorBigData = null;
            if (!OneCarMultyLotFlag.equals("2")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                }
            } else {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                } else {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        Document docItemBigData = iteratorBigData.next();
                        group_id = docItemBigData.getString("group_id");
                        iteratorBigData.close();
                    }
                }
            }

            //继续查询
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    String new_pallet_num = docItemBigData.getString("pallet_num");
                    if (!pallet_num.equals("")) new_pallet_num = pallet_num;
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", new_pallet_num);
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();

                //修改载具ID
                Update updateBigData = new Update();
                if (OneCarMultyLotFlag.equals("2")) {
                    //创建一个虚拟的载具ID,防止存在重复的载具ID导致找不到台车比对数据
                    String virtu_pallet_num = CFuncUtilsSystem.CreateUUID(true);
                    updateBigData.set("pallet_num", pallet_num);
                    updateBigData.set("virtu_pallet_num", virtu_pallet_num);
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                } else {
                    if (!pallet_num.equals("")) {
                        updateBigData.set("pallet_num", pallet_num);
                        mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                    }
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在收板任务M2异常:" + ex;
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //判断是否存在收板任务并且选择
    @RequestMapping(value = "/EapCoreUnLoadPlanExistJudgeAndSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanExistJudgeAndSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanExistJudgeAndSel";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String station_id = jsonParas.getString("station_id");
            String pallet_num = jsonParas.getString("pallet_num");
            String port_code = jsonParas.getString("port_code");
            if (pallet_num == null) pallet_num = "";
            long station_id_long = Long.parseLong(station_id);

            //先读取是否为一批多车模式
            //1.查询工位信息,并且针对一车多批不做处理
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //查询
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = null;
            MongoCursor<Document> iteratorBigData = null;
            if (!OneCarMultyLotFlag.equals("2")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                }
            } else {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                } else {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        Document docItemBigData = iteratorBigData.next();
                        group_id = docItemBigData.getString("group_id");
                        iteratorBigData.close();
                    }
                }
            }

            //继续查询
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    String new_pallet_num = docItemBigData.getString("pallet_num");
                    if (!pallet_num.equals("")) new_pallet_num = pallet_num;
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", new_pallet_num);
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();

                //修改端口号
                Update updateBigData = new Update();
                updateBigData.set("port_code", port_code);
                updateBigData.set("group_lot_status", "WORK");
                if (OneCarMultyLotFlag.equals("2")) {
                    //创建一个虚拟的载具ID,防止存在重复的载具ID导致找不到台车比对数据
                    String virtu_pallet_num = CFuncUtilsSystem.CreateUUID(true);
                    updateBigData.set("pallet_num", pallet_num);
                    updateBigData.set("virtu_pallet_num", virtu_pallet_num);
                } else {
                    if (!pallet_num.equals("")) {
                        updateBigData.set("pallet_num", pallet_num);
                    }
                }
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "判断是否存在收板任务并且选择异常:" + ex;
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //根据端口号判断是否存在收板任务
    @RequestMapping(value = "/EapCoreUnLoadPlanExistJudgeByPort", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanExistJudgeByPort(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanExistJudgeByPort";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();

            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在收板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //选择端口任务为进行中
    @RequestMapping(value = "/EapCoreUnLoadPlanWorkPlanSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanWorkPlanSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanWorkPlanSelect";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String group_lot_num = "";
            long station_id_long = Long.parseLong(station_id);

            //1.查询工位信息,并且针对一车多批不做处理
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id;
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //针对一批多车不进行CANCEL任务
            Query queryBigData = new Query();
            if (!OneCarMultyLotFlag.equals("2")) {
                //先将当前端口正在进行中的任务结束掉
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                Update updateBigData2 = new Update();
                updateBigData2.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
            } else {
                //1.先获取WORK任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_lot_num = docItemBigData.getString("group_lot_num");
                    iteratorBigData.close();
                }
                if (!group_lot_num.equals("")) {
                    OpStaticElements.unLoadPlanCountUpdLock.unlock();
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
                    return transResult;
                }
            }

            //选择PLAN任务
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                iteratorBigData.close();
            }
            if (!group_lot_num.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                updateBigData.set("port_code", port_code);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "选择端口任务为进行中异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //选择端口任务为进行中
    @RequestMapping(value = "/EapCoreUnLoadPlanWorkPlanSelect2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanWorkPlanSelect2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanWorkPlanSelect2";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String group_lot_num = "";
            long station_id_long = Long.parseLong(station_id);

            //1.查询工位信息,并且针对一车多批不做处理
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id;
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            //针对一批多车不进行CANCEL任务
            Query queryBigData = new Query();
            if (!OneCarMultyLotFlag.equals("2")) {
                //先将当前端口正在进行中的任务结束掉
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                Update updateBigData2 = new Update();
                updateBigData2.set("group_lot_status", "CANCEL");
                mongoTemplate.updateMulti(queryBigData, updateBigData2, apsPlanTable);
            } else {
                //1.先获取WORK任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_lot_num = docItemBigData.getString("group_lot_num");
                    iteratorBigData.close();
                }
                if (!group_lot_num.equals("")) {
                    OpStaticElements.unLoadPlanCountUpdLock.unlock();
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
                    return transResult;
                }
            }

            //选择PLAN任务
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                iteratorBigData.close();
            }
            if (!group_lot_num.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                updateBigData.set("port_code", port_code);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "选择端口任务为进行中异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //收板机Panel校验  ----暂时先不做一车多批的判断,这个再想想
    @RequestMapping(value = "/EapCoreUnLoadPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_count = 0;//计划首检数量
            Integer inspect_finish_count = 0;//实际完成首检数量
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_count = docItemBigData.getInteger("inspect_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));

            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            if (plan_id.equals("")) {//当未找到任务时直接记录
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 99;
                }
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                panel_ng_code = 99;//Dummy模式返回100代码
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            //判断是否为Strip
                            if (strip_flag.equals("Y")) {
                                if (item_info != null && !item_info.equals("")) {
                                    JSONArray jaStripList = JSONArray.parseArray(item_info);
                                    if (jaStripList != null && jaStripList.size() > 0) {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        for (int m = 0; m < jaStripList.size(); m++) {
                                            JSONObject jbStripItem = jaStripList.getJSONObject(m);
                                            String strip_barcode = jbStripItem.getString("strip_barcode");
                                            String strip_level = jbStripItem.getString("strip_level");
                                            String strip_status = jbStripItem.getString("strip_status");
                                            if (strip_barcode.equals(panel_barcode)) {
                                                if (strip_status.equals("OK")) {
                                                    panel_status = "OK";
                                                    panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            } else {
                                String projectCode = cFuncDbSqlResolve.GetParameterValue("projectCode");
                                if (projectCode.equals("fastprint")) {
                                    //广州快捷20241209定制 判断板件是否属于当前任务，如果不属于当前任务，则取消当前任务和板件所属任务
                                    Query queryBigDataD = new Query();
                                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                    iteratorBigData = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                            sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                                    if (iteratorBigData.hasNext()) {
                                        Document doc = iteratorBigData.next();
                                        String plan_id_item = doc.getString("plan_id");
                                        if (!plan_id_item.equals(plan_id)) {
                                            //判断板件是否对应其他板件,则取消两个任务
                                            Update update = new Update();
                                            update.set("lot_status", "CANCEL");
                                            Query query1 = new Query();
                                            query1.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            mongoTemplate.updateFirst(query1, update, apsPlanTable);

                                            Query query2 = new Query();
                                            query2.addCriteria(Criteria.where("plan_id").is(plan_id_item));
                                            mongoTemplate.updateFirst(query2, update, apsPlanTable);
                                        }
                                        iteratorBigData.close();
                                    } else {
                                        if (ng_auto_pass_value.equals("1")) {
                                            panel_status = "NG_PASS";
                                            panel_ng_code = 4;
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        } else {
                                            panel_status = "NG";
                                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                    }
                                } else {
                                    long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                                    if (pnListCount > 0) {
                                        Query queryBigDataD = new Query();
                                        queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                        queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                        long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                        if (okCount <= 0) {
                                            if (ng_auto_pass_value.equals("1")) {
                                                panel_status = "NG_PASS";
                                                panel_ng_code = 4;
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            } else {
                                                panel_status = "NG";
                                                panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            }
                                        }
                                    } else {
                                        if (!lot_short_num.equals("")) {//采用简码判断
                                            if (!panel_barcode.contains(lot_short_num)) {
                                                if (ng_auto_pass_value.equals("1")) {
                                                    panel_status = "NG_PASS";
                                                    panel_ng_code = 4;
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                } else {
                                                    panel_status = "NG";
                                                    panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if (panel_status.equals("OK")) {
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if (panelCount > 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            if (inspect_count > 0 && inspect_finish_count < inspect_count) {
                mapBigDataRow.put("inspect_flag", "Y");
                inspect_finish_count++;
            }

            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
            String lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (inspect_count > 0 && inspect_finish_count <= inspect_count) {
                updateBigData.set("inspect_finish_count", inspect_finish_count);
            }
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "DEMO异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //收板机Panel校验  ----一体机
    @RequestMapping(value = "/EapCoreUnLoadPlanPanelCheckAndSave2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanPanelCheckAndSave2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanPanelCheckAndSave2";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_count = 0;//计划首检数量
            Integer inspect_finish_count = 0;//实际完成首检数量
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_count = docItemBigData.getInteger("inspect_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));

            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            if (plan_id.equals("")) {//当未找到任务时直接记录
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 99;
                }
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                panel_ng_code = 99;//Dummy模式返回100代码
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            //判断是否为Strip
                            if (strip_flag.equals("Y")) {
                                if (item_info != null && !item_info.equals("")) {
                                    JSONArray jaStripList = JSONArray.parseArray(item_info);
                                    if (jaStripList != null && jaStripList.size() > 0) {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        for (int m = 0; m < jaStripList.size(); m++) {
                                            JSONObject jbStripItem = jaStripList.getJSONObject(m);
                                            String strip_barcode = jbStripItem.getString("strip_barcode");
                                            String strip_level = jbStripItem.getString("strip_level");
                                            String strip_status = jbStripItem.getString("strip_status");
                                            if (strip_barcode.equals(panel_barcode)) {
                                                if (strip_status.equals("OK")) {
                                                    panel_status = "OK";
                                                    panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            } else {
                                long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                                if (pnListCount > 0) {
                                    Query queryBigDataD = new Query();
                                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                    long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                    if (okCount <= 0) {
                                        if (ng_auto_pass_value.equals("1")) {
                                            panel_status = "NG_PASS";
                                            panel_ng_code = 4;
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        } else {
                                            panel_status = "NG";
                                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                    }
                                } else {
                                    if (!lot_short_num.equals("")) {//采用简码判断
                                        if (!panel_barcode.contains(lot_short_num)) {
                                            if (ng_auto_pass_value.equals("1")) {
                                                panel_status = "NG_PASS";
                                                panel_ng_code = 4;
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            } else {
                                                panel_status = "NG";
                                                panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            }
                                        }
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if (panel_status.equals("OK")) {
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if (panelCount > 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            if (inspect_count > 0 && inspect_finish_count < inspect_count) {
                mapBigDataRow.put("inspect_flag", "Y");
                inspect_finish_count++;
            }

            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
            String lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (inspect_count > 0 && inspect_finish_count <= inspect_count) {
                updateBigData.set("inspect_finish_count", inspect_finish_count);
            }
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "DEMO异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //收板机Panel校验  ----上游没有NG且读码校验成功才给OK，否则全部给NG
    @RequestMapping(value = "/EapCoreUnLoadPlanPanelCheckAndSave3", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanPanelCheckAndSave3(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanPanelCheckAndSave3";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断
            String up_panel_status = jsonParas.getString("up_panel_status");//PLC上游设备判定panel结果(1为NG,0为OK)
            if (up_panel_status == null) up_panel_status = "0";

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_count = 0;//计划首检数量
            Integer inspect_finish_count = 0;//实际完成首检数量
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_count = docItemBigData.getInteger("inspect_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));

            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            if (plan_id.equals("")) {//当未找到任务时直接记录
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 99;
                }
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                panel_ng_code = 99;//Dummy模式返回100代码
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK" + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            //判断是否为Strip
                            if (strip_flag.equals("Y")) {
                                if (item_info != null && !item_info.equals("")) {
                                    JSONArray jaStripList = JSONArray.parseArray(item_info);
                                    if (jaStripList != null && jaStripList.size() > 0) {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        for (int m = 0; m < jaStripList.size(); m++) {
                                            JSONObject jbStripItem = jaStripList.getJSONObject(m);
                                            String strip_barcode = jbStripItem.getString("strip_barcode");
                                            String strip_level = jbStripItem.getString("strip_level");
                                            String strip_status = jbStripItem.getString("strip_status");
                                            if (strip_barcode.equals(panel_barcode)) {
                                                if (strip_status.equals("OK")) {
                                                    panel_status = "OK";
                                                    panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            } else {
                                String projectCode = cFuncDbSqlResolve.GetParameterValue("projectCode");
                                if (projectCode.equals("fastprint")) {
                                    //广州快捷20241209定制 判断板件是否属于当前任务，如果不属于当前任务，则取消当前任务和板件所属任务
                                    Query queryBigDataD = new Query();
                                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                    iteratorBigData = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                            sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                                    if (iteratorBigData.hasNext()) {
                                        Document doc = iteratorBigData.next();
                                        String plan_id_item = doc.getString("plan_id");
                                        if (!plan_id_item.equals(plan_id)) {
                                            //判断板件是否对应其他板件,则取消两个任务
                                            Update update = new Update();
                                            update.set("lot_status", "CANCEL");
                                            Query query1 = new Query();
                                            query1.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            mongoTemplate.updateFirst(query1, update, apsPlanTable);

                                            Query query2 = new Query();
                                            query2.addCriteria(Criteria.where("plan_id").is(plan_id_item));
                                            mongoTemplate.updateFirst(query2, update, apsPlanTable);
                                        }
                                        iteratorBigData.close();
                                    } else {
                                        if (ng_auto_pass_value.equals("1")) {
                                            panel_status = "NG_PASS";
                                            panel_ng_code = 4;
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        } else {
                                            panel_status = "NG";
                                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                    }
                                } else {
                                    long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                                    if (pnListCount > 0) {
                                        Query queryBigDataD = new Query();
                                        queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                        queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                        long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                        if (okCount <= 0) {
                                            if (ng_auto_pass_value.equals("1")) {
                                                panel_status = "NG_PASS";
                                                panel_ng_code = 4;
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            } else {
                                                panel_status = "NG";
                                                panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            }
                                        }
                                    } else {
                                        if (!lot_short_num.equals("")) {//采用简码判断
                                            if (!panel_barcode.contains(lot_short_num)) {
                                                if (ng_auto_pass_value.equals("1")) {
                                                    panel_status = "NG_PASS";
                                                    panel_ng_code = 4;
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                } else {
                                                    panel_status = "NG";
                                                    panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if (panel_status.equals("OK")) {
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if (panelCount > 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if (up_panel_status.equals("1")) {
                panel_status = "NG";
                panel_ng_code = 9;
                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            if (inspect_count > 0 && inspect_finish_count < inspect_count) {
                mapBigDataRow.put("inspect_flag", "Y");
                inspect_finish_count++;
            }

            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_ok_count >= plan_lot_count) lot_status = "FINISH";
            String lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            Update updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (inspect_count > 0 && inspect_finish_count <= inspect_count) {
                updateBigData.set("inspect_finish_count", inspect_finish_count);
            }
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2 + "," + group_lot_num;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "DEMO异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //收板机Panel校验
    @RequestMapping(value = "/EapCoreUnLoadPlanPanelCheckAndSave4", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanPanelCheckAndSave4(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanPanelCheckAndSave4";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String meUpRecordTable = "a_eap_me_up_record";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            Integer up_ng_code = jsonParas.getInteger("up_ng_code");//上游OK/NG标识(0:OK,1:NG,2:陪镀板)
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            //当前主生产端口信息
            String port_group_id = jsonParas.getString("port_group_id");
            String plan_id = jsonParas.getString("plan_id");
            Integer port_index = jsonParas.getInteger("port_index");
            Integer port_count = jsonParas.getInteger("port_count");
            String port_pallet_num = jsonParas.getString("port_pallet_num");
            Integer next_port_index = jsonParas.getInteger("next_port_index");
            //当前上游NG端口信息
            Integer ng_port_index = jsonParas.getInteger("ng_port_index");//-1代表无专门的上游NG收板端口,0代表载具未载入
            Integer ng_port_lotcount = jsonParas.getInteger("ng_port_lotcount");
            String ng_port_planid_list = jsonParas.getString("ng_port_planid_list");
            Integer ng_port_count = jsonParas.getInteger("ng_port_count");
            String ng_port_pallet_num = jsonParas.getString("ng_port_pallet_num");
            String PnlCheckLengthStr = cFuncDbSqlResolve.GetParameterValue("PnlCheckLength");//pnl条码校验截取长度(-1标识不用截取，大于0标识需截取长度)
            if (PnlCheckLengthStr == null || PnlCheckLengthStr.equals(""))
                PnlCheckLengthStr = "-1";
            int PnlCheckLengthInt = Integer.parseInt(PnlCheckLengthStr);

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String group_id = "";
            String lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:上游NG,10其他异常
            Integer plc_ng_code = 0;//1-10代表OK,其他为NG，11陪镀板，
            Integer panel_index = 0;
            Integer target_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer finish_up_ng_count = 0;
            Double panel_tickness = 10.0;
            String panel_flag = "0";
            String lot_first_flag = "N";
            String lot_finish_flag = "N";
            String port_back_flag = "N";//生产端口是否退载具
            String next_port_flag = "N";//是否跳转到下一生产端口
            String next_port_back_flag = "N";//生产端口是否退载具
            String use_ng_port_flag = "N";//是否使用上游NG端口
            String ng_port_back_flag = "N";//NG工位是否退载具
            String plan_port_code = "";//计划选择端口号
            String plan_pallet_num = "";//计划载具条码
            String plan_pallet_type = "";//计划载具类型
            String flow_port_code = "";//过站信息端口号
            String flow_pallet_num = "";//过站载具条码
            String port_sign = "";
            if (sys_model == 0) offline_flag = "Y";
            if (plan_id.equals("")) lot_first_flag = "Y";
            String first_flag = "N";
            //配方信息
            Integer inspect_count = 0;//首件数量
            Integer inspect_finish_count = 0;//首件完成数量
            //1.判断是否为Dummy板
            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
                Query queryBigDataDummy = new Query();
                queryBigDataDummy.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDummy.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                queryBigDataDummy.addCriteria(Criteria.where("dummy_flag").is("Y"));
                long dummyCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataDummy.getQueryObject());
                if (dummyCount > 0) {
                    dummy_flag = "Y";
                    panel_ng_code = 11;
                }
            }
            //判断是否是首件板
//            if (panel_model == 1 && !panel_barcode.equals("") && !panel_barcode.equals("NoRead")) {
//                Query queryBigDataInspect = new Query();
//                queryBigDataInspect.addCriteria(Criteria.where("station_id").is(station_id));
//                queryBigDataInspect.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
//                queryBigDataInspect.addCriteria(Criteria.where("inspect_flag").is("Y"));
//                long inspectCount = mongoTemplate.getCollection(meUpRecordTable).countDocuments(queryBigDataInspect.getQueryObject());
//                if (inspectCount > 0) {
//                    inspect_flag = "Y";
//                }
//            }
            //根据plc给出的标识判定是否是陪镀板
            if (up_ng_code == 2) {
                dummy_flag = "Y";
                panel_ng_code = 11;
            }

            //2.判断板件状态
            if (sys_model > 0 && dummy_flag.equals("N")) {
                if (lot_first_flag.equals("Y")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    queryBigData.addCriteria(Criteria.where("lot_status").is("PLAN"));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                } else {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        mapLotInfo = iteratorBigData.next();
                        iteratorBigData.close();
                    }
                }
                if (mapLotInfo == null) {
                    panel_ng_code = 8;
                    if (up_ng_code == 1) {
                        panel_ng_code = 9;
                        if (ng_port_index > 0) {
                            use_ng_port_flag = "Y";
                            ng_port_count++;
                        }
                    }
                } else {
                    group_id = mapLotInfo.get("group_id").toString();
                    plan_id = mapLotInfo.get("plan_id").toString();
                    lot_status = mapLotInfo.get("lot_status").toString();
                    target_lot_count = Integer.parseInt(mapLotInfo.get("target_lot_count").toString());
                    panel_tickness = Double.parseDouble(mapLotInfo.get("panel_tickness").toString());
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    if (up_ng_code == 0) {
                        if (port_index <= 0) panel_ng_code = 10;//无端口收板
                        else {
                            if (lot_first_flag.equals("Y")) {
                                if (port_group_id != null && !port_group_id.equals("") && group_id != null && !group_id.equals("") && !group_id.equals(port_group_id)) {
                                    if (port_count > 0) port_back_flag = "Y";
                                    if (next_port_index > 0) next_port_flag = "Y";
                                    else panel_ng_code = 10;//无端口收板
                                }
                            }
                            if (panel_ng_code == 0) {
                                if (panel_model == 1) {//有Panel读码模式
                                    if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                        if (panel_barcode.equals("")) panel_ng_code = 7;
                                        else panel_ng_code = 2;
                                    } else {
                                        long okCount = 0;
                                        String panel_barcode_check = panel_barcode;
                                        if (PnlCheckLengthInt > 0 && panel_barcode.length() >= PnlCheckLengthInt) {
                                            panel_barcode_check.substring(0, PnlCheckLengthInt);
                                        }
                                        Query queryBigDataD = new Query();
                                        queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                        queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                        MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                                sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                        if (iteratorBigDataD.hasNext()) {
                                            okCount = 1l;
                                            Document docItem = iteratorBigDataD.next();
                                            iteratorBigDataD.close();
                                        }
                                        if (okCount <= 0) panel_ng_code = 12;
                                        else {
                                            queryBigDataD = new Query();
                                            queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                            queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode_check));
                                            long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (flowCount > 0) {
                                                panel_ng_code = 3;//重码
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        if (up_ng_code == 1) {
                            finish_up_ng_count += 1;
                        }
                        panel_ng_code = 9;
                        if (ng_port_index > 0) {
                            use_ng_port_flag = "Y";
                            ng_port_count++;
                        }
                    }

                    if (panel_ng_code == 0 && inspect_finish_count < inspect_count) {
                        inspect_flag = "Y";
                        inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                    }
                    //重码放行但不计数
                    if (panel_ng_code != 3)
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                    panel_index = finish_count;
                    //读码NG的直接放行，算到收板OK
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                    } else if (panel_ng_code != 3) {
                        finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                    }
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 7) {
                        if (next_port_flag.equals("N")) {
                            port_count++;
                        }
                    }
                    //判断当前作业端口是否退载具
                    if (finish_count >= target_lot_count) {
                        lot_finish_flag = "Y";
                        //上游NG时退载具
                        if (finish_up_ng_count > 0) {
                            if (port_index > 0) port_back_flag = "Y";
                        } else {
                            if (port_index > 0) {
                                port_back_flag = "Y";
                            }
                        }
                        //判断上游NG工位批次数量是否达到最大值
                        if (ng_port_index > 0 && ng_port_lotcount > 0) {
                            if (panel_ng_code == 9) {
                                if (ng_port_planid_list.equals("")) ng_port_planid_list = plan_id;
                                else {
                                    String[] tempList = ng_port_planid_list.split(",", -1);
                                    if (!Arrays.asList(tempList).contains(plan_id))
                                        ng_port_planid_list += "," + plan_id;
                                }
                            }
                            if (!ng_port_planid_list.equals("")) {
                                String[] tempList = ng_port_planid_list.split(",", -1);
                                if (tempList != null && tempList.length >= ng_port_lotcount) {
                                    ng_port_back_flag = "Y";
                                }
                            }
                        }
                    }
                    //判断是否存在特殊板件
                    if (panel_flag.equals("6")) {
                        if (port_index > 0) port_back_flag = "Y";
                    }
                }
            }

            //3.锁定选择端口以及返回给PLC代码
            if (dummy_flag.equals("Y")) {
                plc_ng_code = panel_ng_code;
            } else {
                if (sys_model > 0) {
                    plc_ng_code = panel_ng_code + 10;
                    plan_pallet_num = port_pallet_num;
                    flow_pallet_num = port_pallet_num;
                    //重码放行但不计数
                    if (panel_ng_code == 0 || panel_ng_code == 2 || panel_ng_code == 3 || panel_ng_code == 7) {
                        if (next_port_flag.equals("Y")) {
                            plc_ng_code = next_port_index;
                            plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, next_port_index, plan_port_code, port_sign);
                            flow_port_code = plan_port_code;
                        } else {
                            if (port_index > 0) {
                                plc_ng_code = port_index;
                            }
                        }
                    }
                    if (mapLotInfo != null && next_port_flag.equals("N") && port_index > 0) {
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, port_index, plan_port_code, port_sign);
                        flow_port_code = plan_port_code;
                    }
                    if (use_ng_port_flag.equals("Y")) {
                        plc_ng_code = ng_port_index;
                        plan_port_code = opCommonFunc.GetPortInfoByPortIndex(station_id, ng_port_index, flow_port_code, port_sign);
                        flow_pallet_num = ng_port_pallet_num;
                    }
                } else {
                    plc_ng_code = 1;
                }
            }

            if (inspect_flag.equals("Y")) {
                port_index = 10;
                plc_ng_code = 10;
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : (panel_ng_code == 4 || panel_ng_code == 2 || panel_ng_code == 7) ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_id").toString());
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);

            JSONObject jbResult = new JSONObject();
            String first_bind_flag = "N";
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                //更改
                Update updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                updateBigData.set("attribute3", finish_up_ng_count.toString());
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                String port_code = mapLotInfo.get("port_code").toString();
                if (port_code.equals("") && !plan_port_code.equals("")) {
                    first_flag = "Y";
                    first_bind_flag = "Y";
                    updateBigData.set("port_code", plan_port_code);
                    updateBigData.set("pallet_num", plan_pallet_num);
                    updateBigData.set("pallet_type", plan_pallet_type);
                    updateBigData.set("lot_status", "WORK");
                    updateBigData.set("group_lot_status", "WORK");
                }
                if (lot_finish_flag.equals("Y")) {
                    String task_start_time = mapLotInfo.get("task_start_time").toString();
                    if (task_start_time.equals("")) task_start_time = CFuncUtilsSystem.GetNowDateTime("");
                    String task_end_time = CFuncUtilsSystem.GetNowDateTime("");
                    long task_cost_time = CFuncUtilsSystem.GetDiffMsTimes(task_start_time, task_end_time);
                    Integer task_error_code = 0;
                    if (target_lot_count == finish_ok_count) task_error_code = 1;
                    else if (target_lot_count > finish_ok_count) task_error_code = 2;
                    else if (target_lot_count < finish_ok_count) task_error_code = 3;
                    updateBigData.set("lot_status", "FINISH");
                    updateBigData.set("group_lot_status", "FINISH");
                    updateBigData.set("task_end_time", task_end_time);
                    updateBigData.set("task_cost_time", task_cost_time);
                    updateBigData.set("task_error_code", task_error_code);
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }

            //组合返回数据
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : flow_pallet_num);
            jbResult.put("pallet_type", "");
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : String.valueOf(plc_ng_code));
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("plc_ng_code", plc_ng_code);
            jbResult.put("first_bind_flag", first_bind_flag);
            jbResult.put("lot_finish_flag", lot_finish_flag);
            jbResult.put("port_back_flag", port_back_flag);
            jbResult.put("next_port_flag", next_port_flag);
            jbResult.put("next_port_back_flag", next_port_back_flag);
            jbResult.put("use_ng_port_flag", use_ng_port_flag);
            jbResult.put("ng_port_back_flag", ng_port_back_flag);
            jbResult.put("port_count", port_count);
            jbResult.put("ng_port_count", ng_port_count);
            jbResult.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            jbResult.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            jbResult.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            jbResult.put("port_index", port_index);
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("first_flag", first_flag);

            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //收板机Panel校验  ----暂时先不做一车多批的判断,这个再想想
    @RequestMapping(value = "/EapCoreUnLoadPlanPanelCheckByFaceAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanPanelCheckByFaceAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanPanelCheckByFaceAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            String strip_flag = jsonParas.getString("strip_flag");//是否为Strip判断
            String face_code2 = jsonParas.getString("face_code");//存储A面B面,1:A,2:B

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (port_index == null || port_index.equals("")) port_index = "0";
            if (strip_flag == null || strip_flag.equals("")) strip_flag = "N";
            if (face_code2 == null || face_code2.equals("")) face_code2 = "0";
            Integer face_code2_int = Integer.parseInt(face_code2);

            long station_id_long = Long.parseLong(station_id);
            String port_code = "";
            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort != null && lstPort.size() > 0) port_code = lstPort.get(0).get("port_code").toString();

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //2.获取当前计划中或者执行中的子任务
            String group_lot_num = "";
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;
            String item_info = "";//Strip集合
            String old_plan_status = "";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                item_info = docItemBigData.getString("item_info");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            if (face_code2_int > 0) face_code = face_code2_int;//若有传递值,则用传递值
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));

            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", "N");
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            if (plan_id.equals("")) {//当未找到任务时直接记录
                if (dummy_flag.equals("Y")) {
                    panel_ng_code = 99;
                }
                mapBigDataRow.put("offline_flag", "Y");
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK";//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }

            //查询List
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData2 = new Query();
            queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData2.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData2.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData2.getQueryObject()).
                    sort(queryBigData2.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //1.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                panel_ng_code = 99;//Dummy模式返回100代码
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                        panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK";//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            //判断是否为Strip
                            if (strip_flag.equals("Y")) {
                                if (item_info != null && !item_info.equals("")) {
                                    JSONArray jaStripList = JSONArray.parseArray(item_info);
                                    if (jaStripList != null && jaStripList.size() > 0) {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        for (int m = 0; m < jaStripList.size(); m++) {
                                            JSONObject jbStripItem = jaStripList.getJSONObject(m);
                                            String strip_barcode = jbStripItem.getString("strip_barcode");
                                            String strip_level = jbStripItem.getString("strip_level");
                                            String strip_status = jbStripItem.getString("strip_status");
                                            if (strip_barcode.equals(panel_barcode)) {
                                                if (strip_status.equals("OK")) {
                                                    panel_status = "OK";
                                                    panel_ng_code = 0;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                                }
                                                break;
                                            }
                                        }
                                    }
                                }
                            } else {
                                long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                                if (pnListCount > 0) {
                                    Query queryBigDataD = new Query();
                                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                    queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                    long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                    if (okCount <= 0) {
                                        if (ng_auto_pass_value.equals("1")) {
                                            panel_status = "NG_PASS";
                                            panel_ng_code = 4;
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        } else {
                                            panel_status = "NG";
                                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                    }
                                } else {
                                    if (!lot_short_num.equals("")) {//采用简码判断
                                        if (!panel_barcode.contains(lot_short_num)) {
                                            if (ng_auto_pass_value.equals("1")) {
                                                panel_status = "NG_PASS";
                                                panel_ng_code = 4;
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            } else {
                                                panel_status = "NG";
                                                panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                                panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                            }
                                        }
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if (panel_status.equals("OK")) {
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                queryBigDataFlow.addCriteria(Criteria.where("face_code").is(face_code));
                                long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if (panelCount > 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量 广州快捷（B面不修改任务数量）
//            finish_count++;
//            if(panel_status.equals("NG"))  finish_ng_count++;
//            else finish_ok_count++;
//            String lot_status="WORK";
//            if(finish_count>=plan_lot_count) lot_status="FINISH";
//            String lot_status2=lot_status;
//            if(old_plan_status.equals("FINISH")) lot_status2="FINISH2";
//            Update updateBigData = new Update();
//            updateBigData.set("lot_status", lot_status);
//            updateBigData.set("finish_count", finish_count);
//            updateBigData.set("finish_ok_count", finish_ok_count);
//            updateBigData.set("finish_ng_count", finish_ng_count);
//            if(finish_count==1){
//                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
//            }
//            if(lot_status.equals("FINISH")){
//                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
//            }
//            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," +
                    panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK";//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "DEMO异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更改任务完工状态以及查询完工明细
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateFinishStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateFinishStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanUpdateFinishStatus";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();

            //查询是否一批多车模式
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //1.查询当前是否存在进行中的任务
            Integer plan_all_count = 0;
            Integer finish_all_count = 0;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                String pallet_num2 = docItemBigData.getString("pallet_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                Integer inspect_count = docItemBigData.getInteger("inspect_count");
                Integer inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                Integer panel_model = docItemBigData.getInteger("panel_model");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("pallet_num", pallet_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("inspect_count", inspect_count);
                jbItem2.put("inspect_finish_count", inspect_finish_count);
                jbItem2.put("panel_model", panel_model);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
                plan_all_count += plan_lot_count2;
                finish_all_count += finish_ok_count2;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //判断是否需要更新
            Boolean isUpdTaskStatus = true;
            if (task_error_code_int < 4 && OneCarMultyLotFlag.equals("2")) {
                if (finish_all_count < plan_all_count) isUpdTaskStatus = false;
            }

            //更改完工任务状态
            if (isUpdTaskStatus) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "FINISH");
                updateBigData.set("lot_status", "FINISH");
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                updateBigData.set("task_error_code", task_error_code_int);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //更改任务完工状态以及查询完工明细,integratedFlag(一体机标识)，一体机收板机任务attribute1=Y
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateFinishStatus02", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateFinishStatus02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanUpdateFinishStatus02";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String integratedFlag = jsonParas.getString("integratedFlag");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();

            //查询是否一批多车模式
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //1.查询当前是否存在进行中的任务
            Integer plan_all_count = 0;
            Integer finish_all_count = 0;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            if (integratedFlag.equals("Y")) {
                queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));
            }
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                String pallet_num2 = docItemBigData.getString("pallet_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                Integer inspect_count = docItemBigData.getInteger("inspect_count");
                Integer inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("pallet_num", pallet_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("inspect_count", inspect_count);
                jbItem2.put("inspect_finish_count", inspect_finish_count);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
                plan_all_count += plan_lot_count2;
                finish_all_count += finish_ok_count2;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //判断是否需要更新
            Boolean isUpdTaskStatus = true;
            if (task_error_code_int < 4 && OneCarMultyLotFlag.equals("2")) {
                if (finish_all_count < plan_all_count) isUpdTaskStatus = false;
            }

            //更改完工任务状态
            if (isUpdTaskStatus) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "FINISH");
                updateBigData.set("lot_status", "FINISH");
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                updateBigData.set("task_error_code", task_error_code_int);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }


    //更改任务完工状态以及查询完工明细,返回备用属性1，2，3
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateFinishStatus03", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateFinishStatus03(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanUpdateFinishStatus03";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String task_error_code = jsonParas.getString("task_error_code");
            Integer task_error_code_int = Integer.parseInt(task_error_code);
            List<Map<String, Object>> itemList = new ArrayList<>();

            //查询是否一批多车模式
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //1.查询当前是否存在进行中的任务
            Integer plan_all_count = 0;
            Integer finish_all_count = 0;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                String pallet_num2 = docItemBigData.getString("pallet_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                Integer inspect_count = docItemBigData.getInteger("inspect_count");
                Integer inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                String attribute1 = docItemBigData.getString("attribute1");
                String attribute2 = docItemBigData.getString("attribute2");
                String attribute3 = docItemBigData.getString("attribute3");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("pallet_num", pallet_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("inspect_count", inspect_count);
                jbItem2.put("inspect_finish_count", inspect_finish_count);
                jbItem2.put("attribute1", attribute1);
                jbItem2.put("attribute2", attribute2);
                jbItem2.put("attribute3", attribute3);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
                plan_all_count += plan_lot_count2;
                finish_all_count += finish_ok_count2;
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //判断是否需要更新
            Boolean isUpdTaskStatus = true;
            if (task_error_code_int < 4 && OneCarMultyLotFlag.equals("2")) {
                if (finish_all_count < plan_all_count) isUpdTaskStatus = false;
            }

            //更改完工任务状态
            if (isUpdTaskStatus) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "FINISH");
                updateBigData.set("lot_status", "FINISH");
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
                updateBigData.set("task_error_code", task_error_code_int);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "更改任务完工状态以及查询完工明细异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //绑定天盖或者载具到任务
    @RequestMapping(value = "/EapCoreUnLoadPlanBindPallet", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanBindPallet(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanBindPallet";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_index = jsonParas.getString("port_index");
            String pallet_num = jsonParas.getString("pallet_num");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";

            //获取当前作业端口号
            String sqlPort = "select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id=" + station_id + " and port_index=" + port_index + "";
            List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
            if (lstPort == null || lstPort.size() <= 0) {
                errorMsg = "未能根据端口序号{" + port_index + "}查找到端口编号";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String port_code = lstPort.get(0).get("port_code").toString();

            //选择PLAN任务
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //更新
            if (!group_id.equals("")) {
                //计划表更新
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                Update updateBigData = new Update();
                updateBigData.set("pallet_num", pallet_num);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                //过站更新
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                updateBigData = new Update();
                updateBigData.set("pallet_num", pallet_num);
                mongoTemplate.updateMulti(queryBigData, updateBigData, meStationFlowTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "绑定天盖或者载具到任务异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更新板件Tray码
    @RequestMapping(value = "/EapCoreUnLoadPlanUpdateTray", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanUpdateTray(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanUpdateTray";
        String transResult = "";
        String errorMsg = "";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String station_flow_id = jsonParas.getString("station_flow_id");
            String tray_barcode = jsonParas.getString("tray_barcode");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_flow_id").is(station_flow_id));
            Update updateBigData = new Update();
            updateBigData.set("tray_barcode", tray_barcode);
            mongoTemplate.updateFirst(queryBigData, updateBigData, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "更新板件Tray码异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //获取当前正在执行的工单号
    @RequestMapping(value = "/EapCoreUnLoadPlanPortLotSelect", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanPortLotSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanPortLotSelect";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            String group_lot_num = "";
            long ngPanelCount = 0l;
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");

            /*
            //获取当前正在执行的端口号
            String aisMonitorModel=cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String tagPlcWorkPortIndex="";
            if(aisMonitorModel.equals("AIS-PC")){
                tagPlcWorkPortIndex="UnLoadPlc/PlcStatus/PlcWorkPortIndex";
            }
            else if(aisMonitorModel.equals("AIS-SERVER")){
                tagPlcWorkPortIndex="UnLoadPlc_"+station_code+"/PlcStatus/PlcWorkPortIndex";
            }
            else{
                errorMsg="未配置收板机系统参数AIS_MONITOR_MODE";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            JSONArray jsonArray= cFuncUtilsCellScada.ReadTagByStation(station_code,tagPlcWorkPortIndex);
            String port_index="";
            if(jsonArray!=null && jsonArray.size()>0){
                JSONObject jbItem=jsonArray.getJSONObject(0);
                port_index=jbItem.getString("tag_value");
            }
            if(port_index==null || port_index.equals("")){
                errorMsg="收板机PLC掉线,未能读取到当前作业端口序号";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if(port_index.equals("0")) port_index="1";
            //1.根据工位ID与端口序号查找端口号
            String sqlPort="select port_code " +
                    "from a_eap_fmod_station_port " +
                    "where station_id="+station_id+" and port_index="+port_index+"";
            List<Map<String, Object>> lstPort=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlPort,false,request,apiRoutePath);
            if(lstPort==null || lstPort.size()<=0){
                errorMsg= "未能根据PLC给出端口序号{"+port_index+"}查找到端口编号";
                transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String port_code=lstPort.get(0).get("port_code").toString();
             */


            //2.查询当前是否存在进行中的任务
            List<String> lstPlanId = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (lstPlanId.size() > 0) {
                //判断混板数量
                Integer panel_ng_code = 0;//只要不等于0则都为混板状态
                Query queryBigDataFlow = new Query();
                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigDataFlow.addCriteria(Criteria.where("panel_ng_code").ne(panel_ng_code));
                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                ngPanelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
            }
            group_lot_num = group_lot_num + "," + ngPanelCount;
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
        } catch (Exception ex) {
            errorMsg = "获取当前正在执行的工单号异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //接受上游设备通知切单信号
    @RequestMapping(value = "/EapCoreUnLoadRecvUpChangeLot", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadRecvUpChangeLot(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadRecvUpChangeLot";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String autoBackFlag = "N";//是否需要自动强制退载具
            String work_port_code = "";
            String work_port_index = "0";
            String station_id = jsonParas.getString("station_id");
            String lot_num = jsonParas.getString("lot_num");
            String checkResult = "2";//回应群翊下板机工单比对结果（1OK，2NG）
            //空批次不做处理
            if (lot_num == null || lot_num.equals("")) {
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null,
                        autoBackFlag + "," + work_port_index + "," + checkResult, "", 0);
                return transResult;
            }


            //判断是否启用了先进后出参数设置,若启用则不接受控制
            //1.1查询工位信息
            String sqlStation = "select station_code from sys_fmod_station where station_id=" + station_id;
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlStation, false, request, apiRoutePath);
            String station_code = lstStation.get(0).get("station_code").toString();
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String tagTaskOrderByUpDevice = "";
            if (aisMonitorModel.equals("AIS-PC")) {
                tagTaskOrderByUpDevice = "UnLoadAis/AisConfig/TaskOrderByUpDevice";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                tagTaskOrderByUpDevice = "UnLoadAis_" + station_code + "/AisConfig/TaskOrderByUpDevice";
            }
            JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, tagTaskOrderByUpDevice);
            if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                String tagTaskOrderByUpDeviceValue = jbItem.getString("tag_value");
                if (tagTaskOrderByUpDeviceValue.equals("")) {
                    errorMsg = "查询收板机工单任务生产顺序来源于上游设备状态时收板机PLC断网";
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                if (tagTaskOrderByUpDeviceValue.equals("0")) {
                    //不需要做处理
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null,
                            autoBackFlag + "," + work_port_index + "," + checkResult, "", 0);
                    return transResult;
                }
            } else {
                errorMsg = "未查询到收板机工单任务生产顺序来源于上游设备状态";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }


            //1.首先判断当前是否存在正在进行中的任务,并且获取当前任务号
            List<String> work_lstLotNum = new ArrayList<>();
            Integer work_target_lot_count = 0;
            Integer work_finish_count = 0;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                work_port_code = docItemBigData.getString("port_code");
                Integer target_lot_count = docItemBigData.getInteger("target_lot_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                work_target_lot_count += target_lot_count;
                work_finish_count += finish_count;
                String lot_num2 = docItemBigData.getString("lot_num");
                work_lstLotNum.add(lot_num2);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            //2.先判断当前给定的lot_num是否是work中，若是则返回不做处理
            if (work_lstLotNum.contains(lot_num)) {
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null,
                        autoBackFlag + "," + work_port_index + "," + checkResult, "", 0);
                return transResult;
            }
            //获取端口号
            if (work_port_code != null && !work_port_code.equals("")) {
                String sqlPort = "select port_index " +
                        "from a_eap_fmod_station_port " +
                        "where station_id=" + station_id + " and port_code='" + work_port_code + "' " +
                        "LIMIT 1 OFFSET 0";
                List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
                if (lstPort != null && lstPort.size() > 0) {
                    work_port_index = lstPort.get(0).get("port_index").toString();
                }
            }
            //3.判断是否为强制结批
            if (lot_num.equals("0")) {
                if (work_lstLotNum.size() > 0 && (work_target_lot_count > work_finish_count)) {
                    //必须强制结束批次,需要通过AIS发出强制结批信号到PLC
                    autoBackFlag = "Y";
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null,
                            autoBackFlag + "," + work_port_index + "," + checkResult, "", 0);
                    return transResult;
                }
                //正常让PLC去做结批即可
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null,
                        autoBackFlag + "," + work_port_index + "," + checkResult, "", 0);
                return transResult;
            }
            //4.若是做一些切换批次,先判断之前的是否要做强制结批动作
            if (work_lstLotNum.size() > 0 && !work_lstLotNum.contains(lot_num) && (work_target_lot_count > work_finish_count)) {
                autoBackFlag = "Y";//代表必须强制结批次
            }
            //5.更改当前工单状态为PLAN状态
            String now_group_lot_num = "";
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                now_group_lot_num = docItemBigData.getString("group_lot_num");
                iteratorBigData.close();
            }
            if (!now_group_lot_num.equals("")) {
                //做更新
                checkResult = "1";
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(now_group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "PLAN");
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null,
                    autoBackFlag + "," + work_port_index + "," + checkResult, "", 0);
        } catch (Exception ex) {
            errorMsg = "接受上游设备通知切单信号异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //根据母批号查询任务信息
    @RequestMapping(value = "/EapCoreUnLoadPlanInfoSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanInfoSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanInfoSel";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String group_lot_num = jsonParas.getString("group_lot_num");
            String station_id = jsonParas.getString("station_id");
            List<Map<String, Object>> itemList = new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            JSONArray jaLotFinish = new JSONArray();
            String group_id = "";
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String group_id2 = docItemBigData.getString("group_id");
                if (group_id == null || group_id.equals("")) {
                    group_id = group_id2;
                } else {
                    if (!group_id2.equals(group_id)) {
                        if (iteratorBigData.hasNext()) iteratorBigData.close();
                        break;
                    }
                }
                String lot_num = docItemBigData.getString("lot_num");
                Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count = docItemBigData.getInteger("target_lot_count");
                Integer finish_count = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                String pallet_num = docItemBigData.getString("pallet_num");
                mapBigDataRow.put("lot_num", lot_num);
                mapBigDataRow.put("plan_lot_count", plan_lot_count);
                mapBigDataRow.put("target_lot_count", target_lot_count);
                mapBigDataRow.put("finish_count", finish_count);
                mapBigDataRow.put("finish_ok_count", finish_ok_count);
                mapBigDataRow.put("finish_ng_count", finish_ng_count);
                mapBigDataRow.put("pallet_num", pallet_num);
                itemList.add(mapBigDataRow);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据母批号查询任务信息异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //查询当前正在作业的工单任务
    @RequestMapping(value = "/EapCoreUnLoadPlanFindWorkTask", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPlanFindWorkTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPlanFindWorkTask";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String group_lot_num = "";
            List<String> lstPlanId = new ArrayList<>();
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String port_index = jsonParas.getString("port_index");//当前扫描所属Port口排序
            if (port_index != null && !port_index.equals("")) {
                //获取当前作业端口号
                String sqlPort = "select port_code " +
                        "from a_eap_fmod_station_port " +
                        "where station_id=" + station_id + " and port_index=" + port_index + "";
                List<Map<String, Object>> lstPort = cFuncDbSqlExecute.ExecSelectSql(station_id, sqlPort, false, request, apiRoutePath);
                if (lstPort != null && lstPort.size() > 0)
                    port_code = lstPort.get(0).get("port_code").toString();
            }

            List<Map<String, Object>> itemList = new ArrayList<>();
            //1.查询当前是否存在进行中的任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            JSONArray jaLotFinish = new JSONArray();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_lot_num = docItemBigData.getString("group_lot_num");
                String plan_id = docItemBigData.getString("plan_id");
                String lot_num2 = docItemBigData.getString("lot_num");
                String pallet_num2 = docItemBigData.getString("pallet_num");
                Integer plan_lot_count2 = docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count2 = docItemBigData.getInteger("target_lot_count");
                Integer finish_count2 = docItemBigData.getInteger("finish_count");
                Integer finish_ok_count2 = docItemBigData.getInteger("finish_ok_count");
                Integer finish_ng_count2 = docItemBigData.getInteger("finish_ng_count");
                Integer inspect_count = docItemBigData.getInteger("inspect_count");
                Integer inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                JSONObject jbItem2 = new JSONObject();
                jbItem2.put("group_lot_num", group_lot_num);
                jbItem2.put("plan_id", plan_id);
                jbItem2.put("lot_num", lot_num2);
                jbItem2.put("pallet_num", pallet_num2);
                jbItem2.put("plan_lot_count", plan_lot_count2);
                jbItem2.put("target_lot_count", target_lot_count2);
                jbItem2.put("finish_count", finish_count2);
                jbItem2.put("finish_ok_count", finish_ok_count2);
                jbItem2.put("finish_ng_count", finish_ng_count2);
                jbItem2.put("inspect_count", inspect_count);
                jbItem2.put("inspect_finish_count", inspect_finish_count);
                jaLotFinish.add(jbItem2);
                lstPlanId.add(plan_id);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (group_lot_num == null || group_lot_num.equals("")) {
                selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
                return selectResult;
            }
            //查询panel明细
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").in(lstPlanId));
            //queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(50).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String item_date = sdf.format(docItemBigData.getDate("item_date"));
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                mapBigDataRow.put("panel_barcode", docItemBigData.getString("panel_barcode"));
                mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_index", docItemBigData.getInteger("panel_index"));
                mapBigDataRow.put("panel_status", docItemBigData.getString("panel_status"));
                mapBigDataRow.put("panel_ng_code", docItemBigData.getInteger("panel_ng_code"));
                mapBigDataRow.put("panel_ng_msg", docItemBigData.getString("panel_ng_msg"));
                mapBigDataRow.put("inspect_flag", docItemBigData.getString("inspect_flag"));
                mapBigDataRow.put("manual_judge_code", docItemBigData.getString("manual_judge_code"));
                mapBigDataRow.put("panel_flag", docItemBigData.getString("panel_flag"));
                mapBigDataRow.put("user_name", docItemBigData.getString("user_name"));
                mapBigDataRow.put("eap_flag", docItemBigData.getString("eap_flag"));
                mapBigDataRow.put("tray_barcode", docItemBigData.getString("tray_barcode"));
                mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                mapBigDataRow.put("dummy_flag", docItemBigData.getString("dummy_flag"));
                itemList.add(mapBigDataRow);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, jaLotFinish.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "查询当前正在作业的工单任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //存储板件信息到收板机
    @RequestMapping(value = "/EapCoreUnLoadPnlSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreUnLoadPnlSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/unload/EapCoreUnLoadPnlSave";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        String panel_barcode = "";
        try {
            String group_lot_num = jsonParas.getString("group_lot_num");
            panel_barcode = jsonParas.getString("panel_barcode");
            String plan_id = "";
            //选择任务
            String[] group_lot_status = new String[]{"WAIT", "PLAN", "WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                iteratorBigData.close();
            }
            if (!plan_id.equals("")) {
                List<Map<String, Object>> lstPlanBDocuments = new ArrayList<>();
                Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                String plan_d_id = CFuncUtilsSystem.CreateUUID(true);
                Map<String, Object> mapBigDataRowB = new HashMap<>();
                mapBigDataRowB.put("item_date", item_date);
                mapBigDataRowB.put("item_date_val", item_date_val);
                mapBigDataRowB.put("plan_d_id", plan_d_id);
                mapBigDataRowB.put("plan_id", plan_id);
                mapBigDataRowB.put("panel_barcode", panel_barcode);
                lstPlanBDocuments.add(mapBigDataRowB);
                if (lstPlanBDocuments.size() > 0)
                    mongoTemplate.insert(lstPlanBDocuments, apsPlanBTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, group_lot_num, "", 0);
        } catch (Exception ex) {
            errorMsg = "插入板件{" + panel_barcode + "}信息异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
