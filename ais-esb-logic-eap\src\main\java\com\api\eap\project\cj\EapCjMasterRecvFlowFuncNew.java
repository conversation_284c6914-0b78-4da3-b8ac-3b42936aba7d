package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定颖EAP接受流程功能函数(新版本)
 * 1.ProductionInfoDownload:(在线)接受EAP下发任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapCjMasterRecvFlowFuncNew {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapCjInterfCommon eapCjInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.(在线)接受EAP下发任务
    public JSONObject ProductionInfoDownload(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="ProductionInfoDownload";
        String esbInterfCode="ProductionInfoDownload";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        long station_id=0L;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String apsPlanTable="a_eap_aps_plan";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(0L,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();

            //3.批量读取TAG数据
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String OneCarMultyLotFlagTag="";
            String EapLHTParasTag="";
            String OnOffLineTag="";
            String UnLoadLinkFlagTag="";
            String InspectCountTag="";
            String WebUserIdTag="";
            String SysModelTag="";
            //值
            String OneCarMultyLotFlag="";//一批多车模式:0一车一批、1一车多批、2一批多车
            String EapLHTParas="";//EAP接口长宽高参数集合
            String OnOffLine="";//在线离线
            String UnLoadLinkFlag="";//投收联机模式
            Integer InspectCount=0;//首件数量
            String WebUserId="";//员工号
            String SysModel="";//本地远程
            String NgPanelManualFlag="";//扫描Panel的NG是否需要人工处理
            String NgPanelPassFlag="";//NG板件时强制放行标识(0不启用、1启用)
            if (aisMonitorModel.equals("AIS-PC")) {
                OneCarMultyLotFlagTag=station_attr+"Ais/AisConfig/OneCarMultyLotFlag";
                EapLHTParasTag=station_attr+"Ais/AisConfig/EapLHTParas";
                OnOffLineTag=station_attr+"Plc/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais/AisConfig/UnLoadLinkFlag";
                InspectCountTag=station_attr+"Eap/EapStatus/InspectCount";
                WebUserIdTag=station_attr+"Ais/AisStatus/WebUserId";
                SysModelTag=station_attr+"Plc/PlcConfig/SysModel";
                NgPanelManualFlag = station_attr+"Ais/AisConfig/NgPanelManualFlag";
                NgPanelPassFlag = station_attr+"Ais/AisConfig/NgPanelPassFlag";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                OneCarMultyLotFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/OneCarMultyLotFlag";
                EapLHTParasTag=station_attr+"Ais_"+station_code+"/AisConfig/EapLHTParas";
                OnOffLineTag=station_attr+"Plc_"+station_code+"/PlcConfig/OnOffLine";
                UnLoadLinkFlagTag=station_attr+"Ais_"+station_code+"/AisConfig/UnLoadLinkFlag";
                InspectCountTag=station_attr+"Eap_"+station_code+"/EapStatus/InspectCount";
                WebUserIdTag=station_attr+"Ais_"+station_code+"/AisStatus/WebUserId";
                SysModelTag=station_attr+"Plc_"+station_code+"/PlcConfig/SysModel";
                NgPanelManualFlag = station_attr+"Ais_"+station_code+"/AisConfig/NgPanelManualFlag";
                NgPanelPassFlag = station_attr+"Ais_"+station_code+"/AisConfig/NgPanelPassFlag";
            }
            else{
                code=-3;
                errorMsg="AIS需要配置系统参数{AIS_MONITOR_MODE}值为AIS-PC或者AIS-SERVER";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String sumReadTags=OneCarMultyLotFlagTag+","+EapLHTParasTag+","+OnOffLineTag+","+UnLoadLinkFlagTag+","+InspectCountTag+","+WebUserIdTag+","+SysModelTag;
            JSONArray jsonArrayTag= cFuncUtilsCellScada.ReadTagByStation(station_code,sumReadTags);
            if(jsonArrayTag!=null && jsonArrayTag.size()>0){
                for(int i=0;i<jsonArrayTag.size();i++){
                    JSONObject jbItem=jsonArrayTag.getJSONObject(i);
                    String tag_key=jbItem.getString("tag_key");
                    String tag_value=jbItem.getString("tag_value");
                    if(tag_key.equals(OneCarMultyLotFlagTag)) OneCarMultyLotFlag=tag_value;
                    if(tag_key.equals(EapLHTParasTag)) EapLHTParas=tag_value;
                    if(tag_key.equals(OnOffLineTag)) OnOffLine=tag_value;
                    if(tag_key.equals(UnLoadLinkFlagTag)) UnLoadLinkFlag=tag_value;
                    if(tag_key.equals(InspectCountTag)) InspectCount=Integer.parseInt(tag_value);
                    if(tag_key.equals(WebUserIdTag)) WebUserId=tag_value;
                    if(tag_key.equals(SysModelTag)) SysModel=tag_value;
                }
            }

            //3.1 判断是否断网
            if(OneCarMultyLotFlag==null || OneCarMultyLotFlag.equals("") ||
                    OnOffLine==null || OnOffLine.equals("") ||
                    UnLoadLinkFlag==null || UnLoadLinkFlag.equals("") ||
                    SysModel==null || SysModel.equals("")){
                code=-4;
                errorMsg="读取AIS配置参数时,SCADA断网,请重试";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.2 若是离线模式不接受任务
            if(!OnOffLine.equals("1")){
                code=-5;
                errorMsg="设备当前为离线模式,AIS拒绝接受工单下发";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(!SysModel.equals("1")){
                code=-5;
                errorMsg="设备当前为本地模式,AIS拒绝接受远程工单下发";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.3 若是收扳机,且是联机模式也拒绝接受任务
            if(station_attr.equals("UnLoad") && UnLoadLinkFlag.equals("1")){
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"仅作提醒,投收联机模式不需EAP下发任务");
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message","仅作提醒,投收联机模式不需EAP下发任务");
                return jbResult;
            }

            //3.4 判断EAP接口长宽高参数集合设置是否正确
            if(EapLHTParas==null || EapLHTParas.equals("")) EapLHTParas="S001,S002,S007";
            String[] panel_attr_list=EapLHTParas.split(",",-1);
            if(panel_attr_list==null || panel_attr_list.length<3){
                code=-6;
                errorMsg="未设置正确AIS配置参数{EAP接口长宽高参数集合}值";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String colname_length=panel_attr_list[0];//板长item_id名称
            String colname_width=panel_attr_list[1];//板宽item_id名称
            String colname_tickness=panel_attr_list[2];//板厚item_id名称

            //4.数据解析
            String download_type=jbRecvBody.getString("download_type");//下发类型
            String lot_num=jbRecvBody.getString("lot_id");//批次号
            String material_code=jbRecvBody.getString("prod_id");//產品代碼
            Integer plan_count=jbRecvBody.getInteger("lot_qty");//基板数量
            String lot_short_num=jbRecvBody.getString("lot_short_id");//批次短代码
            String lot_version=jbRecvBody.getString("prod_version");//版號
            String attribute1=jbRecvBody.getString("process_code");//製程代碼
            String attribute2=jbRecvBody.getString("use_in_name");//產品用途
            String layer=jbRecvBody.getString("layer");
            String step_seq=jbRecvBody.getString("step_seq");
            String route_type=jbRecvBody.getString("route_type");
            String rework_route_id=jbRecvBody.getString("rework_route_id");
            String rework_step_seq=jbRecvBody.getString("rework_step_seq");
            String pnl_side=jbRecvBody.getString("pnl_side");
            String spec_seq=jbRecvBody.getString("spec_seq");
            String spec_control=jbRecvBody.getString("spec_control");
            if(layer==null) layer="";
            if(step_seq==null) step_seq="";
            if(route_type==null) route_type="";
            if(rework_route_id==null) rework_route_id="";
            if(rework_step_seq==null) rework_step_seq="";
            if(pnl_side==null) pnl_side="";
            if(spec_seq==null) spec_seq="";
            if(spec_control==null) spec_control="";
            JSONObject jbElseAttr=new JSONObject();
            jbElseAttr.put("layer",layer);
            jbElseAttr.put("step_seq",step_seq);
            jbElseAttr.put("route_type",route_type);
            jbElseAttr.put("rework_route_id",rework_route_id);
            jbElseAttr.put("rework_step_seq",rework_step_seq);
            jbElseAttr.put("pnl_side",pnl_side);
            jbElseAttr.put("spec_seq",spec_seq);
            jbElseAttr.put("spec_control",spec_control);
            String attribute_else=jbElseAttr.toString();

            //D:BC请求主动删除
            if(download_type.equals("D")){
                Query queryBigDataDel = new Query();
                queryBigDataDel.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataDel.addCriteria(Criteria.where("lot_num").is(lot_num));
                queryBigDataDel.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                long planCountDel =  mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigDataDel.getQueryObject());
                if(planCountDel<=0){
                    code=-30;
                    errorMsg="工单{"+lot_num+"}正在执行中或者工单已经不存在,不允许删除";
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                    responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
                else{
                    mongoTemplate.remove(queryBigDataDel,apsPlanTable);
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS","EAP远程执行工单{"+lot_num+"}删除",5);
                    responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",true);
                    jbResult.put("message","删除成功");
                    return jbResult;
                }
            }

            //U:BC请求主动修改,先删除再新增
            if(download_type.equals("U")){
                Query queryBigDataUpd = new Query();
                queryBigDataUpd.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigDataUpd.addCriteria(Criteria.where("lot_num").is(lot_num));
                queryBigDataUpd.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                long planCountUpd =  mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigDataUpd.getQueryObject());
                if(planCountUpd<=0){
                    code=-31;
                    errorMsg="工单{"+lot_num+"}正在执行中或者工单已经不存在,不允许修改";
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                    responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
                else{
                    mongoTemplate.remove(queryBigDataUpd,apsPlanTable);
                }
            }

            //新增工单
            JSONObject lot_infos=jbRecvBody.getJSONObject("lot_infos");
            if(lot_infos==null){
                code=-7;
                errorMsg="lot_infos为null数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            JSONArray lot_attr_list=lot_infos.getJSONArray("lot");//item_id、item_value
            String lot_attr_list_str="[]";
            if(lot_attr_list==null || lot_attr_list.size()<=0){
                code=-8;
                errorMsg="lot_infos属性数据为空,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            lot_attr_list_str=lot_attr_list.toString();
            String panel_length="0";
            String panel_width="0";
            String panel_tickness="0";
            for(int i=0;i<lot_attr_list.size();i++){
                JSONObject jbItem=lot_attr_list.getJSONObject(i);
                String item_id=jbItem.getString("item_id");
                String item_value=jbItem.getString("item_value");
                if(item_id.equals(colname_length)) panel_length=item_value;
                else if(item_id.equals(colname_width)) panel_width=item_value;
                else if(item_id.equals(colname_tickness)) panel_tickness=item_value;
                else if (item_id.equals("S021")) {
                    //0：开启各种校验，1：读码强制放行    modified by chenru 2023-03-08 现场需求变化
                    if (item_value.equals("0") || item_value.equals("1")) {
                        String tagOnlyKeyList =  NgPanelPassFlag;
                        String tagValueList = "0";
                        if (item_value.equals("1")) {
                            tagValueList = "1";
                        }
                        errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
                        if (!errorMsg.equals("")) {
                            jbResult.put("responseParas",responseParas);
                            jbResult.put("successFlag",false);
                            jbResult.put("message",errorMsg);
                            return jbResult;
                        }
                    }
                }
            }
            if(panel_length==null || panel_length.equals("")) panel_length="0";
            if(panel_width==null || panel_width.equals("")) panel_width="0";
            if(panel_tickness==null || panel_tickness.equals("")) panel_tickness="0";
            if(Float.parseFloat(panel_length)<=0 || Float.parseFloat(panel_width)<=0){
                code=-9;
                errorMsg="EAP下发任务中板长{"+panel_length+"}与板宽{"+panel_width+"}小于等于0,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(lot_num==null || lot_num.equals("") || lot_short_num==null || lot_short_num.equals("") || plan_count==null || plan_count<=0){
                code=-10;
                errorMsg="下发任务中lot_id|lot_short_id|pnl_count参数字段不能为空或者不能为0";
                opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //4.1 判断是否存在重复的lot，若存在则不执行
            String[] group_lot_status_list=new String[]{"WAIT","PLAN","WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            long planCount =  mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
            if(planCount>0){
                String message="仅作提醒,工单{"+lot_num+"}已存在且未完成,EAP无需重复下发";
                responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,message);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message",message);
                return jbResult;
            }

            //4.2 若是一批多车时,若有放扳机任务未完结,此时不允许插入数据
            //modified by chenru20240327 收板机不考虑是否一批多车模式
            if(station_attr.equals("Load") && OneCarMultyLotFlag.equals("2")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                planCount =  mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
                if(planCount>0){
                    code=-11;
                    errorMsg="当前作业模式为一批多车模式,设备有尚未完成工单,不允许下发新的工单";
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                    responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
            }

            //若是DY-3Version以上版本则需要写入点位通知RCS已经在执行任务下发过程
            if(eapCjInterfCommon.CheckDyVersion(3)){
                errorMsg= opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Eap","EapStatus","EapDownTaskFlag",
                        "EAP","1",true);
                if(!errorMsg.equals("")){
                    code=-12;
                    opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
                    responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
            }

            //4.3 插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            String plan_id=CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("plan_id",plan_id);
            mapBigDataRow.put("station_id",station_id);
            mapBigDataRow.put("task_from","EAP");
            mapBigDataRow.put("group_lot_num","");
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("lot_short_num",lot_short_num);
            mapBigDataRow.put("lot_index",1);
            mapBigDataRow.put("plan_lot_count",plan_count);
            mapBigDataRow.put("target_lot_count",plan_count);
            mapBigDataRow.put("port_code","");
            mapBigDataRow.put("material_code",material_code);
            mapBigDataRow.put("pallet_num","");
            mapBigDataRow.put("pallet_type","");
            mapBigDataRow.put("lot_level",lot_version);
            mapBigDataRow.put("panel_length",Double.parseDouble(panel_length));
            mapBigDataRow.put("panel_width",Double.parseDouble(panel_width));
            mapBigDataRow.put("panel_tickness",Double.parseDouble(panel_tickness));
            mapBigDataRow.put("panel_model",-1);
            mapBigDataRow.put("inspect_count",InspectCount);
            mapBigDataRow.put("inspect_finish_count",0);
            mapBigDataRow.put("pdb_count",0);
            mapBigDataRow.put("pdb_rule",0);
            mapBigDataRow.put("fp_count",0);
            mapBigDataRow.put("group_lot_status","WAIT");
            mapBigDataRow.put("lot_status","PLAN");
            mapBigDataRow.put("finish_count",0);
            mapBigDataRow.put("finish_ok_count",0);
            mapBigDataRow.put("finish_ng_count",0);
            mapBigDataRow.put("in_finish_count",0);
            mapBigDataRow.put("in_finish_ok_count",0);
            mapBigDataRow.put("in_finish_ng_count",0);
            mapBigDataRow.put("out_finish_count",0);
            mapBigDataRow.put("out_finish_ok_count",0);
            mapBigDataRow.put("out_finish_ng_count",0);
            mapBigDataRow.put("task_error_code",0);
            mapBigDataRow.put("item_info",lot_attr_list_str);
            mapBigDataRow.put("task_start_time","");
            mapBigDataRow.put("task_end_time","");
            mapBigDataRow.put("task_cost_time",(long)0);
            mapBigDataRow.put("attribute1",attribute1);
            mapBigDataRow.put("attribute2",attribute2);
            mapBigDataRow.put("attribute3",attribute_else);
            mapBigDataRow.put("face_code",0);
            mapBigDataRow.put("pallet_use_count",0);
            mapBigDataRow.put("user_name",WebUserId);
            mapBigDataRow.put("group_id","");
            mapBigDataRow.put("offline_flag","N");
            mapBigDataRow.put("target_update_count",0);
            mapBigDataRow.put("pnl_infos","");
            mongoTemplate.insert(mapBigDataRow,apsPlanTable);

            responseParas=eapCjInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            opCommonFunc.SaveCimMessage(station_id,"0",esbInterfCode,"AIS",errorMsg,5);
            responseParas=eapCjInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
