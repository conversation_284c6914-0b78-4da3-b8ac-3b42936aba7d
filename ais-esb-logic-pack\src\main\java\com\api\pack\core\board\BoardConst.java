package com.api.pack.core.board;

import com.api.base.Const;
import com.api.pack.core.ccd.CCDConst;
import com.api.pack.core.sort.SortConst;

public interface BoardConst
{
    String BLANK = Const.BLANK;
    String OK = "OK";
    String NG = "NG";
    String NO_READ = "NoRead";

    String NONE = "None";
    String SINGLE = "Single";
    String DOUBLE = "Double";

    String SET = "Set";
    String PCS = "Pcs";
    String CHAR = "Char";
    String FRONT = "Front";
    String BACK = "Back";
    String CONTENT = "Content";
    String NUM = "Num";
    String MARK = "DirMarkChkRtl";
    String DIRECTION = "Direction";
    String MSG_LIST = "MsgList";
    String IS_XOUT = "IsXout";
    String QRC = "QRC";
    String QRC_LEVEL = "QRCLevel";
    String PART_NUMBER = "PartNumber";
    String DATE_CODE = "DateCode";
    String LOT_NUM = "LotNum";

    String FLAG_N = Const.FLAG_N;
    String FLAG_Y = Const.FLAG_Y;

    String VALUE_NC = "@NC@";
    String VALUE_NG = "@NG@";
    String VALUE_NULL = "@NULL@";
    String VALUE_NULL_1 = "@NULL1@";
    String VALUE_NULL_2 = "@NULL2@";
    String VALUE_NULL_3 = "@NULL3@";
    String VALUE_NULL_4 = "@NULL4@";

    String ORIENT_FRONT = "front";
    String ORIENT_BACK = "back";

    String SET_FRONT_CONTENT = SET + FRONT + CONTENT;
    String SET_BACK_CONTENT = SET + BACK + CONTENT;

    String CCD_CONTENT = CONTENT; // CCD线扫内容
    String CCD_CONTENT_TRANSMIT_ID = CCDConst.TRANSMIT_ID; // CCD线扫流水号
    String CCD_CONTENT_SET_XOUT_QTY = CCDConst.XOUT_QTY;
    String CCD_CONTENT_SET_LAYOUT_QTY = CCDConst.LAYOUT_QTY;
    String CCD_CONTENT_SET_CUSTOMER_QRC1 = CCDConst.CUSTOMER_QRC1; // 客户QRC1
    String CCD_CONTENT_SET_CUSTOMER_QRC2 = CCDConst.CUSTOMER_QRC2; // 客户QRC2
    String CCD_CONTENT_SET_REPAIR_LABEL = CCDConst.REPAIR_LABEL; // 修复标签
    String CCD_CONTENT_SET_FLOW_ERROR = CCDConst.FLOW_ERROR; // 流程错误
    String CCD_CONTENT_SET_FLOW_ERROR_MSG = CCDConst.FLOW_ERROR_MSG; // 流程错误信息

    String STATUS_OK = OK;
    String STATUS_NG = NG;

    Integer DEPOSIT_POSITION_OK = 1;
    Integer DEPOSIT_POSITION_NG = 2;

    int CODE_NG = -1;
    int CODE_OK = 0;

    int TURN_1 = 1;
    int TURN_2 = 2;

    int RESULT_OK = 1;
    int RESULT_X_OUT = 2;
    int RESULT_NG = 3;

    String SORT_CODE_XOUT_NP = SortConst.CODE_XOUT_NP;
    String SORT_CYCLE_PERIOD = SortConst.CYCLE_PERIOD;
    String SORT_BATCH_NUMBER = SortConst.BATCH_NUMBER;
    String SORT_BOARD_DIRECTION = SortConst.BOARD_DIRECTION;
    String SORT_BOARD_F2B =  SortConst.BOARD_F2B;
    String SORT_BOARD_MAPPING = SortConst.BOARD_MAPPING;

    String BOARD_TYPE_NONE = NONE;
    String BOARD_TYPE_SINGLE = SINGLE;
    String BOARD_TYPE_DOUBLE = DOUBLE;

    String BOARD_PROPERTY_BARCODE = "boardBarcode"; // 板件条码
    String BOARD_PROPERTY_LEVEL = "boardLevel"; // 板件等级
    String BOARD_PROPERTY_MARK = "boardMark"; // 板件标记/板件光学点标记
    String BOARD_PROPERTY_DIRECTION = "boardDirection"; // 板件方向
    String BOARD_PROPERTY_LAYOUT_NUMBER = "boardLayoutNumber"; // 板件排版数
    String BOARD_PROPERTY_XOUT_ACTUAL_NUMBER = "xoutActualNumber"; // 板件Xout实际数量
    String BOARD_PROPERTY_REPAIR_LABEL = "repairLabel"; // 修复标签
    String BOARD_PROPERTY_FLOW_ERROR = "flowError"; // 流程异常标记
    String BOARD_PROPERTY_FLOW_ERROR_MSG = "flowErrorMsg"; // 流程异常信息

    String[] SET_BOARD_INITIAL_PROPERTIES = new String[]{
            BOARD_PROPERTY_BARCODE,
            BOARD_PROPERTY_LEVEL,
            BOARD_PROPERTY_MARK,
            BOARD_PROPERTY_DIRECTION,
            BOARD_PROPERTY_LAYOUT_NUMBER,
            BOARD_PROPERTY_XOUT_ACTUAL_NUMBER,
            BOARD_PROPERTY_REPAIR_LABEL,
            BOARD_PROPERTY_FLOW_ERROR,
            BOARD_PROPERTY_FLOW_ERROR_MSG
    };

    String BOARD_COMPARE_MESSAGES_PACK_COMPARE_RULES_F2B = SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_F2B;
    String BOARD_COMPARE_MESSAGES_PACK_NOT_MATCH_ALL = SortConst.COMPARE_MESSAGES_PACK_NOT_MATCH_ALL;
    String BOARD_COMPARE_MESSAGES_PACK_FORMAT_ERROR = SortConst.COMPARE_MESSAGES_PACK_FORMAT_ERROR;
    String BOARD_COMPARE_MESSAGES_PACK_COMPARE_RULES_F2B_NOT_MATCH = SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_F2B_NOT_MATCH;
    String BOARD_COMPARE_MESSAGES_PACK_COMPARE_RULES_XNP = SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XNP;

    static String convertValue(String value)
    {
        return SortConst.convertValue(value);
    }
}
