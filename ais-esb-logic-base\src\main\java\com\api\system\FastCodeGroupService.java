package com.api.system;

import com.api.base.Const;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class FastCodeGroupService extends ServiceImpl<FastCodeGroupMapper, FastCodeGroup> implements IService<FastCodeGroup>
{
    private final FastCodeGroupMapper mapper;
    private final FastCodeMapper childMapper;

    public FastCodeGroupService(FastCodeGroupMapper mapper, FastCodeMapper childMapper)
    {
        this.mapper = mapper;
        this.childMapper = childMapper;
    }

    public FastCodeGroup getByCode(String code)
    {
        QueryWrapper<FastCodeGroup> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(Const.PROPERTY_ENABLE_FLAG,  Const.FLAG_Y);
        queryWrapper.eq("fastcode_group_code", code);
        FastCodeGroup t = mapper.selectOne(queryWrapper);
        if (t != null)
        {
            t.setChildren(FastCode.listByParentId(t.getId(), childMapper));
        }
        return t;
    }
}
