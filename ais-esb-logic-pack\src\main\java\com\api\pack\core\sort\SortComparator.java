package com.api.pack.core.sort;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.base.Const;
import com.api.pack.core.board.SETBoard;
import com.api.pack.core.board.SETBoardService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.MessageSource;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import java.util.Arrays;
import java.util.Locale;

public interface SortComparator
{
    default String getPackNoDetectedMessage(String param1, String param2, MessageSource messageSource)
    {
        return messageSource.getMessage(SortConst.COMPARE_MESSAGES_PACK_NO_DETECTED, new Object[]{
                param1, param2
        }, Locale.getDefault());
    }

    default String getPackNotMatchMessage(String param1, String param2, Object param3, Object param4, MessageSource messageSource)
    {
        return messageSource.getMessage(SortConst.COMPARE_MESSAGES_PACK_NOT_MATCH, new Object[]{
                param1, param2, String.valueOf(param3), String.valueOf(param4)
        }, Locale.getDefault());
    }

    default String getPackNotConfiguredMessage(String param1, MessageSource messageSource)
    {
        return messageSource.getMessage(SortConst.COMPARE_MESSAGES_PACK_NOT_CONFIGURED, new Object[]{
                param1
        }, Locale.getDefault());
    }

    default String getPackUnconformityMessage(String param1, String param2, MessageSource messageSource)
    {
        return messageSource.getMessage(SortConst.COMPARE_MESSAGES_PACK_UNCONFORMITY, new Object[]{
                param1, param2
        }, Locale.getDefault());
    }

    default String getPackUnconformityMessageWithValues(String param1, String param2, Object param3, Object param4, MessageSource messageSource)
    {
        return messageSource.getMessage(SortConst.COMPARE_MESSAGES_PACK_UNCONFORMITY_WITH_VALUES, new Object[]{
                param1, param2, String.valueOf(param3), String.valueOf(param4)
        }, Locale.getDefault());
    }

    default void compare(SortCompareOriginEvent event) throws SortCompareException
    {
        throw new RuntimeException("not implemented");
    }

    default SortCompareResult compare(SortCompareData data, SortCompareRule sortCompareRule, MessageSource messageSource)
    {
        if (data.isNC())
        {
            return new SortCompareResult(SortConst.STATUS_OK, SortConst.CODE_OK, SortConst.BLANK);
        }
        if (data.isNULL())
        {
            String errMessage = this.getPackNoDetectedMessage(data.getName(), sortCompareRule.getName(), messageSource);
            return new SortCompareResult(SortConst.STATUS_NG, SortConst.CODE_NG, errMessage);
        }
        if (!ObjectUtils.isEmpty(sortCompareRule.getSourceValue()))
        {
            if (sortCompareRule.getSourceValue().equals(data.getValue()))
            {
                return new SortCompareResult(SortConst.STATUS_OK, SortConst.CODE_OK, SortConst.BLANK);
            }
            String errMessage = this.getPackUnconformityMessage(data.getName() + sortCompareRule.getName(), sortCompareRule.getName(), messageSource);
            return new SortCompareResult(SortConst.STATUS_NG, SortConst.CODE_NG, errMessage);
        }
        return null;
    }

    default SortCompareResult compare(SortCompareData data, SortSplitRule.Item splitRule, SortCompareRule sortCompareRule, MessageSource messageSource)
    {
        SortCompareResult compareResult = this.compare(data, sortCompareRule, messageSource);
        if (compareResult != null)
        {
            return compareResult;
        }
        String dataValue = data.getValue();
        String dataName = data.getName();
        dataValue = splitRule.processOfSrc(dataName, dataValue);
        try
        {
            dataValue = splitRule.processOfSrc(dataName, dataValue);
        }
        catch (SortSplitRuleException ex)
        {
            return new SortCompareResult(SortConst.STATUS_NG, SortConst.CODE_NG, ex.getMessage());
        }
        sortCompareRule.setSourceValue(dataValue);
        return sortCompareRule.process(dataName);
    }

    default SortCompareResult compare(SortCompareData srcData, SortCompareData dstData, SortSplitRule.Item sortSplitRule, SortCompareRule sortCompareRule, MessageSource messageSource)
    {
        SortCompareResult srcCompareResult = this.compare(srcData, sortCompareRule, messageSource);
        if (srcCompareResult != null)
        {
            return srcCompareResult;
        }
        SortCompareResult dstCompareResult = this.compare(dstData, sortCompareRule, messageSource);
        if (dstCompareResult != null)
        {
            return dstCompareResult;
        }
        try
        {
            String srcDataValue = srcData.getValue();
            String srcDataName = srcData.getName();
            srcDataValue = sortSplitRule.processOfSrc(srcDataName, srcDataValue);
            sortCompareRule.setSourceValue(srcDataValue);
            String dstDataValue = dstData.getValue();
            String dstDataName = dstData.getName();
            dstDataValue = sortSplitRule.processOfDst(dstDataName, dstDataValue);
            sortCompareRule.setTargetValue(dstDataValue);
            String ruleName = sortCompareRule.getName();
            String srcName = srcDataName + ruleName;
            String dstName = dstDataName + ruleName;
            String errMessage = this.getPackUnconformityMessageWithValues(srcName, srcDataValue, dstName, dstDataValue, messageSource);
            return sortCompareRule.process(dstDataName, errMessage);
        }
        catch (SortSplitRuleException ex)
        {
            return new SortCompareResult(SortConst.STATUS_NG, SortConst.CODE_NG, ex.getMessage());
        }
    }

    /**
     * 默认比较器：根据规则比较来源数据与目标数据
     */
    @Data
    @AllArgsConstructor
    @Slf4j
    @Component
    class Default implements SortComparator
    {
        private final MessageSource messageSource;

        @EventListener(condition = "#event.check() && #event.source.getRule().isDefault()")
        public void compare(SortCompareOriginEvent event) throws SortCompareException
        {
            SortCompareOrigin origin = event.getSource();
            String srcCategory = origin.getSrc().getCategory();
            String srcOrient = origin.getSrc().getOrient();
            Integer srcIndex = origin.getSrc().getIndex();
            Object srcBoard = origin.getSrc().getMapping();
            String dstCategory = origin.getDst().getCategory();
            String dstOrient = origin.getDst().getOrient();
            Integer dstIndex = origin.getDst().getIndex();
            Object dstBoard = origin.getDst().getMapping();
            SortSplitRule.Item rule = origin.getRule();
            SortCompareRule compareRule = new SortCompareRule(rule.getName());
            SortCompareData srcSortCompareData =
                    srcIndex != null ? new SortCompareData(srcCategory, srcOrient, srcBoard, rule.getSrcKey(), srcIndex)
                                     : new SortCompareData(srcCategory, srcOrient, srcBoard, rule.getSrcKey());
            SortCompareData dstSortCompareData =
                    dstIndex != null ? new SortCompareData(dstCategory, dstOrient, dstBoard, rule.getDstKey(), dstIndex)
                                     : new SortCompareData(dstCategory, dstOrient, dstBoard, rule.getDstKey());
            if (dstSortCompareData.getValue() == null || Const.NULL.equals(dstSortCompareData.getValue())) // 目标数据为空时，检查计划
            {
                dstSortCompareData = new SortCompareData(srcCategory, srcOrient, origin.getPlan(), rule.getSrcKey());
                if (dstSortCompareData.getValue() == null || Const.NULL.equals(dstSortCompareData.getValue())) // 计划数据为空时，跳过检查
                {
                    return;
                }
            }
            SortCompareResult sortCompareResult = this.compare(srcSortCompareData, dstSortCompareData, rule, compareRule, this.messageSource);
            if (sortCompareResult.isNG())
            {
                throw new SortCompareException(sortCompareResult.getNgMsg(), sortCompareResult.getNgCode());
            }
        }
    }

    /**
     * XOut比较器
     * 当HasDirMark为true
     * 1、IsXout为ture DirMark为NG  正确
     * 2、IsXout为false DirMark为OK   正确
     * 如果HasDirMark为false 则无论其他两个是什么都是错误的
     */
    @Data
    @AllArgsConstructor
    @Slf4j
    @Component
    class XOut implements SortComparator
    {
        private final MessageSource messageSource;

        @EventListener(condition = "#event.checkXOut()")
        public void compare(SortCompareOriginEvent event) throws SortCompareException
        {
            SortCompareOrigin origin = event.getSource();
            SortSplitRule.Item rule = origin.getRule();
            String srcCategory = origin.getSrc().getCategory() != null ? origin.getSrc().getCategory()
                                                                       : origin.getDst().getCategory();
            Integer srcIndex =
                    origin.getSrc().getIndex() != null ? origin.getSrc().getIndex() : origin.getDst().getIndex();
            String boardName = srcCategory + (srcIndex != null ? "[" + srcIndex + "]" : SortConst.BLANK);
            // 正面板数据
            JSONObject frontBoard = origin.getSrc().getMapping();
            // 反面板数据
            JSONObject backBoard = origin.getDst().getMapping();
            Boolean frontIsXOut = frontBoard.getBoolean(SortConst.CCD_CONTENT_ALL_IS_X_OUT);
            Boolean backIsXOut = backBoard.getBoolean(SortConst.CCD_CONTENT_ALL_IS_X_OUT);
            String frontMark = frontBoard.getString(SortConst.CCD_CONTENT_ALL_MARK);
            frontMark = SortConst.convertValue(frontMark);
            String backMark = backBoard.getString(SortConst.CCD_CONTENT_ALL_MARK);
            backMark = SortConst.convertValue(backMark);
            Boolean frontBoardHasDirMark = frontBoard.getBoolean(SortConst.CCD_CONTENT_ALL_HAS_DIR_MARK);
            Boolean backBoardHasDirMark = backBoard.getBoolean(SortConst.CCD_CONTENT_ALL_HAS_DIR_MARK);
            if ((frontBoardHasDirMark != null && !frontBoardHasDirMark) || (backBoardHasDirMark != null && !backBoardHasDirMark))
            {
                throw new SortCompareException(
                        messageSource.getMessage(
                                SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_DIR_MARK_IS_INVALID,
                                new Object[]{boardName, frontBoardHasDirMark, backBoardHasDirMark},
                                Locale.getDefault()
                        )
                );
            }
            if (Boolean.TRUE.equals(frontIsXOut) || Boolean.TRUE.equals(backIsXOut))
            {
                if (Boolean.TRUE.equals(frontIsXOut) && Boolean.FALSE.equals(backIsXOut))
                {
                    String errMessage = messageSource.getMessage(
                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_FOR_FRONT,
                            new Object[]{boardName},
                            Locale.getDefault()
                    );
                    throw new SortCompareException(errMessage);
                }
                else if (Boolean.FALSE.equals(frontIsXOut))
                {
                    String errMessage = messageSource.getMessage(
                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_FOR_BACK,
                            new Object[]{boardName},
                            Locale.getDefault()
                    );
                    throw new SortCompareException(errMessage);
                }
                if (Boolean.TRUE.equals(frontIsXOut) && SortConst.STATUS_OK.equals(frontMark))
                {
                    String errMessage = messageSource.getMessage(
                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_MARK_IS_OK,
                            new Object[]{boardName},
                            Locale.getDefault()
                    );
                    throw new SortCompareException(errMessage);
                }
                if (Boolean.TRUE.equals(backIsXOut) && SortConst.STATUS_OK.equals(backMark))
                {
                    String errMessage = messageSource.getMessage(
                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_MARK_IS_OK,
                            new Object[]{boardName},
                            Locale.getDefault()
                    );
                    throw new SortCompareException(errMessage);
                }
            }
            if (SortConst.BOARD_CATEGORY_SET.toUpperCase().equals(srcCategory) && !ObjectUtils.isEmpty(rule.getSrcKey()))
            {
                try
                {
                    JSONArray frontPcsArr = frontBoard.getJSONArray(SortConst.CCD_CONTENT_SET_PCS_MSG_LIST);
                    JSONArray backPcsArr = backBoard.getJSONArray(SortConst.CCD_CONTENT_SET_PCS_MSG_LIST);
                    if (frontPcsArr.size() != backPcsArr.size())
                    {
                        String errMessage = messageSource.getMessage(
                                SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_OF_PCS,
                                new Object[]{boardName, frontPcsArr.size(), backPcsArr.size()},
                                Locale.getDefault()
                        );
                        throw new SortCompareException(errMessage);
                    }
                    int xOutQty = 0;
                    for (int i = 0; i < frontPcsArr.size(); i++)
                    {
                        int pcsIndex = i + 1;
                        // 正面板数据
                        JSONObject pcsFront = frontPcsArr.getJSONObject(i);
                        String pcsFrontBoardName = SortConst.BOARD_CATEGORY_PCS + "[" + pcsIndex + "]";
                        Boolean pcsFrontIsXOut = pcsFront.getBoolean(SortConst.CCD_CONTENT_ALL_IS_X_OUT);
                        String pcsFrontDirMarkRtl = pcsFront.getString(SortConst.CCD_CONTENT_ALL_MARK);
                        Boolean pcsFrontHasDirMark = pcsFront.getBoolean(SortConst.CCD_CONTENT_ALL_HAS_DIR_MARK);
                        // 反面板数据
                        JSONObject pcsBack = backPcsArr.getJSONObject(i);
                        String pcsBackBoardName = SortConst.BOARD_CATEGORY_PCS + "[" + pcsIndex + "]";
                        Boolean pcsBackIsXOut = pcsBack.getBoolean(SortConst.CCD_CONTENT_ALL_IS_X_OUT);
                        String pcsBackDirMarkRtl = pcsBack.getString(SortConst.CCD_CONTENT_ALL_MARK);
                        Boolean pcsBackHasDirMark = pcsBack.getBoolean(SortConst.CCD_CONTENT_ALL_HAS_DIR_MARK);

                        if (Boolean.TRUE.equals(pcsFrontIsXOut) && Boolean.TRUE.equals(pcsBackIsXOut))
                        {
                            xOutQty++;
                        }
                        else if (SortConst.STATUS_NG.equals(pcsFrontDirMarkRtl))
                        {
                            String errMessage = messageSource.getMessage(
                                    SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_MARK_IS_NG,
                                    new Object[]{pcsFrontBoardName},
                                    Locale.getDefault()
                            );
                            throw new SortCompareException(errMessage);
                        }
                        else if (SortConst.STATUS_NG.equals(pcsBackDirMarkRtl))
                        {
                            String errMessage = messageSource.getMessage(
                                    SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_MARK_IS_NG,
                                    new Object[]{pcsBackBoardName},
                                    Locale.getDefault()
                            );
                            throw new SortCompareException(errMessage);
                        }
                        if (pcsFrontHasDirMark != null && !pcsFrontHasDirMark)
                        {
                            String errMessage = messageSource.getMessage(
                                    SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_DIR_MARK_IS_INVALID,
                                    new Object[]{pcsFrontBoardName},
                                    Locale.getDefault()
                            );
                            throw new SortCompareException(errMessage);
                        }
                        if (pcsBackHasDirMark != null && !pcsBackHasDirMark)
                        {
                            String errMessage = messageSource.getMessage(
                                    SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_DIR_MARK_IS_INVALID,
                                    new Object[]{pcsBackBoardName},
                                    Locale.getDefault()
                            );
                            throw new SortCompareException(errMessage);
                        }
                    }
                    String xOutRuleVal = rule.getSrcKey();
                    int xOutRuleValInt = Integer.parseInt(xOutRuleVal.replace("X", ""));
                    Integer frontXOutQty = frontBoard.getInteger(SortConst.CCD_CONTENT_SET_X_OUT_QTY);
                    Integer backXOutQty = backBoard.getInteger(SortConst.CCD_CONTENT_SET_X_OUT_QTY);
                    if (frontXOutQty != null && backXOutQty != null && !frontXOutQty.equals(backXOutQty))
                    {
                        String errMessage = messageSource.getMessage(
                                SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH,
                                new Object[]{boardName, frontXOutQty, backXOutQty},
                                Locale.getDefault()
                        );
                        throw new SortCompareException(errMessage);
                    }

                    if (frontXOutQty != null)
                    {
                        if (frontXOutQty != xOutQty)
                        {
                            String errMessage = messageSource.getMessage(
                                    SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_FOR_ACTUAL,
                                    new Object[]{boardName, frontXOutQty, xOutQty},
                                    Locale.getDefault()
                            );
                            throw new SortCompareException(errMessage);
                        }
                        else if (frontXOutQty != xOutRuleValInt)
                        {
                            String errMessage = messageSource.getMessage(
                                    SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_FOR_RULE,
                                    new Object[]{boardName, frontXOutQty, xOutRuleVal},
                                    Locale.getDefault()
                            );
                            throw new SortCompareException(errMessage);
                        }
                    }
                }
                catch (NumberFormatException ex)
                {
                    String errMessage = messageSource.getMessage(
                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_RULE_IS_INVALID,
                            new Object[]{boardName},
                            Locale.getDefault()
                    );
                    throw new SortCompareException(errMessage);
                }
            }
        }
    }

    /**
     * 方向比较器
     */
    @Data
    @AllArgsConstructor
    @Slf4j
    @Component
    class Direction implements SortComparator
    {
        private final MessageSource messageSource;

        @EventListener(condition = "#event.checkDirection()")
        public void compare(SortCompareOriginEvent event) throws SortCompareException
        {
            SortCompareOrigin origin = event.getSource();
            String boardType =
                    origin.getSrc().getType() != null ? origin.getSrc().getType() : origin.getDst().getType();
            SortSplitRule.Item rule = origin.getRule();
            String boardCategory = origin.getSrc().getCategory() != null ? origin.getSrc().getCategory()
                                                                         : origin.getDst().getCategory();
            Integer boardIndex =
                    origin.getSrc().getIndex() != null ? origin.getSrc().getIndex() : origin.getDst().getIndex();
            String boardName = boardCategory + (boardIndex != null ? "[" + boardIndex + "]" : SortConst.BLANK);
            // 正面板数据
            JSONObject frontBoard = origin.getSrc().getMapping();
            // 反面板数据
            JSONObject backBoard = origin.getDst().getMapping();
            String boardDirection;
            String frontBoardDirection = SortConst.convertValue(frontBoard.getString(SortConst.CCD_CONTENT_ALL_DIRECTION));
            String backBoardDirection = SortConst.convertValue(backBoard.getString(SortConst.CCD_CONTENT_ALL_DIRECTION));
            if (SortConst.BOARD_SIDE_DOUBLE.equals(boardType))
            {
                // 正反面板方向不一致
                if (!frontBoardDirection.equals(backBoardDirection))
                {
                    throw new SortCompareException(
                            messageSource.getMessage(
                                    SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_DIRECTION_NOT_MATCH,
                                    new Object[]{boardName, frontBoardDirection, backBoardDirection},
                                    Locale.getDefault()
                            )
                    );
                }
                boardDirection = frontBoardDirection;
            }
            else
            {
                boardDirection = !ObjectUtils.isEmpty(frontBoardDirection) ? frontBoardDirection : backBoardDirection;
            }
            // SET正面（或反面）初定位识别异常请前往CCD系统查看
            SortCompareRule rule1 = new SortCompareRule(rule.getName(), boardDirection, SortConst.VALUE_NULL_1);
            String rule1Msg = String.format("%s定位训练异常，请前往CCD系统查看", boardName);
            if (rule1.process(boardName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new SortCompareException(rule1Msg);
            }
            // SET正面（或反面）板件放反导致异常
            SortCompareRule rule2 = new SortCompareRule(rule.getName(), boardDirection, SortConst.VALUE_NULL_2);
            String rule2Msg = String.format("%s产品特征异常，请前往CCD系统查看", boardName);
            if (rule2.process(boardName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new SortCompareException(rule2Msg);
            }
            // SET正面（或反面）产品训练异常
            SortCompareRule rule3 = new SortCompareRule(rule.getName(), boardDirection, SortConst.VALUE_NULL_3);
            String rule3Msg = String.format("%s产品训练异常，请前往CCD系统查看", boardName);
            if (rule3.process(boardName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new SortCompareException(rule3Msg);
            }
            // CCD手动NG过板
            SortCompareRule rule4 = new SortCompareRule(rule.getName(), boardDirection, SortConst.VALUE_NULL_4);
            String rule4Msg = "CCD手动释放NG板";
            if (rule4.process(boardName).isOK()) // 规则匹配成功时说明存在此异常
            {
                throw new SortCompareException(rule4Msg);
            }
        }
    }

    /**
     * 等级比较器
     */
    @Data
    @AllArgsConstructor
    @Slf4j
    @Component
    class Level implements SortComparator
    {
        private final MessageSource messageSource;

        @EventListener(condition = "#event.checkLevel()")
        public void compare(SortCompareOriginEvent event) throws SortCompareException
        {
            SortCompareOrigin origin = event.getSource();
            String boardType =
                    origin.getSrc().getType() != null ? origin.getSrc().getType() : origin.getDst().getType();
            if (SortConst.BOARD_SIDE_NONE.equals(boardType))
            {
                return;
            }
            SortSplitRule.Item rule = origin.getRule();
            String boardCategory = origin.getSrc().getCategory() != null ? origin.getSrc().getCategory()
                                                                         : origin.getDst().getCategory();
            // 类别首字母大写，其他小写
            String boardCategoryCamel = boardCategory.substring(0, 1).toUpperCase() + boardCategory.substring(1).toLowerCase();
            Integer boardIndex =
                    origin.getSrc().getIndex() != null ? origin.getSrc().getIndex() : origin.getDst().getIndex();
            String boardName = boardCategory + (boardIndex != null ? "[" + boardIndex + "]" : SortConst.BLANK);
            // 正面板数据
            JSONObject frontBoard = origin.getSrc().getMapping();
            // 反面板数据
            JSONObject backBoard = origin.getDst().getMapping();
            String boardLevel = SortConst.LEVEL_F;
            int boardLevelInt = 0;
            String frontLevel = frontBoard.getString(boardCategoryCamel + SortConst.CCD_CONTENT_ALL_QRC_LEVEL);
            int frontLevelInt = this.convertInt(frontLevel);
            String backLevel = backBoard.getString(boardCategoryCamel + SortConst.CCD_CONTENT_ALL_QRC_LEVEL);
            int backLevelInt = this.convertInt(backLevel);
            String targetLevel = rule.getSrcKey();
            int targetLevelInt = this.convertInt(targetLevel);
            boolean checkResult = true;
            if (SortConst.BOARD_SIDE_DOUBLE.equals(boardType))
            {
                // 不判断双面板等级是否一致（湖北健鼎项目） modified by jay-y 2024/11/15
//                if (frontLevelInt != backLevelInt)
//                {
//                    String errMessage = messageSource.getMessage(
//                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_LEVEL_NOT_MATCH,
//                            new Object[]{boardName, frontLevel, backLevel},
//                            Locale.getDefault()
//                    );
//                    throw new SortCompareException(errMessage);
//                }
                if (frontLevelInt > targetLevelInt)
                {
                    boardLevel = frontLevel;
                    checkResult = false;
                }
                else if (backLevelInt > targetLevelInt)
                {
                    boardLevel = backLevel;
                    checkResult = false;
                }
            }
            else if (SortConst.BOARD_SIDE_SINGLE.equals(boardType))
            {
                if (frontLevelInt > 0)
                {
                    boardLevel = frontLevel;
                    boardLevelInt = frontLevelInt;
                }
                else
                {
                    boardLevel = backLevel;
                    boardLevelInt = backLevelInt;
                }
                // 判断Level等级
                if (boardLevelInt > targetLevelInt)
                {
                    checkResult = false;
                }
            }
            // 判断Level等级
            if (!checkResult)
            {
                String errMessage = messageSource.getMessage(
                        SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_LEVEL_GREATER_THAN_STAND,
                        new Object[]{boardName, boardLevel, targetLevel},
                        Locale.getDefault()
                );
                throw new SortCompareException(errMessage);
            }
        }

        // 等级转换
        private int convertInt(String level)
        {
            int levelInt = 0;
            if (ObjectUtils.isEmpty(level))
            {
                levelInt = 6;
                return levelInt;
            }
            switch (level)
            {
                case SortConst.VALUE_NC:
                    break;
                case SortConst.LEVEL_A:
                    levelInt = 1;
                    break;
                case SortConst.LEVEL_B:
                    levelInt = 2;
                    break;
                case SortConst.LEVEL_C:
                    levelInt = 3;
                    break;
                case SortConst.LEVEL_D:
                    levelInt = 4;
                    break;
                case SortConst.LEVEL_E:
                    levelInt = 5;
                    break;
                default:
                    levelInt = 6;
                    break;
            }
            return levelInt;
        }
    }

    /**
     * 板件条码比较器（条码长度/大小写/是否重码）
     */
    @Data
    @AllArgsConstructor
    @Slf4j
    @Component
    class BoardBarcode implements SortComparator
    {
        private final MessageSource messageSource;

        private final SETBoardService setBoardService;

        @EventListener(condition = "#event.checkBarcodeLength() || #event.checkBarcodeCase() || #event.checkRepeatedCode() || event.checkETPass()")
        public void compare(SortCompareOriginEvent event) throws SortCompareException
        {
            SortCompareOrigin origin = event.getSource();
            String boardType =
                    origin.getSrc().getType() != null ? origin.getSrc().getType() : origin.getDst().getType();
            if (SortConst.BOARD_SIDE_NONE.equals(boardType))
            {
                return;
            }
            String boardCategory = origin.getSrc().getCategory() != null ? origin.getSrc().getCategory()
                                                                         : origin.getDst().getCategory();
            // 类别首字母大写，其他小写
            String boardCategoryCamel = boardCategory.substring(0, 1).toUpperCase() + boardCategory.substring(1).toLowerCase();
            // 板件名称
            String boardName = boardCategory + (origin.getSrc().getIndex() != null
                                                ? "[" + origin.getSrc().getIndex() + "]" : SortConst.BLANK);
            // 正面板数据
            JSONObject frontBoard = origin.getSrc().getMapping();
            // 反面板数据
            JSONObject backBoard = origin.getDst().getMapping();
            String frontBoardBarcode = SortConst.convertValue(frontBoard.getString(boardCategoryCamel + SortConst.CCD_CONTENT_ALL_QRC));
            String backBoardBarcode = SortConst.convertValue(backBoard.getString(boardCategoryCamel + SortConst.CCD_CONTENT_ALL_QRC));
            int frontBarcodeLength = frontBoardBarcode.length();
            int backBarcodeLength = backBoardBarcode.length();
            String boardBarcode = frontBarcodeLength >= backBarcodeLength ? frontBoardBarcode : backBoardBarcode;
            int boardBarcodeLength = Math.max(frontBarcodeLength, backBarcodeLength);
            JSONObject plan = origin.getPlan();
            if (event.checkBarcodeLength())
            {
                int planBarcodeLength = plan.getIntValue("array_length");
                if (SortConst.BOARD_SIDE_DOUBLE.equals(boardType) && frontBarcodeLength != backBarcodeLength)
                {
                    String errMessage = messageSource.getMessage(
                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_BARCODE_LENGTH_NOT_MATCH,
                            new Object[]{boardName, frontBarcodeLength, backBarcodeLength},
                            Locale.getDefault()
                    );
                    throw new SortCompareException(errMessage);
                }
                if (planBarcodeLength > 0 && boardBarcodeLength != planBarcodeLength)
                {
                    String errMessage = messageSource.getMessage(
                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_BARCODE_LENGTH_NOT_MATCH_FOR_RULE,
                            new Object[]{boardName, boardBarcodeLength, planBarcodeLength},
                            Locale.getDefault()
                    );
                    throw new SortCompareException(errMessage);
                }
            }
            if (event.checkBarcodeCase())
            {
                String planBarcodeCase = plan.getString("array_case");
                boolean isAllUpper = boardBarcode.matches("[A-Z\\d]+");
                boolean isAllLower = boardBarcode.matches("[a-z\\d]+");
                if ((SortConst.BAR_CASE_V_UPPER.equals(planBarcodeCase) && !isAllUpper)
                        || (SortConst.BAR_CASE_V_LOWER.equals(planBarcodeCase) && !isAllLower)
                )
                {
                    String errMessage = messageSource.getMessage(
                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_BARCODE_CASE_NOT_MATCH_FOR_RULE,
                            new Object[]{boardName, boardBarcode, planBarcodeCase},
                            Locale.getDefault()
                    );
                    throw new SortCompareException(errMessage);
                }
            }
            if (event.checkRepeatedCode())
            {
                SETBoard setBoard = this.setBoardService.findOneByBoardBarcodeAndBoardStatusAndUnbindFlagAndPileUseFlag(boardBarcode, SortConst.STATUS_OK, Const.FLAG_N, Const.FLAG_N);
                if (setBoard != null)
                {
                    String errMessage = messageSource.getMessage(
                            SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_BARCODE_REPEATED,
                            new Object[]{boardName, boardBarcode},
                            Locale.getDefault()
                    );
                    throw new SortCompareException(errMessage);
                }
            }
            if (event.checkETPass())
            {
                SortSplitRule.Item rule = origin.getRule();
                String planETPassInfo = plan.getString(rule.getDstKey());
                if (planETPassInfo != null && !planETPassInfo.trim().isEmpty())
                {
                    // 将字符串转换为Array并检测数组中是否包含条码
                    boolean matched = Arrays.asList(planETPassInfo.split(",")).contains(boardBarcode);
                    if (!matched)
                    {
                        String errMessage = messageSource.getMessage(
                                SortConst.COMPARE_MESSAGES_PACK_COMPARE_RULES_ET_PASS_NOT_MATCH_FOR_RULE,
                                new Object[]{boardName, rule.getSrcKey(), boardBarcode},
                                Locale.getDefault()
                        );
                        throw new SortCompareException(errMessage);
                    }
                }
            }
        }
    }
}
