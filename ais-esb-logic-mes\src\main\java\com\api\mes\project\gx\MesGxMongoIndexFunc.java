package com.api.mes.project.gx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbMongoIndex;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 国轩项目定制化创建MongodbIndex
 * 1.创建c_mes_me_dx_original动态索引
 * 2.创建c_mes_me_dx_quality动态索引
 * 3.创建c_mes_me_mz_quality动态索引
 * 4.创建c_mes_me_mz_dx_rel动态索引
 * 5.创建c_mes_me_pack_quality动态索引
 * 6.创建c_mes_me_pack_mz_rel动态索引
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */

@Service
@Slf4j
public class MesGxMongoIndexFunc {
    @Autowired
    private CFuncDbMongoIndex cFuncDbMongoIndex;

    //创建表索引
    @Async
    public void CreateMesIndex() {
        CreateMesMeDxOriginalIndex();
        CreateMesMeDxQualityIndex();
        CreateMesMeMzQualityIndex();
        CreateMesMeMzDxRelIndex();
        CreateMesMePackQualityIndex();
        CreateMesMePackMzRelIndex();
    }

    //创建c_mes_me_dx_original动态索引
    @Async
    public void CreateMesMeDxOriginalIndex() {
        String tableName = "c_mes_me_dx_original";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "dx_barcode"};
        int keep_days = 60;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表c_mes_me_dx_original索引成功");
            else log.warn("创建Mongo表c_mes_me_dx_original索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表c_mes_me_dx_original索引失败:" + ex.getMessage());
        }
    }

    //创建c_mes_me_dx_quality动态索引
    @Async
    public void CreateMesMeDxQualityIndex() {
        String tableName = "c_mes_me_dx_quality";
        JSONArray jArrayIndex = new JSONArray();
        //"item_date",
        String[] indexList = new String[]{"item_date_val", "dx_barcode", "small_model_type", "make_order", "ng_code", "station_code", "dx_location_num", "dx_status", "ng_rack"};
        int keep_days = 0;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表c_mes_me_dx_quality索引成功");
            else log.warn("创建Mongo表c_mes_me_dx_quality索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表c_mes_me_dx_quality索引失败:" + ex.getMessage());
        }
    }

    //创建c_mes_me_mz_quality动态索引
    @Async
    public void CreateMesMeMzQualityIndex() {
        String tableName = "c_mes_me_mz_quality";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "mz_quality_id", "small_model_type", "make_order", "mz_barcode", "mz_status", "ng_code", "up_flag", "enable_flag", "station_code", "ng_rack"};
        int keep_days = 0;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表c_mes_me_mz_quality索引成功");
            else log.warn("创建Mongo表c_mes_me_mz_quality索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表c_mes_me_mz_quality索引失败:" + ex.getMessage());
        }
    }

    //创建c_mes_me_mz_dx_rel动态索引
    @Async
    public void CreateMesMeMzDxRelIndex() {
        String tableName = "c_mes_me_mz_dx_rel";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "mz_quality_id", "dx_barcode", "mz_status"};
        int keep_days = 0;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表c_mes_me_mz_dx_rel索引成功");
            else log.warn("创建Mongo表c_mes_me_mz_dx_rel索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表c_mes_me_mz_dx_rel索引失败:" + ex.getMessage());
        }
    }

    //创建c_mes_me_pack_quality动态索引
    @Async
    public void CreateMesMePackQualityIndex() {
        String tableName = "c_mes_me_pack_quality";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "pack_quality_id", "make_order", "small_model_type", "pack_barcode", "pack_status", "ng_code", "finish_flag", "up_flag", "up_ng_code", "enable_flag"};
        int keep_days = 0;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表c_mes_me_pack_quality索引成功");
            else log.warn("创建Mongo表c_mes_me_pack_quality索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表c_mes_me_pack_quality索引失败:" + ex.getMessage());
        }
    }

    //创建c_mes_me_pack_mz_rel动态索引
    @Async
    public void CreateMesMePackMzRelIndex() {
        String tableName = "c_mes_me_pack_mz_rel";
        JSONArray jArrayIndex = new JSONArray();
        String[] indexList = new String[]{"item_date", "item_date_val", "pack_quality_id", "mz_barcode"};
        int keep_days = 0;
        try {
            for (String indexName : indexList) {
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("col_name", indexName);
                jsonObject.put("background", true);
                if (indexName.equals("item_date_val")) jsonObject.put("dirction", "DESC");
                else jsonObject.put("dirction", "ASC");
                if (indexName.equals("item_date")) jsonObject.put("keep_days", keep_days);
                else jsonObject.put("keep_days", 0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess = cFuncDbMongoIndex.CreateIndex(tableName, jArrayIndex);
            if (isSuccess) log.info("创建Mongo表c_mes_me_pack_mz_rel索引成功");
            else log.warn("创建Mongo表c_mes_me_pack_mz_rel索引失败");
        } catch (Exception ex) {
            log.warn("创建Mongo表c_mes_me_pack_mz_rel索引失败:" + ex.getMessage());
        }
    }
}
