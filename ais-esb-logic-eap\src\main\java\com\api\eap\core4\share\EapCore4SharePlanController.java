package com.api.eap.core4.share;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * AIS4.0 生产计划共用对外API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-7-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/core4/share")
public class EapCore4SharePlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //取消端口任务
    @RequestMapping(value = "/EapCore4SharePlanPortCancel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4SharePlanPortCancel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/share/EapCore4SharePlanPortCancel";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String[] group_lot_status_list = new String[]{"WAIT", "PLAN", "WORK"};
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "CANCEL");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "取消端口任务异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //根据PlanID查询任务信息
    @RequestMapping(value = "/EapCore4ShareTaskSelByPlanID", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4ShareTaskSelByPlanID(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/share/EapCore4ShareTaskSelByPlanID";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        try {
            long station_id = jsonParas.getLong("station_id");
            String plan_id = jsonParas.getString("plan_id");
            List<Map<String, Object>> itemList = new ArrayList<>();
            Map<String, Object> mapBigDataRow = null;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                mapBigDataRow = iteratorBigData.next();
                mapBigDataRow.remove("_id");
                mapBigDataRow.remove("item_date");
                mapBigDataRow.remove("item_date_val");
                mapBigDataRow.remove("station_id");
                mapBigDataRow.remove("group_lot_status");
                mapBigDataRow.remove("lot_status");
                mapBigDataRow.put("ori_plan_id", plan_id);
                iteratorBigData.close();
            }
            if (mapBigDataRow != null) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                List<Map> panelList = mongoTemplate.find(queryBigData, Map.class, apsPlanDTable);
                JSONArray jaPnl = new JSONArray();
                if (panelList != null && panelList.size() > 0) {
                    for (Map mapItem : panelList) {
                        JSONObject jbItem = new JSONObject();
                        Object panel_barcode = mapItem.get("panel_barcode");
                        Object panel_attr = mapItem.get("panel_attr");
                        jbItem.put("panel_barcode", panel_barcode);
                        jbItem.put("panel_attr", panel_attr);
                        jaPnl.add(jbItem);
                    }
                }
                mapBigDataRow.put("panel_list", jaPnl);
                itemList.add(mapBigDataRow);
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据PlanID查询任务信息异常:" + ex;
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //保存任务信息到数据库
    @RequestMapping(value = "/EapCore4SharePlanSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4SharePlanSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/share/EapCore4SharePlanSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_code = jsonParas.getString("station_code");
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();
            String station_attr = lstStation.get(0).get("station_attr").toString();
            JSONArray plan_list = jsonParas.getJSONArray("plan_list");
            if (plan_list == null || plan_list.size() <= 0) {
                errorMsg = "保存任务信息不能为空集合";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            //查询当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //插入数据到数据库
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstPlanDocuments = new ArrayList<>();
            List<Map<String, Object>> lstPlanBDocuments = new ArrayList<>();
            String group_id_new = CFuncUtilsSystem.CreateUUID(true);
            String lot_group_num_new = CFuncUtilsSystem.GetOnlySign("LG");
            for (int i = 0; i < plan_list.size(); i++) {
                JSONObject jbItem = plan_list.getJSONObject(i);
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String plan_id = CFuncUtilsSystem.CreateUUID(true);
                String port_code = jbItem.getString("port_code") == null ? "" : jbItem.getString("port_code");
                String group_lot_num = jbItem.getString("group_lot_num") == null ? "" : jbItem.getString("group_lot_num");
                String group_id = jbItem.getString("group_id") == null ? "" : jbItem.getString("group_lot_num");
                if (group_lot_num.equals("")) group_lot_num = lot_group_num_new;
                if (group_id.equals("")) group_id = group_id_new;
                if (station_attr.equals("UnLoad")) port_code = "";
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("plan_id", plan_id);
                mapBigDataRow.put("station_id", Long.parseLong(station_id));
                mapBigDataRow.put("task_from", jbItem.getString("task_from") == null ? "AIS" : jbItem.getString("task_from"));
                mapBigDataRow.put("group_lot_num", group_lot_num);
                mapBigDataRow.put("lot_num", jbItem.getString("lot_num") == null ? "" : jbItem.getString("lot_num"));
                mapBigDataRow.put("lot_short_num", jbItem.getString("lot_short_num") == null ? "" : jbItem.getString("lot_short_num"));
                mapBigDataRow.put("lot_index", jbItem.getInteger("lot_index") == null ? 1 : jbItem.getInteger("lot_index"));
                mapBigDataRow.put("plan_lot_count", jbItem.getInteger("plan_lot_count") == null ? 0 : jbItem.getInteger("plan_lot_count"));
                mapBigDataRow.put("target_lot_count", jbItem.getInteger("target_lot_count") == null ? 0 : jbItem.getInteger("target_lot_count"));
                mapBigDataRow.put("port_code", port_code);
                mapBigDataRow.put("material_code", jbItem.getString("material_code") == null ? "" : jbItem.getString("material_code"));
                String pallet_num = jbItem.getString("pallet_num") == null ? "" : jbItem.getString("pallet_num");
                if (station_attr.equals("UnLoad")) pallet_num = "";
                mapBigDataRow.put("pallet_num", pallet_num);
                String pallet_type = jbItem.getString("pallet_type") == null ? "" : jbItem.getString("pallet_type");
                //if (station_attr.equals("UnLoad")) pallet_type = "";
                mapBigDataRow.put("pallet_type", pallet_type);
                mapBigDataRow.put("lot_level", jbItem.getString("lot_level") == null ? "" : jbItem.getString("lot_level"));
                mapBigDataRow.put("panel_length", jbItem.getDouble("panel_length") == null ? 0d : jbItem.getDouble("panel_length"));
                mapBigDataRow.put("panel_width", jbItem.getDouble("panel_width") == null ? 0d : jbItem.getDouble("panel_width"));
                mapBigDataRow.put("panel_tickness", jbItem.getDouble("panel_tickness") == null ? 0d : jbItem.getDouble("panel_tickness"));
                mapBigDataRow.put("panel_model", jbItem.getInteger("panel_model") == null ? -1 : jbItem.getInteger("panel_model"));
                mapBigDataRow.put("inspect_count", jbItem.getInteger("inspect_count") == null ? 0 : jbItem.getInteger("inspect_count"));
                mapBigDataRow.put("inspect_finish_count", 0);
                mapBigDataRow.put("pdb_count", jbItem.getInteger("pdb_count") == null ? 0 : jbItem.getInteger("pdb_count"));
                mapBigDataRow.put("pdb_rule", jbItem.getInteger("pdb_rule") == null ? 0 : jbItem.getInteger("pdb_rule"));
                mapBigDataRow.put("fp_count", jbItem.getInteger("fp_count") == null ? 0 : jbItem.getInteger("fp_count"));
                mapBigDataRow.put("group_lot_status", "PLAN");
                mapBigDataRow.put("lot_status", "PLAN");
                mapBigDataRow.put("finish_count", 0);
                mapBigDataRow.put("finish_ok_count", 0);
                mapBigDataRow.put("finish_ng_count", 0);
                mapBigDataRow.put("task_error_code", 0);
                mapBigDataRow.put("item_info", jbItem.getString("item_info") == null ? "" : jbItem.getString("item_info"));
                mapBigDataRow.put("task_start_time", "");
                mapBigDataRow.put("task_end_time", "");
                mapBigDataRow.put("task_cost_time", (long) 0);
                mapBigDataRow.put("attribute1", jbItem.getString("attribute1") == null ? "" : jbItem.getString("attribute1"));
                mapBigDataRow.put("attribute2", jbItem.getString("attribute2") == null ? "" : jbItem.getString("attribute2"));
                mapBigDataRow.put("attribute3", jbItem.getString("attribute3") == null ? "" : jbItem.getString("attribute3"));
                mapBigDataRow.put("face_code", jbItem.getInteger("face_code") == null ? 0 : jbItem.getInteger("face_code"));
                mapBigDataRow.put("pallet_use_count", jbItem.getInteger("pallet_use_count") == null ? 0 : jbItem.getInteger("pallet_use_count"));
                mapBigDataRow.put("user_name", user_name);
                mapBigDataRow.put("group_id", group_id);
                mapBigDataRow.put("offline_flag", "N");
                mapBigDataRow.put("target_update_count", 0);
                mapBigDataRow.put("pnl_infos", jbItem.getString("pnl_infos") == null ? "" : jbItem.getString("pnl_infos"));
                mapBigDataRow.put("ori_plan_id", jbItem.getString("ori_plan_id") == null ? "" : jbItem.getString("ori_plan_id"));
                lstPlanDocuments.add(mapBigDataRow);

                //明细
                String panel_list = jbItem.getString("panel_list") == null ? "" : jbItem.getString("panel_list");
                if (panel_list != null && !panel_list.equals("")) {
                    JSONArray jaPnl = JSONArray.parseArray(panel_list);
                    if (jaPnl != null && jaPnl.size() > 0) {
                        for (int j = 0; j < jaPnl.size(); j++) {
                            JSONObject jbItemPanel = jaPnl.getJSONObject(j);
                            String panel_barcode = jbItemPanel.getString("panel_barcode") == null ? "" : jbItemPanel.getString("panel_barcode");
                            String panel_attr = jbItemPanel.getString("panel_attr") == null ? "" : jbItemPanel.getString("panel_attr");
                            String plan_d_id = CFuncUtilsSystem.CreateUUID(true);
                            Map<String, Object> mapBigDataRowB = new HashMap<>();
                            mapBigDataRowB.put("item_date", item_date);
                            mapBigDataRowB.put("item_date_val", item_date_val);
                            mapBigDataRowB.put("plan_d_id", plan_d_id);
                            mapBigDataRowB.put("plan_id", plan_id);
                            mapBigDataRowB.put("panel_barcode", panel_barcode);
                            mapBigDataRowB.put("panel_attr", panel_attr);
                            lstPlanBDocuments.add(mapBigDataRowB);
                        }
                    }
                }
            }
            log.info("同步任务信息：{}", lstPlanDocuments);
            if (lstPlanBDocuments.size() > 0) mongoTemplate.insert(lstPlanBDocuments, apsPlanBTable);
            mongoTemplate.insert(lstPlanDocuments, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "插入成功", 0);
        } catch (Exception ex) {
            errorMsg = "保存任务信息到数据库异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //更新收板机批次信息
    @RequestMapping(value = "/EapCore4LoadGroupFinishUpd", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4LoadGroupFinishUpd(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/share/EapCore4LoadGroupFinishUpd";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_code = jsonParas.getString("station_code");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String lot_num = jsonParas.getString("lot_num");
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='" + station_code + "'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}查找到工位配置信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String station_id = lstStation.get(0).get("station_id").toString();

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            Update updateBigData = new Update();
            updateBigData.set("attribute2", "Y");//标识母批最后一个子批信息
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "更新成功", 0);
        } catch (Exception ex) {
            errorMsg = "保存任务信息到数据库异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //根据plan_id取消任务
    @RequestMapping(value = "/EapCore4SharePlanCancelById", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCore4SharePlanCancelById(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core4/share/EapCore4SharePlanCancelById";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            long station_id = jsonParas.getLong("station_id");
            String plan_id_list_str = jsonParas.getString("plan_id_list");
            String[] plan_id_list = plan_id_list_str.split(",");

            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("plan_id").in(plan_id_list));
            Update updateBigData = new Update();
            updateBigData.set("lot_status", "CANCEL");
            updateBigData.set("group_lot_status", "CANCEL");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "取消端口任务异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
