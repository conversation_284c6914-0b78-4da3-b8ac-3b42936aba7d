package com.api.pack.core.op;

import com.api.common.utils.CFuncUtilsCellScada;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
public class PackCoreService
{
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    public void stop(String stationCode)
    {
        // 将"运行标志"置为2
        String appStartTag = "BzPlc01/PlcBase/AppStart";
        String errorMsg = this.cFuncUtilsCellScada.WriteTagByStation("AIS", stationCode, appStartTag, "2", true);
        if (!ObjectUtils.isEmpty(errorMsg))
        {
            throw new RuntimeException("停止失败：" + errorMsg);
        }
    }
}
