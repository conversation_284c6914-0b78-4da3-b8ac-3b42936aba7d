package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;

import java.net.*;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定颖EAP事件接口(Sub)公共方法
 * 1.UserVerify:[接口]用户登入登出验证
 * 2.UserLoginRequest:[接口]人员登入登出事件
 * 3.EQPInfoVerify:[接口]检测与EAP通讯并且返回EAP时间由应用进行更新本地时间
 * 4.AlarmReport:[接口]上报报警信息到EAP
 * 5.RealStatusReport:[接口]设备状态上报到EAP
 * 6.StatusChangeReport:[接口]只要报警与状态发生任何变化都需要进行上报到EAP
 * 7.ParamVerify:[接口]查询工单信息
 * 8.UtilityReport:[接口]上报三率
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapCjMasterSendEventSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapCjInterfCommon eapCjInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.[接口]用户登入登出验证
    public JSONObject UserVerify(Boolean checkOut, String station_code, String user_id) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "UserVerify";
        String esbInterfCode = "UserVerify";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            String event_type = "I";
            if (checkOut) event_type = "O";
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            if (eapCjInterfCommon.CheckDyVersion(3)) {
                request_body.put("clock_type", event_type);
            } else {
                request_body.put("event_type", event_type);
            }
            request_body.put("user_id", user_id);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);
            if (!response_body.containsKey("dept_id") || !response_body.containsKey("shift_id") || !response_body.containsKey("nick_name")) {
                errorMsg = "EAP返回response_body数据格式必须包含dept_id|shift_id|nick_name字段名";
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.[接口]人员登入登出事件
    public JSONObject UserLoginRequest(Boolean checkOut, String station_code, String user_id, String dept_id, String shift_id, String nick_name) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "UserLoginRequest";
        String esbInterfCode = "UserLoginRequest";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            String event_type = "I";
            if (checkOut) event_type = "O";
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("event_type", event_type);
            request_body.put("user_id", user_id);
            request_body.put("dept_id", dept_id);
            request_body.put("shift_id", shift_id);
            request_body.put("nick_me", nick_name);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.[接口]检测与EAP通讯并且返回EAP时间由应用进行更新本地时间
    public JSONObject EQPInfoVerify(String station_code) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "EQPInfoVerify";
        String esbInterfCode = "EQPInfoVerify";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);
            String eapDateTime = response_body.getString("now");
            if (eapDateTime == null || eapDateTime.equals("")) {
                errorMsg = "EAP返回接口now时间为空";
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.[接口]上报报警信息到EAP
    public JSONObject AlarmReport(Long station_id, String station_code, String reset_flag, String warn_flag, String alarm_id, String alarm_desc, String local_flag, String status_code, String offline_flag) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "AlarmReport";
        String esbInterfCode = "AlarmReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
        String esb_prod_intef_url ="";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数

            String alarm_type = "S";
            String alarm_code = "A";
            String keep_reason = "0";
            if (isNewVersion) {
                if (offline_flag.equals("Y") && local_flag.equals("Y")) keep_reason = "3";
            } else {
                if (offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";
            }

            if (reset_flag.equals("Y")) alarm_type = "R";
            if (warn_flag.equals("Y")) alarm_code = "W";

            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            request_body.put("report_dt", CFuncUtilsSystem.GetNowDateTime(""));
            request_body.put("keep_reason", keep_reason);//0直接上传、1离线、2超时
            request_body.put("alarm_type", alarm_type);//S：發生警報、R：警報解除
            request_body.put("alarm_code", alarm_code);
            request_body.put("alarm_id", alarm_id);
            request_body.put("alarm_desc", alarm_desc);
            //DY3.0以上增加status_id
            if (isNewVersion) {
                request_body.put("status_id", status_code);
            }
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            /***office测试专用***/
            if (eapCjInterfCommon.DyOfficeTest()) {
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                return jbResult;
            }

            //2.若为离线,则放入离线上报
            if (offline_flag.equals("Y")) {//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //4.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (ResourceAccessException ex) {
            //判断断线
            URL url = new URL(esb_prod_intef_url);
            String host = url.getHost();
            int port = url.getPort();
            Socket socket = new Socket();
            try {
                socket.connect(new InetSocketAddress(host, port), 3000);
                if (!socket.isConnected()) {
                    throw new SocketTimeoutException();
                }
            } catch (SocketTimeoutException | NoRouteToHostException e) {
                //断线
                JSONObject jsonObject = JSONObject.parseObject(requestParas);
                request_body = jsonObject.getJSONObject("request_body");
                request_body.put("keep_reason", "1");
                jsonObject.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
            }
            //超时
            JSONObject jsonObject = JSONObject.parseObject(requestParas);
            request_body = jsonObject.getJSONObject("request_body");
            request_body.put("keep_reason", "2");
            jsonObject.put("request_body", request_body);
            opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
        }  catch (Exception ex) {
            if(ex.getMessage().contains("about:blank")){
                //断线
                JSONObject jsonObject = JSONObject.parseObject(requestParas);
                request_body = jsonObject.getJSONObject("request_body");
                request_body.put("keep_reason", "1");
                jsonObject.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
            }
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //5.[接口]设备状态上报到EAP
    public JSONObject RealStatusReport(String station_code, String local_flag, String auto_flag, String stop_flag, String repair_flag, String hold_flag, String status_code, String reset_flag, String warn_flag, String alarm_id, String short_bettery_flag, String power_on_time, String idle_delay_time, String current_lot_count, String history_lot_count, String red_light_flag, String yellow_light_flag, String green_light_flag, String blue_light_flag, String user_name, String lot_num, String eap_offline_flag, String plc_offline_flag, String prod_mode, String alarm_desc, String offline_flag) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "RealStatusReport";
        String esbInterfCode = "RealStatusReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        String esb_prod_intef_url = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
            String cim_mode = "Remote";
            if (offline_flag.equals("Y")) cim_mode = "Local";
            else {
                if (isNewVersion) {
                    if (local_flag.equals("Y")) cim_mode = "Semi-Auto";
                }
            }
            String auto_mode = "Auto";
            if (!auto_flag.equals("Y")) {
                auto_mode = "Manual";
                cim_mode = "Local";
            }
            String emergency_mode = "Normal";
            if (stop_flag.equals("Y")) emergency_mode = "Emergeny";
            if (isNewVersion) {
                if (emergency_mode.equals("Normal")) emergency_mode = "OFF";
                if (emergency_mode.equals("Emergeny")) emergency_mode = "ON";
            }
            String maintain_mode = "Normal";
            if (repair_flag.equals("Y")) maintain_mode = "Maintain";
            String hold_mode = "Normal";
            if (hold_flag.equals("Y")) hold_mode = "Hold";
            String alarm_type = "R";
            String alarm_alarm_id = "0";
            String warn_type = "R";
            String warn_id = "0";
            if (alarm_id != null && !alarm_id.equals("")) {
                if (warn_flag.equals("N")) {//重大报警
                    alarm_alarm_id = alarm_id;
                    if (reset_flag.equals("N")) alarm_type = "S";
                } else {
                    warn_id = alarm_id;
                    if (reset_flag.equals("N")) warn_type = "S";
                }
            }
            String bettery_status = "Normal";
            if (short_bettery_flag.equals("Y")) bettery_status = "Low";
            String tower_red = "OFF";
            String tower_yellow = "OFF";
            String tower_green = "OFF";
            String tower_blue = "OFF";
            if (red_light_flag.equals("Y")) tower_red = "ON";
            if (yellow_light_flag.equals("Y")) tower_yellow = "ON";
            if (green_light_flag.equals("Y")) tower_green = "ON";
            if (blue_light_flag.equals("Y")) tower_blue = "ON";
            String upstream_connect_status = "INLINE";
            if (eap_offline_flag.equals("Y")) upstream_connect_status = "OFFLINE";
            String downstream_connect_status = "INLINE";
            if (plc_offline_flag.equals("Y")) downstream_connect_status = "OFFLINE";
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("cim_mode", cim_mode);
            request_body.put("auto_mode", auto_mode);
            request_body.put("emergency_mode", emergency_mode);
            request_body.put("status_id", status_code);
            if (isNewVersion) {
                if (alarm_alarm_id.equals("0")) alarm_alarm_id = "";
            } else {
                request_body.put("maintain_mode", maintain_mode);
                request_body.put("hold_mode", hold_mode);
                request_body.put("down_alarm_id", alarm_id);
                request_body.put("alarm_type", alarm_type);
                request_body.put("warning_type", warn_type);
                request_body.put("warning_id", warn_id);
                request_body.put("battery_status", bettery_status);
                request_body.put("power_on_time", power_on_time);
                request_body.put("current_lot_count", current_lot_count);
                request_body.put("history_lot_count", history_lot_count);
                request_body.put("tower_red", tower_red);
                request_body.put("tower_yellow", tower_yellow);
                request_body.put("tower_green", tower_green);
                request_body.put("tower_blue", tower_blue);
                request_body.put("eqp_id", station_code);
            }
            request_body.put("alarm_id", alarm_alarm_id);
            request_body.put("user_id", user_name);
            request_body.put("idle_delay_time", idle_delay_time);
            request_body.put("upstream_connect_status", upstream_connect_status);
            request_body.put("downstream_connect_status", downstream_connect_status);
            request_body.put("lot_id", lot_num);
            request_body.put("alarm_desc", "");
            request_body.put("prod_mix_mode", "");
            request_body.put("is_stop_receive", "");
            request_body.put("program_version", "");
            if (isNewVersion) {
                prod_mode = "Production";
                if (prod_mode.equals("1")) prod_mode = "FAI";
                else if (prod_mode.equals("2")) prod_mode = "Dummy";
                request_body.put("prod_mode", prod_mode);
            } else {
                request_body.put("prod_mode", String.valueOf(prod_mode));
            }
            if (isNewVersion) {
                request_body.put("prod_mix_mode", "ON");
                if (red_light_flag.equals("Y")) request_body.put("is_stop_receive", "Y");
                else request_body.put("is_stop_receive", "N");
                request_body.put("program_version", "V2.0");//暂时先写死,后期需要读配置信息
                request_body.put("queue_lot_count", "0");//先不做吧
                request_body.put("alarm_desc", alarm_desc);
            }
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);

        }
        return jbResult;
    }

    //6.[接口]只要报警与状态发生任何变化都需要进行上报到EAP
    public JSONObject StatusChangeReport(Long station_id, String station_code, String auto_flag, String status_code, String alarm_id, String local_flag, String alarm_desc, String offline_flag) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "StatusChangeReport";
        String esbInterfCode = "StatusChangeReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
        String esb_prod_intef_url = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            String cim_mode = "Remote";
            if (offline_flag.equals("Y")) cim_mode = "Local";
            else {
                if (isNewVersion) {
                    if (local_flag.equals("Y")) cim_mode = "Semi-Auto";
                }
            }
            String auto_mode = "Auto";
            if (!auto_flag.equals("Y")) {
                auto_mode = "Manual";
                cim_mode = "Local";
            }
            String keep_reason = "0";
            if (isNewVersion) {
                if (offline_flag.equals("Y") && local_flag.equals("Y")) keep_reason = "3";
            } else {
                if (offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";
            }
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            request_body.put("report_dt", CFuncUtilsSystem.GetNowDateTime(""));
            request_body.put("keep_reason", keep_reason);//0直接上传、1离线、2超时
            request_body.put("cim_mode", cim_mode);
            request_body.put("auto_mode", auto_mode);
            request_body.put("status_id", status_code);
            request_body.put("alarm_id", alarm_id);
            request_body.put("alarm_desc", "");
            if (isNewVersion) {
                request_body.put("alarm_desc", alarm_desc);
            }
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            /***office测试专用***/
            if (eapCjInterfCommon.DyOfficeTest()) {
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                return jbResult;
            }

            //2.若为离线,则放入离线上报
            if (offline_flag.equals("Y")) {//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //4.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (ResourceAccessException ex) {
            //判断断线
            URL url = new URL(esb_prod_intef_url);
            String host = url.getHost();
            int port = url.getPort();
            Socket socket = new Socket();
            try {
                socket.connect(new InetSocketAddress(host, port), 3000);
                if (!socket.isConnected()) {
                    throw new SocketTimeoutException();
                }
            } catch (SocketTimeoutException|NoRouteToHostException e) {
                //断线
                JSONObject jsonObject = JSONObject.parseObject(requestParas);
                request_body = jsonObject.getJSONObject("request_body");
                request_body.put("keep_reason", "1");
                jsonObject.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
            }
            //超时
            JSONObject jsonObject = JSONObject.parseObject(requestParas);
            request_body = jsonObject.getJSONObject("request_body");
            request_body.put("keep_reason", "2");
            jsonObject.put("request_body", request_body);
            opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);


        }
        return jbResult;
    }

    //7.[接口]查询工单信息
    public JSONObject ParamVerify(String station_code, String production_mode, String lot_num) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "ParamVerify";
        String esbInterfCode = "ParamVerify";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("production_mode", production_mode);
            request_body.put("lot_id", lot_num);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //8.[接口]三率上报
    public JSONObject UtilityReport(Long station_id, String station_code, String shfit_id, JSONArray item_list, String report_dt, String offline_flag, String local_flag) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "UtilityReport";
        String esbInterfCode = "UtilityReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
        String esb_prod_intef_url ="";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject utl_infos = new JSONObject();
            if (item_list != null && item_list.size() > 0) {
                utl_infos.put("utl", item_list);
            }
            String keep_reason = "0";
            if (isNewVersion) {
                if (offline_flag.equals("Y") && local_flag.equals("Y")) keep_reason = "3";
            } else {
                if (offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";
            }
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            request_body.put("report_dt", report_dt);
            request_body.put("keep_reason", keep_reason);//0直接上传、1离线、2超时
            request_body.put("shfit_id", shfit_id);
            request_body.put("utl_infos", utl_infos);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            /***office测试专用***/
            if (eapCjInterfCommon.DyOfficeTest()) {
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                return jbResult;
            }

            //2.若为离线,则放入离线上报
            if (local_flag.equals("Y")) {//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //4.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (ResourceAccessException ex) {
            //判断断线
            URL url = new URL(esb_prod_intef_url);
            String host = url.getHost();
            int port = url.getPort();
            Socket socket = new Socket();
            try {
                socket.connect(new InetSocketAddress(host, port), 3000);
                if (!socket.isConnected()) {
                    throw new SocketTimeoutException();
                }
            } catch (SocketTimeoutException|NoRouteToHostException e) {
                //断线
                JSONObject jsonObject = JSONObject.parseObject(requestParas);
                request_body = jsonObject.getJSONObject("request_body");
                request_body.put("keep_reason", "1");
                jsonObject.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
            }
            //超时
            JSONObject jsonObject = JSONObject.parseObject(requestParas);
            request_body = jsonObject.getJSONObject("request_body");
            request_body.put("keep_reason", "2");
            jsonObject.put("request_body", request_body);
            opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
        }
        catch (Exception ex) {
            if (ex.getMessage().indexOf("timed out") != -1) {
                request_body.put("keep_reason", "2");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            } else {
                request_body.put("keep_reason", "1");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //CimMode模式变化后事件上报
    public JSONObject CimModeChangeReport(Long station_id, String station_code, String cim_model, String user_id) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CimModeChangeReport";
        String esbInterfCode = "CimModeChangeReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            if (!eapCjInterfCommon.CheckDyVersion(3)) {
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "EAP无此接口");
                return jbResult;
            }

            //1.创建参数
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            request_body.put("cim_mode", cim_model);
            request_body.put("user_id", user_id);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //4.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);
            String cim_password = "";
            if (response_body != null && response_body.containsKey("cim_password")) {
                cim_password = response_body.getString("cim_password");
                if (cim_password == null) cim_password = "";
            }
            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
            jbResult.put("cimPassword", cim_password);
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
