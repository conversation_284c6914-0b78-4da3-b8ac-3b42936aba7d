package com.api.pack.core.board;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;
import org.springframework.context.MessageSource;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;

/**
 * <p>
 * 板件X数/位比较器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Data
@AllArgsConstructor
public class BoardDefaultComparator implements BoardComparator
{
    public <BD extends Board> BoardCompareResult compare(BD board, String property, Object targetValue, MessageSource messageSource)
    {
        try
        {
            String boardName = ObjectUtils.isEmpty(board.getBoardName()) ? BoardConst.BLANK : board.getBoardName();
            BoardCompareResult result = new BoardCompareResult(BoardConst.STATUS_OK, BoardConst.CODE_OK, BoardConst.BLANK);
            PropertyDescriptor propertyDescriptor = BeanUtils.getPropertyDescriptor(board.getClass(), property);
            if (propertyDescriptor == null)
            {
                throw new FatalBeanException("Could not find property '" + property + "' on class " + board.getClass());
            }
            Field field = board.getClass().getDeclaredField(property);
            String propertyName = property;
            ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
            if (annotation != null)
            {
                propertyName = annotation.value();
            }
            Method writeMethod = propertyDescriptor.getWriteMethod();
            Method readMethod = propertyDescriptor.getReadMethod();
            if (writeMethod != null && readMethod != null && ClassUtils.isAssignable(writeMethod.getParameterTypes()[0], readMethod.getReturnType()))
            {
                if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers()))
                {
                    readMethod.setAccessible(true);
                }
                Object value = readMethod.invoke(board);
                if (isNC(value))
                {
                    return result;
                }
                if (isNULL(value))
                {
                    String err = getPackNoDetectedMessage(boardName, propertyName, messageSource);
                    return new BoardCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
                }
                if (value.equals(targetValue))
                {
                    return result;
                }
                else
                {
                    String err = getPackNotMatchMessage(boardName, propertyName, value, targetValue, messageSource);
                    return new BoardCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, err);
                }
            }
        }
        catch (Throwable ex)
        {
            throw new FatalBeanException("Could not find property '" + property + "' on class " + board.getClass(), ex);
        }
        return null;
    }

    protected boolean isNC(Object value)
    {
        return BoardConst.VALUE_NC.equals(value);
    }

    protected boolean isNULL(Object value)
    {
        return BoardConst.VALUE_NULL.equals(value) || ObjectUtils.isEmpty(value);
    }
}
