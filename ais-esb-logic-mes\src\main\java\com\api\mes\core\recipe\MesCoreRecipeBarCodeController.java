package com.api.mes.core.recipe;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.mes.project.byd.MesBydBarCodeCreateFunc;
import com.api.mes.project.sh.MesShBarCodeCreateFunc;
import com.api.mes.project.gx.MesGxBarCodeCreateFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@RestController
@Slf4j
@RequestMapping("/mes/core/recipe")
public class MesCoreRecipeBarCodeController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MesGxBarCodeCreateFunc mesGxBarCodeCreateFunc;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private MesBydBarCodeCreateFunc mesBydBarCodeCreateFunc;
    @Autowired
    private MesShBarCodeCreateFunc mesShBarCodeCreateFunc;
    //按照订单配方生成条码
    @Transactional
    @RequestMapping(value = "/MesCoreRecipeBarCodeCreate", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipeBarCodeCreate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipeBarCodeCreate";
        String meOnlineFlowTable = "c_mes_me_online_flow";
        String transResult = "";
        String errorMsg = "";
        String make_order = "";
        String station_code = "";
        try {
            String prod_line_id = jsonParas.getString("prod_line_id");//产线ID
            station_code = jsonParas.getString("station_code");//工位号
            make_order = jsonParas.getString("make_order");//订单号
            int barcode_count = jsonParas.getInteger("barcode_count");//生成条码数量
            if (make_order == null || make_order.equals("")) {
                errorMsg = "传递订单号{" + make_order + "}不能为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if (prod_line_id == null || prod_line_id.equals("")) {
                errorMsg = "传递产线{" + prod_line_id + "}不能为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if (barcode_count <= 0) {
                errorMsg = "打印条码数量{" + barcode_count + "}不能<=0";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String sqlMoRecipe = "select c.recipe_id " +
                    "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " +
                    "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " +
                    "on b.recipe_id=c.recipe_id " +
                    "where a.prod_line_id=" + prod_line_id + " and a.make_order='" + make_order + "' " +
                    "and c.recipe_type='CODERULE' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe,
                    false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "根据产线{" + prod_line_id + "}以及订单号{" + make_order + "}未能找到条码配方";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String recipe_id = itemListMoRecipe.get(0).get("recipe_id").toString();
            String sqlBarCodeRule = "select barcode_rule_code," +
                    "COALESCE(barcode_length,0) barcode_length,num_increase,next_num,change_recov_flag," +
                    "COALESCE(recov_next_num,1) recov_next_num," +
                    "COALESCE(fix_field1,'NAN') fix_field1," +
                    "COALESCE(fix_field2,'NAN') fix_field2," +
                    "COALESCE(fix_field3,'NAN') fix_field3," +
                    "COALESCE(fix_field4,'NAN') fix_field4," +
                    "COALESCE(fix_field5,'NAN') fix_field5," +
                    "COALESCE(fix_field6,'NAN') fix_field6," +
                    "COALESCE(fix_field7,'NAN') fix_field7," +
                    "COALESCE(fix_field8,'NAN') fix_field8," +
                    "COALESCE(change_field_type,'') change_field_type," +
                    "COALESCE(attr_field1,'') attr_field1," +
                    "COALESCE(attr_field2,'') attr_field2," +
                    "COALESCE(attr_field3,'') attr_field3," +
                    "COALESCE(attr_field4,'') attr_field4," +
                    "COALESCE(attr_field5,'') attr_field5," +
                    "COALESCE(attr_field6,'') attr_field6," +
                    "COALESCE(attr_field7,'') attr_field7," +
                    "COALESCE(attr_field8,'') attr_field8 " +
                    "from c_mes_fmod_recipe_barcode_rule where recipe_id=" + recipe_id + " " +
                    "and enable_flag='Y' LIMIT 1 OFFSET 0 ";
            List<Map<String, Object>> itemListBarCodeRule = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlBarCodeRule,
                    true, request, apiRoutePath);
            if (itemListBarCodeRule == null || itemListBarCodeRule.size() <= 0) {
                errorMsg = "根据产线{" + prod_line_id + "}以及订单号{" + make_order + "}未能找到条码配方";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Map<String, Object> mapRule = itemListBarCodeRule.get(0);
            String barcode_rule_code = mapRule.get("barcode_rule_code").toString();
            Integer barcode_length = Integer.parseInt(mapRule.get("barcode_length").toString());
            String barCodeResult = "";
            for (int i = 0; i < barcode_count; i++) {
                //下面需要根据具体的barcode_rule_code去做对应的定制化逻辑
                String barCode = "";
                switch (barcode_rule_code) {
                    case "GX_BARCODE"://国轩模组与PACK条码
                        Integer increaNum = ComonGetIncreaNum(station_code, mapRule, apiRoutePath, request);
                        barCode = mesGxBarCodeCreateFunc.CreateGxBarCode(increaNum, mapRule);
                        break;
                    case "GX_BARCODE_NEW"://国轩模组与PACK条码
                        increaNum = ComonGetIncreaNum(station_code, mapRule, apiRoutePath, request);
                        barCode = mesGxBarCodeCreateFunc.CreateGxBarCodeNew(increaNum, mapRule);
                        break;
                    case "BYD_BARCODE"://比亚迪条形码
                        increaNum = ComonGetIncreaNum(station_code, mapRule, apiRoutePath, request);
                        barCode = mesBydBarCodeCreateFunc.CreatePrintBarCode(increaNum, mapRule);
                        break;
                    case "SH_BARCODE"://双环零部件二维追溯码
                        increaNum = ComonGetIncreaNum(station_code, mapRule, apiRoutePath, request);
                        barCode = mesShBarCodeCreateFunc.CreateShBarCode(increaNum, mapRule);
                        break;
                    case "SH_BARCODE_PACK"://双环箱体码
                        increaNum = ComonGetIncreaNum(station_code, mapRule, apiRoutePath, request);
                        barCode = mesShBarCodeCreateFunc.CreateShBarCodePack(increaNum, mapRule);
                        break;
                    default:
                        break;
                }
                if(barCode != null && !barCode.equals("")){
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("serial_num").is(barCode));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meOnlineFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if(iteratorBigData.hasNext()){
                        errorMsg = "当前总成编号{"+ barCode+" } 重复 ";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
                if (barcode_length > 0) {
                    if (barCode.length() != barcode_length) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}," +
                                "创建生成条码号{" + barCode + "},条码长度不符合规格要求长度为{" + barcode_length + "}";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
                if (barCodeResult.equals("")) barCodeResult = barCode;
                else barCodeResult += "," + barCode;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, barCodeResult, "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}配方生成条码发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //按照订单配方生成条码--唐山国轩PACK码
    @Transactional
    @RequestMapping(value = "/MesCoreRecipeBarCodeCreate02", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesCoreRecipeBarCodeCreate02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/recipe/MesCoreRecipeBarCodeCreate02";
        String transResult = "";
        String errorMsg = "";
        String make_order = "";
        String station_code = "";
        try {
            String prod_line_id = jsonParas.getString("prod_line_id");//产线ID
            station_code = jsonParas.getString("station_code");//工位号
            make_order = jsonParas.getString("make_order");//订单号
            int barcode_count = jsonParas.getInteger("barcode_count");//生成条码数量
            String car_code = jsonParas.getString("car_code");//车辆码
            if (car_code == null) car_code = "";
            if (make_order == null || make_order.equals("")) {
                errorMsg = "传递订单号{" + make_order + "}不能为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if (prod_line_id == null || prod_line_id.equals("")) {
                errorMsg = "传递产线{" + prod_line_id + "}不能为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if (barcode_count <= 0) {
                errorMsg = "打印条码数量{" + barcode_count + "}不能<=0";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String sqlMoRecipe = "select c.recipe_id " +
                    "from c_mes_aps_plan_mo a inner join c_mes_aps_plan_mo_recipe b " +
                    "on a.mo_id=b.mo_id inner join c_mes_fmod_recipe c " +
                    "on b.recipe_id=c.recipe_id " +
                    "where a.prod_line_id=" + prod_line_id + " and a.make_order='" + make_order + "' " +
                    "and c.recipe_type='CODERULE' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListMoRecipe = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoRecipe,
                    false, request, apiRoutePath);
            if (itemListMoRecipe == null || itemListMoRecipe.size() <= 0) {
                errorMsg = "根据产线{" + prod_line_id + "}以及订单号{" + make_order + "}未能找到条码配方";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String recipe_id = itemListMoRecipe.get(0).get("recipe_id").toString();
            String sqlBarCodeRule = "select barcode_rule_code," +
                    "COALESCE(barcode_length,0) barcode_length,num_increase,next_num,change_recov_flag," +
                    "COALESCE(recov_next_num,1) recov_next_num," +
                    "COALESCE(fix_field1,'NAN') fix_field1," +
                    "COALESCE(fix_field2,'NAN') fix_field2," +
                    "COALESCE(fix_field3,'NAN') fix_field3," +
                    "COALESCE(fix_field4,'NAN') fix_field4," +
                    "COALESCE(fix_field5,'NAN') fix_field5," +
                    "COALESCE(fix_field6,'NAN') fix_field6," +
                    "COALESCE(fix_field7,'NAN') fix_field7," +
                    "COALESCE(fix_field8,'NAN') fix_field8," +
                    "COALESCE(change_field_type,'') change_field_type," +
                    "COALESCE(attr_field1,'') attr_field1," +
                    "COALESCE(attr_field2,'') attr_field2," +
                    "COALESCE(attr_field3,'') attr_field3," +
                    "COALESCE(attr_field4,'') attr_field4," +
                    "COALESCE(attr_field5,'') attr_field5," +
                    "COALESCE(attr_field6,'') attr_field6," +
                    "COALESCE(attr_field7,'') attr_field7," +
                    "COALESCE(attr_field8,'') attr_field8 " +
                    "from c_mes_fmod_recipe_barcode_rule where recipe_id=" + recipe_id + " " +
                    "and enable_flag='Y' LIMIT 1 OFFSET 0 ";
            List<Map<String, Object>> itemListBarCodeRule = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlBarCodeRule,
                    true, request, apiRoutePath);
            if (itemListBarCodeRule == null || itemListBarCodeRule.size() <= 0) {
                errorMsg = "根据产线{" + prod_line_id + "}以及订单号{" + make_order + "}未能找到条码配方";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Map<String, Object> mapRule = itemListBarCodeRule.get(0);
            String barcode_rule_code = mapRule.get("barcode_rule_code").toString();
            Integer barcode_length = Integer.parseInt(mapRule.get("barcode_length").toString());
            String barCodeResult = "";
            for (int i = 0; i < barcode_count; i++) {
                //下面需要根据具体的barcode_rule_code去做对应的定制化逻辑
                String barCode = "";
                switch (barcode_rule_code) {
                    case "GX_BARCODE"://国轩模组与PACK条码
                        Integer increaNum = ComonGetIncreaNum(station_code, mapRule, apiRoutePath, request);
                        barCode = mesGxBarCodeCreateFunc.CreateGxBarCode(increaNum, mapRule);
                        break;
                    case "GX_BARCODE_PACK"://唐山国轩PACK条码
                        Integer increaNum1 = ComonGetCarIncreaNum(station_code, barcode_rule_code, car_code, apiRoutePath, request);
                        Integer increaNum2 = ComonGetIncreaNum(station_code, mapRule, apiRoutePath, request);
                        barCode = mesGxBarCodeCreateFunc.CreateGxBarCodePack(increaNum1, increaNum2, mapRule);
                        break;
                    default:
                        break;
                }
                if (barcode_length > 0) {
                    if (barCode.length() != barcode_length) {
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                        errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}," +
                                "创建生成条码号{" + barCode + "},条码长度不符合规格要求长度为{" + barcode_length + "}";
                        transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        return transResult;
                    }
                }
                if (barCodeResult.equals("")) barCodeResult = barCode;
                else barCodeResult += "," + barCode;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, barCodeResult, "", 0);
        } catch (Exception ex) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg = "当前工位号{" + station_code + "},按照订单{" + make_order + "}配方生成条码发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //共用方法获取递增值
    public Integer ComonGetIncreaNum(String station_code, Map<String, Object> mapRule,
                                     String apiRoutePath, HttpServletRequest request) throws Exception {
        Integer increaNum = 0;
        String errorMsg = "";
        String nowDateTime = CFuncUtilsSystem.GetNowDateTime("");
        String barcode_rule_code = mapRule.get("barcode_rule_code").toString();
        Integer num_increase = Integer.parseInt(mapRule.get("num_increase").toString());
        Integer next_num = Integer.parseInt(mapRule.get("next_num").toString());
        String change_recov_flag = mapRule.get("change_recov_flag").toString();
        Integer recov_next_num = Integer.parseInt(mapRule.get("recov_next_num").toString());
        String fix_field1 = mapRule.get("fix_field1").toString();
        String fix_field2 = mapRule.get("fix_field2").toString();
        String fix_field3 = mapRule.get("fix_field3").toString();
        String fix_field4 = mapRule.get("fix_field4").toString();
        String fix_field5 = mapRule.get("fix_field5").toString();
        String fix_field6 = mapRule.get("fix_field6").toString();
        String fix_field7 = mapRule.get("fix_field7").toString();
        String fix_field8 = mapRule.get("fix_field8").toString();
        if (fix_field1.equals("null") || fix_field1.equals("")) fix_field1 = "NAN";
        if (fix_field2.equals("null") || fix_field2.equals("")) fix_field2 = "NAN";
        if (fix_field3.equals("null") || fix_field3.equals("")) fix_field3 = "NAN";
        if (fix_field4.equals("null") || fix_field4.equals("")) fix_field4 = "NAN";
        if (fix_field5.equals("null") || fix_field5.equals("")) fix_field5 = "NAN";
        if (fix_field6.equals("null") || fix_field6.equals("")) fix_field6 = "NAN";
        if (fix_field7.equals("null") || fix_field7.equals("")) fix_field7 = "NAN";
        if (fix_field8.equals("null") || fix_field8.equals("")) fix_field8 = "NAN";

        String attr_field2 = mapRule.get("attr_field2").toString();
        String change_field_type = mapRule.get("change_field_type").toString();
        try {
            if (!change_field_type.equals("DAY")
                    && !change_field_type.equals("MONTH")
                    && !change_field_type.equals("YEAR")
                    && !change_field_type.equals("ORDERNUM")
                    && !change_field_type.equals("NONE")) {
                errorMsg = "MES系统只支持按天|月|年改变增量,如需其他类型开发请联系系统开发定制需求";
                throw new Exception(errorMsg);
            }
            String change_field_value = "";
            if (change_field_type.equals("DAY")) {//获取当天
                change_field_value = new SimpleDateFormat("yyyyMMdd").format(new Date());
            } else if (change_field_type.equals("MONTH")) {//获取当月
                change_field_value = new SimpleDateFormat("yyyyMM").format(new Date());
            } else if (change_field_type.equals("YEAR")) {//获取当年
                change_field_value = new SimpleDateFormat("yyyy").format(new Date());
            } else if (change_field_type.equals("ORDERNUM")) {//订单号
                change_field_value = new SimpleDateFormat("yyyyMM").format(new Date()) + attr_field2;
            } else if (change_field_type.equals("NONE")) {//永久递增
                change_field_value = "NONE";
            }
            String sqlSelect = "select barcode_rule_d_id,now_num " +
                    "from c_mes_fmod_recipe_barcode_rule_d " +
                    "where barcode_rule_code='" + barcode_rule_code + "' " +
                    "and COALESCE(fix_field1,'NAN')='" + fix_field1 + "' " +
                    "and COALESCE(fix_field2,'NAN')='" + fix_field2 + "' " +
                    "and COALESCE(fix_field3,'NAN')='" + fix_field3 + "' " +
                    "and COALESCE(fix_field4,'NAN')='" + fix_field4 + "' " +
                    "and COALESCE(fix_field5,'NAN')='" + fix_field5 + "' " +
                    "and COALESCE(fix_field6,'NAN')='" + fix_field6 + "' " +
                    "and COALESCE(fix_field7,'NAN')='" + fix_field7 + "' " +
                    "and COALESCE(fix_field8,'NAN')='" + fix_field8 + "' " +
                    "and change_field_type='" + change_field_type + "' " +
                    "and change_field_value='" + change_field_value + "' ";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlSelect,
                    true, request, apiRoutePath);
            if (itemList != null && itemList.size() > 1) {
                errorMsg = "条码生成规则出现重码,请检查配方数据是否出现异常基础数据";
                throw new Exception(errorMsg);
            }
            if (itemList == null || itemList.size() <= 0) {//新的需要增加
                Integer nowNum = next_num;
                //先判断之前是否做了插入
                String sqlOnlySelect = "select count(1) from c_mes_fmod_recipe_barcode_rule_d " +
                        "where barcode_rule_code='" + barcode_rule_code + "'";
                Integer onlyCount = cFuncDbSqlResolve.GetSelectCount(sqlOnlySelect);
                if (onlyCount > 0) {
                    if (change_recov_flag.equals("Y")) nowNum = recov_next_num;
                }
                long increNumId = cFuncDbSqlResolve.GetIncreaseID("c_mes_fmod_recipe_barcode_rule_d_id_seq", false);
                String sqlInsert = "insert into c_mes_fmod_recipe_barcode_rule_d " +
                        "(created_by,creation_date,barcode_rule_d_id,barcode_rule_code,fix_field1,fix_field2," +
                        "fix_field3,fix_field4,fix_field5,fix_field6,fix_field7,fix_field8,change_field_type," +
                        "change_field_value,now_num) values " +
                        "('" + station_code + "','" + nowDateTime + "'," + increNumId + ",'" + barcode_rule_code + "','" + fix_field1 + "','" + fix_field2 + "'," +
                        "'" + fix_field3 + "','" + fix_field4 + "','" + fix_field5 + "','" + fix_field6 + "','" + fix_field7 + "','" + fix_field8 + "','" + change_field_type + "'," +
                        "'" + change_field_value + "'," + nowNum + ")";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsert, true, request, apiRoutePath);
                increaNum = nowNum;
                return increaNum;
            }
            //有旧的存在
            String barcode_rule_d_id = itemList.get(0).get("barcode_rule_d_id").toString();
            Integer now_num = Integer.parseInt(itemList.get(0).get("now_num").toString());
            increaNum = now_num + num_increase;
            String sqlUpdate = "update c_mes_fmod_recipe_barcode_rule_d set " +
                    "last_updated_by='" + station_code + "'," +
                    "last_update_date='" + nowDateTime + "'," +
                    "now_num=" + increaNum + " where barcode_rule_d_id=" + barcode_rule_d_id;
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdate, true, request, apiRoutePath);
        } catch (Exception ex) {
            errorMsg = "条码规则{" + barcode_rule_code + "},获取递增值发生异常:" + ex.getMessage();
            throw new Exception(errorMsg);
        }
        return increaNum;
    }

    //共用方法获取递增值-唐山国轩，按车辆码生产箱体流水号
    public Integer ComonGetCarIncreaNum(String station_code, String barcode_rule_code, String car_code,
                                        String apiRoutePath, HttpServletRequest request) throws Exception {
        Integer increaNum = 0;
        String errorMsg = "";
        String nowDateTime = CFuncUtilsSystem.GetNowDateTime("");
        Integer num_increase = 1;
        Integer next_num = 1;
        try {
            String change_field_type = "CAR_CODE";
            String change_field_value = car_code;
            String sqlSelect = "select barcode_rule_d2_id,now_num " +
                    "from c_mes_fmod_recipe_barcode_rule_d2 " +
                    "where barcode_rule_code='" + barcode_rule_code + "' " +
                    "and change_field_value='" + change_field_value + "' ";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlSelect,
                    true, request, apiRoutePath);
            if (itemList != null && itemList.size() > 1) {
                errorMsg = "条码生成规则出现重码,请检查配方数据是否出现异常基础数据";
                throw new Exception(errorMsg);
            }
            if (itemList == null || itemList.size() <= 0) {//新的需要增加
                Integer nowNum = next_num;
                long increNumId = cFuncDbSqlResolve.GetIncreaseID("c_mes_fmod_recipe_barcode_rule_d2_id_seq", false);
                String sqlInsert = "insert into c_mes_fmod_recipe_barcode_rule_d2 " +
                        "(created_by,creation_date,barcode_rule_d2_id,barcode_rule_code,change_field_type," +
                        "change_field_value,now_num) values " +
                        "('" + station_code + "','" + nowDateTime + "'," + increNumId + ",'" + barcode_rule_code + "','" + change_field_type + "'," +
                        "'" + change_field_value + "'," + nowNum + ")";
                cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlInsert, true, request, apiRoutePath);
                increaNum = nowNum;
                return increaNum;
            }
            //有旧的存在
            String barcode_rule_d2_id = itemList.get(0).get("barcode_rule_d2_id").toString();
            Integer now_num = Integer.parseInt(itemList.get(0).get("now_num").toString());
            increaNum = now_num + num_increase;
            String sqlUpdate = "update c_mes_fmod_recipe_barcode_rule_d2 set " +
                    "last_updated_by='" + station_code + "'," +
                    "last_update_date='" + nowDateTime + "'," +
                    "now_num=" + increaNum + " where barcode_rule_d2_id=" + barcode_rule_d2_id;
            cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdate, true, request, apiRoutePath);
        } catch (Exception ex) {
            errorMsg = "条码规则{" + barcode_rule_code + "},获取递增值发生异常:" + ex.getMessage();
            throw new Exception(errorMsg);
        }
        return increaNum;
    }
}
