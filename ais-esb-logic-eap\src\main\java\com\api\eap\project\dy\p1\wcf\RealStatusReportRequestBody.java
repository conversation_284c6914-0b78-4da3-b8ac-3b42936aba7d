
package com.api.eap.project.dy.p1.wcf;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>realStatusReportRequestBody complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="realStatusReportRequestBody"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="cim_mode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="auto_mode" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="status_id" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="alarm_id" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tower_red" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tower_yellow" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tower_green" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="tower_blue" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "realStatusReportRequestBody", propOrder = {
    "cimMode",
    "autoMode",
    "statusId",
    "alarmId",
    "towerRed",
    "towerYellow",
    "towerGreen",
    "towerBlue"
})
public class RealStatusReportRequestBody {

    @XmlElement(name = "cim_mode")
    protected String cimMode;
    @XmlElement(name = "auto_mode")
    protected String autoMode;
    @XmlElement(name = "status_id")
    protected String statusId;
    @XmlElement(name = "alarm_id")
    protected String alarmId;
    @XmlElement(name = "tower_red")
    protected String towerRed;
    @XmlElement(name = "tower_yellow")
    protected String towerYellow;
    @XmlElement(name = "tower_green")
    protected String towerGreen;
    @XmlElement(name = "tower_blue")
    protected String towerBlue;

    /**
     * 获取cimMode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCimMode() {
        return cimMode;
    }

    /**
     * 设置cimMode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCimMode(String value) {
        this.cimMode = value;
    }

    /**
     * 获取autoMode属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAutoMode() {
        return autoMode;
    }

    /**
     * 设置autoMode属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAutoMode(String value) {
        this.autoMode = value;
    }

    /**
     * 获取statusId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getStatusId() {
        return statusId;
    }

    /**
     * 设置statusId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setStatusId(String value) {
        this.statusId = value;
    }

    /**
     * 获取alarmId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getAlarmId() {
        return alarmId;
    }

    /**
     * 设置alarmId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAlarmId(String value) {
        this.alarmId = value;
    }

    /**
     * 获取towerRed属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTowerRed() {
        return towerRed;
    }

    /**
     * 设置towerRed属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTowerRed(String value) {
        this.towerRed = value;
    }

    /**
     * 获取towerYellow属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTowerYellow() {
        return towerYellow;
    }

    /**
     * 设置towerYellow属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTowerYellow(String value) {
        this.towerYellow = value;
    }

    /**
     * 获取towerGreen属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTowerGreen() {
        return towerGreen;
    }

    /**
     * 设置towerGreen属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTowerGreen(String value) {
        this.towerGreen = value;
    }

    /**
     * 获取towerBlue属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getTowerBlue() {
        return towerBlue;
    }

    /**
     * 设置towerBlue属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTowerBlue(String value) {
        this.towerBlue = value;
    }

}
