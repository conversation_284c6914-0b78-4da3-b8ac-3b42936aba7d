package com.api.mes.project.sh;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Map;

/**
 * <p>
 * 双环定制化条码生成
 * 1.模组条码与PACK条码
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@Service
@Slf4j
public class MesShBarCodeCreateFunc {

    //生成双环零部件二维追溯码
    //双环零部件二维追溯码生成规则：如 00003.1.02.20.003072AV2A10001
    public String CreateShBarCode(Integer increaNum, Map<String, Object> mapRule) throws Exception {
        String barCode = "";
        String attr_field1 = mapRule.get("attr_field1").toString();//00003.1.02.20.00307 --零部件序列号(举例)
        String attr_field2 = mapRule.get("attr_field2").toString();//2AV   --供应商编码(举例)
        String attr_field3 = mapRule.get("attr_field3").toString();
        String attr_field4 = mapRule.get("attr_field4").toString();
        String attr_field5 = mapRule.get("attr_field5").toString();
        String attr_field6 = mapRule.get("attr_field6").toString();
        try {
            //B--年
            //8--月
            //4--日
            //获取当前年份
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;
            int day = cal.get(Calendar.DATE);
            String yearHeader = "";
            String monthHeader = "";
            String dayHeader = "";
            switch (year) {
                case 2020:
                    yearHeader = "3";
                    break;
                case 2021:
                    yearHeader = "4";
                    break;
                case 2022:
                    yearHeader = "5";
                    break;
                case 2023:
                    yearHeader = "6";
                    break;
                case 2024:
                    yearHeader = "7";
                    break;
                case 2025:
                    yearHeader = "8";
                    break;
                case 2026:
                    yearHeader = "9";
                    break;
                case 2027:
                    yearHeader = "A";
                    break;
                case 2028:
                    yearHeader = "B";
                    break;
                case 2029:
                    yearHeader = "C";
                    break;
                case 2030:
                    yearHeader = "D";
                    break;
                case 2031:
                    yearHeader = "E";
                    break;
                case 2032:
                    yearHeader = "F";
                    break;
                case 2033:
                    yearHeader = "G";
                    break;
                case 2034:
                    yearHeader = "H";
                    break;
                case 2035:
                    yearHeader = "J";
                    break;
                case 2036:
                    yearHeader = "K";
                    break;
                case 2037:
                    yearHeader = "L";
                    break;
                case 2038:
                    yearHeader = "M";
                    break;
                case 2039:
                    yearHeader = "N";
                    break;
                case 2040:
                    yearHeader = "P";
                    break;
                case 2041:
                    yearHeader = "R";
                    break;
                case 2042:
                    yearHeader = "S";
                    break;
                case 2043:
                    yearHeader = "T";
                    break;
            }
            //月
            if (month <= 9) monthHeader = String.valueOf(month);
            else if (month == 10) monthHeader = "A";
            else if (month == 11) monthHeader = "B";
            else if (month == 12) monthHeader = "C";
            //天
            switch (day) {
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                case 8:
                case 9:
                    dayHeader = String.valueOf(day);
                    break;
                case 10:
                    dayHeader = "A";
                    break;
                case 11:
                    dayHeader = "B";
                    break;
                case 12:
                    dayHeader = "C";
                    break;
                case 13:
                    dayHeader = "D";
                    break;
                case 14:
                    dayHeader = "E";
                    break;
                case 15:
                    dayHeader = "F";
                    break;
                case 16:
                    dayHeader = "G";
                    break;
                case 17:
                    dayHeader = "H";
                    break;
                case 18:
                    dayHeader = "J";
                    break;
                case 19:
                    dayHeader = "K";
                    break;
                case 20:
                    dayHeader = "L";
                    break;
                case 21:
                    dayHeader = "M";
                    break;
                case 22:
                    dayHeader = "N";
                    break;
                case 23:
                    dayHeader = "P";
                    break;
                case 24:
                    dayHeader = "R";
                    break;
                case 25:
                    dayHeader = "S";
                    break;
                case 26:
                    dayHeader = "T";
                    break;
                case 27:
                    dayHeader = "U";
                    break;
                case 28:
                    dayHeader = "V";
                    break;
                case 29:
                    dayHeader = "W";
                    break;
                case 30:
                    dayHeader = "X";
                    break;
                case 31:
                    dayHeader = "Y";
                    break;
            }
            barCode = attr_field1 + attr_field2 + yearHeader + monthHeader + dayHeader +
                    String.format("%04d", increaNum);
        } catch (Exception ex) {
            throw ex;
        }
        return barCode;
    }

    //生成双环箱体码
    //双环箱体码生成规则：如 JSQ-01-CE031006A1-240912-00001
    //increaNum1：箱体编号 从c_mes_fmod_recipe_barcode_rule_d2获取当前序号 第X11-X12位
    //increaNum2:流水号
    public String CreateShBarCodePack(Integer increaNum , Map<String, Object> mapRule) throws Exception {
        String barCode = "";
        String attr_field1 = mapRule.get("attr_field1").toString();//JSQ --产线类别(举例)
        String attr_field2 = mapRule.get("attr_field2").toString();//01   --线别(举例)
        String attr_field3 = mapRule.get("attr_field3").toString();//CE031006A1   --双环产品内部代号(举例)
        String attr_field4 = mapRule.get("attr_field4").toString();
        String attr_field5 = mapRule.get("attr_field5").toString();
        String attr_field6 = mapRule.get("attr_field6").toString();
        String attr_field7 = mapRule.get("attr_field7").toString();
        try {
            //B--年
            //8--月
            //4--日
            //获取当前年份
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;
            int day = cal.get(Calendar.DATE);
            String yearHeader = "";
            String monthHeader = "";
            String dayHeader = "";
            switch (year) {
                case 2020:
                    yearHeader = "20";
                    break;
                case 2021:
                    yearHeader = "21";
                    break;
                case 2022:
                    yearHeader = "22";
                    break;
                case 2023:
                    yearHeader = "23";
                    break;
                case 2024:
                    yearHeader = "24";
                    break;
                case 2025:
                    yearHeader = "25";
                    break;
                case 2026:
                    yearHeader = "26";
                    break;
                case 2027:
                    yearHeader = "27";
                    break;
                case 2028:
                    yearHeader = "28";
                    break;
                case 2029:
                    yearHeader = "29";
                    break;
                case 2030:
                    yearHeader = "30";
                    break;
                case 2031:
                    yearHeader = "31";
                    break;
                case 2032:
                    yearHeader = "32";
                    break;
                case 2033:
                    yearHeader = "33";
                    break;
                case 2034:
                    yearHeader = "34";
                    break;
                case 2035:
                    yearHeader = "35";
                    break;
                case 2036:
                    yearHeader = "36";
                    break;
                case 2037:
                    yearHeader = "37";
                    break;
                case 2038:
                    yearHeader = "38";
                    break;
                case 2039:
                    yearHeader = "39";
                    break;
                case 2040:
                    yearHeader = "40";
                    break;
            }
            //月
            monthHeader = String.valueOf(month);
            if (month <= 9) {
                monthHeader = "0" + monthHeader;
            }
            //天
            dayHeader = String.valueOf(day);
            if (day <= 9) {
                dayHeader = "0" + dayHeader;
            }
            barCode = attr_field1 + "-" + attr_field2 + "-" + attr_field3 + "-" + yearHeader + monthHeader + dayHeader + "-" + String.format("%05d", increaNum);
        } catch (Exception ex) {
            throw ex;
        }
        return barCode;
    }
}
