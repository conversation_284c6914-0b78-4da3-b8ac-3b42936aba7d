package com.api.mes.core.recipe;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

@Service
@Slf4j
public class MesCoreRecipeFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //获取工位工单完成数量
    public Integer GetStationMoFinishCount(String prod_line_code,String station_code,String make_order) throws Exception{
        Integer mo_finish_count=0;
        String meStationMoFinishTable = "c_mes_me_station_mo_finish";
        try{
            if(prod_line_code==null || prod_line_code.isEmpty() ||
                    station_code==null || station_code.isEmpty() ||
                    make_order==null || make_order.isEmpty()){
                return mo_finish_count;
            }
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("make_order").is(make_order));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationMoFinishTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                mo_finish_count=docItemBigData.getInteger("mo_finish_count");
                iteratorBigData.close();
            }
        }
        catch (Exception ex){
            log.warn("GetStationMoFinishCount"+ex);
        }
        return mo_finish_count;
    }

    //2.增加工位工单完工数量
    public void AddStationMoFinishCount(String prod_line_code,String station_code,String make_order,Integer add_count) throws Exception{
        String meStationMoFinishTable = "c_mes_me_station_mo_finish";
        String meStationFlowTable = "c_mes_me_station_flow";
        try{
            if(prod_line_code==null || prod_line_code.isEmpty() ||
                    station_code==null || station_code.isEmpty() ||
                    make_order==null || make_order.isEmpty() ||
                    add_count==null || add_count<=0){
                return;
            }
            Integer mo_finish_count=0;
            Boolean isExistRow=false;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("make_order").is(make_order));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationMoFinishTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                isExistRow=true;
                mo_finish_count=docItemBigData.getInteger("mo_finish_count");
                iteratorBigData.close();
            }
            if(!isExistRow){
                //第一次从过站数据中查询出来
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
                queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
                queryBigData.addCriteria(Criteria.where("make_order").is(make_order));
                queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                long moFlowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
                //判断
                long mo_finish_count2=moFlowCount;
                if(mo_finish_count2+(long)add_count>Integer.MAX_VALUE) mo_finish_count2=(long)add_count;
                else mo_finish_count2+=(long)add_count;
                mo_finish_count=(int)mo_finish_count2;
                //新增
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("prod_line_code",prod_line_code);
                mapBigDataRow.put("station_code",station_code);
                mapBigDataRow.put("make_order",make_order);
                mapBigDataRow.put("mo_finish_count",mo_finish_count);
                mongoTemplate.insert(mapBigDataRow, meStationMoFinishTable);
                return;
            }
            long mo_finish_count2=(long)mo_finish_count;
            if(mo_finish_count2+(long)add_count>Integer.MAX_VALUE) mo_finish_count2=(long)add_count;
            else mo_finish_count2+=(long)add_count;
            mo_finish_count=(int)mo_finish_count2;
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("prod_line_code").is(prod_line_code));
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("make_order").is(make_order));
            Update updateBigData = new Update();
            updateBigData.set("mo_finish_count", mo_finish_count);
            mongoTemplate.updateFirst(queryBigData, updateBigData, meStationMoFinishTable);
        }
        catch (Exception ex){
            log.warn("AddStationMoFinishCount"+ex);
        }
    }
}
