package com.api.eap.project.thailand.hs;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 泰国沪士EAP事件接口(Sub)公共方法
 * 1.通用事件上报
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Service
@Slf4j
public class EapTlHsSendEventSubFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //1.通用事件上报SUB方法
    public JSONObject ComomEventSubFunc(String esbInterfCode,JSONObject postParas) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String token="";
        String requestParas="";
        String responseParas="";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);

            //1.创建参数
            requestParas=postParas.toString();

            //2.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack= cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url,postParas);
            responseParas=jsonObjectBack.toString();
            Integer status_code=jsonObjectBack.getInteger("status_code");
            if(status_code!=200){
                errorMsg=jsonObjectBack.getString("message");
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //成功
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送成功");
        }
        catch (Exception ex){
            errorMsg="Exception:"+ex;
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
