package com.api.system;

import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.api.base.IMybatisBasic;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 快速编码
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-28
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("sys_fastcode")
public class FastCode extends IMybatisBasic implements Serializable
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @JsonProperty("fastcode_id")
    @JSONField(name = "fastcode_id")
    @TableId(value = "fastcode_id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "组ID")
    @JsonProperty("fastcode_group_id")
    @JSONField(name = "fastcode_group_id")
    @TableField(value = "fastcode_group_id")
    private Long parentId;

    @ApiModelProperty(value = "编码")
    @JsonProperty("fastcode_code")
    @JSONField(name = "fastcode_code")
    @TableField(value = "fastcode_code")
    private String code;

    @ApiModelProperty(value = "描述")
    @JsonProperty("fastcode_des")
    @JSONField(name = "fastcode_des")
    @TableField(value = "fastcode_des")
    private String desc;

    @ApiModelProperty(value = "排序")
    @JsonProperty("fastcode_order")
    @JSONField(name = "fastcode_order")
    @TableField(value = "fastcode_order")
    private Integer order;

    public static List<FastCode> listByParentId(Long parentId, FastCodeMapper mapper)
    {
        QueryWrapper<FastCode> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(Const.PROPERTY_ENABLE_FLAG,  Const.FLAG_Y);
        queryWrapper.eq("fastcode_group_id", parentId);
        return mapper.selectList(queryWrapper);
    }
}
