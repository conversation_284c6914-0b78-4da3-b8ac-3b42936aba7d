package com.api.mes.project.gx;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Calendar;
import java.util.Map;

/**
 * <p>
 * 国轩定制化条码生成
 * 1.模组条码与PACK条码
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-30
 */
@Service
@Slf4j
public class MesGxBarCodeCreateFunc {

    //生成国轩模组与PACK条码
    //国轩模组与PACK生成规则：如 03HMB03X0000FDB840000001
    public String CreateGxBarCode(Integer increaNum, Map<String, Object> mapRule) throws Exception {
        String barCode = "";
        String attr_field1 = mapRule.get("attr_field1").toString();//03H --公司代码(举例)
        String attr_field2 = mapRule.get("attr_field2").toString();//M   --产品类型(举例)
        String attr_field3 = mapRule.get("attr_field3").toString();//B   --电池类型(举例)
        String attr_field4 = mapRule.get("attr_field4").toString();//03X  --规格代码(举例)
        String attr_field5 = mapRule.get("attr_field5").toString();//0000FD  --追溯信息代码(举例)
        String attr_field6 = mapRule.get("attr_field6").toString();//0       --9代表实验产品，8代表返工产品，0代表批量
        try {
            //B--年
            //8--月
            //4--日
            //获取当前年份
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;
            int day = cal.get(Calendar.DATE);
            String yearHeader = "";
            String monthHeader = "";
            String dayHeader = "";
            switch (year) {
                case 2020:
                    yearHeader = "A";
                    break;
                case 2021:
                    yearHeader = "B";
                    break;
                case 2022:
                    yearHeader = "C";
                    break;
                case 2023:
                    yearHeader = "D";
                    break;
                case 2024:
                    yearHeader = "E";
                    break;
                case 2025:
                    yearHeader = "F";
                    break;
                case 2026:
                    yearHeader = "G";
                    break;
                case 2027:
                    yearHeader = "H";
                    break;
                case 2028:
                    yearHeader = "J";
                    break;
                case 2029:
                    yearHeader = "K";
                    break;
                case 2030:
                    yearHeader = "L";
                    break;
                case 2031:
                    yearHeader = "M";
                    break;
                case 2032:
                    yearHeader = "N";
                    break;
                case 2033:
                    yearHeader = "P";
                    break;
                case 2034:
                    yearHeader = "R";
                    break;
                case 2035:
                    yearHeader = "S";
                    break;
                case 2036:
                    yearHeader = "T";
                    break;
                case 2037:
                    yearHeader = "V";
                    break;
                case 2038:
                    yearHeader = "W";
                    break;
                case 2039:
                    yearHeader = "X";
                    break;
                case 2040:
                    yearHeader = "Y";
                    break;
            }
            //月
            if (month <= 9) monthHeader = String.valueOf(month);
            else if (month == 10) monthHeader = "A";
            else if (month == 11) monthHeader = "B";
            else if (month == 12) monthHeader = "C";
            //天
            switch (day) {
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                case 8:
                case 9:
                    dayHeader = String.valueOf(day);
                    break;
                case 10:
                    dayHeader = "A";
                    break;
                case 11:
                    dayHeader = "B";
                    break;
                case 12:
                    dayHeader = "C";
                    break;
                case 13:
                    dayHeader = "D";
                    break;
                case 14:
                    dayHeader = "E";
                    break;
                case 15:
                    dayHeader = "F";
                    break;
                case 16:
                    dayHeader = "G";
                    break;
                case 17:
                    dayHeader = "H";
                    break;
                case 18:
                    dayHeader = "J";
                    break;
                case 19:
                    dayHeader = "K";
                    break;
                case 20:
                    dayHeader = "L";
                    break;
                case 21:
                    dayHeader = "M";
                    break;
                case 22:
                    dayHeader = "N";
                    break;
                case 23:
                    dayHeader = "P";
                    break;
                case 24:
                    dayHeader = "R";
                    break;
                case 25:
                    dayHeader = "S";
                    break;
                case 26:
                    dayHeader = "T";
                    break;
                case 27:
                    dayHeader = "V";
                    break;
                case 28:
                    dayHeader = "W";
                    break;
                case 29:
                    dayHeader = "X";
                    break;
                case 30:
                    dayHeader = "Y";
                    break;
                case 31:
                    dayHeader = "0";
                    break;
            }
            barCode = attr_field1 + attr_field2 + attr_field3 + attr_field4 + attr_field5 + yearHeader + monthHeader + dayHeader + attr_field6 + String.format("%06d", increaNum);
        } catch (Exception ex) {
            throw ex;
        }
        return barCode;
    }

    //生成国轩模组与PACK条码
    //国轩模组与PACK生成规则：如 03HMB03X0000FDB840000001
    //新的编码规则
    public String CreateGxBarCodeNew(Integer increaNum, Map<String, Object> mapRule) throws Exception {
        String barCode = "";
        String attr_field1 = mapRule.get("attr_field1").toString();//03H --公司代码(举例)
        String attr_field2 = mapRule.get("attr_field2").toString();//M   --产品类型(举例)
        String attr_field3 = mapRule.get("attr_field3").toString();//B   --电池类型(举例)
        String attr_field4 = mapRule.get("attr_field4").toString();//03X  --规格代码(举例)
        String attr_field5 = mapRule.get("attr_field5").toString();//0000FD  --追溯信息代码(举例)
        String attr_field6 = mapRule.get("attr_field6").toString();//0       --9代表实验产品，8代表返工产品，0代表批量
        String attr_field7 = mapRule.get("attr_field7").toString();//1       --1代表正模组，2代表负模组，仅限模组
        try {
            //B--年
            //8--月
            //4--日
            //获取当前年份
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;
            int day = cal.get(Calendar.DATE);
            String yearHeader = "";
            String monthHeader = "";
            String dayHeader = "";
            switch (year) {
                case 2020:
                    yearHeader = "A";
                    break;
                case 2021:
                    yearHeader = "B";
                    break;
                case 2022:
                    yearHeader = "C";
                    break;
                case 2023:
                    yearHeader = "D";
                    break;
                case 2024:
                    yearHeader = "E";
                    break;
                case 2025:
                    yearHeader = "F";
                    break;
                case 2026:
                    yearHeader = "G";
                    break;
                case 2027:
                    yearHeader = "H";
                    break;
                case 2028:
                    yearHeader = "J";
                    break;
                case 2029:
                    yearHeader = "K";
                    break;
                case 2030:
                    yearHeader = "L";
                    break;
                case 2031:
                    yearHeader = "M";
                    break;
                case 2032:
                    yearHeader = "N";
                    break;
                case 2033:
                    yearHeader = "P";
                    break;
                case 2034:
                    yearHeader = "R";
                    break;
                case 2035:
                    yearHeader = "S";
                    break;
                case 2036:
                    yearHeader = "T";
                    break;
                case 2037:
                    yearHeader = "V";
                    break;
                case 2038:
                    yearHeader = "W";
                    break;
                case 2039:
                    yearHeader = "X";
                    break;
                case 2040:
                    yearHeader = "Y";
                    break;
            }
            //月
            if (month <= 9) monthHeader = String.valueOf(month);
            else if (month == 10) monthHeader = "A";
            else if (month == 11) monthHeader = "B";
            else if (month == 12) monthHeader = "C";
            //天
            switch (day) {
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                case 8:
                case 9:
                    dayHeader = String.valueOf(day);
                    break;
                case 10:
                    dayHeader = "A";
                    break;
                case 11:
                    dayHeader = "B";
                    break;
                case 12:
                    dayHeader = "C";
                    break;
                case 13:
                    dayHeader = "D";
                    break;
                case 14:
                    dayHeader = "E";
                    break;
                case 15:
                    dayHeader = "F";
                    break;
                case 16:
                    dayHeader = "G";
                    break;
                case 17:
                    dayHeader = "H";
                    break;
                case 18:
                    dayHeader = "J";
                    break;
                case 19:
                    dayHeader = "K";
                    break;
                case 20:
                    dayHeader = "L";
                    break;
                case 21:
                    dayHeader = "M";
                    break;
                case 22:
                    dayHeader = "N";
                    break;
                case 23:
                    dayHeader = "P";
                    break;
                case 24:
                    dayHeader = "R";
                    break;
                case 25:
                    dayHeader = "S";
                    break;
                case 26:
                    dayHeader = "T";
                    break;
                case 27:
                    dayHeader = "V";
                    break;
                case 28:
                    dayHeader = "W";
                    break;
                case 29:
                    dayHeader = "X";
                    break;
                case 30:
                    dayHeader = "Y";
                    break;
                case 31:
                    dayHeader = "0";
                    break;
            }
            barCode = attr_field1 + attr_field2 + attr_field3 + attr_field4 + attr_field5 + yearHeader + monthHeader + dayHeader + attr_field6 + attr_field7 + String.format("%05d", increaNum);
        } catch (Exception ex) {
            throw ex;
        }
        return barCode;
    }

    //生成唐山国轩PACK条码
    //唐山国轩PACK生成规则：如 03HMB03X0001FDB840000001
    //increaNum1：箱体编号 从c_mes_fmod_recipe_barcode_rule_d2获取当前序号 第X11-X12位
    //increaNum2:流水号
    public String CreateGxBarCodePack(Integer increaNum1, Integer increaNum2, Map<String, Object> mapRule) throws Exception {
        String barCode = "";
        String attr_field1 = mapRule.get("attr_field1").toString();//03H --公司代码(举例)
        String attr_field2 = mapRule.get("attr_field2").toString();//M   --产品类型(举例)
        String attr_field3 = mapRule.get("attr_field3").toString();//B   --电池类型(举例)
        String attr_field4 = mapRule.get("attr_field4").toString();//03X  --规格代码(举例)
        String attr_field5 = mapRule.get("attr_field5").toString();//00 --追溯信息代码(举例) -批次及拓展码
        String attr_field6 = mapRule.get("attr_field6").toString();//0  --9代表实验产品，8代表返工产品，0代表批量
        String attr_field7 = mapRule.get("attr_field7").toString();//FD  --追溯信息代码(举例) -产线及产地代码
        try {
            //B--年
            //8--月
            //4--日
            //获取当前年份
            Calendar cal = Calendar.getInstance();
            int year = cal.get(Calendar.YEAR);
            int month = cal.get(Calendar.MONTH) + 1;
            int day = cal.get(Calendar.DATE);
            String yearHeader = "";
            String monthHeader = "";
            String dayHeader = "";
            switch (year) {
                case 2020:
                    yearHeader = "A";
                    break;
                case 2021:
                    yearHeader = "B";
                    break;
                case 2022:
                    yearHeader = "C";
                    break;
                case 2023:
                    yearHeader = "D";
                    break;
                case 2024:
                    yearHeader = "E";
                    break;
                case 2025:
                    yearHeader = "F";
                    break;
                case 2026:
                    yearHeader = "G";
                    break;
                case 2027:
                    yearHeader = "H";
                    break;
                case 2028:
                    yearHeader = "J";
                    break;
                case 2029:
                    yearHeader = "K";
                    break;
                case 2030:
                    yearHeader = "L";
                    break;
                case 2031:
                    yearHeader = "M";
                    break;
                case 2032:
                    yearHeader = "N";
                    break;
                case 2033:
                    yearHeader = "P";
                    break;
                case 2034:
                    yearHeader = "R";
                    break;
                case 2035:
                    yearHeader = "S";
                    break;
                case 2036:
                    yearHeader = "T";
                    break;
                case 2037:
                    yearHeader = "V";
                    break;
                case 2038:
                    yearHeader = "W";
                    break;
                case 2039:
                    yearHeader = "X";
                    break;
                case 2040:
                    yearHeader = "Y";
                    break;
            }
            //月
            if (month <= 9) monthHeader = String.valueOf(month);
            else if (month == 10) monthHeader = "A";
            else if (month == 11) monthHeader = "B";
            else if (month == 12) monthHeader = "C";
            //天
            switch (day) {
                case 1:
                case 2:
                case 3:
                case 4:
                case 5:
                case 6:
                case 7:
                case 8:
                case 9:
                    dayHeader = String.valueOf(day);
                    break;
                case 10:
                    dayHeader = "A";
                    break;
                case 11:
                    dayHeader = "B";
                    break;
                case 12:
                    dayHeader = "C";
                    break;
                case 13:
                    dayHeader = "D";
                    break;
                case 14:
                    dayHeader = "E";
                    break;
                case 15:
                    dayHeader = "F";
                    break;
                case 16:
                    dayHeader = "G";
                    break;
                case 17:
                    dayHeader = "H";
                    break;
                case 18:
                    dayHeader = "J";
                    break;
                case 19:
                    dayHeader = "K";
                    break;
                case 20:
                    dayHeader = "L";
                    break;
                case 21:
                    dayHeader = "M";
                    break;
                case 22:
                    dayHeader = "N";
                    break;
                case 23:
                    dayHeader = "P";
                    break;
                case 24:
                    dayHeader = "R";
                    break;
                case 25:
                    dayHeader = "S";
                    break;
                case 26:
                    dayHeader = "T";
                    break;
                case 27:
                    dayHeader = "V";
                    break;
                case 28:
                    dayHeader = "W";
                    break;
                case 29:
                    dayHeader = "X";
                    break;
                case 30:
                    dayHeader = "Y";
                    break;
                case 31:
                    dayHeader = "0";
                    break;
            }
            barCode = attr_field1 + attr_field2 + attr_field3 + attr_field4 + attr_field5 + String.format("%02d", increaNum1) + attr_field7 + yearHeader + monthHeader + dayHeader + attr_field6 + String.format("%06d", increaNum2);
        } catch (Exception ex) {
            throw ex;
        }
        return barCode;
    }
}
