package com.api.dcs.core.aps;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 生产任务逻辑
 * 1.传输上料工位查询可以使用订单
 * 2.任务状态修改
 * 3.根据RFID任务号查询任务相关信息
 * 4.判断是否存在计划中的任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-20
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/aps")
public class DcsCoreApsController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.传输上料工位查询可以使用订单
    @RequestMapping(value = "/DcsCoreApsOnlineSel", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreApsOnlineSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/aps/DcsCoreApsOnlineSel";
        String selectResult = "";
        String errorMsg = "";
        String apsTaskTable = "b_dcs_aps_task";
        String dcsFlowEvent = "b_dcs_flow_event";
        try {
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_status").is("PLAN"));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "task_order"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(10).iterator();
            List<Map<String, Object>> itemList = new ArrayList<>();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapItem = new HashMap<>();
                String is_auto_car = docItemBigData.getString("is_auto_car");
                String model_type = docItemBigData.getString("model_type");
                String is_auto_blast = docItemBigData.getString("is_auto_blast");
                if (is_auto_blast == null || is_auto_blast.equals("")) is_auto_blast = "N";
                String task_num = docItemBigData.getString("task_num");
                mapItem.put("mo_id", docItemBigData.getString("mo_id"));
                mapItem.put("task_num", task_num);
                mapItem.put("task_from", docItemBigData.getString("task_from"));
                mapItem.put("serial_num", docItemBigData.getString("serial_num"));
                mapItem.put("lot_num", docItemBigData.getString("lot_num"));
                mapItem.put("task_type", docItemBigData.getString("task_type"));
                mapItem.put("model_type", model_type);
                mapItem.put("m_length", docItemBigData.getDouble("m_length"));
                mapItem.put("m_width", docItemBigData.getDouble("m_width"));
                mapItem.put("m_height", docItemBigData.getDouble("m_height"));
                mapItem.put("m_weight", docItemBigData.getDouble("m_weight"));
                mapItem.put("m_texture", docItemBigData.getString("m_texture"));
                mapItem.put("cut_code", docItemBigData.getString("cut_code"));
                mapItem.put("is_auto_blast", docItemBigData.getString("is_auto_blast"));
                mapItem.put("is_auto_car", is_auto_car);
                mapItem.put("is_auto_print", docItemBigData.getString("is_auto_print"));
                mapItem.put("is_auto_cut", docItemBigData.getString("is_auto_cut"));
                mapItem.put("is_auto_sort", docItemBigData.getString("is_auto_sort"));
                mapItem.put("is_center", docItemBigData.getString("is_center"));
                if (!is_auto_car.equals("Y")) {
                    //代表不需要选择天车
                    itemList.add(mapItem);
                    iteratorBigData.close();
                    //如果为手动上料则插入event
                    StringBuffer eventMsg = new StringBuffer();
                    eventMsg.append("G2工位-任务号：").append(task_num).append(" 切割文件：").append(docItemBigData.getString("nc_name")).append(" 为手动上料，上料完成后，请人工确认放行");
                    Map<String, Object> event = new HashMap();
                    event.put("event_msg", eventMsg.toString());
                    event.put("item_date", CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
                    event.put("item_date_val", CFuncUtilsSystem.GetMongoDataValue(CFuncUtilsSystem.GetMongoISODate("")));
                    event.put("event_data", "G2");
                    event.put("event_type", "1002");
                    event.put("id", UUID.randomUUID().toString().replace("-", ""));
                    event.put("handle_flag", "N");
                    event.put("task_num",task_num);
                    mongoTemplate.insert(event, dcsFlowEvent);
                    break;
                }
                if (!is_auto_blast.equals("Y")) {
                    //A:不需要抛丸后钢板
                    //验证库存型号,若后续存在抛丸,则再进行修改抛丸
                    String sqlStockCount = "select count(1) " + "from b_dcs_wms_fmod_stock a inner join b_dcs_fmod_model b " + "on a.model_id=b.model_id " + "where a.enable_flag='Y' and b.enable_flag='Y' " + "and a.stock_status='NORMAL' and a.stock_region_code='KW' " + "and a.stock_count>0 " + "and b.model_type='" + model_type + "'";
                    Integer stockCount = cFuncDbSqlResolve.GetSelectCount(sqlStockCount);
                    if (stockCount > 0) {
                        itemList.add(mapItem);
                        iteratorBigData.close();
                        break;
                    }
                } else {
                    //B.需要进行抛丸,则直接选择已经抛丸的物料
                    String sqlStockCount = "select count(1) " + "from b_dcs_wms_fmod_stock a inner join b_dcs_fmod_model b " + "on a.model_id=b.model_id " + "where a.enable_flag='Y' and b.enable_flag='Y' " + "and a.stock_status='NORMAL' and a.stock_region_code='KW' " + "and a.stock_count>0 " + "and b.model_type='" + model_type + "' " + "and a.stock_id not in " + "(select distinct stock_id from b_dcs_wms_me_stock " + "where alarm_flag='Y' group by stock_id having count(stock_id)>0) ";
                    Integer stockCount = cFuncDbSqlResolve.GetSelectCount(sqlStockCount);
                    if (stockCount > 0) {
                        itemList.add(mapItem);
                        iteratorBigData.close();
                        break;
                    }
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "获取上线任务发生异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.任务状态修改
    @RequestMapping(value = "/DcsCoreApsStatusUpd", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreApsStatusUpd(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/aps/DcsCoreApsStatusUpd";
        String transResult = "";
        String errorMsg = "";
        String apsTaskTable = "b_dcs_aps_task";
        String mo_id = "";
        try {
            //1.获取参数
            mo_id = jsonParas.getString("mo_id");
            String task_status_flag = jsonParas.getString("task_status_flag");
            String task_status = jsonParas.getString("task_status");
            String task_msg_flag = jsonParas.getString("task_msg_flag");
            String task_msg = jsonParas.getString("task_msg");
            if (task_status_flag == null || task_status_flag.equals("")) task_status_flag = "N";
            if (task_msg_flag == null || task_msg_flag.equals("")) task_msg_flag = "N";
            if (mo_id == null) mo_id = "";
            if (task_status == null) task_status = "";
            if (task_msg == null) task_msg = "";

            //2.修改状态
            Boolean isNeedUpd = false;
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            Update updateBigData = new Update();
            if (task_status_flag.equals("Y") && !task_status.equals("")) {
                isNeedUpd = true;
                updateBigData.set("task_status", task_status);
            }
            if (task_msg_flag.equals("Y")) {
                isNeedUpd = true;
                updateBigData.set("task_msg", task_msg);
            }
            if (isNeedUpd) {
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsTaskTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据任务ID{" + mo_id + "}修改任务状态发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.根据RFID任务号查询任务相关信息
    @RequestMapping(value = "/DcsCoreApsTaskInfoSel", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreApsTaskInfoSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/aps/DcsCoreApsTaskInfoSel";
        String selectResult = "";
        String errorMsg = "";
        String apsTaskTable = "b_dcs_aps_task";
        String task_num = "";
        try {
            //1.获取参数
            task_num = jsonParas.getString("task_num");
            if (task_num == null) task_num = "";

            //2.根据任务号查询任务相关信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            List<Map<String, Object>> itemList = new ArrayList<>();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapItem = new HashMap<>();
                mapItem.put("mo_id", docItemBigData.getString("mo_id"));
                mapItem.put("task_num", docItemBigData.getString("task_num"));
                mapItem.put("task_from", docItemBigData.getString("task_from"));
                mapItem.put("serial_num", docItemBigData.getString("serial_num"));
                mapItem.put("lot_num", docItemBigData.getString("lot_num"));
                mapItem.put("task_type", docItemBigData.getString("task_type"));
                mapItem.put("model_type", docItemBigData.getString("model_type"));
                mapItem.put("material_code", docItemBigData.getString("material_code"));
                mapItem.put("material_draw", docItemBigData.getString("material_draw"));
                mapItem.put("m_length", docItemBigData.getDouble("m_length"));
                mapItem.put("m_width", docItemBigData.getDouble("m_width"));
                mapItem.put("m_height", docItemBigData.getDouble("m_height"));
                mapItem.put("m_weight", docItemBigData.getDouble("m_weight"));
                mapItem.put("m_texture", docItemBigData.getString("m_texture"));
                mapItem.put("cut_texture", docItemBigData.getString("cut_texture"));
                mapItem.put("npa_startx", docItemBigData.getDouble("npa_startx"));
                mapItem.put("npa_starty", docItemBigData.getDouble("npa_starty"));
                mapItem.put("cut_way", docItemBigData.getString("cut_way"));
                mapItem.put("cut_speed", docItemBigData.getDouble("cut_speed"));
                mapItem.put("cut_ampere", docItemBigData.getDouble("cut_ampere"));
                mapItem.put("cut_offset", docItemBigData.getDouble("cut_offset"));
                mapItem.put("dxf_name", docItemBigData.getString("dxf_name"));
                mapItem.put("dxf_url", docItemBigData.getString("dxf_url"));
                mapItem.put("json_name", docItemBigData.getString("json_name"));
                mapItem.put("json_url", docItemBigData.getString("json_url"));
                mapItem.put("nc_name", docItemBigData.getString("nc_name"));
                mapItem.put("nc_url", docItemBigData.getString("nc_url"));
                mapItem.put("cut_code", docItemBigData.getString("cut_code"));
                mapItem.put("cut_type", docItemBigData.getString("cut_type"));
                mapItem.put("cut_plan_minutes", docItemBigData.getInteger("cut_plan_minutes"));
                mapItem.put("is_auto_blast", docItemBigData.getString("is_auto_blast"));
                mapItem.put("is_auto_car", docItemBigData.getString("is_auto_car"));
                mapItem.put("is_auto_print", docItemBigData.getString("is_auto_print"));
                mapItem.put("is_auto_cut", docItemBigData.getString("is_auto_cut"));
                mapItem.put("is_auto_sort", docItemBigData.getString("is_auto_sort"));
                mapItem.put("is_center", docItemBigData.getString("is_center"));
                mapItem.put("bg_flag", docItemBigData.getString("bg_flag"));
                mapItem.put("task_status", docItemBigData.getString("task_status"));
                itemList.add(mapItem);
                iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "根据任务号{" + task_num + "}查询任务信息发生异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //4.判断是否存在计划中的任务
    @RequestMapping(value = "/DcsCoreApsTaskPlanExist", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreApsTaskPlanExist(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/aps/DcsCoreApsTaskPlanExist";
        String transResult = "";
        String errorMsg = "";
        String apsTaskTable = "b_dcs_aps_task";
        String isExistPlanTask = "Y";
        try {
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_status").is("PLAN"));
            long taskCount = mongoTemplate.getCollection(apsTaskTable).countDocuments(queryBigData.getQueryObject());
            if (taskCount <= 0) isExistPlanTask = "N";
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, isExistPlanTask, "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在计划中的任务发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
    @RequestMapping(value = "/DcsCoreWhetherApplyForWork", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String DcsCoreWhetherApplyForWork(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/core/aps/DcsCoreWhetherApplyForWork";
        String transResult = "";
        String errorMsg = "";
        String apsTaskTable = "b_dcs_aps_task";
        String isExistPlanTask = "Y";
        try {
            String mo_id = jsonParas.getString("mo_id");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            queryBigData.addCriteria(Criteria.where("bg_flag").is("Y"));
            Document one = mongoTemplate.findOne(queryBigData, Document.class, apsTaskTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, one==null?"N":"Y", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在计划中的任务发生异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


}
