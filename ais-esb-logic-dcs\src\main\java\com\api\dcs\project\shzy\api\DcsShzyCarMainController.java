package com.api.dcs.project.shzy.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.wms.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * WMS天车主调度逻辑
 * 1.取消锁定任务以及锁定调度路线
 * 2.根据条件创建天车调度
 * 3.调度任务类型查询以及锁定天车任务
 * 4.判断任务是否执行完成
 * 5.将调度任务转移到HIS表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-18
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/shzy/wms")
public class DcsShzyCarMainController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsCarCommonFunc dcsCarCommonFunc;
    @Autowired
    private DcsShzyCarCommonFunc dcsShzyCarCommonFunc;
    @Autowired
    private DcsShzyCarMainManualInFunc dcsShzyCarMainManualInFunc;//1.手动天车闯入路线
    @Autowired
    private DcsShzyCarMainStockOutFunc dcsShzyCarMainStockOutFunc;//2.出库上料传输路线
    @Autowired
    private DcsShzyCarMainUnSuitInFunc dcsShzyCarMainUnSuitInFunc;//3.不成套物料转移
    @Autowired
    private DcsShzyCarMainStockDDFunc dcsShzyCarMainStockDDFunc;//4.倒垛路线
    @Autowired
    private DcsShzyCarMainBiRangFunc dcsShzyCarMainBiRangFunc;//5.自动回归远点避让流程

    //1.取消锁定任务以及锁定调度路线
    @RequestMapping(value = "/DcsShzyWmsCarMainCancel", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsShzyWmsCarMainCancel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/shzy/wms/DcsShzyWmsCarMainCancel";
        String transResult="";
        String errorMsg="";
        String wmsCarTaskTable="b_dcs_wms_car_task";
        String userID="-1";
        try{
            //1.获取参数
            String nowDateTime=CFuncUtilsSystem.GetNowDateTime("");
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");
            //2.查询当前锁定调度任务,然后取消对应的任务
            List<String> lstTaskId=new ArrayList<>();
            String sqlLockCarTaskSel="select task_id " +
                    "from b_dcs_wms_lock_car_task " +
                    "where ware_house='"+ware_house+"' " +
                    "order by car_task_id";
            List<Map<String, Object>> itemListLockCarTask=cFuncDbSqlExecute.ExecSelectSql(userID,sqlLockCarTaskSel,
                    false,request,apiRoutePath);
            if(itemListLockCarTask!=null && itemListLockCarTask.size()>0){
                for(Map<String, Object> mapItem : itemListLockCarTask){
                    String task_id=mapItem.get("task_id").toString();
                    lstTaskId.add(task_id);
                }
            }
            if(lstTaskId!=null && lstTaskId.size()>0){
                String[] task_status_list=new String[]{"PLAN","WORK"};
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("task_id").in(lstTaskId));
                queryBigData.addCriteria(Criteria.where("task_status").in(task_status_list));
                Update updateBigData = new Update();
                updateBigData.set("task_status", "CANCEL");
                updateBigData.set("lock_flag", "N");
                updateBigData.set("tell_cancel_date", nowDateTime);
                updateBigData.set("tell_cancel_by", userID);
                mongoTemplate.updateMulti(queryBigData, updateBigData, wmsCarTaskTable);

                //修改当前调度路线任务为CANCEL状态
                String sqlLockCarTaskUpd="update b_dcs_wms_lock_car_task set " +
                        "tell_cancel_date='"+nowDateTime+"'," +
                        "tell_cancel_by='"+userID+"' " +
                        "where ware_house='"+ware_house+"'";
                cFuncDbSqlExecute.ExecUpdateSql(userID,sqlLockCarTaskUpd,false,request,apiRoutePath);
            }
            //3.取消库存锁定状态
            String sqlStockUpdate="update b_dcs_wms_map_me_stock set " +
                    "lock_flag='N' " +
                    "where lock_flag='Y' ";
            cFuncDbSqlExecute.ExecUpdateSql(userID,sqlStockUpdate,false,request,apiRoutePath);
            //4.将调度信息转移到历史
            dcsShzyCarCommonFunc.MoveLockCarRouteToHis(userID,request,apiRoutePath,ware_house);
            //5.删除锁定调度
            dcsCarCommonFunc.DelLockCarRoute(userID,request,apiRoutePath,ware_house);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "取消锁定任务以及锁定调度路线异常:"+ex;
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.根据条件创建天车调度(主天车)
    @Transactional
    @RequestMapping(value = "/DcsShzyWmsCarMainCreate", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsShzyWmsCarMainCreate(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/shzy/wms/DcsShzyWmsCarMainCreate";
        String transResult="";
        String errorMsg="";
        String userID="-1";
        try{
            //1.获取参数
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");
            String car_code=jsonParas.getString("car_code");//天车编码
            String auto_birang_flag=jsonParas.getString("auto_birang_flag");//是否需要自动避让
            String manual_car_in_flag=jsonParas.getString("manual_car_in_flag");//手动天车是否开进来
            Integer car_location_x=jsonParas.getInteger("car_location_x");//当前天车坐标

            //2.将调度全部删除
            dcsCarCommonFunc.DelLockCarRoute(userID,request,apiRoutePath,ware_house);

            //3.按照先后顺序依次判断调度路线
            Boolean routePassFlag=false;
            String routeErrorMsg="";
            List<String> lstRouteError=new ArrayList<>();
            String okCreateRouteFlag="N";

            //3.1 手动天车闯入路线
            if(manual_car_in_flag.equals("Y") && auto_birang_flag.equals("Y")){
                Map<String, Object> mapManualInResult=dcsShzyCarMainManualInFunc.CreateTaskRoute(
                        userID,request,apiRoutePath,ware_house,car_code,car_location_x);
                routePassFlag=Boolean.parseBoolean(mapManualInResult.get("passFlag").toString());
                routeErrorMsg=mapManualInResult.get("errorMsg").toString();
                if(!routePassFlag && !routeErrorMsg.equals("")){
                    errorMsg= routeErrorMsg;
                    transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
                if(!routePassFlag) okCreateRouteFlag="Y";
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,okCreateRouteFlag,"",0);
                return transResult;
            }

            //3.2 依次对路线顺序进行判断
            String[] lstRouteOrder=new String[]{"StockOut","UnSuitIn","BiRang"};//"StockDD"
            for(String routeOrder : lstRouteOrder){
                Map<String, Object> mapResult=null;
                switch (routeOrder){
                    case "StockOut":
                        mapResult=dcsShzyCarMainStockOutFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code);
                        break;
                    case "UnSuitIn":
                        mapResult=dcsShzyCarMainUnSuitInFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code);
                        break;
                    case "StockDD":
                        mapResult=dcsShzyCarMainStockDDFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code);
                        break;
                    case "BiRang":
                        if(auto_birang_flag.equals("Y")){
                            mapResult=dcsShzyCarMainBiRangFunc.CreateTaskRoute(userID,request,apiRoutePath,ware_house,car_code,car_location_x);
                        }
                        break;
                }
                if(mapResult!=null){
                    routePassFlag=Boolean.parseBoolean(mapResult.get("passFlag").toString());
                    routeErrorMsg=mapResult.get("errorMsg").toString();
                    if(!routePassFlag && routeErrorMsg.equals("")){
                        okCreateRouteFlag="Y";
                        transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,okCreateRouteFlag,"",0);
                        return transResult;
                    }
                    if(!routePassFlag && !routeErrorMsg.equals("")){
                        lstRouteError.add(routeErrorMsg);
                    }
                }
            }

            //4.判断调度是否存在异常,抛出第一个异常
            if(lstRouteError.size()>0){
                errorMsg=lstRouteError.get(0);
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,okCreateRouteFlag,"",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "根据条件创建天车调度发生异常:"+ex;
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.将调度任务转移到HIS表
    @RequestMapping(value = "/DcsShzyWmsCarMainMoveHis", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsShzyWmsCarMainMoveHis(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "dcs/project/shzy/wms/DcsShzyWmsCarMainMoveHis";
        String transResult = "";
        String errorMsg = "";
        String userID="-1";
        try{
            //1.获取参数
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");

            //2.将调度信息转移到历史
            dcsShzyCarCommonFunc.MoveLockCarRouteToHis(userID,request,apiRoutePath,ware_house);

            //3.删除锁定调度
            dcsCarCommonFunc.DelLockCarRoute(userID,request,apiRoutePath,ware_house);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "将调度任务转移到HIS表发生异常:"+ex;
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
