package com.api.eap.project.thailand.guanghe;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <p>
 * EAP接口公共方法
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class EapTlGhInterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.创建返回报文
    public String CreateResponseHead01(Integer code, String msg) throws Exception{
        JSONObject response_head=new JSONObject();
        response_head.put("code",code);
        response_head.put("info",msg);
        return response_head.toString();
    }

    //获取返回信息
    public JSONObject GetResponseData(JSONObject jsonResult) throws Exception{
        JSONObject response_data=null;
        String errorMsg="";
        try{
            Integer rtn_code=jsonResult.getInteger("code");
            String rtn_msg=jsonResult.getString("info");
            if(rtn_code!=200){
                errorMsg=rtn_code+"@"+rtn_msg;
                throw new Exception(errorMsg);
            }
            if(jsonResult.containsKey("data")){
                String tempData=jsonResult.getString("data");
                if(tempData!=null && !tempData.equals("")){
                    response_data=jsonResult.getJSONObject("data");
                }
            }
        }
        catch (Exception ex){
            throw ex;
        }
        return response_data;
    }

    //请求EAP
    public String PostJbBackJb(String funcName,String url,JSONObject jsonParas) throws Exception{
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("messagename",funcName);
        headers.add("transactionid",CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmss"));
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        HttpEntity<String> formEntity = new HttpEntity<String>(jsonParas.toString(), headers);
        String jsonResult = restTemplate.postForObject(url , formEntity, String.class);
        return jsonResult;
    }
}
