package com.api.dcs.project.fjrm.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 码头调度逻辑
 * 1.根据任务锁定码头
 * 2.判断任务对应码头-RGV是否到位
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/fjrm/wharf")
public class DcsFjrmWharfController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //1.根据任务锁定码头
    @Transactional
    @RequestMapping(value = "/DcsFjrmTaskLockWharf", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsFjrmTaskLockWharf(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/fjrm/wharf/DcsFjrmTaskLockWharf";
        String transResult="";
        String errorMsg="";
        String userID="-1";
        try{
            //获取参数
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");//库区域
            String task_num=jsonParas.getString("task_num");//任务号
            String wharf_code=jsonParas.getString("wharf_code");//码头编码
            //修改
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            String sqlWharfUpd="update b_dcs_fmod_wharf set " +
                    "last_updated_by='"+userID+"'," +
                    "last_update_date='"+nowDateTime+"'," +
                    "task_num='"+task_num+"'," +
                    "lock_flag='Y' " +
                    "where enable_flag='Y' " +
                    "and wharf_code='"+wharf_code +"' "+
                    "and ware_house='"+ware_house+"' ";
            cFuncDbSqlExecute.ExecUpdateSql(userID,sqlWharfUpd,false,null,apiRoutePath);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null, "","",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "根据任务锁定码头发生异常:"+ex;
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.判断任务对应码头-RGV是否到位
    @Transactional
    @RequestMapping(value = "/DcsFjrmTaskJudgeWharf", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsFjrmTaskJudgeWharf(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/fjrm/wharf/DcsFjrmTaskJudgeWharf";
        String transResult="";
        String errorMsg="";
        String userID="-1";
        try{
            //获取参数
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");//库区域
            String wharf_code=jsonParas.getString("wharf_code");//码头编码
            //查询码头
            String sqlWharfSel="select a.wharf_id," +
                    "COALESCE(a.wharf_code,'') wharf_code," +
                    "COALESCE(a.wharf_des,'') wharf_des," +
                    "COALESCE(a.location_x,0) location_x," +
                    "COALESCE(a.location_y,0) location_y," +
                    "COALESCE(a.location_z,0) location_z," +
                    "COALESCE(b.rgv_code,'') rgv_code, " +
                    "COALESCE(b.ready_tag,'') ready_tag, " +
                    "COALESCE(b.location_tag,'') location_tag " +
                    "from b_dcs_fmod_wharf a left join b_dcs_fmod_rgv b " +
                    "on a.rgv_code=b.rgv_code " +
                    "where a.enable_flag='Y' "+
                    "and ware_house='"+ware_house+"' " +
                    "and wharf_code='"+wharf_code+"' ";
            List<Map<String, Object>> wharfList=cFuncDbSqlExecute.ExecSelectSql(userID,sqlWharfSel,
                    false,request,apiRoutePath);
            String ready_tag_value="";//读取点位的值
            if(wharfList!=null && wharfList.size()>0){
                for(Map<String, Object> mapItem : wharfList){
                    String reach_tag=mapItem.get("reach_tag").toString();//读取点位点位:RGV到达上料位置
                    //读取到SCADA
                    if (!reach_tag.equals("")) {
                        JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation("P01",reach_tag);
                        if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                            JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                            ready_tag_value = jbItem.getString("tag_value");
                        }
                    }
                }
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null, ready_tag_value,"",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "判断任务对应码头-RGV是否到位发生异常:"+ex;
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.解锁并通知RGV
    @Transactional
    @RequestMapping(value = "/DcsFjrmTaskWharfUnLock", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsFjrmTaskWharfUnLock(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/fjrm/wharf/DcsFjrmTaskWharfUnLock";
        String transResult="";
        String errorMsg="";
        String userID="-1";
        try{
            //获取参数
            userID=jsonParas.getString("userID");
            String ware_house=jsonParas.getString("ware_house");//库区域
            String wharf_code=jsonParas.getString("wharf_code");//码头编码
            //1.查询码头
            String sqlWharfSel="select a.wharf_id," +
                    "COALESCE(a.wharf_code,'') wharf_code," +
                    "COALESCE(a.wharf_des,'') wharf_des," +
                    "COALESCE(a.location_x,0) location_x," +
                    "COALESCE(a.location_y,0) location_y," +
                    "COALESCE(a.location_z,0) location_z," +
                    "COALESCE(b.rgv_code,'') rgv_code, " +
                    "COALESCE(b.leave_tag,'') leave_tag " +
                    "from b_dcs_fmod_wharf a left join b_dcs_fmod_rgv b " +
                    "on a.rgv_code=b.rgv_code " +
                    "where a.enable_flag='Y' "+
                    "and ware_house='"+ware_house+"' " +
                    "and wharf_code='"+wharf_code+"' ";
            List<Map<String, Object>> wharfList=cFuncDbSqlExecute.ExecSelectSql(userID,sqlWharfSel,
                    false,request,apiRoutePath);
            String unLockFlag="N";//解锁标记
            if(wharfList!=null && wharfList.size()>0){
                for(Map<String, Object> mapItem : wharfList){
                    String leave_tag=mapItem.get("location_tag").toString();//写入点位:通知RGV离开取料位置
                    //2.入到SCADA
                    String tagOnlyKeyList = leave_tag;
                    String tagValueList = "1";
                    errorMsg = cFuncUtilsCellScada.WriteTagByStation(userID, "P01", tagOnlyKeyList, tagValueList, true);
                    if (!errorMsg.equals("")) {
                        unLockFlag="Y";
                    } else {
                        //写入失败
                        unLockFlag="N";
                    }
                }
            }
            //3.解锁码头
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            String sqlWharfUpd="update b_dcs_fmod_wharf set " +
                    "last_updated_by='"+userID+"'," +
                    "last_update_date='"+nowDateTime+"'," +
                    "task_num=''," +
                    "lock_flag='N' " +
                    "where wharf_code='"+wharf_code +"' "+
                    "and ware_house='"+ware_house+"' ";
            cFuncDbSqlExecute.ExecUpdateSql(userID,sqlWharfUpd,false,null,apiRoutePath);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null, unLockFlag,errorMsg,0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "解锁并通知RGV发生异常:"+ex;
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
