package com.api.mes.core.recipe;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 1.拧紧逻辑
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-25
 */
@RestController
@Slf4j
@RequestMapping("/mes/core/recipe")
public class MesCoreRecipeShaftController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //查询当前拧紧信息
    @Transactional
    @RequestMapping(value = "/MesCoreRecipeShaftSelect", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeShaftSelect(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeShaftSelect";
        String selectResult="";
        String errorMsg="";
        String station_code="";
        try{
            station_code = jsonParas.getString("station_code");//工位号
            String proceduce_id= jsonParas.getString("proceduce_id");//工序ID
            String sqlQuality="select group_order,COALESCE(quality_status,'WAIT') quality_status," +
                    "quality_id,COALESCE(tag_id,0) tag_id," +
                    "COALESCE(tag_quality_sign_id,0) tag_quality_sign_id," +
                    "COALESCE(bolt_code,0) bolt_code,COALESCE(progm_num,0) progm_num " +
                    "from c_mes_me_cr_pdure_quality where station_code='"+station_code+"' " +
                    "and proceduce_id="+proceduce_id+" and quality_from='SCADA' " +
                    "and (quality_status='WAIT' or quality_status='PLAN' or quality_status='NG') " +
                    "order by group_order,tag_col_order,tag_col_inner_order";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlQuality,
                    false,request,apiRoutePath);
            if(itemList==null || itemList.size()<=0){
                selectResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
                return selectResult;
            }
            String group_order=itemList.get(0).get("group_order").toString();
            String quality_status=itemList.get(0).get("quality_status").toString();
            if(quality_status.equals("WAIT")){
                String sqlUpdate="update c_mes_me_cr_pdure_quality set " +
                        "quality_status='PLAN' " +
                        "where station_code='"+station_code+"' and proceduce_id="+proceduce_id+" " +
                        "and group_order="+group_order+"";
                cFuncDbSqlExecute.ExecSelectSql(station_code, sqlUpdate, false,request,apiRoutePath);
            }
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "查询当前工位{"+station_code+"}工序对应的拧紧组异常:"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
    //查询当前拧紧信息
    @Transactional
    @RequestMapping(value = "/MesCoreRecipeShaftSelect02", method = {RequestMethod.POST,RequestMethod.GET})
    public String MesCoreRecipeShaftSelect02(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="mes/core/recipe/MesCoreRecipeShaftSelect02";
        String selectResult="";
        String errorMsg="";
        String station_code="";
        try{
            station_code = jsonParas.getString("station_code");//工位号
            String proceduce_id= jsonParas.getString("proceduce_id");//工序ID
            String sqlQuality="select group_order,tag_col_order,COALESCE(bolt_code,0) bolt_code,COALESCE(progm_num,0) progm_num " +
                    "from c_mes_me_cr_pdure_quality where station_code='"+station_code+"' " +
                    "and proceduce_id="+proceduce_id+" and quality_from='SCADA' " +
                    "group by group_order,tag_col_order,bolt_code,progm_num " +
                    "order by group_order,tag_col_order";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlQuality,
                    false,request,apiRoutePath);
            if(itemList==null || itemList.size()<=0){
                selectResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
                return selectResult;
            }
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "查询当前工位{"+station_code+"}工序对应的拧紧组异常:"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
