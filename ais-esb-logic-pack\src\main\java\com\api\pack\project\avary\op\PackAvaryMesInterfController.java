package com.api.pack.project.avary.op;

import com.alibaba.fastjson.JSONObject;
import com.api.base.Const;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pack.core.ccd.CCDMappingResultMessageContentItemDetail;
import com.api.pack.core.ccd.CCDMappingResultMessageContentItemDetailService;
import com.api.pack.core.plan.PlanService;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 鹏鼎包装机MES接口
 * 1.MES下发设备批次信息
 * 2.向MES请求内标码
 * 3.通知MES打印内包码
 * 4.开始生产获取mapping结果
 * 5.处理校验结果 added by jay-y 2024/08/26
 * </p>
 *
 * <AUTHOR>
 * @since 2024-5-8
 */
@RestController
@Slf4j
@RequestMapping("/pack/project/avary/op/mes/interf")
public class PackAvaryMesInterfController
{
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PackAvaryMesInterfFunc packAvaryMesInterfFunc;
    @Autowired
    private PackAvaryInterfCommon packAvaryInterfCommon;

    @Autowired
    private CCDMappingResultMessageContentItemDetailService ccdMesMessageMappingResultService;
    @Autowired
    private PlanService planService;

    //1.MES下发设备批次信息
    @RequestMapping(value = "/MesSetPackingEqpInfoDownLoad", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesSetPackingEqpInfoDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "/pack/project/avary/op/mes/interf/MesSetPackingEqpInfoDownLoad";
        String transResult = "";
        String errorMsg = "";
        try
        {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = packAvaryMesInterfFunc.MesSetPackingEqpInfoDownLoad(apiRoutePath, jsonParas);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag)
            {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
            }
            if (!successFlag)
            {
                transResult = PackAvaryInterfCommon.GetErrorJson(message);
                return transResult;
            }
            String writeResult = cFuncUtilsCellScada.WriteTagByStation("aisEsbApi", "BzSort", "SortAis/Mes/SendMsg", jsonParas.toString(), false);
            if (!writeResult.equals(""))
            {
                return PackAvaryInterfCommon.GetErrorJson(writeResult);
            }
            transResult = responseParas;
        }
        catch (Exception ex)
        {
            errorMsg = "MesSetPackingEqpInfoDownLoad error:" + ex.getMessage();
            transResult = PackAvaryInterfCommon.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.向MES请求内标码
    @RequestMapping(value = "/MesBagInternalLabelReq", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesBagInternalLabelReq(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "/pack/project/avary/op/mes/interf/MesBagInternalLabelReq";
        String transResult = "";
        String errorMsg = "";
        String result = "";
        try
        {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            String station_code = jsonParas.getString("station_code");
            String gross_weight = jsonParas.getString("gross_weight");
            String sort_station_code = jsonParas.getString("sort_station_code");
            String xout_act_num = jsonParas.getString("xout_act_num");
            String plc_stack_qty = jsonParas.getString("plc_stack_qty");
            JSONObject jbResult = packAvaryMesInterfFunc.MesBagInternalLabelReq(station_code, gross_weight, sort_station_code, xout_act_num, plc_stack_qty);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag)
            {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
            }
            if (ObjectUtils.isEmpty(responseParas))
            {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, responseParas, "", 0);
        }
        catch (Exception ex)
        {
            errorMsg = "MesBagInternalLabelReq error:" + ex.getMessage();
            log.error(errorMsg, ex);
            transResult = PackAvaryInterfCommon.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.通知MES打印内包码
    @RequestMapping(value = "/MesBagInternalLabelPrint", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesBagInternalLabelPrint(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "/pack/project/avary/op/mes/interf/MesBagInternalLabelPrint";
        String transResult = "";
        String errorMsg = "";
        String result = "";
        String packMePileTable = "a_pack_me_pile";
        try
        {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            String station_code = jsonParas.getString("station_code");
            String barCode = jsonParas.getString("barCode");

            JSONObject jbResult = packAvaryMesInterfFunc.MesBagInternalLabelPrint(station_code, barCode);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String esbInterfReturnCode = jbResult.getString("esbInterfReturnCode");
            String esbInterfReturnMsg = jbResult.getString("esbInterfReturnMsg");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            // 增加打印接口的返回码和返回信息 added by jay-y 2024/08/24
            Query pileQuery = new Query();
            pileQuery.addCriteria(Criteria.where("pile_barcode").is(barCode));
            pileQuery.addCriteria(Criteria.where("enable_flag").is("Y"));
            Update pileUpdate = new Update();
            pileUpdate.set("print_return_code", esbInterfReturnCode);
            pileUpdate.set("print_return_msg", esbInterfReturnMsg);
            if (!successFlag)
            {
                pileUpdate.set("union_status", Const.NG);
            }
            mongoTemplate.updateFirst(pileQuery, pileUpdate, packMePileTable);
            //记录日志
            if (isSaveFlag)
            {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, responseParas, "", 0);
        }
        catch (Exception ex)
        {
            errorMsg = "MesBagInternalLabelPrint error:" + ex.getMessage();
            transResult = PackAvaryInterfCommon.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.开始生产获取mapping结果
    @Transactional(rollbackFor = Exception.class)
    @RequestMapping(value = "/GetStripInspectData", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetStripInspectData(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "/pack/project/avary/op/mes/interf/GetStripInspectData";
        String transResult = "";
        String errorMsg = "";
        String result = "";
        try
        {
            //查询系统参数Pack_Mapping_ApiOrFtp，判断获取mapping的方式是webApi还是FTP
            String ApiORFtp = cFuncDbSqlResolve.GetParameterValue("Pack_Mapping_ApiOrFtp");//判断获取mapping结果时是通过webApi还是FTP   1:webApi  2:FTP  0:不启用mapping下载
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            String lot_no = jsonParas.getString("lot_no");  //批号
            if (lot_no == null || lot_no.equals(""))
            {
                return CFuncUtilsLayUiResut.GetErrorJson("批号不能为空");
            }
            String product_no = jsonParas.getString("product_no");  //料号
            String process_no = jsonParas.getString("process_no");  //前站工序 默认AVI
            // 下发新任务的时候，需要把之前的所有mapping结果置为无效
            Update update = new Update();
            update.set("enable_flag", "N");
            mongoTemplate.updateMulti(new Query(), update, "a_pack_strip_inspect_data");
            if (!StringUtils.isEmpty(ApiORFtp) && ApiORFtp.equals("1"))
            {
                // 批号截取9位 —— 鹏鼎包装机项目 added by jay-y 2024-07-13
                String apiLotNo = lot_no.length() > 9 ? lot_no.substring(0, 9) : lot_no;
                //走webApi下载mapping结果
                JSONObject jbResult = packAvaryMesInterfFunc.ResolveStripInspectDataToDB(apiLotNo, product_no, process_no);
                Boolean prodFlag = true;
                Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
                String esbInterfCode = jbResult.getString("esbInterfCode");
                String token = jbResult.getString("token");
                String requestParas = jbResult.getString("requestParas");
                String responseParas = jbResult.getString("responseParas");
                Boolean successFlag = jbResult.getBoolean("successFlag");
                String message = jbResult.getString("message");
                String endDate = CFuncUtilsSystem.GetNowDateTime("");
                //记录日志
                if (isSaveFlag)
                {
                    cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, null);
                }
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, responseParas, "", 0);
            }
            else if (!StringUtils.isEmpty(ApiORFtp) && ApiORFtp.equals("2"))
            {   //走FTP下载mapping结果
                String ftpPath = cFuncDbSqlResolve.GetParameterValue("Pack_Mapping_FTP_Path");  // FTP服务器路径

                String ftpIP = cFuncDbSqlResolve.GetParameterValue("Pack_Mapping_FTP_Path_IP");  // FTP服务器IP

                String ftpPathPort = cFuncDbSqlResolve.GetParameterValue("Pack_Mapping_FTP_Path_Port");
                int port = Integer.parseInt(ftpPathPort);  // FTP服务器端口

                String ftpLogin = cFuncDbSqlResolve.GetParameterValue("Pack_Mapping_FTP_Login");  // FTP登录账号

                String ftpLoginPassword = cFuncDbSqlResolve.GetParameterValue("Pack_Mapping_FTP_Password");  // FTP登录密码

                String ftpMode = cFuncDbSqlResolve.GetParameterValue("Pack_Mapping_FTP_Path_Mode");  // FTP主被动模式    1主动模式   0被动模式

                String dataFrom = "FTP";
                FTPClient ftpClient = new FTPClient();
                try
                {
                    ftpClient.connect(ftpIP, port);
                    ftpClient.login(ftpLogin, ftpLoginPassword);
                    if (ftpMode.equals("1"))
                    {
                        ftpClient.enterLocalActiveMode();   //主动模式
                    }
                    else
                    {
                        ftpClient.enterLocalPassiveMode();   // 如果服务器在防火墙后面，需要设置被动模式
                    }
                    // 批号截取9位，料号截取8位 —— 鹏鼎包装机项目 added by jay-y 2024-07-13
                    String ftpLotNo = lot_no.length() > 9 ? lot_no.substring(0, 9) : lot_no;
                    String ftpProductNo = product_no.length() > 8 ? product_no.substring(0, 8) : product_no;
                    boolean res = ftpClient.changeWorkingDirectory(ftpPath + "/" + ftpProductNo + "/" + ftpLotNo);
                    if (!res)
                    {
                        throw new Exception("FTP上找不到当前计划对应的mapping文件");
                    }
                    // 列出文件
                    Map<String, CCDMappingResultMessageContentItemDetail> mappingResultsMap = new HashMap<>();
                    String[] files = ftpClient.listNames();
                    for (String file : files)
                    {
                        CCDMappingResultMessageContentItemDetail newMappingResult = new CCDMappingResultMessageContentItemDetail(lot_no, product_no, process_no, file, dataFrom);
                        CCDMappingResultMessageContentItemDetail oldMappingResult = mappingResultsMap.get(newMappingResult.getBarcode());
                        if (oldMappingResult == null || (oldMappingResult.getUploadDateVal() < newMappingResult.getUploadDateVal()))
                        {
                            mappingResultsMap.put(newMappingResult.getBarcode(), newMappingResult);
                        }
                    }
                    this.ccdMesMessageMappingResultService.saveAll(mappingResultsMap.values());
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "0", "", 0);
                }
                catch (IOException ex)
                {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    errorMsg = "GetStripInspectData error:" + ex.getMessage();
                    log.error(errorMsg, ex);
                    transResult = PackAvaryInterfCommon.GetErrorJson(errorMsg);
                }
                finally
                {
                    try
                    {
                        if (ftpClient.isConnected())
                        {
                            ftpClient.logout();
                            ftpClient.disconnect();
                        }
                    }
                    catch (IOException ex)
                    {
                        log.error("FTP关闭异常", ex);
                    }
                }
            }
            else if (!StringUtils.isEmpty(ApiORFtp) && ApiORFtp.equals("0"))
            {
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "2", "", 0);
            }
            // 下发新任务的时候，需要把之前的所有a_pack_strip_inspect_data中的记录转移到a_pack_get_strip_inspect_data中 modified by jay-y 2024/09/19
            int batchSize = 100;
            Query query = new Query();
            try (MongoCursor<Document> cursor = mongoTemplate.getCollection("a_pack_strip_inspect_data"). // get collection
                    find(query.getQueryObject()). // find by query
                    sort(query.getSortObject()). // sort by query
                    batchSize(batchSize). // set batch size
                    noCursorTimeout(true). // no cursor timeout
                    iterator() // get iterator
            )
            {
                while (cursor.hasNext())
                {
                    Document document = cursor.next();
                    mongoTemplate.save(document, "a_pack_get_strip_inspect_data");
                }
            }
            // 删除a_pack_strip_inspect_data中enable_flag为N的记录
            mongoTemplate.remove(new Query(Criteria.where("enable_flag").is("N")), "a_pack_strip_inspect_data");
        }
        catch (Exception ex)
        {
            errorMsg = "GetStripInspectData error:" + ex.getMessage();
            log.error(errorMsg, ex);
            transResult = PackAvaryInterfCommon.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.处理校验结果 added by jay-y 2024/08/27
    @RequestMapping(value = "/HandleInspectResult", method = RequestMethod.POST)
    public String HandleInspectResult(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "/pack/project/avary/op/mes/interf/HandleInspectResult";
        String transResult = "";
        String packApsPlanTable = "a_pack_aps_plan";
        String packMePileTable = "a_pack_me_pile";
        String errorMsg = "";
        try
        {
            String pileBarcode = jsonParas.getString("pileBarcode");
            String inspectResultCode = jsonParas.getString("inspectResultCode");
            String inspectResultMsg = jsonParas.getString("inspectResultMsg");
            Query pileQuery = new Query();
            pileQuery.addCriteria(Criteria.where("pile_barcode").is(pileBarcode));
            pileQuery.addCriteria(Criteria.where("pile_status").is(Const.OK));
            pileQuery.addCriteria(Criteria.where("union_status").is(Const.ON));
            pileQuery.addCriteria(Criteria.where("enable_flag").is("Y"));
            Update pileUpdate = new Update();
            pileUpdate.set("inspect_result_code", inspectResultCode);
            pileUpdate.set("inspect_result_msg", inspectResultMsg);
            pileUpdate.set("union_status", inspectResultCode);
            mongoTemplate.updateFirst(pileQuery, pileUpdate, packMePileTable);
            // 获取当前工作中的订单/计划
            Document plan = planService.getCurrent();
            if (plan != null)
            {
                String lotNum = plan.getString("lot_num");
                // 获取所有ok与ng的pile信息
                Query pliesOfPlanQuery = new Query();
                pliesOfPlanQuery.addCriteria(Criteria.where("lot_num").is(lotNum));
                pliesOfPlanQuery.addCriteria(Criteria.where("enable_flag").is("Y"));
                List<Document> piles = mongoTemplate.find(pliesOfPlanQuery, Document.class, packMePileTable);
                if (!CollectionUtils.isEmpty(piles))
                {
                    Update planUpdate = new Update();
                    //完成打包次数
                    long count = 0L;
                    //完成打包数量
                    long sum = 0L;
                    for (Document pile : piles)
                    {
                        if (pile.containsKey("union_status") && Const.OK.equals(pile.get("union_status")))
                        {
                            count++;
                            if (pile.containsKey("array_count"))
                            {
                                sum += Long.parseLong(pile.get("array_count").toString());
                            }
                        }
                    }
                    // 修改任务信息中的 finish_pile_count 完成打包数量 finish_label_count 打包次数
                    planUpdate.set("finish_pile_count", sum);
                    planUpdate.set("finish_lable_count", count); // 兼容之前的字段名
                    planUpdate.set("finish_label_count", count);
                    Query planQuery = new Query();
                    planQuery.addCriteria(Criteria.where("lot_num").is(lotNum));
                    planQuery.addCriteria(Criteria.where("lot_status").is("WORK"));
                    planQuery.addCriteria(Criteria.where("enable_flag").is("Y"));
                    mongoTemplate.updateFirst(planQuery, planUpdate, packApsPlanTable);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        }
        catch (Exception ex)
        {
            errorMsg = "ReceiveInspectResult error:" + ex.getMessage();
            log.error(errorMsg, ex);
            transResult = PackAvaryInterfCommon.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
