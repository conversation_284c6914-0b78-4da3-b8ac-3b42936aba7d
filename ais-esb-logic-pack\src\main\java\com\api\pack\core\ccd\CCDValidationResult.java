package com.api.pack.core.ccd;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.IMongoBasic;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@ApiModel(value = "CCDValidationResult", description = "CCD Validation Result")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Document("a_pack_ccd_v_res")
public class CCDValidationResult extends IMongoBasic
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点编码")
    @JSONField(name = "station_code")
    @JsonProperty("station_code")
    @Field("station_code")
    private String stationCode;

    @ApiModelProperty(value = "站点描述")
    @JSONField(name = "station_desc")
    @JsonProperty("station_desc")
    @Field("station_desc")
    private String stationDesc;

    @ApiModelProperty(value = "内部标签追溯码")
    @JsonProperty("serial_num")
    @JSONField(name = "serial_num")
    @Field("serial_num")
    private String serialNum;

    @ApiModelProperty(value = "流程信息")
    @JSONField(name = "flow_info")
    @JsonProperty("flow_info")
    @Field("flow_info")
    private String flowInfo;

    @ApiModelProperty(value = "任务信息")
    @JSONField(name = "task_info")
    @JsonProperty("task_info")
    @Field("task_info")
    private String taskInfo;

    @ApiModelProperty(value = "CCD数据")
    @JSONField(name = "ccd_data")
    @JsonProperty("ccd_data")
    @Field("ccd_data")
    private String ccdData;

    @ApiModelProperty(value = "校验时间")
    @JSONField(name = "validate_time")
    @JsonProperty("validate_time")
    @Field("validate_time")
    private Long validateTime;

    @ApiModelProperty(value = "校验结果")
    @JSONField(name = "validate_result")
    @JsonProperty("validate_result")
    @Field("validate_result")
    private String validateResult;

    @ApiModelProperty(value = "校验消息")
    @JSONField(name = "validate_msg")
    @JsonProperty("validate_msg")
    @Field("validate_msg")
    private String validateMsg;

    public static CCDValidationResult of(JSONObject flowInfo, JSONObject taskInfo, JSONObject ccdData, String validateResult, String validateMsg)
    {
        String stationCode = flowInfo.getString("code");
        String stationDesc = flowInfo.getString("desc");
        String serialNum = taskInfo.getString("barcode");
        if (serialNum == null || serialNum.isEmpty())
        {
            serialNum = taskInfo.getString("serial_num");
        }
        return new CCDValidationResult(stationCode, stationDesc, serialNum, flowInfo.toJSONString(), taskInfo.toJSONString(),
                ccdData != null ? ccdData.toJSONString()
                                : null, System.currentTimeMillis(), validateResult, validateMsg);
    }
}
