package com.api.dcs.project.xcmg;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.dcs.core.wms.DcsStockCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 徐工定制化抛丸处理逻辑
 * 1.查询与锁定抛丸任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-12-15
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/xcmg/pw")
public class EapXcmgPwController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsStockCommonFunc dcsStockCommonFunc;

    //上料工位任务查询
    @RequestMapping(value = "/DcsXcmgPwTaskSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsXcmgPwTaskSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/xcmg/pw/DcsXcmgPwTaskSel";
        String selectResult="";
        String errorMsg="";
        String wmsCarTaskTable="b_dcs_wms_car_task";
        String task_type="KW_PW";
        List<Map<String, Object>> itemList=null;
        try{
            //1.从抛丸任务中查询WAIT任务
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_type").is(task_type));
            queryBigData.addCriteria(Criteria.where("task_status").is("WAIT"));
            queryBigData.addCriteria(Criteria.where("lock_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(wmsCarTaskTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            String task_id="";
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                Map<String, Object> mapItem=new HashMap<>();
                task_id=docItemBigData.getString("task_id");
                String task_num=docItemBigData.getString("task_num");
                String model_type=docItemBigData.getString("model_type");
                String from_stock_code=docItemBigData.getString("from_stock_code");
                //根据型号从信息查询
                Map<String, Object> mapModel= dcsStockCommonFunc.GetModelInfo("",model_type);
                if(mapModel==null){
                    iteratorBigData.close();
                    errorMsg="任务号为{"+task_num+"}的抛丸出库任务,未能根据机型{"+model_type+"}查找到机型基础信息";
                    selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return selectResult;
                }
                itemList=new ArrayList<>();
                String material_code=mapModel.get("material_code").toString();
                String m_length=mapModel.get("m_length").toString();
                String m_width=mapModel.get("m_width").toString();
                String m_height=mapModel.get("m_height").toString();
                String m_weight=mapModel.get("m_weight").toString();
                String m_texture=mapModel.get("m_texture").toString();
                //
                mapItem.put("task_num",task_num);
                mapItem.put("model_type",model_type);
                mapItem.put("from_stock_code",from_stock_code);
                mapItem.put("material_code",material_code);
                mapItem.put("m_length",m_length);
                mapItem.put("m_width",m_width);
                mapItem.put("m_height",m_height);
                mapItem.put("m_weight",m_weight);
                mapItem.put("m_texture",m_texture);
                itemList.add(mapItem);
                iteratorBigData.close();
            }
            //2.修改为PLAN状态
            if(!task_id.equals("")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("task_id").is(task_id));
                Update updateBigData = new Update();
                updateBigData.set("task_status", "PLAN");
                mongoTemplate.updateFirst(queryBigData, updateBigData, wmsCarTaskTable);
            }
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
