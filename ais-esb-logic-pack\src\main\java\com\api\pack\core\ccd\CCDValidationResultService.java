package com.api.pack.core.ccd;

import com.api.base.IMongoBasicService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class CCDValidationResultService extends IMongoBasicService<CCDValidationResult, CCDValidationResultRepository>
{
    public CCDValidationResultService(CCDValidationResultRepository repository)
    {
        super(repository);
    }
}
