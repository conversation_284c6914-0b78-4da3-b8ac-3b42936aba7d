package com.api.eap.project.mflex;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 维信接口处理函数
 * 1.扫描上报MES获取JDE料号
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapMflexInterfFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //PLASMA获取料号
    public JSONObject GetJDEPruoduct(String station_code, String panel, JSONObject information) {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "GetJDEPruoduct";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("PanelId", panel);
            String param = "MessageId=" + CFuncUtilsSystem.CreateUUID(true).toUpperCase() + "&MessageCode=20001&ResourceName=" + station_code + "&MessageContent=" + jsonObjectReq.toString();

            requestParas = jsonObjectReq.toString();
            String resultMes = cFuncUtilsRest.GetUrlBackString(esb_prod_intef_url + "?" + param, information);
            JSONObject jsonObjectMes = JSONObject.parseObject(resultMes);

            responseParas = jsonObjectMes.toString();
            boolean isSuccess = jsonObjectMes.getBoolean("isSuccess");
            String backMsg = jsonObjectMes.getString("messageContent");
            if (!isSuccess) {
                errorMsg = "Plasma获取料号接口返回失败：" + backMsg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //上传成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "上传成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    /*上报根据messageCode区分
        “1”：上报MES化学清洗电导率
        “10002”：Plasma提交Panel信息
        "30001":生产参数上传
        "40001":机台状态上传
        "60001":机台状态上传
        "160001":设备状态上传
        "50001":设备参数上传
        "150001":设备参数上传
        "70001":环境参数上传
        "170001":环境参数上传
        "80001":员工登录
        "90001":设备自检
        "100001":工具验证
        "110001":物料验证
        "120001":产品批次验证
        "130001":生产扫码上传验证
        "140001":检测结果上传
     */
    public JSONObject UploadData(String station_code, String esbInterfCode, String messageCode, String messageContent, String info) {
        JSONObject jbResult = null;
        String errorMsg = "";
        boolean successFlag = true;
        String token = "";
        String esb_interf_des = "";
        String requestParas = "";
        String responseParas = "";
        try {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url," + "COALESCE(esb_interf_des,'') esb_interf_des " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            esb_interf_des = itemList.get(0).get("esb_interf_des").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //2.调用接口
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("MessageId", CFuncUtilsSystem.CreateUUID(true).toUpperCase());
            jsonObjectReq.put("MessageCode", messageCode);//消息Code
            jsonObjectReq.put("ResourceName", station_code);//MES系统中的设备编号
            jsonObjectReq.put("MessageContent", messageContent);//消息内容
            if (info != null) {
                jsonObjectReq.put("Info", info);//消息内容
            }
            requestParas = jsonObjectReq.toString();
            JSONObject jsonObjectMes = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, jsonObjectReq);
            responseParas = jsonObjectMes.toString();
            boolean isSuccess = jsonObjectMes.getBoolean("isSuccess");
            String backMsg = jsonObjectMes.getString("messageContent");
            if (!isSuccess) {
                errorMsg = esb_interf_des + "接口返回失败：" + backMsg;
                successFlag = false;
            } else {
                errorMsg = "上传成功";
                successFlag = true;
            }
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            successFlag = false;
        }
        jbResult = new JSONObject();
        jbResult.put("isSaveFlag", true);
        jbResult.put("esbInterfCode", esbInterfCode);
        jbResult.put("token", token);
        jbResult.put("requestParas", requestParas);
        jbResult.put("responseParas", responseParas);
        jbResult.put("successFlag", successFlag);
        jbResult.put("message", errorMsg);
        return jbResult;
    }

    //申请上传配方
    public JSONObject ApplyUploadRecipe(String station_code, String recipe_name, String user_name) {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "ApplyUploadRecipe";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //2.调用接口
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("requestId", CFuncUtilsSystem.CreateUUID(true).toUpperCase());
            jsonObjectReq.put("eqpCode", station_code);//设备编码
            jsonObjectReq.put("recipeName", recipe_name);//配方名
            jsonObjectReq.put("userNo", user_name);//上传人

            requestParas = jsonObjectReq.toString();
            JSONObject jsonObjectMes = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, jsonObjectReq);
            responseParas = jsonObjectMes.toString();
            String backMsg = jsonObjectMes.getString("msg");
            if (jsonObjectMes.getBoolean("success") == false) {
                errorMsg = "申请上传配方接口返回失败：" + backMsg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //上传成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", errorMsg);
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    public JSONObject ApplyDownloadRecipe(String station_code, String recipe_name, String user_name) {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "ApplyDownloadRecipe";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //2.调用接口
            JSONObject jsonObjectReq = new JSONObject();
            jsonObjectReq.put("requestId", CFuncUtilsSystem.CreateUUID(true).toUpperCase());
            jsonObjectReq.put("eqpCode", station_code);//设备编码
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
            if (projectCode.equals("TLWX")) {
	                jsonObjectReq.put("recipeName", "");
	                //批次号
	                jsonObjectReq.put("lot", recipe_name);	
            } else {
                jsonObjectReq.put("recipeName", "");//配方名
                jsonObjectReq.put("lot", recipe_name);//批次号
            }
            jsonObjectReq.put("userNo", user_name);//上传人
            requestParas = jsonObjectReq.toString();
            JSONObject jsonObjectMes = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, jsonObjectReq);
            responseParas = jsonObjectMes.toString();
            String backMsg = jsonObjectMes.getString("msg");
            if (jsonObjectMes.getBoolean("success") == false) {
                errorMsg = "申请下载配方接口返回失败：" + backMsg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //上传成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", errorMsg);
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    public JSONObject RecipeUploadResultNotice(String session, JSONArray uploadResults) {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "RecipeUploadResultNotice";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("session", session);
            jsonObject.put("recipeAttach", uploadResults);
            jsonObject.put("recipeParam", new JSONArray());
            jsonObject.put("requestId", UUID.randomUUID().toString().replace("-", ""));
            requestParas = jsonObject.toString();
            //2.调用接口
            JSONObject jsonObjectMes = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, jsonObject);
            responseParas = jsonObjectMes.toString();
            String backMsg = jsonObjectMes.getString("msg");
            if (jsonObjectMes.getBoolean("success") == false) {
                errorMsg = "上传结果通知接口返回失败：" + backMsg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //上传成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", errorMsg);
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    public JSONObject RecipeDownloadResultNotice(String session, JSONArray downLoadResults) {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "RecipeDownloadResultNotice";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //下载多个，批量通知
            for (int i = 0; i < downLoadResults.size(); i++) {
                JSONObject result = downLoadResults.getJSONObject(i);
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("session", session);
                jsonObject.put("result", result.getBoolean("success") == true ? "success" : "fail");
                jsonObject.put("success", result.getBoolean("success"));
                log.error("request:{}", jsonObject);
                //2.调用接口
                requestParas = jsonObject.toString();
                JSONObject jsonObjectMes = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, jsonObject);
                responseParas = jsonObjectMes.toString();
                log.error("respone:{}", responseParas);
                String backMsg = jsonObjectMes.getString("msg");
                if (jsonObjectMes.getBoolean("success") == false) {
                    log.error("请求失败");
                    errorMsg = "下载结果通知接口返回失败：" + backMsg;
                    jbResult = new JSONObject();
                    jbResult.put("isSaveFlag", true);
                    jbResult.put("esbInterfCode", esbInterfCode);
                    jbResult.put("token", token);
                    jbResult.put("requestParas", requestParas);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
            }
            //上传成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", errorMsg);
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    public void loadData(String localFilePath, String recipeName, String lot) throws Exception {
        File file = new File(localFilePath);
        if (!file.exists()) {
            log.error("localFilePath does not exist :{}", localFilePath);
        }
        List<String> data = importCsv(file);
        if (CollectionUtils.isEmpty(data)) {
            log.error("data is empty targetFile:{}", localFilePath);
            return;
        }
        String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
        long recipe_id = cFuncDbSqlResolve.GetIncreaseID("a_eap_fmod_recipe_id_seq", false);
        String insertRecipeSql = "insert into a_eap_fmod_recipe(created_by,creation_date,recipe_id,recipe_type,recipe_name,recipe_version,device_code,device_des,material_code,material_des,enable_flag) " +
                "values('null','" + nowDateTime + "'," + recipe_id + ",'MATERIAL','" + recipeName + "','" + CFuncUtilsSystem.GetNowDateTime("yyMMddHHmmss") + "','" + lot + "','','','','Y')";
        cFuncDbSqlExecute.ExecUpdateSql("", insertRecipeSql, true, null, null);
        String insertRecipeDetailSQL = "insert into a_eap_fmod_recipe_detail(created_by,creation_date,recipe_detail_id,recipe_id,parameter_code,parameter_des,parameter_val,tag_id,enable_flag) values ";
        long recipe_detail_id = cFuncDbSqlResolve.GetIncreaseID("a_eap_fmod_recipe_detail_id_seq", false);
        List<Map<String, Object>> tagData = cFuncDbSqlExecute.ExecSelectSql("", "select data_type,data_format,tag_des as parameter_des,tag_id,tag_code as parameter_code from scada_tag as tag LEFT JOIN scada_tag_group as grp on tag.tag_group_id=grp.tag_group_id  where tag_group_code= 'PlcRecipe'", false, null, null);
        Map<String, List<Map<String, Object>>> groupByTagId = tagData.stream().collect(Collectors.groupingBy(g -> g.get("tag_id").toString()));
        //将所有plc配方组的点位都初始化到配方详细里面
        for (int i = 1; i < data.size(); i++) {
            String[] split = data.get(i).replace("\"", "").split(",");
            Map<String, Object> tag = groupByTagId.get(split[2]).get(0);
            insertRecipeDetailSQL += "('','" + nowDateTime + "'," + (++recipe_detail_id) + ",'" + recipe_id + "','" + tag.get("parameter_code") + "','" + split[3] + "'," + "'" + split[6] + "','" + split[2] + "','Y'),";
        }
        insertRecipeDetailSQL = insertRecipeDetailSQL.substring(0, insertRecipeDetailSQL.length() - 1);
        cFuncDbSqlExecute.ExecUpdateSql("", insertRecipeDetailSQL, true, null, null);
        setIncreaseID("a_eap_fmod_recipe_detail_id_seq", recipe_detail_id);
        String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");
    }

    private void setIncreaseID(String seqName, long id) throws Exception {
        String sql = String.format("SELECT setval('%s', %d, true)", seqName, id);
        cFuncDbSqlExecute.ExecUpdateSql("", sql, true, null, null);
    }

    /**
     * 导入
     *
     * @param file csv文件(路径+文件)
     * @return
     */
    public static List<String> importCsv(File file) {
        List<String> dataList = new ArrayList<String>();

        BufferedReader br = null;
        try {
            br = new BufferedReader(new FileReader(file));
            String line = "";
            while ((line = br.readLine()) != null) {
                dataList.add(line);
            }
        } catch (Exception e) {
        } finally {
            if (br != null) {
                try {
                    br.close();
                    br = null;
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

        return dataList;
    }
}
