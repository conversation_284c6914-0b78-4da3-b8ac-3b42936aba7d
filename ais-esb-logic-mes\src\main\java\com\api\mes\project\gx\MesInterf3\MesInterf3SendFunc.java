package com.api.mes.project.gx.MesInterf3;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 国轩改造项目上报信息相关接口
 * 1.首工序获取工单接口
 * 2.非工序获取工单接口
 * 3.全工序单个物料投入校验接口
 * 4.全工序全量物料投入校验接口
 * 5.模组线生产数据上传接口
 * 6.Pack线生产数据上传接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-10-25
 */
@RestController
@Slf4j
@RequestMapping("/mes/interf3/project/gx")
public class MesInterf3SendFunc
{
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MesGxInterfCommon mesGxInterfCommon;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.[接口]首工序获取工单接口
    public JSONObject GetSourceOrderInfoByProcess(JSONObject jsonParas) throws Exception
    {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "GetSourceOrderInfoByProcess";
        String esbInterfCode = "GetSourceOrderInfoByProcess";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try
        {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            int produce_type = jsonParas.getInteger("produce_Type");
            String work_center_code = jsonParas.getString("work_center_code");//工作中心
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("produce_Type", produce_type);//生成类型：1，电芯（C），2模组（M），3电池包（P）
            postParas.put("tenantID", work_center_code);//庐江2线ID：JD
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = mesGxInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONArray response_datas = mesGxInterfCommon.GetResponseDatas(JSONObject.parseObject(jsonObjectBack));
            if (response_datas != null && response_datas.size() > 0)
            {
                for (int i = 0; i < response_datas.size(); i++)
                {
                    JSONObject jbItem = response_datas.getJSONObject(i);
                    String code = jbItem.getString("code");//工单编号
                    String type = jbItem.getString("type");//生产工单类型
                    String route_No = jbItem.getString("route_No");//工艺路线编码
                    String route_Name = jbItem.getString("route_Name");//工艺路线名称
                    int orderProductCount = jbItem.getInteger("orderProductCount");//产出数量
                    int orderInputCount = jbItem.getInteger("orderInputCount");//投入数量
                    String produce_Type = jbItem.getString("produce_Type");//生产类型1，电芯（C），2模组（M），3电池包（P）
                    String plan_Start_date = jbItem.getString("plan_Start_date");//计划开始日期
                    String plan_End_date = jbItem.getString("plan_End_date");//计划结束时间
                    String specsCode = jbItem.getString("specsCode");//产品规格
                    String isCompleteRoute = jbItem.getString("isCompleteRoute");//是否完整工单
                    String polarDesc = jbItem.getString("polarDesc");//极性
                    String formulaCode = jbItem.getString("formulaCode");//配方编码
                    String faC_NO = jbItem.getString("faC_NO");//工厂代码
                    String cellType = jbItem.getString("cellType");//材料类型
                    String order_Status = jbItem.getString("order_Status");//工单状态：1，新建；2，下发，3执行中
                    int formulaCount1 = jbItem.getInteger("formulaCount1");//配方1数量
                    String formulaSpecs1 = jbItem.getString("formulaSpecs1");//配方规格1
                    int formulaCount2 = jbItem.getInteger("formulaCount2");//配方2数量
                    String formulaSpecs2 = jbItem.getString("formulaSpecs2");//配方规格2
                    String union_Line = jbItem.getString("union_Line");//关联线体
                    String union_Code = jbItem.getString("union_Code");//关联工单
                    int order_Type = jbItem.getInteger("order_Type");//工单类型：0.普通工单；1.主工单；2.辅工单'
                    int product_Mode_First = jbItem.getInteger("product_Mode_First");//生产模组1方式
                    int product_Mode_Second = jbItem.getInteger("product_Mode_Second");//生产模组2方式
                    int product_Mode_Three = jbItem.getInteger("product_Mode_Three");//生产模组3方式
                    String projectCode = jbItem.getString("projectCode");//项目号
                    String cell_Level = jbItem.getString("cell_Level");//电芯允许挡位
                    String cell_Batch = jbItem.getString("cell_Batch");//电芯允许批次
                    String cell_Spec = jbItem.getString("cell_Spec");//电芯允许规格
                    String mo_type = "";
                    switch (produce_Type)
                    {
                        case "1":
                            mo_type = "DX";
                            break;
                        case "2":
                            mo_type = "MODEL";
                            break;
                        case "3":
                            mo_type = "PACK";
                            break;
                    }
                    String mo_status = "";
                    switch (order_Status)
                    {
                        case "1":
                            mo_status = "WAIT_PUBLISH";
                            break;
                        case "2":
                            mo_status = "LINE_UP";
                            break;
                        case "3":
                            mo_status = "CARRY_ON";
                            break;
                    }
                    //插入订单表c_mes_aps_plan_mo
                    long prod_line_id = 0;
                    String sqlProdLine = "select prod_line_id from sys_fmod_prod_line where prod_line_code='" + union_Line + "'";
                    List<Map<String, Object>> itemListProdLine = cFuncDbSqlExecute.ExecSelectSql("", sqlProdLine, false, null, "");
                    if (itemListProdLine != null && itemListProdLine.size() > 0)
                    {
                        prod_line_id = Long.parseLong(itemListProdLine.get(0).get("prod_line_id").toString());
                    }
                    String mo_online_count = "0";
                    String mo_offline_count = "0";
                    String mo_finish_count = "0";
                    String mo_scrap_count = "0";
                    String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                    long mo_id = cFuncDbSqlResolve.GetIncreaseID("c_mes_aps_plan_mo_id_seq", false);
                    String insertSql = "insert into c_mes_aps_plan_mo(created_by,creation_date,mo_id,prod_line_id,mo_from,mo_type," +
                            "make_order,product_batch,small_model_type,mo_plan_count,mo_online_count,mo_offline_count,mo_finish_count," +
                            "mo_scrap_count,mo_status,mo_order_by,liable_person,remarks,plan_start_time," +
                            "plan_end_time,enable_flag,attribute1,attribute2,attribute3) " +
                            "values('MES','" + nowDateTime + "','" + mo_id + "','" + prod_line_id + "','MES','" + mo_type + "'," +
                            "'" + code + "','" + route_Name + "','" + route_No + "','" + orderProductCount + "','" + mo_online_count + "'," +
                            "'" + mo_offline_count + "','" + mo_finish_count + "','" + mo_scrap_count + "'," +
                            "'" + mo_status + "','" + (i + 1) + "','MES','','" + plan_Start_date + "','" + plan_End_date + "','Y','" + projectCode + "','" + faC_NO + "','" + cellType + "')";
                    cFuncDbSqlExecute.ExecUpdateSql("AIS", insertSql, true, null, "");

                    String sqlRecipe = "select recipe.recipe_id from " +
                            "c_mes_fmod_recipe recipe join c_mes_fmod_small_model_recipe model_recipe " +
                            "on recipe.recipe_id=model_recipe.recipe_id join c_mes_fmod_small_model model " +
                            "on model.small_type_id=model_recipe.small_type_id where model.small_model_type='" + route_No + "'";
                    List<Map<String, Object>> itemListRecipe = cFuncDbSqlExecute.ExecSelectSql("", sqlRecipe, false, null, "");
                    if (itemListRecipe != null && itemListRecipe.size() > 0)
                    {
                        for (int k = 0; k < itemListRecipe.size(); k++)
                        {
                            String recipe_id = itemListRecipe.get(k).get("recipe_id").toString();
                            long mo_recipe_id = cFuncDbSqlResolve.GetIncreaseID("c_mes_aps_plan_mo_recipe_id_seq", false);
                            String insertSql01 = "insert into c_mes_aps_plan_mo_recipe(mo_recipe_id,mo_id,recipe_id) values('" + mo_recipe_id + "','" + mo_id + "','" + recipe_id + "')";
                            cFuncDbSqlExecute.ExecUpdateSql("AIS", insertSql01, true, null, "");
                        }
                    }
                }
            }

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_datas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "获取成功");
        }
        catch (Exception ex)
        {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.[接口]非首工序获取工单
    public JSONObject GetOtherOrderInfoByProcess(JSONObject jsonParas) throws Exception
    {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "GetOtherOrderInfoByProcess";
        String esbInterfCode = "GetOtherOrderInfoByProcess";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try
        {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            String station_code = jsonParas.getString("station_code");
            String serial_num = jsonParas.getString("serial_num");
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("technicsProcessCode", station_code);//工序编码
            postParas.put("productCode", serial_num);//在制品条码
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = mesGxInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONArray response_datas = mesGxInterfCommon.GetResponseDatas(JSONObject.parseObject(jsonObjectBack));
            if (response_datas != null && response_datas.size() > 0)
            {
                for (int i = 0; i < response_datas.size(); i++)
                {
                    JSONObject jbItem = response_datas.getJSONObject(i);
                    String orderCode = jbItem.getString("orderCode");//工单编码
                    String route_No = jbItem.getString("route_No");//工艺路线编码
                    String specsCode = jbItem.getString("specsCode");//电芯规格
                    String cell_Level = jbItem.getString("cell_Level");//电芯挡位
                    String cell_Batch = jbItem.getString("cell_Batch");//电芯批次
                    String productMixCode = jbItem.getString("productMixCode");//产品规格
                }
            }

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_datas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "获取成功");
        }
        catch (Exception ex)
        {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.[接口]单个物料校验接口
    public JSONObject MaterialCheckInput(JSONObject jsonParas) throws Exception
    {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "MaterialCheckInput";
        String esbInterfCode = "MaterialCheckInput";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try
        {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            String station_code = jsonParas.getString("station_code");
            String routeNo = jsonParas.getString("routeNo");
            String make_order = jsonParas.getString("make_order");
            String material_code = jsonParas.getString("material_code");
            String work_center_code = jsonParas.getString("work_center_code");
            String prod_line_code = jsonParas.getString("prod_line_code");
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("technicsProcessCode", station_code);//工序编码
            postParas.put("routeNo", routeNo);//工艺路线编码
            postParas.put("produceOrderCode", make_order);//工单编码
            postParas.put("materialCode", material_code);//投入物料的条码
            postParas.put("tenantID", work_center_code);//产线编码
            postParas.put("productLine", prod_line_code);//线体编码

            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = mesGxInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = mesGxInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "获取成功");
        }
        catch (Exception ex)
        {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.全量物料校验接口
    public JSONObject CompleteCheckInput(JSONObject jsonParas) throws Exception
    {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CompleteCheckInput";
        String esbInterfCode = "CompleteCheckInput";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try
        {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            String station_code = jsonParas.getString("station_code");
            String routeNo = jsonParas.getString("small_model_type");
            String make_order = jsonParas.getString("make_order");
            JSONArray materialList = jsonParas.getJSONArray("materialList");
            String productMixCode = jsonParas.getString("productMixCode");
            String work_center_code = "JD";
            String prod_line_code = jsonParas.getString("prod_line_code");
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("technicsProcessCode", station_code);//工序编码
            postParas.put("routeNo", routeNo);//工艺路线编码
            postParas.put("produceOrderCode", make_order);//工单编码
            postParas.put("materialList", materialList);//物料集合
            postParas.put("tenantID", work_center_code);//产线ID
            postParas.put("productMixCode", productMixCode);//产品规格
            postParas.put("productLine", prod_line_code);//线体编码
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = mesGxInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = mesGxInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "获取成功");
        }
        catch (Exception ex)
        {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //5.[接口]模组数据上传MES接口
    public JSONObject PushMessageToMes(JSONObject jsonParas) throws Exception
    {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "PushMessageToMes";
        String esbInterfCode = "PushMessageToMes";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try
        {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            String station_code = jsonParas.getString("station_code");
            String station_des = jsonParas.getString("station_des");
            String routeNo = jsonParas.getString("small_model_type");
            String make_order = jsonParas.getString("make_order");
            String product_type = jsonParas.getString("product_type");
            String work_center_code = "";
            String prod_line_code = jsonParas.getString("prod_line_code");
            String arrive_date = jsonParas.getString("arrive_date");
            String technicsStepCode = "";
            String technicsStepName = "";
            String serial_num = jsonParas.getString("serial_num");
            String productQuality = jsonParas.getString("quality_sign");
            String technicsParamQuality = "NG".equals(productQuality) ? "0" : "1";
            String userName = "MES";
            String userAccount = "MES";
            String deviceCode = "001";
            String deviceName = "001";
            String remarks = "";
            JSONArray produceInEntityList = new JSONArray();
            JSONObject inEntity = new JSONObject();
            inEntity.put("productCode", serial_num);
            inEntity.put("productCount", 1);
            produceInEntityList.add(inEntity);

            JSONArray produceParamEntityList = new JSONArray();
            JSONArray ngEntityList = new JSONArray();
            JSONArray cellParamEntityList = new JSONArray();
            JSONArray otherParamEntityList = new JSONArray();

            if (product_type.equals("DX"))
            {
                String qualityTable = "c_mes_me_dx_quality";
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("dx_barcode").is(serial_num));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("qualityTable").find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                while (iteratorBigData.hasNext())
                {
                    Document doc = iteratorBigData.next();
                    double dx_pressure_kl_value = doc.getDouble("dx_pressure_kl_value");
                    double dx_pressure_fd_value = doc.getDouble("dx_pressure_fd_value");
                    double dx_dianzu_value = doc.getDouble("dx_dianzu_value");
                    JSONObject paramEntity1 = new JSONObject();
                    paramEntity1.put("productCode", serial_num);
                    paramEntity1.put("technicsParamName", "电阻");
                    paramEntity1.put("technicsParamCode", "dx_dianzu_value");
                    paramEntity1.put("technicsParamValue", dx_dianzu_value);
                    paramEntity1.put("technicsParamQuality", technicsParamQuality);
                    paramEntity1.put("desc", "");
                    produceInEntityList.add(paramEntity1);

                    JSONObject paramEntity2 = new JSONObject();
                    paramEntity2.put("productCode", serial_num);
                    paramEntity2.put("technicsParamName", "开路电压");
                    paramEntity2.put("technicsParamCode", "dx_pressure_kl_value");
                    paramEntity2.put("technicsParamValue", dx_pressure_kl_value);
                    paramEntity2.put("technicsParamQuality", technicsParamQuality);
                    paramEntity2.put("desc", "");
                    produceInEntityList.add(paramEntity2);

                    JSONObject paramEntity3 = new JSONObject();
                    paramEntity3.put("productCode", serial_num);
                    paramEntity3.put("technicsParamName", "负路电压");
                    paramEntity3.put("technicsParamCode", "dx_pressure_fd_value");
                    paramEntity3.put("technicsParamValue", dx_pressure_fd_value);
                    paramEntity3.put("technicsParamQuality", technicsParamQuality);
                    paramEntity3.put("desc", "");
                    produceInEntityList.add(paramEntity3);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("produceOrderCode", make_order);//生产工单编码
            postParas.put("routeNo", routeNo);//工艺路线编码
            postParas.put("technicsProcessCode", station_code);//工序编码。甲方提供工序编码列表供乙方使用
            postParas.put("technicsProcessName", station_des);//工序名称。甲方提供工序名称列表供乙方使用
            postParas.put("technicsStepCode", technicsStepCode);//工步编码
            postParas.put("technicsStepName", technicsStepName);//工步名称
            postParas.put("productCode", serial_num);//产品编码
            postParas.put("productCount", 1);//产品数量
            postParas.put("productQuality", 1);//产品质量：0，整批不合格；1，合格；
            postParas.put("produceDate", nowDateTime);//生产日期
            postParas.put("startTime", arrive_date);//生产开始时间
            postParas.put("endTime", nowDateTime);//生产结束时间
            postParas.put("userName", userName);//用户名称，甲方提供
            postParas.put("userAccount", userAccount);//用户账号，甲方提供
            postParas.put("deviceCode", deviceCode);//设备编码。甲方提供设备编码列表供乙方使用
            postParas.put("deviceName", deviceName);//设备名称。甲方提供设备名称列表供乙方使用
            postParas.put("remarks", remarks);//备注
            postParas.put("tenantID", "");//产线ID
            postParas.put("produceInEntityList", produceInEntityList);//投入集合
            postParas.put("produceParamEntityList", produceParamEntityList);//产出集合
            postParas.put("ngEntityList", ngEntityList);//NG参数集合
            postParas.put("cellParamEntityList", cellParamEntityList);//电芯参数集合
            postParas.put("otherParamEntityList", otherParamEntityList);//详细参数集合
            JSONArray array = new JSONArray();
            array.add(postParas);
            requestParas = array.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = mesGxInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = mesGxInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "获取成功");
        }
        catch (Exception ex)
        {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //6.[接口]PACK数据上传MES接口
    public JSONObject PushPackMessageToMes(JSONObject jsonParas) throws Exception
    {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "PushPackMessageToMes";
        String esbInterfCode = "PushPackMessageToMes";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try
        {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            String station_code = jsonParas.getString("station_code");
            String routeNo = jsonParas.getString("routeNo");
            String produceOrderCode = jsonParas.getString("station_code");
            String materialCode = jsonParas.getString("station_code");
            String work_center_code = jsonParas.getString("work_center_code");
            String prod_line_code = jsonParas.getString("prod_line_code");
            //1.创建参数
            JSONObject postParas = new JSONObject();
            postParas.put("technicsProcessCode", station_code);//工序编码
            postParas.put("routeNo", routeNo);//工艺路线编码
            postParas.put("produceOrderCode", produceOrderCode);//工单编码
            postParas.put("materialCode", materialCode);//投入物料的条码
            postParas.put("tenantID", work_center_code);//产线编码
            postParas.put("productLine", prod_line_code);//线体编码

            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0)
            {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals(""))
            {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            String jsonObjectBack = mesGxInterfCommon.PostJbBackJb(funcName, esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack;
            JSONObject response_data = mesGxInterfCommon.GetResponseData(JSONObject.parseObject(jsonObjectBack));

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseData", response_data);
            jbResult.put("successFlag", true);
            jbResult.put("message", "获取成功");
        }
        catch (Exception ex)
        {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
