package com.api.base;

public interface Const
{
    String BLANK = "";
    String NULL = "null";

    int CODE_OK = 0;
    int CODE_ERROR = -1;

    String DATE_FORMAT_DEFAULT = "yyyyMMddHHmmss";
    String DATE_FORMAT_LOCAL = "yyyy-MM-dd HH:mm:ss.SSS";

    String FLAG_N = "N";
    String FLAG_Y = "Y";

    String KEY_ACTION = "action";
    String KEY_LANG = "lang";
    String KEY_PAGE = "page";
    String KEY_SIZE = "size";
    String KEY_SORT = "sort";
    String KEY_SORTED = "sorted";
    String KEY_USER_CODE = "user_code";
    String KEY_USER_NAME = "user_name";
    String KEY_PARAMETER_CODE = "parameter_code";
    String KEY_SKIP = "skip";

    String PARAM_CODE_SYS_USER_PASSWORD_EXPIRE_DAYS = "SYS_USER_PASSWORD_EXPIRE_DAYS";

    String MESSAGES_SYS_PASSWORD_HAS_EXPIRED = "sys.passwordHasExpired";

    String OK = "OK";
    String NG = "NG";

    String ON = "ON";
    String OFF = "OFF";

    String QUERY_PARAM_USER_CODE = "userCode";

    String PROFILE_DEV = "dev";
    String PROFILE_PROD = "prod";

    String PATH_ENABLE_FLAG = "enable_flag";
    String PATH_ITEM_DATE = "item_date";
    String PATH_ITEM_DATE_VAL = "item_date_val";
    String[] IGNORE_PATHS = {PATH_ITEM_DATE, PATH_ITEM_DATE_VAL};

    String PROPERTY_ENABLE_FLAG = PATH_ENABLE_FLAG;
    String PROPERTY_ITEM_DATE = PATH_ITEM_DATE;
    String PROPERTY_ITEM_DATE_VAL = PATH_ITEM_DATE_VAL;

    String SERVICE_NAME = "aisEsbApi";
}
