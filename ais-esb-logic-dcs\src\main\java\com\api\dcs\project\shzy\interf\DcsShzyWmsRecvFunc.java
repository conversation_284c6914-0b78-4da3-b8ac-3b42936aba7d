package com.api.dcs.project.shzy.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import com.api.dcs.project.shzy.api.DcsShzyWmsMapStockFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * WMS接受流程功能函数
 * 1.分拣请求开始任务
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@Service
@Slf4j
public class DcsShzyWmsRecvFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsShzyWmsInterfCommon dcsShzyWmsInterfCommon;
    @Autowired
    private DcsShzyWmsMapStockFunc dcsShzyWmsMapStockFunc;

    //1.分拣请求开始任务
    public JSONObject RecvFjStartTask(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "RecvFjStartTask";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String wmsFjTaskTable = "b_dcs_wms_fj_task";
        String wmsKitTaskTable="b_dcs_wms_dailykit_task";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.接受参数
            JSONObject jbData=jsonParas.getJSONObject("data");
            String partTaskNo=jbData.getString("partTaskNo");
            String projectCode=jbData.getString("projectCode");
            String partCode=jbData.getString("partCode");
            String plateCode=jbData.getString("plateCode");
            Integer partLength=jbData.getInteger("partLength");
            Integer partWidth=jbData.getInteger("partWidth");
            Integer partThickness=jbData.getInteger("partThickness");
            Integer grabDirection=jbData.getInteger("grabDirection");
            Integer offsetX=jbData.getInteger("offsetX");
            Integer offsetY=jbData.getInteger("offsetY");
            if (partTaskNo == null) partTaskNo = "";
            if (projectCode == null) projectCode = "";
            if (partCode == null) partCode = "";
            if (plateCode == null) plateCode = "";
            if (partLength == null) partLength = 0;
            if (partWidth == null) partWidth = 0;
            if (partThickness == null) partThickness = 0;
            if (grabDirection == null) grabDirection = 0;
            if (offsetX == null) offsetX = 0;
            if (offsetY == null) offsetY = 0;

            //2.判断参数信息
            if(partTaskNo.equals("")){
                errorMsg="分拣传递任务号不能为空";
                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if(projectCode.equals("")){
                errorMsg="分拣传递项目编号不能为空";
                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if(partCode.equals("")){
                errorMsg="分拣传递物料号不能为空";
                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(partLength<=0 || partWidth<=0 || partThickness<=0){
                errorMsg="板长|板宽|板厚不能<=0";
                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //3.判断任务信息是否存在
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(partTaskNo));
            long taskCount = mongoTemplate.getCollection(wmsFjTaskTable).countDocuments(queryBigData.getQueryObject());
            if(taskCount>0){
                errorMsg="分拣入库任务已经存在WMS系统";
                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断是否存在WORK任务
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_status").is("WORK"));
            taskCount = mongoTemplate.getCollection(wmsFjTaskTable).countDocuments(queryBigData.getQueryObject());
            if(taskCount>0){
                errorMsg="分拣入库任务存在进行中任务未完结,不允许插入新的分拣入库任务";
                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //查询当前任务是否属于当日配套任务
            String kit_task_id="";
            String kit_task_num="";
            String kit_structure_no="";
            String kit_structure_sn="";
            String kit_material_type="";
            String kit_flag="N";
            String task_date=CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd");

            queryBigData = new Query();
            //取消日期维度条件
            //queryBigData.addCriteria(Criteria.where("task_date").is(task_date));
            queryBigData.addCriteria(Criteria.where("use_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("project_code").is(projectCode));
            queryBigData.addCriteria(Criteria.where("material_code").is(partCode));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(wmsKitTaskTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                kit_task_id=docItemBigData.getString("kit_task_id");
                kit_task_num=docItemBigData.getString("task_num");
                kit_structure_no=docItemBigData.getString("structure_no");
                kit_structure_sn=docItemBigData.getString("structure_sn");
                kit_material_type=docItemBigData.getString("material_type");
                kit_flag=docItemBigData.getString("kit_flag");
                iteratorBigData.close();
            }
            if(!kit_task_id.equals("")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("kit_task_id").is(kit_task_id));
                Update updateBigData = new Update();
                updateBigData.set("use_flag", "Y");
                mongoTemplate.updateFirst(queryBigData, updateBigData, wmsKitTaskTable);
            }
            //4.插入分拣启动任务
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            String fj_task_id = CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("fj_task_id", fj_task_id);
            mapBigDataRow.put("task_num", partTaskNo);
            mapBigDataRow.put("project_code", projectCode);
            mapBigDataRow.put("material_code", partCode);
            mapBigDataRow.put("serial_num", plateCode);
            mapBigDataRow.put("material_length", partLength);
            mapBigDataRow.put("material_width", partWidth);
            mapBigDataRow.put("material_thickness", partThickness);
            mapBigDataRow.put("grab_direction", grabDirection);
            mapBigDataRow.put("off_set_x", offsetX);
            mapBigDataRow.put("off_set_y", offsetY);
            mapBigDataRow.put("task_status", "PLAN");
            mapBigDataRow.put("stock_group_code", "");
            mapBigDataRow.put("start_cell_row", 0);
            mapBigDataRow.put("end_cell_row", 0);
            mapBigDataRow.put("start_cell_col", 0);
            mapBigDataRow.put("end_cell_col", 0);
            mapBigDataRow.put("position_x", 0);
            mapBigDataRow.put("position_y", 0);
            mapBigDataRow.put("position_z", 0);
            mapBigDataRow.put("stock_code", "");
            mapBigDataRow.put("start_date", "");
            mapBigDataRow.put("end_date", "");
            mapBigDataRow.put("stock_id", 0L);
            mapBigDataRow.put("kit_task_date", task_date);
            mapBigDataRow.put("kit_task_id", kit_task_id);
            mapBigDataRow.put("kit_task_num", kit_task_num);
            mapBigDataRow.put("kit_structure_no", kit_structure_no);
            mapBigDataRow.put("kit_structure_sn", kit_structure_sn);
            mapBigDataRow.put("kit_material_type", kit_material_type);
            mapBigDataRow.put("kit_flag", kit_flag);
            mongoTemplate.insert(mapBigDataRow, wmsFjTaskTable);

            //创建返回信息
            responseParas = dcsShzyWmsInterfCommon.CreateResponse(200, "OK");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.分拣放置完成报工
    public JSONObject RecvFjFinishTask(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "RecvFjFinishTask";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String wmsFjTaskTable = "b_dcs_wms_fj_task";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.接受参数
            JSONObject jbData=jsonParas.getJSONObject("data");
            String partTaskNo=jbData.getString("partTaskNo");
            String status=jbData.getString("status");
            if (partTaskNo == null) partTaskNo = "";
            if (status == null) status = "";
            //查询任务信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(partTaskNo));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(wmsFjTaskTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(!iteratorBigData.hasNext()){
                errorMsg="分拣入库任务号{"+partTaskNo+"}不存在WMS系统中,请先发送任分拣入库任务到WMS";
                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            Document docItemBigData = iteratorBigData.next();
            Long stock_id=docItemBigData.getLong("stock_id");
            String stock_group_code=docItemBigData.getString("stock_group_code");
            String stock_code=docItemBigData.getString("stock_code");
            Integer m_length=docItemBigData.getInteger("material_length");
            Integer m_width=docItemBigData.getInteger("material_width");
            Integer m_thickness=docItemBigData.getInteger("material_thickness");
            Integer start_cell_row=docItemBigData.getInteger("start_cell_row");
            Integer end_cell_row=docItemBigData.getInteger("end_cell_row");
            Integer start_cell_col=docItemBigData.getInteger("start_cell_col");
            Integer end_cell_col=docItemBigData.getInteger("end_cell_col");
            Integer position_x=docItemBigData.getInteger("position_x");
            Integer position_y=docItemBigData.getInteger("position_y");
            Integer position_z=docItemBigData.getInteger("position_z");
            String task_from="FJ";
            String task_num=partTaskNo;
            String material_code=docItemBigData.getString("material_code");//3
            String project_code=docItemBigData.getString("project_code");
            String serial_num=docItemBigData.getString("serial_num");
            String kit_task_num=docItemBigData.getString("kit_task_num");//1
            String kit_structure_no=docItemBigData.getString("kit_structure_no");
            String kit_structure_sn=docItemBigData.getString("kit_structure_sn");
            String kit_material_type=docItemBigData.getString("kit_material_type");//2
            String kit_flag=docItemBigData.getString("kit_flag");
            String temp_flag="N";
            if(!kit_flag.equals("Y")) temp_flag="Y";
            String task_way="AUTO";
            String task_type="SL_KW";
            iteratorBigData.close();
            //2.更新任务状态以及更新库存
            if(status.equals("1")){
                JSONObject jbRow=new JSONObject();
                jbRow.put("stock_id",stock_id);
                jbRow.put("stock_group_code",stock_group_code);
                jbRow.put("stock_code",stock_code);
                jbRow.put("m_length",m_length);
                jbRow.put("m_width",m_width);
                jbRow.put("m_thickness",m_thickness);
                jbRow.put("start_cell_row",start_cell_row);
                jbRow.put("end_cell_row",end_cell_row);
                jbRow.put("start_cell_col",start_cell_col);
                jbRow.put("end_cell_col",end_cell_col);
                jbRow.put("position_x",position_x);
                jbRow.put("position_y",position_y);
                jbRow.put("position_z",position_z);
                jbRow.put("task_from",task_from);
                jbRow.put("task_num",task_num);
                jbRow.put("material_code",material_code);
                jbRow.put("lot_num",project_code);
                jbRow.put("task_way",task_way);
                jbRow.put("task_type",task_type);
                jbRow.put("serial_num",serial_num);
                jbRow.put("temp_flag",temp_flag);
                jbRow.put("kit_task_num",kit_task_num);
                jbRow.put("kit_structure_no",kit_structure_no+"-"+kit_structure_sn);
                jbRow.put("kit_material_type",kit_material_type);
                jbRow.put("kit_flag",kit_flag);
                dcsShzyWmsMapStockFunc.MapStockIn(jbRow);
                //完成任务
                Update updateBigData = new Update();
                updateBigData.set("task_status", "FINISH");
                updateBigData.set("end_date", CFuncUtilsSystem.GetNowDateTime(""));
                mongoTemplate.updateFirst(queryBigData, updateBigData, wmsFjTaskTable);
            }
            else{
                //3.取消任务
                Update updateBigData = new Update();
                updateBigData.set("task_status", "CANCEL");
                updateBigData.set("end_date", CFuncUtilsSystem.GetNowDateTime(""));
                mongoTemplate.updateFirst(queryBigData, updateBigData, wmsFjTaskTable);

                Query query = new Query();
                Update update = new Update();
                query.addCriteria(Criteria.where("task_num").is(kit_task_num));
                query.addCriteria(Criteria.where("material_type").is(kit_material_type));
                query.addCriteria(Criteria.where("material_code").is(material_code));
                update.set("use_flag", "N");
                mongoTemplate.updateFirst(query, update, "b_dcs_wms_dailykit_task");

                //取消锁定
                if(stock_id<=0L){
                    dcsShzyWmsMapStockFunc.LockMapStockCell("FJ",task_num,stock_group_code,
                            start_cell_row,end_cell_row,start_cell_col,end_cell_col,"N");
                }
                else{
                    String sqlUpdateStock="update b_dcs_wms_map_me_stock set " +
                            "last_updated_by='FJ'," +
                            "last_update_date='"+ CFuncUtilsSystem.GetNowDateTime("") +"'," +
                            "lock_flag='N' " +
                            "where stock_id="+stock_id+"";
                    cFuncDbSqlExecute.ExecUpdateSql("WMS",sqlUpdateStock,false,request,apiRoutePath);
                }
            }
            //创建返回信息
            responseParas = dcsShzyWmsInterfCommon.CreateResponse(200, "OK");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.WMS接受中控出库任务
    public JSONObject RecvStockOutTask(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "RecvStockOutTask";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String wmsTaskTable = "b_dcs_wms_car_task";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.接受参数
            String taskNum=jsonParas.getString("feedTaskNo");
            String projectCode=jsonParas.getString("projectCode");
            String serialNum=jsonParas.getString("plateCode");
            String materialCode=jsonParas.getString("partCode");
            String toStockCode=jsonParas.getString("targetDevice");
            String version=jsonParas.getString("version");
            if (taskNum == null) taskNum = "";
            if (projectCode == null) projectCode = "";
            if (serialNum == null) serialNum = "";
            if (materialCode == null) materialCode = "";
            if (toStockCode == null) toStockCode = "";
            if (version == null) version = "";

            //2.判断参数信息
            if(taskNum.equals("")){
                errorMsg="出库任务号不能为空";
                responseParas = dcsShzyWmsInterfCommon.CreateZkResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(materialCode.equals("") || projectCode.equals("")){
                errorMsg="传递物料号|项目编号不能为空";
                responseParas = dcsShzyWmsInterfCommon.CreateZkResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(toStockCode.equals("")){
                errorMsg="出库目标位置不能为空";
                responseParas = dcsShzyWmsInterfCommon.CreateZkResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String sqlStockCount="select count(1) " +
                    "from b_dcs_wms_fmod_stock " +
                    "where stock_code='"+toStockCode+"' " +
                    "and stock_region_code='XL'";
            Integer stockCount=cFuncDbSqlResolve.GetSelectCount(sqlStockCount);
            if(stockCount<=0){
                errorMsg="出库目标位置不存在WMS中";
                responseParas = dcsShzyWmsInterfCommon.CreateZkResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //3.判断任务信息是否存在
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(taskNum));
            long taskCount = mongoTemplate.getCollection(wmsTaskTable).countDocuments(queryBigData.getQueryObject());
            if(taskCount>0){
                errorMsg="出库任务已经存在WMS系统";
                responseParas = dcsShzyWmsInterfCommon.CreateZkResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //4.插入分拣启动任务
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            String task_id = CFuncUtilsSystem.CreateUUID(true);
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("task_id", task_id);
            mapBigDataRow.put("task_from", "ZK");
            mapBigDataRow.put("task_way", "AUTO");
            mapBigDataRow.put("task_type", "KW_XL");
            mapBigDataRow.put("task_num", taskNum);
            mapBigDataRow.put("serial_num", serialNum);
            mapBigDataRow.put("lot_num", projectCode);
            mapBigDataRow.put("model_type", materialCode);
            mapBigDataRow.put("from_stock_code", "");
            mapBigDataRow.put("to_stock_code", toStockCode);
            mapBigDataRow.put("need_check_gd_flag", "N");
            mapBigDataRow.put("need_check_model_flag", "N");
            mapBigDataRow.put("need_tell_start_flag", "N");
            mapBigDataRow.put("tell_start_date", "");
            mapBigDataRow.put("tell_cancel_date", "");
            mapBigDataRow.put("tell_stop_date", "");
            mapBigDataRow.put("tell_start_by", "");
            mapBigDataRow.put("tell_cancel_by", "");
            mapBigDataRow.put("tell_stop_by", "");
            mapBigDataRow.put("task_status", "PLAN");
            mapBigDataRow.put("lock_flag", "N");
            mapBigDataRow.put("enable_flag", "Y");
            mapBigDataRow.put("attribute1", version);
            mapBigDataRow.put("attribute2", "");
            mapBigDataRow.put("attribute3", "");
            mapBigDataRow.put("cq_if_tags_list", "");
            mapBigDataRow.put("cq_if_tags_value", "");
            mapBigDataRow.put("fz_if_tags_list", "");
            mapBigDataRow.put("fz_if_tags_value", "");
            mapBigDataRow.put("check_gd_tag", "");
            mapBigDataRow.put("check_gd_value", "");
            mapBigDataRow.put("check_model_tag", "");
            mongoTemplate.insert(mapBigDataRow, wmsTaskTable);

            //创建返回信息
            responseParas = dcsShzyWmsInterfCommon.CreateZkResponse(200, "success");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.WMS接受中控切割与焊接任务
    public JSONObject RecvDailyKitTask(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "RecvDailyKitTask";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String wmsTaskTable = "b_dcs_wms_dailykit_task";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.接受参数，待定开发
            Integer eventType=jsonParas.getInteger("eventType");
            String taskDate=jsonParas.getString("taskDate");
            JSONArray jaTask=jsonParas.getJSONArray("mainPolishWeldTasks");
            if(eventType!=1 && eventType!=2){
                errorMsg="eventType字段只能为1或者2";
                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(taskDate==null || taskDate.equals("")){
                errorMsg="taskDate字段不能为空";
                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
//            这个位置米追飞说不影响，需要注释
//            if(jaTask==null || jaTask.size()<=0){
//                errorMsg="mainPolishWeldTasks任务集合不能为空";
//                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
//                jbResult.put("responseParas", responseParas);
//                jbResult.put("successFlag", false);
//                jbResult.put("message", errorMsg);
//                return jbResult;
//            }
            if(2==eventType){//删除指定日期计划
                List<String> lstTaskNo=new ArrayList<>();
                for(int i=0;i<jaTask.size();i++){
                    JSONObject jbTaskItem=jaTask.getJSONObject(i);
                    String taskNo=jbTaskItem.getString("taskNo");
                    if(taskNo==null) taskNo="";
                    if(!taskNo.equals("") && !lstTaskNo.contains(taskNo)) lstTaskNo.add(taskNo);
                }
//                这个位置米追飞说不影响，需要注释
//                if(lstTaskNo.size()<=0){
//                    errorMsg="mainPolishWeldTasks任务集合不能为空";
//                    responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
//                    jbResult.put("responseParas", responseParas);
//                    jbResult.put("successFlag", false);
//                    jbResult.put("message", errorMsg);
//                    return jbResult;
//                }
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("task_date").is(taskDate));
                queryBigData.addCriteria(Criteria.where("use_flag").is("N"));
                queryBigData.addCriteria(Criteria.where("task_num").in(lstTaskNo));
                mongoTemplate.remove(queryBigData, wmsTaskTable);
            }
            else{//新增指定日期计划
                List<Map<String, Object>> lstTaskDocuments = new ArrayList<>();
                Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                for(int i=0;i<jaTask.size();i++){
                    JSONObject jbTaskItem=jaTask.getJSONObject(i);
                    String taskNo=jbTaskItem.getString("taskNo");
                    String projectName=jbTaskItem.getString("projectName");
                    String projectCode=jbTaskItem.getString("projectCode");
                    String structureNo=jbTaskItem.getString("structureNo");
                    String structureSn=jbTaskItem.getString("structureSn");
                    Integer structureLength=jbTaskItem.getInteger("structureLength");
                    String material=jbTaskItem.getString("material");
                    JSONArray jaDetail=jbTaskItem.getJSONArray("structureDetail");
                    String kit_flag="N";//是否齐套
                    if(taskNo==null) taskNo="";
                    if(projectName==null) projectName="";
                    if(projectCode==null) projectCode="";
                    if(structureNo==null) structureNo="";
                    if(structureSn==null) structureSn="";
                    if(structureLength==null) structureLength=0;
                    if(material==null) material="";
                    if(taskNo.equals("") || projectCode.equals("") || structureNo.equals("")){
                        errorMsg="任务列表中taskNo|projectCode|structureNo字段不能为空";
                        responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                        jbResult.put("responseParas", responseParas);
                        jbResult.put("successFlag", false);
                        jbResult.put("message", errorMsg);
                        return jbResult;
                    }
                    if(jaDetail==null || jaDetail.size()<=0){
                        errorMsg="任务列表中零件明细集合为空";
                        responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                        jbResult.put("responseParas", responseParas);
                        jbResult.put("successFlag", false);
                        jbResult.put("message", errorMsg);
                        return jbResult;
                    }
                    if(jaDetail.size()==3){
                        Boolean checkA=false;
                        Boolean checkB=false;
                        Boolean checkC=false;
                        for(int j=0;j<jaDetail.size();j++){
                            JSONObject jbMaterial=jaDetail.getJSONObject(j);
                            String partType=jbMaterial.getString("partType");
                            String partCode=jbMaterial.getString("partCode");
                            if(partType==null) partType="";
                            if(partCode==null) partCode="";
                            if(partType.equals("") || partCode.equals("")){
                                errorMsg="任务号{"+taskNo+"}配套零件信息中partType|partCode不能为空";
                                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                                jbResult.put("responseParas", responseParas);
                                jbResult.put("successFlag", false);
                                jbResult.put("message", errorMsg);
                                return jbResult;
                            }
                            if(!partType.equals("A") && !partType.equals("B") && !partType.equals("C")){
                                errorMsg="任务号{"+taskNo+"}配套零件信息中partType只能约束为A|B|C";
                                responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                                jbResult.put("responseParas", responseParas);
                                jbResult.put("successFlag", false);
                                jbResult.put("message", errorMsg);
                                return jbResult;
                            }
                            if(partType.equals("A")) checkA=true;
                            if(partType.equals("B")) checkB=true;
                            if(partType.equals("C")) checkC=true;
                        }
                        if(checkA && checkB && checkC) kit_flag="Y";
                    }
                    //判断任务是否存在
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("task_num").is(taskNo));
                    long taskCount=mongoTemplate.getCollection(wmsTaskTable).countDocuments(queryBigData.getQueryObject());
                    if(taskCount>0){
                        errorMsg="任务号{"+taskNo+"}已存在,不允许重复新增";
                        responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
                        jbResult.put("responseParas", responseParas);
                        jbResult.put("successFlag", false);
                        jbResult.put("message", errorMsg);
                        return jbResult;
                    }
                    //插入计划任务
                    for(int j=0;j<jaDetail.size();j++){
                        JSONObject jbMaterial=jaDetail.getJSONObject(j);
                        String partType=jbMaterial.getString("partType");
                        String partCode=jbMaterial.getString("partCode");
                        if(partType==null) partType="";
                        if(partCode==null) partCode="";
                        Map<String, Object> mapBigDataRow = new HashMap<>();
                        String task_id = CFuncUtilsSystem.CreateUUID(true);
                        mapBigDataRow.put("item_date", item_date);
                        mapBigDataRow.put("item_date_val", item_date_val);
                        mapBigDataRow.put("kit_task_id", task_id);
                        mapBigDataRow.put("task_date", taskDate);
                        mapBigDataRow.put("task_from", "ZK");
                        mapBigDataRow.put("task_num", taskNo);
                        mapBigDataRow.put("project_code", projectCode);
                        mapBigDataRow.put("project_name", projectName);
                        mapBigDataRow.put("structure_no", structureNo);
                        mapBigDataRow.put("structure_sn", structureSn);
                        mapBigDataRow.put("material_length", structureLength);
                        mapBigDataRow.put("m_texture", material);
                        mapBigDataRow.put("material_type", partType);
                        mapBigDataRow.put("material_code", partCode);
                        mapBigDataRow.put("use_flag", "N");
                        mapBigDataRow.put("kit_flag", kit_flag);
                        lstTaskDocuments.add(mapBigDataRow);
                    }
                }
                if(lstTaskDocuments.size()>0) mongoTemplate.insert(lstTaskDocuments, wmsTaskTable);
            }
            //创建返回信息
            responseParas = dcsShzyWmsInterfCommon.CreateZkResponse(200, "success");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //5.WMS接受中控定期查询库存信息
    public JSONObject RecvSelStockInfo(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "RecvSelStockInfo";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //查询库存数据
            JSONObject data=new JSONObject();
            JSONArray jaStockList=new JSONArray();
            String sqlStock="select " +
                    "a.stock_group_code,a.stock_code,a.lock_flag,a.position_x,a.position_y," +
                    "a.m_length,a.m_width,b.stock_index," +
                    "COALESCE(b.lot_num,'') project_code,COALESCE(b.serial_num,'') serial_num," +
                    "b.material_code,b.m_thickness," +
                    "TO_CHAR(b.stock_d_time, 'yyyy-MM-dd HH:mm:ss') stock_d_time," +
                    "COALESCE(b.kit_task_num,'') kit_task_num," +
                    "COALESCE(b.kit_structure_no,'') kit_structure_no," +
                    "COALESCE(b.kit_material_type,'') kit_material_type," +
                    "COALESCE(b.kit_flag,'') kit_flag " +
                    "from b_dcs_wms_map_me_stock a inner join b_dcs_wms_map_me_stock_d b " +
                    "on a.stock_id=b.stock_id " +
                    "order by a.stock_group_code,a.stock_code,b.stock_index";
            List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("ZK",sqlStock,
                    false,request,apiRoutePath);
            if(itemListStock!=null && itemListStock.size()>0){
                JSONObject jbStock=new JSONObject();
                JSONArray jaStockD=new JSONArray();
                String old_stock_code="";
                for(int i=0;i<itemListStock.size();i++){
                    Map<String, Object> mapItem=itemListStock.get(i);
                    String stock_group_code=mapItem.get("stock_group_code").toString();
                    String stock_code=mapItem.get("stock_code").toString();
                    String lock_flag=mapItem.get("lock_flag").toString();
                    Integer position_x=Integer.parseInt(mapItem.get("position_x").toString());
                    Integer position_y=Integer.parseInt(mapItem.get("position_y").toString());
                    Integer m_length=Integer.parseInt(mapItem.get("m_length").toString());
                    Integer m_width=Integer.parseInt(mapItem.get("m_width").toString());
                    Integer stock_index=Integer.parseInt(mapItem.get("stock_index").toString());
                    String project_code=mapItem.get("project_code").toString();
                    String material_code=mapItem.get("material_code").toString();
                    String serial_num=mapItem.get("serial_num").toString();
                    Integer m_thickness=Integer.parseInt(mapItem.get("m_thickness").toString());
                    String stock_d_time=mapItem.get("stock_d_time").toString();
                    String kit_structure_no=mapItem.get("kit_structure_no").toString();
                    String kit_material_type=mapItem.get("kit_material_type").toString();
                    String kit_flag=mapItem.get("kit_flag").toString();
                    if(!old_stock_code.equals(stock_code)){
                        old_stock_code=stock_code;
                        if(jaStockD.size()>0){
                            jbStock.put("parts",jaStockD);
                            jaStockList.add(jbStock);
                            jaStockD=new JSONArray();
                        }
                        String stackGroup="";
                        Integer stackType=1;
                        if(kit_flag.equals("Y")) stackGroup=project_code+"-"+kit_structure_no;
                        if(kit_material_type.equals("B")) stackType=2;
                        jbStock=new JSONObject();
                        jbStock.put("stockArea",stock_group_code.equals("A") ? "1":"2");
                        jbStock.put("stackId",stock_code);
                        jbStock.put("stackGroup",stackGroup);
                        jbStock.put("stackType",stackType);
                        jbStock.put("lockFlag",lock_flag.equals("Y") ? 1:0);
                        jbStock.put("stackLocationX",position_x);
                        jbStock.put("stackLocationY",position_y);
                        jbStock.put("stackWidth",m_width);
                        jbStock.put("stackLength",m_length);
                    }
                    JSONObject jbPart=new JSONObject();
                    jbPart.put("layer",stock_index);
                    jbPart.put("projectCode",project_code);
                    jbPart.put("plateCode",serial_num);
                    jbPart.put("partCode",material_code);
                    jbPart.put("instockDate",stock_d_time);
                    jbPart.put("length",m_length);//暂时待定
                    jbPart.put("width",m_width);//暂时待定
                    jbPart.put("thickness",m_thickness);
                    jaStockD.add(jbPart);
                    if(i==itemListStock.size()-1){
                        if(jaStockD.size()>0){
                            jbStock.put("parts",jaStockD);
                            jaStockList.add(jbStock);
                        }
                    }
                }
            }
            data.put("version",1);//暂时待定
            data.put("stockList",jaStockList);

            //创建返回信息
            JSONObject jbBack=new JSONObject();
            jbBack.put("code",200);
            jbBack.put("message","success");
            jbBack.put("data",data);
            responseParas=jbBack.toString();

            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = dcsShzyWmsInterfCommon.CreateResponse(500, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
