package com.api.pmc.core.mongo;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbMongoIndex;
import com.api.common.db.CFuncDbSqlResolve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 1.PMC标准MongoDB索引创建
 * 2.根据系统参数读取判断项目索引创建
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */

@Service
@Slf4j
public class PmcCoreMongoIndexFunc {
    @Autowired
    private CFuncDbMongoIndex cFuncDbMongoIndex;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //创建表索引
    @Async
    public void CreatePmcIndex(){
        String Pmc_MongoDbIndexList=cFuncDbSqlResolve.GetParameterValue("Pmc_MongoDbIndexList");
        if(Pmc_MongoDbIndexList!=null && !Pmc_MongoDbIndexList.equals("")){
            String[] lst=Pmc_MongoDbIndexList.split(",",-1);
            if(lst!=null && lst.length>0){
                for(String itemName : lst){
                    if(itemName.equals("PMC")){
                        CreatePmcCoreIndex();
                    }
                }
            }
        }
    }

    //标准表索引
    @Async
    public void CreatePmcCoreIndex(){
        CreatePmcMeQualityIndex();
        //总装 ZA
        CreatePmcMeShaftQualityIndex();
        CreatePmcMeFillQualityIndex();
        CreatePmcMeDkQualityIndex();
        CreatePmcMeSldwQualityIndex();
        CreatePmcMeFillDeviceStatusIndex();

        CreatePmcMeObdQualityIndex();
        CreatePmcMeWlccQualityIndex();
        CreatePmcMeSdQualityIndex();
        CreatePmcMeLzQualityIndex();
        CreatePmcMeDbQualityIndex();
        CreatePmcMeZdQualityIndex();
        CreatePmcMeDdQualityIndex();
        CreatePmcMeSjQualityIndex();
        CreatePmcMeChQualityIndex();
        CreatePmcMeZtQualityIndex();

        //AGV
        CreatePmcMeAgvStatusIndex();
        //检测线排放
        CreatePmcMePfQualityIndex();

        //焊装 HA
        CreatePmcMeHadkQualityIndex();

        //加注参数
        CreatePmcStationOilFillIndex();

    }

    //创建d_pmc_me_station_quality动态索引(标准质量数据)
    @Async
    public void CreatePmcMeQualityIndex(){
        String tableName="d_pmc_me_station_quality";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","station_flow_id",
                "work_center_code","prod_line_code","station_code","serial_num","tag_id","quality_for",
                "tag_des","tag_value","quality_d_sign","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality索引失败:"+ex.getMessage());
        }
    }

    //创建d_pmc_me_station_quality_shaft动态索引(拧紧结果)
    @Async
    public void CreatePmcMeShaftQualityIndex(){
        String tableName="d_pmc_me_station_quality_shaft";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","work_center_code",
                "prod_line_code","station_code","vin","device_code","shaft_status","parameter_set_id",
                "up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_shaft索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_shaft索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_shaft索引失败:"+ex.getMessage());
        }
    }

    //创建d_pmc_me_station_quality_fill动态索引(加注结果)
    @Async
    public void CreatePmcMeFillQualityIndex(){
        String tableName="d_pmc_me_station_quality_fill";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id",
                "vin","shebeibh","dh","zuoyesj","panding","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_fill索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_fill索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_fill索引失败:"+ex.getMessage());
        }
    }

    //创建d_pmc_me_station_quality_dk动态索引(打刻结果)
    @Async
    public void CreatePmcMeDkQualityIndex(){
        String tableName="d_pmc_me_station_quality_dk";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id",
                "vin","dk_type","dk_result","dk_time","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_dk索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_dk索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_dk索引失败:"+ex.getMessage());
        }
    }

    //创建d_pmc_me_station_quality_sldw动态索引(四轮定位结果)
    @Async
    public void CreatePmcMeSldwQualityIndex(){
        String tableName="d_pmc_me_station_quality_sldw";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id",
                "vin","device_code","car_model","test_time","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_sldw索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_sldw索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_sldw索引失败:"+ex.getMessage());
        }
    }

    //创建d_pmc_me_station_quality_obd动态索引(OBD设备检测)
    @Async
    public void CreatePmcMeObdQualityIndex(){
        String tableName="d_pmc_me_station_quality_obd";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","testresult","testtime",
                "connectresult","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_obd索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_obd索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_obd索引失败:"+ex.getMessage());
        }
    }
    //创建d_pmc_me_station_quality_wlcc动态索引(外廓尺寸测量设备)
    @Async
    public void CreatePmcMeWlccQualityIndex(){
        String tableName="d_pmc_me_station_quality_wlcc";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","wai_judge",
                "up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_wlcc索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_wlcc索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_wlcc索引失败:"+ex.getMessage());
        }
    }
    //创建d_pmc_me_station_quality_sd动态索引(速度校验)
    @Async
    public void CreatePmcMeSdQualityIndex(){
        String tableName="d_pmc_me_station_quality_sd";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","speed_judge",
                "up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_sd索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_sd索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_sd索引失败:"+ex.getMessage());
        }
    }
    //创建d_pmc_me_station_quality_lz动态索引(轮重-整备质量)
    @Async
    public void CreatePmcMeLzQualityIndex(){
        String tableName="d_pmc_me_station_quality_lz";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","aw_t_res",
                "up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_lz索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_lz索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_lz索引失败:"+ex.getMessage());
        }
    }
    //创建d_pmc_me_station_quality_db动态索引(地泵)
    @Async
    public void CreatePmcMeDbQualityIndex(){
        String tableName="d_pmc_me_station_quality_db";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","b_zbzl_judge",
                "up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_db索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_db索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_db索引失败:"+ex.getMessage());
        }
    }
    //创建d_pmc_me_station_quality_zd动态索引(制动)
    @Async
    public void CreatePmcMeZdQualityIndex(){
        String tableName="d_pmc_me_station_quality_zd";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","b_t_judge",
                "up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_zd索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_zd索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_zd索引失败:"+ex.getMessage());
        }
    }
    //创建d_pmc_me_station_quality_dd动态索引(大灯检测仪)
    @Async
    public void CreatePmcMeDdQualityIndex(){
        String tableName="d_pmc_me_station_quality_dd";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","lt_left_judge",
                "lt_right_judge","lt_judge","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_dd索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_dd索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_dd索引失败:"+ex.getMessage());
        }
    }
    //创建d_pmc_me_station_quality_sj动态索引(声级)
    @Async
    public void CreatePmcMeSjQualityIndex(){
        String tableName="d_pmc_me_station_quality_sj";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","noise_judge",
                "up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_sj索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_sj索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_sj索引失败:"+ex.getMessage());
        }
    }
    //创建d_pmc_me_station_quality_ch动态索引(侧滑)
    @Async
    public void CreatePmcMeChQualityIndex(){
        String tableName="d_pmc_me_station_quality_ch";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","sslip_judge",
                "up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_ch索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_ch索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_ch索引失败:"+ex.getMessage());
        }
    }
    //创建d_pmc_me_station_quality_zt动态索引(整体检测)
    @Async
    public void CreatePmcMeZtQualityIndex(){
        String tableName="d_pmc_me_station_quality_zt";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","test_judge",
                "up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_zt索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_zt索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_zt索引失败:"+ex.getMessage());
        }
    }

    //创建d_pmc_me_station_agv_status动态索引(AGV设备状态)
    @Async
    public void CreatePmcMeAgvStatusIndex(){
        String tableName="d_pmc_me_station_agv_status";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","agv_trace_id","station_code","serial_num",
                "make_order","agv_errorid","agv_status","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_agv_status索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_agv_status索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_agv_status索引失败:"+ex.getMessage());
        }
    }

    //创建d_pmc_me_station_quality_pf动态索引(检测线排放)
    @Async
    public void CreatePmcMePfQualityIndex(){
        String tableName="d_pmc_me_station_quality_pf";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","vin","obd_chk_date","dis_chk_date","obd_chk_time",
        "dis_chk_time","obd_result","dis_result","report_result","chk_section","vehicle_data","envir_data",
        "test_data","obd_data","result_data","device_data","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_pf索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_pf索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_pf索引失败:"+ex.getMessage());
        }
    }

    //焊装 HADK
    //创建d_pmc_me_station_quality_hadk动态索引
    @Async
    public void CreatePmcMeHadkQualityIndex(){
        String tableName="d_pmc_me_station_quality_hadk";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id",
                "order_prod","dk_type","dk_code","dk_time","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_quality_hadk索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_quality_hadk索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_hadk索引失败:"+ex.getMessage());
        }
    }

    //创建d_pmc_me_fill_device_status动态索引(加注设备状态)
    @Async
    public void CreatePmcMeFillDeviceStatusIndex(){
        String tableName="d_pmc_me_fill_device_status";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","fill_device_id","shebeibh","device_status"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_fill_device_status索引成功");
            else log.warn("创建Mongo表d_pmc_me_fill_device_status索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_quality_fill索引失败:"+ex.getMessage());
        }
    }

    //创建d_pmc_me_station_oil_filling动态索引(加注参数)
    @Async
    public void CreatePmcStationOilFillIndex(){
        String tableName="d_pmc_me_station_oil_filling";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","oil_filling_id","order_prod","vin","part_code1","part_name1","part_code2",
                "part_name2","facility_code","facility_name","program_code","program_name","coarse_vacuum","coarse_vacuum_time",
                "fine_vacuum","fine_vacuum_time","vacuum_leak_sensing","vacuum_leak_sensing_time","final_vacuum","final_vacuum_time",
                "filling_way","filling_quantity","filling_quantity_deviation","filling_quantity_under_deviation","filling_time","filling_pressure",
                "filling_pressure_deviation","filling_pressure_under_deviation","suction_pressure_back_time1","blow_back_suction_time",
                "suction_pressure_back_time2","reserved_field1","reserved_field2","reserved_field3","positive_pressure_inflation",
                "positive_pressure_leak_detection","positive_pressure_leak_detection_time","up_flag","up_code","up_msg"};
        int keep_days=0;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表d_pmc_me_station_oil_filling索引成功");
            else log.warn("创建Mongo表d_pmc_me_station_oil_filling索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表d_pmc_me_station_oil_filling索引失败:"+ex.getMessage());
        }
    }


}
