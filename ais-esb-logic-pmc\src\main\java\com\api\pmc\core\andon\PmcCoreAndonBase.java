package com.api.pmc.core.andon;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 安灯基础类
 * 1.安灯按钮事件新增
 * 2.安灯工位牌事件新增
 * </p>
 *
 * <AUTHOR>
 * @since 2021-4-7
 */
@Service
@Slf4j
public class PmcCoreAndonBase {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;

    //1.安灯按钮事件新增
    public void AndonBtnEventIns(HttpServletRequest request, String apiRoutePath,
                                 String btn_tag_id, String btn_tag_val) throws Exception {
        try {
            //默认值
            String userName = "andon";
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //1、获取安灯按钮信息
            String andon_btn_id = "";//安灯按钮ID
            String station_id = "";//工位ID
            String andon_type = "";//安灯类型(1物料/2设备/3质量/4工艺/5消音/6急停等)
            String andon_des = "";//安灯描述
            String andon_color = "";//安灯按钮颜色(不开启是无颜色;开启:1RED/2RED/3RED/4RED/5RED/6RED等)
            String btn_level = "";//优先级(从小到大，越大优先越高)
            String line_stop_tag_id = "";//产线停线TAG_ID(非必须)
            String line_stop_api = "";//产线停线API接口(非必须)
            String andon_music_id = "";//音响音乐ID
            String andon_limit_time = "";//响应超时时间(秒)
            String andon_btn_status = "";//当前按钮状态0/1(有触发需要进行更新实时状态)
            String sqlAndonBtn = "select COALESCE(andon_btn_id,0) andon_btn_id," +
                    "COALESCE(station_id,0) station_id, " +
                    "COALESCE(andon_type,'') andon_type, " +
                    "COALESCE(andon_des,'') andon_des, " +
                    "COALESCE(andon_color,'') andon_color, " +
                    "COALESCE(btn_level,0) btn_level, " +
                    "COALESCE(line_stop_tag_id,0) line_stop_tag_id, " +
                    "COALESCE(line_stop_api,'') line_stop_api, " +
                    "COALESCE(andon_music_id,0) andon_music_id, " +
                    "COALESCE(andon_limit_time,0) andon_limit_time, " +
                    "COALESCE(andon_btn_status,'') andon_btn_status " +
                    "from d_pmc_fmod_andon_btn " +
                    "where enable_flag='Y' " +
                    "and btn_tag_id=" + btn_tag_id +
                    " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListAndonBtn = cFuncDbSqlExecute.ExecSelectSql(userName, sqlAndonBtn, false, null, apiRoutePath);
            if (itemListAndonBtn != null && itemListAndonBtn.size() > 0) {
                andon_btn_id = itemListAndonBtn.get(0).get("andon_btn_id").toString();
                station_id = itemListAndonBtn.get(0).get("station_id").toString();
                andon_type = itemListAndonBtn.get(0).get("andon_type").toString();
                andon_des = itemListAndonBtn.get(0).get("andon_des").toString();
                andon_color = itemListAndonBtn.get(0).get("andon_color").toString();
                btn_level = itemListAndonBtn.get(0).get("btn_level").toString();
                line_stop_tag_id = itemListAndonBtn.get(0).get("line_stop_tag_id").toString();
                line_stop_api = itemListAndonBtn.get(0).get("line_stop_api").toString();
                andon_music_id = itemListAndonBtn.get(0).get("andon_music_id").toString();
                andon_limit_time = itemListAndonBtn.get(0).get("andon_limit_time").toString();
                andon_btn_status = itemListAndonBtn.get(0).get("andon_btn_status").toString();
                //2、修改 安灯按钮状态
                if (!andon_type.equals("5") || (andon_type.equals("5") && btn_tag_val.equals("1"))) {
                    String updAndonBtn = "update d_pmc_fmod_andon_btn set " +
                            "last_updated_by='" + userName + "'," +
                            "last_update_date='" + nowDateTime + "'," +
                            "andon_btn_status='" + btn_tag_val + "' " +
                            " where andon_btn_id=" + andon_btn_id;
                    cFuncDbSqlExecute.ExecUpdateSql(userName, updAndonBtn, true, request, apiRoutePath);
                }
                //消音
                if (!andon_type.equals("5")) {
                    String updAndonBtnXy = "update d_pmc_fmod_andon_btn set " +
                            "last_updated_by='" + userName + "'," +
                            "last_update_date='" + nowDateTime + "'," +
                            "andon_btn_status='0' " +
                            "where andon_btn_status='1' " +
                            "and andon_type='5' " +
                            "and station_id=" + station_id;
                    cFuncDbSqlExecute.ExecUpdateSql(userName, updAndonBtnXy, true, request, apiRoutePath);
                }
                //3、获取工位信息
                String prod_line_code = "";//产线编码
                String work_center_code = "";//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
                String station_code = "";//工位编码
                String station_des = "";//工位描述
                String line_section_code = "";//产线分段编码(来自快速编码)
                String sqlStation = "select COALESCE(b.prod_line_code,'') prod_line_code," +
                        "COALESCE(b.work_center_code,'') work_center_code," +
                        "COALESCE(a.station_code,'') station_code," +
                        "COALESCE(a.station_des,'') station_des, " +
                        "COALESCE(a.line_section_code,'') line_section_code " +
                        "from sys_fmod_station a," +
                        "     sys_fmod_prod_line b " +
                        "where a.prod_line_id=b.prod_line_id " +
                        "and a.enable_flag='Y' " +
                        "and b.enable_flag='Y' " +
                        "and a.station_id=" + station_id +
                        " LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
                if (itemListStation != null && itemListStation.size() > 0) {
                    prod_line_code = itemListStation.get(0).get("prod_line_code").toString();
                    work_center_code = itemListStation.get(0).get("work_center_code").toString();
                    station_code = itemListStation.get(0).get("station_code").toString();
                    station_des = itemListStation.get(0).get("station_des").toString();
                    line_section_code = itemListStation.get(0).get("line_section_code").toString();
                    //4、判断是否停/开线 (安灯类型(1物料/2设备/3质量/4工艺/5消音/6急停等))
                    if (andon_type.equals("6")) {
                        Integer andonEventCount = 0;//同一产线是否有为开工的工位
                        if (btn_tag_val.equals("0")) {
                            String sqlAndonEventCount = "select count(1) " +
                                    "from d_pmc_me_andon_event " +
                                    "where enable_flag='Y' " +
                                    "and andon_type='6' " +
                                    "and reset_flag='N' " +
                                    "and work_center_code='" + work_center_code + "' " +
                                    "and prod_line_code='" + prod_line_code + "' " +
                                    "and station_code<>'" + station_code + "' " +
                                    "and line_section_code='" + line_section_code + "' ";
                            andonEventCount = cFuncDbSqlResolve.GetSelectCount(sqlAndonEventCount);
                        }
                        //5、写入产线停线TAG_ID
                        if (line_stop_tag_id != null && !line_stop_tag_id.equals("")
                                && !line_stop_tag_id.equals("0") && andonEventCount == 0) {
                            //计算 批次
                            String sqlAndonBtnBatch = "select COALESCE(andon_event_batch_id,0) andon_event_batch_id " +
                                    "from d_pmc_me_andon_event_batch " +
                                    "where reset_flag='N' " +
                                    "and work_center_code='" + work_center_code + "' " +
                                    "and prod_line_code='" + prod_line_code + "' " +
                                    "and line_section_code='" + line_section_code + "' ";
                            List<Map<String, Object>> itemListAndonBtnBatch = cFuncDbSqlExecute.ExecSelectSql(userName, sqlAndonBtnBatch, false, null, apiRoutePath);
                            if (itemListAndonBtnBatch != null && itemListAndonBtnBatch.size() > 0) {
                                if (btn_tag_val.equals("0")) {
                                    String andon_event_batch_id = itemListAndonBtnBatch.get(0).get("andon_event_batch_id").toString();
                                    String updAndonEventBatch = "update d_pmc_me_andon_event_batch set " +
                                            "last_updated_by='" + userName + "'," +
                                            "last_update_date='" + nowDateTime + "'," +
                                            "happen_reset_date='" + nowDateTime + "'," +
                                            "reset_flag='Y', " +
                                            "up_flag='N' " +
                                            " where andon_event_batch_id=" + andon_event_batch_id;
                                    cFuncDbSqlExecute.ExecUpdateSql(userName, updAndonEventBatch, true, request, apiRoutePath);
                                }
                            } else if (btn_tag_val.equals("1")) {
                                String andon_event_batch = CFuncUtilsSystem.GetOnlySign(work_center_code + "-" + prod_line_code + "-" + line_section_code);//批次
                                long andon_event_batch_id = cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_andon_event_batch_id_seq", true);
                                String sqlAndonEventBatchIns = "insert into d_pmc_me_andon_event_batch " +
                                        "(created_by,creation_date,andon_event_batch_id," +
                                        "work_center_code,prod_line_code,line_section_code," +
                                        "andon_event_batch,happen_reset_date,reset_flag) values " +
                                        "('" + station_code + "','" + nowDateTime + "'," + andon_event_batch_id + ",'" +
                                        work_center_code + "','" + prod_line_code + "','" + line_section_code + "','" +
                                        andon_event_batch + "','" + nowDateTime + "','N')";
                                cFuncDbSqlExecute.ExecUpdateSql(userName, sqlAndonEventBatchIns, false, request, apiRoutePath);
                            }
                            //点位KEY
                            String client_code = "";
                            String tag_station_code = "";
                            String tag_group_code = "";
                            String tag_code = "";
                            String sqlTag = "select c.client_id,c.client_code,c.client_des," +
                                    "c.station_code," +
                                    "b.tag_group_id,b.tag_group_code,b.tag_group_des," +
                                    "a.tag_id,a.tag_code,a.tag_des " +
                                    "from scada_tag a,scada_tag_group b,scada_client c " +
                                    "where c.client_id = b.client_id " +
                                    "and b.tag_group_id=a.tag_group_id " +
                                    "and c.enable_flag='Y' " +
                                    "and b.enable_flag='Y' " +
                                    "and a.enable_flag='Y' " +
                                    "and a.tag_id= " + line_stop_tag_id;
                            List<Map<String, Object>> itemListTag = cFuncDbSqlExecute.ExecSelectSql(userName, sqlTag, false, request, apiRoutePath);
                            if (itemListTag != null && itemListTag.size() > 0) {
                                client_code = itemListTag.get(0).get("client_code").toString();
                                tag_station_code = itemListTag.get(0).get("station_code").toString();
                                tag_group_code = itemListTag.get(0).get("tag_group_code").toString();
                                tag_code = itemListTag.get(0).get("tag_code").toString();
                            }
                            String tag_list = client_code + "/" + tag_group_code + "/" + tag_code;
                            String tag_value_list = btn_tag_val;
                            log.info("写入工位【" + tag_station_code + "】,点位【" + tag_list + "】,值【" + tag_value_list + "】");
                            String errorMsg = cFuncUtilsCellScada.WriteTagByStation(userName, tag_station_code, tag_list, tag_value_list, true);
                            log.info("写入点位结果信息：" + errorMsg);
                        /*
                        if(!errorMsg.equals("")){
                            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                            return selectResult;
                        }*/
                        }
                        //6、写入产线停线TAG_ID
                        String status = "";
                        if (line_stop_api != null && !line_stop_api.equals("")) {
                            if (btn_tag_val.equals("1")) {
                                status = "stop";
                            } else if (btn_tag_val.equals("0")) {
                                status = "start";
                            }
                            line_stop_api = line_stop_api + "&status=" + status;

                            //调用接口
                            log.info("调用AGV停开线接口传入信息：" + line_stop_api);
                            try {
                                String stringReq = cFuncUtilsRest.GetUrlBackString(line_stop_api);
                                log.info("调用AGV停开线接口结果信息：" + stringReq);
                            } catch (Exception ex) {
                                log.info("调用AGV停开线接口异常：" + ex.getMessage());
                            }
                        }
                    }
                    //7、记录事件
                    String andonEventId = "";
                    String happen_date = nowDateTime;//安灯发生时间
                    String reset_date = "";//安灯复位时间
                    String reset_flag = "N";//是否复位
                    String cost_time = "0";//消耗时间(秒)
                    String over_time_flag = "N";//是否超出设定超时时间
                    String sqlAndonEvent = "select andon_event_id," +
                            "COALESCE(happen_date,'') happen_date " +
                            "from d_pmc_me_andon_event " +
                            "where enable_flag='Y' " +
                            "and reset_flag='N' " +
                            "and work_center_code='" + work_center_code + "' " +
                            "and prod_line_code='" + prod_line_code + "' " +
                            "and line_section_code='" + line_section_code + "' " +
                            "and station_code='" + station_code + "' " +
                            "and andon_type='" + andon_type + "' ";
                    List<Map<String, Object>> itemListAndonEvent = cFuncDbSqlExecute.ExecSelectSql(userName, sqlAndonEvent, false, request, apiRoutePath);
                    if (itemListAndonEvent != null && itemListAndonEvent.size() > 0) {
                        //安灯恢复
                        if (btn_tag_val.equals("0")) {
                            andonEventId = itemListAndonEvent.get(0).get("andon_event_id").toString();
                            happen_date = itemListAndonEvent.get(0).get("happen_date").toString();
                            if (happen_date == null || happen_date.equals("")) {
                                reset_date = nowDateTime;
                                cost_time = "0";
                            } else {
                                reset_date = nowDateTime;
                                SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                                long arriveTime = df.parse(happen_date).getTime();
                                long leaveTime = df.parse(nowDateTime).getTime();
                                long diffTime = (leaveTime - arriveTime) / 1000;
                                cost_time = String.valueOf(diffTime);
                            }
                            String updAndonEvent = "update d_pmc_me_andon_event set " +
                                    "last_updated_by='" + userName + "'," +
                                    "last_update_date='" + nowDateTime + "'," +
                                    "reset_date='" + reset_date + "'," +
                                    "cost_time='" + cost_time + "'," +
                                    "reset_flag='Y'," +
                                    "up_flag='N' " +
                                    " where andon_event_id=" + andonEventId;
                            cFuncDbSqlExecute.ExecUpdateSql(userName, updAndonEvent, true, request, apiRoutePath);
                        }
                    } else {
                        //安灯报警
                        if (btn_tag_val.equals("1")) {
                            long andon_event_id = cFuncDbSqlResolve.GetIncreaseID("d_pmc_me_andon_event_id_seq", true);
                            String sqlAndonEventIns = "insert into d_pmc_me_andon_event " +
                                    "(created_by,creation_date,andon_event_id," +
                                    "work_center_code,prod_line_code,station_code,station_des,line_section_code," +
                                    "andon_btn_id,andon_type,andon_des,andon_limit_time," +
                                    "happen_date,reset_date,reset_flag,cost_time," +
                                    "over_time_flag,enable_flag) values " +
                                    "('" + station_code + "','" + nowDateTime + "'," + andon_event_id + ",'" +
                                    work_center_code + "','" + prod_line_code + "','" + station_code + "','" + station_des + "','" + line_section_code + "'," +
                                    andon_btn_id + ",'" + andon_type + "','" + andon_des + "'," + andon_limit_time + ",'" +
                                    happen_date + "','" + reset_date + "','" + reset_flag + "'," + cost_time + ",'" +
                                    over_time_flag + "','Y')";
                            cFuncDbSqlExecute.ExecUpdateSql(userName, sqlAndonEventIns, false, request, apiRoutePath);
                            if (andon_type.equals("6")) {
                                try {
                                    String main_material_code = "";//物料编号
                                    String material_description = "";//物料描述
                                    String method = "/aisEsbOra/pmc/core/interf/PmcCoreMesNextMakeOrderSel";
                                    if (PmcCoreServer.EsbUrl == null || PmcCoreServer.EsbUrl.isEmpty()) {
                                        pmcCoreServerInit.ServerInit();
                                    }
                                    //获取当前订单
                                    JSONObject jsonObjectMoReq = new JSONObject();
                                    jsonObjectMoReq.put("order_prod", "");//下一个订单
                                    jsonObjectMoReq.put("workshop", work_center_code);
                                    jsonObjectMoReq.put("order_state", "1");//订单状态(1.已生成2.已发布3.已上线4.拉入5.下线6.拉出)
                                    JSONObject jsonObjectMoRes = cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl + method, jsonObjectMoReq);
                                    Integer moCode = jsonObjectMoRes.getInteger("code");
                                    if (moCode == 0) {
                                        JSONArray oraArry = jsonObjectMoRes.getJSONArray("data");
                                        if (oraArry != null && oraArry.size() > 0) {
                                            JSONObject oraJsonObject = oraArry.getJSONObject(0);
                                            main_material_code = oraJsonObject.getString("MATERIAL_CODE");//物料编码
                                            material_description = oraJsonObject.getString("MATERIAL_DESCRIPTION");//物料描述
                                        }
                                    }
                                    long andon_event_part_id = cFuncDbSqlResolve.GetIncreaseID("andon_event_part_id_seq", true);
                                    String sqlAndonEventPartIns = "insert into d_pmc_me_andon_event_part " +
                                            "(created_by,creation_date,andon_event_part_id,andon_event_id,andon_event_part_code,andon_event_part_name) values " +
                                            "('" + station_code + "','" + nowDateTime + "'," + andon_event_part_id + "," +
                                            andon_event_id + ",'" + main_material_code + "','" + material_description + "')";
                                    cFuncDbSqlExecute.ExecUpdateSql(userName, sqlAndonEventPartIns, false, request, apiRoutePath);
                                } catch (Exception ex) {
                                    log.info("插入停线时零件信息保存失败:" + ex.getMessage().toString());
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception ex) {
            throw new Exception("安灯按钮事件新增失败:" + ex.getMessage());
        }
    }

    //2.安灯工位牌事件新增
    public void AndonCardEventIns(HttpServletRequest request, String apiRoutePath,
                                  String card_tag_id, String card_tag_val) throws Exception {
        try {
            //默认值
            String userName = "andon";
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //1、获取安灯工位牌信息
            String andon_card_id = "";//安灯工位牌ID
            String sqlAndonCard = "select COALESCE(andon_card_id,0) andon_card_id " +
                    "from d_pmc_fmod_andon_card " +
                    "where enable_flag='Y' " +
                    "and andon_card_type='" + card_tag_val + "' " +
                    "and card_tag_id=" + card_tag_id +
                    " LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListAndonCart = cFuncDbSqlExecute.ExecSelectSql(userName, sqlAndonCard, false, request, apiRoutePath);
            if (itemListAndonCart != null && itemListAndonCart.size() > 0) {
                andon_card_id = itemListAndonCart.get(0).get("andon_card_id").toString();
                //2、修改 安灯状态
                String updAndonCard1 = "update d_pmc_fmod_andon_card set " +
                        "last_updated_by='" + userName + "'," +
                        "last_update_date='" + nowDateTime + "'," +
                        "andon_card_status='1' " +
                        " where andon_card_id=" + andon_card_id;
                cFuncDbSqlExecute.ExecUpdateSql(userName, updAndonCard1, false, request, apiRoutePath);
                //3、复位其他状态
                String updAndonCard2 = "update d_pmc_fmod_andon_card set " +
                        "last_updated_by='" + userName + "'," +
                        "last_update_date='" + nowDateTime + "'," +
                        "andon_card_status='0' " +
                        "where andon_card_id<>" + andon_card_id +
                        " and card_tag_id=" + card_tag_id;
                cFuncDbSqlExecute.ExecUpdateSql(userName, updAndonCard2, false, request, apiRoutePath);
            }
        } catch (Exception ex) {
            throw new Exception("安灯工位牌事件新增失败:" + ex.getMessage());
        }
    }

}
