package com.api.pack.core.sort;

import com.alibaba.fastjson.JSONObject;
import com.api.base.Const;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SortService extends ServiceImpl<SortMapper, Sort> implements IService<Sort>
{
    public JSONObject getSortSwitch()
    {
        JSONObject sortSwitch = new JSONObject();
        QueryWrapper<Sort> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq(SortConst.PROPERTY_SORT_FLAG, Const.FLAG_Y);
        queryWrapper.eq(Const.PROPERTY_ENABLE_FLAG, Const.FLAG_Y);
        queryWrapper.orderByAsc(SortConst.PROPERTY_SORT_INDEX);
        List<Sort> sorts = this.list(queryWrapper);
        for (Sort sort : sorts)
        {
            sortSwitch.put(sort.getSortCode(), sort.getSortValue());
        }
        return sortSwitch;
    }
}
