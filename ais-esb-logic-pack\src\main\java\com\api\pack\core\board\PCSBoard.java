package com.api.pack.core.board;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.domain.Example;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * <p>
 * PCS Board
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@ApiModel(value = "Board", description = "板件信息")
@Document("a_pack_me_bd")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class PCSBoard extends Board
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "板件朝向")
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Transient
    private String boardOrient;

    @ApiModelProperty(value = "板件ID")
    @JsonProperty("bd_id")
    @JSONField(name = "bd_id")
    @Field("bd_id")
    private String boardId;

    @ApiModelProperty(value = "板件索引")
    @JsonProperty("bd_index")
    @JSONField(name = "bd_index")
    @Field("bd_index")
    private Integer boardIndex;

    @ApiModelProperty(value = "板件条码")
    @JsonProperty("bd_barcode")
    @JSONField(name = "bd_barcode")
    @Field("bd_barcode")
    private String boardBarcode;

    @ApiModelProperty(value = "板件名称")
    @JsonProperty("bd_name")
    @JSONField(name = "bd_name")
    @Field("bd_name")
    private String boardName;

    @ApiModelProperty(value = "板件等级")
    @JsonProperty("bd_level")
    @JSONField(name = "bd_level")
    @Field("bd_level")
    private String boardLevel;

    @ApiModelProperty(value = "板件类别")
    @JsonProperty("bd_category")
    @JSONField(name = "bd_category")
    @Field("bd_category")
    private String boardCategory;

    @ApiModelProperty(value = "板件类型")
    @JsonProperty("bd_type")
    @JSONField(name = "bd_type")
    @Field("bd_type")
    private String boardType;

    @ApiModelProperty(value = "板件状态")
    @JsonProperty("bd_status")
    @JSONField(name = "bd_status")
    @Field("bd_status")
    private String boardStatus;

    @ApiModelProperty(value = "板件NG码")
    @JsonProperty("bd_ng_code")
    @JSONField(name = "bd_ng_code")
    @Field("bd_ng_code")
    private Integer boardNgCode;

    @ApiModelProperty(value = "板件NG信息")
    @JsonProperty("bd_ng_msg")
    @JSONField(name = "bd_ng_msg")
    @Field("bd_ng_msg")
    private String boardNgMsg;

    @ApiModelProperty(value = "板件标记")
    @JsonProperty("bd_mark")
    @JSONField(name = "bd_mark")
    @Field("bd_mark")
    private String boardMark;

    @ApiModelProperty(value = "板件方向")
    @JsonProperty("bd_direction")
    @JSONField(name = "bd_direction")
    @Field("bd_direction")
    private String boardDirection;

    @ApiModelProperty(value = "板件字符")
    @JsonProperty("bd_char")
    @JSONField(name = "bd_char")
    @Field("bd_char")
    private String boardChar;

    @ApiModelProperty(value = "XOUT标记")
    @JsonProperty("xout_flag")
    @JSONField(name = "xout_flag")
    @Field("xout_flag")
    private String xoutFlag;

    @ApiModelProperty(value = "父板件ID")
    @JsonProperty("array_id")
    @JSONField(name = "array_id")
    @Field("array_id")
    private String parentBoardId;

    @ApiModelProperty(value = "父板件名称")
    @JsonProperty("array_name")
    @JSONField(name = "array_name")
    @Field("array_name")
    private String parentBoardName;

    @ApiModelProperty(value = "父板件状态")
    @JsonProperty("array_status")
    @JSONField(name = "array_status")
    @Field("array_status")
    private String parentBoardStatus;

    @ApiModelProperty(value = "父板件条码")
    @JsonProperty("array_barcode")
    @JSONField(name = "array_barcode")
    @Field("array_barcode")
    private String parentBoardBarcode;

    @ApiModelProperty(value = "父板件类型")
    @JsonProperty("array_type")
    @JSONField(name = "array_type")
    @Field("array_type")
    private String parentBoardType;

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Transient
    private Board parentBoard;

    public PCSBoard(String orient, JSONObject bd)
    {
        // PCS BOARD INFO，{... PcsChar ...}
        /*
        {
            "PcsNum": "1",
            "PcsQRC": "12002024",
            "PcsQRCLevel": "A",
            "IsXout": "false",
            "DirMarkChkRtl": "@NC@",
            "Direction": "0",
            "UnRecognise": "false",
            "PcsChar": {
                "PlainCode": "@NC@",
                "DateCode": "2401",
                "LotNum": "@NC@",
                "PartNumber": "@NC@",
                "CProductName": "@NC@"
            }
         }
         */
        super(orient, BoardConst.PCS, bd);
        String boardBarcodeKey = BoardConst.PCS + BoardConst.QRC;
        String boardCharKey = BoardConst.PCS + BoardConst.CHAR;
        if (bd.containsKey(boardBarcodeKey) && bd.containsKey(boardCharKey))
        {
            this.setXoutFlag(bd.containsKey(BoardConst.IS_XOUT) ? (Boolean.FALSE.equals(Boolean.valueOf(bd.getString(BoardConst.IS_XOUT))) ? BoardConst.FLAG_N : BoardConst.FLAG_Y) : null);
        }
    }

    public static List<PCSBoard> listByParentBoardId(String parentBoardId, PCSBoardRepository pcsBoardRepository)
    {
        if (StringUtils.isEmpty(parentBoardId))
        {
            return null;
        }
        PCSBoard params = new PCSBoard();
        params.setParentBoardId(parentBoardId);
        return pcsBoardRepository.findAll(Example.of(params));
    }

    public void setParentBoard(Board parentBoard)
    {
        this.parentBoard = parentBoard;
        if (parentBoard != null)
        {
            this.setParentBoardId(parentBoard.getBoardId());
            this.setParentBoardName(parentBoard.getBoardName());
            this.setParentBoardStatus(parentBoard.getBoardStatus());
            this.setParentBoardBarcode(parentBoard.getBoardBarcode());
            this.setParentBoardType(parentBoard.getBoardType());
        }
    }
}
