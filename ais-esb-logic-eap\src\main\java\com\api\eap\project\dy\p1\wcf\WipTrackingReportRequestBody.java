
package com.api.eap.project.dy.p1.wcf;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;


/**
 * <p>wipTrackingReportRequestBody complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="wipTrackingReportRequestBody"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="report_dt" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="keep_reason" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="lot_id" type="{http://www.w3.org/2001/XMLSchema}string" minOccurs="0"/&gt;
 *         &lt;element name="edc_infos" type="{http://tempuri.org/}edcInfos" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "wipTrackingReportRequestBody", propOrder = {
    "reportDt",
    "keepReason",
    "lotId",
    "edcInfos"
})
public class WipTrackingReportRequestBody {

    @XmlElement(name = "report_dt", required = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar reportDt;
    @XmlElement(name = "keep_reason")
    protected String keepReason;
    @XmlElement(name = "lot_id")
    protected String lotId;
    @XmlElement(name = "edc_infos")
    protected EdcInfos edcInfos;

    /**
     * 获取reportDt属性的值。
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getReportDt() {
        return reportDt;
    }

    /**
     * 设置reportDt属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setReportDt(XMLGregorianCalendar value) {
        this.reportDt = value;
    }

    /**
     * 获取keepReason属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getKeepReason() {
        return keepReason;
    }

    /**
     * 设置keepReason属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setKeepReason(String value) {
        this.keepReason = value;
    }

    /**
     * 获取lotId属性的值。
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getLotId() {
        return lotId;
    }

    /**
     * 设置lotId属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLotId(String value) {
        this.lotId = value;
    }

    /**
     * 获取edcInfos属性的值。
     * 
     * @return
     *     possible object is
     *     {@link EdcInfos }
     *     
     */
    public EdcInfos getEdcInfos() {
        return edcInfos;
    }

    /**
     * 设置edcInfos属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link EdcInfos }
     *     
     */
    public void setEdcInfos(EdcInfos value) {
        this.edcInfos = value;
    }

}
