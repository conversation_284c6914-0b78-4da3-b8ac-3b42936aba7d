package com.api.pack.core.op;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Array逻辑处理
 * 1.线扫判断与存储
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@RestController
@Slf4j
@RequestMapping("/pack/core/op")
public class PackCoreOpArrayController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PackCoreOpArrayFunc packCoreOpArrayFunc;

    //1.线扫判断与存储
    @RequestMapping(value = "/PackCoreOpArraySave", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String PackCoreOpArraySave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pack/core/op/PackCoreOpArraySave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_pack_aps_plan";
        String meArrayTable = "a_pack_me_array";
        String meBdTable = "a_pack_me_bd";
        String result = "";
        try {
            //计时开始
            long l1 = System.currentTimeMillis();

            String user_name = "";
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            JSONObject ccd_data = jsonParas.getJSONObject("ccd_data");

            //1.获取当前WORK订单信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                errorMsg = "当前未下发任务,无法执行线扫判断,请先选择任务进行下发";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            Document docItemBigData = iteratorBigData.next();
            JSONObject jbPlan = new JSONObject();
            String plan_id = docItemBigData.getString("plan_id");
            Integer finish_ok_count = docItemBigData.getInteger("finish_ok_count");
            Integer finish_ng_count = docItemBigData.getInteger("finish_ng_count");
            String cyclePeriod = String.valueOf(docItemBigData.get("cycle_period"));
            String modelVersion = String.valueOf(docItemBigData.get("model_version"));
            jbPlan.put("plan_id", plan_id);
            jbPlan.put("lot_num", docItemBigData.getString("lot_num"));
            jbPlan.put("task_type", docItemBigData.getString("task_type"));
            jbPlan.put("model_type", docItemBigData.getString("model_type"));
            jbPlan.put("model_version", modelVersion);
            jbPlan.put("array_type", docItemBigData.getString("array_type"));
            jbPlan.put("bd_type", docItemBigData.getString("bd_type"));
            jbPlan.put("m_length", docItemBigData.getDouble("m_length"));
            jbPlan.put("m_width", docItemBigData.getDouble("m_width"));
            jbPlan.put("m_tickness", docItemBigData.getDouble("m_tickness"));
            jbPlan.put("m_weight", docItemBigData.getDouble("m_weight"));
            jbPlan.put("cycle_period", cyclePeriod);
            jbPlan.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
            jbPlan.put("finish_ok_count", finish_ok_count);
            jbPlan.put("finish_ng_count", finish_ng_count);
            String recipe_paras = docItemBigData.getString("recipe_paras");
            iteratorBigData.close();

            //2.获取当前用户信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //3.获取配方信息
            JSONObject jbRecipe = JSONObject.parseObject(recipe_paras);

            //4.查询当前分选条件
            String sqlSort = "select " +
                    "sort_code,sort_name," +
                    "COALESCE(sort_value,'') sort_value " +
                    "from a_pack_fmod_sort " +
                    "where enable_flag='Y' and sort_flag='Y' order by sort_index";
            List<Map<String, Object>> itemListSort = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlSort,
                    false, request, apiRoutePath);
            JSONObject jbSort = new JSONObject();
            if (itemListSort != null && itemListSort.size() > 0) {
                for (Map<String, Object> mapItem : itemListSort) {
                    String sort_code = mapItem.get("sort_code").toString();
                    String sort_value = mapItem.get("sort_value").toString();
                    jbSort.put(sort_code, sort_value);
                }
            }

            //5.解析获取原始数据
            JSONObject jbCcdResolveResult = packCoreOpArrayFunc.ResolveCcdResult(user_name, jbPlan, jbRecipe, jbSort, ccd_data);
            Map<String, Object> mapRowArray = (Map<String, Object>) jbCcdResolveResult.get("array");
            List<Map<String, Object>> bdRowsList = (List<Map<String, Object>>) jbCcdResolveResult.get("bd");
            mongoTemplate.insert(mapRowArray, meArrayTable);
            if (bdRowsList != null && bdRowsList.size() > 0) mongoTemplate.insert(bdRowsList, meBdTable);

            //6.更新订单完工数量
            String array_status = mapRowArray.get("array_status").toString();
            if (array_status.equals("OK")) finish_ok_count = finish_ok_count + 1;
            else finish_ng_count = finish_ng_count + 1;
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            Update updateBigData = new Update();
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //计时结束
            long l2 = System.currentTimeMillis();
            long cost_time = l2 - l1;

            //7.返回参数
            JSONObject jbResult = new JSONObject();
            jbResult.put("array_id", mapRowArray.get("array_id").toString());
            jbResult.put("array_barcode", mapRowArray.get("array_barcode").toString());
            jbResult.put("board_sn", mapRowArray.get("board_sn").toString());
            jbResult.put("deposit_position", Integer.parseInt(mapRowArray.get("deposit_position").toString()));
            jbResult.put("board_result", Integer.parseInt(mapRowArray.get("board_result").toString()));
            jbResult.put("board_turn", Integer.parseInt(mapRowArray.get("board_turn").toString()));
            jbResult.put("cost_time", String.valueOf(cost_time) + "ms");
            jbResult.put("xout_act_num", Integer.parseInt(mapRowArray.get("xout_act_num").toString()));
            jbResult.put("array_index", Integer.parseInt(mapRowArray.get("array_index").toString()));
            jbResult.put("array_level", mapRowArray.get("array_level").toString());
            jbResult.put("finish_ok_count", finish_ok_count);
            jbResult.put("finish_ng_count", finish_ng_count);
            jbResult.put("array_ng_msg", mapRowArray.get("array_ng_msg").toString());
            result = jbResult.toString();

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "线扫判断与存储发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.删除Set数据
    @RequestMapping(value = "/PackCoreOpDeleteSetBySetSN", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String PackCoreOpDeleteSetBySetSN(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pack/core/op/PackCoreOpDeleteSetBySetSN";
        String transResult = "";
        String errorMsg = "";
        String meArrayTable = "a_pack_me_array";
        String meDbTable = "a_pack_me_bd";
        String result = "";
        try {
            //计时开始
            String setSNClr = jsonParas.getString("SetSNClr");
            String str = setSNClr;
            while (str.length() >= 14) {
                String setSn = str.substring(0, 14);
                str = str.substring(14);
                //修改array有效标识为N
                String array_id="";
                Query query = new Query();
                query.addCriteria(Criteria.where("board_sn").is(setSn));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meArrayTable).find(query.getQueryObject()).
                        sort(query.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if(iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    array_id=docItemBigData.getString("array_id");
                    iteratorBigData.close();
                }
                // 1. 数据禁用：包装Set记录[禁用] -> a_pack_me_array（包装SET记录表：主要enable_flag=N）
                Update updateBigData = new Update();
                updateBigData.set("enable_flag", "N");
                mongoTemplate.updateMulti(query, updateBigData, meArrayTable);
                // 2. 数据禁用：包装Pcs记录[禁用] a_pack_me_bd（包装Pcs记录表：主要enable_flag=N）
                query = new Query();
                query.addCriteria(Criteria.where("array_id").is(array_id));
                mongoTemplate.updateMulti(query, updateBigData, meDbTable);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "线扫判断与存储发生异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
