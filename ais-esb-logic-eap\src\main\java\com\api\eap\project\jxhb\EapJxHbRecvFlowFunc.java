package com.api.eap.project.jxhb;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * (江西红森)EAP接受流程功能函数
 * 1.配方下发
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@Service
@Slf4j
public class EapJxHbRecvFlowFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapJxHbInterfCommon eapJxHbInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.配方下发
    public JSONObject EipEapApiLoadRecipe(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "EipEapApiLoadRecipe";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 500;
        Long station_id = 0L;
        String station_code = "";
        String station_attr = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.解析接受参数
            station_code = jsonParas.getString("EQ_CODE") == null ? "" : jsonParas.getString("EQ_CODE");
            String Recipe_Name=jsonParas.getString("Recipe_Name") == null ? "" : jsonParas.getString("Recipe_Name");
            String Board_Width=jsonParas.getString("Board_Width") == null ? "" : jsonParas.getString("Board_Width");
            String Plate_Thick=jsonParas.getString("Plate_Thick") == null ? "" : jsonParas.getString("Plate_Thick");
            String Expect_CompletBoard_Num=jsonParas.getString("Expect_CompletBoard_Num") == null ? "" :
                    jsonParas.getString("Expect_CompletBoard_Num");
            String CompletBoard_Num=jsonParas.getString("CompletBoard_Num") == null ? "" : jsonParas.getString("CompletBoard_Num");
            String PutBoard_Time=jsonParas.getString("PutBoard_Time") == null ? "" : jsonParas.getString("PutBoard_Time");
            //1.数据转换判断
            Integer recipeCode=Integer.parseInt(Recipe_Name);
            BigDecimal bigDecimal = new BigDecimal(Board_Width);
            Integer panelWidth = bigDecimal.intValue();
            bigDecimal = new BigDecimal(Plate_Thick);
            Integer panelThick=bigDecimal.intValue();
            bigDecimal = new BigDecimal(Expect_CompletBoard_Num);
            Integer planCount=bigDecimal.intValue();
            bigDecimal= new BigDecimal(CompletBoard_Num);
            Integer finishCount=bigDecimal.intValue();
            bigDecimal = new BigDecimal(PutBoard_Time);
            Integer planTimes=bigDecimal.intValue();
            if(recipeCode<=0){
                errorMsg = "EAP下发工位号{"+station_code+"},配方代码不能<=0";
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(panelWidth<=0){
                errorMsg = "EAP下发工位号{"+station_code+"},板宽不能<=0";
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(panelThick<=0){
                errorMsg = "EAP下发工位号{"+station_code+"},板厚不能<=0";
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(planCount<=0){
                errorMsg = "EAP下发工位号{"+station_code+"},计划数量不能<=0";
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(finishCount<=0){
                errorMsg = "EAP下发工位号{"+station_code+"},完板片数预计不能<=0";
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(planTimes<=0){
                errorMsg = "EAP下发工位号{"+station_code+"},放板时间不能<=0";
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //2.查询工位信息是否匹配
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + station_code + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发配方任务工位号{" + station_code + "}在AIS系统中未配置";
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            station_attr = itemListStation.get(0).get("station_attr").toString();
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String recipeCodeTag="";
            String panelWidthTag="";
            String panelThickTag="";
            String planCountTag="";
            String finishCountTag="";
            String planTimesTag="";
            String downRecipeReqTag="";
            String downRecipeRespTag="";
            String clientCodeSub="Plc";
            if (aisMonitorModel.equals("AIS-PC")) {
                recipeCodeTag = station_attr + clientCodeSub + "/DownRecipe/Recipe_Name";
                panelWidthTag= station_attr + clientCodeSub + "/DownRecipe/Board_Width";
                panelThickTag= station_attr + clientCodeSub + "/DownRecipe/Plate_Thick";
                planCountTag= station_attr + clientCodeSub + "/DownRecipe/Expect_CompletBoard_Num";
                finishCountTag= station_attr + clientCodeSub + "/DownRecipe/CompletBoard_Num";
                planTimesTag= station_attr + clientCodeSub + "/DownRecipe/PutBoard_Time";
                downRecipeReqTag= station_attr + clientCodeSub + "/DownRecipe/DownRecipeReq";
                downRecipeRespTag= station_attr + clientCodeSub + "/DownRecipe/DownRecipeResp";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                recipeCodeTag = station_attr + clientCodeSub+ "_" + station_code + "/DownRecipe/Recipe_Name";
                panelWidthTag= station_attr + clientCodeSub+ "_" + station_code + "/DownRecipe/Board_Width";
                panelThickTag= station_attr + clientCodeSub+ "_" + station_code + "/DownRecipe/Plate_Thick";
                planCountTag= station_attr + clientCodeSub+ "_" + station_code + "/DownRecipe/Expect_CompletBoard_Num";
                finishCountTag= station_attr + clientCodeSub+ "_" + station_code + "/DownRecipe/CompletBoard_Num";
                planTimesTag= station_attr + clientCodeSub+ "_" + station_code + "/DownRecipe/PutBoard_Time";
                downRecipeReqTag= station_attr + clientCodeSub+ "_" + station_code + "/DownRecipe/DownRecipeReq";
                downRecipeRespTag= station_attr + clientCodeSub+ "_" + station_code + "/DownRecipe/DownRecipeResp";
            }
            //3.1先清空自身交互点位
            errorMsg= cFuncUtilsCellScada.WriteTagByStation("EAP",station_code,downRecipeReqTag,"0",true);
            if(!errorMsg.equals("")){
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //3.2 批量写入到PLC
            String tag_list=recipeCodeTag+","+panelWidthTag+","+panelThickTag+","+
                    planCountTag+","+finishCountTag+","+planTimesTag+","+downRecipeReqTag;
            String tag_value_list=recipeCode+"&"+panelWidth+"&"+panelThick+"&"+
                    planCount+"&"+finishCount+"&"+planTimes+"&1";
            errorMsg= cFuncUtilsCellScada.WriteTagByStation("EAP",station_code,tag_list,tag_value_list,true);
            if(!errorMsg.equals("")){
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //3.2 超时等待PLC返回结果
            String respValue="";
            for(int i=0;i<10;i++){
                respValue=opCommonFunc.ReadCellOneRedisValueByTag(station_code,downRecipeRespTag);
                if(!respValue.equals("") && !respValue.equals("0")) break;
                Thread.sleep(500);
            }
            //处理复位
            cFuncUtilsCellScada.WriteTagByStation("EAP",station_code,downRecipeReqTag,"0",false);
            if(respValue.equals("") || respValue.equals("0")){
                errorMsg="下发配方到PLC,PLC超时5秒响应,写入失败";
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if(!respValue.equals("1")){
                errorMsg="机台故障,此时写入配方任务失败";
                if(respValue.equals("2")) errorMsg="设备内有板件，不允许下发配方";
                else if(respValue.equals("3")) errorMsg="设备内无定义此配方号";
                responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            responseParas = eapJxHbInterfCommon.CreateResponseHead01(true, "下发配方任务成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = eapJxHbInterfCommon.CreateResponseHead01(false, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
