package com.api.eap.core.interf.ais1;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <p>
 * 投收扳机标准发送事件接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@RestController
@Slf4j
@RequestMapping("/eap/core/interf/send")
public class Eap1CoreSendEventController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private Eap1CoreSendEventFunc eap1CoreSendEventFunc;

    //1.[接口]AIS上报设备状态(EAP发布)
    @RequestMapping(value = "/UpDeviceStatusEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String UpDeviceStatusEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/UpDeviceStatusEvent";
        String transResult = "";
        String errorMsg = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String esbInterfCode = "UpDeviceStatusEvent";
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            String station_code = jsonParas.getString("station_code");
            String device_status = jsonParas.getString("device_status");//设备状态(IDLE空闲，BUSY 忙碌，ALARM 故障，STOP 暂停，KEEP保养、RUN运行)
            String control_model = jsonParas.getString("control_model");//设备模式(ON 自动,OFF 手动)
            String cim_model = jsonParas.getString("cim_model");//作业模式(LOCAL本地、REMOTE远程联机)
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("device_status", device_status);
            postParas.put("control_model", control_model);
            postParas.put("cim_model", cim_model);
            eap1CoreSendEventFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS完板上报事件异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]AIS上报设备报警信息(EAP发布)
    @RequestMapping(value = "/UpDeviceAlarmEvent", method = {RequestMethod.POST, RequestMethod.GET})
    public String UpDeviceAlarmEvent(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/UpDeviceAlarmEvent";
        String transResult = "";
        String errorMsg = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String esbInterfCode = "UpDeviceStatusEvent";
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            String station_code = jsonParas.getString("station_code");
            String alarm_code = jsonParas.getString("alarm_code");
            String alarm_msg = jsonParas.getString("alarm_msg");
            String alarm_level = jsonParas.getString("alarm_level");
            String alarm_time = jsonParas.getString("alarm_time");
            String alarm_value = jsonParas.getString("alarm_value");
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            JSONArray alarm_list = new JSONArray();
            JSONObject alarm = new JSONObject();
            alarm.put("alarm_code", alarm_code);
            alarm.put("alarm_msg", alarm_msg);
            alarm.put("alarm_level", alarm_level);
            alarm.put("alarm_time", alarm_time);
            alarm.put("alarm_value", alarm_value);
            alarm_list.add(alarm);
            postParas.put("alarm_list", alarm_list);

            eap1CoreSendEventFunc.EapCoreCommonEvent(esbInterfCode, postParas);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS完板上报事件异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.[接口]EAP通讯状态测试(EAP发布)
    @RequestMapping(value = "/EapConnTest", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapConnTest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/interf/send/EapConnTest";
        String transResult = "";
        String errorMsg = "";
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String esbInterfCode = "EapConnTest";
            Long station_id = jsonParas.getLong("station_id");
            //获取当前登入者
            String user_name = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection("a_eap_me_station_user").find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            String station_code = jsonParas.getString("station_code");
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(false));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("sys_name", "AIS");
            postParas.put("user_id", user_name);
            postParas.put("station_code", station_code);
            postParas.put("token", CFuncUtilsSystem.GetOnlySign(""));
            JSONObject response = JSONObject.parseObject(eap1CoreSendEventFunc.EapCoreCommonEvent(esbInterfCode, postParas));
            String server_time = response.getString("server_time");
            if (server_time != null && !server_time.equals("")) {
                //判断当前时间与EAP时间是否相差10s以上,若相差10s以上则修改
                long TIME_TOLERANCE = 10000;
                Date eap_date = CFuncUtilsSystem.GetMongoISODate(server_time);
                long currentTime = new Date().getTime();
                if (Math.abs(currentTime - eap_date.getTime()) >= TIME_TOLERANCE) {
                    //更改本地时间,先取消对时功能
                    String[] lst = server_time.split(" ", -1);
                    String dataStr_ = lst[0];
                    String timeStr_ = lst[1];
                    String cmd = " cmd /c date " + dataStr_;
                    Runtime.getRuntime().exec(cmd);
                    cmd = " cmd /c time " + timeStr_;
                    Runtime.getRuntime().exec(cmd);
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "AIS完板上报事件异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
