package com.api.eap.project.dayuan;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * EapDyPotionsRecordInterfController
 * 药水添加记录
 * <AUTHOR>
 * @date 2023-03-09 10:22
 */
@Slf4j
@RestController
@RequestMapping("/eap/project/dayuan/interf")
public class EapDyPotionsRecordInterfController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //记录新增
    @Transactional
    @RequestMapping(value = "/EapDyPotionsRecordIns", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyPotionsRecordIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/dayuan/EapDyPotionsRecordIns";
        String tranResult="";
        String errorMsg="";
        try{
            String dosage = jsonParas.getString("dosage");
            String potions_name = jsonParas.getString("potions_name");
            String unit = jsonParas.getString("unit");
            String nowDateTime= CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            long potions_record_id=cFuncDbSqlResolve.GetIncreaseID("a_eap_me_potions_record_id_seq",false);
            String insertSql="insert into a_eap_me_potions_record(created_by,creation_date,potions_record_id,potions_name,dosage,unit,add_time,added_by) " +
                    "values('admin','"+nowDateTime+"',"+potions_record_id+",'"+potions_name+"','"+dosage+"'," +
                    "'"+unit+"','"+nowDateTime+"','admin')";
            cFuncDbSqlExecute.ExecUpdateSql("admin", insertSql, true, request,apiRoutePath);
            tranResult= CFuncUtilsLayUiResut.GetStandJson(true,null,String.valueOf(potions_record_id),"",0);
        }
        catch (Exception ex){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            errorMsg= "插入药水记录数据发生异常"+ex.getMessage();
            log.error(errorMsg);
            tranResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return tranResult;
    }
}
