package com.api.eap.project.zhcy;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.project.zhcy.utils.SystemTimeUpdater;
import com.mongodb.client.MongoCursor;

import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 志圣-珠海超毅-EAP接收接口功能实现
 * 1.初始化状态请求
 * 2.时间同步
 * 3.询问设备关键参数
 * 4.任务信息下发
 * 5.通知设备任务更改
 * </p>
 * <AUTHOR>
 */
@Service
@Slf4j
public class EapZhcyRecvInterfFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapZhcyInterfCommon eapZhcyInterfCommon;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private EapZhcySendBusinessFunc eapZhcySendBusinessFunc;
    /**
     * 处理所有功能
     * @param jsonParas
     * @param funcName
     * @param request
     * @param apiRoutePath
     * @return
     */
    public JSONObject processFunc(JSONObject jsonParas, String funcName, HttpServletRequest request,String apiRoutePath) {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = funcName;
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String code="0000";
        JSONObject jbResponse = null;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("requestParas", requestParas);
            //根据功能名调用不同方法
            switch (funcName) {
                // 初始化状态请求 - 设备启动时向EAP请求初始状态数据
                case "EAP_InitialDataRequest":
                    jbResponse = processEAPInitialDataRequest(jsonParas,request,apiRoutePath,esbInterfCode);
                    break;
                // 时间同步 - EAP时间下发
                case "EAP_DateTimeSyncCommand":
                    jbResponse = processEAPDateTimeSyncCommand(jsonParas,request,apiRoutePath,esbInterfCode);
                    break;
                // 任务信息下发 - 向设备下发生产任务信息
                case "EAP_JobDataDownload":
                    jbResponse = processEAPJobDataDownload(jsonParas,request,apiRoutePath,esbInterfCode);
                    break;
                // 通知设备任务更改 - 告知设备当前任务有变更
                case "EAP_JobDataModifyCommand":
                    jbResponse = processEAPJobDataModifyCommand(jsonParas,request,apiRoutePath,esbInterfCode);
                    break;
                // 远程提示信息下达 - 通知设备显示提示信息，开启蜂鸣器
                case "EAP_CIMMessageCommand":
                    jbResponse = processEAPCIMMessageCommand(jsonParas,request,apiRoutePath,esbInterfCode);
                    break;
                // 膜材料校验 - 检查扫描的膜材料是否与当前任务匹配
                case "EAP_FilmMaterialValidation":
                    jbResponse = processEAPFilmMaterialValidation(jsonParas,request,apiRoutePath,esbInterfCode);
                    break;
                default:
                    errorMsg = "不支持的接口功能：" + funcName;
                    jbResponse = eapZhcyInterfCommon.CreateFaillResult(errorMsg,jsonParas.getString("RequestId"),funcName,requestParas.toString());
            }
            jbResult.put("responseParas", jbResponse.toString());
            jbResult.put("Success", jbResponse.getBoolean("Success"));
            jbResult.put("Msg", jbResponse.getString("Msg"));
            String responseCode = jbResponse.getString("Code");
            if(StringUtils.hasText(responseCode)) {
               code = responseCode;
            }
            jbResult.put("Code", code);
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResponse = eapZhcyInterfCommon.CreateFaillResult(errorMsg,jsonParas.getString("RequestId"),funcName,requestParas.toString());
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
        }
        return jbResult;
    }

    /***
     * 初始化状态请求
     * @param jsonParas
     * @param request
     * @param apiRoutePath
     * @return
     * @throws Exception
     */
    private JSONObject processEAPInitialDataRequest(JSONObject jsonParas,HttpServletRequest request,String apiRoutePath,String esbInterfCode) throws Exception {
    	//From 调用接口来源（EAP）
    	String From  = jsonParas.getString("From");
    	//RequestId 请求 ID（17位唯一标识符：yyyyMMddHHmmssfff）（Format:毫秒时间格式）、
    	String requestId  = jsonParas.getString("RequestId");
    	//Message 消息（接口名）、
    	//String Message  = jsonParas.getString("Message");
    	//String DateTime  = jsonParas.getString("DateTime");
        JSONObject content = jsonParas.getJSONObject("Content");
        String station_code = content.getString("EqpId");
        String errorMsg="";
        String sqlStation = "select station_id  from sys_fmod_station where station_code='" + station_code + "'";
        List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, request, apiRoutePath);
        if (lstStation == null || lstStation.size() <= 0) {
        	errorMsg = "设备编号{" + station_code + "}不存在系统中";
        	log.warn(errorMsg+",jsonParas:"+jsonParas.toString());
        	return EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId,esbInterfCode,jsonParas.toString());
        }
        //从redis中取 EqpId、ControlMode、EqpStatus、JobId 数据。
        String controlMode = "";
        String eqpStatus = "";
        String jobId = "";
        //通过工站code，查询PLC结尾的client_code。
        String sqlClientCode = "SELECT client_code from  scada_client where station_code='" + station_code + "' and client_code like '%Plc' and enable_flag='Y'";
        List<Map<String, Object>> lstsqlClientCode = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlClientCode, false, request, apiRoutePath);
        String client_code = lstsqlClientCode.get(0).get("client_code").toString();

        //这个地方 获取全部设备的数据
        //String tagList="Tm1Plc/PlcStatus/DeviceMode,Tm1Plc/PlcStatus/DeviceStatus";
        String tagList=client_code+"/PlcStatus/DeviceMode,"+client_code+"/PlcStatus/DeviceStatus";
        JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, tagList);
        if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
            //遍历判断key等于：TmPlc/PlcStatus/DeviceMode和TmPlc/PlcStatus/DeviceStatus
            for (int i = 0; i < jsonArrayTag.size(); i++) {
                JSONObject jbItem = jsonArrayTag.getJSONObject(i);
                String tag_key = jbItem.getString("tag_key");
                String tag_value = jbItem.getString("tag_value");
                if (tag_key.equals(client_code+"/PlcStatus/DeviceMode")) {
                    if ("".equals(tag_value)) {
                        errorMsg = "查询贴膜机控制模式时贴膜机PLC断网";
                        return EapZhcyInterfCommon.CreateFaillResult(errorMsg,requestId,esbInterfCode,jsonParas.toString(),"");
                    }
                    //模式0:离线; 1:在线/远程;2:在线/本地
                    if ("1".equals(tag_value)) {
                    	 controlMode = "Remote";
                    }else if("2".equals(tag_value)){
                        controlMode = "Local";
                    }
                }else if (tag_key.equals(client_code+"/PlcStatus/DeviceStatus")) {
                    if ("".equals(tag_value)) {
                        errorMsg = "查询贴膜机设备状态时贴膜机PLC断网";
                        return EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId,esbInterfCode,jsonParas.toString(),"");
                    }
                    //1初始化/准备 2待机 3报警/故障 4停止 5运行 6PM/保养 7暂停
                    if ("1".equals(tag_value)) {//准备
                        eqpStatus = "Ready";
                    }else if ("2".equals(tag_value)) {//待机
                        eqpStatus = "Idle";
                    }else if ("3".equals(tag_value)) {//报警/故障
                        eqpStatus = "Down";
                    }else if ("4".equals(tag_value)) {//停止
                        eqpStatus = "Down";
                    }else if ("5".equals(tag_value)) {//运行
                        eqpStatus = "Run";
                    }else if ("6".equals(tag_value)) {//PM/保养
                        eqpStatus = "PM";
                    }else if ("7".equals(tag_value)) {//暂停
                        eqpStatus = "Pause";
                    }
                }
            }
        }
        // 查询正在WORK状态的任务ID
        try {
        	 //1.查询当前Work任务
            Map<String, Object> mapLotInfo= null;
        	Query queryBigData= new Query();
            queryBigData.addCriteria(Criteria.where("lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"lot_index"));
            MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection("a_eap_aps_plan").find(queryBigData.getQueryObject(),Map.class).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                mapLotInfo = iteratorBigData.next();
                iteratorBigData.close();
            }
            if(mapLotInfo!=null && mapLotInfo.get("plan_id")!=null) {
            	jobId = mapLotInfo.get("plan_id").toString();
            }
        } catch (Exception e) {
            log.error("查询工作中的任务ID失败：{}", e.getMessage(), e);
            errorMsg = "查询工作中的任务ID失败："+e.getMessage();
            return EapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId,esbInterfCode,jsonParas.toString(),"");
        }
        // 构建返回结果
        JSONObject resultContent = new JSONObject();
        resultContent.put("EqpId", station_code);
        resultContent.put("ControlMode", controlMode);
        resultContent.put("EqpStatus", eqpStatus);
        resultContent.put("JobId", jobId);
        return EapZhcyInterfCommon.CreateSuccessResult( "", requestId, resultContent);
    }

    /***
     * EAP时间下发
     * @param jsonParas
     * @param request
     * @param apiRoutePath
     * @return
     */
    private JSONObject processEAPDateTimeSyncCommand(JSONObject jsonParas,HttpServletRequest request,String apiRoutePath,String esbInterfCode) {
    	String requestId =null;
        try {
            // 验证请求参数
            if (!jsonParas.containsKey("From") || !jsonParas.containsKey("Message") ||
                !jsonParas.containsKey("DateTime") || !jsonParas.containsKey("Content") ||
                !jsonParas.containsKey("RequestId")) {
                return eapZhcyInterfCommon.CreateFaillResult("请求参数不完整","",esbInterfCode,jsonParas.toString());
            }
            // 验证Content内容
            JSONObject content = jsonParas.getJSONObject("Content");
            if (!content.containsKey("EqpId") || !content.containsKey("DateTime")) {
                return eapZhcyInterfCommon.CreateFaillResult("Content参数不完整",jsonParas.getString("RequestId"),esbInterfCode,jsonParas.toString());
            }
            String eqpId = content.getString("EqpId");
            String dateTime = content.getString("DateTime");
            requestId = jsonParas.getString("RequestId");

            // 记录时间同步请求
            log.info("收到设备[{}]时间同步请求，时间：{}，请求ID：{}", eqpId, dateTime, requestId);
           //修改本地电脑时间为dateTime
            SystemTimeUpdater.syncSystemTime(dateTime);

            //修改PC时间
            /**
            Date item_date=CFuncUtilsSystem.GetMongoISODate(dateTime);
            SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateTime=df.format(item_date);
            String[] lst=dateTime.split(" ",-1);
            String dataStr_=lst[0];
            String timeStr_=lst[1];
            String cmd = " cmd /c date " + dataStr_;
            Runtime.getRuntime().exec(cmd);
            cmd = " cmd /c time " + timeStr_;
            Runtime.getRuntime().exec(cmd);
            **/
         // 解析时间为PLC所需的格式 (2024/11/27 15:00:00)
            SimpleDateFormat sdfInput = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
            Date parsedDate = sdfInput.parse(dateTime);
            SimpleDateFormat sdfYear = new SimpleDateFormat("yyyy");
            SimpleDateFormat sdfMonth = new SimpleDateFormat("MM");
            SimpleDateFormat sdfDay = new SimpleDateFormat("dd");
            SimpleDateFormat sdfHour = new SimpleDateFormat("HH");
            SimpleDateFormat sdfMinute = new SimpleDateFormat("mm");
            SimpleDateFormat sdfSecond = new SimpleDateFormat("ss");
            // 构建PLC写入的tag值，使用&连接
            String tagValues = sdfYear.format(parsedDate) + "&" +
                              sdfMonth.format(parsedDate) + "&" +
                              sdfDay.format(parsedDate) + "&" +
                              sdfHour.format(parsedDate) + "&" +
                              sdfMinute.format(parsedDate) + "&" +
                              sdfSecond.format(parsedDate)+"&1";

            //通过工站code，查询PLC结尾的client_code。
            String sqlClientCode = "SELECT client_code from  scada_client where station_code='" + eqpId + "' and client_code like '%Plc' and enable_flag='Y'";
            List<Map<String, Object>> lstsqlClientCode = cFuncDbSqlExecute.ExecSelectSql(eqpId, sqlClientCode, false, request, apiRoutePath);
            String client_code = lstsqlClientCode.get(0).get("client_code").toString();
            // 写入PLC点位
            String writeTags =
            			client_code+"/PcStatus/AysnYear,"
            		+ ""+client_code+"/PcStatus/AysnMonth,"
    				+ ""+client_code+"/PcStatus/AysnDay,"
    				+ ""+client_code+"/PcStatus/AysnHour,"
					+ ""+client_code+"/PcStatus/AysnMinute,"
					+ ""+client_code+"/PcStatus/AysnSecond,"
					+ ""+client_code+"/PcStatus/AysnDateTimeFlag";
            cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, writeTags, tagValues, false);
            // 构建响应
            JSONObject response = eapZhcyInterfCommon.CreateSuccessResult("时间同步成功",requestId,jsonParas);
            response.put("Content", new JSONObject());
            return response;
        } catch (Exception ex) {
            log.error("处理时间同步请求异常：{}", ex.getMessage(), ex);
            return eapZhcyInterfCommon.CreateFaillResult("处理时间同步请求异常：" + ex.getMessage(),requestId,esbInterfCode,jsonParas.toString());
        }
    }

    /**
     * 任务信息下发 (EAP_JobDataDownload)
     * EAP下发生产任务信息给设备，处理任务重复（覆盖非生产任务）或添加到队列（每设备至少支持5个任务）
     */
    private JSONObject processEAPJobDataDownload(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath, String esbInterfCode) {
    	JSONObject content = null;
        try {
            // 验证请求参数
            String[] requiredKeys = {"From", "Message", "DateTime", "Content", "RequestId"};
            for (String key : requiredKeys) {
                if (!jsonParas.containsKey(key)) {
                    return EapZhcyInterfCommon.CreateFaillResult(
                        "缺少必要参数: " + key,
                        jsonParas.getString("RequestId"),
                        esbInterfCode,
                        jsonParas.toString()
                    );
                }
            }
            content = jsonParas.getJSONObject("Content");
            String requestId = jsonParas.getString("RequestId");
            // 验证Content内容
            String[] requiredContentKeys = {"EqpId", "JobId", "Pn", "TotalPanelCount", "PanelList", "RecipeParameterList"};
            for (String key : requiredContentKeys) {
                if (!content.containsKey(key)) {
                    return eapZhcyInterfCommon.CreateFaillResult(
                        "Content缺少必要参数: " + key,
                        requestId,
                        esbInterfCode,
                        jsonParas.toString()
                    );
                }
            }
            String eqpId = content.getString("EqpId");
            String jobId = content.getString("JobId");
            String pn = content.getString("Pn");
            String portId = content.getString("PortId"); // 可选参数
            Integer totalPanelCount = content.getInteger("TotalPanelCount");
            JSONArray panelList = content.getJSONArray("PanelList");
            JSONArray recipeParameterList = content.getJSONArray("RecipeParameterList");

            // 验证参数有效性
            if (eqpId.isEmpty() || jobId.isEmpty() || pn.isEmpty()) {
                return eapZhcyInterfCommon.CreateFaillResult(
                    "EqpId, JobId, Pn不能为空",
                    requestId,
                    esbInterfCode,
                    jsonParas.toString()
                );
            }
            if (totalPanelCount < 0) {
                return eapZhcyInterfCommon.CreateFaillResult(
                    "TotalPanelCount不能为负数",
                    requestId,
                    esbInterfCode,
                    jsonParas.toString()
                );
            }
            if (panelList.isEmpty()) {
                return eapZhcyInterfCommon.CreateFaillResult(
                    "PanelList不能为空",
                    requestId,
                    esbInterfCode,
                    jsonParas.toString()
                );
            }

            // 验证设备是否存在
            String sqlStation = "select station_id from sys_fmod_station where station_code='" + eqpId +"'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(eqpId, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.isEmpty()) {
                String errorMsg = "设备编号{" + eqpId + "}不存在系统中";
                log.warn(errorMsg + ", jsonParas: {}", jsonParas.toString());
                return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
            }

            // 检查任务队列数量（每设备最多50个任务）
            Query queueQuery = new Query(Criteria.where("enable_flag").is("Y").and("eqp_id").is(eqpId));
            long queueCount = mongoTemplate.count(queueQuery, "a_eap_aps_plan");

            // 检查任务是否重复
            Query jobQuery = new Query(Criteria.where("plan_id").is(jobId).and("eqp_id").is(eqpId).and("enable_flag").is("Y"));
            org.bson.Document existingJob = mongoTemplate.findOne(jobQuery, org.bson.Document.class, "a_eap_aps_plan");

            boolean isOverwrite = false;
            if (existingJob != null) {
                String lotStatus = existingJob.getString("lot_status");
                if ("WORK".equals(lotStatus)) {
                    log.warn("任务[{}]正在设备[{}]生产中，无法覆盖", jobId, eqpId);
                    return eapZhcyInterfCommon.CreateFaillResult(
                        "任务 " + jobId + " 正在生产中，无法覆盖",
                        requestId,
                        esbInterfCode,
                        jsonParas.toString()
                    );
                } else {
                    // 更新非生产中的任务
                    Update update = new Update()
                        .set("pn", pn)
                        .set("port_id", portId != null ? portId : "")
                        .set("total_panel_count", totalPanelCount)
                        .set("panel_list", panelList.toString())
                        .set("recipe_parameter_list", recipeParameterList.toString())
                        .set("lot_status", "PLAN")
                        .set("update_time", new Date());
                    mongoTemplate.updateFirst(jobQuery, update, "a_eap_aps_plan");
                    log.info("任务[{}]在设备[{}]上已存在且非生产中，执行更新", jobId, eqpId);
                    isOverwrite = true;

                    // 上报任务更新状态
                    try {
                        // 调用上报方法
                    	eapZhcySendBusinessFunc.reportJobDataProcess(eqpId, jobId, totalPanelCount, 0, "Update", request, apiRoutePath);
                    } catch (Exception ex) {
                        // 上报失败不影响主流程，只记录日志
                        log.error("任务更新后上报状态失败: {}", ex.getMessage(), ex);
                    }
                }
            } else {
                // 新任务，验证队列空间
                if (queueCount >= 50) {
                    log.warn("设备[{}]任务队列已满，当前数量：{}", eqpId, queueCount);
                    return eapZhcyInterfCommon.CreateFaillResult(
                        "任务队列已满，最大支持50个任务",
                        requestId,
                        esbInterfCode,
                        jsonParas.toString()
                    );
                }

                // 构建新任务数据
                org.bson.Document jobData = new org.bson.Document()
                    .append("eqp_id", eqpId)
                    .append("plan_id", jobId)
                    .append("pn", pn)
                    .append("port_id", portId != null ? portId : "")
                    .append("total_panel_count", totalPanelCount)
                    .append("panel_list", panelList.toString())
                    .append("recipe_parameter_list", recipeParameterList.toString())
                    .append("lot_status", "PLAN")
                    .append("enable_flag", "Y")
                    .append("create_time", new Date())
                    .append("lot_index", System.currentTimeMillis());

                // 保存到MongoDB
                mongoTemplate.getCollection("a_eap_aps_plan").insertOne(jobData);
                log.info("任务[{}]添加到设备[{}]队列，当前队列数：{}", jobId, eqpId, queueCount + 1);

                // 上报任务创建状态
                try {
                    // 调用上报方法
                	eapZhcySendBusinessFunc.reportJobDataProcess(eqpId, jobId, totalPanelCount, 0, "Create", request, apiRoutePath);
                } catch (Exception ex) {
                    // 上报失败不影响主流程，只记录日志
                    log.error("任务创建后上报状态失败: {}", ex.getMessage(), ex);
                }

            }

            // 构建响应
            JSONObject response = new JSONObject();
            response.put("Code", "0000");
            response.put("Success", true);
            response.put("Msg", isOverwrite ? "任务更新并下发成功" : "任务信息下发成功");
            response.put("DateTime", jsonParas.getString("DateTime"));
            response.put("Content", new JSONObject());
            response.put("RequestId", requestId);

            return response;
        } catch (Exception ex) {
            log.error("处理任务信息下发请求异常，设备[{}], 任务[{}]：{}", content.getString("EqpId"), content.getString("JobId"), ex.getMessage(), ex);
            return eapZhcyInterfCommon.CreateFaillResult(
                "处理任务信息下发请求异常：" + ex.getMessage(),
                jsonParas.getString("RequestId"),
                esbInterfCode,
                jsonParas.toString()
            );
        }
    }

    /**
     * 通知设备任务更改
     * 处理EAP_JobDataModifyCommand请求，根据ModifyType更新任务状态
     *
     * 任务切换流程：
     * 1. EAP系统向AIS发送任务切换通知EAP_JobDataModifyCommand，ModifyType为Update表示切换到新任务
     * 2. AIS收到通知后，将D6117=1（TmjPlc/PcStatus/ChangeTaskFlag）触发任务切换
     * 3. PLC检测到D6117=1后，停止生产，并在完成后设置D6014=1
     * 4. AIS检测到D6014=1后，调用本地方法和EAP任务结束接口，然后设置D6117=0
     *
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEAPJobDataModifyCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath, String esbInterfCode) {
        // 提前获取requestId，避免异常处理时可能的空指针
        String requestId = jsonParas.containsKey("RequestId") ? jsonParas.getString("RequestId") : "";
        String eqpId = "";
        String jobId = "";

        try {
            // 1. 验证请求参数 - 使用数组和循环简化验证逻辑
            String[] requiredKeys = {"From", "Message", "DateTime", "Content", "RequestId"};
            for (String key : requiredKeys) {
                if (!jsonParas.containsKey(key)) {
                    return eapZhcyInterfCommon.CreateFaillResult(
                        "缺少必要参数: " + key,
                        requestId,
                        esbInterfCode,
                        jsonParas.toString()
                    );
                }
            }

            // 2. 获取并验证Content内容
            JSONObject content = jsonParas.getJSONObject("Content");
            requestId = jsonParas.getString("RequestId"); // 重新获取确保有值

            String[] requiredContentKeys = {"EqpId", "JobId", "ModifyType"};
            for (String key : requiredContentKeys) {
                if (!content.containsKey(key)) {
                    return eapZhcyInterfCommon.CreateFaillResult(
                        "Content缺少必要参数: " + key,
                        requestId,
                        esbInterfCode,
                        jsonParas.toString()
                    );
                }
            }

            // 3. 获取关键参数
            eqpId = content.getString("EqpId");
            jobId = content.getString("JobId");
            String modifyType = content.getString("ModifyType");

            // 参数有效性检查
            if (eqpId.isEmpty() || jobId.isEmpty() || modifyType.isEmpty()) {
                return eapZhcyInterfCommon.CreateFaillResult(
                    "EqpId、JobId或ModifyType不能为空",
                    requestId,
                    esbInterfCode,
                    jsonParas.toString()
                );
            }

            // 4. 验证设备是否存在
            String sqlStation = "select station_id from sys_fmod_station where station_code='" + eqpId +"'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(eqpId, sqlStation, false, request, apiRoutePath);

            if (lstStation == null || lstStation.isEmpty()) {
                String errorMsg = "设备编号{" + eqpId + "}不存在系统中";
                log.warn("{}，请求参数: {}", errorMsg, jsonParas);
                return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
            }

            // 5. 检查任务是否存在且状态是否为WORK
            Query query = new Query(Criteria.where("plan_id").is(jobId).and("enable_flag").is("Y"));
            org.bson.Document existingJob = mongoTemplate.findOne(query, org.bson.Document.class, "a_eap_aps_plan");
            if (existingJob == null) {
                String errorMsg = "任务ID{" + jobId + "}不存在或已被删除";
                log.warn(errorMsg);
                return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
            }

            // 6. 根据ModifyType处理任务状态更新
            Update update = new Update().set("update_time", new Date());
            String successMsg;

            if ("Update".equalsIgnoreCase(modifyType)) {
                // 如果是Update类型，表示任务切换
                log.info("收到任务切换通知: EqpId={}, JobId={}", eqpId, jobId);
                // 检查任务是否在运行中 - 如果是WORK状态的任务，不允许修改
                String lotStatus = existingJob.getString("lot_status");
                if ("WORK".equals(lotStatus)) {
                    String errorMsg = "任务[" + jobId + "]正在设备[" + eqpId + "]生产中，无法修改";
                    log.warn(errorMsg);
                    return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
                }
                // 获取PLC客户端代码
                String sqlClientCode = "SELECT client_code from scada_client where station_code='" + eqpId + "' and client_code like '%Plc' and enable_flag='Y'";
                List<Map<String, Object>> lstsqlClientCode = cFuncDbSqlExecute.ExecSelectSql(eqpId, sqlClientCode, false, request, apiRoutePath);

                if (lstsqlClientCode == null || lstsqlClientCode.isEmpty()) {
                    String errorMsg = "未找到设备的PLC客户端配置: " + eqpId;
                    log.error(errorMsg);
                    return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
                }

                String clientCode = lstsqlClientCode.get(0).get("client_code").toString();

                // 设置任务切换标志 D6117=1
                String changeTaskFlagTag = clientCode + "/PcStatus/ChangeTaskFlag";
                String writeResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, changeTaskFlagTag, "1", true);

                if (!writeResult.isEmpty()) {
                    String errorMsg = "设置任务切换标志D6117=1失败: " + writeResult;
                    log.error(errorMsg);
                    return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
                }

                log.info("已设置任务切换标志D6117=1: EqpId={}, JobId={}", eqpId, jobId);

                // 更新任务状态为计划任务
                Integer totalPanelCount = content.getInteger("TotalPanelCount");
                update.set("lot_status", "PLAN");
                update.set("enable_flag", "Y");
                if(totalPanelCount != null) {
                    update.set("total_panel_count", totalPanelCount);
                }
                mongoTemplate.updateFirst(query, update, "a_eap_aps_plan");

                successMsg = "任务切换触发成功，已设置D6117=1";
                log.info("任务切换触发成功: EqpId={}, JobId={}", eqpId, jobId);

                // 注意：PLC会检测到D6117=1，停止生产，并在完成后设置D6014=1
                // AIS的Event模块会检测到D6014=1，调用本地方法和EAP任务结束接口，然后设置D6117=0
                // 这部分逻辑在EapZhcySendBusinessFunc.handleTaskChange方法中实现
            } else if ("Delete".equalsIgnoreCase(modifyType)) {
                // 检查任务是否在运行中 - 如果是WORK状态的任务，不允许删除
                String lotStatus = existingJob.getString("lot_status");
                if ("WORK".equals(lotStatus)) {
                    String errorMsg = "任务[" + jobId + "]正在设备[" + eqpId + "]生产中，无法删除";
                    log.warn(errorMsg);
                    return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
                }

                // 更新任务状态为CANCEL并设置enable_flag为N
                update.set("lot_status", "CANCEL").set("enable_flag", "N");
                mongoTemplate.updateFirst(query, update, "a_eap_aps_plan");
                successMsg = "任务删除成功";
                log.info("任务删除成功: EqpId={}, JobId={}", eqpId, jobId);
            } else {
                String errorMsg = "不支持的ModifyType类型: " + modifyType + "，支持的类型为Update或Delete";
                log.warn(errorMsg);
                return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
            }

            // 7. 构建成功响应
            JSONObject resultContent = new JSONObject();
            resultContent.put("EqpId", eqpId);
            resultContent.put("JobId", jobId);
            resultContent.put("ModifyType", modifyType);

            return eapZhcyInterfCommon.CreateSuccessResult(successMsg, requestId, resultContent);
        } catch (Exception ex) {
            // 详细记录异常信息
            String errorMsg = "处理任务更改通知异常: " + ex.getMessage();
            log.error("处理任务[{}]更改通知异常，设备[{}]: {}", jobId, eqpId, ex.getMessage(), ex);
            return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
        }
    }

    /**
     * 远程提示信息下达
     * 处理EAP_CIMMessageCommand请求，通知设备显示提示信息并触发蜂鸣器
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEAPCIMMessageCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath, String esbInterfCode) {
        // 提前获取requestId，避免异常处理时可能的空指针
        String requestId = jsonParas.containsKey("RequestId") ? jsonParas.getString("RequestId") : "";
        String eqpId = "";

        try {
            // 1. 验证请求参数
            String[] requiredKeys = {"From", "Message", "DateTime", "Content", "RequestId"};
            for (String key : requiredKeys) {
                if (!jsonParas.containsKey(key)) {
                    return eapZhcyInterfCommon.CreateFaillResult(
                        "缺少必要参数: " + key,
                        requestId,
                        esbInterfCode,
                        jsonParas.toString()
                    );
                }
            }

            // 2. 获取并验证Content内容
            JSONObject content = jsonParas.getJSONObject("Content");
            requestId = jsonParas.getString("RequestId"); // 重新获取确保有值

            String[] requiredContentKeys = {"EqpId", "IntervalSecondTime", "CimMessage"};
            for (String key : requiredContentKeys) {
                if (!content.containsKey(key)) {
                    return eapZhcyInterfCommon.CreateFaillResult(
                        "Content缺少必要参数: " + key,
                        requestId,
                        esbInterfCode,
                        jsonParas.toString()
                    );
                }
            }

            // 3. 获取关键参数
            eqpId = content.getString("EqpId");
            String intervalSecondTime = content.getString("IntervalSecondTime");
            String cimMessage = content.getString("CimMessage");

            // 参数有效性检查
            if (eqpId.isEmpty()) {
                return eapZhcyInterfCommon.CreateFaillResult(
                    "EqpId不能为空",
                    requestId,
                    esbInterfCode,
                    jsonParas.toString()
                );
            }

            if (cimMessage.isEmpty()) {
                return eapZhcyInterfCommon.CreateFaillResult(
                    "CimMessage不能为空",
                    requestId,
                    esbInterfCode,
                    jsonParas.toString()
                );
            }

            // 4. 验证设备是否存在
            String sqlStation = "select station_id from sys_fmod_station where station_code='" + eqpId +"'";
            List<Map<String, Object>> lstStation = cFuncDbSqlExecute.ExecSelectSql(eqpId, sqlStation, false, request, apiRoutePath);
            if (lstStation == null || lstStation.isEmpty()) {
                String errorMsg = "设备编号{" + eqpId + "}不存在系统中";
                log.warn("{}，请求参数: {}", errorMsg, jsonParas);
                return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
            }

            //通过工站code，查询PLC结尾的client_code。
            String sqlClientCode = "SELECT client_code from  scada_client where station_code='" + eqpId + "' and client_code like '%Plc' and enable_flag='Y'";
            List<Map<String, Object>> lstsqlClientCode = cFuncDbSqlExecute.ExecSelectSql(eqpId, sqlClientCode, false, request, apiRoutePath);
            String client_code = lstsqlClientCode.get(0).get("client_code").toString();

            // 6. 触发蜂鸣器
            // 写入触发蜂鸣器的点位
            String buzzerTag = client_code+"/PlcStatus/LightBee";
            String buzzerValue = "1"; // 1表示触发
            String writeResult = cFuncUtilsCellScada.WriteTagByStation("AIS", eqpId, buzzerTag, buzzerValue, false);
            if (!writeResult.isEmpty()) {
                String errorMsg = "触发蜂鸣器失败: " + writeResult;
                log.warn(errorMsg);
                // 这里不返回错误，因为消息已经发送成功，只是蜂鸣器触发失败
            }
            // 7. 将消息插入到a_eap_me_station_hmi_show表
            try {
                // 获取设备ID
                long stationId = Long.parseLong(lstStation.get(0).get("station_id").toString());
                // 准备当前时间
                Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                // 构建插入数据
                Map<String, Object> mapBigDataRow = new HashMap<>();
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("hmi_show_id", CFuncUtilsSystem.CreateUUID(true));
                mapBigDataRow.put("station_id", stationId);
                mapBigDataRow.put("screen_control", "1");
                mapBigDataRow.put("screen_code", "EAP");
                mapBigDataRow.put("cim_from", "EAP");
                mapBigDataRow.put("cim_msg", cimMessage);
                mapBigDataRow.put("finish_flag", "N");
                if(StringUtils.hasLength(intervalSecondTime) && !"0".equals(intervalSecondTime)) {
                	mapBigDataRow.put("interval_second_time", Integer.parseInt(intervalSecondTime));
                	//不需要确认。自动关闭。
                	mapBigDataRow.put("screen_control", "0");
                }
                // 插入MongoDB
                mongoTemplate.insert(mapBigDataRow, "a_eap_me_station_hmi_show");
                log.info("成功将消息插入到a_eap_me_station_hmi_show表: EqpId={}, Message={}", eqpId, cimMessage);
            } catch (Exception ex) {
                // 插入数据库失败不影响整体流程，只记录日志
                log.error("将消息插入到a_eap_me_station_hmi_show表失败: {}", ex.getMessage(), ex);
            }
            // 8. 构建成功响应
            log.info("远程提示信息下达成功: EqpId={}, Message={}, IntervalTime={}", eqpId, cimMessage, intervalSecondTime);
            return eapZhcyInterfCommon.CreateSuccessResult("远程提示信息下达成功", requestId, new JSONObject());
        } catch (Exception ex) {
            // 详细记录异常信息
            String errorMsg = "处理远程提示信息下达异常: " + ex.getMessage();
            log.error("处理远程提示信息下达异常，设备[{}]: {}", eqpId, ex.getMessage(), ex);
            return eapZhcyInterfCommon.CreateFaillResult(errorMsg, requestId, esbInterfCode, jsonParas.toString());
        }
    }

    /**
     * 膜材料校验 - 检查扫描的膜材料是否与当前任务匹配
     * @param jsonParas 请求参数JSON对象
     * @param request HTTP请求对象
     * @param apiRoutePath API路径
     * @param esbInterfCode 接口代码
     * @return 处理结果JSON对象
     */
    private JSONObject processEAPFilmMaterialValidation(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath, String esbInterfCode) {
        // 实现膜材料校验的逻辑
        // 这里需要根据实际需求实现具体的校验逻辑
        String errorMsg = "膜材料校验功能尚未实现";
        return eapZhcyInterfCommon.CreateFaillResult(errorMsg, jsonParas.getString("RequestId"), esbInterfCode, jsonParas.toString());
    }
}