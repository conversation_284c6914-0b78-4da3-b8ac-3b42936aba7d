package com.api.dcs.project.whzsj.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 移库计划功能函数
 * 1.新增移库计划
 * 2.判断是否存在并新增移库计划
 * 3.判断是否存在并新增移库计划明细
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
@Slf4j
public class DcsWhzsjMomRecvPlanMoveFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.新增移库计划
    public JSONObject receiveMomPlanMove(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        Integer code = 200;
        String userID="-1";
        String response_time = CFuncUtilsSystem.GetNowDateTime("");
        String request_uuid = "";
        try {
            //1.解析 JSON
            request_uuid = jsonParas.getString("reqNo");//请求编号
            String plan_num = jsonParas.getString("planNo");//计划编号
            String project_code = jsonParas.getString("projectCode");//项目号
            String block_code =jsonParas.getString("blockCode");//分段号
            String lot_num = jsonParas.getString("batchCode");//批次号
            String plan_type = jsonParas.getString("planType");//计划类型
            String stock_type = jsonParas.getString("stockType");//备料类型：0 板材、1 型材
            String start_time = jsonParas.getString("startTime");//备料开始时间
            String end_time = jsonParas.getString("endTime");//备料结束时间
            String plan_from = jsonParas.getString("reqSystem");//计划来源：AIS、MOM
            String plan_status = "PLAN";//计划状态(PLAN、WORK、FINISH、CANCEL)
            String flow_first_type=""; //先出类型：XB_OUT 西部车间(本车间)、GJ_OUT 刚加车间
            //备料类型：0 板材  1 型材(不管)
            if("1".equals(stock_type)){
                jbResult.put("response_uuid", request_uuid);
                jbResult.put("response_time", response_time);
                jbResult.put("plan_num", plan_num);
                jbResult.put("code", code);
                jbResult.put("msg", "成功");
                return jbResult;
            }
            //2.判断 移库计划是否存在
            Long plan_move_id=PlanMoveExistsIns(userID,plan_num,project_code,block_code,lot_num,
                    plan_type,stock_type,start_time,end_time,plan_from,plan_status,
                    flow_first_type,response_time);
            //3.解析明细
            JSONArray dataList = jsonParas.getJSONArray("data");
            if(dataList!=null && dataList.size()>0){
                String flow_code = "";//分道流向代码：XB 西部车间、GJ 刚加车间
                String scli_plan_no = "";//切割计划单号(分道流向代码为：中部车间时为空)
                String serial_num = "";//唯一码
                String scli_plan_order = "";//切割顺序：同一切割计划单内从1依次递增，1代表切割任务中的第一张
                String material_code = "";//钢材编码
                String material_des = "";//钢材名称
                String m_texture = "";//材质
                String ship_class = "";//船级社
                String m_length = "";//长
                String m_width = "";//宽
                String m_height = "";//高
                String m_weight = "";//重量
                for (int i = 0; i < dataList.size(); i++) {
                    JSONObject jsonObject = dataList.getJSONObject(i);
                    flow_code = jsonObject.getString("flowCode");//分道流向代码
                    scli_plan_no = jsonObject.getString("scliPlanNo");//切割计划单号
                    serial_num = jsonObject.getString("uniqNo");//唯一码
                    scli_plan_order = jsonObject.getString("scliPlanOrder");//切割顺序
                    material_code = jsonObject.getString("materialsCode");//钢材编码
                    material_des = jsonObject.getString("materialsName");//钢材名称
                    m_texture = jsonObject.getString("material");//材质
                    ship_class = jsonObject.getString("shipClass");//船级社
                    m_length = jsonObject.getString("length");//长
                    m_width = jsonObject.getString("width");//宽
                    m_height = jsonObject.getString("height");//高
                    m_weight = jsonObject.getString("weight");//重量
                    if (scli_plan_order == null ||"".equals(scli_plan_order)) scli_plan_order = "0";
                    if (m_length == null ||"".equals(m_length)) m_length = "0";
                    if (m_width == null ||"".equals(m_width)) m_width = "0";
                    if (m_height == null ||"".equals(m_height)) m_height = "0";
                    if (m_weight == null ||"".equals(m_weight)) m_weight = "0";
                    //4.新增 移库计划明细
                    PlanMoveDExistsIns(userID,plan_move_id,flow_code,scli_plan_no,
                            serial_num,scli_plan_order,material_code,material_des,m_texture,ship_class,
                            m_length,m_width,m_height,m_weight,response_time);
                }
            }
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("plan_num", plan_num);
            jbResult.put("code", code);
            jbResult.put("msg", "成功");
        } catch (Exception ex) {
            code = 500;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("plan_num", "");
            jbResult.put("code", code);
            jbResult.put("msg", errorMsg);
        }
        return jbResult;
    }

    //2.判断是否存在并新增移库计划
    public Long PlanMoveExistsIns(String userID, String plan_num,String project_code,String block_code,
                                  String lot_num,String plan_type,String stock_type,
                                  String start_time,String end_time,String plan_from,
                                  String plan_status,String flow_first_type,String response_time) throws Exception{
        //1.检索是否存在
        Long plan_move_id=0l;
        String sqlPlanMoveSel="select plan_move_id " +
                "from b_dcs_wms_plan_move " +
                "where plan_num='"+plan_num+"' ";
        List<Map<String, Object>> itemPlanMoveList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlPlanMoveSel,
                false,null,"");
        if(itemPlanMoveList!=null && itemPlanMoveList.size()>0){
            plan_move_id=Long.parseLong(itemPlanMoveList.get(0).get("plan_move_id").toString());
        }
        else {
            //2.不存在，新增
            plan_move_id=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_plan_move_id_seq",true);
            String sqlPlanMoveIns="insert into b_dcs_wms_plan_move " +
                    "(created_by,creation_date,plan_move_id," +
                    "plan_num,project_code,block_code,lot_num," +
                    "plan_type,stock_type,start_time,end_time," +
                    "plan_from,plan_status,flow_first_type,enable_flag) values" +
                    "('"+userID+"','"+response_time+"',"+plan_move_id+",'"+
                    plan_num+"','"+project_code+"','"+block_code+"','"+lot_num+"',"+
                    plan_type+","+stock_type+",'"+start_time+"','"+end_time+"','"+
                    plan_from+"','"+plan_status+"','"+flow_first_type+"','Y')";
            cFuncDbSqlExecute.ExecUpdateSql("AIS",sqlPlanMoveIns,
                    false,null,"");
        }
        return plan_move_id;
    }

    //3.判断是否存在并新增移库计划明细
    public Long PlanMoveDExistsIns(String userID, Long plan_move_id,
                                   String flow_code,String scli_plan_no,String serial_num,
                                   String scli_plan_order,String material_code,String material_des,
                                   String m_texture,String ship_class,String m_length,String m_width,
                                   String m_height,String m_weight,String response_time) throws Exception{
        //1.检索是否存在
        Long plan_move_d_id=0l;
        String sqlPlanMoveDSel="select plan_move_d_id " +
                "from b_dcs_wms_plan_move_d " +
                "where plan_move_id="+plan_move_id+" "+
                "and serial_num='"+serial_num+"' ";
        List<Map<String, Object>> itemPlanMoveDList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlPlanMoveDSel,
                false,null,"");
        if(itemPlanMoveDList!=null && itemPlanMoveDList.size()>0){
            plan_move_d_id=Long.parseLong(itemPlanMoveDList.get(0).get("plan_move_d_id").toString());
        }
        else {
            //2.不存在，新增
            plan_move_d_id=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_plan_move_d_id_seq",true);
            String sqlPlanMoveDIns="insert into b_dcs_wms_plan_move_d " +
                    "(created_by,creation_date,plan_move_d_id,plan_move_id," +
                    "flow_code,scli_plan_no,serial_num,scli_plan_order," +
                    "material_code,material_des,m_texture,ship_class," +
                    "m_length,m_width,m_height,m_weight,lock_flag) values" +
                    "('"+userID+"','"+response_time+"',"+plan_move_d_id+","+plan_move_id+",'"+
                    flow_code+"','"+scli_plan_no+"','"+serial_num+"',"+scli_plan_order+",'"+
                    material_code+"','"+material_des+"','"+m_texture+"','"+ship_class+"',"+
                    m_length+","+m_width+","+m_height+","+m_weight+",'N')";
            cFuncDbSqlExecute.ExecUpdateSql("AIS",sqlPlanMoveDIns,
                    false,null,"");
        }
        return plan_move_d_id;
    }

}
