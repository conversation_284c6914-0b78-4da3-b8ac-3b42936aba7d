package com.api.dcs.core.wms;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * WMS天车任务管理
 * 1.新增天车任务
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/wms")
public class DcsCoreCarTaskController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.新增天车任务
    @RequestMapping(value = "/DcsCoreWmsCarTaskIns", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreWmsCarTaskIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/wms/DcsCoreWmsCarTaskIns";
        String transResult="";
        String errorMsg="";
        String wmsCarTaskTable="b_dcs_wms_car_task";
        try{
            //1.获取参数
            String task_num=jsonParas.getString("task_num");
            String task_from=jsonParas.getString("task_from");
            String task_way=jsonParas.getString("task_way");
            String task_type=jsonParas.getString("task_type");
            String serial_num=jsonParas.getString("serial_num");
            String lot_num=jsonParas.getString("lot_num");
            String model_type=jsonParas.getString("model_type");
            String from_stock_code=jsonParas.getString("from_stock_code");
            String to_stock_code=jsonParas.getString("to_stock_code");
            String need_check_gd_flag=jsonParas.getString("need_check_gd_flag");
            String need_check_model_flag=jsonParas.getString("need_check_model_flag");
            String need_tell_start_flag=jsonParas.getString("need_tell_start_flag");
            String attribute1=jsonParas.getString("attribute1");
            String attribute2=jsonParas.getString("attribute2");
            String attribute3=jsonParas.getString("attribute3");
            String cq_if_tags_list=jsonParas.getString("cq_if_tags_list");
            String cq_if_tags_value=jsonParas.getString("cq_if_tags_value");
            String fz_if_tags_list=jsonParas.getString("fz_if_tags_list");
            String fz_if_tags_value=jsonParas.getString("fz_if_tags_value");
            String check_gd_tag=jsonParas.getString("check_gd_tag");
            String check_gd_value=jsonParas.getString("check_gd_value");
            String check_model_tag=jsonParas.getString("check_model_tag");

            if(task_from==null || task_from.equals("")) task_from="AIS";
            if(serial_num==null) serial_num="";
            if(lot_num==null) lot_num="";
            if(from_stock_code==null) from_stock_code="";
            if(to_stock_code==null) to_stock_code="";
            if(need_check_gd_flag==null || need_check_gd_flag.equals("")) need_check_gd_flag="N";
            if(need_check_model_flag==null || need_check_model_flag.equals("")) need_check_model_flag="N";
            if(need_tell_start_flag==null || need_tell_start_flag.equals("")) need_tell_start_flag="N";
            if(attribute1==null) attribute1="";
            if(attribute2==null) attribute2="";
            if(attribute3==null) attribute3="";
            if(cq_if_tags_list==null) cq_if_tags_list="";
            if(cq_if_tags_value==null) cq_if_tags_value="";
            if(fz_if_tags_list==null) fz_if_tags_list="";
            if(fz_if_tags_value==null) fz_if_tags_value="";
            if(check_gd_tag==null) check_gd_tag="";
            if(check_gd_value==null) check_gd_value="";
            if(check_model_tag==null) check_model_tag="";
            //2.判断参数是否满足要求
            if(task_num==null || task_num.equals("")){
                errorMsg="天车任务不能为空";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if(task_way==null || task_way.equals("")){
                errorMsg="天车任务方式不能为空";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if(task_type==null || task_type.equals("")){
                errorMsg="天车任务类型不能为空";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if(model_type==null || model_type.equals("")){
                errorMsg="型号不能为空";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            //3.判断任务是否已经存在,若存在则直接返回
            String[] task_status_list=new String[]{"PLAN","WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
            queryBigData.addCriteria(Criteria.where("task_status").in(task_status_list));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            long taskCount=mongoTemplate.getCollection(wmsCarTaskTable).countDocuments(queryBigData.getQueryObject());
            if(taskCount>0){
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
                return transResult;
            }
            //4.判断天车任务类型是否存在
            String sqlTaskTypeCount="select count(1) " +
                    "from sys_fastcode_group sfg inner join sys_fastcode sf " +
                    "on sfg.fastcode_group_id=sf.fastcode_group_id " +
                    "where sfg.enable_flag='Y' and sf.enable_flag='Y' " +
                    "and sfg.fastcode_group_code='TASK_TYPE' and sf.fastcode_code='"+task_type+"'";
            Integer taskTypeCount=cFuncDbSqlResolve.GetSelectCount(sqlTaskTypeCount);
            if(taskTypeCount<=0){
                errorMsg="天车任务类型{"+task_type+"}未在快速编码{TASK_TYPE}启用列表中";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            //5.判断型号是否存在
            String sqlModelCount="select count(1) " +
                    "from b_dcs_fmod_model " +
                    "where enable_flag='Y' and model_type='"+model_type+"'";
            Integer modelCount=cFuncDbSqlResolve.GetSelectCount(sqlModelCount);
            if(modelCount<=0){
                errorMsg="型号{"+model_type+"}未维护在型号基础数据启用列表中";
                transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            //6.判断来源库位
            if(!from_stock_code.equals("")){
                String sqlStockCount="select count(1) " +
                        "from b_dcs_wms_fmod_stock " +
                        "where enable_flag='Y' and stock_code='"+from_stock_code+"'";
                Integer stockCount=cFuncDbSqlResolve.GetSelectCount(sqlStockCount);
                if(stockCount<=0){
                    errorMsg="来源库位{"+from_stock_code+"}未存在库区基础数据启用列表中";
                    transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }
            //7.判断目标库位
            if(!to_stock_code.equals("")){
                String sqlStockCount="select count(1) " +
                        "from b_dcs_wms_fmod_stock " +
                        "where enable_flag='Y' and stock_code='"+to_stock_code+"'";
                Integer stockCount=cFuncDbSqlResolve.GetSelectCount(sqlStockCount);
                if(stockCount<=0){
                    errorMsg="目标库位{"+to_stock_code+"}未存在库区基础数据启用列表中";
                    transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }
            //8.插入任务
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            String task_id=CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("task_id",task_id);
            mapBigDataRow.put("task_from",task_from);
            mapBigDataRow.put("task_way",task_way);
            mapBigDataRow.put("task_type",task_type);
            mapBigDataRow.put("task_num",task_num);
            mapBigDataRow.put("serial_num",serial_num);
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("model_type",model_type);
            mapBigDataRow.put("from_stock_code",from_stock_code);
            mapBigDataRow.put("to_stock_code",to_stock_code);
            mapBigDataRow.put("need_check_gd_flag",need_check_gd_flag);
            mapBigDataRow.put("need_check_model_flag",need_check_model_flag);
            mapBigDataRow.put("need_tell_start_flag",need_tell_start_flag);
            mapBigDataRow.put("tell_start_date","");
            mapBigDataRow.put("tell_cancel_date","");
            mapBigDataRow.put("tell_stop_date","");
            mapBigDataRow.put("tell_start_by","");
            mapBigDataRow.put("tell_cancel_by","");
            mapBigDataRow.put("tell_stop_by","");
            mapBigDataRow.put("task_status","PLAN");
            mapBigDataRow.put("lock_flag","N");
            mapBigDataRow.put("enable_flag","Y");
            mapBigDataRow.put("attribute1",attribute1);
            mapBigDataRow.put("attribute2",attribute2);
            mapBigDataRow.put("attribute3",attribute3);
            mapBigDataRow.put("cq_if_tags_list",cq_if_tags_list);
            mapBigDataRow.put("cq_if_tags_value",cq_if_tags_value);
            mapBigDataRow.put("fz_if_tags_list",fz_if_tags_list);
            mapBigDataRow.put("fz_if_tags_value",fz_if_tags_value);
            mapBigDataRow.put("check_gd_tag",check_gd_tag);
            mapBigDataRow.put("check_gd_value",check_gd_value);
            mapBigDataRow.put("check_model_tag",check_model_tag);
            mongoTemplate.insert(mapBigDataRow,wmsCarTaskTable);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= ex.getMessage();
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
