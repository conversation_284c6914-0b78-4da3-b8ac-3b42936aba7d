
package com.api.eap.project.dy.p1.wcf;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebResult;
import javax.jws.WebService;
import javax.xml.bind.annotation.XmlSeeAlso;
import javax.xml.datatype.XMLGregorianCalendar;
import javax.xml.ws.RequestWrapper;
import javax.xml.ws.ResponseWrapper;


/**
 * This class was generated by the JAX-WS RI.
 * JAX-WS RI 2.3.0-SNAPSHOT
 * Generated source version: 2.2
 * 
 */
@WebService(name = "DyServiceSoap", targetNamespace = "http://tempuri.org/")
@XmlSeeAlso({
    ObjectFactory.class
})
public interface DyServiceSoap {


    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.Response
     */
    @WebMethod(operationName = "AlarmReport", action = "http://tempuri.org/AlarmReport")
    @WebResult(name = "AlarmReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "AlarmReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.AlarmReport")
    @ResponseWrapper(localName = "AlarmReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.AlarmReportResponse")
    public Response alarmReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        AlarmReportRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.UserVerifyResponseItem
     */
    @WebMethod(operationName = "UserVerify", action = "http://tempuri.org/UserVerify")
    @WebResult(name = "UserVerifyResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "UserVerify", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.UserVerify")
    @ResponseWrapper(localName = "UserVerifyResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.UserVerifyResponse")
    public UserVerifyResponseItem userVerify(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        UserVerifyRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.Response
     */
    @WebMethod(operationName = "RTMReport", action = "http://tempuri.org/RTMReport")
    @WebResult(name = "RTMReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "RTMReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.RTMReport")
    @ResponseWrapper(localName = "RTMReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.RTMReportResponse")
    public Response rtmReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        RtmReportRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.PanelDataUploadReportResponseItem
     */
    @WebMethod(operationName = "PanelDataUploadReport", action = "http://tempuri.org/PanelDataUploadReport")
    @WebResult(name = "PanelDataUploadReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "PanelDataUploadReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.PanelDataUploadReport")
    @ResponseWrapper(localName = "PanelDataUploadReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.PanelDataUploadReportResponse")
    public PanelDataUploadReportResponseItem panelDataUploadReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        PanelDataUploadReportRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.ParamVerifyResponseItem
     */
    @WebMethod(operationName = "ParamVerify", action = "http://tempuri.org/ParamVerify")
    @WebResult(name = "ParamVerifyResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "ParamVerify", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.ParamVerify")
    @ResponseWrapper(localName = "ParamVerifyResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.ParamVerifyResponse")
    public ParamVerifyResponseItem paramVerify(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        ParamVerifyRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.Response
     */
    @WebMethod(operationName = "WIPTrackingReport", action = "http://tempuri.org/WIPTrackingReport")
    @WebResult(name = "WIPTrackingReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "WIPTrackingReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.WIPTrackingReport")
    @ResponseWrapper(localName = "WIPTrackingReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.WIPTrackingReportResponse")
    public Response wipTrackingReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        WipTrackingReportRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.Response
     */
    @WebMethod(operationName = "RecipeChangeReport", action = "http://tempuri.org/RecipeChangeReport")
    @WebResult(name = "RecipeChangeReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "RecipeChangeReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.RecipeChangeReport")
    @ResponseWrapper(localName = "RecipeChangeReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.RecipeChangeReportResponse")
    public Response recipeChangeReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        RecipeChangeReportRequestBody requestBody);

    /**
     * 
     * @return
     *     returns javax.xml.datatype.XMLGregorianCalendar
     */
    @WebMethod(operationName = "HelloWorld", action = "http://tempuri.org/HelloWorld")
    @WebResult(name = "HelloWorldResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "HelloWorld", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.HelloWorld")
    @ResponseWrapper(localName = "HelloWorldResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.HelloWorldResponse")
    public XMLGregorianCalendar helloWorld();

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.Response
     */
    @WebMethod(operationName = "RecipeReport", action = "http://tempuri.org/RecipeReport")
    @WebResult(name = "RecipeReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "RecipeReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.RecipeReport")
    @ResponseWrapper(localName = "RecipeReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.RecipeReportResponse")
    public Response recipeReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        RecipeReportRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.Response
     */
    @WebMethod(operationName = "RealStatusReport", action = "http://tempuri.org/RealStatusReport")
    @WebResult(name = "RealStatusReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "RealStatusReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.RealStatusReport")
    @ResponseWrapper(localName = "RealStatusReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.RealStatusReportResponse")
    public Response realStatusReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        RealStatusReportRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.Response
     */
    @WebMethod(operationName = "UtilityReport", action = "http://tempuri.org/UtilityReport")
    @WebResult(name = "UtilityReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "UtilityReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.UtilityReport")
    @ResponseWrapper(localName = "UtilityReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.UtilityReportResponse")
    public Response utilityReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        UtilityReportRequestBody requestBody);

    /**
     * 
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.EqpInfoVerifyResponseItem
     */
    @WebMethod(operationName = "EQPInfoVerify", action = "http://tempuri.org/EQPInfoVerify")
    @WebResult(name = "EQPInfoVerifyResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "EQPInfoVerify", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.EQPInfoVerify")
    @ResponseWrapper(localName = "EQPInfoVerifyResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.EQPInfoVerifyResponse")
    public EqpInfoVerifyResponseItem eqpInfoVerify(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.MaterialStatusReportResponseItem
     */
    @WebMethod(operationName = "MaterialStatusReport", action = "http://tempuri.org/MaterialStatusReport")
    @WebResult(name = "MaterialStatusReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "MaterialStatusReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.MaterialStatusReport")
    @ResponseWrapper(localName = "MaterialStatusReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.MaterialStatusReportResponse")
    public MaterialStatusReportResponseItem materialStatusReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        MaterialStatusReportRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.Response
     */
    @WebMethod(operationName = "StatusChangeReport", action = "http://tempuri.org/StatusChangeReport")
    @WebResult(name = "StatusChangeReportResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "StatusChangeReport", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.StatusChangeReport")
    @ResponseWrapper(localName = "StatusChangeReportResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.StatusChangeReportResponse")
    public Response statusChangeReport(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        StatusChangeReportRequestBody requestBody);

    /**
     * 
     * @param requestBody
     * @param requestHead
     * @return
     *     returns com.api.eap.project.dy.p1.wcf.MaterialVerifyResponseItem
     */
    @WebMethod(operationName = "MaterialVerify", action = "http://tempuri.org/MaterialVerify")
    @WebResult(name = "MaterialVerifyResult", targetNamespace = "http://tempuri.org/")
    @RequestWrapper(localName = "MaterialVerify", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.MaterialVerify")
    @ResponseWrapper(localName = "MaterialVerifyResponse", targetNamespace = "http://tempuri.org/", className = "com.api.eap.project.dy.p1.wcf.MaterialVerifyResponse")
    public MaterialVerifyResponseItem materialVerify(
        @WebParam(name = "request_head", targetNamespace = "http://tempuri.org/")
        RequestHead requestHead,
        @WebParam(name = "request_body", targetNamespace = "http://tempuri.org/")
        MaterialVerifyRequestBody requestBody);

}
