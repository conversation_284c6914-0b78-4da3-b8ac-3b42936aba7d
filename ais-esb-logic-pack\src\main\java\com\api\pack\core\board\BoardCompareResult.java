package com.api.pack.core.board;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 板件比较结果类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-24
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BoardCompareResult
{
    private String status;

    private int code;

    private String msg;

    private Integer position;

    public BoardCompareResult(String status, int code, String msg)
    {
        this.status = status;
        this.code = code;
        this.msg = msg;
    }

    public boolean isOK() {
        return BoardConst.STATUS_OK.equals(status);
    }

    public boolean isNG() {
        return BoardConst.STATUS_NG.equals(status);
    }
}
