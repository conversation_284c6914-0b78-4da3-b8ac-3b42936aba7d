<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ais-esb-api</artifactId>
        <groupId>com.api</groupId>
        <version>2.1.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ais-esb-api-starter</artifactId>
    <packaging>war</packaging>
    <name>ais-esb-api-starter</name>
    <description>project for ais esb api starter</description>

    <dependencies>
        <!-- 代码生成模块 -->
        <dependency>
            <groupId>com.api</groupId>
            <artifactId>ais-esb-logic-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.api</groupId>
            <artifactId>ais-esb-logic-eap</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.api</groupId>
            <artifactId>ais-esb-logic-dcs</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.api</groupId>
            <artifactId>ais-esb-logic-pmc</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.api</groupId>
            <artifactId>ais-esb-logic-mes</artifactId>
            <version>2.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.api</groupId>
            <artifactId>ais-esb-logic-pack</artifactId>
            <version>2.1.0</version>
        </dependency>
    </dependencies>

    <!-- 打包 -->
    <build>
        <finalName>aisEsbApi</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <!-- 跳过单元测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
        </plugins>
    </build>

</project>
