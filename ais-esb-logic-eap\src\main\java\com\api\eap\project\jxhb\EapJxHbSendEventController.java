package com.api.eap.project.jxhb;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * (江西红板)投收扳机标准发送事件接口
 * 1.嫁动率上报
 * 2.生产参数实时值上报
 * 3.报警上传
 * 4.设备状态上报
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/jxhb/interf/send")
public class EapJxHbSendEventController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapJxHbSendEventFunc eapJxHbSendEventFunc;

    //1.嫁动率上报
    @RequestMapping(value = "/ActivationReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapJxHbActivationReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/jxhb/interf/send/ActivationReport";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        try{
            String station_code=jsonParas.getString("station_code");
            String shift_name=jsonParas.getString("shift_name");
            String activation=jsonParas.getString("activation");
            //查询工位信息
            String sqlStation="select station_id," +
                    "station_des,COALESCE(bg_proceduce_des,'') bg_proceduce_des " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(userId,sqlStation,false,request,apiRoutePath);
            Long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_des=itemListStation.get(0).get("station_des").toString();
            String bg_proceduce_des=itemListStation.get(0).get("bg_proceduce_des").toString();
            eapJxHbSendEventFunc.ActivationReport(station_id,station_code, station_des,bg_proceduce_des,
                    shift_name,activation);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "嫁动率上报到EAP异常:"+ex;
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.生产参数实时值上报
    @RequestMapping(value = "/ProductParasReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapJxHbProductParasReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/jxhb/interf/send/ProductParasReport";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        try{
            String station_code=jsonParas.getString("station_code");
            String recipe_code=jsonParas.getString("recipe_code");
            String panel_width=jsonParas.getString("panel_width");
            String panel_thick=jsonParas.getString("panel_thick");
            String device_status=jsonParas.getString("device_status");
            String total_output=jsonParas.getString("total_output");
            String unit_output=jsonParas.getString("unit_output");
            String plan_count=jsonParas.getString("plan_count");
            String finish_count=jsonParas.getString("finish_count");
            String putboard_time=jsonParas.getString("putboard_time");
            String putboard_signal=jsonParas.getString("putboard_signal");
            String device_mode=jsonParas.getString("device_mode");

            //查询工位信息
            String sqlStation="select station_id," +
                    "station_des,COALESCE(bg_proceduce_des,'') bg_proceduce_des " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(userId,sqlStation,false,request,apiRoutePath);
            Long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_des=itemListStation.get(0).get("station_des").toString();
            String bg_proceduce_des=itemListStation.get(0).get("bg_proceduce_des").toString();
            eapJxHbSendEventFunc.ProductParasReport(station_id,station_code, station_des,bg_proceduce_des,
                    recipe_code,panel_width,panel_thick,
                    device_status,total_output,unit_output,plan_count,finish_count,
                    putboard_time,putboard_signal,device_mode);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "生产参数实时值上报到EAP异常:"+ex;
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.报警上传
    @RequestMapping(value = "/AlarmReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapJxHbAlarmReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/jxhb/interf/send/AlarmReport";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        try{
            String station_code=jsonParas.getString("station_code");
            String alarm_code=jsonParas.getString("alarm_code");
            String alarm_des=jsonParas.getString("alarm_des");
            String alarm_value=jsonParas.getString("alarm_value");

            //查询工位信息
            String sqlStation="select station_id," +
                    "station_des,COALESCE(bg_proceduce_des,'') bg_proceduce_des " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(userId,sqlStation,false,request,apiRoutePath);
            Long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_des=itemListStation.get(0).get("station_des").toString();
            String bg_proceduce_des=itemListStation.get(0).get("bg_proceduce_des").toString();
            eapJxHbSendEventFunc.AlarmReport(station_id,station_code, station_des,bg_proceduce_des,
                    alarm_code,alarm_des,alarm_value);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "报警上传到EAP异常:"+ex;
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.设备状态上报
    @RequestMapping(value = "/DeviceStatusReport", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapJxHbDeviceStatusReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/jxhb/interf/send/DeviceStatusReport";
        String transResult="";
        String errorMsg="";
        String userId="-1";
        try{
            String station_code=jsonParas.getString("station_code");
            String device_status=jsonParas.getString("device_status");

            //查询工位信息
            String sqlStation="select station_id," +
                    "station_des,COALESCE(bg_proceduce_des,'') bg_proceduce_des " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(userId,sqlStation,false,request,apiRoutePath);
            Long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_des=itemListStation.get(0).get("station_des").toString();
            String bg_proceduce_des=itemListStation.get(0).get("bg_proceduce_des").toString();
            eapJxHbSendEventFunc.DeviceStatusReport(station_id,station_code, station_des,bg_proceduce_des,device_status);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "设备状态上报EAP异常:"+ex;
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
