package com.api.eap.project.ajl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.eap.project.thailand.guanghe.EapTlGhInterfCommon;
import com.api.eap.project.thailand.guanghe.EapTlGhSendEventSubFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * (安捷利)EAP发送事件功能函数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class EapAjlSendEventFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //1.[接口]响应AGV请求点位
    public String EapAjlAgvStaus(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String transResult = "";
        String errorMsg = "";
        try {
            String rcmd_code = jsonParas.getString("rcmd_code");
            String line_code = jsonParas.getString("line_code");
            String station_code = jsonParas.getString("station_code");
            String[] strList = rcmd_code.split("_");
            String str = strList[strList.length - 1];
            String port_code = jsonParas.getString("port_code" + "_" + str);
            String sqlStationPort = "select a.port_index,COALESCE(b.station_attr,'') station_attr  from a_eap_fmod_station_port a " +
                    "inner join sys_fmod_station b on a.station_id=b.station_id " +
                    "where b.station_code='" + station_code + "' and a.port_code='" + port_code + "' " +
                    "and a.enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> lstStationPort = cFuncDbSqlExecute.ExecSelectSql("AGV", sqlStationPort,
                    false, request, "");
            if (lstStationPort == null || lstStationPort.size() <= 0) {
                errorMsg = "未能根据工位号{" + station_code + "}，port_code{" + port_code + "}在AIS查找地码配置工位端口信息";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            String port_index = lstStationPort.get(0).get("port_index").toString();
            String station_attr = lstStationPort.get(0).get("station_attr").toString();
            //获取当前模式
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String PortAgvStatusTag = "";
            String PortAgvStatusValue = "";
            if (aisMonitorModel.equals("AIS-PC")) {
                if (station_attr.equals("Load")) {
                    PortAgvStatusTag = "LoadPlc/PlcStatus/Port" + port_index + "AgvStatus";
                } else {
                    PortAgvStatusTag = "UnLoadPlc/PlcStatus/Port" + port_index + "AgvStatus";
                }
            } else {
                if (station_attr.equals("Load")) {
                    PortAgvStatusTag = "LoadPlc_" + station_code + "/PlcStatus/Port" + port_index + "AgvStatus";
                } else {
                    PortAgvStatusTag = "UnLoadPlc_" + station_code + "/PlcStatus/Port" + port_index + "AgvStatus";
                }
            }
            String checkValue = "1";
            Boolean sucess = false;
            for (int i = 0; i < 6; i++) {
                if (!PortAgvStatusTag.equals("")) {
                    JSONArray jsonArrayTag = cFuncUtilsCellScada.ReadTagByStation(station_code, PortAgvStatusTag);
                    if (jsonArrayTag != null && jsonArrayTag.size() > 0) {
                        JSONObject jbItem = jsonArrayTag.getJSONObject(0);
                        PortAgvStatusValue = jbItem.getString("tag_value");
                    } else {
                        throw new Exception("未读取到标签{" + PortAgvStatusTag + "}对应值");
                    }
                }
                if (rcmd_code.indexOf("agv_before_in") != -1) checkValue = "1";
                if (rcmd_code.indexOf("execute") != -1) checkValue = "2";
                if (PortAgvStatusValue.equals(checkValue))
                    sucess = true;
                else
                    sucess = false;
                if (sucess) break;
                else Thread.sleep(5000);
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(sucess, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "响应AGV请求进入事件异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
