package com.api.pack.core.fmod;

import com.api.base.Const;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class FmodRecipeShipAddressService extends ServiceImpl<FmodRecipeShipAddressMapper, FmodRecipeShipAddress> implements IService<FmodRecipeShipAddress>
{
    private final FmodRecipeShipAddressMapper mapper;

    private final FmodRecipeShipAddressDetailMapper detailMapper;

    public FmodRecipeShipAddressService(FmodRecipeShipAddressMapper mapper, FmodRecipeShipAddressDetailMapper detailMapper)
    {
        this.mapper = mapper;
        this.detailMapper = detailMapper;
    }

    public FmodRecipeShipAddress getById(Long id)
    {
        FmodRecipeShipAddress t = mapper.selectById(id);
        if (t != null)
        {
            t.setDetails(FmodRecipeShipAddressDetail.listByParentId(id, detailMapper));
        }
        return t;
    }

    public FmodRecipeShipAddress getByName(String name)
    {
        QueryWrapper<FmodRecipeShipAddress>  queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq(Const.PROPERTY_ENABLE_FLAG,  Const.FLAG_Y);
        queryWrapper.eq("shipaddress_name", name);
        FmodRecipeShipAddress t = mapper.selectOne(queryWrapper);
        if (t != null)
        {
            t.setDetails(FmodRecipeShipAddressDetail.listByParentId(t.getId(), detailMapper));
        }
        return t;
    }
}
