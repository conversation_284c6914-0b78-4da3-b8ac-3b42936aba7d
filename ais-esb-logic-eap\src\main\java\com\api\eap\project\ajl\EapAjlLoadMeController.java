package com.api.eap.project.ajl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 泰国广合放板机定制生产计划处理逻辑
 * 1.放板机Panel校验
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/ajl/me")
public class EapAjlLoadMeController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //治具板件绑定信息存储
    @RequestMapping(value = "/EapAjlLoadMeZjPanelBind", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapAjlLoadMeZjPanelBind(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/ajl/me/EapAjlLoadMeZjPanelBind";
        String transResult = "";
        String errorMsg = "";
        String meZjPanelTable = "a_eap_me_zj_panel";
        try {
            long station_id = jsonParas.getLong("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String zj_barcode = jsonParas.getString("zj_barcode");

            //存储
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String zj_panel_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("zj_panel_id", zj_panel_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("zj_barcode", zj_barcode);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("enable_flag", "Y");
            mongoTemplate.insert(mapBigDataRow, meZjPanelTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, zj_panel_id, "", 0);
        } catch (Exception ex) {
            errorMsg = "离线板件信息存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //治具板件信息解绑
    @RequestMapping(value = "/EapAjlLoadMeZjPanelUnBind", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapAjlLoadMeZjPanelUnBind(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/ajl/me/EapAjlLoadMeZjPanelUnBind";
        String transResult = "";
        String errorMsg = "";
        String meZjPanelTable = "a_eap_me_zj_panel";
        try {
            long station_id = jsonParas.getLong("station_id");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String zj_barcode = jsonParas.getString("zj_barcode");

            //更新为无效
            Criteria criteria = new Criteria();
            criteria.orOperator(Criteria.where("zj_barcode").is(zj_barcode), Criteria.where("panel_barcode").is(panel_barcode));
            Query queryBigData = new Query(criteria);
            Update updateBigData = new Update();
            updateBigData.set("enable_flag", "N");
            mongoTemplate.updateFirst(queryBigData, updateBigData, meZjPanelTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "离线板件信息存储异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


}
