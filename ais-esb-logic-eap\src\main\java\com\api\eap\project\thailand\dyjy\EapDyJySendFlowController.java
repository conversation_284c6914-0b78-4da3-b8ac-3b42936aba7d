package com.api.eap.project.thailand.dyjy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.dy.EapDyInterfCommon;
import com.api.eap.project.thailand.dycj.EapDyCjSendFlowFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 * 泰国定颖(竞益)EAP发送流程数据定义接口
 * 1.WIPTrackingReport:[接口]每一lot完板上报
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-08
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/dyjy/interf/send")
public class EapDyJySendFlowController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyCjSendFlowFunc eapDyCjSendFlowFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;

    //4.EAP子批次完板上报
    @RequestMapping(value = "/SubLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyJySubLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dyjy/interf/send/SubLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meUserTable = "a_eap_me_station_user";
        MongoCursor<Document> iteratorBigData = null;
        try {
            String station_attr = jsonParas.getString("station_attr");
            String sys_model = jsonParas.getString("sys_model");//本地远程
            String task_start_time= jsonParas.getString("task_start_time");
            String task_end_time= jsonParas.getString("task_end_time");
            String material_code_P001= jsonParas.getString("material_code");
            String lot_num= jsonParas.getString("lot_num");
            Integer lot_finish_count= jsonParas.getInteger("lot_finish_count");
            Integer first_plan_count_P004= jsonParas.getInteger("first_plan_count");
            Integer sum_plan_count= jsonParas.getInteger("sum_plan_count");
            String lot_version_P002= jsonParas.getString("lot_version");
            String prod_mode= jsonParas.getString("prod_mode");
            String lot_short_num= jsonParas.getString("lot_short_num");
            String attribute1_P005= jsonParas.getString("process_code");
            String attribute2_P006= jsonParas.getString("use_in_name");
            String T001= jsonParas.getString("T001");
            String T002= jsonParas.getString("T002");
            String T003= jsonParas.getString("T003");
            String T004= jsonParas.getString("T004");
            String T005= jsonParas.getString("T005");
            String T006= jsonParas.getString("T006");
            String T007= jsonParas.getString("T007");
            String T008= jsonParas.getString("T008");
            String T009= jsonParas.getString("T009");
            //防止错误信息
            if(station_attr==null) station_attr="";
            if(sys_model==null || sys_model.equals("")) sys_model="0";
            if(task_start_time==null || task_start_time.equals("")) task_start_time=CFuncUtilsSystem.GetNowDateTime("");
            if(task_end_time==null || task_end_time.equals("")) task_end_time=CFuncUtilsSystem.GetNowDateTime("");
            if(material_code_P001==null) material_code_P001="";
            if(lot_num==null) lot_num="";
            if(lot_finish_count==null) lot_finish_count=0;
            if(first_plan_count_P004==null) first_plan_count_P004=0;
            if(sum_plan_count==null) sum_plan_count=0;
            if(lot_version_P002==null) lot_version_P002="";
            if(prod_mode==null) prod_mode="";
            if(lot_short_num==null) lot_short_num="";
            if(attribute1_P005==null) attribute1_P005="";
            if(attribute2_P006==null) attribute2_P006="";
            if(T001==null) T001="";
            if(T002==null) T002="";
            if(T003==null) T003="";
            if(T004==null) T004="";
            if(T005==null) T005="";
            if(T006==null) T006="";
            if(T007==null) T007="";
            if(T008==null) T008="";
            if(T009==null) T009="";

            //判断在线离线
            String offline_flag = "N";
            String local_flag = "Y";
            if (sys_model.equals("3")) local_flag = "N";
            if(sys_model.equals("0") || sys_model.equals("1")) offline_flag="Y";

            //参数信息
            String user_name = "";
            String dept_id = "";
            String shift_id = "";
            JSONObject attr_else = null;

            //1.获取工位信息
            String sqlStation="select station_id," +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_attr='"+station_attr+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(
                    "AIS",sqlStation,false,request,apiRoutePath);
            String station_id=itemListStation.get(0).get("station_id").toString();
            String station_code=itemListStation.get(0).get("station_code").toString();
            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                dept_id = docItemBigData.getString("dept_id");
                shift_id = docItemBigData.getString("shift_id");
                iteratorBigData.close();
            }
            //3.创建参数
            JSONArray item_attr_list = new JSONArray();
            //网板类型
            JSONObject jbAttr=new JSONObject();
            jbAttr.put("item_id", "T001");
            jbAttr.put("item_value", T001);
            item_attr_list.add(jbAttr);
            //网版宽度
            jbAttr=new JSONObject();
            jbAttr.put("item_id", "T002");
            jbAttr.put("item_value", T002);
            item_attr_list.add(jbAttr);
            //网版高度
            jbAttr=new JSONObject();
            jbAttr.put("item_id", "T003");
            jbAttr.put("item_value", T003);
            item_attr_list.add(jbAttr);
            //网板T数
            jbAttr=new JSONObject();
            jbAttr.put("item_id", "T004");
            jbAttr.put("item_value", T004);
            item_attr_list.add(jbAttr);
            //印刷面别
            jbAttr=new JSONObject();
            jbAttr.put("item_id", "T005");
            jbAttr.put("item_value", T005);
            item_attr_list.add(jbAttr);
            //扫描速度
            jbAttr=new JSONObject();
            jbAttr.put("item_id", "T006");
            jbAttr.put("item_value", T006);
            item_attr_list.add(jbAttr);
            //曝光能量
            jbAttr=new JSONObject();
            jbAttr.put("item_id", "T007");
            jbAttr.put("item_value", T007);
            item_attr_list.add(jbAttr);

            //上报WIP【异步】
            eapDyCjSendFlowFunc.WIPTrackingReport(Long.parseLong(station_id), station_code, user_name, dept_id, shift_id,
                    task_start_time, task_end_time, lot_num, sum_plan_count, first_plan_count_P004, lot_finish_count,
                    material_code_P001, lot_short_num, lot_version_P002, prod_mode, attribute1_P005, attribute2_P006,
                    item_attr_list, offline_flag, local_flag, attr_else);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            try {
                if (iteratorBigData != null) {
                    if (iteratorBigData.hasNext()) iteratorBigData.close();
                }
            } catch (Exception exx) {
            }
            errorMsg = "EAP子批次完板上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
