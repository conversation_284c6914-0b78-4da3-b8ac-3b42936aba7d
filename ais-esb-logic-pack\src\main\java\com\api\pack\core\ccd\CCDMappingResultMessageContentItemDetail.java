package com.api.pack.core.ccd;

import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.api.base.IMongoBasic;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;


@ApiModel(value = "CCDMappingResultMessageContentItemDetail", description = "CCD MES")
@Document("a_pack_strip_inspect_data")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class CCDMappingResultMessageContentItemDetail extends IMongoBasic {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "2Dmapping批号")
    @JSONField(name = "TWODLOT_NO")
    @JsonProperty("TWODLOT_NO")
    @Field("twodlot_no")
    private String twodLotNo = "";

    @ApiModelProperty(value = "旋转角度")
    @JSONField(name = "Angle")
    @JsonProperty("Angle")
    @Field("angle")
    private String angle = "";

    @ApiModelProperty(value = "不良數據结果")
    @JSONField(name = "ERR_TYPE")
    @JsonProperty("ERR_TYPE")
    @Field("err_type")
    private String errType = "";

    @ApiModelProperty(value = "X,Y（翻转）")
    @JSONField(name = "FLIP_TYPE")
    @JsonProperty("FLIP_TYPE")
    @Field("flip_type")
    private String flipType = "";

    @ApiModelProperty(value = "Each  Piece（Strip） X")
    @JSONField(name = "STRIPEACHPIECEX")
    @JsonProperty("STRIPEACHPIECEX")
    @Field("stripeachpiecex")
    private String stripEeachPieceX = "";

    @ApiModelProperty(value = "Each  Piece（Strip） Y")
    @JSONField(name = "STRIPEACHPIECEY")
    @JsonProperty("STRIPEACHPIECEY")
    @Field("stripeachpiecey")
    private String stripEachPieceY = "";

    @ApiModelProperty(value = "原始母批")
    @JSONField(name = "PARENTLOT_NO")
    @JsonProperty("PARENTLOT_NO")
    @Field("parentlot_no")
    private String parentlotNo = "";

    @ApiModelProperty(value = "重工数")
    @JSONField(name = "REWORKNUM")
    @JsonProperty("REWORKNUM")
    @Field("reworknum")
    private String reworkNum = "";

    @ApiModelProperty(value = "制程代号")
    @JSONField(name = "PROCESS_NO")
    @JsonProperty("PROCESS_NO")
    @Field("process_no")
    private String processNo = "";

    @ApiModelProperty(value = "料号")
    @JSONField(name = "Product_NO")
    @JsonProperty("Product_NO")
    @Field("product_no")
    private String productNo = "";

    @ApiModelProperty(value = "料号版本")
    @JSONField(name = "PRODUCT_REV")
    @JsonProperty("PRODUCT_REV")
    @Field("product_rev")
    private String productRev = "";

    @ApiModelProperty(value = "批号")
    @JSONField(name = "LOT_NO")
    @JsonProperty("LOT_NO")
    @Field("lot_no")
    private String lotNo = "";

    @ApiModelProperty(value = "条码")
    @JSONField(name = "BARCODE")
    @JsonProperty("BARCODE")
    @Field("barcode")
    private String barcode = "";

    @ApiModelProperty(value = "mapping结果（1000010010100）")
    @JSONField(name = "BADMARK")
    @JsonProperty("BADMARK")
    @Field("bad_mark")
    private String badMark = "";

    @ApiModelProperty(value = "数据来源")
    @JSONField(name = "dataFrom")
    @JsonProperty("dataFrom")
    @Field("dataFrom")
    private String dataFrom = "";

    @ApiModelProperty(value = "上传时间")
    @JSONField(name = "upload_date_val")
    @JsonProperty("upload_date_val")
    @Field("upload_date_val")
    private Long uploadDateVal;

    public CCDMappingResultMessageContentItemDetail(String lotNo, String productNo, String processNo, String filename, String dataFrom) {
        LocalDateTime now = LocalDateTime.now();
        this.itemDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        this.itemDateVal = Long.parseLong(now.format(DateTimeFormatter.ofPattern(Const.DATE_FORMAT_DEFAULT)));
        this.enableFlag = Const.FLAG_Y;

        // 通过文件名提取数据 modified by jay-y 2024/08/19
        String name = filename.replace(".txt", "");
        String[] values = name.split("_");
        if (values.length != 4) {
            return;
        }

        this.productNo = productNo;
        this.processNo = processNo;
        this.lotNo = lotNo;
        this.barcode = values[0];
        this.badMark = values[1];
        this.uploadDateVal = Long.parseLong(values[3]);
        this.dataFrom = dataFrom;
    }
}
