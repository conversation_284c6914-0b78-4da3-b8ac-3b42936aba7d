package com.api.dcs.core.mongo;

import
        com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbMongoIndex;
import com.api.common.db.CFuncDbSqlResolve;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 1.DCS标准MongoDB索引创建
 * 2.根据系统参数读取判断项目索引创建
 * </p>
 *
 * <AUTHOR>
 * @since 2020-03-17
 */

@Service
@Slf4j
public class DcsCoreMongoIndexFunc {
    @Autowired
    private CFuncDbMongoIndex cFuncDbMongoIndex;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //创建表索引
    @Async
    public void CreateDcsIndex(){
        String Dcs_MongoDbIndexList=cFuncDbSqlResolve.GetParameterValue("Dcs_MongoDbIndexList");
        if(Dcs_MongoDbIndexList!=null && !Dcs_MongoDbIndexList.equals("")){
            String[] lst=Dcs_MongoDbIndexList.split(",",-1);
            if(lst!=null && lst.length>0){
                for(String itemName : lst){
                    if(itemName.equals("DCS")){
                        CreateDcsCoreIndex();
                    }
                }
            }
        }
    }

    //标准表索引
    @Async
    public void CreateDcsCoreIndex(){
        CreateDcsApsTaskIndex();
        CreateDcsApsTaskPlusIndex();
        CreateDcsApsTaskEventIndex();
        CreateDcsApsTaskResolveIndex();
        CreateDcsMeSortResultIndex();
        CreateDcsMeSortBoxIndex();
        CreateDcsMeStationFlowIndex();
        CreateDcsMeStationQualityIndex();
        CreateDcsWmsCarTaskIndex();
        CreateDcsWmsTransferTaskIndex();
        CreateDcsWmsStockEventIndex();
        CreateDcsMeCarTaskHisIndex();
        CreateDcsMeCarRouteHisIndex();
        CreateDcsWmsFjTaskIndex();
    }

    //创建b_dcs_aps_task动态索引,生产任务表
    @Async
    public void CreateDcsApsTaskIndex(){
        String tableName="b_dcs_aps_task";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","mo_id","task_num","task_from","serial_num","task_type","material_code",
                "model_type","m_texture","plan_date","task_status","cut_code","is_auto_blast","is_auto_car","is_auto_cut","is_auto_sort","is_center",
                "is_materialt","now_station_code","bg_flag","enable_flag"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_aps_task索引成功");
            else log.warn("创建Mongo表b_dcs_aps_task索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_aps_task索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_aps_task_plus动态索引,生产任务余料表
    @Async
    public void CreateDcsApsTaskPlusIndex(){
        String tableName="b_dcs_aps_task_plus";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","mo_plus_id","mo_id","plus_task_num","plus_serial_num"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_aps_task_plus索引成功");
            else log.warn("创建Mongo表b_dcs_aps_task_plus索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_aps_task_plus索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_aps_task_event动态索引,生产任务事件记录表
    @Async
    public void CreateDcsApsTaskEventIndex(){
        String tableName="b_dcs_aps_task_event";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","mo_event_id","mo_id","event_type",
                "send_data","event_code","event_status","start_date","end_date"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_aps_task_event索引成功");
            else log.warn("创建Mongo表b_dcs_aps_task_event索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_aps_task_event索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_aps_task_resolve动态索引,生产任务分拣解析结果
    @Async
    public void CreateDcsApsTaskResolveIndex(){
        String tableName="b_dcs_aps_task_resolve";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","mo_resolve_id","mo_id","station_code",
                "part_barcode","part_code","part_type","part_attr"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_aps_task_resolve索引成功");
            else log.warn("创建Mongo表b_dcs_aps_task_resolve索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_aps_task_resolve索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_me_sort_result动态索引,生产任务分拣结果
    @Async
    public void CreateDcsMeSortResultIndex(){
        String tableName="b_dcs_me_sort_result";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","sort_result_id","mo_id","station_code","robot_id",
                "part_barcode","part_code","part_type","fj_code","fj_start_time","fj_end_time"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_me_sort_result索引成功");
            else log.warn("创建Mongo表b_dcs_me_sort_result索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_me_sort_result索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_me_sort_box动态索引,二次分拣料框结果
    @Async
    public void CreateDcsMeSortBoxIndex(){
        String tableName="b_dcs_me_sort_box";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","sort_box_id","box_location","box_barcode","task_num",
                "part_barcode","part_code","part_type","fj_code","fj_start_time","fj_end_time"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_me_sort_box索引成功");
            else log.warn("创建Mongo表b_dcs_me_sort_box索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_me_sort_box索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_me_station_flow动态索引,过站信息
    @Async
    public void CreateDcsMeStationFlowIndex(){
        String tableName="b_dcs_me_station_flow";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","station_flow_id","station_code","task_num",
                "task_from","serial_num","lot_num","pallet_num","material_code", "model_type","m_texture","pass_way",
                "arrive_date","shif_code","online_flag","offline_flag","quality_sign","bg_flag","up_flag","enable_flag"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_me_station_flow索引成功");
            else log.warn("创建Mongo表b_dcs_me_station_flow索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_me_station_flow索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_me_station_quality动态索引,质量追溯
    @Async
    public void CreateDcsMeStationQualityIndex(){
        String tableName="b_dcs_me_station_quality";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","quality_trace_id","station_flow_id","station_code",
                "task_num","serial_num","group_order","group_name","tag_col_order","tag_id",
                "tag_col_inner_order","quality_for","tag_des","quality_d_sign","trace_d_time"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_me_station_quality索引成功");
            else log.warn("创建Mongo表b_dcs_me_station_quality索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_me_station_quality索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_wms_car_task动态索引,WMS天车任务
    @Async
    public void CreateDcsWmsCarTaskIndex(){
        String tableName="b_dcs_wms_car_task";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","task_id","task_from","task_way","task_type","task_num",
                "serial_num","lot_num","model_type","from_stock_code","to_stock_code","need_check_gd_flag",
                "need_check_model_flag","need_tell_start_flag","task_status","lock_flag","enable_flag"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_wms_car_task索引成功");
            else log.warn("创建Mongo表b_dcs_wms_car_task索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_wms_car_task索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_wms_transfer_task动态索引,WMS辊道任务
    @Async
    public void CreateDcsWmsTransferTaskIndex(){
        String tableName="b_dcs_wms_transfer_task";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","transfer_task_id","task_num","serial_num","lot_num",
                "pallet_num","location_code","from_location_code","execute_status","next_location_code",
                "task_code","model_type","arrive_date","leave_date","finish_xz_flag","allow_pass","enable_flag"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_wms_transfer_task索引成功");
            else log.warn("创建Mongo表b_dcs_wms_transfer_task索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_wms_transfer_task索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_wms_me_stock_event动态索引,WMS库存事件
    @Async
    public void CreateDcsWmsStockEventIndex(){
        String tableName="b_dcs_wms_me_stock_event";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","stock_event_id","stock_id","car_code","stock_code",
                "stock_way","task_way","task_type","task_from","task_num","serial_num","lot_num",
                "model_type","material_code","m_texture","from_stock_code","to_stock_code","event_status","up_flag","up_code"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_wms_me_stock_event索引成功");
            else log.warn("创建Mongo表b_dcs_wms_me_stock_event索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_wms_me_stock_event索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_wms_me_car_task_his动态索引,WMS天车调度任务历史表
    @Async
    public void CreateDcsMeCarTaskHisIndex(){
        String tableName="b_dcs_wms_me_car_task_his";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","car_task_id","cycle_code","car_code","ware_house",
                "task_from","task_way","task_type","task_num","serial_num","lot_num","material_code",
                "model_type","m_texture","from_stock_code","to_stock_code","task_start_date",
                "finish_check_gd_flag","check_model_flag","execute_status"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_wms_me_car_task_his索引成功");
            else log.warn("创建Mongo表b_dcs_wms_me_car_task_his索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_wms_me_car_task_his索引失败:"+ex.getMessage());
        }
    }

    //创建b_dcs_wms_me_car_route_his动态索引,WMS天车调度任务路线历史表
    @Async
    public void CreateDcsMeCarRouteHisIndex(){
        String tableName="b_dcs_wms_me_car_route_his";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","car_route_id","ware_house","car_code",
                "car_task_type","step_status","need_check_gd_flag","need_check_model_flag","car_task_id"};
        int keep_days=360;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_wms_me_car_route_his索引成功");
            else log.warn("创建Mongo表b_dcs_wms_me_car_route_his索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_wms_me_car_route_his索引失败:"+ex.getMessage());
        }
    }

    @Async
    public void CreateDcsWmsFjTaskIndex(){
        String tableName="b_dcs_wms_fj_task";
        JSONArray jArrayIndex=new JSONArray();
        String[] indexList=new String[]{"item_date","item_date_val","fj_task_id","task_num","material_code",
                "task_status","stock_group_code","stock_id"};
        int keep_days=60;
        try{
            for(String indexName :indexList){
                JSONObject jsonObject=new JSONObject();
                jsonObject.put("col_name",indexName);
                jsonObject.put("background",true);
                if(indexName.equals("item_date_val")) jsonObject.put("dirction","DESC");
                else jsonObject.put("dirction","ASC");
                if(indexName.equals("item_date")) jsonObject.put("keep_days",keep_days);
                else jsonObject.put("keep_days",0);
                jArrayIndex.add(jsonObject);
            }
            Boolean isSuccess=cFuncDbMongoIndex.CreateIndex(tableName,jArrayIndex);
            if(isSuccess) log.info("创建Mongo表b_dcs_wms_fj_task索引成功");
            else log.warn("创建Mongo表b_dcs_wms_fj_task索引失败");
        }
        catch (Exception ex){
            log.warn("创建Mongo表b_dcs_wms_fj_task索引失败:"+ex.getMessage());
        }
    }
}
