package com.api.base;

import org.springframework.http.ResponseEntity;

import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.net.URI;
import java.util.Map;

public interface IController<T extends Serializable>
{
    default <T> ResponseEntity<IResponseBody> ok()
    {
        return this.ok(null);
    }

    /**
     * [GET]：服务器成功返回用户请求的数据
     *
     * @param <T> data
     * @return ResponseEntity
     */
    default <T> ResponseEntity<IResponseBody> ok(T data)
    {
        return ResponseEntity.ok(IResponseBody.ok(data));
    }


    /**
     * [GET]：服务器成功返回用户请求的数据
     *
     * @param <T> data
     * @param count 数据总数
     * @return ResponseEntity
     */
    default <T> ResponseEntity<IResponseBody> ok(T data, long count)
    {
        return ResponseEntity.ok(IResponseBody.ok(data, count));
    }

    /**
     * [POST/PUT/PATCH]：用户新建或修改数据成功
     *
     * @param resourceId
     * @param <T>
     * @return
     */
    default <T> ResponseEntity<IResponseBody> created(String resourceId)
    {
        return this.created(resourceId, IResponseBody.ok(null));
    }

    default <T> ResponseEntity<IResponseBody> accepted()
    {
        return this.accepted(IResponseBody.ok(null));
    }

    default <T> ResponseEntity<IResponseBody> created(String resourceId, T data)
    {
        return ResponseEntity.created(URI.create(resourceId)).body(IResponseBody.ok(data));
    }

    default <T> ResponseEntity<IResponseBody> accepted(T data)
    {
        return ResponseEntity.accepted().body(IResponseBody.ok(data));
    }

    default <T> ResponseEntity<T> noContent()
    {
        return ResponseEntity.noContent().build();
    }

    default <T> ResponseEntity<T> notFound()
    {
        return ResponseEntity.notFound().build();
    }

    default T newInstanceOfVO()
    {
        try
        {
            return this.voClass().newInstance();
        }
        catch (InstantiationException | IllegalAccessException e)
        {
            throw new RuntimeException(String.format("实例化或非法访问异常：%s", e.getMessage()));
        }
    }

    default Class<T> voClass()
    {
        Type type;
        Type[] interfaces = this.getClass().getGenericInterfaces();
        if (interfaces.length > 0)
        {
            type = interfaces[0];
        }
        else
        {
            type = this.getClass().getGenericSuperclass();
        }
        ParameterizedType parameterizedType = type instanceof ParameterizedType ? (ParameterizedType) type : type instanceof Class ? getParameterizedType((Class<?>) type) : null;
        if (parameterizedType == null)
        {
            throw new RuntimeException("处理器实现类解析失败，请检查程序设计是否正常");
        }
        Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
        if (actualTypeArguments.length == 0)
        {
            throw new RuntimeException("处理器实现类缺少泛型参数，请检查程序设计是否正常");
        }
        return (Class<T>) parameterizedType.getActualTypeArguments()[0];
    }

    default void overlay(Map<String, Object> map, T instance)
    {
        Class<T> voClass = this.voClass();
        for (Map.Entry<String, Object> entry : map.entrySet())
        {
            try
            {
                Field field = voClass.getDeclaredField(entry.getKey());
                field.setAccessible(true);
                // 判断字段类型并进行转换（仅支持Long、Integer）
                if (field.getType().equals(Long.class) && entry.getValue() instanceof String)
                {
                    field.set(instance, Long.parseLong((String) entry.getValue()));
                }
                else if (field.getType().equals(Integer.class) && entry.getValue() instanceof String)
                {
                    field.set(instance, Integer.parseInt((String) entry.getValue()));
                }
                else
                {
                    field.set(instance, entry.getValue());
                }
            }
            catch (NoSuchFieldException | IllegalArgumentException | IllegalAccessException e)
            {
                // Handle the case where the field does not exist or type mismatch
                e.printStackTrace();
            }
        }
    }

    default ParameterizedType getParameterizedType(Class<?> cls)
    {
        Type type;
        Type[] interfaces = cls.getGenericInterfaces();
        if (interfaces.length > 0)
        {
            type = interfaces[0];
        }
        else
        {
            type = cls.getGenericSuperclass();
        }
        return type instanceof ParameterizedType ? (ParameterizedType) type : null;
    }
}
