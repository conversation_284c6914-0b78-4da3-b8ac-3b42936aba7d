package com.api.pack.core.board;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.api.base.IMongoBasic;
import com.api.common.utils.CFuncUtilsSystem;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.context.MessageSource;
import org.springframework.util.ObjectUtils;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;

/**
 * <p>
 * 板件基类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@ApiModel(value = "Board", description = "板件信息")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public abstract class Board extends IMongoBasic
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "板件朝向")
    private String boardOrient;

    @ApiModelProperty(value = "板件ID")
    private String boardId;

    @ApiModelProperty(value = "板件索引")
    private Integer boardIndex;

    @ApiModelProperty(value = "板件条码")
    private String boardBarcode;

    @ApiModelProperty(value = "板件名称")
    private String boardName;

    @ApiModelProperty(value = "板件等级")
    private String boardLevel;

    @ApiModelProperty(value = "板件类别")
    private String boardCategory;

    @ApiModelProperty(value = "板件类型")
    private String boardType;

    @ApiModelProperty(value = "板件状态")
    private String boardStatus;

    @ApiModelProperty(value = "板件判定结果")
    private Integer boardResult;

    @ApiModelProperty(value = "板件NG码")
    private Integer boardNgCode;

    @ApiModelProperty(value = "板件NG信息")
    private String boardNgMsg;

    @ApiModelProperty(value = "板件标记")
    private String boardMark;

    @ApiModelProperty(value = "板件方向")
    private String boardDirection;

    @ApiModelProperty(value = "板件字符")
    private String boardChar;

    @ApiModelProperty(value = "XOUT标记")
    private String xoutFlag;

    public Board(String orient, String boardCategory, JSONObject bd)
    {
        LocalDateTime now = LocalDateTime.now();
        this.itemDate = Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
        this.itemDateVal = Long.parseLong(now.format(DateTimeFormatter.ofPattern(Const.DATE_FORMAT_DEFAULT)));
        this.enableFlag = Const.FLAG_Y;
        this.setBoardOrient(orient);
        String boardBarcodeKey = boardCategory + BoardConst.QRC;
        String boardCharKey = boardCategory + BoardConst.CHAR;
        if (bd.containsKey(boardBarcodeKey) && bd.containsKey(boardCharKey))
        {
            this.setBoardId(CFuncUtilsSystem.CreateUUID(true));
            this.setBoardCategory(boardCategory.toUpperCase());
            this.setBoardType(BoardConst.BOARD_TYPE_NONE);
            this.setBoardIndex(bd.getIntValue(boardCategory + BoardConst.NUM));
            String index = ObjectUtils.isEmpty(this.getBoardIndex()) ? "" : "[" + this.getBoardIndex() + "]";
            this.setBoardName(String.format("%s%s %s", this.getBoardCategory(), index,
                    BoardConst.ORIENT_BACK.equals(this.getBoardOrient()) ? BoardConst.BACK : BoardConst.FRONT));
            String boardBarcode = bd.getString(boardBarcodeKey);
            String boardLevel = bd.getString(boardCategory + BoardConst.QRC_LEVEL);
            String boardMark = bd.getString(BoardConst.MARK);
            boardMark = BoardConst.VALUE_NG.equals(boardMark) ? BoardConst.NG : boardMark; // @NG@ -> NG
            String boardDirection = bd.getString(BoardConst.DIRECTION);
            this.setBoardBarcode(boardBarcode);
            this.setBoardLevel(boardLevel);
            this.setBoardStatus(BoardConst.OK);
            this.setBoardNgCode(0);
            this.setBoardNgMsg(BoardConst.BLANK);
            this.setBoardMark(boardMark);
            this.setBoardDirection(boardDirection);
            Object boardChar = bd.get(boardCharKey);
            if (boardChar instanceof String)
            {
                this.setBoardChar((String) boardChar);
            }
            else if (boardChar instanceof Map)
            {
                this.setBoardChar(JSON.toJSONString(boardChar));
            }
        }
        else
        {
            this.setEnableFlag(BoardConst.FLAG_N);
        }
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isNone()
    {
        return BoardConst.BOARD_TYPE_NONE.equals(this.getBoardType());
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isSingle()
    {
        return BoardConst.BOARD_TYPE_SINGLE.equals(this.getBoardType());
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isDouble()
    {
        return BoardConst.BOARD_TYPE_DOUBLE.equals(this.getBoardType());
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isXout()
    {
        return BoardConst.FLAG_Y.equals(this.getXoutFlag());
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isValid()
    {
        return !BoardConst.VALUE_NULL.equals(this.getBoardBarcode()) // 空板件
                && !BoardConst.NO_READ.equals(this.getBoardBarcode()); // 未读板件
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isOK()
    {
        return BoardConst.OK.equals(this.getBoardStatus());
    }

    /**
     * 检测板件相关属性值
     *
     * @param comparator     BoardDefaultComparator
     * @param targetProperty String
     * @param targetValue    Object
     * @param messageSource  MessageSource
     */
    public void checkValue(BoardDefaultComparator comparator, String targetProperty, Object targetValue, MessageSource messageSource)
    {
        this.checkValue(comparator, targetProperty, targetValue, messageSource, false);
    }

    /**
     * 检测板件相关属性值
     *
     * @param comparator     BoardDefaultComparator
     * @param targetProperty String
     * @param targetValue    Object
     * @param messageSource  MessageSource
     * @param needResult     boolean
     * @return BoardCompareResult
     */
    public BoardCompareResult checkValue(BoardDefaultComparator comparator, String targetProperty, Object targetValue, MessageSource messageSource, boolean needResult)
    {
        BoardCompareResult result = comparator.compare(this, targetProperty, targetValue, messageSource);
        if (!needResult && result.isNG())
        {
            throw new BoardCompareException(result.getMsg(), result.getStatus(), result.getCode());
        }
        return result;
    }

    @SuppressWarnings("unchecked")
    public <S extends Board> S toOK()
    {
        String index = ObjectUtils.isEmpty(this.getBoardIndex()) ? "" : "[" + this.getBoardIndex() + "]";
        this.setBoardName(this.getBoardCategory() + index);
        this.setBoardStatus(BoardConst.OK);
        this.setBoardResult(this.isXout() ? BoardConst.RESULT_X_OUT : BoardConst.RESULT_OK);
        this.setBoardNgCode(BoardConst.CODE_OK);
        this.setBoardNgMsg(BoardConst.BLANK);
        this.covertValues();
        return (S) this;
    }

    @SuppressWarnings("unchecked")
    public <S extends Board> S toNG(int code, String msg)
    {
        this.setBoardStatus(BoardConst.NG);
        this.setBoardResult(BoardConst.RESULT_NG);
        this.setBoardNgCode(code);
        this.setBoardNgMsg(msg);
        this.setEnableFlag(Const.FLAG_N);
        this.covertValues();
        return (S) this;
    }

    private void covertValues()
    {
        this.setBoardBarcode(BoardConst.convertValue(this.getBoardBarcode()));
        this.setBoardLevel(BoardConst.convertValue(this.getBoardLevel()));
        this.setBoardMark(BoardConst.convertValue(this.getBoardMark()));
        this.setBoardDirection(BoardConst.convertValue(this.getBoardDirection()));
    }
}
