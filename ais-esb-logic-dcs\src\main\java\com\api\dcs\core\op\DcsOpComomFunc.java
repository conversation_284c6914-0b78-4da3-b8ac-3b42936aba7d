package com.api.dcs.core.op;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * <p>
 * 工位公共方法
 * 1.获取工位信息
 * 2.工件到达更新MIS信息
 * 3.工件离开更新MIS信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Service
@Slf4j
public class DcsOpComomFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.获取工位信息
    public Map<String, Object> GetStationInfo(String station_code) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlStation="select sfs.station_des,sfs.online_flag,sfs.offline_flag," +
                "sfs.prod_line_id,sfpl.prod_line_code," +
                "COALESCE(sfs.bg_proceduce_code,'') bg_proceduce_code," +
                "COALESCE(sfs.bg_proceduce_des,'') bg_proceduce_des," +
                "COALESCE(sfs.data_collect_type,'SCADA') data_collect_type," +
                "COALESCE(sfs.product_type,'') product_type," +
                "COALESCE(sfs.beat_times,0) beat_times," +
                "COALESCE(sfs.station_short_code,0) station_short_code," +
                "COALESCE(sfs.line_section_code,'') line_section_code," +
                "COALESCE(sfs.sub_station_code,'') sub_station_code," +
                "COALESCE(sfs.station_attr,'') station_attr " +
                "from sys_fmod_station sfs inner join sys_fmod_prod_line sfpl " +
                "on sfs.prod_line_id=sfpl.prod_line_id " +
                "where sfs.station_code='"+station_code+"' LIMIT 1 OFFSET 0";
        List<Map<String, Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation,
                false,null,"");
        if(itemListStation!=null && itemListStation.size()>0){
            mapItem=itemListStation.get(0);
        }
        return mapItem;
    }

    //2.工件到达更新MIS信息
    public void MisArriveUpdate(String user_name,String arrive_date,
                                String station_code,String station_des,String station_attr,
                                String task_num,String task_from,String serial_num,String model_type,
                                String lot_num,String pallet_num,String pallet_use_count,
                                Integer ideal_beats,String shift_code,String shift_des,
                                Map<String, Object> mapModel) throws Exception{
        String apsTaskTable="b_dcs_aps_task";
        String meStationFlowTable="b_dcs_me_station_flow";
        String dcsFlowEvent = "b_dcs_flow_event";
        Integer pallet_use_count_int=Integer.parseInt(pallet_use_count);
        Integer pallet_max_count=0;
        String pallet_alarm_flag="N";
        String mo_id="";
        String dxf_name="";
        String json_name="";
        String nc_name="";
        String material_code="";
        String material_des="";
        String material_draw="";
        Float m_length=0F;
        Float m_width=0F;
        Float m_height=0F;
        Float m_weight=0F;
        String m_texture="";
        String cut_texture="";
        String online_time= CFuncUtilsSystem.GetNowDateTime("");
        String from_station_code="";
        String arrive_date2=arrive_date;
        Long serial_stay_time=0L;//工件在线时间
        String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
        long currentTimeMillis = System.currentTimeMillis()/1000;
        if(arrive_date2==null || arrive_date2.equals("")) arrive_date2=CFuncUtilsSystem.GetNowDateTime("");

        //1.根据托盘查询托盘信息
        String sqlPalletSel="select maintenance_value " +
                "from b_dcs_fmod_pallet " +
                "where pallet_num='"+pallet_num+"'";
        List<Map<String, Object>> itemListPallet=cFuncDbSqlExecute.ExecSelectSql(station_code, sqlPalletSel,
                false,null,"");
        if(itemListPallet!=null && itemListPallet.size()>0){
            pallet_max_count=Integer.parseInt(itemListPallet.get(0).get("maintenance_value").toString());
            if(pallet_max_count>0 && pallet_use_count_int>=pallet_max_count) pallet_alarm_flag="Y";
        }

        //2.根据task_num查询任务信息
        Query queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
        queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
        MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).
                sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
        if(iteratorBigData.hasNext()){
            Document docItemBigData = iteratorBigData.next();
            mo_id=docItemBigData.getString("mo_id");
            dxf_name=docItemBigData.getString("dxf_name");
            json_name=docItemBigData.getString("json_name");
            nc_name=docItemBigData.getString("nc_name");
            iteratorBigData.close();
        }

        //3.获取机型信息
        if(mapModel!=null){
            material_code=mapModel.get("material_code").toString();
            material_des=mapModel.get("material_des").toString();
            material_draw=mapModel.get("material_draw").toString();
            m_length=Float.parseFloat(mapModel.get("m_length").toString());
            m_width=Float.parseFloat(mapModel.get("m_width").toString());
            m_height=Float.parseFloat(mapModel.get("m_height").toString());
            m_weight=Float.parseFloat(mapModel.get("m_weight").toString());
            m_texture=mapModel.get("m_texture").toString();
            cut_texture=mapModel.get("cut_texture").toString();
        }

        //4.获取上线信息
        queryBigData = new Query();
        queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
        queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
        queryBigData.addCriteria(Criteria.where("online_flag").is("Y"));
        queryBigData.with(Sort.by(Sort.Direction.DESC,"_id"));
        iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).
                sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
        if(iteratorBigData.hasNext()){
            Document docItemBigData = iteratorBigData.next();
            online_time=docItemBigData.getString("arrive_date");
            iteratorBigData.close();
        }

        //5.计算在线时间
        serial_stay_time=CFuncUtilsSystem.GetDiffMsTimes(online_time,arrive_date2);
        serial_stay_time=serial_stay_time/1000;//获取秒

        //6.判断是否新增还是Update
        String sqlMisDel="delete from b_dcs_me_station_mis " +
                "where station_code='"+station_code+"'";
        cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlMisDel,false,null,"");

        //7.新增
        String sqlMisIns="insert into b_dcs_me_station_mis " +
                "(user_name,arrive_date,station_code,station_des,station_attr," +
                "station_status,task_num,task_from,serial_num,lot_num," +
                "pallet_num,pallet_use_count,pallet_max_count,pallet_alarm_flag," +
                "material_code,material_des,material_draw,model_type," +
                "m_length,m_width,m_height,m_weight,m_texture,cut_texture," +
                "online_time,serial_stay_time,ideal_beats,actual_beats," +
                "shift_code,shift_des,from_station_code,dxf_name,json_name,nc_name) values " +
                "('"+user_name+"','"+arrive_date2+"','"+station_code+"','"+station_des+"','"+station_attr+"'," +
                "'ARRIVE','"+task_num+"','"+task_from+"','"+serial_num+"','"+lot_num+"'," +
                "'"+pallet_num+"',"+pallet_use_count+","+pallet_max_count+",'"+pallet_alarm_flag+"'," +
                "'"+material_code+"','"+material_des+"','"+material_draw+"','"+model_type+"'," +
                ""+m_length+","+m_width+","+m_height+","+m_weight+",'"+m_texture+"','"+cut_texture+"'," +
                "'"+online_time+"',"+serial_stay_time+","+ideal_beats+",0," +
                "'"+shift_code+"','"+shift_des+"','"+from_station_code+"','"+dxf_name+"','"+json_name+"','"+nc_name+"')";
        cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlMisIns,false,null,"");

        //8.修改任务当前工位
        if(!mo_id.equals("")){
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(mo_id));
            Update updateBigData = new Update();
            updateBigData.set("now_station_code", station_code);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsTaskTable);
        }

        //9.如果当前工位是G16并且状态是ARRIVE，则该托盘使用次数+1
        String sqlUpdate = "update b_dcs_fmod_pallet set usage_times=(usage_times+1) where usage_times < maintenance_value and pallet_num = (SELECT fp.pallet_num FROM b_dcs_me_station_mis sm inner join b_dcs_fmod_pallet fp on fp.pallet_num=sm.pallet_num where sm.station_code = 'G16' and sm.station_status = 'ARRIVE')";
        cFuncDbSqlExecute.ExecUpdateSql(user_name, sqlUpdate, false, null, "");

        //10.查使用次数是否达到维修值，如果达到维修值则插入event表记录，提示需要维修
        String palletnumSql = "select pallet_num from b_dcs_fmod_pallet where usage_times = maintenance_value";
        List<Map<String, Object>> rs = cFuncDbSqlExecute.ExecSelectSql("",palletnumSql, false, null, "");
        if (rs.size() > 0) {
            StringBuffer eventMsg = new StringBuffer();
            eventMsg.append(String.valueOf(rs.get(0).get("pallet_num"))).append(" 托盘已达到托盘维修值 请使用半自动模式将托盘输送到G11工位进行维修");
            Map<String, Object> event = new HashMap();
            event.put("event_msg", eventMsg.toString());
            event.put("item_date", CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
            event.put("item_date_val", CFuncUtilsSystem.GetMongoDataValue(CFuncUtilsSystem.GetMongoISODate("")));
            JSONObject js = new JSONObject();
            js.put("pallet_num",rs.get(0).get("pallet_num"));
            event.put("event_data", js.toJSONString());
            event.put("event_type", "1001");
            event.put("handle_flag", "N");
            event.put("id", UUID.randomUUID().toString().replace("-", ""));
            event.put("task_num","STATIONG16"+currentTimeMillis);
            mongoTemplate.insert(event, dcsFlowEvent);
        }
    }

    //3.工件离开更新MIS信息
    public void MisLeaveUpdate(String leave_date,String station_code,Long actual_beats) throws Exception{
        String leave_date2=leave_date;
        if(leave_date2==null || leave_date2.equals("")) leave_date2=CFuncUtilsSystem.GetNowDateTime("");
        String sqlMisUpd="update b_dcs_me_station_mis set " +
                "station_status='LEAVE'," +
                "leave_date='"+leave_date2+"'," +
                "actual_beats="+actual_beats+" " +
                "where station_code='"+station_code+"'";
        cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlMisUpd,false,null,"");
    }
}
