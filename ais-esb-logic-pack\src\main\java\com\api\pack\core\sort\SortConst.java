package com.api.pack.core.sort;

import com.api.base.Const;
import com.api.pack.core.board.BoardConst;
import com.api.pack.core.ccd.CCDConst;

import java.util.Arrays;
import java.util.List;

public interface SortConst
{
    String BLANK = Const.BLANK;

    String ACTION_CCD_COMPARE = "ccd_compare"; // 线扫分选比对
    String ACTION_PILE_VERIFY = "pile_verify"; // 堆叠校验

    String BOARD_FRONT = CCDConst.FRONT.toLowerCase();
    String BOARD_BACK = CCDConst.BACK.toLowerCase();

    String BOARD_CATEGORY_SET = BoardConst.SET; // 板件类型 Set
    String BOARD_CATEGORY_PCS = BoardConst.PCS; // 板件类型 Pcs

    String BOARD_SIDE_NONE = BoardConst.NONE;
    String BOARD_SIDE_SINGLE = BoardConst.SINGLE;
    String BOARD_SIDE_DOUBLE = BoardConst.DOUBLE;

    String LEVEL = "LevelSort";
    String SET_LEVEL = BOARD_CATEGORY_SET + LEVEL;
    String PCS_LEVEL = BOARD_CATEGORY_PCS + LEVEL;
    String X_OUT = "XoutSort";
    String CODE_XOUT_NP = "XoutNPSort";
    String CYCLE_PERIOD = "CyclePeriodSort";
    String QR_CYCLE_PERIOD = "QR" + CYCLE_PERIOD;
    String BATCH_NUMBER = "BatchNumSort";
    String QR_BATCH_NUMBER = "QR" + BATCH_NUMBER;
    String BOARD_MAPPING = "CheckMapping";
    String BOARD_F2B = "FrontAndBackSort";
    String BOARD_DIRECTION = "BoardDirectionSort"; // 板件方向比对
    String READ_DURATION = "ReadDurationSort"; // 读取时长比对
    String MULTI_CODE = "MultiCodeSort"; // 重码比对
    String PCS_INDEX = "PcsIndexSort"; // PCS序号比对
    String BAR_LENGTH = "BarLengthSort"; // 条码长度比对
    String BAR_CASE = "BarCaseSort"; // 条码大小写比对
    String DIR_MARK = "DirMarkSort"; // 光学点比对
    String ET_PASS = "ETPassSort"; // 电测通过比对
    List<String> NON_DEFAULT = Arrays.asList(X_OUT, CODE_XOUT_NP, SET_LEVEL, PCS_LEVEL, BOARD_DIRECTION, MULTI_CODE, PCS_INDEX, BAR_LENGTH, BAR_CASE, ET_PASS);

    String BAR_CASE_V_UPPER = "Upper";
    String BAR_CASE_V_LOWER = "Lower";

    String COMPARE_FUNC_EQUAL = "="; // 比对函数 等于
    String COMPARE_FUNC_NOT_EQUAL = "!="; // 比对函数 不等于
    String COMPARE_FUNC_GREATER = ">"; // 比对函数 大于
    String COMPARE_FUNC_GREATER_EQUAL = ">="; // 比对函数 大于等于
    String COMPARE_FUNC_LESS = "<"; // 比对函数 小于
    String COMPARE_FUNC_LESS_EQUAL = "<="; // 比对函数 小于等于
    String COMPARE_FUNC_CONTAINS = "contains"; // 比对函数 包含
    String COMPARE_FUNC_NOT_CONTAINS = "not_contains"; // 比对函数 不包含

    String COMPARE_MESSAGES_PACK_CCD_DATA_ERROR = "pack.ccdDataError"; // CCD数据错误
    String COMPARE_MESSAGES_PACK_PCS_COUNT_ERROR = "pack.pcsCountError"; // PCS数量错误
    String COMPARE_MESSAGES_PACK_FORMAT_ERROR = "pack.formatError"; // 格式错误
    String COMPARE_MESSAGES_PACK_UNCONFORMITY = "pack.unconformity"; // 不一致
    String COMPARE_MESSAGES_PACK_UNCONFORMITY_WITH_VALUES = "pack.unconformityWithValues"; // 不一致（携带值）
    String COMPARE_MESSAGES_PACK_NO_DETECTED = "pack.noDetected"; // 未检测到
    String COMPARE_MESSAGES_PACK_NOT_MATCH = "pack.notMatch"; // 不匹配
    String COMPARE_MESSAGES_PACK_NOT_MATCH_ALL = "pack.notMatchAll"; // 完全不匹配
    String COMPARE_MESSAGES_PACK_NOT_CONFIGURED = "pack.notConfigured"; // 未配置
    String COMPARE_MESSAGES_PACK_COMPARE_RULES = "pack.compareRules"; // 比对规则
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_F2B = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + BOARD_F2B; // 前后比对
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_F2B_NOT_MATCH = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + BOARD_F2B + ".notMatch"; // 前后比对不匹配
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XNP = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + CODE_XOUT_NP; // X数/位
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + X_OUT + ".notMatch";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_FOR_FRONT = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + X_OUT + ".notMatchForFront";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_FOR_BACK = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + X_OUT + ".notMatchForBack";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_MARK_IS_OK = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + X_OUT + ".markIsOk";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_MARK_IS_NG = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + X_OUT + ".markIsNG";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_OF_PCS = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + X_OUT + ".notMatchOfPcs";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_FOR_ACTUAL = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + X_OUT + ".notMatchForActual";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_NOT_MATCH_FOR_RULE = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + X_OUT + ".notMatchForRule";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_XOUT_RULE_IS_INVALID = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + X_OUT + ".ruleIsInvalid";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_LEVEL_NOT_MATCH = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + LEVEL + ".notMatch";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_LEVEL_GREATER_THAN_STAND = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + LEVEL + ".greaterThanStand"; // 二维码等级大于设定等级
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_DIRECTION_NOT_MATCH = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + BOARD_DIRECTION + ".notMatch";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_BARCODE_LENGTH_NOT_MATCH = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + BAR_LENGTH + ".notMatch";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_BARCODE_LENGTH_NOT_MATCH_FOR_RULE = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + BAR_LENGTH + ".notMatchForRule";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_BARCODE_CASE_NOT_MATCH_FOR_RULE = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + BAR_CASE + ".notMatchForRule";
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_BARCODE_REPEATED = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + MULTI_CODE + ".repeated"; // 条码重复
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_DIR_MARK_IS_INVALID = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + DIR_MARK + ".statusIsNG"; // 光学点状态NG
    String COMPARE_MESSAGES_PACK_COMPARE_RULES_ET_PASS_NOT_MATCH_FOR_RULE = COMPARE_MESSAGES_PACK_COMPARE_RULES + "." + ET_PASS + ".notMatch"; // 电测通过比对不匹配

    String CCD_CONTENT_SET_CUSTOMER_QRC1 = CCDConst.CUSTOMER_QRC1;
    String CCD_CONTENT_SET_CUSTOMER_QRC2 = CCDConst.CUSTOMER_QRC2;
    String CCD_CONTENT_SET_PCS_MSG_LIST = CCDConst.PCS_MSG_LIST;
    String CCD_CONTENT_SET_X_OUT_QTY = CCDConst.XOUT_QTY;
    String CCD_CONTENT_ALL_IS_X_OUT = CCDConst.IS_XOUT;
    String CCD_CONTENT_ALL_MARK = CCDConst.MARK;
    String CCD_CONTENT_ALL_HAS_DIR_MARK = CCDConst.HAS_DIR_MARK;
    String CCD_CONTENT_ALL_QRC = CCDConst.QRC;
    String CCD_CONTENT_ALL_QRC_LEVEL = CCDConst.QRC_LEVEL;
    String CCD_CONTENT_ALL_DIRECTION = CCDConst.DIRECTION;

    String PROPERTY_SORT_FLAG = "sort_flag";
    String PROPERTY_SORT_INDEX = "sort_index";

    String STATUS_OK = BoardConst.STATUS_OK;
    String STATUS_NG = BoardConst.STATUS_NG;

    int CODE_NG = BoardConst.CODE_NG;
    int CODE_OK = BoardConst.CODE_OK;

    String[] SET_BOARD_INITIAL_PROPERTIES = BoardConst.SET_BOARD_INITIAL_PROPERTIES;

    String LEVEL_A = "A";
    String LEVEL_B = "B";
    String LEVEL_C = "C";
    String LEVEL_D = "D";
    String LEVEL_E = "E";
    String LEVEL_F = "F";

    String VALUE_NO_READ = "NoRead";
    String VALUE_NC = "@NC@";
    String VALUE_NG = "@NG@";
    String VALUE_NULL = "@NULL@";
    String VALUE_NULL_1 = "@NULL1@";
    String VALUE_NULL_2 = "@NULL2@";
    String VALUE_NULL_3 = "@NULL3@";
    String VALUE_NULL_4 = "@NULL4@";

    static String convertValue(String value)
    {
        value = value != null ? value.trim() : BoardConst.BLANK;
        boolean isValid = !VALUE_NO_READ.equals(value) // NoRead
                && !VALUE_NULL.equals(value) // @NULL@
                && !VALUE_NULL_1.equals(value) // @NULL1@
                && !VALUE_NULL_2.equals(value) // @NULL2@
                && !VALUE_NULL_3.equals(value) // @NULL3@
                && !VALUE_NULL_4.equals(value) // @NULL4@
                && !VALUE_NC.equals(value); // @NC@
        if (isValid)
        {
            return value;
        }
        return BoardConst.BLANK;
    }
}
