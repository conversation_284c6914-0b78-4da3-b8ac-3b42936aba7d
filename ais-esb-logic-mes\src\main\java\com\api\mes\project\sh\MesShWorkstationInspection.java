package com.api.mes.project.sh;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

@RestController
@Slf4j
@RequestMapping("/mes/project/sh")
public class MesShWorkstationInspection {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    //双环校验工件在上一工位是否合格
    @RequestMapping(value = "/MesShWorkstationInspection", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesShWorkstationInspection(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/sh/MesShWorkstationInspection";
        String transResult = "";
        String errorMsg = "";
        String userName="-1";
        try {
            Query query = new Query();
            String serial_num = jsonParas.getString("serial_num");
            String station_code = jsonParas.getString("station_code");
            if (serial_num != null && !serial_num.equals("")) {
                Query querySH = new Query();
                querySH.addCriteria(Criteria.where("serial_num").regex(serial_num));
                MongoCursor<Map> iteratorSH = mongoTemplate.getCollection("c_mes_me_station_material").find(querySH.getQueryObject(), Map.class).sort(querySH.getSortObject()).noCursorTimeout(true).batchSize(1000).iterator();
                String exact_barcode_num_list = "";
                while (iteratorSH.hasNext()) {
                    Map map = iteratorSH.next();
                    map.put("id", map.get("_id").toString());
                    if(map.get("exact_barcode").toString() == null || map.get("exact_barcode").toString().equals(""))continue;
                    String exact_barcode_num=map.get("exact_barcode").toString();
                    if (!exact_barcode_num_list.contains(exact_barcode_num + ",")) {
                        exact_barcode_num_list += exact_barcode_num + ",";
                    }
                }
                exact_barcode_num_list += serial_num;
                query.addCriteria(Criteria.where("serial_num").in(exact_barcode_num_list.split(",")));
            }
            if (station_code == "RAL01-OP250"){
                query.addCriteria(Criteria.where("quality_sign").is("OK"));
                query.addCriteria(Criteria.where("enable_flag").is("Y"));
                long allCount = mongoTemplate.getCollection("c_mes_me_station_flow").countDocuments(query.getQueryObject());
                if (allCount>20){
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
                    return transResult;
                }else {
                    errorMsg = "工位信息缺失或不合格" ;
                    transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return transResult;
                }
            }
            String sql="SELECT " +
                    "attribute1 " +"FROM " +"sys_fmod_station  " +"WHERE " +"station_code = '"+station_code+"'";
            List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(userName, sql,true,request,apiRoutePath);
            String attribute1 = itemList.get(0).get("attribute1").toString();
            if(attribute1 == null || attribute1.equals("")){
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
                return transResult;
            }
            query.addCriteria(Criteria.where("station_code").in(attribute1.split(",")));
            query.addCriteria(Criteria.where("quality_sign").is("OK"));
            query.addCriteria(Criteria.where("enable_flag").is("Y"));

            long allCount = mongoTemplate.getCollection("c_mes_me_station_flow").countDocuments(query.getQueryObject());
            if (allCount>=attribute1.split(",").length){
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", allCount);
            }else {
                errorMsg = "工位信息缺失" ;
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            }

        } catch (Exception ex) {
            errorMsg = "工位信息缺失" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
    //双环选垫片
    @RequestMapping(value = "/MesShWorkRelInsInspectionShim", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesShWorkRelInsInspectionShim(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/project/sh/MesShWorkRelInsInspectionShim";
        String transResult = "";
        String errorMsg = "";
        String userName="-1";
        try {
            String serial_num = jsonParas.getString("serial_num");
            String station_code = jsonParas.getString("station_code");
            String shim1 = jsonParas.getString("shim1");
            String shim2 = jsonParas.getString("shim2");
            String shim3 = jsonParas.getString("shim3");
            if (shim1 == null || shim1.equals("") || shim2 == null || shim2.equals("")) {
                errorMsg = "等号{" + shim1 + shim2 + "}不能为空";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }
            if(shim3 == null || shim3.equals("") ){
                String sql = "SELECT material_code FROM c_mes_fmod_small_model_bom c " +
                        "INNER  JOIN sys_fmod_station s ON s.station_id=c.station_id " +
                        "WHERE s.station_code = '"+station_code+"' AND c.attribute3 != '' " +
                        "AND material_id NOT IN ( SELECT material_id FROM c_mes_fmod_small_model_bom WHERE attribute3 = '"+shim1+"' OR attribute3 = '"+shim2+"') ";
                List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(userName, sql,true,request,apiRoutePath);
                if(itemList != null && itemList.size()>0){
                    for (int i=0;i<itemList.size();i++){
                        String material_code = itemList.get(i).get("material_code").toString();
                        Query query = new Query();
                        query.addCriteria(Criteria.where("material_code").is(material_code));
                        query.addCriteria(Criteria.where("serial_num").is(serial_num));
                        query.addCriteria(Criteria.where("station_code").is(station_code));
                        mongoTemplate.remove(query, "c_mes_me_station_material");
                        sql = "SELECT used_count  " +
                                "FROM c_mes_me_batch set " +
                                "where material_code='" + material_code + "' " +
                                "and station_code='" + station_code + "' "+
                                "and batch_id IN ( SELECT MIN ( batch_id ) AS batch_id FROM c_mes_me_batch  WHERE station_code = '" + station_code + "' GROUP BY material_code ) ";
                        List<Map<String, Object>> itemList1=cFuncDbSqlExecute.ExecSelectSql(userName, sql,true,request,apiRoutePath);
                        int used_count = Integer.parseInt(itemList1.get(0).get("used_count").toString())-1;
                        String sqlUpdatePdureBom = "update c_mes_me_batch set " +
                                "used_count="+used_count+" " +
                                "where material_code='" + material_code + "' " +
                                "and station_code='" + station_code + "' "+
                                "and batch_id IN ( SELECT MIN ( batch_id ) AS batch_id FROM c_mes_me_batch  WHERE station_code = '" + station_code + "' GROUP BY material_code ) ";
                        cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdatePdureBom, false, request, apiRoutePath);
                    }
                }
            }else {
                String sql = "SELECT material_code FROM c_mes_fmod_small_model_bom c " +
                        "INNER  JOIN sys_fmod_station s ON s.station_id=c.station_id " +
                        "WHERE s.station_code = '"+station_code+"' AND c.attribute3 != '' " +
                        "AND material_id NOT IN ( SELECT material_id FROM c_mes_fmod_small_model_bom WHERE attribute3 = '"+shim1+"' OR attribute3 = '"+shim2+"' OR attribute3 = '"+shim3+"') ";
                List<Map<String, Object>> itemList=cFuncDbSqlExecute.ExecSelectSql(userName, sql,true,request,apiRoutePath);
                if(itemList != null && itemList.size()>0){
                    for (int i=0;i<itemList.size();i++){
                        String material_code = itemList.get(i).get("material_code").toString();
                        Query query = new Query();
                        query.addCriteria(Criteria.where("material_code").is(material_code));
                        query.addCriteria(Criteria.where("serial_num").is(serial_num));
                        query.addCriteria(Criteria.where("station_code").is(station_code));
                        mongoTemplate.remove(query, "c_mes_me_station_material");
                        sql = "SELECT used_count  " +
                                "FROM c_mes_me_batch set " +
                                "where material_code='" + material_code + "' " +
                                "and station_code='" + station_code + "' "+
                                "and batch_id IN ( SELECT MIN ( batch_id ) AS batch_id FROM c_mes_me_batch  WHERE station_code = '" + station_code + "' GROUP BY material_code ) ";
                        List<Map<String, Object>> itemList1=cFuncDbSqlExecute.ExecSelectSql(userName, sql,true,request,apiRoutePath);
                        int used_count = Integer.parseInt(itemList1.get(0).get("used_count").toString())-1;
                        String sqlUpdatePdureBom = "update c_mes_me_batch set " +
                                "used_count="+used_count+" " +
                                "where material_code='" + material_code + "' " +
                                "and station_code='" + station_code + "' "+
                                "and batch_id IN ( SELECT MIN ( batch_id ) AS batch_id FROM c_mes_me_batch  WHERE station_code = '" + station_code + "' GROUP BY material_code ) ";
                        cFuncDbSqlExecute.ExecUpdateSql(station_code, sqlUpdatePdureBom, false, request, apiRoutePath);
                    }
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "垫片绑定失败" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
