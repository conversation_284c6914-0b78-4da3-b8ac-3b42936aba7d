package com.api.pack.core.plan;

import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.IMongoBasic;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * Plan
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-12
 */
@ApiModel(value = "Plan", description = "任务计划")
@Document("a_pack_aps_plan")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class Plan extends IMongoBasic
{
    private static final long serialVersionUID = 1L;

    @Deprecated
    @ApiModelProperty(value = "计划ID")
    @JsonProperty("plan_id")
    @JSONField(name = "plan_id")
    @Field("plan_id")
    private String planId;

    @ApiModelProperty(value = "任务来源")
    @JsonProperty("task_from")
    @JSONField(name = "task_from")
    @Field("task_from")
    private String taskFrom;

    @ApiModelProperty(value = "任务类型")
    @JsonProperty("task_type")
    @JSONField(name = "task_type")
    @Field("task_type")
    private String taskType;

    @ApiModelProperty(value = "任务开始时间")
    @JsonProperty("task_start_time")
    @JSONField(name = "task_start_time")
    @Field("task_start_time")
    private String taskStartTime;

    @ApiModelProperty(value = "任务结束时间")
    @JsonProperty("task_end_time")
    @JSONField(name = "task_end_time")
    @Field("task_end_time")
    private String taskEndTime;

    @ApiModelProperty(value = "任务消耗时间")
    @JsonProperty("task_cost_time")
    @JSONField(name = "task_cost_time")
    @Field("task_cost_time")
    private String taskCostTime;

    @ApiModelProperty(value = "工厂编码")
    @JsonProperty("plant_code")
    @JSONField(name = "plant_code")
    @Field("plant_code")
    private String plantCode;

    @ApiModelProperty(value = "工厂订单号")
    @JsonProperty("plant_order_num")
    @JSONField(name = "plant_order_num")
    @Field("plant_order_num")
    private String plantOrderNum;

    @ApiModelProperty(value = "LOT号")
    @JsonProperty("lot_num")
    @JSONField(name = "lot_num")
    @Field("lot_num")
    private String lotNum;

    @ApiModelProperty(value = "LOT状态")
    @JsonProperty("lot_status")
    @JSONField(name = "lot_status")
    @Field("lot_status")
    private String lotStatus; // PLAN、WORK、FINISH、CANCEL等

    @ApiModelProperty(value = "料号")
    @JsonProperty("model_type")
    @JSONField(name = "model_type")
    @Field("model_type")
    private String partNumber;

    @ApiModelProperty(value = "料号版本")
    @JsonProperty("model_version")
    @JSONField(name = "model_version")
    @Field("model_version")
    private String partVersion;

    @ApiModelProperty(value = "SET计划板件数量")
    @JsonProperty("plan_lot_count")
    @JSONField(name = "plan_lot_count")
    @Field("plan_lot_count")
    private Integer setBoardPlanNumber;

    @ApiModelProperty(value = "SET单包数量")
    @JsonProperty("unit_count")
    @JSONField(name = "unit_count")
    @Field("unit_count")
    private Integer setBoardUnitNumber;

    @ApiModelProperty(value = "SET板件类型")
    @JsonProperty("array_type")
    @JSONField(name = "array_type")
    @Field("array_type")
    private String setBoardType;

    @ApiModelProperty(value = "PCS板件类型")
    @JsonProperty("bd_type")
    @JSONField(name = "bd_type")
    @Field("bd_type")
    private String pcsBoardType;

    @ApiModelProperty(value = "SET板件长度(mm)")
    @JsonProperty("m_length")
    @JSONField(name = "m_length")
    @Field("m_length")
    private Integer setBoardLength;

    @ApiModelProperty(value = "SET板件宽度(mm)")
    @JsonProperty("m_width")
    @JSONField(name = "m_width")
    @Field("m_width")
    private Integer setBoardWidth;

    @ApiModelProperty(value = "SET板件厚度(mm)")
    @JsonProperty("m_tickness")
    @JSONField(name = "m_tickness")
    @Field("m_tickness")
    private Integer setBoardThickness;

    @ApiModelProperty(value = "SET板件重量(g)")
    @JsonProperty("m_weight")
    @JSONField(name = "m_weight")
    @Field("m_weight")
    private Integer setBoardWeight;

    @ApiModelProperty(value = "周期")
    @JsonProperty("cycle_period")
    @JSONField(name = "cycle_period")
    @Field("cycle_period")
    private String cyclePeriod;

    @ApiModelProperty(value = "销售订单号")
    @JsonProperty("sales_order")
    @JSONField(name = "sales_order")
    @Field("sales_order")
    private String salesOrder;

    @ApiModelProperty(value = "销售订单行")
    @JsonProperty("sales_item")
    @JSONField(name = "sales_item")
    @Field("sales_item")
    private String salesItem;

    @ApiModelProperty(value = "销售组织")
    @JsonProperty("sales_org")
    @JSONField(name = "sales_org")
    @Field("sales_org")
    private String salesOrg;

    @ApiModelProperty(value = "销售类型")
    @JsonProperty("sales_type")
    @JSONField(name = "sales_type")
    @Field("sales_type")
    private String salesType;

    @ApiModelProperty(value = "终端客户PN")
    @JsonProperty("custom_pn")
    @JSONField(name = "custom_pn")
    @Field("custom_pn")
    private String customPn;

    @ApiModelProperty(value = "终端客户订单")
    @JsonProperty("custom_po")
    @JSONField(name = "custom_po")
    @Field("custom_po")
    private String customPo;

    @ApiModelProperty(value = "终端客户编码")
    @JsonProperty("custom_code")
    @JSONField(name = "custom_code")
    @Field("custom_code")
    private String customCode;

    @ApiModelProperty(value = "终端客户名称")
    @JsonProperty("custom_name")
    @JSONField(name = "custom_name")
    @Field("custom_name")
    private String customName;

    @ApiModelProperty(value = "截取批次号")
    @JsonProperty("split_lot")
    @JSONField(name = "split_lot")
    @Field("split_lot")
    private String splitLot;

    @ApiModelProperty(value = "截取料号")
    @JsonProperty("split_model")
    @JSONField(name = "split_model")
    @Field("split_model")
    private String splitModel;

    @ApiModelProperty(value = "完成OK数量")
    @JsonProperty("finish_ok_count")
    @JSONField(name = "finish_ok_count")
    @Field("finish_ok_count")
    private Integer finishOkCount;

    @ApiModelProperty(value = "完成NG数量")
    @JsonProperty("finish_ng_count")
    @JSONField(name = "finish_ng_count")
    @Field("finish_ng_count")
    private Integer finishNgCount;

    @ApiModelProperty(value = "完成打包数量")
    @JsonProperty("finish_pile_count")
    @JSONField(name = "finish_pile_count")
    @Field("finish_pile_count")
    private Integer finishPileCount;

    @ApiModelProperty(value = "打包次数")
    @JsonProperty("finish_lable_count")
    @JSONField(name = "finish_lable_count")
    @Field("finish_lable_count")
    private Integer finishLableCount;


    @ApiModelProperty(value = "当前任务选择的配方参数集合")
    @JsonProperty("recipe_paras")
    @JSONField(name = "recipe_paras")
    @Field("recipe_paras")
    private String recipeParas;

    @ApiModelProperty(value = "当前任务选择的分选参数集合")
    @JsonProperty("sort_paras")
    @JSONField(name = "sort_paras")
    @Field("sort_paras")
    private String sortParas;

    @ApiModelProperty(value = "预留属性1")
    @JsonProperty("attribute1")
    @JSONField(name = "attribute1")
    @Field("attribute1")
    private String attribute1;

    @ApiModelProperty(value = "预留属性2")
    @JsonProperty("attribute2")
    @JSONField(name = "attribute2")
    @Field("attribute2")
    private String attribute2;

    @ApiModelProperty(value = "预留属性3")
    @JsonProperty("attribute3")
    @JSONField(name = "attribute3")
    @Field("attribute3")
    private String attribute3;

    @ApiModelProperty(value = "出货地址")
    @JsonProperty("ship_address")
    @JSONField(name = "ship_address")
    @Field("ship_address")
    private String shipAddress;

    @ApiModelProperty(value = "批号")
    @JsonProperty("batch_no")
    @JSONField(name = "batch_no")
    @Field("batch_no")
    private String batchNumber;

    @ApiModelProperty(value = "镭射批号")
    @JsonProperty("laser_batch_no")
    @JSONField(name = "laser_batch_no")
    @Field("laser_batch_no")
    private String laserBatchNumber;

    @ApiModelProperty(value = "排版数")
    @JsonProperty("typesetting_no")
    @JSONField(name = "typesetting_no")
    @Field("typesetting_no")
    private Integer layoutNum;

    @ApiModelProperty(value = "客戶料号")
    @JsonProperty("customer_mn")
    @JSONField(name = "customer_mn")
    @Field("customer_mn")
    private String customerMn;

    @ApiModelProperty(value = "标签到期日")
    @JsonProperty("label_due_date")
    @JSONField(name = "label_due_date")
    @Field("label_due_date")
    private String labelDueDate;

    @ApiModelProperty(value = "尾包数量")
    @JsonProperty("tail_count")
    @JSONField(name = "tail_count")
    @Field("tail_count")
    private Integer tailCount;

    @ApiModelProperty(value = "物料")
    @JsonProperty("materials")
    @JSONField(name = "materials")
    @Field("materials")
    private Map<String, Material> materials;

    @ApiModelProperty(value = "捆包")
    @JsonProperty("bundles")
    @JSONField(name = "bundles")
    @Field("bundles")
    private List<Bundle> bundles;

    public static Plan byId(String id, PlanService planService)
    {
        return planService.findById(id).orElse(null);
    }

    public static Plan current(PlanService planService)
    {
        return planService.getByLotStatus(PlanConst.STATUS_WORK);
    }

    public String getPlanId()
    {
        if (this.planId == null && this.getId() != null)
        {
            this.planId = this.getId();
        }
        return this.planId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Material implements Serializable
    {
        private String code; // 物料编码

        private String unit; // 物料单位

        private Integer qty; // 物料数量

        private String desc; // 物料描述
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Bundle implements Serializable
    {
        private Integer index; // 捆包序号
        private String barcode; // 捆包标签
        private Integer qty; // 捆包数量
        private boolean last; // 是否最后一捆包
    }
}
