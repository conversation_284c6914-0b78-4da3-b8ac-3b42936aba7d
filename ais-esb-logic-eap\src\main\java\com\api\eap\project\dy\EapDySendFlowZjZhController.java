package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.OpStaticElements;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 定颖EAP(载具转换)发送流程数据定义接口
 * 1.PortStatusChangeReport:[接口]端口状态发生变化推送到EAP
 * 2.CarrierStatusReport:[接口]开始/结束/取消/终止投板（收板）时上报
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-10
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/dy/interf/send")
public class EapDySendFlowZjZhController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDySendFlowFunc eapDySendFlowFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;

    //1.[接口]端口状态发生变化推送到EAP
    @RequestMapping(value = "/ZjZhPortStatusChangeReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyZjZhPortStatusChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/ZjZhPortStatusChangeReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String port_status = jsonParas.getString("port_status");//UDCM、LDRQ、WAIT、PROC、ABOT、CANE、PREN、UDRQ、
            String pallet_num = jsonParas.getString("pallet_num");
            Integer left_count = jsonParas.getInteger("left_count");
            String eqp_job_type = jsonParas.getString("eqp_job_type");//FCL、ECL、ECR、FCU、FCR、ECRFCL、FCUECL
            if (eqp_job_type == null) eqp_job_type = "";

            if (pallet_num == null) pallet_num = "";
            if (left_count == null) left_count = 0;

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = "UnLoad";
            String allow_sb_emptypallet_flag = "N";
            String user_name = "";
            String port_ng_flag = "N";
            String pallet_reload_flag = "N";
            if (port_status.equals("LDRQ")) pallet_reload_flag = "Y";
            if (port_status.equals("UDCM") || port_status.equals("LDRQ")) {
                pallet_num = "";
                left_count = 0;
            }
            if(port_status.equals("LDCM")){
                if(station_attr.equals("Load")) eqp_job_type="FCL";
                else eqp_job_type="ECL";
            }

            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //端口状态上报
            eapDySendFlowFunc.ZjZhPortStatusChangeReport(station_code, port_code, "Load", port_status, allow_sb_emptypallet_flag, pallet_num, String.valueOf(left_count), user_name, port_ng_flag, pallet_reload_flag,eqp_job_type,station_attr);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "端口状态上报EAP异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]载具扫描上报验证
    @RequestMapping(value = "/ZjZhCarrierIDReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyZjZhCarrierIDReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/ZjZhCarrierIDReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String mePalletQueueTable = "a_eap_me_pallet_queue";
        try {
            String result = "OK";
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String production_mode = jsonParas.getString("production_mode");//制程分类
            String ccdNo = jsonParas.getString("ccd_no");
            if (production_mode == null || production_mode.equals("")) production_mode = "Default";
            String pre_pallet_scan = jsonParas.getString("pre_pallet_scan");//是否提前扫描载具
            if (pre_pallet_scan == null || pre_pallet_scan.equals("")) pre_pallet_scan = "N";
            String read_type = jsonParas.getString("read_type");
            String eqp_job_type = jsonParas.getString("eqp_job_type");//FCL、ECL、ECR、FCU、FCR、ECRFCL、FCUECL
            if (eqp_job_type == null) eqp_job_type = "";
            Boolean isNewVersion=eapDyInterfCommon.CheckDyVersion(3);

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = "UnLoad";
            String user_name = "";

            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            String lot_infos_strs="";
            JSONObject response_body2 = eapDySendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, station_attr, 0, production_mode, "N", null, "Y", read_type);
            if (response_body2 == null) result = "NG";
            else{
                if(isNewVersion){
                    if (response_body2.containsKey("lot_infos")) {
                        JSONObject lot_infos = response_body2.getJSONObject("lot_infos");
                        if (lot_infos != null) {
                            if (lot_infos.containsKey("lot")) {
                                JSONArray lot_list = lot_infos.getJSONArray("lot");
                                if (lot_list != null && lot_list.size() > 0) {
                                    JSONArray new_lot_list=new JSONArray();
                                    for(int i=0;i<lot_list.size();i++){
                                        JSONObject jbItemAttr=lot_list.getJSONObject(i);
                                        String lot_id=jbItemAttr.getString("lot_id");
                                        String lot_qty=jbItemAttr.getString("lot_qty");
                                        JSONObject jbItemAttrNew=new JSONObject();
                                        jbItemAttrNew.put("lot_id",lot_id);
                                        jbItemAttrNew.put("lot_count",lot_qty);
                                        new_lot_list.add(jbItemAttrNew);
                                    }
                                    lot_infos_strs=new_lot_list.toString();
                                    result=result+"|"+lot_infos_strs;
                                }
                            }
                        }
                    }
                }
            }

            //成功后发送LDCM
            String eqp_job_type3="ECL";
            if(station_attr.equals("UnLoad")) eqp_job_type3="ECL";
            eapDySendFlowFunc.ZjZhPortStatusChangeReport(station_code, port_code, "Load", "LDCM", "N", pallet_num, "0", user_name, "N", "N",eqp_job_type3,station_attr);

            //1.CCDDataReport:[接口]CCD读码上报
            eapDySendFlowFunc.CCDDataReport(station_code, pallet_num, ccdNo);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "载具扫描上报验证异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.[接口]开始/结束/取消/终止上报
    @RequestMapping(value = "/ZjZhCarrierStatusReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyZjZhCarrierStatusReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/ZjZhCarrierStatusReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String task_status = jsonParas.getString("task_status");
            String pallet_num = jsonParas.getString("pallet_num");
            String lot_list_str = jsonParas.getString("lot_list");
            String group_lot_num=jsonParas.getString("group_lot_num");
            String manual_wip_flag=jsonParas.getString("manual_wip_flag");
            if(group_lot_num==null) group_lot_num="";
            if(manual_wip_flag==null) manual_wip_flag="";
            String sqlStation = "select station_id," + "COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, request, apiRoutePath);
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            Long station_id_long=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            JSONArray lot_list = JSONArray.parseArray(lot_list_str);
            eapDySendFlowFunc.ZjZhCarrierStatusReport(station_code, pallet_num, task_status, lot_list, "0",
                    station_attr, port_code,manual_wip_flag);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "开始/结束/取消/终止上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.判断是否存在收板任务并且选择(载具转换)
    @RequestMapping(value = "/ZjZhEapUnLoadPlanExistJudgeAndSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyZjZhUnLoadPlanExistJudgeAndSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/ZjZhEapUnLoadPlanExistJudgeAndSel";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        OpStaticElements.unLoadPlanCountUpdLock.lock();
        try {
            String station_id = jsonParas.getString("station_id");
            String pallet_num = jsonParas.getString("pallet_num");
            String port_code = jsonParas.getString("port_code");
            if (pallet_num == null) pallet_num = "";
            long station_id_long = Long.parseLong(station_id);

            //先读取是否为一批多车模式
            //1.查询工位信息,并且针对一车多批不做处理
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation, false, request, apiRoutePath);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String OneCarMultyLotFlag = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr,
                    "Ais", "AisConfig", "OneCarMultyLotFlag");
            if (OneCarMultyLotFlag == null || OneCarMultyLotFlag.equals("")) {
                OpStaticElements.unLoadPlanCountUpdLock.unlock();
                errorMsg = "读取Ais参数{OneCarMultyLotFlag}为空";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }

            //查询
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = null;
            MongoCursor<Document> iteratorBigData = null;
            if (!OneCarMultyLotFlag.equals("2")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                }
            } else {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                } else {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                    queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                    queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                    iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                            sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        Document docItemBigData = iteratorBigData.next();
                        group_id = docItemBigData.getString("group_id");
                        iteratorBigData.close();
                    }
                }
            }

            //继续查询
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    String new_pallet_num = docItemBigData.getString("pallet_num");
                    if (!pallet_num.equals("")) new_pallet_num = pallet_num;
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", new_pallet_num);
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();

                //修改端口号
                Update updateBigData = new Update();
                updateBigData.set("group_lot_status", "WORK");
                if (OneCarMultyLotFlag.equals("2")) {
                    //创建一个虚拟的载具ID,防止存在重复的载具ID导致找不到台车比对数据
                    String virtu_pallet_num = CFuncUtilsSystem.CreateUUID(true);
                    updateBigData.set("pallet_num", pallet_num);
                    updateBigData.set("virtu_pallet_num", virtu_pallet_num);
                } else {
                    if (!pallet_num.equals("")) {
                        updateBigData.set("pallet_num", pallet_num);
                    }
                }
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            OpStaticElements.unLoadPlanCountUpdLock.unlock();
            errorMsg = "判断是否存在收板任务并且选择异常:" + ex;
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
