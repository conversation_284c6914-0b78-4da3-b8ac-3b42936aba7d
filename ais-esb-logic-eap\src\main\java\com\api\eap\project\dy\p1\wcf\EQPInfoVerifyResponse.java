
package com.api.eap.project.dy.p1.wcf;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>anonymous complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="EQPInfoVerifyResult" type="{http://tempuri.org/}eqpInfoVerifyResponseItem" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "eqpInfoVerifyResult"
})
@XmlRootElement(name = "EQPInfoVerifyResponse")
public class EQPInfoVerifyResponse {

    @XmlElement(name = "EQPInfoVerifyResult")
    protected EqpInfoVerifyResponseItem eqpInfoVerifyResult;

    /**
     * 获取eqpInfoVerifyResult属性的值。
     * 
     * @return
     *     possible object is
     *     {@link EqpInfoVerifyResponseItem }
     *     
     */
    public EqpInfoVerifyResponseItem getEQPInfoVerifyResult() {
        return eqpInfoVerifyResult;
    }

    /**
     * 设置eqpInfoVerifyResult属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link EqpInfoVerifyResponseItem }
     *     
     */
    public void setEQPInfoVerifyResult(EqpInfoVerifyResponseItem value) {
        this.eqpInfoVerifyResult = value;
    }

}
