package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;
import org.springframework.web.client.ResourceAccessException;

import java.net.*;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定颖EAP流程接口(Sub)公共方法
 * 1.PortStatusChangeReport:[接口]端口状态发生变化推送到EAP
 * 2.CarrierIDReport:[接口]载具扫描上报验证
 * 3.CarrierStatusReport:[接口]开始/结束/取消/终止投板（收板）时上报
 * 4.CCDDataReport:[接口]CCD读码上报
 * 5.FetchOutReport:[接口]每放一片板需要上报（针对放板机）
 * 6.StoreInReport:[接口]每收一片板需要上报（针对收板机）
 * 7.JobCountReport:[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
 * 8.PanelDataUploadReport:[接口]每片Panel收放台車事件上報
 * 9.WIPTrackingReport:[接口]每一lot完板上报
 * 10.CarrierDataUploadReport:[接口]最后全部任务完板后上报
 * 11.JobDataCreateModifyReport:[接口]人工操作界面拿走一片板子(手工录入)
 * 12.JobRemoveRecoveryReport:[接口]人工操作界面放回板子(手工录入)
 * 13.EachPositionReport:[接口]暂存位置信息动态上报
 * 14.EachPanelDataDownLoad:[上下游接口]当片信息(上游传递给下游)
 * 15.BcOffLineLotDownLoad:[上下游接口]BC离线工单信息下发
 * 16.BcOffLineLoginInDownLoad:[上下游接口]BC离线同步登入
 * 17.LotFinishDownLoad:[上下游接口]传递到下游设备结批信息
 * 18.ProductionModeDownLoad:[上下游接口]首件状态下发
 * 19.HeartBeatCheckDownLoad:[上下游接口]检测下游心跳状态
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapCjMasterSendFlowSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapCjInterfCommon eapCjInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.[接口]端口状态发生变化推送到EAP
    public JSONObject PortStatusChangeReport(String station_code, String port_code, String port_type, String port_status, String allow_sb_emptypallet_flag, String pallet_num, String pallet_all_count, String user_name, String port_ng_flag, String pallet_reload_flag) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "PortStatusChangeReport";
        String esbInterfCode = "PortStatusChangeReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
            if (port_status.equals("PREN") || port_status.equals("PROC")) {
                if (isNewVersion) {
                    jbResult.put("isSaveFlag", false);
                    jbResult.put("requestParas", requestParas);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", true);
                    jbResult.put("message", "不发送");
                    return jbResult;
                }
            }

            //1.创建参数
            String carrier_partial_full = "Disable";
            if (port_type.equals("UnLoad") && allow_sb_emptypallet_flag.equals("Y")) carrier_partial_full = "Enable";
            String port_type2 = port_type;
            if (port_type.equals("UnLoad")) port_type2 = "Unload";
            String port_mode = "OK";
            if (port_ng_flag.equals("Y")) port_mode = "NG";
            String carrier_clamp = "Normal";
            if (pallet_reload_flag.equals("Y")) carrier_clamp = "Reload";
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("port_no", String.format("%02d", Integer.parseInt(port_code)));
            request_body.put("port_status", port_status);
            request_body.put("carrier_id", pallet_num);
            request_body.put("carrier_partial_full", carrier_partial_full);
            request_body.put("job_count", pallet_all_count);
            request_body.put("operation_id", user_name);
            request_body.put("port_type", port_type2);
            request_body.put("port_mode", port_mode);
            request_body.put("carrier_clamp", carrier_clamp);
            if (isNewVersion) {
                request_body.put("port_enable", "Enable");
                String PalletAllCount = "0";
                if (port_type.equals("Load")) {
                    PalletAllCount = opCommonFunc.ReadCellOneRedisValue(station_code, "Load", "Eap", "EapStatus", "PalletAllCount");
                    if (PalletAllCount == null || PalletAllCount.equals("")) PalletAllCount = "0";
                }
                request_body.put("carrier_seq", PalletAllCount);
            }
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //2.[接口]载具扫描上报验证
    public JSONObject CarrierIDReport(String station_code, String pallet_num, String port_code, String port_type, int model, String production_mode, String manual_scan_flag, JSONArray lot_list, String first_scan_flag, String read_type) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CarrierIDReport";
        String esbInterfCode = "CarrierIDReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        String existCarryID = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            String port_type2 = port_type;
            if (port_type.equals("UnLoad")) port_type2 = "Unload";

            //1.创建参数

            JSONObject rfid_infos = new JSONObject();
            JSONArray arrays = new JSONArray();
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("lot_id", "");
            jsonObject.put("lot_count", "");
            arrays.add(jsonObject);
            rfid_infos.put("rfid", arrays);
            if (read_type == null || read_type.equals("")) {
                if (lot_list == null || lot_list.size() <= 0) read_type = "S";
                if (lot_list != null && lot_list.size() > 0) {
                    rfid_infos.put("rfid", lot_list);
                }
                if (!first_scan_flag.equals("Y")) {
                    read_type = "B";
                }
            }
            Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("carrier_id", pallet_num);
            request_body.put("port_type", port_type2);
            request_body.put("port_no", String.format("%02d", Integer.parseInt(port_code)));
            if (isNewVersion) {
                String prod_mode = "Production";
                if (model == 1) prod_mode = "FAI";
                else if (model == 2) prod_mode = "Dummy";
                request_body.put("prod_mode", prod_mode);
            } else {
                request_body.put("model", String.valueOf(model));
            }
            request_body.put("production_mode", production_mode);
            request_body.put("read_type", read_type);
            request_body.put("rfid_infos", rfid_infos);
            //针对定颖3.0以上版本增加字段
            if (isNewVersion) {
                //carrier_seq【載具流水號，當loader放入新載具時(LDCM)加1】
                String PalletAllCount = "0";
                if (port_type.equals("Load")) {
                    PalletAllCount = opCommonFunc.ReadCellOneRedisValue(station_code, "Load", "Eap", "EapStatus", "PalletAllCount");
                    if (PalletAllCount == null || PalletAllCount.equals("")) PalletAllCount = "0";
                }
                request_body.put("carrier_seq", PalletAllCount);
                request_body.put("S5", "");
                request_body.put("S6", "");
                request_body.put("S7", "");
                request_body.put("S8", "");
                request_body.put("S9", "");
                request_body.put("S10", "");
            }
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.离线直接返回默认
            if (eapCjInterfCommon.isLocal()) {
                JSONObject response = new JSONObject();
                JSONObject response_head = new JSONObject();
                response_head.put("function_name", "CarrierIDReport");
                response_head.put("trx_id", request_head.get("trx_id"));
                response_head.put("rtn_msg", "No default value");
                response_head.put("rtn_code", "0");
                response_head.put("result", "OK");
                JSONObject response_body = new JSONObject();
                response_body.put("carrier_id", " ");
                response_body.put("prod_mode", " ");
                response_body.put("rtn_fuder", " ");
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("responseBody", response_body);
                jbResult.put("existCarryID", "Y");
                jbResult.put("successFlag", true);
                jbResult.put("message", "发送成功-离线");
                return jbResult;
            }
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            if (!jsonObjectBack.containsKey("response_head")) {
                errorMsg = "EAP返回数据格式不包含response_head";
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            JSONObject response_head = jsonObjectBack.getJSONObject("response_head");
            String result = response_head.getString("result");
            String rtn_code = response_head.getString("rtn_code");
            String rtn_msg = response_head.getString("rtn_msg");
            JSONObject response_body = new JSONObject();
            if (!result.equals("OK")) {
                existCarryID = "N";
                errorMsg = rtn_code + "@" + rtn_msg;
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
            } else {
                existCarryID = "Y";
                jbResult.put("successFlag", true);
                jbResult.put("message", "发送成功");
            }
            if (jsonObjectBack.containsKey("response_body")) {
                response_body = jsonObjectBack.getJSONObject("response_body");
            }
            if (jsonObjectBack.containsKey("response_Body")) {
                response_body = jsonObjectBack.getJSONObject("response_Body");
            }
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("existCarryID", existCarryID);
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.[接口]开始/结束/取消/终止投板（收板）时上报
    public JSONObject CarrierStatusReport(String station_code, String pallet_num, String task_status, JSONArray lot_list, String port_mode) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CarrierStatusReport";
        String esbInterfCode = "CarrierStatusReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        String station_attr = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
            JSONObject lot_infos = new JSONObject();
            if (lot_list != null && lot_list.size() > 0) {
                if (isNewVersion) {
                    JSONArray lot_list_new = new JSONArray();
                    for (int i = 0; i < lot_list.size(); i++) {
                        JSONObject jbNew = lot_list.getJSONObject(i);
                        String lot_id = jbNew.getString("lot_id");
                        String lot_count = jbNew.getString("lot_count");
                        JSONObject jbNew2 = new JSONObject();
                        jbNew2.put("lot_id", lot_id);
                        jbNew2.put("lot_qty", lot_count);
                        lot_list_new.add(jbNew2);
                    }
                    lot_infos.put("lot", lot_list_new);
                } else {
                    lot_infos.put("lot", lot_list);
                }
            } else {
                lot_infos.put("lot", new JSONArray());
            }
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("carrier_id", pallet_num);
            request_body.put("carrier_status", task_status);
            if (isNewVersion) {
                String prod_mode = "Production";
                if (port_mode.equals("1")) prod_mode = "FAI";
                else if (port_mode.equals("2")) prod_mode = "Dummy";
                request_body.put("prod_mode", prod_mode);
                //carrier_seq【載具流水號，當loader放入新載具時(LDCM)加1】
                String PalletAllCount = "0";

                String sqlStation = "select " + "COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_code='" + station_code + "'";
                List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
                if (itemListStation != null && itemListStation.size() > 0) {
                    station_attr = itemListStation.get(0).get("station_attr").toString();
                }
                if (station_attr.equals("Load")) {
                    PalletAllCount = opCommonFunc.ReadCellOneRedisValue(station_code, "Load", "Eap", "EapStatus", "PalletAllCount");
                    if (PalletAllCount == null || PalletAllCount.equals("")) PalletAllCount = "0";
                }
                request_body.put("carrier_seq", PalletAllCount);
            } else {
                request_body.put("prod_mode", port_mode);
            }
            request_body.put("lot_infos", lot_infos);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            if (eapCjInterfCommon.isLocal(station_code, station_attr)) {
                JSONObject response = new JSONObject();
                JSONObject response_head = new JSONObject();
                response_head.put("function_name", "CarrierIDReport");
                response_head.put("trx_id", request_head.get("trx_id"));
                response_head.put("rtn_msg", "No default value");
                response_head.put("rtn_code", "0");
                response_head.put("result", "OK");
                JSONObject response_body = new JSONObject();
                response_body.put("carrier_id", " ");
                response_body.put("prod_mode", " ");
                response_body.put("rtn_fuder", " ");
                response.put("response_head", response_head);
                response.put("response_body", response_body);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", response_head);
                jbResult.put("responseBody", response_body);
                jbResult.put("successFlag", true);
                jbResult.put("message", "发送成功-离线");
                return jbResult;
            }
            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            //NG数据不做处理
            JSONObject response_body = new JSONObject();
            //JSONObject response_body=eapCjInterfCommon.GetResponseBody(jsonObjectBack);
            JSONObject response_head = jsonObjectBack.getJSONObject("response_head");
            String result = response_head.getString("result");
            if ("NG".equals(result)) {
                throw new Exception(response_head.getString("rtn_msg"));
            }
            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", ex.getMessage());
        }
        return jbResult;
    }

    //4.[接口]CCD读码上报
    public JSONObject CCDDataReport(String station_code, String panel_id, String ccd_no) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CCDDataReport";
        String esbInterfCode = "CCDDataReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //新版本不上报CCD
            if (eapCjInterfCommon.CheckDyVersion(3)) {
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "不上报");
                return jbResult;
            }

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("panel_id", panel_id);
            request_body.put("ccd_no", ccd_no);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //5.[接口]每放一片板需要上报（针对放板机）
    public JSONObject FetchOutReport(String station_code, String panel_id, String port_code, String slot_no, String lot_num) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "FetchOutReport";
        String esbInterfCode = "FetchOutReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            if (eapCjInterfCommon.CheckDyVersion(3)) {
                request_body.put("pnl_id", panel_id);
                request_body.put("lot_id", lot_num);
                request_body.put("port_no", String.format("%02d", Integer.parseInt(port_code)));
                request_body.put("seq_no", slot_no);
            } else {
                request_body.put("panel_id", panel_id);
                request_body.put("port_no", String.format("%02d", Integer.parseInt(port_code)));
                request_body.put("slot_no", slot_no);
            }
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //6.[接口]每收一片板需要上报（针对收板机）
    public JSONObject StoreInReport(String station_code, String panel_id, String port_code, String slot_no, String lot_num) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "StoreInReport";
        String esbInterfCode = "StoreInReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            if (eapCjInterfCommon.CheckDyVersion(3)) {
                request_body.put("panel_id", panel_id);
                request_body.put("lot_id", lot_num);
                request_body.put("port_no", String.format("%02d", Integer.parseInt(port_code)));
                request_body.put("seq_no", slot_no);
            } else {
                request_body.put("panel_id", panel_id);
                request_body.put("port_no", String.format("%02d", Integer.parseInt(port_code)));
                request_body.put("slot_no", slot_no);
            }
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //7.[接口]任务剩余数量上报(如果是收板机就是收板数量增量)
    public JSONObject JobCountReport(String station_code, String station_attr, int task_left_count, String port_no) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "JobCountReport";
        String esbInterfCode = "JobCountReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("job_count", task_left_count);
            if (eapCjInterfCommon.CheckDyVersion(3)) {
                request_body.put("port_no", port_no);
            }
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            if (eapCjInterfCommon.isLocal(station_code, station_attr)) {
                JSONObject response = new JSONObject();
                JSONObject response_head = new JSONObject();
                response_head.put("function_name", "JobCountReport");
                response_head.put("trx_id", request_head.get("trx_id"));
                response_head.put("rtn_msg", "No default value");
                response_head.put("rtn_code", "0");
                response_head.put("result", "OK");
                response.put("response_head", response_head);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", response_head);
                jbResult.put("successFlag", true);
                jbResult.put("message", "发送成功-离线");
                return jbResult;
            }
            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //8.[接口]每片Panel收放台車事件上報
    public JSONObject PanelDataUploadReport(Long station_id, String station_code, String panel_id, String slot_no, JSONArray item_attr_list, String offline_flag, String local_flag, String ccd_no, String lot_num, String prod_id, String prod_version, String process_code, JSONObject attr_else, String holding_time, String over_times, Integer hold_result) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "PanelDataUploadReport";
        String esbInterfCode = "PanelDataUploadReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            String panel_id2 = panel_id;
            if (panel_id2 == null || panel_id2.equals("") || panel_id2.equals("FFFFFFFF")) panel_id2 = "NoRead";
            JSONObject edc_infos = new JSONObject();
            if (!isNewVersion) {
                JSONArray item_attr_list2 = item_attr_list;
                if (item_attr_list2 == null) item_attr_list2 = new JSONArray();
                JSONObject jbAttr = new JSONObject();
                //新增Hold反馈字段
                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P009");
                jbAttr.put("item_value", holding_time);
                item_attr_list2.add(jbAttr);

                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P010");
                jbAttr.put("item_value", over_times);
                item_attr_list2.add(jbAttr);

                jbAttr = new JSONObject();
                jbAttr.put("item_id", "P011");
                jbAttr.put("item_value", hold_result);
                item_attr_list2.add(jbAttr);

                edc_infos.put("edc", item_attr_list2);
            } else {
                if (item_attr_list != null && item_attr_list.size() > 0) {
                    edc_infos.put("edc", item_attr_list);
                }
            }
            String keep_reason = "0";
            if (isNewVersion) {
                if (offline_flag.equals("Y") && local_flag.equals("Y")) keep_reason = "3";
            } else {
                if (offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";
            }
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            request_body.put("report_dt", CFuncUtilsSystem.GetNowDateTime(""));
            request_body.put("keep_reason", keep_reason);//0直接上传、1离线、2超时
            if (isNewVersion) {
                request_body.put("ccd_no", ccd_no);
                request_body.put("pnl_id", panel_id2);
                request_body.put("seq_no", slot_no);
                request_body.put("lot_id", lot_num);
                request_body.put("prod_id", prod_id);
                request_body.put("prod_version", prod_version);
                request_body.put("process_code", process_code);
                request_body.put("layer", (attr_else != null && attr_else.containsKey("layer") ? attr_else.getString("layer") : ""));
                request_body.put("step_seq", (attr_else != null && attr_else.containsKey("step_seq") ? attr_else.getString("step_seq") : ""));
                request_body.put("route_type", (attr_else != null && attr_else.containsKey("route_type") ? attr_else.getString("route_type") : ""));
                request_body.put("rework_route_id", (attr_else != null && attr_else.containsKey("rework_route_id") ? attr_else.getString("rework_route_id") : ""));
                request_body.put("rework_step_seq", (attr_else != null && attr_else.containsKey("rework_step_seq") ? attr_else.getString("rework_step_seq") : ""));
                request_body.put("pnl_side", (attr_else != null && attr_else.containsKey("pnl_side") ? attr_else.getString("pnl_side") : ""));
                request_body.put("edc_infos", edc_infos);
            } else {
                request_body.put("panel_id", panel_id2);
                request_body.put("slot_no", slot_no);
                request_body.put("edc_infos", edc_infos);
            }
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            /***office测试专用***/
            if (eapCjInterfCommon.DyOfficeTest()) {
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                return jbResult;
            }

            //2.若为离线,则放入离线上报
            if (offline_flag.equals("Y")) {//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            if (ex.getMessage().indexOf("timed out") != -1) {
                request_body.put("keep_reason", "2");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            } else {
                request_body.put("keep_reason", "1");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //9.[接口]每一lot完板上报
    public JSONObject WIPTrackingReport(Long station_id, String station_code, String user_name, String dept_id, String shift_id, String start_date, String end_date, String lot_num, int task_count, int lot_count, int lot_finish_count, String material_code, String lot_short_num, String lot_version, String prod_mode, String attribute1, String attribute2, JSONArray item_attr_list, String offline_flag, String local_flag, JSONObject attr_else) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "WIPTrackingReport";
        String esbInterfCode = "WIPTrackingReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
        String esb_prod_intef_url = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject edc_infos = new JSONObject();
            if (item_attr_list != null && item_attr_list.size() > 0) {
                edc_infos.put("edc", item_attr_list);
            } else {
                JSONArray item_attr_list2 = new JSONArray();
                for (int i = 1; i <= 27; i++) {
                    JSONObject nw_obj = new JSONObject();
                    nw_obj.put("item_id", "T0" + String.format("%02d", i));
                    nw_obj.put("item_value", "0");
                    item_attr_list2.add(nw_obj);
                }
                edc_infos.put("edc", item_attr_list2);
            }
            String keep_reason = "0";
            if (isNewVersion) {
                if (offline_flag.equals("Y") && local_flag.equals("Y")) keep_reason = "3";
            } else {
                if (offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";
            }
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            request_body.put("report_dt", CFuncUtilsSystem.GetNowDateTime(""));
            request_body.put("keep_reason", keep_reason);//0直接上传、1离线、2超时
            request_body.put("user_id", user_name);
            request_body.put("dept_id", dept_id);
            request_body.put("shift_id", shift_id);
            request_body.put("start_dt", start_date);
            request_body.put("end_dt", end_date);
            request_body.put("lot_id", lot_num);
            request_body.put("output_qty", lot_finish_count);
            request_body.put("lot_qty", lot_count);
            if (!isNewVersion) {
                request_body.put("pnl_count", task_count);
            }
            request_body.put("prod_id", material_code);
            request_body.put("lot_short_id", lot_short_num);
            request_body.put("prod_version", lot_version);
            request_body.put("prod_mode", prod_mode);
            request_body.put("process_code", attribute1);
            request_body.put("use_in_name", attribute2);
            if (isNewVersion) {
                String prod_mode_new = "Production";
                if (prod_mode.equals("1")) prod_mode_new = "FAI";
                else if (prod_mode.equals("2")) prod_mode_new = "Dummy";
                request_body.put("prod_mode", prod_mode_new);
                request_body.put("layer", (attr_else != null && attr_else.containsKey("layer") ? attr_else.getString("layer") : ""));
                request_body.put("step_seq", (attr_else != null && attr_else.containsKey("step_seq") ? attr_else.getString("step_seq") : ""));
                request_body.put("route_type", (attr_else != null && attr_else.containsKey("route_type") ? attr_else.getString("route_type") : ""));
                request_body.put("rework_route_id", (attr_else != null && attr_else.containsKey("rework_route_id") ? attr_else.getString("rework_route_id") : ""));
                request_body.put("rework_step_seq", (attr_else != null && attr_else.containsKey("rework_step_seq") ? attr_else.getString("rework_step_seq") : ""));
                request_body.put("pnl_side", (attr_else != null && attr_else.containsKey("pnl_side") ? attr_else.getString("pnl_side") : ""));
                request_body.put("spec_control", (attr_else != null && attr_else.containsKey("spec_control") ? attr_else.getString("spec_control") : ""));
            }
            request_body.put("edc_infos", edc_infos);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            /***office测试专用***/
            if (eapCjInterfCommon.DyOfficeTest()) {
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                return jbResult;
            }

            //2.若为离线,则放入离线上报
            if (offline_flag.equals("Y")) {//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (ResourceAccessException ex) {
            //判断断线
            URL url = new URL(esb_prod_intef_url);
            String host = url.getHost();
            int port = url.getPort();
            Socket socket = new Socket();
            try {
                socket.connect(new InetSocketAddress(host, port), 3000);
                if (!socket.isConnected()) {
                    throw new SocketTimeoutException();
                }
            } catch (SocketTimeoutException | NoRouteToHostException e) {
                //断线
                JSONObject jsonObject = JSONObject.parseObject(requestParas);
                request_body = jsonObject.getJSONObject("request_body");
                request_body.put("keep_reason", "1");
                jsonObject.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
            }
            //超时
            JSONObject jsonObject = JSONObject.parseObject(requestParas);
            request_body = jsonObject.getJSONObject("request_body");
            request_body.put("keep_reason", "2");
            jsonObject.put("request_body", request_body);
            opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //10.[接口]最后全部任务完板后上报
    public JSONObject CarrierDataUploadReport(Long station_id, String station_code, String pallet_num, String model, String splitseq, String lastSplit, JSONArray lot_list, String offline_flag, String local_flag) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "CarrierDataUpload";
        String esbInterfCode = "CarrierDataUploadReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        JSONObject postParas = new JSONObject();
        JSONObject request_body = new JSONObject();
        Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);
        String esb_prod_intef_url = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            if (eapCjInterfCommon.CheckDyVersion(3)) {
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "不需要保存");
                return jbResult;
            }

            //1.创建参数
            JSONObject carr_infos = new JSONObject();
            if (lot_list != null && lot_list.size() > 0) {
                carr_infos.put("carr", lot_list);
            }
            String keep_reason = "0";
            if (isNewVersion) {
                if (offline_flag.equals("Y") && local_flag.equals("Y")) keep_reason = "3";
            } else {
                if (offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";
            }
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            request_body.put("report_dt", CFuncUtilsSystem.GetNowDateTime(""));
            request_body.put("keep_reason", keep_reason);//0直接上传、1离线、2超时
            request_body.put("carrier_id", pallet_num);
            request_body.put("splitseq", splitseq);
            request_body.put("lastsplit", lastSplit);
            request_body.put("model", model);
            request_body.put("carr_infos", carr_infos);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            /***office测试专用***/
            if (eapCjInterfCommon.DyOfficeTest()) {
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                return jbResult;
            }

            //2.若为离线,则放入离线上报
            if (offline_flag.equals("Y")) {//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (ResourceAccessException ex) {
            //判断断线
            URL url = new URL(esb_prod_intef_url);
            String host = url.getHost();
            int port = url.getPort();
            Socket socket = new Socket();
            try {
                socket.connect(new InetSocketAddress(host, port), 3000);
                if (!socket.isConnected()) {
                    throw new SocketTimeoutException();
                }
            } catch (SocketTimeoutException | NoRouteToHostException e) {
                //断线
                JSONObject jsonObject = JSONObject.parseObject(requestParas);
                request_body = jsonObject.getJSONObject("request_body");
                request_body.put("keep_reason", "1");
                jsonObject.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
            }
            //超时
            JSONObject jsonObject = JSONObject.parseObject(requestParas);
            request_body = jsonObject.getJSONObject("request_body");
            request_body.put("keep_reason", "2");
            jsonObject.put("request_body", request_body);
            opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, jsonObject);
        } catch (Exception ex) {
            if (ex.getMessage().indexOf("timed out") != -1) {
                request_body.put("keep_reason", "2");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            } else {
                request_body.put("keep_reason", "1");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //11.[接口]人工操作界面拿走一片板子(手工录入)
    public JSONObject JobDataCreateModifyReport(String station_code, String panel_id, String port_code, String slot_no, String port_type, String user_name) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "JobDataCreateModifyReport";
        String esbInterfCode = "JobDataCreateModifyReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            String port_type2 = port_type;
            if (port_type.equals("UnLoad")) port_type2 = "Unload";

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("event_type", "Create");//Remove移除、Recovery复位、Create创建、Modify修改
            request_body.put("port_no", String.format("%02d", Integer.parseInt(port_code)));
            request_body.put("port_type", port_type2);
            request_body.put("slot_no", slot_no);
            request_body.put("operation_id", user_name);
            request_body.put("panel_id", panel_id);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //12.[接口]人工操作界面放回板子(手工录入)
    public JSONObject JobRemoveRecoveryReport(String station_code, String panel_id, String port_code, String slot_no, String port_type, String user_name, Boolean isRemove) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "JobRemoveRecoveryReport";
        String esbInterfCode = "JobRemoveRecoveryReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            String port_type2 = port_type;
            if (port_type.equals("UnLoad")) port_type2 = "Unload";

            //1.创建参数
            String event_type = "Recovery";
            if (isRemove) event_type = "Remove";
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("event_type", event_type);//Remove移除、Recovery复位、Create创建、Modify修改
            request_body.put("port_no", String.format("%02d", Integer.parseInt(port_code)));
            request_body.put("port_type", port_type2);
            request_body.put("slot_no", slot_no);
            request_body.put("operation_id", user_name);
            request_body.put("panel_id", panel_id);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //13.[接口]暂存位置信息动态上报
    public JSONObject EachPositionReport(String station_code, JSONArray panel_list) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "EachPositionReport";
        String esbInterfCode = "EachPositionReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject eppid_infos = new JSONObject();
            if (panel_list != null && panel_list.size() > 0) {
                eppid_infos.put("eppid", panel_list);
            }
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("eppid_infos", eppid_infos);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //14.[上下游接口]当片信息(上游传递给下游)
    public JSONObject EachPanelDataDownLoad(String station_code, String lot_id, String panel_id, String panel_status) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "EachPanelDataDownLoad";
        String esbInterfCode = "DeviceEachPanelDataDownLoad";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("lot_id", lot_id);
            request_body.put("panel_id", panel_id);
            request_body.put("panel_status", panel_status);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //15.[上下游接口]BC离线工单信息下发
    public JSONObject BcOffLineLotDownLoad(String station_code, String lot_id, String prod_id, int pnl_count, String lot_short_id, JSONArray item_list) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "BcOffLineLotDownLoad";
        String esbInterfCode = "DeviceBcOffLineLotDownLoad";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("lot_id", lot_id);
            request_body.put("prod_id", prod_id);
            request_body.put("pnl_count", pnl_count);
            request_body.put("lot_short_id", lot_short_id);
            request_body.put("item_list", item_list);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //16.[上下游接口]BC离线同步登入
    public JSONObject BcOffLineLoginInDownLoad(String station_code, String user_id, String dept_id, String shift_id, String nick_name) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "BcOffLineLoginInDownLoad";
        String esbInterfCode = "DeviceBcOffLineLoginInDownLoad";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("user_id", user_id);
            request_body.put("dept_id", dept_id);
            request_body.put("shift_id", shift_id);
            request_body.put("nick_name", nick_name);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //17.[上下游接口]传递到下游设备结批信息
    public JSONObject LotFinishDownLoad(String station_code, String lot_id, int lot_count, String lot_short_id) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "LotFinishDownLoad";
        String esbInterfCode = "DeviceLotFinishDownLoad";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("lot_id", lot_id);
            request_body.put("lot_count", lot_count);
            request_body.put("lot_short_id", lot_short_id);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //18.:[上下游接口]首件状态下发
    public JSONObject ProductionModeDownLoad(String station_code, int inspectCount) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "ProductionModeDownLoad";
        String esbInterfCode = "DeviceProductionModeDownLoad";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            String production_mode = "0";
            if (inspectCount > 0) production_mode = "1";
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("production_mode", production_mode);
            request_body.put("inspect_count", inspectCount);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //19.[上下游接口]检测下游心跳状态
    public JSONObject HeartBeatCheckDownLoad(String station_code, int force_end_a, int force_end_b) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "HeartBeatCheckDownLoad";
        String esbInterfCode = "DeviceHeartBeatCheckDownLoad";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            //1.创建参数
            String now_time = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("now_time", now_time);
            request_body.put("force_end_a", force_end_a);
            request_body.put("force_end_b", force_end_b);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();

            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    public JSONObject ReceiveReport(String station_code, String station_attr, String lot_num, String panel_index, String panel_barcode) {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "ReceiveReport";
        String esbInterfCode = "ReceiveReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);

            Boolean isNewVersion = eapCjInterfCommon.CheckDyVersion(3);

            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("lot_id", lot_num);
            request_body.put("pnl_id", panel_barcode);
            request_body.put("seq_no", panel_index);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();
            if (eapCjInterfCommon.isLocal(station_code, station_attr)) {
                JSONObject response = new JSONObject();
                JSONObject response_head = new JSONObject();
                response_head.put("function_name", "JobCountReport");
                response_head.put("trx_id", request_head.get("trx_id"));
                response_head.put("rtn_msg", "No default value");
                response_head.put("rtn_code", "0");
                response_head.put("result", "OK");
                response.put("response_head", response_head);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", response_head);
                jbResult.put("successFlag", true);
                jbResult.put("message", "发送成功-离线");
                return jbResult;
            }
            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    public JSONObject SendOutReport(String station_code, String station_attr, String lot_num, String panel_barcode, String panel_index) {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String funcName = "SendOutReport";
        String esbInterfCode = "SendOutReport";
        String token = "";
        String requestParas = "";
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            JSONObject postParas = new JSONObject();
            JSONObject request_head = eapCjInterfCommon.CreateRequestHeader(funcName, station_code);
            postParas.put("request_head", request_head);
            JSONObject request_body = new JSONObject();
            request_body.put("lot_id", lot_num);
            request_body.put("pnl_id", panel_barcode);
            request_body.put("seq_no", panel_index);
            postParas.put("request_body", request_body);
            requestParas = postParas.toString();
            if (eapCjInterfCommon.isLocal(station_code, station_attr)) {
                JSONObject response = new JSONObject();
                JSONObject response_head = new JSONObject();
                response_head.put("function_name", "JobCountReport");
                response_head.put("trx_id", request_head.get("trx_id"));
                response_head.put("rtn_msg", "No default value");
                response_head.put("rtn_code", "0");
                response_head.put("result", "OK");
                response.put("response_head", response_head);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", response_head);
                jbResult.put("successFlag", true);
                jbResult.put("message", "发送成功-离线");
                return jbResult;
            }
            //2.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " + "from sys_core_esb_interf " + "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            //3.发送数据
            JSONObject jsonObjectBack = eapCjInterfCommon.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectBack.toString();
            JSONObject response_body = eapCjInterfCommon.GetResponseBody(jsonObjectBack);

            //成功
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("responseBody", response_body);
            jbResult.put("successFlag", true);
            jbResult.put("message", "发送成功");
        } catch (Exception ex) {
            errorMsg = "发生异常:" + ex.getMessage();
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
