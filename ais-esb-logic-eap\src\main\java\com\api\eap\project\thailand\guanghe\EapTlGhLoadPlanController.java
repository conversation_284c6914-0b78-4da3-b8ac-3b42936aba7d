package com.api.eap.project.thailand.guanghe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 泰国广合放板机定制生产计划处理逻辑
 * 1.放板机Panel校验
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/guanghe/load")
public class EapTlGhLoadPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.放板机Panel校验
    @RequestMapping(value = "/EapTlGhLoadPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/load/EapTlGhLoadPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            String group_id = jsonParas.getString("group_id");
            String plan_id = jsonParas.getString("plan_id");
            Integer panel_attr = jsonParas.getInteger("panel_attr");//0正常端口板，1是NG工位板，2是陪镀板工位板
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            Integer ng_auto_pass = jsonParas.getInteger("ng_auto_pass");//NG自动越过标识,1为启用
            String curLayer = jsonParas.getString("curLayer");//当前层别
            if (curLayer == null) curLayer = "";

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String lot_status = "";
            String group_lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:其他异常
            Integer panel_index = 0;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            String panel_flag = "0";//0正常，1首件，2制中件，3修理件，4混批混料，5读码NG，6特殊板件，7层偏位预报废板8首件NG，9检测NG
            String putPnlNGmode = "1";//读码混批混料码板件的处置方式1：放入NG工位；2：设备停机
            String putPnlErrormode = "1";//未读到码板件的处置方式 1：放入NG工位；2：设备停机；3：批次第一片停止，后续的放行
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件，3：不停机首件（内外层）
            Integer inspect_count = 0;//首件数量
            Integer inspect_finish_count = 0;//首件完成数量
            JSONArray layerOrder = null;//叠层顺序
            JSONArray lotlist = null;//批次对象列表
            JSONArray curLayerPnlList = null;//当前层别对应pnl列表
            //(||***|***|***||表示：AroundDragCylinderNum=2，MidDragCylinderNum=1，proPanelNum=3)
            String AroundDragCylinderNum = "0";//前后拖缸板数量
            String MidDragCylinderNum = "0";//中间拖缸板数量
            String proPanelNum = "0";//产品板数量

            if (panel_attr == 2) dummy_flag = "Y";
            if (sys_model == 0) offline_flag = "Y";
            if (sys_model > 0) {
                queryBigData = new Query();
                String[] lot_status_list = new String[]{"PLAN", "WORK"};
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
                MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    mapLotInfo = iteratorBigData.next();
                    iteratorBigData.close();
                }
                if (mapLotInfo == null) panel_ng_code = 8;
                else {
                    lot_status = mapLotInfo.get("lot_status").toString();
                    group_lot_status = mapLotInfo.get("group_lot_status").toString();
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    plan_lot_count = Integer.parseInt(mapLotInfo.get("plan_lot_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    String item_info = mapLotInfo.get("item_info").toString();
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jsonObject = JSONObject.parseObject(item_info);
                        firstmode = jsonObject.getString("firstmode") == null ? "" : jsonObject.getString("firstmode");
                        putPnlNGmode = jsonObject.get("putPnlNGmode").toString();
                        putPnlErrormode = jsonObject.get("putPnlErrormode").toString();
                        layerOrder = jsonObject.containsKey("layerOrder") ? jsonObject.getJSONArray("layerOrder") : new JSONArray();
                        lotlist = jsonObject.containsKey("lotlist") ? jsonObject.getJSONArray("lotlist") : new JSONArray();
                        AroundDragCylinderNum = jsonObject.getString("AroundDragCylinderNum") == null ? "0" : jsonObject.getString("AroundDragCylinderNum");
                        MidDragCylinderNum = jsonObject.getString("MidDragCylinderNum") == null ? "0" : jsonObject.getString("MidDragCylinderNum");
                        proPanelNum = jsonObject.getString("proPanelNum") == null ? "0" : jsonObject.getString("proPanelNum");
                    }

                    if (putPnlErrormode.equals("3")) ng_auto_pass = 1;
                    if (dummy_flag.equals("N")) {
                        if (panel_model == 1) {//有Panel读码模式
                            if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                if (ng_auto_pass == 1) {
                                    if (Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) == 0) {
                                        if (panel_barcode.equals("")) panel_ng_code = 7;
                                        else panel_ng_code = 2;
                                    } else panel_ng_code = 4;
                                } else {
                                    if (panel_barcode.equals("")) panel_ng_code = 7;
                                    else panel_ng_code = 2;
                                }
                            } else {
                                long okCount = 0;
                                Query queryBigDataD = new Query();
                                queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataD.addCriteria(Criteria.where("panel_status").ne("NG"));
                                MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                        sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                if (iteratorBigDataD.hasNext()) {
                                    okCount = 1l;
                                    Document docItem = iteratorBigDataD.next();
                                    panel_flag = docItem.getString("panel_attr");
                                    iteratorBigDataD.close();
                                }
                                if (okCount <= 0) {
                                    //泰国广合要求混码不放行
                                    //if(ng_auto_pass==1) panel_ng_code=4;
                                    //else panel_ng_code=1;
                                    panel_ng_code = 1;
                                } else {
                                    //判断层级
                                    if (layerOrder != null && layerOrder.size() > 0) {
                                        int curLayerIndex = 0;
                                        if (curLayer.equals("")) curLayerIndex = 0;
                                        else curLayerIndex = layerOrder.indexOf(curLayer);
                                        curLayer = layerOrder.getString(curLayerIndex);
                                        for (int i = 0; i < lotlist.size(); i++) {
                                            JSONObject lot = lotlist.getJSONObject(i);
                                            String layer = lot.getString("layer");
                                            if (layer.equals(curLayer)) {
                                                curLayerPnlList = lot.getJSONArray("pnllist");
                                                break;
                                            }
                                        }
                                        if (curLayerPnlList != null && curLayerPnlList.indexOf(panel_barcode) < 0) {
                                            panel_ng_code = 1;
                                        }
                                        //校验成功后切换到下一个层级
                                        if (panel_ng_code == 0) {
                                            if (curLayerIndex < layerOrder.size() - 1) {
                                                curLayer = layerOrder.getString(curLayerIndex + 1);
                                            } else {
                                                curLayer = layerOrder.getString(0);
                                            }
                                        }
                                    }

                                    if (panel_ng_code == 0) {
                                        //特殊板，不是从NG工位取板，给plc回应20放到ng工位，如果是从NG工位取板，则直接给通过
                                        if (panel_flag.equals("6")) {
                                            if (panel_attr != 1) panel_ng_code = 19;
                                            else panel_ng_code = 0;
                                        } else if (panel_flag.equals("9")) {
                                            panel_ng_code = 4;
                                        } else {
                                            long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (flowCount > 0) {
                                                //泰国广合要求重码不放行
                                                //if(ng_auto_pass==1) panel_ng_code=4;
                                                //else panel_ng_code=3;
                                                panel_ng_code = 3;
                                            }
                                        }
                                    }
                                }
                            }
                        } else {//无Panel模式
                            panel_barcode = mapLotInfo.get("lot_num").toString() +
                                    String.format("%03d", Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1);
                        }
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                        if (panel_ng_code == 0 || panel_ng_code == 4) {
                            //下料首件模式，任意投完首件数量后，上报首板完结，然后把载具退料
                            if (firstmode.equals("2")) {
                                inspect_flag = "Y";
                                inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                            }
                            //停机首件模式
                            if ((firstmode.equals("1") || firstmode.equals("3")) && inspect_finish_count < inspect_count) {
                                inspect_flag = "Y";
                                inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                            }
                            finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                            panel_index = finish_ok_count;
                        } else {
                            finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                            panel_index = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                        }
                    } else {
                        panel_index = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    }
                }
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("port_code").toString());
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("pallet_num").toString());
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("pallet_type").toString());
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : panel_ng_code == 4 ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);
            mapBigDataRow.put("straight_flag", "N");

            String first_flag = "N";//是否为第一片Dummy或者第一片板件
            String aysn_lot_unLoad = "N";//是否同步数据到收扳机
            JSONObject jbResult = new JSONObject();
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                Update updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                if ((panel_ng_code == 0 || panel_ng_code == 4) && finish_ok_count == 1) {
                    aysn_lot_unLoad = "Y";
                    if (lot_status.equals("PLAN")) {
                        first_flag = "Y";
                        updateBigData.set("lot_status", "WORK");
                    }
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                //载具任务组状态
                if ((panel_ng_code == 0 || panel_ng_code == 4) && finish_ok_count == 1 && group_lot_status.equals("PLAN")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                    updateBigData = new Update();
                    updateBigData.set("group_lot_status", "WORK");
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }
            //Dummy模式
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("Y")) {
                if ((panel_ng_code == 0 || panel_ng_code == 4) && lot_status.equals("PLAN")) {
                    first_flag = "Y";
                    Update updateBigData = new Update();
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                    updateBigData.set("lot_status", "WORK");
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                    //载具任务组状态
                    if (group_lot_status.equals("PLAN")) {
                        queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                        queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                        updateBigData = new Update();
                        updateBigData.set("group_lot_status", "WORK");
                        mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                    }
                }
            }

            //组合返回数据
            jbResult.put("putPnlNGmode", putPnlNGmode);
            jbResult.put("putPnlErrormode", putPnlErrormode);
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("pallet_num").toString());
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("port_code").toString());
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("aysn_lot_unLoad", aysn_lot_unLoad);
            jbResult.put("first_flag", first_flag);
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("curLayer", curLayer);
            jbResult.put("plan_lot_count", plan_lot_count);
            jbResult.put("AroundDragCylinderNum", AroundDragCylinderNum);
            jbResult.put("MidDragCylinderNum", MidDragCylinderNum);
            jbResult.put("proPanelNum", proPanelNum);
            jbResult.put("firstmode", firstmode);
            jbResult.put("inspect_count", inspect_count);
            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.放板机Panel校验-配板机
    @RequestMapping(value = "/EapTlGhLoadPlanPanelCheckAndSave2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhLoadPlanPanelCheckAndSave2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/load/EapTlGhLoadPlanPanelCheckAndSave2";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            String group_id = jsonParas.getString("group_id");
            String plan_id = jsonParas.getString("plan_id");
            Integer panel_attr = jsonParas.getInteger("panel_attr");//0正常端口板，1是NG工位板，2是陪镀板工位板
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer sys_model = jsonParas.getInteger("sys_model");//生产模式(0:离线，1:半自动，2:自动)
            Integer panel_model = jsonParas.getInteger("panel_model");//有无panel模式,1为有panel模式
            Integer ng_auto_pass = jsonParas.getInteger("ng_auto_pass");//NG自动越过标识,1为启用

            Map<String, Object> mapLotInfo = null;
            Query queryBigData = new Query();
            String lot_status = "";
            String group_lot_status = "";
            String inspect_flag = "N";
            String dummy_flag = "N";
            String offline_flag = "N";
            Integer panel_ng_code = 0;//0:OK,1:混板,2:读码异常,3:重码,4:强制越过,5:板件过期,6:面次错误,7:读码超时,8:未找到工单,9:其他异常
            Integer panel_index = 0;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            String panel_flag = "0";//0正常，1首件，2制中件，3修理件，4混批混料，5读码NG，6特殊板件，7层偏位预报废板8首件NG，9检测NG
            String putPnlNGmode = "1";//读码混批混料码板件的处置方式1：放入NG工位；2：设备停机
            String putPnlErrormode = "1";//未读到码板件的处置方式 1：放入NG工位；2：设备停机；3：批次第一片停止，后续的放行
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件，3：不停机首件（内外层）
            Integer inspect_count = 0;//首件数量
            Integer inspect_finish_count = 0;//首件完成数量
            JSONArray portOrder = null;//叠层对应端口顺序
            Integer sequence = 0;//配套时放板顺序
            Integer port_index_next = 0;//配套时下一个放板顺序
            String locationlayer = "";//配套工位对应板件的层别
            //(||***|***|***||表示：AroundDragCylinderNum=2，MidDragCylinderNum=1，proPanelNum=3)
            String AroundDragCylinderNum = "0";//前后拖缸板数量
            String MidDragCylinderNum = "0";//中间拖缸板数量
            String proPanelNum = "0";//产品板数量

            if (panel_attr == 2) dummy_flag = "Y";
            if (sys_model == 0) offline_flag = "Y";
            if (sys_model > 0) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    mapLotInfo = iteratorBigData.next();
                    iteratorBigData.close();
                }
                if (mapLotInfo == null) panel_ng_code = 8;
                else {
                    lot_status = mapLotInfo.get("lot_status").toString();
                    group_lot_status = mapLotInfo.get("group_lot_status").toString();
                    finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString());
                    plan_lot_count = Integer.parseInt(mapLotInfo.get("plan_lot_count").toString());
                    finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString());
                    inspect_count = Integer.parseInt(mapLotInfo.get("inspect_count").toString());
                    inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString());
                    String item_info = mapLotInfo.get("item_info").toString();
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jsonObject = JSONObject.parseObject(item_info);
                        firstmode = jsonObject.getString("firstmode") == null ? "" : jsonObject.getString("firstmode");
                        putPnlNGmode = jsonObject.get("putPnlNGmode").toString();
                        putPnlErrormode = jsonObject.get("putPnlErrormode").toString();
                        portOrder = jsonObject.containsKey("portOrder") ? jsonObject.getJSONArray("portOrder") : new JSONArray();
                        sequence = jsonObject.getString("sequence") == null ? 0 : jsonObject.getInteger("sequence");
                        locationlayer = jsonObject.getString("locationlayer") == null ? "" : jsonObject.getString("locationlayer");
                        AroundDragCylinderNum = jsonObject.getString("AroundDragCylinderNum") == null ? "0" : jsonObject.getString("AroundDragCylinderNum");
                        MidDragCylinderNum = jsonObject.getString("MidDragCylinderNum") == null ? "0" : jsonObject.getString("MidDragCylinderNum");
                        proPanelNum = jsonObject.getString("proPanelNum") == null ? "0" : jsonObject.getString("proPanelNum");
                    }

                    if (putPnlErrormode.equals("3")) ng_auto_pass = 1;
                    if (dummy_flag.equals("N")) {
                        if (panel_model == 1) {//有Panel读码模式
                            if (panel_barcode.equals("") || panel_barcode.equals("NoRead")) {
                                if (ng_auto_pass == 1) {
                                    panel_ng_code = 4;
                                } else {
                                    if (panel_barcode.equals("")) panel_ng_code = 7;
                                    else panel_ng_code = 2;
                                }
                            } else {
                                long okCount = 0;
                                Query queryBigDataD = new Query();
                                queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataD.addCriteria(Criteria.where("panel_status").ne("NG"));
                                MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanDTable).find(queryBigDataD.getQueryObject()).
                                        sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                                if (iteratorBigDataD.hasNext()) {
                                    okCount = 1l;
                                    Document docItem = iteratorBigDataD.next();
                                    panel_flag = docItem.getString("panel_attr");
                                    iteratorBigDataD.close();
                                }
                                if (okCount <= 0) {
                                    //泰国广合要求混码不放行
                                    //if(ng_auto_pass==1) panel_ng_code=4;
                                    //else panel_ng_code=1;
                                    panel_ng_code = 1;
                                } else {
                                    if (panel_ng_code == 0) {
                                        //特殊板，不是从NG工位取板，给plc回应20放到ng工位，如果是从NG工位取板，则直接给通过
                                        if (panel_flag.equals("6")) {
                                            if (panel_attr != 1) panel_ng_code = 19;
                                            else panel_ng_code = 0;
                                        } else if (panel_flag.equals("9")) {
                                            panel_ng_code = 4;
                                        } else {
                                            long flowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataD.getQueryObject());
                                            if (flowCount > 0) {
                                                //泰国广合要求重码不放行
                                                //if(ng_auto_pass==1) panel_ng_code=4;
                                                //else panel_ng_code=3;
                                                panel_ng_code = 3;
                                            }
                                        }
                                    }
                                }
                            }
                        } else {//无Panel模式
                            panel_barcode = mapLotInfo.get("lot_num").toString() +
                                    String.format("%03d", Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1);
                        }
                        finish_count = Integer.parseInt(mapLotInfo.get("finish_count").toString()) + 1;
                        if (panel_ng_code == 0 || panel_ng_code == 4) {
                            //下料首件模式，任意投完首件数量后，上报首板完结，然后把载具退料
                            if (firstmode.equals("2")) {
                                inspect_flag = "Y";
                                inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                            }
                            //停机首件模式
                            if ((firstmode.equals("1") || firstmode.equals("3")) && inspect_finish_count < inspect_count) {
                                inspect_flag = "Y";
                                inspect_finish_count = Integer.parseInt(mapLotInfo.get("inspect_finish_count").toString()) + 1;
                            }
                            finish_ok_count = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                            panel_index = finish_ok_count;
                            if (portOrder != null && portOrder.size() > 0 && sequence > 0) {
                                if (sequence < portOrder.size()) {
                                    port_index_next = portOrder.getInteger(sequence);
                                } else {
                                    port_index_next = portOrder.getInteger(0);
                                }
                            }
                        } else {
                            finish_ng_count = Integer.parseInt(mapLotInfo.get("finish_ng_count").toString()) + 1;
                            panel_index = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString()) + 1;
                        }
                    } else {
                        panel_index = Integer.parseInt(mapLotInfo.get("finish_ok_count").toString());
                    }
                }
            }

            //记录过站信息
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            mapBigDataRow.put("task_from", offline_flag.equals("Y") ? "AIS" : mapLotInfo == null ? "AIS" : mapLotInfo.get("task_from").toString());
            mapBigDataRow.put("group_lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("group_lot_num").toString());
            mapBigDataRow.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            mapBigDataRow.put("lot_short_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_short_num").toString());
            mapBigDataRow.put("lot_index", offline_flag.equals("Y") ? 0 : mapLotInfo == null ? 0 : Integer.parseInt(mapLotInfo.get("lot_index").toString()));
            mapBigDataRow.put("port_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("port_code").toString());
            mapBigDataRow.put("material_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("material_code").toString());
            mapBigDataRow.put("pallet_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("pallet_num").toString());
            mapBigDataRow.put("pallet_type", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("pallet_type").toString());
            mapBigDataRow.put("lot_level", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_level").toString());
            mapBigDataRow.put("fp_index", 0);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_length").toString()));
            mapBigDataRow.put("panel_width", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_width").toString()));
            mapBigDataRow.put("panel_tickness", offline_flag.equals("Y") ? 0D : mapLotInfo == null ? 0D : Double.parseDouble(mapLotInfo.get("panel_tickness").toString()));
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_ng_code == 0 ? "OK" : panel_ng_code == 4 ? "NG_PASS" : "NG");
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", planCommonFunc.getPanelNgMsg(panel_ng_code));
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", "0");
            mapBigDataRow.put("panel_flag", panel_model == 0 ? "N" : "Y");
            mapBigDataRow.put("user_name", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("user_name").toString());
            mapBigDataRow.put("eap_flag", "N");
            mapBigDataRow.put("tray_barcode", "");
            mapBigDataRow.put("face_code", 0);
            mapBigDataRow.put("offline_flag", offline_flag);
            mapBigDataRow.put("group_id", offline_flag.equals("Y") ? "" : group_id);
            mapBigDataRow.put("sys_model", sys_model);
            mapBigDataRow.put("panel_attr", panel_flag);
            mapBigDataRow.put("straight_flag", "N");

            String first_flag = "N";//是否为第一片Dummy或者第一片板件
            String aysn_lot_unLoad = "N";//是否同步数据到收扳机
            JSONObject jbResult = new JSONObject();
            //先更新状态[非Dummy模式]
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("N")) {
                Update updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                updateBigData.set("finish_ng_count", finish_ng_count);
                if (finish_count == 1 && lot_status.equals("PLAN")) {
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                }
                if ((panel_ng_code == 0 || panel_ng_code == 4) && finish_ok_count == 1) {
                    aysn_lot_unLoad = "Y";
                    if (lot_status.equals("PLAN")) {
                        first_flag = "Y";
                        updateBigData.set("lot_status", "WORK");
                    }
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                //载具任务组状态
                if ((panel_ng_code == 0 || panel_ng_code == 4) && finish_ok_count == 1 && group_lot_status.equals("PLAN")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                    queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                    updateBigData = new Update();
                    updateBigData.set("group_lot_status", "WORK");
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }
            //Dummy模式
            if (!offline_flag.equals("Y") && mapLotInfo != null && dummy_flag.equals("Y")) {
                if ((panel_ng_code == 0 || panel_ng_code == 4) && lot_status.equals("PLAN")) {
                    first_flag = "Y";
                    Update updateBigData = new Update();
                    updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
                    updateBigData.set("lot_status", "WORK");
                    mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
                    //载具任务组状态
                    if (group_lot_status.equals("PLAN")) {
                        queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                        queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                        updateBigData = new Update();
                        updateBigData.set("group_lot_status", "WORK");
                        mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                    }
                }
            }

            //组合返回数据
            jbResult.put("putPnlNGmode", putPnlNGmode);
            jbResult.put("putPnlErrormode", putPnlErrormode);
            jbResult.put("station_flow_id", station_flow_id);
            jbResult.put("lot_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("lot_num").toString());
            jbResult.put("pallet_num", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("pallet_num").toString());
            jbResult.put("port_code", offline_flag.equals("Y") ? "" : mapLotInfo == null ? "" : mapLotInfo.get("port_code").toString());
            jbResult.put("panel_index", panel_index);
            jbResult.put("panel_ng_code", panel_ng_code);
            jbResult.put("panel_barcode", panel_barcode);
            jbResult.put("panel_model", panel_model);
            jbResult.put("sys_model", sys_model);
            jbResult.put("dummy_flag", dummy_flag);
            jbResult.put("inspect_flag", inspect_flag);
            jbResult.put("aysn_lot_unLoad", aysn_lot_unLoad);
            jbResult.put("first_flag", first_flag);
            jbResult.put("station_id", station_id);
            jbResult.put("plan_id", offline_flag.equals("Y") ? "" : plan_id);
            jbResult.put("curLayer", locationlayer);
            jbResult.put("plan_lot_count", plan_lot_count);
            jbResult.put("AroundDragCylinderNum", AroundDragCylinderNum);
            jbResult.put("MidDragCylinderNum", MidDragCylinderNum);
            jbResult.put("proPanelNum", proPanelNum);
            jbResult.put("firstmode", firstmode);
            jbResult.put("inspect_count", inspect_count);
            jbResult.put("port_index_next", port_index_next);
            result = jbResult.toString();
            //插入过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "Panel校验未知异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
