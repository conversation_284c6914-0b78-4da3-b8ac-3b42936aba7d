package com.api.pmc.project.bq;

import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 北汽系统时间接口
 * 1.当前系统时间信息
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/project/bq")
public class PmcBqNewDataController {

    //1.当前系统时间信息
    @RequestMapping(value = "/PmcBqSysDataSel", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcBqSysDataSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/project/bq/PmcBqSysDataSel";
        String selectResult="";
        String errorMsg="";
        try{
            List<Map<String, Object>> dataList=new ArrayList<>();
            Map<String, Object> dataItem=new HashMap<>();
            dataItem.put("sysdate",CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));//当前系统时间
            String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
            Calendar calendar=Calendar.getInstance();
            dataItem.put("sysweek",weekDays[calendar.get(Calendar.DAY_OF_WEEK)-1]);//当前系统星期
            dataList.add(dataItem);

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,dataList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "当前系统时间信息异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}

