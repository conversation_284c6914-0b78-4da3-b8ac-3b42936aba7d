package com.api.pack.core.station;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.api.base.IMongoBasic;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.domain.Example;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * <p>
 * StationFlowRecipe: 站点流程配方
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@ApiModel(value = "StationFlow", description = "站点流程配方")
@Document("a_pack_me_station_flow_recipe")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class StationFlowRecipe extends IMongoBasic
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "站点编码")
    @JsonProperty("station_code")
    @JSONField(name = "station_code")
    @Field("station_code")
    private String stationCode;

    @ApiModelProperty(value = "批次号")
    @JsonProperty("lot_num")
    @JSONField(name = "lot_num")
    @Field("lot_num")
    private String lotNum;

    @ApiModelProperty(value = "配方数据")
    @JsonProperty("recipe_paras")
    @JSONField(name = "recipe_paras")
    @Field("recipe_paras")
    private String recipeParas;

    @ApiModelProperty(value = "尾包配方数据")
    @JsonProperty("tail_recipe_paras")
    @JSONField(name = "tail_recipe_paras")
    @Field("tail_recipe_paras")
    private String tailRecipeParas;

    @ApiModelProperty(value = "计划ID")
    @JsonProperty("plan_id")
    @JSONField(name = "plan_id")
    @Field("plan_id")
    private String planId;

    public static StationFlowRecipe byStationCodeAndLotNumAndPlanId(String stationCode, String lotNum, String planId, StationFlowRecipeService service)
    {
        StationFlowRecipe query = new StationFlowRecipe();
        query.setStationCode(stationCode);
        query.setLotNum(lotNum);
        query.setPlanId(planId);
        return service.findOne(Example.of(query)).orElse(null);
    }
}
