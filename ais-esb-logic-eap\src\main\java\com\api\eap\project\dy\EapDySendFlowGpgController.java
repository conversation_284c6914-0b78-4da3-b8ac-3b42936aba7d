package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 定颖EAP(归批机)发送流程数据定义接口
 * 1.PortStatusChangeReport:[接口]端口状态发生变化推送到EAP
 * 2.CarrierStatusReport:[接口]开始/结束/取消/终止投板（收板）时上报
 * 3.CarrierIDReport:[接口]载具扫描上报验证
 * 4.判断任务是否存在
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/dy/interf/send")
public class EapDySendFlowGpgController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDySendFlowFunc eapDySendFlowFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;

    //1.[接口]端口状态发生变化推送到EAP(归批机)
    @RequestMapping(value = "/GpgPortStatusChangeReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyGpgPortStatusChangeReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/GpgPortStatusChangeReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String port_status = jsonParas.getString("port_status");//UDCM、LDRQ、WAIT、PROC、ABOT、CANE、PREN、UDRQ、
            String pallet_num = jsonParas.getString("pallet_num");
            Integer left_count = jsonParas.getInteger("left_count");
            String eqp_job_type = jsonParas.getString("eqp_job_type");//FCL、ECL、ECR、FCU、FCR、ECRFCL、FCUECL
            if (eqp_job_type == null) eqp_job_type = "";
            if (pallet_num == null) pallet_num = "";
            if (left_count == null) left_count = 0;

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String allow_sb_emptypallet_flag = "N";
            String user_name = "";
            String port_ng_flag = "N";
            String pallet_reload_flag = "N";
            if (port_status.equals("LDRQ")) pallet_reload_flag = "Y";
            if (port_status.equals("UDCM") || port_status.equals("LDRQ")) {
                pallet_num = "";
                left_count = 0;
            }

            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //端口状态上报
            eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, port_status, allow_sb_emptypallet_flag,
                    pallet_num, String.valueOf(left_count), user_name, port_ng_flag, pallet_reload_flag,eqp_job_type,"Load");
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "端口状态上报EAP异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]开始/结束/取消/终止上报(归批机)
    @RequestMapping(value = "/GpgCarrierStatusReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyGpgCarrierStatusReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/GpgCarrierStatusReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String task_status = jsonParas.getString("task_status");
            String pallet_num = jsonParas.getString("pallet_num");
            String lot_list_str = jsonParas.getString("lot_list");
            String group_lot_num=jsonParas.getString("group_lot_num");
            String manual_wip_flag=jsonParas.getString("manual_wip_flag");
            if(group_lot_num==null) group_lot_num="";
            if(manual_wip_flag==null) manual_wip_flag="";
            String sqlStation = "select station_id," + "COALESCE(station_attr,'') station_attr " + "from sys_fmod_station where station_code='" + station_code + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, request, apiRoutePath);
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            Long station_id_long=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            JSONArray lot_list=new JSONArray();
            if(lot_list_str!=null && !"".equals(lot_list_str)){
                lot_list = JSONArray.parseArray(lot_list_str);
            }
            //针对WaitStart处理,需要查询数量
            if(task_status.equals("WaitStart")){
                if (lot_list != null && lot_list.size() > 0){
                    JSONArray lot_list_new = new JSONArray();
                    for (int i = 0; i < lot_list.size(); i++) {
                        JSONObject jbNew = lot_list.getJSONObject(i);
                        String lot_id = jbNew.getString("lot_id");
                        String lot_count = jbNew.getString("lot_count");
                        if(!lot_id.equals("") && (lot_count==null || lot_count.equals("") || lot_count.equals("0"))){
                            Query queryBigData = new Query();
                            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_id));
                            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(
                                            queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                                    noCursorTimeout(true).batchSize(1).iterator();
                            if(iteratorBigData.hasNext()){
                                Document docItemBigData = iteratorBigData.next();
                                lot_count=String.valueOf(docItemBigData.getInteger("plan_lot_count"));
                                iteratorBigData.close();
                            }
                        }
                        JSONObject jbNew2 = new JSONObject();
                        jbNew2.put("lot_id", lot_id);
                        jbNew2.put("lot_count", lot_count);
                        lot_list_new.add(jbNew2);
                    }
                    lot_list=lot_list_new;
                }
            }
            eapDySendFlowFunc.CarrierStatusReport(station_code, pallet_num, task_status, lot_list, "0",
                    station_attr, port_code,manual_wip_flag);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "开始/结束/取消/终止上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.[接口]载具扫描上报验证(归批机)
    @RequestMapping(value = "/GpgCarrierIDReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapDyGpgCarrierIDReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/GpgCarrierIDReport";
        String transResult = "";
        String errorMsg = "";
        String userId = "-1";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String result = "OK";
            String station_id = jsonParas.getString("station_id");
            String port_code = jsonParas.getString("port_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String production_mode = jsonParas.getString("production_mode");//制程分类
            String ccdNo = jsonParas.getString("ccd_no");
            if (production_mode == null || production_mode.equals("")) production_mode = "Default";
            String pre_pallet_scan = jsonParas.getString("pre_pallet_scan");//是否提前扫描载具
            if (pre_pallet_scan == null || pre_pallet_scan.equals("")) pre_pallet_scan = "N";
            String read_type = jsonParas.getString("read_type");
            String eqp_job_type = jsonParas.getString("eqp_job_type");//FCL、ECL、ECR、FCU、FCR、ECRFCL、FCUECL
            if (eqp_job_type == null) eqp_job_type = "";
            Boolean isNewVersion=eapDyInterfCommon.CheckDyVersion(3);

            //1.查询工位信息
            String sqlStation = "select " + "station_code,COALESCE(station_attr,'') station_attr " + "from sys_fmod_station " + "where station_id=" + station_id + "";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(userId, sqlStation, false, request, apiRoutePath);
            Long station_id_long = Long.parseLong(station_id);
            String station_code = itemListStation.get(0).get("station_code").toString();
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            String user_name = "";

            //2.获取当前用户
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //其他
            String lot_infos_strs="";
            JSONObject response_body2 = eapDySendFlowFunc.CarrierIDReport(station_code, pallet_num, port_code, station_attr, 0, production_mode, "N", null, "Y", read_type);
            if (response_body2 == null) result = "NG";
            else{
                if(isNewVersion){
                    if (response_body2.containsKey("lot_infos")) {
                        JSONObject lot_infos = response_body2.getJSONObject("lot_infos");
                        if (lot_infos != null) {
                            if (lot_infos.containsKey("lot")) {
                                JSONArray lot_list = lot_infos.getJSONArray("lot");
                                if (lot_list != null && lot_list.size() > 0) {
                                    JSONArray new_lot_list=new JSONArray();
                                    for(int i=0;i<lot_list.size();i++){
                                        JSONObject jbItemAttr=lot_list.getJSONObject(i);
                                        String lot_id=jbItemAttr.getString("lot_id");
                                        String lot_qty=jbItemAttr.getString("lot_qty");
                                        JSONObject jbItemAttrNew=new JSONObject();
                                        jbItemAttrNew.put("lot_id",lot_id);
                                        jbItemAttrNew.put("lot_count",lot_qty);
                                        new_lot_list.add(jbItemAttrNew);
                                    }
                                    lot_infos_strs=new_lot_list.toString();
                                    result=result+"|"+lot_infos_strs;
                                }
                            }
                        }
                    }
                }
            }

            //成功后发送LDCM
            String eqp_job_type3="FCL";
            eapDySendFlowFunc.PortStatusChangeReport(station_code, port_code, station_attr, "LDCM", "N", pallet_num, "0", user_name, "N", "N",eqp_job_type3,"Load");

            //1.CCDDataReport:[接口]CCD读码上报
            eapDySendFlowFunc.CCDDataReport(station_code, pallet_num, ccdNo);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "载具扫描上报验证异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //判断是否存在放板任务(方法2:针对端口和多任务模式)
    @RequestMapping(value = "/GpgPlanExistJudgeM2", method = {RequestMethod.POST, RequestMethod.GET})
    public String GpgPlanExistJudgeM2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/dy/interf/send/GpgPlanExistJudgeM2";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            String station_id = jsonParas.getString("station_id");
            String pallet_num = jsonParas.getString("pallet_num");
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            long station_id_long = Long.parseLong(station_id);
            if (pallet_num == null) pallet_num = "";

            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();

            //增加逻辑,2024-4-26
            //判断最近一条数据是否为WAIT状态,若是WAIT状态则进行更新为PLAN状态
            String[] group_lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
            long planCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
            if (planCount <= 0) {
                String wait_group_id = "";
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigDataWait = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigDataWait.hasNext()) {
                    Document docItemBigData = iteratorBigDataWait.next();
                    wait_group_id = docItemBigData.getString("group_id");
                    iteratorBigDataWait.close();
                }
                if (!wait_group_id.equals("")) {
                    queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                    queryBigData.addCriteria(Criteria.where("group_id").is(wait_group_id));
                    Update updateBigData = new Update();
                    updateBigData.set("group_lot_status", "PLAN");
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
            }

            queryBigData = new Query();
            if (OneCarMultyLotFlag.equals("2")) {
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                }
            }
            if (group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    group_id = docItemBigData.getString("group_id");
                    iteratorBigData.close();
                }
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                //修改载具ID
                Update updateBigData = new Update();
                if (OneCarMultyLotFlag.equals("2")) {
                    //创建一个虚拟的载具ID,防止存在重复的载具ID导致找不到台车比对数据
                    String virtu_pallet_num = CFuncUtilsSystem.CreateUUID(true);
                    updateBigData.set("pallet_num", pallet_num);
                    updateBigData.set("virtu_pallet_num", virtu_pallet_num);
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                } else {
                    if (!pallet_num.equals("")) {
                        updateBigData.set("pallet_num", pallet_num);
                        mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                    }
                }
                //再查询
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    String plan_id = docItemBigData.getString("plan_id");
                    mapBigDataRow.put("group_id", docItemBigData.getString("group_id"));
                    mapBigDataRow.put("task_from", docItemBigData.getString("task_from"));
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_short_num", docItemBigData.getString("lot_short_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("lot_level", docItemBigData.getString("lot_level"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("panel_model", docItemBigData.getInteger("panel_model"));
                    mapBigDataRow.put("inspect_count", docItemBigData.getInteger("inspect_count"));
                    mapBigDataRow.put("pdb_count", docItemBigData.getInteger("pdb_count"));
                    mapBigDataRow.put("pdb_rule", docItemBigData.getInteger("pdb_rule"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("item_info", docItemBigData.getString("item_info"));
                    mapBigDataRow.put("attribute1", docItemBigData.getString("attribute1"));
                    mapBigDataRow.put("attribute2", docItemBigData.getString("attribute2"));
                    mapBigDataRow.put("attribute3", docItemBigData.getString("attribute3"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("pallet_use_count", docItemBigData.getInteger("pallet_use_count"));
                    //增加panel_list
                    String panel_list = "";
                    Integer panel_index = 0;
                    Query queryBigDataD = new Query();
                    queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                    MongoCursor<Document> iteratorBigDataD = mongoTemplate.getCollection(apsPlanBTable).find(queryBigDataD.getQueryObject()).
                            sort(queryBigDataD.getSortObject()).noCursorTimeout(true).batchSize(20).iterator();
                    while (iteratorBigDataD.hasNext()) {
                        Document docItemBigDataD = iteratorBigDataD.next();
                        String panel_barcode = docItemBigDataD.getString("panel_barcode");
                        if (panel_index == 0) panel_list = panel_barcode;
                        else panel_list += "," + panel_barcode;
                        panel_index++;
                    }
                    if (iteratorBigDataD.hasNext()) iteratorBigDataD.close();
                    mapBigDataRow.put("panel_list", panel_list);
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在放板任务异常:" + ex;
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }
}
