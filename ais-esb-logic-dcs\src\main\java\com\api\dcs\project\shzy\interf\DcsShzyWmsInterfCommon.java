package com.api.dcs.project.shzy.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 中冶WMS接口公共方法
 * 1.创建返回
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-8
 */
@Service
@Slf4j
public class DcsShzyWmsInterfCommon {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //1.创建返回
    public String CreateResponse(Integer code, String message) throws Exception{
        JSONObject jbBack=new JSONObject();
        jbBack.put("timestamp",System.currentTimeMillis());
        jbBack.put("code",code);
        jbBack.put("message",message);
        return jbBack.toString();
    }

    //2.创建中控返回
    public String CreateZkResponse(Integer code, String message) throws Exception{
        JSONObject jbBack=new JSONObject();
        jbBack.put("code",code);
        jbBack.put("message",message);
        return jbBack.toString();
    }

    //2.定制接口
    public JSONObject PostJbBackJb(String url, JSONObject jsonParas) throws Exception {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON_UTF8);
        headers.add("Accept", MediaType.APPLICATION_JSON.toString());
        headers.add("timestamp",String.valueOf(System.currentTimeMillis()));
        headers.add("sign","A9203625CA833AF8AB75DDFA884A03F6");
        headers.add("appid","2");
        HttpEntity<JSONObject> formEntity = new HttpEntity(jsonParas, headers);
        JSONObject jsonResult = this.restTemplate.postForObject(url, formEntity, JSONObject.class);
        return jsonResult;
    }
}
