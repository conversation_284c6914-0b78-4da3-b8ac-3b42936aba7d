package com.api.dcs.project.fjrm.api;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 码头公共方法
 * 1.查询任务锁定码头信息
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
public class DcsFjrmWharfCommonFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.查询任务锁定码头信息
    public Map<String, Object> GetWharfInfoLock(String ware_house,String wharf_code,String task_num) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlModelSel="select wharf_id," +
                "COALESCE(ware_house,'') ware_house," +
                "COALESCE(wharf_code,'') wharf_code," +
                "COALESCE(wharf_des,'') wharf_des," +
                "COALESCE(wharf_type,'') wharf_type," +
                "COALESCE(wharf_order,0) wharf_order," +
                "COALESCE(location_x,0) location_x," +
                "COALESCE(location_y,0) location_y," +
                "COALESCE(location_z,0) location_z, " +
                "COALESCE(rgv_code,'') rgv_code," +
                "COALESCE(task_num,'') task_num," +
                "COALESCE(lock_flag,'N') lock_flag " +
                "from b_dcs_fmod_wharf " +
                "where enable_flag='Y' " +
                "and lock_flag='Y' " +
                "and wharf_code='"+wharf_code+"' " +
                "and task_num='"+task_num+"' "+
                "and ware_house='"+ware_house+"' ";
        List<Map<String, Object>> itemListModel=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlModelSel,
                false,null,"");
        if(itemListModel!=null && itemListModel.size()>0){
            mapItem=itemListModel.get(0);
        }
        return mapItem;
    }
}
