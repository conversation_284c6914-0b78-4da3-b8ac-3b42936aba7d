package com.api.eap.project.guangxin;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.AisSystemApplication;
import com.api.eap.project.guangxin.event.BatchesFinishedEvent;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.BufferedReader;
import java.io.FileReader;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AisSystemApplication.class)
@Slf4j
public class EapGxEventServiceTest implements ApplicationEventPublisherAware
{
    private ApplicationEventPublisher eventPublisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher)
    {
        this.eventPublisher = applicationEventPublisher;
    }

    @Test
    public void test0()
    {
        JSONArray arr = new JSONArray();
        JSONObject item1 = new JSONObject();
        item1.put("group_lot_num", "18428101");
        item1.put("plan_id", "52e9968b8b204aee9c22629bc98e25cb");
        item1.put("lot_num", "18428101");
        item1.put("plan_lot_count", 12);
        item1.put("target_lot_count", 0);
        item1.put("fp_count", 1);
        item1.put("finish_count", 3);
        item1.put("finish_ok_count", 2);
        item1.put("finish_ng_count", 1);
        item1.put("material_code", "301115418");
        item1.put("other_attribute", "{\"SpotCheckList\":\"0\",\"SpotCheckFlag\":\"0\",\"ProcedureCode\":\"KBBK\",\"UnLoadTurnRoundFlag\":\"2\",\"LoadIntervalTime\":\"0\",\"ExceptFlag\":\"0\",\"LineSpeed\":\"0\",\"QRCodeFlag\":\"0\",\"LotProductModel\":\"0\",\"TurnRoundFlag\":\"0\"}");
        arr.add(item1);
        System.out.println("old arr: " + JSON.toJSONString(arr));
        this.eventPublisher.publishEvent(new BatchesFinishedEvent(arr, EapGxConst.EQUIPMENT_TYPES_MAGAZINE, false));
        System.out.println("new arr: " + JSON.toJSONString(arr));
    }
}
