package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 打刻系统接口
 * 1.打刻标识回传结果
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfDkController {
    @Autowired
    private MongoTemplate mongoTemplate;

    //1.打刻标识回传结果
    @RequestMapping(value = "/PmcCoreDkResultUpload", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreDkResultUpload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String selectResult="";
        String errorMsg="";
        String dkFillTable="d_pmc_me_station_quality_dk";
        try{
            log.info("打刻标识回传结果:"+jsonParas.toString());

            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            //获取参数
            String request_uuid=jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time=jsonParas.getString("request_time");//请求时间
            String request_attr=jsonParas.getString("request_attr");//预留请求属性
            String data_from_sys=jsonParas.getString("data_from_sys");//来源系统
            //新增
            String vin="";//vin号
            String dk_result="";//打刻结果
            String nowDateTime= CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            JSONArray dkArray = jsonParas.getJSONArray("list");
            if(dkArray != null && dkArray.size()>0) {
                List<Map<String, Object>> lstDocuments=new ArrayList<>();
                for (int i = 0; i < dkArray.size(); i++) {
                    JSONObject jzObject = dkArray.getJSONObject(i);
                    vin = jzObject.getString("vin");//VIN号
                    dk_result=jzObject.getString("dk_result");//打刻结果(Y为打刻成功/N为打刻失败)

                    Map<String, Object> mapDataItem=new HashMap<>();
                    String quality_trace_id=CFuncUtilsSystem.CreateUUID(true);
                    mapDataItem.put("item_date",item_date);
                    mapDataItem.put("item_date_val",item_date_val);
                    mapDataItem.put("quality_trace_id",quality_trace_id);

                    mapDataItem.put("vin",vin);
                    mapDataItem.put("dk_type",data_from_sys);
                    mapDataItem.put("dk_result",dk_result);
                    mapDataItem.put("dk_time",nowDateTime);
                    mapDataItem.put("up_flag","N");
                    mapDataItem.put("up_code",-1);
                    mapDataItem.put("up_msg","");
                    lstDocuments.add(mapDataItem);
                    /*
                    String sqlDxStationIns="insert into d_pmc_me_station_quality_dk " +
                            "(created_by,creation_date," +
                            "vin,dk_result) values " +
                            "('"+data_from_sys+"','"+nowDateTime+"','"+
                            vin+"','"+dk_result+"')";
                    cFuncDbSqlExecute.ExecUpdateSql(data_from_sys,sqlDxStationIns,false,request,apiRoutePath);*/
                }
                mongoTemplate.insert(lstDocuments,dkFillTable);
            }

            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
        }
        catch (Exception ex){
            errorMsg= "打刻标识回传结果异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
