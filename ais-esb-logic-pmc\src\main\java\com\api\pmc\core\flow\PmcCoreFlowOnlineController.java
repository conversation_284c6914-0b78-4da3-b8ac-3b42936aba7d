package com.api.pmc.core.flow;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.pmc.core.PmcCoreServer;
import com.api.pmc.core.PmcCoreServerInit;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 线首上线流程
 * 1.上线工位获取到订单
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/flow")
public class PmcCoreFlowOnlineController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private PmcCoreServerInit pmcCoreServerInit;
    @Autowired
    private PmcCoreOnlineBase pmcCoreOnlineBase;

    //1.上线工位获取到订单(锁)
    @RequestMapping(value = "/PmcCoreFlowMoOnlineLockSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowMoOnlineLockSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowMoOnlineLockSel";
        String selectResult = "";
        String errorMsg = "";
        String method = "/aisEsbOra/pmc/core/interf/PmcCoreMesNextMakeOrderSel";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
            //1、获取工位信息
            String station_des = "";//工位描述
            String bg_proceduce_code = "";//报工工序号
            String bg_proceduce_des = "";//报工工序描述
            String line_section_code = "";//产线分段编码(来自快速编码)
            String dx_flag = "";//是否为定序工位
            String online_flag = "";//是否上线工位
            String show_only_flag = "";//是否工位独立显示(Y过站离开是否清空实时工件信息)
            String flow_flag = "";//是否根据当前工位实时工件信息通过状态触发流程图
            String flow_taglist = "";//触发tagOnlyKeyList(做一个json格式,对应不同的工位状态触发对应的tag点)
            String avi_station = "";//AVI推算工位集
            String nj_flag = "";//是否下发拧紧参数
            String up_flag = "";//过站上传标识(MES,VIN打刻,油液加注,LES)
            String sqlStation = "select COALESCE(a.station_des,'') station_des," +
                    "COALESCE(a.bg_proceduce_code,'') bg_proceduce_code, " +
                    "COALESCE(a.bg_proceduce_des,'') bg_proceduce_des, " +
                    "COALESCE(a.line_section_code,'') line_section_code, " +
                    "COALESCE(a.station_attr,'N') dx_flag," +
                    "COALESCE(a.online_flag,'N') online_flag, " +
                    "COALESCE(a.show_only_flag,'N') show_only_flag, " +
                    "COALESCE(a.attribute1,'N') flow_flag, " +
                    "COALESCE(a.attribute2,'') flow_taglist, " +
                    "COALESCE(a.attribute3,'') avi_station, " +
                    "COALESCE(a.attribute4,'N') nj_flag, " +
                    "COALESCE(a.attribute5,'') up_flag " +
                    "from sys_fmod_station a," +
                    "     sys_fmod_prod_line b " +
                    "where a.prod_line_id=b.prod_line_id " +
                    "and a.enable_flag='Y' " +
                    "and b.enable_flag='Y' " +
                    "and a.station_code='" + station_code + "' " +
                    "and b.prod_line_code='" + prod_line_code + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, null, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "产线{" + prod_line_code + "},工位号{" + station_code + "},系统中不存在";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            station_des = itemListStation.get(0).get("station_des").toString();
            bg_proceduce_code = itemListStation.get(0).get("bg_proceduce_code").toString();
            bg_proceduce_des = itemListStation.get(0).get("bg_proceduce_des").toString();
            line_section_code = itemListStation.get(0).get("line_section_code").toString();
            dx_flag = itemListStation.get(0).get("dx_flag").toString();
            online_flag = itemListStation.get(0).get("online_flag").toString();
            show_only_flag = itemListStation.get(0).get("show_only_flag").toString();
            flow_flag = itemListStation.get(0).get("flow_flag").toString();
            flow_taglist = itemListStation.get(0).get("flow_taglist").toString();
            avi_station = itemListStation.get(0).get("avi_station").toString();
            nj_flag = itemListStation.get(0).get("nj_flag").toString();
            up_flag = itemListStation.get(0).get("up_flag").toString();
            //2、获取工位生产订单
            String make_order = "";//订单号
            String vin = "";//vin号
            String dms = "";//DMS号
            String item_project = "";//行项目
            String serial_num = "";//工件编号
            String pallet_num = "";//托盘号
            String small_model_type = "";//型号
            String main_material_code = "";//物料编号
            String material_color = "";//颜色
            String material_size = "";//尺寸
            String shaft_proc_num = "";//拧紧程序号
            String engine_num = "";//发动机
            String driver_way = "";//驱动形式
            String publish_number = "";//发布顺序号(上位MES)
            String bor_s = "";//天窗形式
            String white_car_adjust = "";//白车身调整总成
            String bbl_s = "";//驾驶室长度
            String bbr_s = "";//驾驶室高度
            String y01 = "";//车身品种
            String right_front_door = "";//右前门
            String left_front_door = "";//左前门
            String sqlStationMo = "select COALESCE(make_order,'') make_order " +
                    "from d_pmc_me_station_mo a " +
                    "where work_status='PLAN' " +
                    "and set_sign='NONE' " +
                    "and station_code='" + station_code + "' " +
                    "order by mo_work_order " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, null, apiRoutePath);
            if (itemListStationMo != null && itemListStationMo.size() > 0) {
                make_order = itemListStationMo.get(0).get("make_order").toString();
                //3、获取线首信息
                String sqlOnline = "select COALESCE(make_order,'') make_order," +
                        "COALESCE(serial_num,'') serial_num," +
                        "COALESCE(dms,'') dms," +
                        "COALESCE(item_project,'') item_project, " +
                        "COALESCE(vin,'') vin, " +
                        "COALESCE(small_model_type,'') small_model_type, " +
                        "COALESCE(main_material_code,'') main_material_code, " +
                        "COALESCE(material_color,'') material_color, " +
                        "COALESCE(material_size,'') material_size, " +
                        "COALESCE(shaft_proc_num,'') shaft_proc_num, " +
                        "COALESCE(engine_num,'') engine_num, " +
                        "COALESCE(driver_way,'') driver_way, " +
                        "COALESCE(publish_number,'') publish_number, " +
                        "COALESCE(bor_s,'') bor_s, " +
                        "COALESCE(white_car_adjust,'') white_car_adjust, " +
                        "COALESCE(bbl_s,'') bbl_s, " +
                        "COALESCE(right_front_door,'') right_front_door, " +
                        "COALESCE(left_front_door,'') left_front_door, " +
                        "COALESCE(bbr_s,'') bbr_s " +
                        "from d_pmc_me_flow_online " +
                        "where enable_flag='Y' " +
                        "and work_center_code='" + work_center_code + "' " +
                        "and make_order='" + make_order + "' " +
                        "LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListOnline = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
                if (itemListOnline == null || itemListOnline.size() <= 0) {
                    errorMsg = "车间{" + work_center_code + "},订单号{" + make_order + "},系统不存在";
                    selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return selectResult;
                }
                make_order = itemListOnline.get(0).get("make_order").toString();
                dms = itemListOnline.get(0).get("dms").toString();
                item_project = itemListOnline.get(0).get("item_project").toString();
                serial_num = itemListOnline.get(0).get("serial_num").toString();
                vin = itemListOnline.get(0).get("vin").toString();
                main_material_code = itemListOnline.get(0).get("main_material_code").toString();
                small_model_type = itemListOnline.get(0).get("small_model_type").toString();
                material_color = itemListOnline.get(0).get("material_color").toString();
                material_size = itemListOnline.get(0).get("material_size").toString();
                shaft_proc_num = itemListOnline.get(0).get("shaft_proc_num").toString();
                engine_num = itemListOnline.get(0).get("engine_num").toString();
                driver_way = itemListOnline.get(0).get("driver_way").toString();
                publish_number = itemListOnline.get(0).get("publish_number").toString();
                bor_s = itemListOnline.get(0).get("bor_s").toString();
                white_car_adjust = itemListOnline.get(0).get("white_car_adjust").toString();
                bbl_s = itemListOnline.get(0).get("bbl_s").toString();
                bbr_s = itemListOnline.get(0).get("bbr_s").toString();
                right_front_door = itemListOnline.get(0).get("right_front_door").toString();
                left_front_door = itemListOnline.get(0).get("left_front_door").toString();
                //4、查询特征值
                if (work_center_code.equals("ZA")) {
                    String feature = "";
                    String feature_value = "";
                    String sqlFeature = "select COALESCE(feature,'') feature," +
                            "COALESCE(feature_desc,'') feature_desc, " +
                            "COALESCE(feature_value,'') feature_value " +
                            "from d_pmc_fmod_feature " +
                            "where feature in ('BOR_S','BBL_S','BBR_S','Y01') " +
                            "and dms='" + dms + "' " +
                            "and item_project='" + item_project + "' ";
                    List<Map<String, Object>> itemListFeature = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlFeature, false, request, apiRoutePath);
                    if (itemListFeature != null && itemListFeature.size() > 0) {
                        for (Map<String, Object> featureValue : itemListFeature) {
                            feature = featureValue.get("feature").toString();
                            feature_value = featureValue.get("feature_value").toString();
                            switch (feature) {
                                case "BOR_S":
                                    bor_s = feature_value;
                                    break;
                                case "BBL_S":
                                    bbl_s = feature_value;
                                    break;
                                case "BBR_S":
                                    bbr_s = feature_value;
                                    break;
                                case "Y01":
                                    y01 = feature_value;
                                    break;
                                default:
                                    break;
                            }
                        }
                    }
                }
            } else {
                //3、获取锁定订单
                String last_make_order = "";//订单编号(查询后面的生产订单)
                String sqlMoStatus = "select COALESCE(last_make_order,'') last_make_order " +
                        "from d_pmc_me_mo_down_status " +
                        "where zk_name='AIS' LIMIT 1 OFFSET 0";
                List<Map<String, Object>> itemListMoStatus = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlMoStatus, false, null, apiRoutePath);
                if (itemListMoStatus != null && itemListMoStatus.size() > 0) {
                    last_make_order = itemListMoStatus.get(0).get("last_make_order").toString();
                }
                //4、获取Oracle订单
                if (PmcCoreServer.EsbUrl == null ||
                        PmcCoreServer.EsbUrl.isEmpty()) {
                    pmcCoreServerInit.ServerInit();
                }
                JSONObject jsonObjectMoReq = new JSONObject();
                jsonObjectMoReq.put("order_prod", last_make_order);//下一个订单
                jsonObjectMoReq.put("workshop", work_center_code);
                jsonObjectMoReq.put("order_state", "2");//订单状态(1.已生成2.已发布3.已上线4.拉入5.下线6.拉出)
                JSONObject jsonObjectMoRes = cFuncUtilsRest.PostJbBackJb(PmcCoreServer.EsbUrl + method, jsonObjectMoReq);
                Integer moCode = jsonObjectMoRes.getInteger("code");
                if (moCode != 0) {
                    errorMsg = "工位号{" + station_code + "},当前订单{" + last_make_order + "},车间{" + work_center_code + "},状态{2},上位MES没有符合上线条件的订单";
                    selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return selectResult;
                }
                JSONArray oraArry = jsonObjectMoRes.getJSONArray("data");
                if (oraArry != null) {
                    JSONObject oraJsonObject = oraArry.getJSONObject(0);
                    make_order = oraJsonObject.getString("ORDER_PROD");//生产订单
                    vin = oraJsonObject.getString("VIN");//vin号
                    dms = oraJsonObject.getString("DMS_NUM");//DMS号
                    item_project = oraJsonObject.getString("DMS_ROW");//DMS行项目
                    //serial_num=String.format("%20s",dms+item_project).replace(" ","0");
                    serial_num = oraJsonObject.getString("RFID");//RFID
                    main_material_code = oraJsonObject.getString("MATERIAL_CODE");//物料编码
                    small_model_type = main_material_code;//型号
                    material_color = oraJsonObject.getString("CAR_COLOUR_CODE");//颜色
                    material_size = oraJsonObject.getString("WIDTH_DIMENSION");//尺寸
                    shaft_proc_num = "";//拧紧程序号
                    engine_num = oraJsonObject.getString("ENGINE_MODEL");//发动机
                    driver_way = oraJsonObject.getString("DRIVE_MODE");//驱动形式
                    publish_number = oraJsonObject.getString("PUBLISH_NUMBER");//发布顺序号
                }
            }
            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("make_order", make_order);
            map.put("dms", dms);
            map.put("item_project", item_project);
            map.put("serial_num", serial_num);
            map.put("vin", vin);
            map.put("main_material_code", main_material_code);
            map.put("small_model_type", small_model_type);
            map.put("material_color", material_color);
            map.put("material_size", material_size);
            map.put("shaft_proc_num", shaft_proc_num);
            map.put("engine_num", engine_num);
            map.put("driver_way", driver_way);
            map.put("publish_number", publish_number);
            map.put("bor_s", bor_s);
            map.put("white_car_adjust", white_car_adjust);
            map.put("bbl_s", bbl_s);
            map.put("bbr_s", bbr_s);
            map.put("y01", y01);
            map.put("right_front_door", right_front_door);
            map.put("left_front_door", left_front_door);
            //工位信息
            map.put("station_des", station_des);
            map.put("bg_proceduce_code", bg_proceduce_code);
            map.put("bg_proceduce_des", bg_proceduce_des);
            map.put("line_section_code", line_section_code);
            map.put("dx_flag", dx_flag);
            map.put("online_flag", online_flag);
            map.put("show_only_flag", show_only_flag);
            map.put("flow_flag", flow_flag);
            map.put("flow_taglist", flow_taglist);
            map.put("avi_station", avi_station);
            map.put("nj_flag", nj_flag);
            map.put("up_flag", up_flag);
            itemListMo.add(map);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "上线工位获取到订单(锁)异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.根据当前工位号获取上线订单信息
    @RequestMapping(value = "/PmcCoreFlowMoOnlineSel", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowMoOnlineSel(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "pmc/core/flow/PmcCoreFlowMoOnlineSel";
        String selectResult = "";
        String errorMsg = "";
        List<Map<String, Object>> itemListMo = null;
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            //1、获取工位信息
            String station_des = "";//工位描述
            String bg_proceduce_code = "";//报工工序号
            String bg_proceduce_des = "";//报工工序描述
            String line_section_code = "";//产线分段编码(来自快速编码)
            String dx_flag = "";//是否为定序工位
            String online_flag = "";//是否上线工位
            String show_only_flag = "";//是否工位独立显示(Y过站离开是否清空实时工件信息)
            String flow_flag = "";//是否根据当前工位实时工件信息通过状态触发流程图
            String flow_taglist = "";//触发tagOnlyKeyList(做一个json格式,对应不同的工位状态触发对应的tag点)
            String avi_station = "";//AVI推算工位集
            String nj_flag = "";//是否下发拧紧参数
            String up_flag = "";//过站上传标识(MES,VIN打刻,油液加注,LES)
            String sqlStation = "select COALESCE(a.station_des,'') station_des," +
                    "COALESCE(a.bg_proceduce_code,'') bg_proceduce_code, " +
                    "COALESCE(a.bg_proceduce_des,'') bg_proceduce_des, " +
                    "COALESCE(a.line_section_code,'') line_section_code, " +
                    "COALESCE(a.station_attr,'N') dx_flag," +
                    "COALESCE(a.online_flag,'N') online_flag, " +
                    "COALESCE(a.show_only_flag,'N') show_only_flag, " +
                    "COALESCE(a.attribute1,'N') flow_flag, " +
                    "COALESCE(a.attribute2,'') flow_taglist, " +
                    "COALESCE(a.attribute3,'') avi_station, " +
                    "COALESCE(a.attribute4,'N') nj_flag, " +
                    "COALESCE(a.attribute5,'') up_flag " +
                    "from sys_fmod_station a," +
                    "     sys_fmod_prod_line b " +
                    "where a.prod_line_id=b.prod_line_id " +
                    "and a.enable_flag='Y' " +
                    "and b.enable_flag='Y' " +
                    "and a.station_code='" + station_code + "' " +
                    "and b.prod_line_code='" + prod_line_code + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStation, false, null, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "产线{" + prod_line_code + "},工位号{" + station_code + "},系统中不存在";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            station_des = itemListStation.get(0).get("station_des").toString();
            bg_proceduce_code = itemListStation.get(0).get("bg_proceduce_code").toString();
            bg_proceduce_des = itemListStation.get(0).get("bg_proceduce_des").toString();
            line_section_code = itemListStation.get(0).get("line_section_code").toString();
            dx_flag = itemListStation.get(0).get("dx_flag").toString();
            online_flag = itemListStation.get(0).get("online_flag").toString();
            show_only_flag = itemListStation.get(0).get("show_only_flag").toString();
            flow_flag = itemListStation.get(0).get("flow_flag").toString();
            flow_taglist = itemListStation.get(0).get("flow_taglist").toString();
            avi_station = itemListStation.get(0).get("avi_station").toString();
            nj_flag = itemListStation.get(0).get("nj_flag").toString();
            up_flag = itemListStation.get(0).get("up_flag").toString();
            //2、获取工位生产订单
            String make_order = "";//订单号
            String sqlStationMo = "select COALESCE(make_order,'') make_order " +
                    "from d_pmc_me_station_mo a " +
                    "where work_status='PLAN' " +
                    "and set_sign='NONE' " +
                    "and station_code='" + station_code + "' " +
                    "order by mo_work_order " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStationMo = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlStationMo, false, null, apiRoutePath);
            if (itemListStationMo == null || itemListStationMo.size() <= 0) {
                errorMsg = "车间{" + work_center_code + "},产线{" + prod_line_code + "},工位号{" + station_code + "},订单队列无可用订单";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            make_order = itemListStationMo.get(0).get("make_order").toString();
            //3、线首判断
            String dms = "";//DMS号
            String item_project = "";//行项目
            String serial_num = "";//工件编号(DMS+行 不满20位前面补0)
            String vin = "";//vin号
            String small_model_type = "";//型号
            String main_material_code = "";//物料编号
            String material_color = "";//颜色
            String material_size = "";//尺寸
            String shaft_proc_num = "";//拧紧程序号
            String engine_num = "";//发动机
            String driver_way = "";//驱动形式
            String publish_number = "";//发布顺序号(上位MES)
            String bor_s = "";//天窗形式
            String white_car_adjust = "";//白车身调整总成
            String bbl_s = "";//驾驶室长度
            String bbr_s = "";//驾驶室高度
            String y01 = "";//车身品种
            //获取线首信息
            String sqlOnline = "select COALESCE(make_order,'') make_order," +
                    "COALESCE(dms,'') dms," +
                    "COALESCE(item_project,'') item_project, " +
                    "COALESCE(vin,'') vin, " +
                    "COALESCE(small_model_type,'') small_model_type, " +
                    "COALESCE(ebom_white_car_code,'') main_material_code, " +
                    "COALESCE(material_color,'') material_color, " +
                    "COALESCE(material_size,'') material_size, " +
                    "COALESCE(shaft_proc_num,'') shaft_proc_num, " +
                    "COALESCE(engine_num,'') engine_num, " +
                    "COALESCE(driver_way,'') driver_way, " +
                    "COALESCE(publish_number,'') publish_number, " +
                    "COALESCE(bor_s,'') bor_s, " +
                    "COALESCE(white_car_adjust,'') white_car_adjust, " +
                    "COALESCE(bbl_s,'') bbl_s, " +
                    "COALESCE(bbr_s,'') bbr_s " +
                    "from d_pmc_me_flow_online " +
                    "where enable_flag='Y' " +
                    "and work_center_code='" + work_center_code + "' " +
                    "and make_order='" + make_order + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListOnline = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
            if (itemListOnline == null || itemListOnline.size() <= 0) {
                errorMsg = "车间{" + work_center_code + "},订单号{" + make_order + "},系统不存在";
                selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            make_order = itemListOnline.get(0).get("make_order").toString();
            dms = itemListOnline.get(0).get("dms").toString();
            item_project = itemListOnline.get(0).get("item_project").toString();
            serial_num = String.format("%20s", dms + item_project).replace(" ", "0");
            vin = itemListOnline.get(0).get("vin").toString();
            main_material_code = itemListOnline.get(0).get("main_material_code").toString();
            small_model_type = itemListOnline.get(0).get("small_model_type").toString();
            material_color = itemListOnline.get(0).get("material_color").toString();
            material_size = itemListOnline.get(0).get("material_size").toString();
            shaft_proc_num = itemListOnline.get(0).get("shaft_proc_num").toString();
            engine_num = itemListOnline.get(0).get("engine_num").toString();
            driver_way = itemListOnline.get(0).get("driver_way").toString();
            publish_number = itemListOnline.get(0).get("publish_number").toString();
            bor_s = itemListOnline.get(0).get("bor_s").toString();
            white_car_adjust = itemListOnline.get(0).get("white_car_adjust").toString();
            bbl_s = itemListOnline.get(0).get("bbl_s").toString();
            bbr_s = itemListOnline.get(0).get("bbr_s").toString();
            //4、查询特征值
            if (work_center_code.equals("ZA")) {
                String feature = "";
                String feature_value = "";
                String sqlFeature = "select COALESCE(feature,'') feature," +
                        "COALESCE(feature_desc,'') feature_desc, " +
                        "COALESCE(feature_value,'') feature_value " +
                        "from d_pmc_fmod_feature " +
                        "where feature in ('BOR_S','BBL_S','BBR_S','Y01') " +
                        "and dms='" + dms + "' " +
                        "and item_project='" + item_project + "' ";
                List<Map<String, Object>> itemListFeature = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlFeature, false, request, apiRoutePath);
                if (itemListFeature != null && itemListFeature.size() > 0) {
                    for (Map<String, Object> featureValue : itemListFeature) {
                        feature = featureValue.get("feature").toString();
                        feature_value = featureValue.get("feature_value").toString();
                        switch (feature) {
                            case "BOR_S":
                                bor_s = feature_value;
                                break;
                            case "BBL_S":
                                bbl_s = feature_value;
                                break;
                            case "BBR_S":
                                bbr_s = feature_value;
                                break;
                            case "Y01":
                                y01 = feature_value;
                                break;
                            default:
                                break;
                        }
                    }
                }
            }
            //拼接返回值
            itemListMo = new ArrayList<>();
            Map<String, Object> map = new HashMap<>();
            map.put("make_order", make_order);
            map.put("dms", dms);
            map.put("item_project", item_project);
            map.put("serial_num", serial_num);
            map.put("vin", vin);
            map.put("main_material_code", main_material_code);
            map.put("small_model_type", small_model_type);
            map.put("material_color", material_color);
            map.put("material_size", material_size);
            map.put("shaft_proc_num", shaft_proc_num);
            map.put("engine_num", engine_num);
            map.put("driver_way", driver_way);
            map.put("publish_number", publish_number);
            map.put("bor_s", bor_s);
            map.put("white_car_adjust", white_car_adjust);
            map.put("bbl_s", bbl_s);
            map.put("bbr_s", bbr_s);
            map.put("y01", y01);
            //工位信息
            map.put("station_des", station_des);
            map.put("bg_proceduce_code", bg_proceduce_code);
            map.put("bg_proceduce_des", bg_proceduce_des);
            map.put("line_section_code", line_section_code);
            map.put("dx_flag", dx_flag);
            map.put("online_flag", online_flag);
            map.put("show_only_flag", show_only_flag);
            map.put("flow_flag", flow_flag);
            map.put("flow_taglist", flow_taglist);
            map.put("avi_station", avi_station);
            map.put("nj_flag", nj_flag);
            map.put("up_flag", up_flag);
            itemListMo.add(map);

            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListMo, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "上线工位获取到订单异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //3.上线信息存储
    @RequestMapping(value = "/PmcCoreFlowMoOnlineIns", method = {RequestMethod.POST, RequestMethod.GET})
    public String PmcCoreFlowMoOnlineIns(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/PmcCoreFlowMoOnlineIns";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String prod_line_code = jsonParas.getString("prod_line_code");//产线
            String work_center_code = jsonParas.getString("work_center_code");//车间(ZA:总装、TA:涂装、HA:焊装、CA:冲压)
            String serial_num = jsonParas.getString("serial_num");//工件编号或RFID(DMS+行 不满20位前面补0)
            String pallet_num = jsonParas.getString("pallet_num");//托盘号(小车编号/滑橇号)
            String make_order = jsonParas.getString("make_order");
            String dms = jsonParas.getString("dms");
            String item_project = jsonParas.getString("item_project");
            String vin = jsonParas.getString("vin");
            String small_model_type = jsonParas.getString("small_model_type");
            String main_material_code = jsonParas.getString("main_material_code");
            String material_color = jsonParas.getString("material_color");
            String material_size = jsonParas.getString("material_size");
            String shaft_proc_num = jsonParas.getString("shaft_proc_num");
            String engine_num = jsonParas.getString("engine_num");
            String driver_way = jsonParas.getString("driver_way");
            String publish_number = jsonParas.getString("publish_number");
            String dx_flag = jsonParas.getString("dx_flag");//是否为定序工位
            String online_flag = jsonParas.getString("online_flag");//是否上线工位

            String white_car_adjust = "";//白车身调整总成
            String right_front_door = "";//右前车门焊接总成
            String left_front_door = "";//左前车门焊接总成
            String vpp_s = "";//平台(描述)
            String bbl_s = "";//驾驶室长度(描述)
            String bbr_s = "";//驾驶室顶高度(描述)
            String bbf_s = "";//驾驶室地板高度(描述)
            String bok_s = "";//工具箱(描述)
            String y26 = "";//变速箱型式(描述)
            String bed_s = "";//车门装饰板(描述)
            String bor_s = "";//天窗形式(特征值)
            //1、获取线首信息
            String sqlOnline = "select COALESCE(make_order,'') make_order " +
                    "from d_pmc_me_flow_online " +
                    "where enable_flag='Y' " +
                    "and work_center_code='" + work_center_code + "' " +
                    "and make_order='" + make_order + "' " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListOnline = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlOnline, false, null, apiRoutePath);
            if (itemListOnline == null || itemListOnline.size() <= 0) {
                //2、新增线首
                pmcCoreOnlineBase.OnlineIntefTask(request, apiRoutePath,
                        work_center_code, prod_line_code, station_code,
                        serial_num, pallet_num,
                        make_order, dms, item_project, vin, small_model_type,
                        main_material_code, material_color, material_size, shaft_proc_num,
                        engine_num, driver_way, publish_number, online_flag, dx_flag,
                        white_car_adjust, right_front_door, left_front_door, vpp_s,
                        bbl_s, bbr_s, bbf_s, bok_s, y26, bed_s, bor_s);
                //3.修改锁定表
                String nowDateTime = CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss");
                String sqlOnly = "select count(1) " +
                        "from d_pmc_me_mo_down_status " +
                        "where zk_name='AIS' ";
                int countOnly = cFuncDbSqlResolve.GetSelectCount(sqlOnly);
                if (countOnly > 0) {
                    String updateSql = "update d_pmc_me_mo_down_status set " +
                            "last_updated_by='" + station_code + "'," +
                            "last_update_date='" + nowDateTime + "'," +
                            "last_make_order='" + make_order + "' " +
                            "where zk_name='AIS' ";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, updateSql, true, null, apiRoutePath);
                } else {
                    String insertSql = "insert into d_pmc_me_mo_down_status(created_by,creation_date," +
                            "zk_name,last_make_order) " +
                            "values('" + station_code + "','" + nowDateTime + "'," +
                            "'AIS','" + make_order + "')";
                    cFuncDbSqlExecute.ExecUpdateSql(station_code, insertSql, true, null, apiRoutePath);
                }
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "上线信息存储异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //4.根据订单号获取轮胎数量
    @RequestMapping(value = "/GetTyreCountByMo", method = {RequestMethod.POST, RequestMethod.GET})
    public String GetTyreCountByMo(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/core/aps/GetTyreCountByMo";
        String selectResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");//工位
            String make_order = jsonParas.getString("make_order");//订单号
            //1、获取线首信息
            String sqlTyre = "select tyre_material_code " +
                    "from d_pmc_fmod_tyre_sort where order_prod='" + make_order + "' and enable_flag='Y'";
            List<Map<String, Object>> itemListTyre = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlTyre, false, null, apiRoutePath);
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemListTyre, "", "", itemListTyre.size());
        } catch (Exception ex) {
            errorMsg = "根据订单号获取轮胎数量异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
