package com.api.config;

import com.api.common.limit.CurrentLimiter;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.web.context.request.async.TimeoutCallableProcessingInterceptor;
import org.springframework.web.servlet.config.annotation.AsyncSupportConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;

@SpringBootConfiguration
public class MyWebConfigurer implements WebMvcConfigurer {

    @Override
    public void addCorsMappings(CorsRegistry corsRegistry) {
        /**
         * 所有请求都允许跨域，使用这种配置就不需要
         * 在interceptor中配置header了
         */

        //设置允许跨域的路径
        corsRegistry.addMapping("/**")         //指哪些接口URL需要增加跨域设置
                .allowCredentials(true)                    //是否允许证书(带cookie等凭证)
                //.allowedOrigins("http://localhost:8099")   //设置允许跨域请求的域名
                .allowedMethods("POST", "GET", "PUT", "OPTIONS", "DELETE")//-指的是允许哪些方法
                .allowedHeaders("*").maxAge(3600);
    }

    @Override
    public void configureAsyncSupport(final AsyncSupportConfigurer configurer) {
        configurer.setDefaultTimeout(20000);
        configurer.registerCallableInterceptors(timeoutInterceptor());
    }

    @Bean
    public TimeoutCallableProcessingInterceptor timeoutInterceptor() {
        return new TimeoutCallableProcessingInterceptor();
    }
    @Resource
    private CurrentLimiter currentLimiter;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //TODO 优化拦截，应当在currentLimiter获取到拦截的接口并添加到此处
        registry.addInterceptor(currentLimiter).addPathPatterns("/**"); // 拦截所有路径
    }
}
