package com.api.eap.core.share;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * <p>
 * 生产任务共用方法
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@Service
public class PlanCommonFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;

    //根据panel_ng_code返回描述
    public String getPanelNgMsg(Integer panel_ng_code){
        String panel_ng_msg="";
        if(panel_ng_code==0) panel_ng_msg="";
        else if(panel_ng_code==1) panel_ng_msg="混板";
        else if(panel_ng_code==2) panel_ng_msg="读码异常";
        else if(panel_ng_code==3) panel_ng_msg="重码";
        else if(panel_ng_code==4) panel_ng_msg="强制越过";
        else if(panel_ng_code==5) panel_ng_msg="板件过期";
        else if(panel_ng_code==6) panel_ng_msg="面次错误";
        else if(panel_ng_code==7) panel_ng_msg="读码超时";
        else if(panel_ng_code==8) panel_ng_msg="未找到工单";
        else if(panel_ng_code==9) panel_ng_msg="上游NG";
        else if(panel_ng_code==10) panel_ng_msg="无端口收板";
        else if(panel_ng_code==11) panel_ng_msg="陪镀板";
        else if(panel_ng_code==19) panel_ng_msg="特殊板件";
        else if(panel_ng_code==30) panel_ng_msg="取消不计数";
        else panel_ng_msg="其他异常";
        return panel_ng_msg;
    }
}
