package com.api.dcs.core.interf.fj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 分拣接受流程功能函数
 * 1.接受分拣解析结果
 * 2.分拣即时结果上报
 * 3.接受分拣完成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Service
@Slf4j
public class DcsCoreFjRecvFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsInterfCommon dcsInterfCommon;

    //1.接受分拣解析结果
    public JSONObject FjRecvTaskResolve(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "FjRecvTaskResolve";
        String token = "";
        String moID = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 0;
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        String apsTaskTable = "b_dcs_aps_task";
        String apsTaskResolveTable = "b_dcs_aps_task_resolve";
        try {
            jbResult.put("code", code);
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("moID", moID);

            //1.接受参数
            String task_num = jsonParas.getString("task_no");
            request_uuid = jsonParas.getString("request_uuid");
            Integer resolve_code = jsonParas.getInteger("resolve_code");
            String resolve_msg = jsonParas.getString("resolve_msg");
            String resolve_start_time = jsonParas.getString("resolve_start_time");
            String resolve_end_time = jsonParas.getString("resolve_end_time");
            Double cut_plan_time = jsonParas.getDouble("cut_plan_time");
            JSONArray resolve_list = jsonParas.getJSONArray("resolve_list");
            JSONArray station_time_list = jsonParas.getJSONArray("station_time_list");
            if (task_num == null) task_num = "";
            if (resolve_code == null) resolve_code = -1;
            if (resolve_msg == null) resolve_msg = "";
            if (resolve_start_time == null) resolve_start_time = "";
            if (resolve_end_time == null) resolve_end_time = "";
            if (cut_plan_time == null) cut_plan_time = 0D;
            double cut_plan_time_d = (double) cut_plan_time;

            jbResult.put("resolve_start_time", resolve_start_time);
            jbResult.put("resolve_end_time", resolve_end_time);

            //2.根据任务号查找mo_id
            String task_status = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                moID = docItemBigData.getString("mo_id");
                task_status = docItemBigData.getString("task_status");
                iteratorBigData.close();
            }
            if (moID.equals("")) {
                code = -1;
                errorMsg = "分拣系统传递任务号{" + task_num + "}不存在中控生产任务计划中";
                responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
                jbResult.put("code", code);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            jbResult.put("moID", moID);
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("mo_id").is(moID));
            Update updateBigData = new Update();
            //判断任务状态
            if (!task_status.equals("WAIT") && !task_status.equals("WAIT_PUBLISH") && !task_status.equals("PLAN")) {
                code = -2;
                errorMsg = "分拣系统传递任务号{" + task_num + "}在中控系统已开始或者已结束,中控拒绝接受分拣解析结果";
                responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
                jbResult.put("code", code);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            if (resolve_list == null || resolve_list.size() <= 0) {
                //通知任务显示
                updateBigData = new Update();
                updateBigData.set("task_msg", "分拣传递解析结果为空");
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsTaskTable);

                code = -3;
                errorMsg = "分拣系统传递任务{" + task_num + "}解析结果为空";
                responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
                jbResult.put("code", code);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断错误
            if (resolve_code != 0) {
                //通知任务显示
                updateBigData = new Update();
                updateBigData.set("task_msg", "分拣解析反馈错误:" + resolve_msg);
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsTaskTable);

                code = resolve_code;
                errorMsg = resolve_msg;
                responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
                jbResult.put("code", code);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //4.插入解析任务
            //4.1 先做删除
            mongoTemplate.remove(queryBigData, apsTaskResolveTable);
            //4.2 再做插入
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstDocuments = new ArrayList<>();
            for (int i = 0; i < resolve_list.size(); i++) {
                Map<String, Object> mapBigDataRow = new HashMap<>();
                JSONObject jbItem = resolve_list.getJSONObject(i);
                String station_code = jbItem.getString("station_code");
                String part_barcode = jbItem.getString("part_barcode");
                String print_text = jbItem.getString("print_text");
                String part_code = jbItem.getString("part_code");
                String part_draw = jbItem.getString("part_draw");
                String cut_datetime = jbItem.getString("cut_datetime");
                String craft_path_code = jbItem.getString("craft_path_code");
                Double print_top_x = jbItem.getDouble("print_top_x");
                Double print_top_y = jbItem.getDouble("print_top_y");
                Integer part_type = jbItem.getInteger("part_type");
                Double rotation_angle = jbItem.getDouble("rotation_angle");
                Double part_length = jbItem.getDouble("part_length");
                Double part_width = jbItem.getDouble("part_width");
                Double part_thickness = jbItem.getDouble("part_thickness");
                Double part_weight = jbItem.getDouble("part_weight");
                String next_process = jbItem.getString("next_process");
                Long plan_time = jbItem.getLong("plan_time");
                String part_attr = jbItem.getString("part_attr");
//                Integer material_num = jbItem.getInteger("material_num");
                //更改接口字段数据类型
                String material_num = jbItem.getString("material_num");

                //参数除Null
                if (StringUtils.isEmpty(station_code)) station_code = "";
                if (StringUtils.isEmpty(part_barcode)) part_barcode = "";
                if (StringUtils.isEmpty(print_text)) print_text = "";
                if (StringUtils.isEmpty(part_code)) part_code = "";
                if (StringUtils.isEmpty(part_draw)) part_draw = "";
                if (StringUtils.isEmpty(cut_datetime)) cut_datetime = "";
                if (StringUtils.isEmpty(craft_path_code)) craft_path_code = "";
                if (print_top_x == null) print_top_x = 0D;
                if (print_top_y == null) print_top_y = 0D;
                if (part_type == null) part_type = 0;
                if (rotation_angle == null) rotation_angle = 0D;
                if (part_length == null) part_length = 0D;
                if (part_width == null) part_width = 0D;
                if (part_thickness == null) part_thickness = 0D;
                if (part_weight == null) part_weight = 0D;
                if (StringUtils.isEmpty(next_process)) next_process = "";
                if (plan_time == null) plan_time = 0L;
                if (StringUtils.isEmpty(part_attr)) part_attr = "";
//                if (material_num == null) material_num = 0;
                //更改接口字段数据类型
                if (material_num == null) material_num = "";
                //插入集合
                String mo_resolve_id = CFuncUtilsSystem.CreateUUID(true);
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("mo_resolve_id", mo_resolve_id);
                mapBigDataRow.put("mo_id", moID);
                mapBigDataRow.put("station_code", station_code);
                mapBigDataRow.put("part_barcode", part_barcode);
                mapBigDataRow.put("print_text", print_text);
                mapBigDataRow.put("part_code", part_code);
                mapBigDataRow.put("part_draw", part_draw);
                mapBigDataRow.put("cut_datetime", cut_datetime);
                mapBigDataRow.put("craft_path_code", craft_path_code);
                mapBigDataRow.put("print_top_x", print_top_x);
                mapBigDataRow.put("print_top_y", print_top_y);
                mapBigDataRow.put("part_type", part_type);
                mapBigDataRow.put("rotation_angle", rotation_angle);
                mapBigDataRow.put("part_length", part_length);
                mapBigDataRow.put("part_width", part_width);
                mapBigDataRow.put("part_thickness", part_thickness);
                mapBigDataRow.put("part_weight", part_weight);
                mapBigDataRow.put("next_process", next_process);
                mapBigDataRow.put("part_attr", part_attr);
                mapBigDataRow.put("material_num", material_num);
                mapBigDataRow.put("plan_time", plan_time);
                lstDocuments.add(mapBigDataRow);
            }
            if (lstDocuments.size() > 0) {
                mongoTemplate.insert(lstDocuments, apsTaskResolveTable);
            }

            //5.更改主状态
            updateBigData = new Update();
            if (task_status.equals("WAIT")) {
                updateBigData.set("task_status", "WAIT_PUBLISH");
            }
            updateBigData.set("task_msg", "");
            updateBigData.set("cut_plan_minutes", (int) cut_plan_time_d);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsTaskTable);

            responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, "");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
            jbResult.put("code", code);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.分拣即时结果上报
    public JSONObject FjRecvProcResult(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "FjRecvProcResult";
        String token = "";
        String moID = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 0;
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        String apsTaskTable = "b_dcs_aps_task";
        String meSortResultTable = "b_dcs_me_sort_result";
        try {
            jbResult.put("code", code);
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("moID", moID);

            //1.接受参数
            String task_num = jsonParas.getString("task_no");
            String station_code = jsonParas.getString("station_code");
            request_uuid = jsonParas.getString("request_uuid");
            String robot_id = jsonParas.getString("robot_id");
            String part_barcode = jsonParas.getString("part_barcode");
            String part_code = jsonParas.getString("part_code");
            Integer part_index = jsonParas.getInteger("part_index");
            Integer part_type = jsonParas.getInteger("part_type");
            Integer fj_code = jsonParas.getInteger("fj_code");
            String fj_msg = jsonParas.getString("fj_msg");
            String fj_start_time = jsonParas.getString("fj_start_time");
            String fj_end_time = jsonParas.getString("fj_end_time");
//            Integer material_num = jsonParas.getInteger("material_num");
            //更改接口字段数据类型
            String material_num = jsonParas.getString("material_num");

            if (task_num == null) task_num = "";
            if (station_code == null) station_code = "";
            if (robot_id == null) robot_id = "";
            if (part_barcode == null) part_barcode = "";
            if (part_code == null) part_code = "";
            if (part_index == null) part_index = 0;
            if (part_type == null) part_type = 0;
            if (fj_code == null) fj_code = 0;
            if (fj_msg == null) fj_msg = "";
            if (fj_start_time == null) fj_start_time = "";
            if (fj_end_time == null) fj_end_time = "";
//            if (material_num == null) material_num = 0;
            //更改接口字段数据类型
            if (material_num == null) material_num = "";

            //2.根据任务号查找mo_id
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                moID = docItemBigData.getString("mo_id");
                iteratorBigData.close();
            }
            if (moID.equals("")) {
                code = -1;
                errorMsg = "分拣系统传递任务号{" + task_num + "}不存在中控生产任务计划中";
                responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
                jbResult.put("code", code);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            jbResult.put("moID", moID);

            //3.插入
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String sort_result_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("sort_result_id", sort_result_id);
            mapBigDataRow.put("mo_id", moID);
            mapBigDataRow.put("station_code", station_code);
            mapBigDataRow.put("robot_id", robot_id);
            mapBigDataRow.put("part_barcode", part_barcode);
            mapBigDataRow.put("part_code", part_code);
            mapBigDataRow.put("part_index", part_index);
            mapBigDataRow.put("part_type", part_type);
            mapBigDataRow.put("fj_code", fj_code);
            mapBigDataRow.put("fj_msg", fj_msg);
            mapBigDataRow.put("fj_start_time", fj_start_time);
            mapBigDataRow.put("fj_end_time", fj_end_time);
            mapBigDataRow.put("material_num", material_num);
            mongoTemplate.insert(mapBigDataRow, meSortResultTable);

            responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, "");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
            jbResult.put("code", code);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.接受分拣完成
    public JSONObject FjRecvTaskFinish(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "FjRecvTaskFinish";
        String token = "";
        String moID = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 0;
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        String apsTaskTable = "b_dcs_aps_task";
        try {
            jbResult.put("code", code);
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("moID", moID);

            //1.接受参数
            String task_num = jsonParas.getString("task_no");
            String station_code = jsonParas.getString("station_code");
            request_uuid = jsonParas.getString("request_uuid");
            Integer unload_code = jsonParas.getInteger("finish_code");
            if (task_num == null) task_num = "";
            if (station_code == null) station_code = "";
            if (unload_code == null) unload_code = -1;

            //2.判断工位是否存在
            String sqlStationCount = "select count(1) " + "from sys_fmod_station " + "where station_code='" + station_code + "'";
            Integer stationCount = cFuncDbSqlResolve.GetSelectCount(sqlStationCount);
            if (stationCount <= 0) {
                code = -1;
                errorMsg = "分拣传递工位号{" + station_code + "}未存在中控系统";
                responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
                jbResult.put("code", code);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            token = station_code;
            jbResult.put("token", token);

            //3.根据工位号寻找token
            String sqlEsbInterf = "select " + "COALESCE(b.paras_list,'') paras_list " + "from sys_core_esb_interf a inner join sys_core_esb_interf_d b " + "on a.esb_interf_id=b.esb_interf_id " + "where a.esb_interf_code='" + esbInterfCode + "' " + "and b.enable_flag='Y' and b.token='" + station_code + "' " + "order by b.esb_interf_d_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListEsb = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlEsbInterf, false, request, apiRoutePath);
            String paras_list = "";
            if (itemListEsb != null && itemListEsb.size() > 0) {
                paras_list = itemListEsb.get(0).get("paras_list").toString();
            }
            String[] lstParas = paras_list.split(",", -1);
            if (lstParas == null || lstParas.length < 2) {
                code = -2;
                errorMsg = "中控分拣完成接口未设置token{" + station_code + "}对应正确的paras_list{完成代码TAG,完成标志位TAG},用于通知流程分拣完成";
                responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
                jbResult.put("code", code);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String fjFinishCodeTag = lstParas[0];//完成代码TAG
            String fjFinishTag = lstParas[1];//完成标志位TAG

            //4.根据任务号查找mo_id
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_num").is(task_num));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsTaskTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                moID = docItemBigData.getString("mo_id");
                iteratorBigData.close();
            }
            if (moID.equals("")) {
                code = -3;
                errorMsg = "分拣系统传递任务号{" + task_num + "}不存在中控生产任务计划中";
                responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
                jbResult.put("code", code);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            jbResult.put("moID", moID);

            //5.写入到SCADA
            String tagOnlyKeyList = fjFinishCodeTag + "," + fjFinishTag;
            String tagValueList = unload_code + "&1";
            errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tagOnlyKeyList, tagValueList, true);
            if (!errorMsg.equals("")) {
                code = -4;
                errorMsg = "通知中控分拣完成失败:" + errorMsg;
                responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
                jbResult.put("code", code);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, "");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            responseParas = dcsInterfCommon.CreateResponse(request_uuid, code, errorMsg);
            jbResult.put("code", code);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
