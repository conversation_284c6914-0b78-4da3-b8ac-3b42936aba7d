package com.api.dcs.core.interf.mom;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.result.UpdateResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 喷码接受流程功能函数
 * 1.接受喷码完成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-21
 */
@Service
@Slf4j
public class DcsCoreMomRecvFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;


    public JSONObject receiveMomProductionPlan(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        Integer code = 0;
        String response_time = CFuncUtilsSystem.GetNowDateTime("");
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        try {
            JSONObject task = jsonParas.getJSONObject("task");
            JSONArray remnant_list = jsonParas.getJSONArray("remnant_list");
            String fileUploadPath = cFuncDbSqlResolve.GetParameterValue("FILE_UPLOAD_PATH");
            String dcsFileUploadPath = cFuncDbSqlResolve.GetParameterValue("DCS_FILE_UPLOAD_PATH");
            String json_url = task.getString("json_url");
            String nc_url = task.getString("nc_url");
            String ncFileName = getFileName(nc_url);
            String jsonFileName = getFileName(json_url);
            log.debug("ncFileName:{}", ncFileName);
            log.debug("jsonFileName:{}", jsonFileName);
            File jsonFolder = new File(fileUploadPath + "JSON/");
            if (!jsonFolder.exists()) {
                jsonFolder.mkdirs();
            }
            File ncFolder = new File(fileUploadPath + "NC/");
            if (!ncFolder.exists()) {
                ncFolder.mkdirs();
            }
            String newJsonFile = fileUploadPath + "JSON/" + jsonFileName;
            String newNcFile = fileUploadPath + "NC/" + ncFileName;
            log.debug("newNcFileName {}", newNcFile);
            log.debug("newJsonFile {}", newJsonFile);
            String jsonServerPath = "";
            String ncServerPath = "";
            String jsonContent = "";
            try {
                download(new String[]{json_url, nc_url}, new String[]{newJsonFile, newNcFile});
                jsonServerPath = getServerPath(dcsFileUploadPath + "JSON/" + jsonFileName);
                ncServerPath = getServerPath(dcsFileUploadPath + "NC/" + ncFileName);
                File file = new File(newJsonFile);
                FileInputStream fileInputStream = new FileInputStream(file);
                byte[] byteArray = new byte[1024 * 1024];
                int read = IOUtils.read(fileInputStream, byteArray);
                jsonContent = new String(byteArray, 0, read, StandardCharsets.UTF_8);
                fileInputStream.close();
            } catch (Exception ex) {
            }
            String model_type = task.getString("model_type");
            //激光=LASER 等离子=PLASMA
            String cut_type = task.getString("cut_type");
            String selectSql = "select fm.*,fs.* from ( SELECT * FROM b_dcs_fmod_model WHERE model_type = '" + model_type + "' ) as fm left join b_dcs_wms_fmod_stock fs on fm.model_id=fs.model_id where fs.stock_count > 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", selectSql, false, null, "");
            if (CollectionUtils.isEmpty(itemList)) {
                jbResult.put("response_uuid", request_uuid);
                jbResult.put("response_time", response_time);
                jbResult.put("task_num", "");
                jbResult.put("code", -1);
                jbResult.put("msg", model_type + " 在库存中没有查到");
                return jbResult;
            }
            if (!"LASER".equals(cut_type) && !"PLASMA".equals(cut_type)) {
                jbResult.put("response_uuid", request_uuid);
                jbResult.put("response_time", response_time);
                jbResult.put("task_num", "");
                jbResult.put("code", -1);
                jbResult.put("msg", "massage返回产线没有对应切割设备");
                return jbResult;
            }
            Integer cut_number = task.getInteger("cut_number");
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            // 设置序号
            Query query = new Query();
            query.with(Sort.by(Sort.Direction.DESC, "task_order"));
            Document bDcsApsTaskOne = mongoTemplate.findOne(query, Document.class, "b_dcs_aps_task");
            int task_order = 0;
            if (bDcsApsTaskOne != null) {
                task_order = (int) bDcsApsTaskOne.get("task_order");
            }
            Map<String, Object> info = itemList.stream().findFirst().get();
            String moId = CFuncUtilsSystem.CreateUUID(true);
            SimpleDateFormat sdf2 = new SimpleDateFormat("yyMMddHHmmss");
            String taskNum = sdf2.format(new Date());
            String taskNumCode = "MO" + taskNum + "100";
            Map<String, Object> taskMap = new HashMap<>();
            taskMap.put("item_date", item_date);
            taskMap.put("item_date_val", item_date_val);
            taskMap.put("mo_id", moId);
            taskMap.put("task_num", taskNumCode);
            taskMap.put("task_from", "MOM");
            taskMap.put("serial_num", "");
            taskMap.put("lot_num", "");
            taskMap.put("task_type", "NORMAL_TASK");
            taskMap.put("material_code", info.get("material_code"));
            taskMap.put("material_des", info.get("material_des"));
            taskMap.put("material_draw", "");
            taskMap.put("model_type", model_type);
            taskMap.put("m_length", info.get("m_length") == null ? 0 : info.get("m_length"));
            taskMap.put("m_width", info.get("m_width") == null ? 0 : info.get("m_width"));
            taskMap.put("m_height", info.get("m_height") == null ? 0 : info.get("m_height"));
            taskMap.put("m_weight", info.get("m_weight") == null ? 0 : info.get("m_weight"));
            taskMap.put("m_texture", info.get("m_texture"));
            taskMap.put("cut_texture", info.get("cut_texture"));
            taskMap.put("npa_startx", task.getDouble("npa_startx") == null ? 0 : task.getDouble("npa_startx"));
            taskMap.put("npa_starty", task.getDouble("npa_starty") == null ? 0 : task.getDouble("npa_starty"));
            taskMap.put("cut_way", task.getString("cut_way") == null ? "" : task.getString("cut_way"));
            taskMap.put("cut_speed", task.getDouble("cut_speed") == null ? 0 : task.getDouble("cut_speed"));
            taskMap.put("cut_ampere", task.getDouble("cut_ampere") == null ? 0 : task.getDouble("cut_ampere"));
            taskMap.put("cut_offset", task.getDouble("cut_offset") == null ? 0 : task.getDouble("cut_offset"));
            taskMap.put("dxf_name", task.getString("dxf_name"));
            taskMap.put("dxf_url", task.getString("dxf_url"));
            taskMap.put("json_name", jsonFileName);
            taskMap.put("json_url", jsonServerPath);
            taskMap.put("json_data", jsonContent);
            taskMap.put("nc_name", ncFileName);
            taskMap.put("nc_url", ncServerPath);
            taskMap.put("plan_date", task.getString("plan_date"));
            taskMap.put("task_order", task_order);
            taskMap.put("task_status", "WAIT");
            taskMap.put("task_msg", task.getString("task_msg"));
            taskMap.put("cut_code", "LASER".equals(cut_type) ? "G5" : "G6");
            taskMap.put("cut_type", cut_type);
            taskMap.put("cut_plan_minutes", Integer.valueOf(0));
            taskMap.put("is_auto_blast", "Y");
            taskMap.put("is_auto_car", "Y");
            taskMap.put("is_auto_print", "Y");
            taskMap.put("is_auto_cut", "Y");
            taskMap.put("is_auto_sort", "Y");
            taskMap.put("is_center", "Y");
            taskMap.put("is_materialt", "Y");
            taskMap.put("now_station_code", "G1");
            taskMap.put("bg_flag", "N");
            taskMap.put("enable_flag", "Y");
            taskMap.put("attribute1", "");
            taskMap.put("attribute2", "");
            taskMap.put("attribute3", "");
            mongoTemplate.insert(taskMap, "b_dcs_aps_task");
            selectSql = "SELECT * FROM b_dcs_fmod_model  order by model_id desc limit 1 ";
            List<Map<String, Object>> models = cFuncDbSqlExecute.ExecSelectSql("AIS", selectSql, false, null, "");
            Integer materialCode = 000000001;
            Integer materialDraw = 000000001;
            if (!CollectionUtils.isEmpty(models)) {
                Map<String, Object> model = models.stream().findFirst().get();
                try {
                    materialCode = Integer.parseInt(model.get("material_code").toString()) + 1;
                    materialDraw = Integer.parseInt(model.get("material_draw").toString()) + 1;
                } catch (Exception e) {
                    log.error("物料编码以及物料图号类型转换错误：{}", model);
                }
            }

            String insertSql = "INSERT INTO b_dcs_fmod_model (created_by, creation_date, model_id, material_code, material_des, " +
                    "material_draw, model_type, m_length, m_width, m_height, m_weight, m_texture, cut_texture," +
                    " data_from, npa_startx, npa_starty, enable_flag,selected_flag) VALUES ";
            for (int i = 0; i < remnant_list.size(); i++) {
                JSONObject jsonObject = remnant_list.getJSONObject(i);
                String remnant_no = jsonObject.getString("remnant_no");
                String remnant_mate = jsonObject.getString("remnant_mate");
                Double remnant_thick = jsonObject.getDouble("remnant_thick");
                Double remnant_length = jsonObject.getDouble("remnant_length");
                Double remnant_width = jsonObject.getDouble("remnant_width");
                Double remnant_weight = jsonObject.getDouble("remnant_weight");
                String modelType = remnant_mate + remnant_length.intValue() + remnant_width.intValue() + remnant_thick.intValue();
                String moPlusId = CFuncUtilsSystem.CreateUUID(true);
                Map<String, Object> taskMapRemnant = new HashMap<>();
                taskMapRemnant.put("item_date", item_date);
                taskMapRemnant.put("item_date_val", item_date_val);
                taskMapRemnant.put("mo_plus_id", moPlusId);
                taskMapRemnant.put("mo_id", moId);
                taskMapRemnant.put("plus_task_num", remnant_no);
                taskMapRemnant.put("plus_remnant_mat", remnant_mate);
                taskMapRemnant.put("plus_remnant_thick", remnant_thick);
                taskMapRemnant.put("plus_remnant_length", remnant_length);
                taskMapRemnant.put("plus_remnant_width", remnant_width);
                taskMapRemnant.put("plus_remnant_weight", remnant_weight);
                //插入余料信息
                mongoTemplate.insert(taskMapRemnant, "b_dcs_aps_task_plus");
                //插入model
                String sqlCount = "select count(1) from b_dcs_fmod_model where model_type='" + modelType + "'";
                Integer count = cFuncDbSqlResolve.GetSelectCount(sqlCount);
                if (count < 0) {
                    long modelId = cFuncDbSqlResolve.GetIncreaseID("b_dcs_fmod_model_id_seq", false);
                    String value = "('MOM','" + item_date + "'," + modelId + ",'" + materialCode + "','填充余料','" + materialDraw + "','" + modelType + "'," + remnant_length + "," + remnant_width + "," + remnant_thick + "," + remnant_weight + ",'" + remnant_mate + "','MS','MOM',0,0,'Y','N')";
                    insertSql = insertSql + value;
                    cFuncDbSqlExecute.ExecUpdateSql("AIS", insertSql, false, null, "");
                }
            }
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("task_num", taskNumCode);
            jbResult.put("code", code);
            jbResult.put("msg", "成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("task_num", "");
            jbResult.put("code", code);
            jbResult.put("msg", errorMsg);
        }
        return jbResult;
    }

    public String getServerPath(String filePath) throws Exception {
        String esbServerIp = "127.0.0.1";
        String esbServerIpSql = "select COALESCE(server_host_1,'127.0.0.1') server_host " + "from sys_core_server where server_code='EsbServer'";
        List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("ais", esbServerIpSql, true, null, "FileSave");
        if (itemList != null && itemList.size() > 0) {
            esbServerIp = itemList.get(0).get("server_host").toString();
        }
        return "http://" + esbServerIp + ":9080" + filePath;
    }

    private String getFileName(String path) {
        File file = new File(path);
        String name = file.getName();
        if (name.lastIndexOf('/') == -1 && name.lastIndexOf('\\') == -1) {
            return name;
        }
        int ncSubIndex = path.lastIndexOf('/');
        ncSubIndex = ncSubIndex == -1 ? path.lastIndexOf('\\') : ncSubIndex;
        String ncFileName = path.substring(ncSubIndex + 1);
        return ncFileName;
    }

    private FTPClient getFtpClient() throws IOException {
        String download_connect = cFuncDbSqlResolve.GetParameterValue("DOWNLOAD_CONNECT");
        Assert.isTrue(!StringUtils.isEmpty(download_connect), "中控系统 下载文件异常");
        JSONObject jsonObject = JSONObject.parseObject(download_connect);
        FTPClient ftp = new FTPClient();
        ftp.connect(jsonObject.getString("ip"), jsonObject.containsKey("port") ? jsonObject.getInteger("port") : 21);
        boolean login = ftp.login(jsonObject.getString("username"), jsonObject.getString("password"));
        log.debug("ftp login flag：{}", login);
        //这个方法的意思就是每次数据连接之前，ftp client告诉ftp server开通一个端口来传输数据
        ftp.enterLocalPassiveMode();
        // 二进制文件传输
        ftp.setFileType(FTPClient.BINARY_FILE_TYPE);
        ftp.setControlEncoding("UTF-8");
        ftp.setBufferSize(4096);
        return ftp;
    }

    private void download(String[] remoteFile, String[] localFile) {
        OutputStream outputStream = null;
        FTPClient ftpClient = null;
        try {
            ftpClient = getFtpClient();
            for (int i = 0; i < remoteFile.length; i++) {
                outputStream = Files.newOutputStream(Paths.get(localFile[i]));
                String path = remoteFile[i].replaceAll("^[A-Za-z]:", "");
                // ftp默认使用ISO-8859-1编码格式,所以这里需要转换为ISO-8859-1，“解决文件名为中文时，下载后为空文件的问题”
                String remoteFileName = new String(path.getBytes("GBK"), StandardCharsets.ISO_8859_1);
                Assert.isTrue(ftpClient.retrieveFile(remoteFileName, outputStream), "文件下载失败或者文件不存在");
            }
        } catch (IOException ex) {
            System.out.println("DownLoad Error: " + ex.getMessage());
            ex.printStackTrace();
        } finally {
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                if (ftpClient != null) {
                    ftpClient.disconnect();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
    }

    public JSONObject cancelMomProductionPlan(JSONObject jsonObject) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        JSONArray taskList = jsonObject.getJSONArray("task_list");
        Integer code = 0;
        String response_time = CFuncUtilsSystem.GetNowDateTime("");
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        try {
            if (CollectionUtils.isEmpty(taskList)) {
                jbResult.put("response_uuid", request_uuid);
                jbResult.put("response_time", response_time);
                jbResult.put("code", -1);
                jbResult.put("msg", "需要取消的任务不能为空");
                return jbResult;
            }
            String errorTask = "";
            for (Object o : taskList) {
                Query query = new Query();
                query.addCriteria(Criteria.where("task_num").is(o));
                query.addCriteria(Criteria.where("task_status").in(new String[]{"WAIT", "WAIT_PUBLISH"}));
                Update update = new Update();
                update.set("task_status", "CANCEL");
                UpdateResult result = mongoTemplate.updateFirst(query, update, "b_dcs_aps_task");
                long modifiedCount = result.getModifiedCount();
                if (modifiedCount == 0) {
                    errorTask += StringUtils.isEmpty(errorTask) ? o : "," + o;
                }
            }
            if (!StringUtils.isEmpty(errorTask)) {
                jbResult.put("response_uuid", request_uuid);
                jbResult.put("response_time", response_time);
                jbResult.put("code", -2);
                jbResult.put("msg", "任务[" + errorTask + "]取消失败");
                return jbResult;
            }
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("code", code);
            jbResult.put("msg", "成功");
        } catch (Exception ex) {
            code = -99;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("task_num", "");
            jbResult.put("code", code);
            jbResult.put("msg", errorMsg);
        }
        return jbResult;
    }
}
