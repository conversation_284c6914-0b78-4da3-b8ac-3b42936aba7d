package com.api.eap.project.guangxin;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * 无锡深南接口处理
 * 1.被动接生产任务
 * 2.上报完板信息
 * </p>
 *
 * <AUTHOR>
 * @since 2023-03-07
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/gx/interf")
public class EapGxInterfController {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapGxSharePlanController eapGxSharePlanController;

    //1.被动接生产任务(从MES下载配方)
    @RequestMapping(value = "/MesRecvTask", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesRecvTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "/eap/project/gx/interf/MesRecvTask";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        log.info("广芯保存配方时接收的数据：" + jsonParas.toJSONString());
        JSONObject jbResult = mesRecvTask(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //处理任务接受
    private JSONObject mesRecvTask(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "MesRecvTask";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            //处理EAP数据
            String station_code = jsonParas.getString("station_code");
            JSONArray arrlist = jsonParas.getJSONArray("arrlist");
            if (arrlist == null || arrlist.size() <= 0) {
                errorMsg = "参数arrlist为null,或者arrlist未传递任务信息";
                responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            JSONArray plan_list = new JSONArray();
            for (int i = 0; i < arrlist.size(); i++) {
                JSONObject jbItem = new JSONObject();
                JSONObject jsonObject = arrlist.getJSONObject(i);
                String PortNum = jsonObject.getString("PortNum");//端口号
                String group_lot_num = jsonObject.getString("GroupLotNum");
                String LotNum = jsonObject.getString("LotNum");//工单ID
                String MaterialCode = jsonObject.getString("MaterialCode");//物资编码
                String ProcedureCode = jsonObject.getString("ProcedureCode");//工序代码
                String LotPlanCount = jsonObject.getString("LotPlanCount");//批次数量
                String LotBoxCount = jsonObject.getString("LotBoxCount");//分盘数量
                String PanelIdList = jsonObject.getString("PanelIdList");//panel列表
                String LineSpeed = jsonObject.getString("LineSpeed");//线速度
                String PalletType = jsonObject.getString("PalletType");//载具类型
                String QRCodeFlag = jsonObject.getString("QRCodeFlag");//有无二维码
                String TurnRoundFlag = jsonObject.getString("TurnRoundFlag");//是否转向
                String FaceCode = jsonObject.getString("FaceCode");//板件面次
                String LoadIntervalTime = jsonObject.getString("LoadIntervalTime");//放板间隔时间
                String LotProductModel = jsonObject.getString("LotProductModel");//陪镀板类型
                String UnLoadTurnRoundFlag = jsonObject.getString("UnLoadTurnRoundFlag");//收板机是否转向
                String ExceptFlag = jsonObject.getString("ExceptFlag");//特急件标识
                String SpotCheckFlag = jsonObject.getString("SpotCheckFlag");//是否抽检标识（1，抽检；0，全放）
                String SpotCheckList = jsonObject.getString("SpotCheckList");//抽检panel序号集合（用空格隔开)（1，抽检；0，全放）
                String PalletNum = jsonObject.getString("PalletNum");//载具条码
                String workMode = jsonObject.getString("WorkMode");//载具条码
                String OldLotNum = jsonObject.getString("OldLotNum");//原始工单ID

                //合成标准
                jbItem.put("task_from", "MES");
                jbItem.put("group_lot_num", group_lot_num);
                jbItem.put("lot_num", LotNum);
                jbItem.put("material_code", MaterialCode);
                jbItem.put("lot_index", i + 1);
                jbItem.put("plan_lot_count", LotPlanCount);
                jbItem.put("fp_count", LotBoxCount);
                jbItem.put("panel_list", PanelIdList);
                jbItem.put("pallet_type", PalletType);
                jbItem.put("face_code", FaceCode);
                jbItem.put("port_code", PortNum);
                jbItem.put("pallet_num", PalletNum);
                jbItem.put("work_mode", workMode);

                JSONObject otherAttribute = new JSONObject();
                otherAttribute.put("ProcedureCode", ProcedureCode);
                otherAttribute.put("LineSpeed", LineSpeed);
                otherAttribute.put("QRCodeFlag", QRCodeFlag);
                otherAttribute.put("TurnRoundFlag", TurnRoundFlag);
                otherAttribute.put("LoadIntervalTime", LoadIntervalTime);
                otherAttribute.put("LotProductModel", LotProductModel);
                otherAttribute.put("UnLoadTurnRoundFlag", UnLoadTurnRoundFlag);
                otherAttribute.put("ExceptFlag", ExceptFlag);
                otherAttribute.put("SpotCheckFlag", SpotCheckFlag);
                otherAttribute.put("SpotCheckList", SpotCheckList);
                otherAttribute.put("OldLotNum", OldLotNum);
                jbItem.put("other_attribute", otherAttribute.toJSONString());
                plan_list.add(jbItem);
            }
            JSONObject postParas = new JSONObject();
            postParas.put("station_code", station_code);
            postParas.put("plan_list", plan_list);
            String result = eapGxSharePlanController.EapGxSharePlanSave(postParas, request);
            JSONObject jbPlanResult = JSONObject.parseObject(result);
            if (jbPlanResult.getInteger("code") != 0) {
                errorMsg = jbPlanResult.getString("error");
                responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            responseParas = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "");
        } catch (Exception ex) {
            errorMsg = "AIS接受任务发生未知异常:" + ex.getMessage();
            responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //判断是否存在收板任务 载具转换，收板任务也在放板工位 attribute1="Y"
    @RequestMapping(value = "/EapWxsnUnLoadPlanExistJudge2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapWxsnUnLoadPlanExistJudge2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/gx/interf/EapWxsnUnLoadPlanExistJudge2";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("attribute1").is("Y"));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                group_id = docItemBigData.getString("group_id");
                iteratorBigData.close();
            }
            if (!group_id.equals("")) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
                queryBigData.addCriteria(Criteria.where("group_id").is(group_id));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    Map<String, Object> mapBigDataRow = new HashMap<>();
                    mapBigDataRow.put("group_lot_num", docItemBigData.getString("group_lot_num"));
                    mapBigDataRow.put("lot_num", docItemBigData.getString("lot_num"));
                    mapBigDataRow.put("lot_index", docItemBigData.getInteger("lot_index"));
                    mapBigDataRow.put("plan_lot_count", docItemBigData.getInteger("plan_lot_count"));
                    mapBigDataRow.put("fp_count", docItemBigData.getInteger("fp_count"));
                    mapBigDataRow.put("target_lot_count", docItemBigData.getInteger("target_lot_count"));
                    mapBigDataRow.put("target_update_count", docItemBigData.getInteger("target_update_count"));
                    mapBigDataRow.put("port_code", docItemBigData.getString("port_code"));
                    mapBigDataRow.put("material_code", docItemBigData.getString("material_code"));
                    mapBigDataRow.put("pallet_num", docItemBigData.getString("pallet_num"));
                    mapBigDataRow.put("pallet_type", docItemBigData.getString("pallet_type"));
                    mapBigDataRow.put("panel_length", docItemBigData.getDouble("panel_length"));
                    mapBigDataRow.put("panel_width", docItemBigData.getDouble("panel_width"));
                    mapBigDataRow.put("panel_tickness", docItemBigData.getDouble("panel_tickness"));
                    mapBigDataRow.put("face_code", docItemBigData.getInteger("face_code"));
                    mapBigDataRow.put("other_attribute", docItemBigData.containsKey("other_attribute") ? docItemBigData.getString("other_attribute") : "");
                    itemList.add(mapBigDataRow);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, itemList, "", "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在收板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
