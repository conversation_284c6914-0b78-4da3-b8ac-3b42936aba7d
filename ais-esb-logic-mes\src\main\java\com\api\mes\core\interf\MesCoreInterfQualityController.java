package com.api.mes.core.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.Cache;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 1.MES接受终端客户端解析上传质量数据进行存储
 * </p>
 *
 * <AUTHOR>
 * @since 2022-12-31
 */
@RestController
@Slf4j
@RequestMapping("/mes/interf/core")
public class MesCoreInterfQualityController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Qualifier("jetCache")
    @Resource
    private Cache cache;

    //1.MES客户端上报MES质量数据
    @RequestMapping(value = "/MesInterfCoreRecvClientQ", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesInterfCoreRecvClientQ(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/interf/core/MesInterfCoreRecvClientQ";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = recvClientQualityData(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //处理接收质量数据
    private JSONObject recvClientQualityData(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "MesInterfCoreRecvClientQ";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String meQualityTable = "c_mes_me_station_quality";
        String meStationFlowTable = "c_mes_me_station_flow";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");

            //接受参数
            String station_code = jsonParas.getString("station_code");
            String serial_num = jsonParas.getString("serial_num");
            String pallet_num = jsonParas.getString("pallet_num");
            String staff_id = jsonParas.getString("staff_id");
            String progm_name = jsonParas.getString("progm_name");
            String quality_sign = jsonParas.getString("quality_sign");
            String arrive_date = jsonParas.getString("arrive_date");
            String leave_date = jsonParas.getString("leave_date");
            if (arrive_date == null || arrive_date.equals("")) arrive_date = CFuncUtilsSystem.GetNowDateTime("");
            if (leave_date == null || leave_date.equals("")) leave_date = CFuncUtilsSystem.GetNowDateTime("");
            //计算cost_time
            Long cost_time = CFuncUtilsSystem.GetDiffMsTimes(arrive_date, leave_date);
            cost_time = cost_time / 1000;
            //去NULL
            if (pallet_num == null) pallet_num = "";
            if (staff_id == null) staff_id = "";
            if (progm_name == null) progm_name = "";
            if (!quality_sign.equals("OK")) quality_sign = "NG";
            JSONArray quanlity_list = jsonParas.getJSONArray("quality_trace_list");

            if (serial_num == null || serial_num.equals("")) {
                errorMsg = "工件编号不能为空";
                responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if (station_code == null || station_code.equals("")) {
                errorMsg = "工位号不能为空";
                responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //1.判断工位号是否符合设定
            String sqlProdLine = "select b.prod_line_code " + "from sys_fmod_station a inner join sys_fmod_prod_line b " + "on a.prod_line_id=b.prod_line_id " + "where a.station_code='" + station_code + "'";
            List<Map<String, Object>> itemListLine = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlProdLine, false, request, apiRoutePath);
            if (itemListLine == null || itemListLine.size() <= 0) {
                errorMsg = "工位号{" + station_code + "}传递错误,请和MES设置工位号一致";
                responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String prod_line_code = itemListLine.get(0).get("prod_line_code").toString();

            //1.根据工位号和序列号查找到过站ID
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
            //long flowCount=mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigData.getQueryObject());
            //if(flowCount<=0){//若无过站则需要进行增加【修改：无论有没有过站数据,都需要进行新增】
            JSONObject postParas = new JSONObject();
            postParas.put("prod_line_code", prod_line_code);
            postParas.put("station_code", station_code);
            postParas.put("serial_num", serial_num);
            postParas.put("container_num", pallet_num);
            postParas.put("arrive_date", arrive_date);
            postParas.put("leave_date", leave_date);
            postParas.put("cost_time", cost_time);
            postParas.put("quality_sign", quality_sign);
            postParas.put("check_online_flag", "N");
            String url = "http://127.0.0.1:9090/aisEsbApi/mes/core/recipe/MesCoreStationFlowSave";
            JSONObject jbFlowResult = cFuncUtilsRest.PostJbBackJb(url, postParas);
            if (jbFlowResult.getInteger("code") != 0) {
                errorMsg = jbFlowResult.getString("error");
                responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //}
            String station_flow_id = "";
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                station_flow_id = docItemBigData.getString("station_flow_id");
                iteratorBigData.close();
            }
            if (station_flow_id == null || station_flow_id.equals("")) {
                errorMsg = "存储与查找过站ID失败";
                responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            String trace_d_time = CFuncUtilsSystem.GetNowDateTime("");
            Date item_date = CFuncUtilsSystem.GetMongoISODate(trace_d_time);
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstQDocuments = new ArrayList<>();
            if (quanlity_list != null && quanlity_list.size() > 0) {
                for (int i = 0; i < quanlity_list.size(); i++) {
                    JSONObject jbItem = quanlity_list.getJSONObject(i);
                    Map<String, Object> mapQDataItem = new HashMap<>();
                    Integer bolt_code = 0;
                    Integer progm_num = 0;
                    Integer group_order = jbItem.getInteger("group_order");
                    String group_name = jbItem.getString("group_name");
                    String quality_d_sign = jbItem.getString("quality_d_sign");
                    Long tag_id = jbItem.getLong("tag_id");
                    Integer tag_col_order = jbItem.getInteger("tag_col_order");
                    Integer tag_col_inner_order = jbItem.getInteger("tag_col_inner_order");
                    String theory_value = jbItem.getString("theory_value");
                    Double down_limit = jbItem.getDouble("down_limit");
                    Double upper_limit = jbItem.getDouble("upper_limit");
                    String quality_for = jbItem.getString("quality_for");
                    String tag_des = jbItem.getString("tag_des");
                    String tag_uom = jbItem.getString("tag_uom");
                    String tag_value = jbItem.getString("tag_value");
                    String trace_d_time2 = jbItem.getString("trace_d_time");
                    String attribute1 = jbItem.containsKey("attribute1") ? jbItem.getString("attribute1") : "0";
                    if (group_order == null) group_order = 1;
                    if (group_name == null) group_name = "";
                    if (!quality_d_sign.equals("OK")) quality_d_sign = "NG";
                    if (tag_id == null) tag_id = 0L;
                    if (tag_col_order == null) tag_col_order = (i) + 1;
                    if (tag_col_inner_order == null) tag_col_inner_order = 1;
                    if (theory_value == null || theory_value.equals("")) theory_value = "-1";
                    if (down_limit == null) down_limit = 0D;
                    if (upper_limit == null) upper_limit = 0D;
                    if (quality_for == null) quality_for = "";
                    if (tag_des == null) tag_des = "";
                    if (tag_uom == null) tag_uom = "";
                    if (tag_value == null) tag_value = "";
                    if (trace_d_time2 == null || trace_d_time2.equals("")) trace_d_time2 = trace_d_time;

                    mapQDataItem.put("item_date", item_date);
                    mapQDataItem.put("item_date_val", item_date_val);
                    mapQDataItem.put("quality_trace_id", CFuncUtilsSystem.CreateUUID(true));
                    mapQDataItem.put("station_flow_id", station_flow_id);
                    mapQDataItem.put("prod_line_code", prod_line_code);
                    mapQDataItem.put("station_code", station_code);
                    mapQDataItem.put("serial_num", serial_num);
                    mapQDataItem.put("group_order", group_order);
                    mapQDataItem.put("group_name", group_name);
                    mapQDataItem.put("tag_col_order", tag_col_order);
                    mapQDataItem.put("tag_id", tag_id);
                    mapQDataItem.put("tag_col_inner_order", tag_col_inner_order);
                    mapQDataItem.put("quality_for", quality_for);
                    mapQDataItem.put("tag_des", tag_des);
                    mapQDataItem.put("tag_uom", tag_uom);
                    mapQDataItem.put("theory_value", theory_value);
                    mapQDataItem.put("down_limit", down_limit);
                    mapQDataItem.put("upper_limit", upper_limit);
                    mapQDataItem.put("bolt_code", bolt_code);
                    mapQDataItem.put("progm_num", progm_num);
                    mapQDataItem.put("tag_value", tag_value);
                    mapQDataItem.put("quality_d_sign", quality_d_sign);
                    mapQDataItem.put("mes_quality_d_sign", quality_d_sign);
                    mapQDataItem.put("trace_d_time", trace_d_time2);
                    mapQDataItem.put("attribute1", attribute1);
                    lstQDocuments.add(mapQDataItem);
                }
            }
//            if ("TSGX".equals(projectCode) && "OP3030".equals(station_code)) {
//                List<Map<String, Object>> collect = lstQDocuments.stream().filter(f -> {
//                    return "TJ".equals(f.get("quality_for")) && "Completion_Status".equals(f.get("group_name"));
//                }).collect(Collectors.toList());
//                if (!CollectionUtils.isEmpty(collect)) {
//                    Map<String, Object> startTimeInfo = collect.stream().findFirst().get();
//                    cache.put("OP3030-" + serial_num, startTimeInfo.get("tag_value"));
//                }
//            }
            //1.存储质量数据
            if (lstQDocuments.size() > 0) {
                mongoTemplate.insert(lstQDocuments, meQualityTable);
            }

            //成功返回
            responseParas = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受设备质量数据成功");
        } catch (Exception ex) {
            errorMsg = "MES处理设备接收质量数据接口发生未知异常:" + ex.getMessage();
            responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.MES客户端上报MES物料装配数据
    @RequestMapping(value = "/MesInterfCoreRecvClientM", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesInterfCoreRecvClientM(@RequestBody JSONArray jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/interf/core/MesInterfCoreRecvClientM";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = recvClientMaterialData(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas, successFlag, message, request);
        }
        return responseParas;
    }

    //处理接收物料数据
    private JSONObject recvClientMaterialData(JSONArray jsonParasArray, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "MesInterfCoreRecvClientM";
        String token = "";
        String requestParas = jsonParasArray.toString();
        String responseParas = "";
        String meMaterialTable = "c_mes_me_station_material";
        String meStationFlowTable = "c_mes_me_station_flow";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            String projectCode = cFuncDbSqlResolve.GetParameterValue("ProjectCode");

            //接受参数
            List<Map<String, Object>> lstMDocuments = new ArrayList<>();
            if(jsonParasArray!=null&&jsonParasArray.size()>0){
                for(int i=0;i<jsonParasArray.size();i++){
                    JSONObject jsonParas=jsonParasArray.getJSONObject(i);
                    String station_code = jsonParas.getString("station_code");
                    String serial_num = jsonParas.getString("serial_num");
                    String material_des = jsonParas.getString("material_des");
                    String exact_barcode = jsonParas.getString("exact_barcode");
                    String material_batch = jsonParas.getString("material_batch");
                    String material_code = jsonParas.getString("material_code");
                    String material_uom = jsonParas.getString("material_uom");
                    String item_date = jsonParas.getString("item_date");

                    //1.根据工位号和序列号查找到过站ID
                    String station_flow_id = "";
                    String prod_line_code = "";
                    Query queryBigData = new Query();
                    queryBigData.addCriteria(Criteria.where("station_code").is(station_code));
                    queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
                    queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
                    queryBigData.with(Sort.by(Sort.Direction.DESC, "_id"));
                    MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                    if (iteratorBigData.hasNext()) {
                        Document docItemBigData = iteratorBigData.next();
                        station_flow_id = docItemBigData.getString("station_flow_id");
                        prod_line_code= docItemBigData.getString("prod_line_code");
                        iteratorBigData.close();
                    }
                    if (station_flow_id == null || station_flow_id.equals("")) {
                        errorMsg = "存储与查找过站ID失败";
                        responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                        jbResult.put("responseParas", responseParas);
                        jbResult.put("successFlag", false);
                        jbResult.put("message", errorMsg);
                        return jbResult;
                    }

                    if (item_date == null || item_date.equals("")) item_date = CFuncUtilsSystem.GetNowDateTime("");
                    Date item_date_now = CFuncUtilsSystem.GetMongoISODate("");
                    long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date_now);
                    Map<String, Object> mapMDataItem = new HashMap<>();
                    String material_trace_id = CFuncUtilsSystem.CreateUUID(true);
                    mapMDataItem.put("item_date", item_date_now);
                    mapMDataItem.put("item_date_val", item_date_val);
                    mapMDataItem.put("material_trace_id", material_trace_id);
                    mapMDataItem.put("station_flow_id", station_flow_id);
                    mapMDataItem.put("prod_line_code", prod_line_code);
                    mapMDataItem.put("station_code", station_code);
                    mapMDataItem.put("serial_num", serial_num);
                    mapMDataItem.put("material_code", material_code);
                    mapMDataItem.put("material_des", material_des);
                    mapMDataItem.put("use_count", 1);
                    mapMDataItem.put("material_uom", material_uom);
                    mapMDataItem.put("material_attr", "");
                    mapMDataItem.put("material_batch", material_batch);
                    mapMDataItem.put("material_supplier", "");
                    mapMDataItem.put("supplier_des", "");
                    mapMDataItem.put("lable_barcode", "");
                    mapMDataItem.put("exact_barcode", exact_barcode);
                    mapMDataItem.put("main_material_flag", "N");
                    mapMDataItem.put("verify_flag","Y");
                    mapMDataItem.put("batch_flag", "N");
                    mapMDataItem.put("bom_version", "");
                    mapMDataItem.put("trace_d_time", item_date);
                    lstMDocuments.add(mapMDataItem);
                }
            }
            if (lstMDocuments.size() > 0) {
                mongoTemplate.insert(lstMDocuments, meMaterialTable);
            }
            //成功返回
            responseParas = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受设备质量数据成功");
        } catch (Exception ex) {
            errorMsg = "MES处理设备接收质量数据接口发生未知异常:" + ex.getMessage();
            responseParas = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

}
