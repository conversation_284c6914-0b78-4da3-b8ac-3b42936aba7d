package com.api.eap.project.zhcy.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

public class SystemTimeUpdater {

    /**
     * 同步系统时间（兼容 Windows 和 Linux）
     * @param timeStr 时间字符串，格式为 yyyy/MM/dd HH:mm:ss
     * @throws IOException 如果命令执行失败
     * @throws InterruptedException 如果命令执行被中断
     * @throws ParseException 如果时间格式错误
     */
    public static void syncSystemTime(String timeStr) throws IOException, InterruptedException, ParseException {
        if (timeStr == null || timeStr.trim().isEmpty()) {
            throw new IllegalArgumentException("Time string cannot be null or empty");
        }

        // 校验时间格式
        SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy/MM/dd HH:mm:ss");
        inputFormat.setLenient(false);
        Date date = inputFormat.parse(timeStr);

        // 根据操作系统选择同步方法
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("win")) {
            syncWindowsTimePowerShell(date);
        } else if (osName.contains("nix") || osName.contains("nux") || osName.contains("mac")) {
            syncLinuxTime(timeStr);
        } else {
            throw new UnsupportedOperationException("Unsupported operating system: " + osName);
        }
    }

    /**
     * 使用 PowerShell 同步 Windows 系统时间
     * @param date 时间对象
     * @throws IOException 如果命令执行失败
     * @throws InterruptedException 如果命令执行被中断
     */
    private static void syncWindowsTimePowerShell(Date date) throws IOException, InterruptedException {
        // 转换为 PowerShell 兼容的格式 (yyyy-MM-dd HH:mm:ss)
        SimpleDateFormat powerShellFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String formattedTimeStr = powerShellFormat.format(date);

        // 构建 PowerShell 命令
        String command = "powershell.exe -Command \"Set-Date -Date \\\"" + formattedTimeStr + "\\\"\"";
        executeCommand(command);
    }

    /**
     * 使用 date 命令同步 Linux 系统时间
     * @param timeStr 时间字符串，格式为 yyyy/MM/dd HH:mm:ss
     * @throws IOException 如果命令执行失败
     * @throws InterruptedException 如果命令执行被中断
     */
    private static void syncLinuxTime(String timeStr) throws IOException, InterruptedException {
        // Linux 的 date -s 命令支持 yyyy/MM/dd HH:mm:ss 格式
        String command = "sudo date -s \"" + timeStr + "\"";
        executeCommand(command);
    }

    /**
     * 执行系统命令并捕获输出
     * @param command 命令字符串
     * @throws IOException 如果命令执行失败
     * @throws InterruptedException 如果命令执行被中断
     */
    private static void executeCommand(String command) throws IOException, InterruptedException {
        Process process = Runtime.getRuntime().exec(command);

        // 捕获标准输出和错误输出
        StringBuilder output = new StringBuilder();
        StringBuilder error = new StringBuilder();

        try (BufferedReader stdOutput = new BufferedReader(new InputStreamReader(process.getInputStream()));
             BufferedReader stdError = new BufferedReader(new InputStreamReader(process.getErrorStream()))) {
            String line;
            while ((line = stdOutput.readLine()) != null) {
                output.append(line).append("\n");
            }
            while ((line = stdError.readLine()) != null) {
                error.append(line).append("\n");
            }
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            throw new IOException("Failed to execute command: " + command + ", exit code: " + exitCode +", output: " + output + ", error: " + error);
        }
    }

    public static void main(String[] args) {
        try {
            syncSystemTime("2025/04/12 15:20:00");
            System.out.println("System time updated successfully");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}