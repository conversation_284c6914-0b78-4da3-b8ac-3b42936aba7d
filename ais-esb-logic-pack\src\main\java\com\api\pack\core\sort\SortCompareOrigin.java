package com.api.pack.core.sort;

import com.alibaba.fastjson.JSONObject;
import com.api.pack.core.board.BoardConst;
import org.springframework.util.ObjectUtils;

import java.util.Locale;

@lombok.Data
@lombok.AllArgsConstructor
@lombok.NoArgsConstructor
public class SortCompareOrigin
{
    private Data src;

    private Data dst;

    private JSONObject plan;

    private SortSplitRule.Item rule;

    private String code;

    public SortCompareOrigin(String srcType, String srcCategory, String srcOrient, Integer srcIndex, JSONObject srcMap, String dstType, String dstCategory, String dstOrient, Integer dstIndex, JSONObject dstMap, JSONObject plan, SortSplitRule.Item rule, String code)
    {
        if (srcCategory == null)
        {
            throw new IllegalArgumentException("srcCategory is null");
        }
        this.src = new Data(srcType, srcCategory, srcOrient, srcIndex, srcMap);
        this.dst = new Data(dstType, dstCategory, dstOrient, dstIndex, dstMap);
        this.plan = plan;
        this.rule = rule;
        this.code = code;
    }

    public SortCompareOrigin(String srcType, String srcCategory, String srcOrient, JSONObject srcMap, String dstType, String dstCategory, String dstOrient, JSONObject dstMap, JSONObject plan, SortSplitRule.Item rule, String code)
    {
        this(srcType, srcCategory, srcOrient, null, srcMap, dstType, dstCategory, dstOrient, null, dstMap, plan, rule, code);
    }

    public SortCompareOrigin(String srcType, String srcCategory, String srcOrient, Integer srcIndex, JSONObject srcMap, String dstType, String dstCategory, String dstOrient, JSONObject dstMap, JSONObject plan, SortSplitRule.Item rule, String code)
    {
        this(srcType, srcCategory, srcOrient, srcIndex, srcMap, dstType, dstCategory, dstOrient, null, dstMap, plan, rule, code);
    }

    @lombok.Data
    @lombok.AllArgsConstructor
    @lombok.NoArgsConstructor
    public static class Data {
        private String name;

        private String type;

        private String category;

        private String orient;

        private Integer index;

        private JSONObject mapping;

        public Data(String type, String category, String orient, Integer index, JSONObject mapping)
        {
            this.type = type;
            this.category = category;
            this.orient = orient;
            this.index = index;
            this.mapping = mapping;
            if (category != null)
            {
                this.name = index != null ? category + "[" + index + "]" : category;
            }
            if (orient != null)
            {
                Locale locale = Locale.getDefault();
                String dataType = category + (!ObjectUtils.isEmpty(index) ? "[" + index + "]" : BoardConst.BLANK);
                String dataOrient = orient.toUpperCase();
                if (Locale.SIMPLIFIED_CHINESE.equals(locale))
                {
                    dataType = dataType + "板";
                    dataOrient = "back".equals(orient) ? "反面" : "正面";
                    this.name = String.format("%s板%s", dataType, dataOrient);
                }
                else
                {
                    this.name = String.format("%s %s", dataType, dataOrient);
                }
            }
        }
    }
}
