package com.api.pack.core.board;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.api.pack.core.sort.SortConst;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.FatalBeanException;
import org.springframework.context.MessageSource;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;
import org.springframework.util.Assert;
import org.springframework.util.ClassUtils;
import org.springframework.util.ObjectUtils;

import java.beans.PropertyDescriptor;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.*;

/**
 * <p>
 * SET Board
 * </p>
 *
 * <AUTHOR>
 * @since 2024-05-10
 */
@ApiModel(value = "SETBoard", description = "SET板件信息")
@Document("a_pack_me_array")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class SETBoard extends Board
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "板件朝向")
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Transient
    private String boardOrient;

    @ApiModelProperty(value = "板件ID")
    @JsonProperty("array_id")
    @JSONField(name = "array_id")
    @Field("array_id")
    private String boardId;

    @ApiModelProperty(value = "板件索引")
    @JsonProperty("array_index")
    @JSONField(name = "array_index")
    @Field("array_index")
    private Integer boardIndex;

    @ApiModelProperty(value = "板件条码")
    @JsonProperty("array_barcode")
    @JSONField(name = "array_barcode")
    @Field("array_barcode")
    private String boardBarcode;

    @ApiModelProperty(value = "板件名称")
    @JsonProperty("array_name")
    @JSONField(name = "array_name")
    @Field("array_name")
    private String boardName;

    @ApiModelProperty(value = "板件等级")
    @JsonProperty("array_level")
    @JSONField(name = "array_level")
    @Field("array_level")
    private String boardLevel;

    @ApiModelProperty(value = "板件类别")
    @JsonProperty("array_category")
    @JSONField(name = "array_category")
    @Field("array_category")
    private String boardCategory;

    @ApiModelProperty(value = "板件类型")
    @JsonProperty("array_type")
    @JSONField(name = "array_type")
    @Field("array_type")
    private String boardType;

    @ApiModelProperty(value = "板件状态")
    @JsonProperty("array_status")
    @JSONField(name = "array_status")
    @Field("array_status")
    private String boardStatus;

    @ApiModelProperty(value = "板件结果")
    @JsonProperty("board_result")
    @JSONField(name = "board_result")
    @Field("board_result")
    private Integer boardResult;

    @ApiModelProperty(value = "板件NG码")
    @JsonProperty("array_ng_code")
    @JSONField(name = "array_ng_code")
    @Field("array_ng_code")
    private Integer boardNgCode;

    @ApiModelProperty(value = "板件NG信息")
    @JsonProperty("array_ng_msg")
    @JSONField(name = "array_ng_msg")
    @Field("array_ng_msg")
    private String boardNgMsg;

    @ApiModelProperty(value = "板件标记/板件光学点标记")
    @JsonProperty("array_mark")
    @JSONField(name = "array_mark")
    @Field("array_mark")
    private String boardMark;

    @ApiModelProperty(value = "板件方向")
    @JsonProperty("array_direction")
    @JSONField(name = "array_direction")
    @Field("array_direction")
    private String boardDirection;

    @ApiModelProperty(value = "板件字符")
    @JsonProperty("array_char")
    @JSONField(name = "array_char")
    @Field("array_char")
    private String boardChar;

    @ApiModelProperty(value = "板件排版数")
    @JsonProperty("array_bd_count")
    @JSONField(name = "array_bd_count")
    @Field("array_bd_count")
    private Integer boardLayoutNumber;

    @ApiModelProperty(value = "板件SN")
    @JsonProperty("board_sn")
    @JSONField(name = "board_sn")
    @Field("board_sn")
    private String boardSn;

    @ApiModelProperty(value = "板件旋转")
    @JsonProperty("board_turn")
    @JSONField(name = "board_turn")
    @Field("board_turn")
    private Integer boardTurn;

    @ApiModelProperty(value = "板件旋转角度")
    @JsonProperty("board_rotate")
    @JSONField(name = "board_rotate")
    @Field("board_rotate")
    private Double boardRotate;

    @ApiModelProperty(value = "板长(mm)")
    @JsonProperty("m_length")
    @JSONField(name = "m_length")
    @Field("m_length")
    private Double boardLength;

    @ApiModelProperty(value = "板宽(mm)")
    @JsonProperty("m_width")
    @JSONField(name = "m_width")
    @Field("m_width")
    private Double boardWidth;

    @ApiModelProperty(value = "板厚(mm)")
    @JsonProperty("m_tickness")
    @JSONField(name = "m_tickness")
    @Field("m_tickness")
    private Double boardThickness;

    @ApiModelProperty(value = "板重(g)")
    @JsonProperty("m_weight")
    @JSONField(name = "m_weight")
    @Field("m_weight")
    private Double boardWeight;

    @ApiModelProperty(value = "用户名称")
    @JsonProperty("user_name")
    @JSONField(name = "user_name")
    @Field("user_name")
    private String userName;

    @ApiModelProperty(value = "单号/批号")
    @JsonProperty("lot_num")
    @JSONField(name = "lot_num")
    @Field("lot_num")
    private String lotNum;

    @ApiModelProperty(value = "任务类型")
    @JsonProperty("task_type")
    @JSONField(name = "task_type")
    @Field("task_type")
    private String taskType;

    @ApiModelProperty(value = "周期")
    @JsonProperty("cycle_period")
    @JSONField(name = "cycle_period")
    @Field("cycle_period")
    private String cyclePeriod;

    @ApiModelProperty(value = "料号")
    @JsonProperty("model_type")
    @JSONField(name = "model_type")
    @Field("model_type")
    private String partNumber;

    @ApiModelProperty(value = "料号版本")
    @JsonProperty("model_version")
    @JSONField(name = "model_version")
    @Field("model_version")
    private String partVersion;

    @ApiModelProperty(value = "批次号")
    @JsonProperty("batch_no")
    @JSONField(name = "batch_no")
    @Field("batch_no")
    private String batchNo;

    @ApiModelProperty(value = "镭射批次号")
    @JsonProperty("laser_batch_no")
    @JSONField(name = "laser_batch_no")
    @Field("laser_batch_no")
    private String laserBatchNo;

    @ApiModelProperty(value = "排版号")
    @JsonProperty("typesetting_no")
    @JSONField(name = "typesetting_no")
    @Field("typesetting_no")
    private String typesettingNo;

    @ApiModelProperty(value = "客户料号")
    @JsonProperty("customer_mn")
    @JSONField(name = "customer_mn")
    @Field("customer_mn")
    private String customerMn;

    @ApiModelProperty(value = "UL码")
    @JsonProperty("ul_code")
    @JSONField(name = "ul_code")
    @Field("ul_code")
    private String ulCode;

    @ApiModelProperty(value = "修复标签")
    @JsonProperty("repair_label")
    @JSONField(name = "repair_label")
    @Field("repair_label")
    private Boolean repairLabel;

    @ApiModelProperty(value = "流程异常标记")
    @JsonProperty("flow_error")
    @JSONField(name = "flow_error")
    @Field("flow_error")
    private Boolean flowError;

    @ApiModelProperty(value = "流程异常信息")
    @JsonProperty("flow_error_msg")
    @JSONField(name = "flow_error_msg")
    @Field("flow_error_msg")
    private String flowErrorMsg;

    @ApiModelProperty(value = "存放位置")
    @JsonProperty("deposit_position")
    @JSONField(name = "deposit_position")
    @Field("deposit_position")
    private Integer depositPosition;

    @ApiModelProperty(value = "XOUT设定数")
    @JsonProperty("xout_set_num")
    @JSONField(name = "xout_set_num")
    @Field("xout_set_num")
    private Integer xoutSetNumber;

    @ApiModelProperty(value = "XOUT实际数")
    @JsonProperty("xout_act_num")
    @JSONField(name = "xout_act_num")
    @Field("xout_act_num")
    private Integer xoutActualNumber;

    @ApiModelProperty(value = "XOUT位置")
    @JsonProperty("xout_positions")
    @JSONField(name = "xout_positions")
    @Field("xout_positions")
    private String xoutPositions;

    @ApiModelProperty(value = "XOUT标记")
    @JsonProperty("xout_flag")
    @JSONField(name = "xout_flag")
    @Field("xout_flag")
    private String xoutFlag;

    @ApiModelProperty(value = "上传标记")
    @JsonProperty("up_flag")
    @JSONField(name = "up_flag")
    @Field("up_flag")
    private String upFlag;

    @ApiModelProperty(value = "上传NG码")
    @JsonProperty("up_ng_code")
    @JSONField(name = "up_ng_code")
    @Field("up_ng_code")
    private Integer upNgCode;

    @ApiModelProperty(value = "上传NG信息")
    @JsonProperty("up_ng_msg")
    @JSONField(name = "up_ng_msg")
    @Field("up_ng_msg")
    private String upNgMsg;

    @ApiModelProperty(value = "解绑标记")
    @JsonProperty("unbind_flag")
    @JSONField(name = "unbind_flag")
    @Field("unbind_flag")
    private String unbindFlag;

    @ApiModelProperty(value = "解绑用户")
    @JsonProperty("unbind_user")
    @JSONField(name = "unbind_user")
    @Field("unbind_user")
    private String unbindUser;

    @ApiModelProperty(value = "解绑时间")
    @JsonProperty("unbind_time")
    @JSONField(name = "unbind_time")
    @Field("unbind_time")
    private String unbindTime;

    @ApiModelProperty(value = "解绑方式")
    @JsonProperty("unbind_way")
    @JSONField(name = "unbind_way")
    @Field("unbind_way")
    private String unbindWay;

    @ApiModelProperty(value = "打包使用标记")
    @JsonProperty("pile_use_flag")
    @JSONField(name = "pile_use_flag")
    @Field("pile_use_flag")
    private String pileUseFlag;

    @ApiModelProperty(value = "打包条码")
    @JsonProperty("pile_barcode")
    @JSONField(name = "pile_barcode")
    @Field("pile_barcode")
    private String pileBarcode;

    @ApiModelProperty(value = "多面板件正面信息")
    @JsonProperty("array_front_info")
    @JSONField(name = "array_front_info")
    @Field("array_front_info")
    private String multiAspectFront;

    @ApiModelProperty(value = "TOP板面读取持续时间")
    @JsonProperty("read_duration")
    @JSONField(name = "read_duration")
    @Field("read_duration")
    private Integer topReadDuration;

    @ApiModelProperty(value = "BOT板面读取持续时间")
    @JsonProperty("read_duration2")
    @JSONField(name = "read_duration2")
    @Field("read_duration2")
    private Integer botReadDuration;

//    @ApiModelProperty(value = "多面板件正面信息（对象）")
//    @JsonIgnore
//    @JSONField(serialize = false, deserialize = false)
//    private SETBoard multiAspectFrontBoard;

    @ApiModelProperty(value = "多面板件背面信息")
    @JsonProperty("array_back_info")
    @JSONField(name = "array_back_info")
    @Field("array_back_info")
    private String multiAspectBack;

//    @ApiModelProperty(value = "多面板件背面信息（对象）")
//    @JsonIgnore
//    @JSONField(serialize = false, deserialize = false)
//    private SETBoard multiAspectBackBoard;

    @ApiModelProperty(value = "多面板件信息")
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Transient
    @ToString.Exclude
    private Map<String, SETBoard> multiAspect;

    @ApiModelProperty(value = "子板件信息")
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @Transient
    @ToString.Exclude
    private List<PCSBoard> childrenBoards;

    @ApiModelProperty(value = "客户二维码1")
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private String customerQRC1;

    @ApiModelProperty(value = "客户二维码2")
    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    private String customerQRC2;

    @ApiModelProperty(value = "工厂订单号")
    @JsonProperty("plant_order_num")
    @JSONField(name = "plant_order_num")
    @Field("plant_order_num")
    private String plantOrderNum;

    @ApiModelProperty(value = "PCS板件类型")
    @JsonProperty("bd_type")
    @JSONField(name = "bd_type")
    @Field("bd_type")
    private String pcsBoardType;

    @ApiModelProperty(value = "计划ID")
    @JsonProperty("plan_id")
    @JSONField(name = "plan_id")
    @Field("plan_id")
    private String planId;

    public SETBoard(String boardId)
    {
        this.setBoardId(boardId);
        this.setBoardBarcode(BoardConst.NO_READ);
        this.setBoardLevel(BoardConst.BLANK);
        this.setBoardLength(0.00D);
        this.setBoardWidth(0.00D);
        this.setBoardThickness(0.00D);
        this.setBoardWeight(0.00D);
        this.setBoardStatus(BoardConst.OK);
        this.setBoardTurn(BoardConst.TURN_2);
        this.setBoardLayoutNumber(0);
        this.setBoardMark(BoardConst.BLANK);
        this.setBoardResult(BoardConst.RESULT_OK);
        this.setUserName(BoardConst.BLANK);
        this.setLotNum(BoardConst.BLANK);
        this.setTaskType(BoardConst.BLANK);
        this.setCyclePeriod(BoardConst.BLANK);
        this.setPartNumber(BoardConst.BLANK);
        this.setPartVersion(BoardConst.BLANK);
        this.setBatchNo(BoardConst.BLANK);
        this.setLaserBatchNo(BoardConst.BLANK);
        this.setTypesettingNo(BoardConst.BLANK);
        this.setCustomerMn(BoardConst.BLANK);
        this.setUlCode(BoardConst.BLANK);
        this.setXoutFlag(BoardConst.FLAG_N);
        this.setXoutSetNumber(0);
        this.setXoutActualNumber(0);
        this.setPileBarcode(BoardConst.BLANK);
        this.setPileUseFlag(BoardConst.FLAG_N);
        this.setPlanId(BoardConst.BLANK);
        this.setBoardNgCode(BoardConst.CODE_OK);
        this.setBoardNgMsg(BoardConst.BLANK);
        this.setUpFlag(BoardConst.FLAG_N);
        this.setUpNgCode(BoardConst.CODE_OK);
        this.setUpNgMsg(BoardConst.BLANK);
        this.setUnbindFlag(BoardConst.FLAG_N);
        this.setUnbindUser(BoardConst.BLANK);
        this.setUnbindTime(BoardConst.BLANK);
        this.setUnbindWay(BoardConst.BLANK);
        this.setDepositPosition(BoardConst.DEPOSIT_POSITION_OK);
    }

    public SETBoard(String orient, JSONObject bd)
    {
        // SET BOARD INFO，{... SetChar ...}
        /*
        {
            "SetQRC": "10002024588040",
            "SetQRCLevel": "A",
            "DirMarkChkRtl": "@NC@",
            "Direction": "0",
            "LayoutQty": "2",
            "XoutQty": "0",
            "SetChar": {
                "PlainCode": "@NC@",
                "DateCode": "2401",
                "LotNum": "@NC@",
                "PartNumber": "@NC@",
                "CProductName": "@NC@"
            },
            "PcsMsgList": [
                {... PcsChar ...}
            ],
			"CustomerQRC1": "0GO012371001.01C01105",
			"CustomerQRC2": "0GO012371001.01C01105",
			"CustQRC1Level": "B",
			"CustQRC2Level": "B",
			"RepairLabel": "TRUE"
        }
        */
        super(orient, BoardConst.SET, bd);
        String boardBarcodeKey = BoardConst.SET + BoardConst.QRC;
        String boardCharKey = BoardConst.SET + BoardConst.CHAR;
        if (bd.containsKey(boardBarcodeKey) && bd.containsKey(boardCharKey))
        {
            this.setXoutActualNumber(bd.getInteger(BoardConst.CCD_CONTENT_SET_XOUT_QTY));
            this.setXoutFlag(this.getXoutActualNumber() > 0 ? BoardConst.FLAG_Y : BoardConst.FLAG_N);
            this.setBoardLayoutNumber(bd.getInteger(BoardConst.CCD_CONTENT_SET_LAYOUT_QTY));
            List<PCSBoard> childrenBoards = new LinkedList<>();
            JSONArray pcsList = bd.getJSONArray(BoardConst.PCS + BoardConst.MSG_LIST);
            for (int i = 0; i < pcsList.size(); i++)
            {
                JSONObject pcs = pcsList.getJSONObject(i);
                PCSBoard pcsBoard = new PCSBoard(orient, pcs);
                pcsBoard.setParentBoard(this);
                childrenBoards.add(pcsBoard);
            }
            this.setChildrenBoards(childrenBoards);
            this.setRepairLabel(bd.getBoolean(BoardConst.CCD_CONTENT_SET_REPAIR_LABEL));
            this.setFlowError(bd.getBoolean(BoardConst.CCD_CONTENT_SET_FLOW_ERROR));
            this.setFlowErrorMsg(bd.getString(BoardConst.CCD_CONTENT_SET_FLOW_ERROR_MSG));
            this.setCustomerQRC1(bd.getString(BoardConst.CCD_CONTENT_SET_CUSTOMER_QRC1));
            if (BoardConst.VALUE_NC.equals(this.getCustomerQRC1()) || BoardConst.VALUE_NULL.equals(this.getCustomerQRC1()))
            {
                this.setCustomerQRC1(null);
            }
            this.setCustomerQRC2(bd.getString(BoardConst.CCD_CONTENT_SET_CUSTOMER_QRC2));
            if (BoardConst.VALUE_NC.equals(this.getCustomerQRC2()) || BoardConst.VALUE_NULL.equals(this.getCustomerQRC2()))
            {
                this.setCustomerQRC2(null);
            }
            this.setBoardDirection(BoardConst.convertValue(this.getBoardDirection()));
            if (!ObjectUtils.isEmpty(this.getBoardDirection()))
            {
                try
                {
                    int boardDirectionInt = Integer.parseInt(this.getBoardDirection());
                    if (boardDirectionInt > 0)
                    {
                        this.setBoardTurn(BoardConst.TURN_1);
                    }
                }
                catch (NumberFormatException ignored)
                {
                }
            }
        }
    }

    /**
     * 补全并检查板件数据是否完整
     *
     * @param boardType          板件类型
     * @param multiAspect        多面板件信息
     * @param boardF2BComparator 板件正反面比对器
     * @param messageSource      消息源
     * @param isCompare          是否比对
     * @throws BoardCompareException 板件比对异常
     */
    public void completeAndCheckFromMultiAspect(String boardType, String pcsBoardType, Map<String, JSONObject> multiAspect, BoardF2BComparator boardF2BComparator, MessageSource messageSource, boolean isCompare) throws BoardCompareException
    {
        JSONObject front = multiAspect.get(SortConst.BOARD_FRONT);
        JSONObject back = multiAspect.get(SortConst.BOARD_BACK);
        SETBoard setFront = new SETBoard(SortConst.BOARD_FRONT, front);
        SETBoard setBack = new SETBoard(SortConst.BOARD_BACK, back);
        this.setMultiAspect(new HashMap<>());
        this.setMultiAspectFront(front.toJSONString());
        this.getMultiAspect().put(SortConst.BOARD_FRONT, setFront);
        this.setMultiAspectBack(back.toJSONString());
        this.getMultiAspect().put(SortConst.BOARD_BACK, setBack);
        try
        {
            String[] setProps = BoardConst.SET_BOARD_INITIAL_PROPERTIES;
            boolean useFront = BoardConst.SINGLE.equals(boardType) && !ObjectUtils.isEmpty(BoardConst.convertValue(setFront.getBoardBarcode()));
            boolean useBack = BoardConst.SINGLE.equals(boardType) && !ObjectUtils.isEmpty(BoardConst.convertValue(setBack.getBoardBarcode()));
            if (useFront)
            {
                this.setBoardOrient(setFront.getBoardOrient());
                this.copyProperties(setFront, this, setProps);
            }
            else if (useBack)
            {
                this.setBoardOrient(setBack.getBoardOrient());
                this.copyProperties(setBack, this, setProps);
            }
            else
            {
                this.setBoardOrient(setFront.getBoardOrient());
                this.copyProperties(setFront, this, setProps);
            }
            this.setBoardBarcode(BoardConst.convertValue(this.getBoardBarcode()));
        }
        catch (FatalBeanException ex)
        {
            throw new BoardCompareException(ex.getMessage());
        }
        this.setBoardType(boardType);
        this.setPcsBoardType(pcsBoardType);
        switch (boardType)
        {
            case SortConst.BOARD_SIDE_NONE:
                this.setBoardBarcode(BoardConst.BLANK);
                break;
            case SortConst.BOARD_SIDE_SINGLE:
                // 判断板件类型：为单面板件时，检查板件条码是否存在
                if (ObjectUtils.isEmpty(this.getBoardBarcode()))
                {
                    String errMessage = messageSource.getMessage(SortConst.COMPARE_MESSAGES_PACK_NO_DETECTED, new Object[]{
                            BoardConst.SET, BoardConst.QRC
                    }, Locale.getDefault());
                    throw new BoardCompareException(errMessage);
                }
                break;
            case SortConst.BOARD_SIDE_DOUBLE:
                if (isCompare)
                {
                    // 判断板件类型：为双面板件时，正反面数据比对
                    boardF2BComparator.compare(setFront, setBack, SortConst.SET_BOARD_INITIAL_PROPERTIES, messageSource);
                }
                break;
        }
    }

    /**
     * 比较SET中的XOUT实际PCS数量和板件PCS数量
     */
    public void compareWithXoutActualNumberAndBoardLayoutNumber() throws BoardCompareException
    {
        if (this.getXoutActualNumber() >= this.getBoardLayoutNumber())
        {
            throw new BoardCompareException("XOUT实际PCS数量大于等于板件PCS数量");
        }
    }

    /**
     * 根据BoardXNPComparator和比较规则进行比较
     *
     * @param comparator       BoardXNPComparator
     * @param compareRuleName  String
     * @param compareRuleValue String
     * @param messageSource    MessageSource
     * @return BoardCompareResult
     */
    public BoardCompareResult compareByBoardXNPComparator(BoardXNPComparator comparator, String compareRuleName, String compareRuleValue, MessageSource messageSource)
    {
        BoardCompareResult boardXNPCompareResult = comparator.compare(this, new BoardCompareRule(compareRuleName, compareRuleValue), messageSource);
        if (boardXNPCompareResult != null)
        {
            if (boardXNPCompareResult.isOK()  // 检测结果为OK
                    && !BoardConst.DEPOSIT_POSITION_OK.equals(boardXNPCompareResult.getPosition()) // 没分配到OK仓位
            )
            {
                this.setXoutFlag(BoardConst.FLAG_Y);
                this.setXoutPositions(boardXNPCompareResult.getMsg());
            }
            else if (boardXNPCompareResult.isNG()) // 检测结果为NG
            {
                return new BoardCompareResult(BoardConst.STATUS_NG, BoardConst.CODE_NG, boardXNPCompareResult.getMsg(), BoardConst.DEPOSIT_POSITION_NG);
            }
        }
        return boardXNPCompareResult;
    }

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    public boolean isXout()
    {
        return BoardConst.FLAG_Y.equals(this.getXoutFlag()) || this.getXoutActualNumber() > 0;
    }

    private void copyProperties(Object source, Object target, String[] properties)
    {
        Assert.notNull(source, "Source must not be null");
        Assert.notNull(target, "Target must not be null");
        for (String property : properties)
        {
            PropertyDescriptor sourceProperty = BeanUtils.getPropertyDescriptor(source.getClass(), property);
            PropertyDescriptor targetProperty = BeanUtils.getPropertyDescriptor(target.getClass(), property);
            if (sourceProperty != null && targetProperty != null)
            {
                Method writeMethod = targetProperty.getWriteMethod();
                Method readMethod = sourceProperty.getReadMethod();
                if (readMethod != null && ClassUtils.isAssignable(writeMethod.getParameterTypes()[0], readMethod.getReturnType()))
                {
                    try
                    {
                        if (!Modifier.isPublic(readMethod.getDeclaringClass().getModifiers()))
                        {
                            readMethod.setAccessible(true);
                        }
                        Object value = readMethod.invoke(source);
                        if (!Modifier.isPublic(writeMethod.getDeclaringClass().getModifiers()))
                        {
                            writeMethod.setAccessible(true);
                        }
                        writeMethod.invoke(target, value);
                    }
                    catch (Throwable ex)
                    {
                        throw new FatalBeanException("Could not copy property '" + property + "' from source to target", ex);
                    }
                }
            }
        }
    }
}
