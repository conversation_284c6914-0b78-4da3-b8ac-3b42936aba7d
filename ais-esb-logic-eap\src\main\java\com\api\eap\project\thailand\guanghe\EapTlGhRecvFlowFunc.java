package com.api.eap.project.thailand.guanghe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.math.BigDecimal;

/**
 * <p>
 * (泰国广合)EAP接受流程功能函数
 * 1.载具批次信息下发
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class EapTlGhRecvFlowFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapTlGhInterfCommon eapTlGhInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.载具批次信息下发
    public JSONObject CarrierLotInfoDownloadCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "CarrierLotInfoDownloadCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 500;
        Long station_id = 0L;
        String station_code = "";
        String station_attr = "";
        String port_code = "";
        String user_name = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.解析接受参数
            JSONArray arrlist = jsonParas.getJSONArray("arrlist");
            if (arrlist == null || arrlist.size() <= 0) {
                errorMsg = "arrlist为null数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            JSONObject jbLotItemFirst = arrlist.getJSONObject(0);
            station_code = jbLotItemFirst.getString("eqno") == null ? "" : jbLotItemFirst.getString("eqno");
            port_code = jbLotItemFirst.getString("portid") == null ? "" : jbLotItemFirst.getString("portid");
            String pallet_type = jbLotItemFirst.getString("carrieridmode") == null ? "" : jbLotItemFirst.getString("carrieridmode");
            if (pallet_type.equals("")) {
                errorMsg = "EAP下发载具类型{" + pallet_type + "}不能为空值";
                opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否正确
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + station_code + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发配方任务中工位号{" + station_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            station_attr = itemListStation.get(0).get("station_attr").toString();
            //判断端口是否正确
            String sqlPortCount = "select count(1) " +
                    "from a_eap_fmod_station_port " +
                    "where enable_flag='Y' and station_id=" + station_id + " " +
                    "and port_code='" + port_code + "'";
            Integer portCount = cFuncDbSqlResolve.GetSelectCount(sqlPortCount);
            if (portCount <= 0) {
                errorMsg = "EAP下发配方任务中端口号{" + port_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //获取当前登入者
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //解析数据
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstPlanDocuments = new ArrayList<>();
            List<Map<String, Object>> lstPlanBDocuments = new ArrayList<>();
            List<String> lstLotNum = new ArrayList<>();
            String lot_group_num = CFuncUtilsSystem.GetOnlySign("LG");
            String PbjLoadFlag = cFuncDbSqlResolve.GetParameterValue("PbjLoadFlag");
            String group_id = CFuncUtilsSystem.CreateUUID(true);
            for (int i = 0; i < arrlist.size(); i++) {
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String plan_id = CFuncUtilsSystem.CreateUUID(true);
                JSONObject jbLotItem = arrlist.getJSONObject(i);
                String lot_num = jbLotItem.getString("lotno") == null ? "" : jbLotItem.getString("lotno");
                String lot_count = jbLotItem.getString("number") == null ? "" : jbLotItem.getString("number");
                if (lot_count.equals("")) lot_count = "0";
                BigDecimal bigDecimal = new BigDecimal(lot_count);
                Integer plan_lot_count = bigDecimal.intValue();
                String material_code = jbLotItem.getString("prodno") == null ? "" : jbLotItem.getString("prodno");
                String pallet_num = jbLotItem.getString("carrierid") == null ? "" : jbLotItem.getString("carrierid");
                String lot_version = jbLotItem.getString("prodrev") == null ? "" : jbLotItem.getString("prodrev");
                String panel_length = jbLotItem.getString("panelLength") == null ? "" : jbLotItem.getString("panelLength");
                String panel_width = jbLotItem.getString("panelWidth") == null ? "" : jbLotItem.getString("panelWidth");
                String panel_tickness = jbLotItem.getString("panelTickNess") == null ? "" : jbLotItem.getString("panelTickNess");
                String firstmode = jbLotItem.getString("firstmode") == null ? "" : jbLotItem.getString("firstmode");//首件模式：0：不做首件，1：停机首件，2：下料首件
                String firstqty = jbLotItem.getString("firstqty") == null ? "" : jbLotItem.getString("firstqty");
                if (PbjLoadFlag.equals("Y")) {
                    //配板机母批号为外层批号
                    JSONArray heatnumberList = jbLotItem.getJSONArray("heatnumberList");
                    if (heatnumberList != null && heatnumberList.size() > 0) {
                        String layerlot = heatnumberList.getJSONObject(i).getString("layerlot");
                        if (layerlot != null && !layerlot.equals(""))
                            lot_group_num = layerlot;
                    }
                }
                if (firstqty.equals("")) firstqty = "0";
                BigDecimal bigDecimal2 = new BigDecimal(firstqty);
                Integer first_qty = bigDecimal2.intValue();
                if (firstmode.equals("2") && first_qty <= 0) {
                    errorMsg = "工单{" + lot_num + "}首件模式是{" + firstmode + "},但首件数量是{" + firstqty + "},此时不允许EAP下发";
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                if (panel_length.equals("")) panel_length = "0";
                if (panel_width.equals("")) panel_width = "0";
                if (panel_tickness.equals("")) panel_tickness = "0";
                bigDecimal = new BigDecimal(panel_length);
                Double panel_length_d = bigDecimal.doubleValue();
                bigDecimal = new BigDecimal(panel_width);
                Double panel_width_d = bigDecimal.doubleValue();
                bigDecimal = new BigDecimal(panel_tickness);
                Double panel_tickness_d = bigDecimal.doubleValue();
                String port_code2 = jbLotItem.getString("portid") == null ? "" : jbLotItem.getString("portid");
                //1.数据合规性判断
                if (lot_num.equals("")) {
                    errorMsg = "EAP下发的配方任务中{lotno}工单字段值不能为空值";
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                if (plan_lot_count <= 0) {
                    errorMsg = "EAP下发的配方任务{" + lot_num + "}中{number}工单数量字段值不能<=0";
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                if (panel_length_d <= 0D || panel_width_d <= 0D) {
                    errorMsg = "EAP下发的配方任务{" + lot_num + "}中板长{" + panel_length + "}或者板宽{" + panel_width + "}不能<=0";
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                if (!port_code2.equals(port_code)) {
                    errorMsg = "同一载具任务不能下发不同的端口号,工单{" + lot_num + "}端口号为{" + port_code2 + "}与第一个任务的端口号{" + port_code + "}不匹配";
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                //2.判断工单是否已经存在
                if (lstLotNum.contains(lot_num)) {
                    errorMsg = "EAP下发配方任务中存在相同工单{" + lot_num + "}";
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                String[] group_lot_status_list = new String[]{"WAIT", "PLAN", "WORK"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                long planCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
                if (planCount > 0) {
                    errorMsg = "工单{" + lot_num + "}已存在且未被消耗掉,此时不允许EAP下发";
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                JSONArray pnlids = jbLotItem.getJSONArray("pnlids");
                //4.获取配方参数
                jbLotItem.remove("eqno");
                jbLotItem.remove("portid");
                jbLotItem.remove("prodno");
                jbLotItem.remove("prodrev");
                jbLotItem.remove("lotno");
                jbLotItem.remove("number");
                jbLotItem.remove("carrierid");
                jbLotItem.remove("panelLength");
                jbLotItem.remove("panelWidth");
                jbLotItem.remove("panelTickNess");
                jbLotItem.remove("pnlids");
                String item_info = jbLotItem.toString();
                //5.加入Lot工单集合
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("plan_id", plan_id);
                mapBigDataRow.put("station_id", station_id);
                mapBigDataRow.put("task_from", "EAP");
                mapBigDataRow.put("group_lot_num", lot_group_num);
                mapBigDataRow.put("lot_num", lot_num);
                mapBigDataRow.put("lot_short_num", "");
                mapBigDataRow.put("lot_index", i + 1);
                mapBigDataRow.put("plan_lot_count", plan_lot_count);
                mapBigDataRow.put("target_lot_count", plan_lot_count);
                mapBigDataRow.put("port_code", port_code);
                mapBigDataRow.put("material_code", material_code);
                mapBigDataRow.put("pallet_num", pallet_num);
                mapBigDataRow.put("pallet_type", pallet_type);
                mapBigDataRow.put("lot_level", lot_version);
                mapBigDataRow.put("panel_length", panel_length_d);
                mapBigDataRow.put("panel_width", panel_width_d);
                mapBigDataRow.put("panel_tickness", panel_tickness_d);
                mapBigDataRow.put("panel_model", -1);
                mapBigDataRow.put("inspect_count", first_qty);
                mapBigDataRow.put("inspect_finish_count", 0);
                mapBigDataRow.put("pdb_count", 0);
                mapBigDataRow.put("pdb_rule", 0);
                mapBigDataRow.put("fp_count", 0);
                mapBigDataRow.put("group_lot_status", "PLAN");
                mapBigDataRow.put("lot_status", "PLAN");
                mapBigDataRow.put("finish_count", 0);
                mapBigDataRow.put("finish_ok_count", 0);
                mapBigDataRow.put("finish_ng_count", 0);
                mapBigDataRow.put("task_error_code", 0);
                mapBigDataRow.put("item_info", item_info);
                mapBigDataRow.put("task_start_time", "");
                mapBigDataRow.put("task_end_time", "");
                mapBigDataRow.put("task_cost_time", (long) 0);
                mapBigDataRow.put("attribute1", "");
                mapBigDataRow.put("attribute2", "");
                mapBigDataRow.put("attribute3", "");
                mapBigDataRow.put("face_code", 0);
                mapBigDataRow.put("pallet_use_count", 0);
                mapBigDataRow.put("user_name", user_name);
                mapBigDataRow.put("group_id", group_id);
                mapBigDataRow.put("offline_flag", "N");
                mapBigDataRow.put("target_update_count", 0);
                mapBigDataRow.put("pnl_infos", "");
                lstPlanDocuments.add(mapBigDataRow);
                lstLotNum.add(lot_num);
                //6.加入PNL集合
                if (pnlids != null && pnlids.size() > 0) {
                    for (int j = 0; j < pnlids.size(); j++) {
                        JSONObject jbPanel = pnlids.getJSONObject(j);
                        String pnlid = jbPanel.getString("pnlid") == null ? "" : jbPanel.getString("pnlid");
                        String flag = jbPanel.getString("flag") == null ? "" : jbPanel.getString("flag");
                        String plan_d_id = CFuncUtilsSystem.CreateUUID(true);
                        Map<String, Object> mapBigDataRowB = new HashMap<>();
                        mapBigDataRowB.put("item_date", item_date);
                        mapBigDataRowB.put("item_date_val", item_date_val);
                        mapBigDataRowB.put("plan_d_id", plan_d_id);
                        mapBigDataRowB.put("plan_id", plan_id);
                        mapBigDataRowB.put("panel_barcode", pnlid);
                        mapBigDataRowB.put("panel_attr", flag);
                        mapBigDataRowB.put("use_sign", 0);
                        lstPlanBDocuments.add(mapBigDataRowB);
                    }
                }
            }
            if (lstPlanBDocuments.size() > 0) mongoTemplate.insert(lstPlanBDocuments, apsPlanBTable);
            if (lstPlanDocuments.size() > 0) mongoTemplate.insert(lstPlanDocuments, apsPlanTable);

            //返回消息
            code = 200;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.控制投板命令下发
    public JSONObject CarrierControlCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "CarrierControlCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 500;
        Long station_id = 0L;
        String station_code = "";
        String station_attr = "";
        String port_code = "";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.解析接受参数
            station_code = jsonParas.getString("eqno") == null ? "" : jsonParas.getString("eqno");
            port_code = jsonParas.getString("portid") == null ? "" : jsonParas.getString("portid");
            String allow_code = jsonParas.getString("result") == null ? "" : jsonParas.getString("result");
            String eap_allow_code = "2";
            if (allow_code.equals("start")) eap_allow_code = "1";

            //判断工位是否正确
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + station_code + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发控制投板命令中工位号{" + station_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            station_attr = itemListStation.get(0).get("station_attr").toString();
            //判断端口序号
            String sqlPort = "select port_index " +
                    "from a_eap_fmod_station_port " +
                    "where enable_flag='Y' and station_id=" + station_id + " " +
                    "and port_code='" + port_code + "'";
            List<Map<String, Object>> itemListPort = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlPort,
                    false, request, apiRoutePath);
            if (itemListPort == null || itemListPort.size() <= 0) {
                errorMsg = "EAP下发控制投板命令中端口号{" + port_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String port_index = itemListPort.get(0).get("port_index").toString();
            if (station_attr.equals("Load")) {
                errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr,
                        "Eap", "EapOutStatus", "EapAllowStart" + port_index,
                        "EAP", eap_allow_code, true);
                if (!errorMsg.equals("")) {
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
            }

            code = 200;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.下发载具类型
    public JSONObject CarrierModeCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "CarrierModeCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 500;
        Long station_id = 0L;
        String station_code = "";
        String station_attr = "";
        String port_code = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.解析接受参数
            station_code = jsonParas.getString("eqno") == null ? "" : jsonParas.getString("eqno");
            port_code = jsonParas.getString("portid") == null ? "" : jsonParas.getString("portid");
            String pallet_num = jsonParas.getString("carrierid") == null ? "" : jsonParas.getString("carrierid");
            String pallet_type = jsonParas.getString("carrieridmode") == null ? "" : jsonParas.getString("carrieridmode");
            String pallet_volume = jsonParas.getString("carrieridvolume") == null ? "" : jsonParas.getString("carrieridvolume");
            String pallet_limit = jsonParas.getString("carrieridlimit") == null ? "" : jsonParas.getString("carrieridlimit");
            if (pallet_volume.equals("")) pallet_volume = "0";
            BigDecimal bigDecimal = new BigDecimal(pallet_volume);
            Integer pallet_volume_int = bigDecimal.intValue();

            if (pallet_limit == null || pallet_limit.equals("")) pallet_limit = "1000";
            BigDecimal bigDecimal2 = new BigDecimal(pallet_limit);
            float pallet_limit_float = bigDecimal2.floatValue();

            if (pallet_type.equals("")) {
                errorMsg = "EAP下发载具类型{" + pallet_type + "}不能为空值";
                opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否正确
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + station_code + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发载具类型中工位号{" + station_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            station_attr = itemListStation.get(0).get("station_attr").toString();
            //判断端口序号
            String sqlPort = "select port_index " +
                    "from a_eap_fmod_station_port " +
                    "where enable_flag='Y' and station_id=" + station_id + " " +
                    "and port_code='" + port_code + "'";
            List<Map<String, Object>> itemListPort = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlPort,
                    false, request, apiRoutePath);
            if (itemListPort == null || itemListPort.size() <= 0) {
                errorMsg = "EAP下发载具类型中端口号{" + port_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String port_index = itemListPort.get(0).get("port_index").toString();
            if (station_attr.equals("Load")) {
                //更新放扳机任务信息
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
//                long planCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
//                if (planCount <= 0) {
//                    errorMsg = "放扳机EAP下发载具类型接口时需要先下发配方任务";
//                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg);
//                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
//                    jbResult.put("responseParas", responseParas);
//                    jbResult.put("successFlag", false);
//                    jbResult.put("message", errorMsg);
//                    return jbResult;
//                }
                Update updateBigData = new Update();
                updateBigData.set("pallet_num", pallet_num);
                updateBigData.set("pallet_type", pallet_type);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            String PbjLoadFlag = cFuncDbSqlResolve.GetParameterValue("PbjLoadFlag");//配板机放板标识
            if (!PbjLoadFlag.equals("Y")) {
                String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
                String eapPalletTypeTag = "";
                String eapPalletNumTag = "";
                String eapPalletVolumeTag = "";
                String eapPalletLimitTag = "";
                if (aisMonitorModel.equals("AIS-PC")) {
                    eapPalletTypeTag = station_attr + "Eap/EapOutStatus/PalletType" + port_index;
                    eapPalletNumTag = station_attr + "Eap/EapOutStatus/PalletNum" + port_index;
                    eapPalletVolumeTag = station_attr + "Eap/EapOutStatus/PalletVolume" + port_index;
                    eapPalletLimitTag = station_attr + "Ais/AisRcsStatus/RemainingLimit" + port_index;
                } else {
                    eapPalletTypeTag = station_attr + "Eap_" + station_code + "/EapOutStatus/PalletType" + port_index;
                    eapPalletNumTag = station_attr + "Eap_" + station_code + "/EapOutStatus/PalletNum" + port_index;
                    eapPalletVolumeTag = station_attr + "Eap_" + station_code + "/EapOutStatus/PalletVolume" + port_index;
                    eapPalletLimitTag = station_attr + "Ais_" + station_code + "/AisRcsStatus/RemainingLimit" + port_index;
                }
                if (pallet_volume_int <= 0) {
                    errorMsg = "收扳机EAP下发载具类型接口时载具最大容量{" + pallet_volume + "}不能<=0";
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                String tagList = eapPalletTypeTag + "," + eapPalletNumTag + "," + eapPalletVolumeTag + "," + eapPalletLimitTag;
                String tagWriteValues = pallet_type + "&" + pallet_num + "&" + pallet_volume_int + "&" + pallet_limit_float;
                errorMsg = cFuncUtilsCellScada.WriteTagByStation("EAP", station_code, tagList, tagWriteValues, true);
                if (!errorMsg.equals("")) {
                    opCommonFunc.SaveCimMessage(station_id, "0", "CarrierLotInfoDownloadCommand", "AIS", errorMsg, 5);
                    responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
            }
            code = 200;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //4.首件结果命令
    public JSONObject FirstResultsCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "FirstResultsCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 500;
        Long station_id = 0L;
        String apsPlanTable = "a_eap_aps_plan";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //首件结果反馈：0:首件NG，1:首件OK，2：退料
            String station_code = jsonParas.getString("eqno");
            String lot_num = jsonParas.getString("lotno");
            String result = jsonParas.getString("result");
            //如果首件结果NG，需要复位首件完成数量，继续首件
            String tagWriteValue = "0";
            if (result.equals("0")) {
                tagWriteValue = "2";
            } else if (result.equals("1")) {
                tagWriteValue = "1";
            }
            errorMsg = cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, "LoadAis/AisWebStatus/WebInspectConfirmStatus", tagWriteValue, true);

            code = 200;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //5.配套端口数下发--配套机
    public JSONObject PortQtyDownloadCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "PortQtyDownloadCommand";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 500;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            String station_code = jsonParas.getString("eqno");
            String portqty = jsonParas.getString("portqty");
            JSONArray portlist = jsonParas.getJSONArray("portlist");
            String aisMonitorModel = cFuncDbSqlResolve.GetParameterValue("AIS_MONITOR_MODE");
            String client_code = "";
            String client_code_ais = "";
            if (aisMonitorModel.equals("AIS-PC")) {
                client_code = "LoadPlc";
                client_code_ais = "LoadAis";
            } else if (aisMonitorModel.equals("AIS-SERVER")) {
                client_code = "LoadPlc_" + station_code;
                client_code_ais = "LoadAis_" + station_code;

            } else {
                errorMsg = "未配置收板机系统参数AIS_MONITOR_MODE";
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String TaskDirection = "";//任务输送方向(1:棕化;2:收板机)
            //mes下发端口锁定状态，奇数端口号锁定状态是1，偶数端口号锁定状态是2
            String tag_list = "";
            String tag_value_list = "";
            if (portlist != null && portlist.size() > 0) {
                //1.判断任务输送方向，以及下发的plc实例
                JSONObject jPortFirst = portlist.getJSONObject(0);
                int port_index_first = jPortFirst.getInteger("portid");
                if (port_index_first == 1) {
                    TaskDirection = "1";
                    tag_list = client_code_ais + "/AisRcsStatus/CCD1NextPortIndex";
                    tag_value_list = "0";
                } else if (port_index_first == 19) {
                    TaskDirection = "2";
                    tag_list = client_code_ais + "/AisRcsStatus/CCD2NextPortIndex";
                    tag_value_list = "0";
                }
                for (int i = 0; i < portlist.size(); i++) {
                    //2.判断plc实例
                    String client_code_port = client_code;
                    String value = "2";
                    JSONObject jPort = portlist.getJSONObject(i);
                    int port_index = jPort.getInteger("portid");
                    switch (port_index) {
                        case 1:
                        case 2:
                            client_code_port += "0";
                            break;
                        case 3:
                        case 4:
                            client_code_port += "1";
                            break;
                        case 5:
                        case 6:
                            client_code_port += "2";
                            break;
                        case 7:
                        case 8:
                            client_code_port += "3";
                            break;
                        case 9:
                        case 10:
                            client_code_port += "4";
                            break;
                        case 11:
                        case 12:
                            client_code_port += "5";
                            break;
                        case 13:
                        case 14:
                            client_code_port += "6";
                            break;
                        case 15:
                        case 16:
                            client_code_port += "7";
                            break;
                        case 17:
                        case 18:
                            client_code_port += "8";
                            break;
                        case 19:
                        case 20:
                            client_code_port += "9";
                            break;

                    }
                    String tag_key = client_code_port + "/PlcStatus/Port" + String.format("%02d", port_index) + "LockStatus";
                    tag_list += "," + client_code_port + "/PlcStatus/TaskDirection" + "," + tag_key;
                    tag_value_list += "&" + TaskDirection + "&" + value;
                }
                tag_list += "," + client_code_ais + "/AisRcsStatus/PortQtyReportStatus";
                tag_value_list += "&0";
                errorMsg = cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, tag_list, tag_value_list, true);
            }

            code = 200;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //6.VRS发送结果信息
    public JSONObject PanelQtychangeReport(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "PanelQtychangeReport";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String apsPlanTable = "a_eap_aps_plan";
        Long station_id = 0L;
        Integer code = 500;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            String station_code = jsonParas.getString("eqno");
            String lot_num = jsonParas.getString("lotno");
            String pnlid = jsonParas.getString("pnlid");
            String switchType = jsonParas.getString("switch");//"--in(首检)", "out（离线检修）"
            String port_code = jsonParas.getString("portid") == null ? "2" : jsonParas.getString("portid");
            //判断工位是否正确
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + station_code + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发载具类型中工位号{" + station_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "PanelQtychangeReport", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            MongoCursor<Map> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject(), Map.class).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Map<String, Object> mapLotInfo = iteratorBigData.next();
                Integer target_lot_count = Integer.parseInt(mapLotInfo.get("target_lot_count").toString());
                if (switchType.equals("in")) target_lot_count += 1;
                else if (switchType.equals("out") && target_lot_count > 0) target_lot_count -= 1;
                Update updateBigData = new Update();
                updateBigData.set("target_lot_count", target_lot_count);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                iteratorBigData.close();
            } else {
                errorMsg = "未查到符合条件的任务信息";
                opCommonFunc.SaveCimMessage(station_id, "0", "PanelQtychangeReport", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            code = 200;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //7.AOI发送结果信息
    public JSONObject PanelAOIRepairReport(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "PanelAOIRepairReport";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String mePanelQueueTable = "a_eap_me_panel_queue";
        Long station_id = 0L;
        Integer code = 500;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            String eqno = jsonParas.getString("eqno");
            String prodno = jsonParas.getString("prodno");
            String prodrev = jsonParas.getString("prodrev");
            String lotno = jsonParas.getString("lotno");
            String pnlid = jsonParas.getString("pnlid");
            String pnlgrade = jsonParas.getString("pnlgrade");
            //判断工位是否正确
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + eqno + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发载具类型中工位号{" + eqno + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "PanelQtychangeReport", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("lot_num").is(lotno));
            queryBigData.addCriteria(Criteria.where("panel_barcode").is(pnlid));
            long panelCount = mongoTemplate.getCollection(mePanelQueueTable).countDocuments(queryBigData.getQueryObject());
            if (panelCount > 0) {
                Update updateBigData = new Update();
                updateBigData.set("panel_status", pnlgrade);
                mongoTemplate.updateMulti(queryBigData, updateBigData, mePanelQueueTable);
            } else {
                Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String panel_queue_id = CFuncUtilsSystem.CreateUUID(true);
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("panel_queue_id", panel_queue_id);
                mapBigDataRow.put("station_id", station_id);
                mapBigDataRow.put("lot_num", lotno);
                mapBigDataRow.put("panel_barcode", pnlid);
                mapBigDataRow.put("panel_status", pnlgrade);
                mapBigDataRow.put("use_flag", "N");
                mongoTemplate.insert(mapBigDataRow, mePanelQueueTable);
            }
            code = 200;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //7.MES下发陪渡板信息
    public JSONObject DragCylinderReport(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "DragCylinderReport";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        String meDragRecordTable = "a_eap_me_drag_record";
        Long station_id = 0L;
        Integer code = 500;
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            String eqno = jsonParas.getString("eqno");
            String updateOrDelete = jsonParas.getString("updateOrDelete");//--增加/删除/全部删除update/delete/allDelete
            String pnlistStr = jsonParas.getString("pnlist");
            JSONArray pnlist = new JSONArray();
            if (pnlistStr != null && !pnlistStr.equals("")) {
                pnlist = jsonParas.getJSONArray("pnlist");
            }
            //判断工位是否正确
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + eqno + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发载具类型中工位号{" + eqno + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "PanelQtychangeReport", "AIS", errorMsg, 5);
                responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            if (updateOrDelete.equals("update")) {
                //增加
                if (pnlist != null && pnlist.size() > 0) {
                    List<Map<String, Object>> lstDocuments = new ArrayList<>();
                    for (int i = 0; i < pnlist.size(); i++) {
                        String pnlid = pnlist.getString(i);
                        Query queryBigData = new Query();
                        queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                        queryBigData.addCriteria(Criteria.where("panel_barcode").is(pnlid));
                        long flowCount = mongoTemplate.getCollection(meDragRecordTable).countDocuments(queryBigData.getQueryObject());
                        if (flowCount > 0)
                            continue;
                        Date item_date = CFuncUtilsSystem.GetMongoISODate("");
                        long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
                        Map<String, Object> mapDataItem = new HashMap<>();
                        String panel_queue_id = CFuncUtilsSystem.CreateUUID(true);
                        mapDataItem.put("item_date", item_date);
                        mapDataItem.put("item_date_val", item_date_val);
                        mapDataItem.put("panel_queue_id", panel_queue_id);
                        mapDataItem.put("station_id", station_id);
                        mapDataItem.put("panel_barcode", pnlid);
                        lstDocuments.add(mapDataItem);
                    }
                    mongoTemplate.insert(lstDocuments, meDragRecordTable);
                }

            } else if (updateOrDelete.equals("delete")) {
                //删除
                String[] pnlistArray = new String[pnlist.size()];
                for (int i = 0; i < pnlist.size(); i++) {
                    pnlistArray[i] = pnlist.getString(i);
                }
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("panel_barcode").in(pnlistArray));
                mongoTemplate.remove(queryBigData, meDragRecordTable);
            } else if (updateOrDelete.equals("allDelete")) {
                //删除全部
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                mongoTemplate.remove(queryBigData, meDragRecordTable);
            }
            code = 200;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            responseParas = eapTlGhInterfCommon.CreateResponseHead01(code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

}
