package com.api.dcs.project.whzsj.interf;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 到货清单功能函数
 * 1.新增到货清单
 * 2.判断是否存在并新增到货清单
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-04-21
 */
@Service
@Slf4j
public class DcsWhzsjMomRecvPlanListFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.新增到货清单
    public JSONObject receiveMomPlanList(JSONObject jsonParas) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        Integer code = 200;
        String userID="-1";
        String response_time = CFuncUtilsSystem.GetNowDateTime("");
        String request_uuid = "";
        try {
            //1.解析 JSON
            String list_num = "";//到货单号
            String plan_from = jsonParas.getString("reqSystem");//请求系统编号：默认MOM
            request_uuid = jsonParas.getString("reqNo");//请求编号
            JSONArray dataList = jsonParas.getJSONArray("data");
            if(dataList!=null && dataList.size()>0){
                list_num=dataList.getJSONObject(0).getString("doNumber");
                //2.判断 到货清单是否存在
                Long plan_list_id=PlanListExistsIns(userID,list_num,plan_from,response_time);
                String project_code = "";//项目号
                String block_code = "";//分段号
                String lot_num = "";//批次号
                String serial_num = "";//唯一码
                String steel_seal_code = "";//钢印号
                String material_code = "";//钢材编码
                String material_des = "";//钢材名称
                String m_texture = "";//材质
                String ship_class = "";//船级社
                String m_length = "";//长
                String m_width = "";//宽
                String m_height = "";//高
                String model_type = "";//规格型号
                String m_weight = "";//重量
                //新增SQL
                String insPlanListDSql = "insert into b_dcs_wms_plan_list_d(created_by, creation_date, plan_list_d_id," +
                        "plan_list_id, project_code, block_code, lot_num, " +
                        "serial_num, steel_seal_code, " +
                        "material_code, material_des, m_texture, " +
                        "ship_class, m_length, m_width," +
                        "m_height, model_type, m_weight, lock_flag) values ";
                for (int i = 0; i < dataList.size(); i++) {
                    JSONObject jsonObject = dataList.getJSONObject(i);
                    list_num = jsonObject.getString("doNumber");//到货单号
                    project_code = jsonObject.getString("projectCode");//项目号
                    block_code = jsonObject.getString("blockCode");//分段号
                    lot_num = jsonObject.getString("batchNumber");//批次号
                    serial_num = jsonObject.getString("uniqNo");//序列号(钢板唯一识别码)
                    steel_seal_code = jsonObject.getString("steelSealCode");//钢印号
                    material_code = jsonObject.getString("materialsCode");//钢材编码
                    material_des = jsonObject.getString("materialsName");//钢材名称
                    m_texture = jsonObject.getString("material");//材质
                    ship_class = jsonObject.getString("shipClass");//船级社
                    m_length = jsonObject.getString("length");//长
                    m_width = jsonObject.getString("width");//宽
                    m_height = jsonObject.getString("height");//高
                    model_type = jsonObject.getString("specifications");//规格型号
                    m_weight = jsonObject.getString("weight");//重量
                    if (lot_num == null ||"".equals(lot_num)) lot_num = "";
                    if (m_length == null ||"".equals(m_length)) m_length = "0";
                    if (m_width == null ||"".equals(m_width)) m_width = "0";
                    if (m_height == null ||"".equals(m_height)) m_height = "0";
                    if (m_weight == null ||"".equals(m_weight)) m_weight = "0";

                    //3.判断 钢板唯一识别码 是否已存在
                    Long plan_list_d_id=0l;
                    String sqlPlanListDSel="select plan_list_d_id " +
                            "from b_dcs_wms_plan_list_d " +
                            "where serial_num='"+serial_num+"' ";
                    List<Map<String, Object>> itemPlanListD=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlPlanListDSel, false,null,"");
                    if(itemPlanListD==null || itemPlanListD.size()<=0){
                        //plan_list_d_id=Long.parseLong(itemPlanListD.get(0).get("plan_list_d_id").toString());
                        //4.不存在 新增
                        plan_list_d_id = cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_plan_list_d_id_seq", false);
                        String value = "('MOM','" + response_time + "'," + plan_list_d_id + "," +
                                plan_list_id + ",'" + project_code + "','" + block_code + "','" + lot_num + "','" +
                                serial_num + "','" + steel_seal_code + "','" +
                                material_code + "','" + material_des + "','" + m_texture + "','" +
                                ship_class + "'," + m_length + "," + m_width + "," +
                                m_height + ",'" + model_type + "'," + m_weight +",'N')";
                        cFuncDbSqlExecute.ExecUpdateSql("AIS", insPlanListDSql + value, false, null, "");
                    }
                }
            }
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("task_num", list_num);
            jbResult.put("code", code);
            jbResult.put("msg", "成功");
        } catch (Exception ex) {
            code = 500;
            errorMsg = "接口发生未知异常:" + ex.getMessage();
            jbResult.put("response_uuid", request_uuid);
            jbResult.put("response_time", response_time);
            jbResult.put("task_num", "");
            jbResult.put("code", code);
            jbResult.put("msg", errorMsg);
        }
        return jbResult;
    }

    //2.判断是否存在并新增到货清单
    public Long PlanListExistsIns(String userID, String list_num, String plan_from, String response_time) throws Exception{
        //1.检索是否存在
        Long plan_list_id=0l;
        String sqlPlanListSel="select plan_list_id " +
                "from b_dcs_wms_plan_list " +
                "where list_num='"+list_num+"' ";
        List<Map<String, Object>> itemPlanList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlPlanListSel,
                false,null,"");
        if(itemPlanList!=null && itemPlanList.size()>0){
            plan_list_id=Long.parseLong(itemPlanList.get(0).get("plan_list_id").toString());
        }
        else {
            //2.不存在，新增
            plan_list_id=cFuncDbSqlResolve.GetIncreaseID("b_dcs_wms_plan_list_id_seq",true);
            String sqlPlanListIns="insert into b_dcs_wms_plan_list " +
                    "(created_by,creation_date,plan_list_id," +
                    "list_num,plan_from,plan_status,enable_flag) values" +
                    "('"+userID+"','"+response_time+"',"+plan_list_id+",'"+
                    list_num+"','"+plan_from+"','PLAN','Y')";
            cFuncDbSqlExecute.ExecUpdateSql("AIS",sqlPlanListIns,
                    false,null,"");
        }
        return plan_list_id;
    }

}
