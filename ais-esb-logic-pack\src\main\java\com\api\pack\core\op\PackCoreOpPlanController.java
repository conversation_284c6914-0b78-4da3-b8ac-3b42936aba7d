package com.api.pack.core.op;

import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.pack.core.plan.PlanService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 任务计划
 * 1.计划任务状态重置
 * </p>
 *
 * <AUTHOR>
 * @since 2024-01-03
 */
@RestController
@RequestMapping("/pack/core/op")
@AllArgsConstructor
@Slf4j
public class PackCoreOpPlanController
{
    private final PlanService planService;

    @RequestMapping(value = "/PlanTaskStatusReset", produces = "application/json;charset=utf-8", method = {RequestMethod.POST, RequestMethod.GET})
    public String PlanTaskStatusReset(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception
    {
        String apiRoutePath = "pack/core/op/PlanTaskStatusReset";
        String tranResult;
        try
        {
            planService.resetOrderStatus();
            tranResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "修改成功", apiRoutePath, 1);
        }
        catch (Exception ex)
        {
            ex.printStackTrace();
            tranResult = CFuncUtilsLayUiResut.GetStandJson(false, null, null, apiRoutePath + "/error", 0);
        }
        return tranResult;
    }
}
