package com.api.eap.project.thailand.dycj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 工单信息查询
 * 1.工单查询
 * 2.工单生产过程工艺参数查询
 * <AUTHOR>
 * @date 2023-08-2 16:49
 */
@RestController
@RequestMapping("/eap/project/thailand/dycj/plan")
public class EapDyCjPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private EapDyCjSendFlowFunc eapDyCjSendFlowFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;

    //Panel校验(入和出)
    @RequestMapping(value = "/EapDyCjPlanPanelCheckAndSave", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/plan/EapDyCjPlanPanelCheckAndSave";
        String transResult="";
        String errorMsg="";
        String meUserTable="a_eap_me_station_user";
        String apsPlanTable="a_eap_aps_plan";
        String apsPlanDTable="a_eap_aps_plan_d";
        String meStationFlowTable="a_eap_me_station_flow";
        String result="";
        try{
            String station_attr=jsonParas.getString("station_attr");
            String group_lot_num="";
            String panel_barcode=jsonParas.getString("panel_barcode");
            String tray_barcode=jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value=jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value=jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value=jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value=jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批
            String onecar_multybatch_check_value=jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code=jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String inspect_flag=jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag=jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag=jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code=jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String in_out_type=jsonParas.getString("in_out_type");//IN代表进入,OUT代表出
            String prod_mode = jsonParas.getString("prod_mode");
            String offline_flag= jsonParas.getString("offline_flag");
            String local_flag= jsonParas.getString("local_flag");

            //防止null数据
            String panel_flag="N";
            Integer panel_ng_code=0;
            String panel_ng_msg="";
            String panel_status="OK";
            String user_name="";
            String dept_id="";
            String shift_id="";
            if(panel_barcode==null) panel_barcode="";
            if(panel_barcode.equals("FFFFFFFF")) panel_barcode="NoRead";
            if(tray_barcode==null) tray_barcode="";
            if(ng_auto_pass_value==null) ng_auto_pass_value="";
            if(ng_manual_pass_value==null) ng_manual_pass_value="";
            if(panel_model_value==null) panel_model_value="";
            if(panel_model_value.equals("1")) panel_flag="Y";
            if(onecar_multybatch_value==null) onecar_multybatch_value="";
            if(onecar_multybatch_check_value==null) onecar_multybatch_check_value="";
            if(manual_judge_code==null || manual_judge_code.equals("")) manual_judge_code="0";
            if(inspect_flag==null || inspect_flag.equals("")) inspect_flag="N";
            if(dummy_flag==null || dummy_flag.equals("")) dummy_flag="N";
            if(eap_flag==null || eap_flag.equals("")) eap_flag="N";
            if(eap_ng_code==null || eap_ng_code.equals("")) eap_ng_code="0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (prod_mode == null || prod_mode.equals("")) prod_mode = "0";
            if(offline_flag==null || offline_flag.equals("")) offline_flag="N";
            if(local_flag==null || local_flag.equals("")) local_flag="N";

            //查询工位信息
            String sqlStation = "select station_id,station_code," +
                    "COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_attr='" + station_attr + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            String station_code=itemListStation.get(0).get("station_code").toString();
            String station_id = itemListStation.get(0).get("station_id").toString();
            long station_id_long=Long.parseLong(station_id);

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                user_name=docItemBigData.getString("user_name");
                dept_id = docItemBigData.getString("dept_id");
                shift_id = docItemBigData.getString("shift_id");
                iteratorBigData.close();
            }

            //获取当前任务
            String[] group_lot_status=new String[]{"PLAN","WORK"};
            List<String> lstPlanId=new ArrayList<>();
            //2.获取当前计划中或者执行中的子任务
            String plan_id="";
            String task_from="";
            String lot_num="";
            String lot_short_num="";
            Integer lot_index=1;
            String port_code="";
            String material_code="";
            String pallet_num="";
            String pallet_type="";
            String lot_level="";
            Integer fp_index=0;
            Double panel_length=0d;
            Double panel_width=0d;
            Double panel_tickness=0d;
            Integer plan_lot_count=0;
            Integer target_lot_count=0;
            Integer finish_count=0;
            Integer finish_ok_count=0;
            Integer finish_ng_count=0;
            Integer face_code=0;
            Integer pallet_use_count=0;
            Integer inspect_finish_count=0;
            Integer panel_index=0;
            Integer target_update_count=0;
            //WIP参数
            String task_start_time="";
            String lot_version="";
            String attribute1="";
            String attribute2="";
            JSONArray item_attr_list=new JSONArray();
            JSONObject attr_else=new JSONObject();
            String item_info="";

            //板进
            if(in_out_type.equals("IN")){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
                queryBigData.addCriteria(
                        new Criteria().andOperator(
                                Criteria.where("$expr").gt(Arrays.asList("$target_lot_count", "$target_update_count"))
                        )
                );
                queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if(iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    String group_lot_status2=docItemBigData.getString("group_lot_status");
                    group_lot_num=docItemBigData.getString("group_lot_num");
                    lstPlanId.add(docItemBigData.getString("plan_id"));
                    //判断是否为PLAN状态,若为PLAN状态则进行UPDATE
                    if(group_lot_status2.equals("PLAN")){
                        Query queryBigData2 = new Query();
                        queryBigData2.addCriteria(Criteria.where("station_id").is(station_id_long));
                        queryBigData2.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                        Update updateBigData2 = new Update();
                        updateBigData2.set("group_lot_status", "WORK");
                        mongoTemplate.updateMulti(queryBigData2, updateBigData2, apsPlanTable);
                    }
                    //获取参数
                    plan_id=docItemBigData.getString("plan_id");
                    task_from=docItemBigData.getString("task_from");
                    lot_num=docItemBigData.getString("lot_num");
                    lot_short_num=docItemBigData.getString("lot_short_num");
                    lot_index=docItemBigData.getInteger("lot_index");
                    port_code=docItemBigData.getString("port_code");
                    material_code=docItemBigData.getString("material_code");
                    pallet_num=docItemBigData.getString("pallet_num");
                    pallet_type=docItemBigData.getString("pallet_type");
                    lot_level=docItemBigData.getString("lot_level");
                    panel_length=docItemBigData.getDouble("panel_length");
                    panel_width=docItemBigData.getDouble("panel_width");
                    panel_tickness=docItemBigData.getDouble("panel_tickness");
                    plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                    target_lot_count=docItemBigData.getInteger("target_lot_count");
                    finish_count=docItemBigData.getInteger("finish_count");
                    finish_ok_count=docItemBigData.getInteger("finish_ok_count");
                    finish_ng_count=docItemBigData.getInteger("finish_ng_count");
                    face_code=docItemBigData.getInteger("face_code");
                    pallet_use_count=docItemBigData.getInteger("pallet_use_count");
                    inspect_finish_count=docItemBigData.getInteger("inspect_finish_count");
                    target_update_count=docItemBigData.getInteger("target_update_count");
                }
            }
            else{
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(
                        new Criteria().andOperator(
                                Criteria.where("$expr").gt(Arrays.asList("$target_lot_count", "$finish_count"))
                        )
                );
                queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    group_lot_num=docItemBigData.getString("group_lot_num");
                    lstPlanId.add(docItemBigData.getString("plan_id"));
                    //获取参数
                    plan_id=docItemBigData.getString("plan_id");
                    task_from=docItemBigData.getString("task_from");
                    lot_num=docItemBigData.getString("lot_num");
                    lot_short_num=docItemBigData.getString("lot_short_num");
                    lot_index=docItemBigData.getInteger("lot_index");
                    port_code=docItemBigData.getString("port_code");
                    material_code=docItemBigData.getString("material_code");
                    pallet_num=docItemBigData.getString("pallet_num");
                    pallet_type=docItemBigData.getString("pallet_type");
                    lot_level=docItemBigData.getString("lot_level");
                    panel_length=docItemBigData.getDouble("panel_length");
                    panel_width=docItemBigData.getDouble("panel_width");
                    panel_tickness=docItemBigData.getDouble("panel_tickness");
                    plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                    target_lot_count=docItemBigData.getInteger("target_lot_count");
                    finish_count=docItemBigData.getInteger("finish_count");
                    finish_ok_count=docItemBigData.getInteger("finish_ok_count");
                    finish_ng_count=docItemBigData.getInteger("finish_ng_count");
                    face_code=docItemBigData.getInteger("face_code");
                    pallet_use_count=docItemBigData.getInteger("pallet_use_count");
                    inspect_finish_count=docItemBigData.getInteger("inspect_finish_count");
                    target_update_count=docItemBigData.getInteger("target_update_count");
                    //WIP参数
                    task_start_time=docItemBigData.getString("task_start_time");
                    lot_version = docItemBigData.getString("lot_level");
                    item_info=docItemBigData.getString("item_info");
                    attribute1 = docItemBigData.getString("attribute1");
                    attribute2 = docItemBigData.getString("attribute2");
                    if (docItemBigData.containsKey("attribute3")) {
                        String attribute_else = docItemBigData.getString("attribute3");
                        if (attribute_else != null && !attribute_else.equals("")) {
                            attr_else = JSONObject.parseObject(attribute_else);
                        }
                    }
                }
            }

            //若无工单,则返回空值
            if(plan_id.equals("")){
                //写入点位
                JSONObject jbSend=new JSONObject();
                jbSend.put("lot_num","");
                jbSend.put("finish_plan","0/0");
                jbSend.put("panel_barcode",panel_barcode);
                String tagCode="InPanelInfo";
                if(in_out_type.equals("OUT")) tagCode="OutPanelInfo";
                opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Ais","AisStatus",tagCode,"AIS",jbSend.toString(),false);
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"","",0);
                return transResult;
            }
            //查询条件
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            String station_flow_id= CFuncUtilsSystem.CreateUUID(true);
            String slot_no="";
            //板件出
            if(!in_out_type.equals("IN")){
                finish_count=finish_count+1;
                finish_ok_count=finish_ok_count+1;
                panel_index=finish_count;
                slot_no=String.format("%03d", panel_index);
                String lot_status="WORK";
                if(finish_count>=target_lot_count) lot_status="FINISH";
                Update updateBigData = new Update();
                if(lot_status.equals("FINISH")){
                    updateBigData.set("lot_status", lot_status);
                }
                updateBigData.set("finish_count", finish_count);
                updateBigData.set("finish_ok_count", finish_ok_count);
                String task_end_time=CFuncUtilsSystem.GetNowDateTime("");
                if(lot_status.equals("FINISH")){
                    Integer task_error_code=1;
                    if(target_lot_count<plan_lot_count) task_error_code=2;
                    updateBigData.set("task_error_code", task_error_code);
                    updateBigData.set("task_end_time", task_end_time);
                }
                mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

                //发送板进--客户指出暂时不需要
                //eapDyCjSendFlowFunc.SendOutReport(station_code,panel_barcode,lot_num,slot_no);

                //判断group_lot_count是否全部完成,若全部完成则直接finish掉
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                Integer sum_target_lot_count=0;
                Integer sum_plan_lot_count=0;
                Integer sum_finish_count=0;
                JSONArray lot_list=new JSONArray();
                while (iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    sum_plan_lot_count+=docItemBigData.getInteger("plan_lot_count");
                    sum_target_lot_count+=docItemBigData.getInteger("target_lot_count");
                    sum_finish_count+=docItemBigData.getInteger("finish_count");
                    Integer left_count=docItemBigData.getInteger("plan_lot_count")-docItemBigData.getInteger("finish_count");
                    if(left_count<0) left_count=0;
                    JSONObject jbItem=new JSONObject();
                    jbItem.put("lot_id",docItemBigData.getString("lot_num"));
                    jbItem.put("lot_count",left_count);
                    lot_list.add(jbItem);
                }
                if(iteratorBigData.hasNext()) iteratorBigData.close();
                if(sum_finish_count>=sum_target_lot_count){
                    updateBigData = new Update();
                    updateBigData.set("group_lot_status", "FINISH");
                    mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
                }
                //上传WIP数据
                if(lot_status.equals("FINISH")){
                    String end_lot_type="3";
                    if(plan_lot_count==finish_count) end_lot_type="1";
                    //类型
                    JSONObject jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T001");
                    jbAttr.put("item_value", end_lot_type);
                    item_attr_list.add(jbAttr);
                    //撈邊尺寸(徑)
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T002");
                    jbAttr.put("item_value", String.format("%.3f", panel_length));
                    item_attr_list.add(jbAttr);
                    //撈邊尺寸(緯)
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T003");
                    jbAttr.put("item_value", String.format("%.3f", panel_width));
                    item_attr_list.add(jbAttr);
                    //壓合板厚（mm）
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T004");
                    jbAttr.put("item_value", String.format("%.3f", panel_tickness));
                    item_attr_list.add(jbAttr);
                    //S004
                    String S004="";
                    if (item_info != null && !item_info.equals("")){
                        JSONArray item_info_list = JSONArray.parseArray(item_info);
                        if (item_info_list != null && item_info_list.size() > 0){
                            for (int i = 0; i < item_info_list.size(); i++){
                                JSONObject jbItem = item_info_list.getJSONObject(i);
                                String item_id = jbItem.getString("item_id");
                                String item_value = jbItem.getString("item_value");
                                if(item_id.equals("S004")) S004=item_value;
                            }
                        }
                    }
                    Double sm_set_speed=0D;
                    try{
                        sm_set_speed=Double.parseDouble(S004);
                    }
                    catch (Exception exD){}
                    //撕膜速度（m/min
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T005");
                    jbAttr.put("item_value", String.format("%.3f", sm_set_speed));
                    item_attr_list.add(jbAttr);
                    //撕膜OK數
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T006");
                    jbAttr.put("item_value", String.valueOf(finish_ok_count));
                    item_attr_list.add(jbAttr);
                    //撕膜NG數
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T007");
                    jbAttr.put("item_value", String.valueOf(plan_lot_count-finish_ok_count));
                    item_attr_list.add(jbAttr);
                    //實際撕膜速度（m/min）
                    Double sm_actual_speed=0D;
                    try{
                        String T008= opCommonFunc.ReadCellOneRedisValue(station_code,station_attr,
                                "Plc","PlcStatus","Speed");
                        sm_actual_speed=Double.parseDouble(T008);
                    }
                    catch (Exception exC){}
                    //实际撕膜速度（m/min)
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T008");
                    jbAttr.put("item_value", String.format("%.3f", sm_actual_speed));
                    item_attr_list.add(jbAttr);
                    //用电量
                    jbAttr=new JSONObject();
                    jbAttr.put("item_id", "T009");
                    jbAttr.put("item_value", String.format("%.3f", 0D));
                    item_attr_list.add(jbAttr);
                    eapDyCjSendFlowFunc.WIPTrackingReport(station_id_long, station_code, user_name, dept_id, shift_id,
                            task_start_time, task_end_time, lot_num, sum_plan_lot_count, plan_lot_count, finish_ok_count,
                            material_code, lot_short_num, lot_version, prod_mode, attribute1, attribute2,
                            item_attr_list, offline_flag, local_flag, attr_else);
                }
                //若整体FINISH,则进行CarrierStatusReport
                if(sum_finish_count>=sum_target_lot_count){
                    String carrierStatus="ProcessEnd";
                    if(sum_finish_count<sum_plan_lot_count) carrierStatus="Abort";
                    eapDyCjSendFlowFunc.CarrierStatusReport(station_code, pallet_num, carrierStatus, lot_list, "0",
                            station_attr, "01","N");
                }

                //写入点位
                JSONObject jbSend=new JSONObject();
                jbSend.put("lot_num",lot_num);
                jbSend.put("finish_plan",""+finish_count+"/"+plan_lot_count+"");
                jbSend.put("panel_barcode",panel_barcode);
                String tagCode="InPanelInfo";
                if(in_out_type.equals("OUT")) tagCode="OutPanelInfo";
                opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Ais","AisStatus",tagCode,"AIS",jbSend.toString(),false);

                //工单结束,变更批次任务信息
                if(lot_status.equals("FINISH")){
                    String writeTags="MasterEap/EapStatus/CurrentLotNum,MasterEap/EapStatus/CurrentLotCount";
                    String tagValues="&0";
                    cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, writeTags, tagValues, false);
                }

                result=station_flow_id+","+lot_num+","+pallet_num+","+ finish_count+","+panel_barcode+","+
                        panel_ng_code+","+inspect_finish_count+","+tray_barcode+","+plan_lot_count+","+lot_status;//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
                return transResult;
            }

            //板件进
            panel_index=target_update_count+1;
            slot_no=String.format("%03d", panel_index);
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val=CFuncUtilsSystem.GetMongoDataValue(item_date);
            Map<String, Object> mapBigDataRow=new HashMap<>();
            mapBigDataRow.put("item_date",item_date);
            mapBigDataRow.put("item_date_val",item_date_val);
            mapBigDataRow.put("station_flow_id",station_flow_id);
            mapBigDataRow.put("station_id",station_id_long);
            mapBigDataRow.put("plan_id",plan_id);
            mapBigDataRow.put("task_from",task_from);
            mapBigDataRow.put("group_lot_num",group_lot_num);
            mapBigDataRow.put("lot_num",lot_num);
            mapBigDataRow.put("lot_short_num",lot_short_num);
            mapBigDataRow.put("lot_index",lot_index);
            mapBigDataRow.put("port_code",port_code);
            mapBigDataRow.put("material_code",material_code);
            mapBigDataRow.put("pallet_num",pallet_num);
            mapBigDataRow.put("pallet_type",pallet_type);
            mapBigDataRow.put("lot_level",lot_level);
            mapBigDataRow.put("fp_index",fp_index);
            mapBigDataRow.put("panel_barcode",panel_barcode);
            mapBigDataRow.put("panel_length",panel_length);
            mapBigDataRow.put("panel_width",panel_width);
            mapBigDataRow.put("panel_tickness",panel_tickness);
            mapBigDataRow.put("panel_index",panel_index);
            mapBigDataRow.put("panel_status",panel_status);
            mapBigDataRow.put("panel_ng_code",panel_ng_code);
            mapBigDataRow.put("panel_ng_msg",panel_ng_msg);
            mapBigDataRow.put("inspect_flag",inspect_flag);
            mapBigDataRow.put("dummy_flag",dummy_flag);
            mapBigDataRow.put("manual_judge_code",manual_judge_code);
            mapBigDataRow.put("panel_flag",panel_flag);
            mapBigDataRow.put("user_name",user_name);
            mapBigDataRow.put("eap_flag",eap_flag);
            mapBigDataRow.put("tray_barcode",tray_barcode);
            mapBigDataRow.put("face_code",face_code);
            mapBigDataRow.put("offline_flag","N");
            mapBigDataRow.put("virtu_pallet_num",pallet_num);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow,meStationFlowTable);
            //更新完工数量
            target_update_count++;
            String lot_status="WORK";
            Update updateBigData = new Update();
            if(target_update_count==1){
                updateBigData.set("lot_status", lot_status);
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            updateBigData.set("target_update_count", target_update_count);
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);

            //发送板进--客户指出暂时不需要
            //eapDyCjSendFlowFunc.ReceiveReport(station_code,panel_barcode,lot_num,slot_no);

            //若是第一片,则给出process
            if(target_update_count==1){
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
                Integer sum_target_update_count=0;
                JSONArray lot_list=new JSONArray();
                while (iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    sum_target_update_count+=docItemBigData.getInteger("target_update_count");
                    Integer left_count=docItemBigData.getInteger("plan_lot_count")-docItemBigData.getInteger("target_update_count");
                    if(left_count<0) left_count=0;
                    JSONObject jbItem=new JSONObject();
                    jbItem.put("lot_id",docItemBigData.getString("lot_num"));
                    jbItem.put("lot_count",left_count);
                    lot_list.add(jbItem);
                }
                if(iteratorBigData.hasNext()) iteratorBigData.close();
                if(sum_target_update_count==1){
                    eapDyCjSendFlowFunc.CarrierStatusReport(station_code, pallet_num, "Process", lot_list, "0",
                            station_attr, "01","N");
                }

                //写入点位
                String writeTags="MasterEap/EapStatus/CurrentLotNum,MasterEap/EapStatus/CurrentLotCount";
                String tagValues=lot_num+"&"+plan_lot_count;
                cFuncUtilsCellScada.WriteTagByStation("AIS", station_code, writeTags, tagValues, false);
            }

            //写入点位
            JSONObject jbSend=new JSONObject();
            jbSend.put("lot_num",lot_num);
            jbSend.put("finish_plan",""+target_update_count+"/"+plan_lot_count+"");
            jbSend.put("panel_barcode",panel_barcode);
            String tagCode="InPanelInfo";
            if(in_out_type.equals("OUT")) tagCode="OutPanelInfo";
            opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                    "Ais","AisStatus",tagCode,"AIS",jbSend.toString(),false);

            //返回数据
            result=station_flow_id+","+lot_num+","+pallet_num+","+panel_index+","+panel_barcode+","+
                    panel_ng_code+","+inspect_finish_count+","+tray_barcode+","+plan_lot_count+","+lot_status;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
        }
        catch (Exception ex){
            errorMsg= "Exception:"+ex;
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //强制结批
    @RequestMapping(value = "/EapDyCjPlanAbort", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjPlanAbort(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/dycj/plan/EapDyCjPlanAbort";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String result = "";
        try{
            String station_attr=jsonParas.getString("station_attr");
            String group_lot_num=jsonParas.getString("group_lot_num");
            String offline_flag="N";
            String local_flag="N";
            String user_name="";
            String dept_id="";
            String shift_id="";

            //1.查询工位信息
            String sqlStation = "select station_id,station_code," +
                    "COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_attr='" + station_attr + "'";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation, false, null, "");
            String station_code=itemListStation.get(0).get("station_code").toString();
            String station_id = itemListStation.get(0).get("station_id").toString();
            long station_id_long=Long.parseLong(station_id);

            //2.查询点位信息
            String SysModel=opCommonFunc.ReadCellOneRedisValue(station_code,station_attr,
                    "Plc","PlcContrl","SysModel");
            String prod_mode=opCommonFunc.ReadCellOneRedisValue(station_code,station_attr,
                    "Ais","AisConfig","ProdMode");
            if("3".equals(SysModel)){
                offline_flag="N";
                local_flag="N";
            }
            else if("2".equals(SysModel)){
                offline_flag="N";
                local_flag="Y";
            }
            else{
                offline_flag="Y";
                local_flag="Y";
            }

            //3.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                user_name=docItemBigData.getString("user_name");
                dept_id = docItemBigData.getString("dept_id");
                shift_id = docItemBigData.getString("shift_id");
                iteratorBigData.close();
            }

            //4.查询任务全部信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            Integer sum_plan_lot_count=0;
            Integer sum_finish_count=0;
            String pallet_num="";
            JSONArray lot_list=new JSONArray();
            while (iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                pallet_num=docItemBigData.getString("pallet_num");
                sum_plan_lot_count+=docItemBigData.getInteger("plan_lot_count");
                sum_finish_count+=docItemBigData.getInteger("finish_count");
                Integer left_count=docItemBigData.getInteger("plan_lot_count")-docItemBigData.getInteger("finish_count");
                if(left_count<0) left_count=0;
                JSONObject jbItem=new JSONObject();
                jbItem.put("lot_id",docItemBigData.getString("lot_num"));
                jbItem.put("lot_count",left_count);
                lot_list.add(jbItem);
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();

            //5.查询未做WIP上报的部分
            String[] group_lot_status=new String[]{"PLAN","WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("lot_status").in(group_lot_status));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                JSONObject attr_else=new JSONObject();
                JSONArray item_attr_list=new JSONArray();
                String lot_num=docItemBigData.getString("lot_num");
                String lot_short_num=docItemBigData.getString("lot_short_num");
                String material_code=docItemBigData.getString("material_code");
                Double panel_length=docItemBigData.getDouble("panel_length");
                Double panel_width=docItemBigData.getDouble("panel_width");
                Double panel_tickness=docItemBigData.getDouble("panel_tickness");
                Integer plan_lot_count=docItemBigData.getInteger("plan_lot_count");
                Integer target_lot_count=docItemBigData.getInteger("target_lot_count");
                Integer finish_count=docItemBigData.getInteger("finish_count");
                Integer finish_ok_count=docItemBigData.getInteger("finish_ok_count");
                //WIP参数
                String task_start_time=docItemBigData.getString("task_start_time");
                if(task_start_time==null || task_start_time.equals("")) task_start_time=CFuncUtilsSystem.GetNowDateTime("");
                String task_end_time=CFuncUtilsSystem.GetNowDateTime("");
                String lot_version = docItemBigData.getString("lot_level");
                String item_info=docItemBigData.getString("item_info");
                String attribute1 = docItemBigData.getString("attribute1");
                String attribute2 = docItemBigData.getString("attribute2");
                if (docItemBigData.containsKey("attribute3")) {
                    String attribute_else = docItemBigData.getString("attribute3");
                    if (attribute_else != null && !attribute_else.equals("")) {
                        attr_else = JSONObject.parseObject(attribute_else);
                    }
                }
                String end_lot_type="5";
                if(plan_lot_count==finish_count) end_lot_type="1";
                if(finish_count<=0) end_lot_type="4";
                //类型
                JSONObject jbAttr=new JSONObject();
                jbAttr.put("item_id", "T001");
                jbAttr.put("item_value", end_lot_type);
                item_attr_list.add(jbAttr);
                //撈邊尺寸(徑)
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T002");
                jbAttr.put("item_value", String.format("%.3f", panel_length));
                item_attr_list.add(jbAttr);
                //撈邊尺寸(緯)
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T003");
                jbAttr.put("item_value", String.format("%.3f", panel_width));
                item_attr_list.add(jbAttr);
                //壓合板厚（mm）
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T004");
                jbAttr.put("item_value", String.format("%.3f", panel_tickness));
                item_attr_list.add(jbAttr);
                //S004
                String S004="";
                if (item_info != null && !item_info.equals("")){
                    JSONArray item_info_list = JSONArray.parseArray(item_info);
                    if (item_info_list != null && item_info_list.size() > 0){
                        for (int i = 0; i < item_info_list.size(); i++){
                            JSONObject jbItem = item_info_list.getJSONObject(i);
                            String item_id = jbItem.getString("item_id");
                            String item_value = jbItem.getString("item_value");
                            if(item_id.equals("S004")) S004=item_value;
                        }
                    }
                }
                Double sm_set_speed=0D;
                try{
                    sm_set_speed=Double.parseDouble(S004);
                }
                catch (Exception exD){}
                //撕膜速度（m/min
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T005");
                jbAttr.put("item_value", String.format("%.3f", sm_set_speed));
                item_attr_list.add(jbAttr);
                //撕膜OK數
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T006");
                jbAttr.put("item_value", String.valueOf(finish_ok_count));
                item_attr_list.add(jbAttr);
                //撕膜NG數
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T007");
                jbAttr.put("item_value", String.valueOf(plan_lot_count-finish_ok_count));
                item_attr_list.add(jbAttr);
                //實際撕膜速度（m/min）
                Double sm_actual_speed=0D;
                try{
                    String T008= opCommonFunc.ReadCellOneRedisValue(station_code,station_attr,
                            "Plc","PlcStatus","Speed");
                    sm_actual_speed=Double.parseDouble(T008);
                }
                catch (Exception exC){}
                //实际撕膜速度（m/min)
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T008");
                jbAttr.put("item_value", String.format("%.3f", sm_actual_speed));
                item_attr_list.add(jbAttr);
                //用电量
                jbAttr=new JSONObject();
                jbAttr.put("item_id", "T009");
                jbAttr.put("item_value", String.format("%.3f", 0D));
                item_attr_list.add(jbAttr);
                eapDyCjSendFlowFunc.WIPTrackingReport(station_id_long, station_code, user_name, dept_id, shift_id,
                        task_start_time, task_end_time, lot_num, sum_plan_lot_count, plan_lot_count, finish_ok_count,
                        material_code, lot_short_num, lot_version, prod_mode, attribute1, attribute2,
                        item_attr_list, offline_flag, local_flag, attr_else);
            }
            if(iteratorBigData.hasNext()) iteratorBigData.close();

            //上报Carrier状态
            String carrierStatus="ProcessEnd";
            if(sum_finish_count<sum_plan_lot_count) carrierStatus="Abort";
            if(sum_finish_count==0) carrierStatus="CancelOP";
            eapDyCjSendFlowFunc.CarrierStatusReport(station_code, pallet_num, carrierStatus, lot_list, "0",
                    station_attr, "01","N");

            //更新状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("lot_status").in(group_lot_status));
            queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status));
            Update updateBigData = new Update();
            updateBigData.set("lot_status", "FINISH");
            updateBigData.set("group_lot_status", "FINISH");
            updateBigData.set("task_error_code", 4);
            updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            result="OK";

            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,result,"",0);
        }
        catch (Exception ex){
            errorMsg= "Exception:"+ex;
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
