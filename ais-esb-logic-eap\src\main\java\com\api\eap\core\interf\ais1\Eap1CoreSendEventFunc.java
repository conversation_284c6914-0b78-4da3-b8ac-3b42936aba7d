package com.api.eap.core.interf.ais1;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <p>
 * EAP发送事件功能函数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class Eap1CoreSendEventFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private Eap1CoreInterfCommon eap1CoreInterfCommon;
    @Autowired
    private Eap1CoreSendEventSubFunc eap1CoreSendEventSubFunc;

    //1.[接口]通用接口(EAP发布)
    public String EapCoreCommonEvent(String esbInterfCode, JSONObject postParas) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = eap1CoreSendEventSubFunc.EapCoreCommonEvent(esbInterfCode,postParas);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, null);
        }
        if (!successFlag) {
            throw new Exception(message);
        }
        return responseParas;
    }

}
