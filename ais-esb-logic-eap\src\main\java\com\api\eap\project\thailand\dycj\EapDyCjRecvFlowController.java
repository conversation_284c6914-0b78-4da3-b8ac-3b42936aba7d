package com.api.eap.project.thailand.dycj;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 泰国定颖(持久)EAP接受流程信息定义接口
 * 1.ProductionInfoDownload:(在线)接受EAP下发任务
 * 2.LotCommandDownload:(在线)接受EAP通知下发任务全部完成命令
 * 3.BcOffLineLotDownLoad:(本地)上游通知下游工单信息
 * 4.LotFinishDownLoad:(本地)结批完成
 * 5.EachPanelDataDownLoad:接受上游设备发送过来的当片信息
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-12
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/dycj/interf/recv")
public class EapDyCjRecvFlowController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapDyCjRecvFlowFunc eapDyCjRecvFlowFunc;

    //:(在线)接受EAP下发任务
    @RequestMapping(value = "/ProductionInfoDownload",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvFlowProductionInfoDownload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/ProductionInfoDownload";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapDyCjRecvFlowFunc.ProductionInfoDownload(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //(在线)接受EAP通知下发任务全部完成命令
    @RequestMapping(value = "/LotCommandDownload",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvFlowLotCommandDownload(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/LotCommandDownload";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapDyCjRecvFlowFunc.LotCommandDownload(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //(本地)上游通知下游工单信息
    @RequestMapping(value = "/BcOffLineLotDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvFlowBcOffLineLotDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/BcOffLineLotDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapDyCjRecvFlowFunc.BcOffLineLotDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //(本地)结批完成
    @RequestMapping(value = "/LotFinishDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvFlowLotFinishDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/LotFinishDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapDyCjRecvFlowFunc.LotFinishDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }

    //接受上游设备发送过来的当片信息
    @RequestMapping(value = "/EachPanelDataDownLoad",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String EapDyCjRecvFlowEachPanelDataDownLoad(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/thailand/dycj/interf/recv/EachPanelDataDownLoad";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=eapDyCjRecvFlowFunc.EachPanelDataDownLoad(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
        }
        return responseParas;
    }
}
