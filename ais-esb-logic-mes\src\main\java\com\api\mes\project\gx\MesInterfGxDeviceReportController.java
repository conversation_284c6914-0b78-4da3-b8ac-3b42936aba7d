package com.api.mes.project.gx;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 1.设备上报MES工件到达
 * </p>
 *
 * <AUTHOR>
 * @since 2022-08-05
 */
@RestController
@Slf4j
@RequestMapping("/mes/interf/project/gx")
public class MesInterfGxDeviceReportController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;

    //1.逸飞设备上报MES工件到达
    @RequestMapping(value = "/MesInterfGxPalletArrive", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesInterfGxPalletArrive(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/interf/project/gx/MesInterfGxPalletArrive";
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = mesInterfGxPalletArrive(jsonParas, request, apiRoutePath);
        Boolean prodFlag = true;
        Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
        String esbInterfCode = jbResult.getString("esbInterfCode");
        String token = jbResult.getString("token");
        String requestParas = jbResult.getString("requestParas");
        String responseParas = jbResult.getString("responseParas");
        Boolean successFlag = jbResult.getBoolean("successFlag");
        String message = jbResult.getString("message");
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if (isSaveFlag) {
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                    successFlag, message, request);
        }
        return responseParas;
    }

    //处理工件到位逻辑
    private JSONObject mesInterfGxPalletArrive(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "MesInterfGxPalletArrive";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        JSONObject jbResponse = null;
        String request_uuid = CFuncUtilsSystem.CreateUUID(false);
        String meOnlineFlowTable = "c_mes_me_online_flow";
        String meStationRecipeTable = "c_mes_me_recipe_data";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //接受参数
            request_uuid = jsonParas.getString("request_uuid");
            String station_code = jsonParas.getString("station_code");
            String serial_num = jsonParas.getString("serial_num");

            //返回初始化
            jbResponse = new JSONObject();
            jbResponse.put("response_uuid", request_uuid);
            jbResponse.put("response_time", CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
            jbResponse.put("response_attr", "");
            jbResponse.put("code", 0);
            jbResponse.put("msg", "");
            jbResponse.put("data", new JSONObject());

            if (serial_num == null || serial_num.equals("")) {
                errorMsg = "模组码不能为空";
                jbResponse.put("code", -1);
                jbResponse.put("msg", errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if (station_code == null || station_code.equals("")) {
                errorMsg = "工位号不能为空";
                jbResponse.put("code", -2);
                jbResponse.put("msg", errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            if (!station_code.equals("OP1130") && !station_code.equals("OP1140-1") && !station_code.equals("OP1140-2")) {
                errorMsg = "工位号只能为OP1130|OP1140-1|OP1140-2";
                jbResponse.put("code", -2);
                jbResponse.put("msg", errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //1.查询线首信息是否存在
            Boolean isOnline = false;
            String small_model_type = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meOnlineFlowTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                isOnline = true;
                Document docItemBigData = iteratorBigData.next();
                small_model_type = docItemBigData.getString("small_model_type");
                iteratorBigData.close();
            }
            if (!isOnline) {
                errorMsg = "当前模组码{" + serial_num + "}未在MES上线过";
                jbResponse.put("code", -3);
                jbResponse.put("msg", errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //汇流排工位定制化
            if (station_code.equals("OP1130")) {
                //先进行复位
                String tag_list = "OP1130/PlcQuality/PlcQ_ModuleBlockCode,OP1130/PlcStatus/PlcS_RequestSignal";
                String tag_value_list = "" + "&0";
                errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tag_list, tag_value_list, true);
                if (!errorMsg.equals("")) {
                    jbResponse.put("code", -4);
                    jbResponse.put("msg", errorMsg);
                    responseParas = jbResponse.toString();
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                Thread.sleep(400);
                //将PLC请求信号以及PLC模组码模拟给出
                tag_list = "OP1130/PlcQuality/PlcQ_ModuleBlockCode,OP1130/PlcStatus/PlcS_RequestSignal";
                tag_value_list = serial_num + "&1";
                errorMsg = cFuncUtilsCellScada.WriteTagByStation(station_code, station_code, tag_list, tag_value_list, true);
                if (!errorMsg.equals("")) {
                    jbResponse.put("code", -5);
                    jbResponse.put("msg", errorMsg);
                    responseParas = jbResponse.toString();
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "接受汇流排设备通知工件到达成功");
                return jbResult;
            }

            //针对焊接房1和2查询配方信息
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("serial_num").is(serial_num));
            queryBigData.addCriteria(Criteria.where("station_code").is("OP1110"));
            queryBigData.addCriteria(Criteria.where("enable_flag").is("Y"));
            iteratorBigData = mongoTemplate.getCollection(meStationRecipeTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(100).iterator();
            Map<String, Object> mapParasItem = new HashMap<>();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                long tag_id = docItemBigData.getLong("tag_id");
                String tag_value = docItemBigData.getString("tag_value");
                mapParasItem.put(String.valueOf(tag_id), tag_value);
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();
            if (!mapParasItem.containsKey("111004021") || !mapParasItem.containsKey("111004022") ||
                    !mapParasItem.containsKey("111004023") || !mapParasItem.containsKey("111004024") ||
                    !mapParasItem.containsKey("111004025") || !mapParasItem.containsKey("111004026") ||
                    !mapParasItem.containsKey("111004027") || !mapParasItem.containsKey("111004029") ||
                    !mapParasItem.containsKey("111004030") || !mapParasItem.containsKey("111004031")) {
                errorMsg = "极柱定位测距PLC未给出定位数据到MES";
                jbResponse.put("code", -6);
                jbResponse.put("msg", errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //存储过站信息
            String sqlProdLine = "select b.prod_line_code " +
                    "from sys_fmod_station a inner join sys_fmod_prod_line b " +
                    "on a.prod_line_id=b.prod_line_id " +
                    "where a.station_code='" + station_code + "'";
            List<Map<String, Object>> itemListLine = cFuncDbSqlExecute.ExecSelectSql(station_code, sqlProdLine,
                    false, request, apiRoutePath);
            String prod_line_code = itemListLine.get(0).get("prod_line_code").toString();
            JSONObject postParas = new JSONObject();
            postParas.put("prod_line_code", prod_line_code);
            postParas.put("station_code", station_code);
            postParas.put("serial_num", serial_num);
            postParas.put("arrive_date", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("serial_num", serial_num);
            String url = "http://127.0.0.1:9090/aisEsbApi/mes/core/recipe/MesCoreStationFlowSave";
            //String url="http://127.0.0.1:9089/mes/core/recipe/MesCoreStationFlowSave";
            JSONObject jbFlowResult = cFuncUtilsRest.PostJbBackJb(url, postParas);
            if (jbFlowResult.getInteger("code") != 0) {
                errorMsg = jbFlowResult.getString("error");
                jbResponse.put("code", -7);
                jbResponse.put("msg", errorMsg);
                responseParas = jbResponse.toString();
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }

            //查询配方并返回
            JSONObject jsonObject1 = new JSONObject();
            jsonObject1.put("small_model_type", small_model_type);
            jsonObject1.put("serial_work_type", 1);
            JSONArray jsonArray = new JSONArray();
            JSONObject jsonObject2 = null;
            for (Map.Entry<String, Object> mItem : mapParasItem.entrySet()) {
                String tag_id = mItem.getKey();
                String tag_value = mItem.getValue().toString();
                if (tag_id.equals("111004025")) {//第一列电芯相机极柱圆心X坐标数组[PlcQ_ModuleCellPole1XCoordinate]
                    String[] tag_value_list = tag_value.split(",", -1);
                    if (tag_value_list != null && tag_value_list.length > 0) {
                        for (int i = 0; i < tag_value_list.length; i++) {
                            jsonObject2 = new JSONObject();
                            jsonObject2.put("item_code", "Column" + (i + 1) + "_M1_XCoordinate");
                            if (i < 52) {
                                jsonObject2.put("item_des", "第一列电芯" + (i + 1) + "第一个相机极柱圆心Y坐标");
                            } else {
                                jsonObject2.put("item_des", "第二列电芯" + (i + 1) + "第一个相机极柱圆心Y坐标");
                            }
                            jsonObject2.put("item_value_type", "Float");
                            jsonObject2.put("item_value", tag_value_list[i]);
                            jsonArray.add(jsonObject2);
                        }
                    }
                }
                if (tag_id.equals("111004026")) {//第一列电芯相机极柱圆心Y坐标数组[PlcQ_ModuleCellPole1YCoordinate]
                    String[] tag_value_list = tag_value.split(",", -1);
                    if (tag_value_list != null && tag_value_list.length > 0) {
                        for (int i = 0; i < tag_value_list.length; i++) {
                            jsonObject2 = new JSONObject();
                            jsonObject2.put("item_code", "Column" + (i + 1) + "_M1_YCoordinate");
                            if (i < 52) {
                                jsonObject2.put("item_des", "第一列电芯" + (i + 1) + "第一个相机极柱圆心Y坐标");
                            } else {
                                jsonObject2.put("item_des", "第二列电芯" + (i + 1) + "第一个相机极柱圆心Y坐标");
                            }
                            jsonObject2.put("item_value_type", "Float");
                            jsonObject2.put("item_value", tag_value_list[i]);
                            jsonArray.add(jsonObject2);
                        }
                    }
                }
                if (tag_id.equals("111004027")) {//第一列电芯相机极柱补偿角度数组[PlcQ_ModuleCellPole1CompensationAngle]
                    String[] tag_value_list = tag_value.split(",", -1);
                    if (tag_value_list != null && tag_value_list.length > 0) {
                        for (int i = 0; i < tag_value_list.length; i++) {
                            jsonObject2 = new JSONObject();
                            jsonObject2.put("item_code", "Column" + (i + 1) + "_M1_AngleCoordinate");
                            if (i < 52) {
                                jsonObject2.put("item_des", "第一列电芯" + (i + 1) + "第一个相机极柱补偿角度");
                            } else {
                                jsonObject2.put("item_des", "第二列电芯" + (i + 1) + "第一个相机极柱补偿角度");
                            }
                            jsonObject2.put("item_value_type", "Float");
                            jsonObject2.put("item_value", tag_value_list[i]);
                            jsonArray.add(jsonObject2);
                        }
                    }
                }
                if (tag_id.equals("111004029")) {//第二列电芯相机极柱圆心X坐标数组[PlcQ_ModuleCellPole2XCoordinate]
                    String[] tag_value_list = tag_value.split(",", -1);
                    if (tag_value_list != null && tag_value_list.length > 0) {
                        for (int i = 0; i < tag_value_list.length; i++) {
                            jsonObject2 = new JSONObject();
                            jsonObject2.put("item_code", "Column" + (i + 1) + "_M2_XCoordinate");
                            if (i < 52) {
                                jsonObject2.put("item_des", "第一列电芯" + (i + 1) + "第二个相机极柱圆心X坐标");
                            } else {
                                jsonObject2.put("item_des", "第二列电芯" + (i + 1) + "第二个相机极柱圆心X坐标");
                            }
                            jsonObject2.put("item_value_type", "Float");
                            jsonObject2.put("item_value", tag_value_list[i]);
                            jsonArray.add(jsonObject2);
                        }
                    }
                }
                if (tag_id.equals("111004030")) {//第二列电芯相机极柱圆心Y坐标数组[PlcQ_ModuleCellPole2YCoordinate]
                    String[] tag_value_list = tag_value.split(",", -1);
                    if (tag_value_list != null && tag_value_list.length > 0) {
                        for (int i = 0; i < tag_value_list.length; i++) {
                            jsonObject2 = new JSONObject();
                            jsonObject2.put("item_code", "Column" + (i + 1) + "_M2_YCoordinate");
                            if (i < 52) {
                                jsonObject2.put("item_des", "第一列电芯" + (i + 1) + "第二个相机极柱圆心Y坐标");
                            } else {
                                jsonObject2.put("item_des", "第二列电芯" + (i + 1) + "第二个相机极柱圆心Y坐标");
                            }
                            jsonObject2.put("item_value_type", "Float");
                            jsonObject2.put("item_value", tag_value_list[i]);
                            jsonArray.add(jsonObject2);
                        }
                    }
                }
                if (tag_id.equals("111004031")) {//第二列电芯相机极柱补偿角度数组[PlcQ_ModuleCellPole2CompensationAngle]
                    String[] tag_value_list = tag_value.split(",", -1);
                    if (tag_value_list != null && tag_value_list.length > 0) {
                        for (int i = 0; i < tag_value_list.length; i++) {
                            jsonObject2 = new JSONObject();
                            jsonObject2.put("item_code", "Column" + (i + 1) + "_M2_AngleCoordinate");
                            if (i < 52) {
                                jsonObject2.put("item_des", "第一列电芯" + (i + 1) + "第二个相机极柱补偿角度");
                            } else {
                                jsonObject2.put("item_des", "第二列电芯" + (i + 1) + "第二个相机极柱补偿角度");
                            }
                            jsonObject2.put("item_value_type", "Float");
                            jsonObject2.put("item_value", tag_value_list[i]);
                            jsonArray.add(jsonObject2);
                        }
                    }
                }
                if (tag_id.equals("111004021")) {//相机Mark1圆心X坐标[PlcQ_Mark1XCoordinate]
                    jsonObject2 = new JSONObject();
                    jsonObject2.put("item_code", "Mark1XCoordinate");
                    jsonObject2.put("item_des", "相机Mark1圆心X坐标");
                    jsonObject2.put("item_value_type", "Float");
                    jsonObject2.put("item_value", tag_value);
                    jsonArray.add(jsonObject2);
                }
                if (tag_id.equals("111004022")) {//相机Mark1圆心Y坐标[PlcQ_Mark1YCoordinate]
                    jsonObject2 = new JSONObject();
                    jsonObject2.put("item_code", "Mark1YCoordinate");
                    jsonObject2.put("item_des", "相机Mark1圆心Y坐标");
                    jsonObject2.put("item_value_type", "Float");
                    jsonObject2.put("item_value", tag_value);
                    jsonArray.add(jsonObject2);
                }
                if (tag_id.equals("111004023")) {//相机Mark2圆心X坐标[PlcQ_Mark2XCoordinate]
                    jsonObject2 = new JSONObject();
                    jsonObject2.put("item_code", "Mark2XCoordinate");
                    jsonObject2.put("item_des", "相机Mark2圆心X坐标");
                    jsonObject2.put("item_value_type", "Float");
                    jsonObject2.put("item_value", tag_value);
                    jsonArray.add(jsonObject2);
                }
                if (tag_id.equals("111004024")) {//相机Mark2圆心Y坐标[PlcQ_Mark2YCoordinate]
                    jsonObject2 = new JSONObject();
                    jsonObject2.put("item_code", "Mark2YCoordinate");
                    jsonObject2.put("item_des", "相机Mark2圆心Y坐标");
                    jsonObject2.put("item_value_type", "Float");
                    jsonObject2.put("item_value", tag_value);
                    jsonArray.add(jsonObject2);
                }
            }
            jsonObject1.put("craft_paras", jsonArray);
            JSONArray jsonArray1 = new JSONArray();
            jsonObject1.put("single_paras", jsonArray1);
            JSONArray jsonArray2 = new JSONArray();
            jsonObject1.put("multy_paras", jsonArray2);
            jbResponse.put("data", jsonObject1);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受焊接设备通知工件到达成功");
        } catch (Exception ex) {
            errorMsg = "MES处理设备通知工件到达接口发生未知异常:" + ex.getMessage();
            jbResponse = new JSONObject();
            jbResponse.put("response_uuid", request_uuid);
            jbResponse.put("response_time", CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd HH:mm:ss"));
            jbResponse.put("response_attr", "");
            jbResponse.put("code", -99);
            jbResponse.put("msg", errorMsg);
            responseParas = jbResponse.toString();
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.MES通知设备放行接口
    @RequestMapping(value = "/MesInterfGxConfirmGo", method = {RequestMethod.POST, RequestMethod.GET})
    public String MesInterfGxConfirmGo(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "mes/interf/project/gx/MesInterfGxConfirmGo";
        String transResult = "";
        String errorMsg = "";
        try {
            String startDate = CFuncUtilsSystem.GetNowDateTime("");
            JSONObject jbResult = mesInterfGxConfirmGo(jsonParas);
            Boolean prodFlag = true;
            Boolean isSaveFlag = jbResult.getBoolean("isSaveFlag");
            String esbInterfCode = jbResult.getString("esbInterfCode");
            String token = jbResult.getString("token");
            String requestParas = jbResult.getString("requestParas");
            String responseParas = jbResult.getString("responseParas");
            Boolean successFlag = jbResult.getBoolean("successFlag");
            String message = jbResult.getString("message");
            String endDate = CFuncUtilsSystem.GetNowDateTime("");
            //记录日志
            if (isSaveFlag) {
                cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag, token, requestParas, responseParas,
                        successFlag, message, null);
            }
            if (!successFlag) {
                transResult = CFuncUtilsLayUiResut.GetErrorJson(message);
                return transResult;
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "MES通知设备放行接口异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //处理放行接口逻辑
    private JSONObject mesInterfGxConfirmGo(JSONObject jsonParas) {
        JSONObject jbResult = null;
        String errorMsg = "";
        String esbInterfCode = "MesInterfGxConfirmGo";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String confirm_code = jsonParas.getString("confirm_code");
            //1.根据接口查询url
            String sqlInterf = "select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='" + esbInterfCode + "' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemList = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlInterf, false, null, "");
            if (itemList == null || itemList.size() <= 0) {
                errorMsg = "接口表中未配置接口代码为{" + esbInterfCode + "}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url = itemList.get(0).get("esb_prod_intef_url").toString();
            if (esb_prod_intef_url == null || esb_prod_intef_url.equals("")) {
                errorMsg = "接口表中配置接口代码为{" + esbInterfCode + "}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }
            JSONObject postParas = new JSONObject();
            postParas.put("request_uuid", CFuncUtilsSystem.CreateUUID(true));
            postParas.put("request_time", CFuncUtilsSystem.GetNowDateTime(""));
            postParas.put("request_attr", "");
            postParas.put("data_from_sys", "MES");
            postParas.put("station_code", station_code);
            postParas.put("confirm_code", confirm_code);
            requestParas = postParas.toString();
            JSONObject jsonObjectYf = cFuncUtilsRest.PostJbBackJb(esb_prod_intef_url, postParas);
            responseParas = jsonObjectYf.toString();
            Integer code = jsonObjectYf.getInteger("code");
            String msg = jsonObjectYf.getString("msg");
            if (code != 0) {
                errorMsg = "通知设备放行接口返回失败：" + msg;
                jbResult = new JSONObject();
                jbResult.put("isSaveFlag", true);
                jbResult.put("esbInterfCode", esbInterfCode);
                jbResult.put("token", token);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //成功
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "通知成功");
        } catch (Exception ex) {
            errorMsg = "发生未知异常:" + ex.getMessage();
            jbResult = new JSONObject();
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }
}
