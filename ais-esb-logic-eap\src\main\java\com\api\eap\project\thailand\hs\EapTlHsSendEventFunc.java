package com.api.eap.project.thailand.hs;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 泰国沪士EAP发送事件功能函数
 * 1.通用事件上报
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-18
 */
@Service
@Slf4j
public class EapTlHsSendEventFunc {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private EapTlHsSendEventSubFunc eapTlHsSendEventSubFunc;

    //1.通用事件上报
    @Async
    public void ComomEventFunc(String esbInterfCode,JSONObject postParas) throws Exception{
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult= eapTlHsSendEventSubFunc.ComomEventSubFunc(esbInterfCode,postParas);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, null);
        }
        if(!successFlag){
            throw new Exception(message);
        }
    }
}
