package com.api.pack.core.sort;

import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.IMybatisBasic;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 * 分选截取比对规则
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("a_pack_fmod_sort_split_rule")
public class SortSplitRule extends IMybatisBasic
{
    @ApiModelProperty(value = "截取规则ID")
    @TableId(value = "split_rule_id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "截取规则名称")
    @TableField(value = "split_rule_name")
    private String name;

    @ApiModelProperty(value = "截取规则内容")
    @TableField(value = "split_rule_content")
    private String content; // 如：["规则名;规则编码;板件类型;比对函数;源字段,起始索引,截取长度,是否反转;目标字段,起始索引,截取长度,是否反转;是否必须有条码"]

    @JsonIgnore
    @JSONField(serialize = false, deserialize = false)
    @TableField(exist = false)
    private List<Item> items;

    public SortSplitRule(String name, String content)
    {
        this.name = name;
        this.content = content;
    }

    public static SortSplitRule byId(Long id, SortSplitRuleService service)
    {
        return service.getById(id);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Item
    {
        @ApiModelProperty(value = "分选规则名称")
        private String name;

        @ApiModelProperty(value = "分选规则编码")
        private String code;

        @ApiModelProperty(value = "板件类型")
        private String boardType;

        @ApiModelProperty(value = "源字段KEY")
        private String srcKey;

        @ApiModelProperty(value = "源字段截取起始索引")
        private Integer srcSplitIndex;

        @ApiModelProperty(value = "源字段截取长度")
        private Integer srcSplitLength;

        @ApiModelProperty(value = "源字段截取是否反转")
        private Boolean srcSplitReverse;

        @ApiModelProperty(value = "目标字段KEY")
        private String dstKey;

        @ApiModelProperty(value = "目标字段分割起始索引")
        private Integer dstSplitIndex;

        @ApiModelProperty(value = "目标字段分割长度")
        private Integer dstSplitLength;

        @ApiModelProperty(value = "目标字段是否反转")
        private Boolean dstSplitReverse;

        @ApiModelProperty(value = "比对函数")
        private String compareFunc;

        @ApiModelProperty(value = "是否必须有条码")
        private Boolean requiredBarcode;

        public Item(String name, String code, String srcKey, Boolean requiredBarcode)
        {
            this.name = name;
            this.code = code;
            this.boardType = SortConst.BOARD_CATEGORY_SET;
            this.srcKey = srcKey;
            this.srcSplitIndex = 0;
            this.srcSplitLength = 0;
            this.srcSplitReverse = false;
            this.compareFunc = SortConst.COMPARE_FUNC_EQUAL;
            this.requiredBarcode = requiredBarcode;
        }

        public Item(String name, String code, String boardType, String srcKey, String dstKey, Boolean requiredBarcode)
        {
            this.name = name;
            this.code = code;
            this.boardType = boardType;
            this.srcKey = srcKey;
            this.srcSplitIndex = 0;
            this.srcSplitLength = 0;
            this.srcSplitReverse = false;
            this.dstKey = dstKey;
            this.dstSplitIndex = 0;
            this.dstSplitLength = 0;
            this.dstSplitReverse = false;
            this.compareFunc = SortConst.COMPARE_FUNC_EQUAL;
            this.requiredBarcode = requiredBarcode;
        }

        public Item(String content)
        {
            String[] c = content.split(";");
            if (c.length < 6)
            {
                throw new IllegalArgumentException("Invalid content: " + content);
            }
            this.name = c[0];
            this.code = c[1];
            this.boardType = c[2];
            this.compareFunc = c[3];
            String src = c[4];
            String dst = c[5];
            String[] s = src.split(",");
            if (s.length != 4)
            {
                throw new IllegalArgumentException("Invalid src: " + src);
            }
            this.srcKey = s[0];
            this.srcSplitIndex = Integer.parseInt(s[1]);
            this.srcSplitLength = Integer.parseInt(s[2]);
            this.srcSplitReverse = Boolean.parseBoolean(s[3]);
            String[] d = dst.split(",");
            if (d.length == 4)
            {
                this.dstKey = d[0];
                this.dstSplitIndex = Integer.parseInt(d[1]);
                this.dstSplitLength = Integer.parseInt(d[2]);
                this.dstSplitReverse = Boolean.parseBoolean(d[3]);
            }
            else
            {
                this.dstKey = d[0];
            }
            if (c.length > 6)
            {
                this.requiredBarcode = Boolean.parseBoolean(c[6]);
            }
            else
            {
                this.requiredBarcode = false;
            }
        }

        public String toContent()
        {
            // 源字段,起始索引,截取长度,是否反转
            String src = String.format("%s,%d,%d,%b", srcKey, srcSplitIndex, srcSplitLength, srcSplitReverse);
            // 目标字段,起始索引,截取长度,是否反转
            String dst = String.format("%s,%d,%d,%b", dstKey, dstSplitIndex, dstSplitLength, dstSplitReverse);
            // 规则名;规则编码;板件类型;比对函数;源字段,起始索引,截取长度,是否反转;目标字段,起始索引,截取长度,是否反转;是否必须有条码
            return String.format("%s;%s;%s;%s;%s;%s;%s", name, code, boardType, compareFunc, src, dst, requiredBarcode);
        }

        public String process(String dataName, String dataValue, Boolean reverse, Integer start, Integer length)
        {
            if (reverse == null)
            {
                return dataValue;
            }
            if (length == 0)
            {
                return dataValue;
            }
            else if (length < 0)
            {
                String err = String.format("%s根据规则名称{%s}规则方法{%s}截取字符失败：截取设置长度%d必须大于0", dataName, this.name, this.code, length);
                throw new SortSplitRuleException(err);
            }
            int dataLength = dataValue.length();
            int actualStart = start;
            int splitDataLength = start + length;
            if (reverse)
            {
                dataValue = new StringBuilder(dataValue).reverse().toString();
            }
            if (splitDataLength > dataLength)
            {
                String err = String.format("%s根据规则名称{%s}规则方法{%s}截取字符失败：字符截止位%d超出界限", dataName, this.name, this.code, splitDataLength);
                throw new SortSplitRuleException(err);
            }
            dataValue = dataValue.substring(actualStart, actualStart + length);
            if (reverse)
            {
                dataValue = new StringBuilder(dataValue).reverse().toString();
            }
            return dataValue;
        }

        public String processOfSrc(String dataName, String dataValue)
        {
            return process(dataName, dataValue, srcSplitReverse, srcSplitIndex, srcSplitLength);
        }

        public String processOfDst(String dataName, String dataValue)
        {
            return process(dataName, dataValue, dstSplitReverse, dstSplitIndex, dstSplitLength);
        }

        public boolean isDefault()
        {
            return !SortConst.NON_DEFAULT.contains(this.code);
        }
    }
}
