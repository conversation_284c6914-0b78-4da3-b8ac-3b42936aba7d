package com.api.pack.core.fmod;

import com.api.system.FastCodeGroupMapper;
import com.api.system.FastCodeMapper;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Transactional(rollbackFor = Exception.class)
public class FmodRecipeShipAddressDetailService extends ServiceImpl<FmodRecipeShipAddressDetailMapper, FmodRecipeShipAddressDetail> implements IService<FmodRecipeShipAddressDetail>
{
    private final FmodRecipeShipAddressDetailMapper mapper;

    private final FastCodeGroupMapper fastCodeGroupMapper;

    private final FastCodeMapper fastCodeMapper;

    public FmodRecipeShipAddressDetailService(FmodRecipeShipAddressDetailMapper mapper, FastCodeGroupMapper fastCodeGroupMapper, FastCodeMapper fastCodeMapper)
    {
        this.mapper = mapper;
        this.fastCodeGroupMapper = fastCodeGroupMapper;
        this.fastCodeMapper = fastCodeMapper;
    }
}
