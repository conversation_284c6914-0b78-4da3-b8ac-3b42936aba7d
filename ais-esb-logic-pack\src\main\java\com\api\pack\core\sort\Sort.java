package com.api.pack.core.sort;

import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.IMybatisBasic;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@TableName("a_pack_fmod_sort")
public class Sort extends IMybatisBasic {

    @ApiModelProperty(value = "分选条件编码")
    @JsonProperty("sort_code")
    @JSONField(name = "sort_code")
    @TableId(value = "sort_code")
    private String sortCode;

    @ApiModelProperty(value = "分选条件名称")
    @JsonProperty("sort_name")
    @JSONField(name = "sort_name")
    @TableField(value = "sort_name")
    private String sortName;

    @ApiModelProperty(value = "分选值")
    @JsonProperty("sort_value")
    @JSONField(name = "sort_value")
    @TableField("sort_value")
    private String sortValue;

    @ApiModelProperty(value = "分选启用标识")
    @JsonProperty("sort_flag")
    @JSONField(name = "sort_flag")
    @TableField("sort_flag")
    private String sortFlag;

    @ApiModelProperty(value = "分选排序索引")
    @JsonProperty("sort_index")
    @JSONField(name = "sort_index")
    @TableField("sort_index")
    private String sortIndex;
}
