package com.api.eap.core.interf.ais1;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.*;

/**
 * <p>
 * EAP接受流程功能函数
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@Service
@Slf4j
public class Eap1CoreRecvFlowFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private Eap1CoreInterfCommon eap1CoreInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private Eap1CoreSendFlowFunc eap1CoreSendFlowFunc;

    //1.AIS接受EAP下发任务(AIS发布)
    public JSONObject EapCoreInterfRecvTask(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "EapCoreInterfRecvTask";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 500;
        Long station_id = 0L;
        String user_name = "";
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.解析接受参数
            request_uuid=jsonParas.getString("request_uuid");
            String station_code = jsonParas.getString("station_code");//工位号/设备编号
            String group_lot_num = jsonParas.getString("group_lot_num");//母批号
            String pallet_num = jsonParas.getString("pallet_num");//载具条码
            String top_tray_num = jsonParas.getString("top_tray_num");//天盖条码
            String port_code = jsonParas.getString("port_code");//端口号
            String pallet_type = jsonParas.getString("pallet_type");//载具类型
            String product_type = jsonParas.getString("product_type");//生产模式 (1:dummy(陪镀板）;0:生产板)
            int inspect_count = jsonParas.getInteger("inspect_count");//首件数量,>0则代表需要首检
            int panel_model = jsonParas.getInteger("panel_model");//是否扫描板件,1扫描,0不扫描
            JSONArray arrlist = jsonParas.getJSONArray("task_list");
            if (arrlist == null || arrlist.size() <= 0) {
                errorMsg = "task_list为null数据,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //判断工位是否正确
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + station_code + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发配方任务中工位号{" + station_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr = itemListStation.get(0).get("station_attr").toString();
            //判断端口是否正确
            String sqlPortCount = "select count(1) " +
                    "from a_eap_fmod_station_port " +
                    "where enable_flag='Y' and station_id=" + station_id + " " +
                    "and port_code='" + port_code + "'";
            Integer portCount = cFuncDbSqlResolve.GetSelectCount(sqlPortCount);
            if (portCount <= 0) {
                errorMsg = "EAP下发配方任务中端口号{" + port_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //获取当前登入者
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }
            //解析数据
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            List<Map<String, Object>> lstPlanDocuments = new ArrayList<>();
            List<Map<String, Object>> lstPlanBDocuments = new ArrayList<>();
            List<String> lstLotNum = new ArrayList<>();
            String group_id = CFuncUtilsSystem.CreateUUID(true);
            for (int i = 0; i < arrlist.size(); i++) {
                Map<String, Object> mapBigDataRow = new HashMap<>();
                String plan_id = CFuncUtilsSystem.CreateUUID(true);
                JSONObject jbLotItem = arrlist.getJSONObject(i);
                String lot_num = jbLotItem.getString("lot_num") == null ? "" : jbLotItem.getString("lot_num");
                String lot_short_num = jbLotItem.getString("lot_short_num") == null ? "" : jbLotItem.getString("lot_short_num");
                String material_code = jbLotItem.getString("material_code") == null ? "" : jbLotItem.getString("material_code");
                int lot_index = jbLotItem.getString("lot_index") == null ? 1 : Integer.parseInt(jbLotItem.getString("lot_index"));
                int plan_count = jbLotItem.getString("plan_count") == null ? 100 : Integer.parseInt(jbLotItem.getString("plan_count"));
                float pallet_length = jbLotItem.getString("pallet_length") == null ? 0 : Float.parseFloat(jbLotItem.getString("pallet_length"));
                float pallet_width = jbLotItem.getString("pallet_width") == null ? 0 : Float.parseFloat(jbLotItem.getString("pallet_width"));
                float pallet_height = jbLotItem.getString("pallet_height") == null ? 0 : Float.parseFloat(jbLotItem.getString("pallet_height"));
                int between_times = jbLotItem.getString("between_times") == null ? 0 : Integer.parseInt(jbLotItem.getString("between_times"));
                String panel_list = jbLotItem.getString("panel_list") == null ? "" : jbLotItem.getString("panel_list");
                String lot_level_list = jbLotItem.getString("lot_level_list") == null ? "" : jbLotItem.getString("lot_level_list");
                String item_info = jbLotItem.getString("item_info") == null ? "" : jbLotItem.getString("item_info");
                String attribute1 = jbLotItem.getString("attribute1") == null ? "" : jbLotItem.getString("attribute1");
                String attribute2 = jbLotItem.getString("attribute2") == null ? "" : jbLotItem.getString("attribute2");
                String attribute3 = jbLotItem.getString("attribute3") == null ? "" : jbLotItem.getString("attribute3");

                //1.数据合规性判断
                if (lot_num.equals("")) {
                    errorMsg = "EAP下发的配方任务中{lot_num}工单字段值不能为空值";
                    opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                    responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                if (plan_count <= 0) {
                    errorMsg = "EAP下发的配方任务{" + lot_num + "}中{plan_count}工单数量字段值不能<=0";
                    opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                    responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                //2.判断工单是否已经存在
                if (lstLotNum.contains(lot_num)) {
                    errorMsg = "EAP下发配方任务中存在相同工单{" + lot_num + "}";
                    opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                    responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                String[] group_lot_status_list = new String[]{"WAIT", "PLAN", "WORK"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(group_lot_status_list));
                long planCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
                if (planCount > 0) {
                    errorMsg = "工单{" + lot_num + "}已存在且未被消耗掉,此时不允许EAP下发";
                    opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                    responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
                //3.加入Lot工单集合
                mapBigDataRow.put("item_date", item_date);
                mapBigDataRow.put("item_date_val", item_date_val);
                mapBigDataRow.put("plan_id", plan_id);
                mapBigDataRow.put("station_id", station_id);
                mapBigDataRow.put("task_from", "EAP");
                mapBigDataRow.put("group_lot_num", group_lot_num);
                mapBigDataRow.put("lot_num", lot_num);
                mapBigDataRow.put("lot_short_num", "");
                mapBigDataRow.put("lot_index", i + 1);
                mapBigDataRow.put("plan_lot_count", plan_count);
                mapBigDataRow.put("target_lot_count", plan_count);
                mapBigDataRow.put("port_code", port_code);
                mapBigDataRow.put("material_code", material_code);
                mapBigDataRow.put("pallet_num", pallet_num);
                mapBigDataRow.put("pallet_type", pallet_type);
                mapBigDataRow.put("lot_level", lot_level_list);
                mapBigDataRow.put("panel_length", pallet_length);
                mapBigDataRow.put("panel_width", pallet_width);
                mapBigDataRow.put("panel_tickness", pallet_height);
                mapBigDataRow.put("panel_model", -1);
                mapBigDataRow.put("inspect_count", inspect_count);
                mapBigDataRow.put("inspect_finish_count", 0);
                mapBigDataRow.put("pdb_count", 0);
                mapBigDataRow.put("pdb_rule", 0);
                mapBigDataRow.put("fp_count", 0);
                mapBigDataRow.put("group_lot_status", "PLAN");
                mapBigDataRow.put("lot_status", "PLAN");
                mapBigDataRow.put("finish_count", 0);
                mapBigDataRow.put("finish_ok_count", 0);
                mapBigDataRow.put("finish_ng_count", 0);
                mapBigDataRow.put("task_error_code", 0);
                mapBigDataRow.put("item_info", item_info);
                mapBigDataRow.put("task_start_time", "");
                mapBigDataRow.put("task_end_time", "");
                mapBigDataRow.put("task_cost_time", (long) 0);
                mapBigDataRow.put("attribute1", "");
                mapBigDataRow.put("attribute2", "");
                mapBigDataRow.put("attribute3", "");
                mapBigDataRow.put("face_code", 0);
                mapBigDataRow.put("pallet_use_count", 0);
                mapBigDataRow.put("user_name", user_name);
                mapBigDataRow.put("group_id", group_id);
                mapBigDataRow.put("offline_flag", "N");
                mapBigDataRow.put("target_update_count", 0);
                mapBigDataRow.put("pnl_infos", "");
                lstPlanDocuments.add(mapBigDataRow);
                lstLotNum.add(lot_num);
                //6.加入PNL集合
                String[] pnlids=panel_list.split(",");
                if (pnlids != null && pnlids.length > 0) {
                    for (int j = 0; j < pnlids.length; j++) {
                        String pnlid = pnlids[j];
                        String plan_d_id = CFuncUtilsSystem.CreateUUID(true);
                        Map<String, Object> mapBigDataRowB = new HashMap<>();
                        mapBigDataRowB.put("item_date", item_date);
                        mapBigDataRowB.put("item_date_val", item_date_val);
                        mapBigDataRowB.put("plan_d_id", plan_d_id);
                        mapBigDataRowB.put("plan_id", plan_id);
                        mapBigDataRowB.put("panel_barcode", pnlid);
                        mapBigDataRowB.put("use_sign", 0);
                        lstPlanBDocuments.add(mapBigDataRowB);
                    }
                }
            }
            if (lstPlanBDocuments.size() > 0) mongoTemplate.insert(lstPlanBDocuments, apsPlanBTable);
            if (lstPlanDocuments.size() > 0) mongoTemplate.insert(lstPlanDocuments, apsPlanTable);

            //返回消息
            code = 0;
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //2.EAP通知允许/不允许放板(AIS发布)
    public JSONObject EapCoreInterfRecvAllowWork(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "EapCoreInterfRecvAllowWork";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 500;
        Long station_id = 0L;
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.解析接受参数
            request_uuid = jsonParas.getString("request_uuid");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String allow_code = jsonParas.getString("allow_code");//允许代码：1允许放板/2主制程未准备好/3任务取消/4需要等待/5其他

            //判断工位是否正确
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + station_code + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发配方任务中工位号{" + station_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            station_id = Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr = itemListStation.get(0).get("station_attr").toString();

            String sqlPort = "select port_index " +
                    "from a_eap_fmod_station_port " +
                    "where enable_flag='Y' and station_id=" + station_id + " " +
                    "and port_code='" + port_code + "'";
            List<Map<String, Object>> itemListPort = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlPort,
                    false, request, apiRoutePath);
            if (itemListPort == null || itemListPort.size() <= 0) {
                errorMsg = "EAP下发控制投板命令中端口号{" + port_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            String port_index = itemListPort.get(0).get("port_index").toString();
            if (station_attr.equals("Load")) {
                errorMsg = opCommonFunc.WriteCellOneTagValue(station_code, station_attr,
                        "Eap", "EapOutStatus", "EapAllowStart" + port_index,
                        "EAP", allow_code, true);
                if (!errorMsg.equals("")) {
                    opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                    responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                    jbResult.put("responseParas", responseParas);
                    jbResult.put("successFlag", false);
                    jbResult.put("message", errorMsg);
                    return jbResult;
                }
            }
            //返回消息
            code = 0;
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

    //3.收板机先进后出请求切换工单号(AIS发布)
    public JSONObject EapCoreInterfUnLoadChangeLot(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception {
        JSONObject jbResult = new JSONObject();
        String errorMsg = "";
        String esbInterfCode = "EapCoreInterfUnLoadChangeLot";
        String token = "";
        String requestParas = jsonParas.toString();
        String responseParas = "";
        Integer code = 500;
        Long station_id = 0L;
        String request_uuid = CFuncUtilsSystem.GetOnlySign("");
        try {
            jbResult.put("isSaveFlag", true);
            jbResult.put("esbInterfCode", esbInterfCode);
            jbResult.put("token", token);
            jbResult.put("requestParas", requestParas);

            //1.解析接受参数
            request_uuid = jsonParas.getString("request_uuid");
            String station_code = jsonParas.getString("station_code");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String lot_num = jsonParas.getString("lot_num");
            //判断工位是否正确
            String sqlStation = "select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where enable_flag='Y' and station_code='" + station_code + "' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("EAP", sqlStation,
                    false, request, apiRoutePath);
            if (itemListStation == null || itemListStation.size() <= 0) {
                errorMsg = "EAP下发配方任务中工位号{" + station_code + "}在AIS系统中未配置";
                opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
                responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", false);
                jbResult.put("message", errorMsg);
                return jbResult;
            }
            //返回消息
            code = 0;
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, "成功");
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", true);
            jbResult.put("message", "接受成功");
        } catch (Exception ex) {
            errorMsg = "接口发生未知异常:" + ex;
            opCommonFunc.SaveCimMessage(station_id, "0", "EapCoreInterfRecvTask", "AIS", errorMsg, 5);
            responseParas = eap1CoreInterfCommon.CreateResponseHead01(request_uuid,code, errorMsg);
            jbResult.put("responseParas", responseParas);
            jbResult.put("successFlag", false);
            jbResult.put("message", errorMsg);
        }
        return jbResult;
    }

}
