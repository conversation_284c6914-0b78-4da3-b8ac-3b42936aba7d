package com.api.eap.project.dy;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 定颖EAP接受事件功能函数
 * 1.CimMessage:接受EAP下发弹窗消息
 * 2.UserLoginRequest:被动接受EAP下发指令登入登出
 * 3.HeartBeatCheckDownLoad:上游设备检测下游设备是否在线
 * 4.BcOffLineLoginInDownLoad:上游通知下游离线登入
 * 5.AreYouThere:EAP询问是否在线
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapDyRecvEventFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private EapDySendEventSubFunc eapDySendEventSubFunc;

    //1.接受EAP下发弹窗消息
    public JSONObject CimMessage(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="CimMessage";
        String esbInterfCode="CimMessage";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //增加CIM消息
            String screen_control=jbRecvBody.getString("screen_control");//0:弹窗5秒消失;1:弹窗人工确认
            String screen_code=jbRecvBody.getString("screen_code");
            String cim_msg=jbRecvBody.getString("cim_msg");
            Integer interval_second_time=0;//CIM消息停留时间,暂时预留
            if(jbRecvBody.containsKey("interval_second_time")) interval_second_time=jbRecvBody.getInteger("interval_second_time");
            if(interval_second_time==null || interval_second_time<=0) interval_second_time=5;
            long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();
            opCommonFunc.SaveCimMessage(station_id,screen_control,screen_code,"EAP",cim_msg,interval_second_time);
            //蜂蜜器
            Boolean isNewVersion = eapDyInterfCommon.CheckDyVersion(3);
            if(isNewVersion && screen_control.equals("1")){
                opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Plc","PlcStatus","AisLightBeer","EAP","1",false);
            }
            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //2.被动接受EAP下发指令登入登出
    public JSONObject UserLoginRequest(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="UserLoginRequest";
        String esbInterfCode="AisUserLoginRequest";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        String meUserTable="a_eap_me_station_user";
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.判定登入或者登出
            String user_name="";
            long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();
            String event_type="";
            Boolean isNewVersion=eapDyInterfCommon.CheckDyVersion(3);
            if(isNewVersion){
                event_type=jbRecvBody.getString("clock_type");
                user_name=jbRecvBody.getString("user_name");
            }
            else{
                event_type=jbRecvBody.getString("event_type");
            }
            String user_id=jbRecvBody.getString("user_id");
            String dept_id=jbRecvBody.getString("dept_id");
            String shift_id=jbRecvBody.getString("shift_id");
            String nick_name=jbRecvBody.getString("nick_name");
            if(user_name!=null && !user_name.equals("")){
                nick_name=user_name+"("+nick_name+")";
            }
            if(!event_type.equals("I") && !event_type.equals("O") && !event_type.equals("Q")){
                code=-3;
                errorMsg="event_type只能为I或者O或者Q,AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            Boolean checkIn=false;
            if(event_type.equals("I") || event_type.equals("O")){
                if(event_type.equals("I")) checkIn=true;
                eapDyInterfCommon.CheckInOrOut(checkIn,station_id,station_code,station_attr,user_id,dept_id,shift_id,nick_name);
            }
            else{//查询
                user_id="";
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
                queryBigData.with(Sort.by(Sort.Direction.DESC,"item_date_val"));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).
                        sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
                if(iteratorBigData.hasNext()){
                    Document docItemBigData = iteratorBigData.next();
                    user_id=docItemBigData.getString("user_name");
                    iteratorBigData.close();
                }
            }
            if(isNewVersion){
                JSONObject jbSend=new JSONObject();
                JSONObject response_head=eapDyInterfCommon.CreateResponseHead(function_name,request_uuid,"OK",code,"");
                JSONObject response_body=new JSONObject();
                response_body.put("clock_type",event_type);
                response_body.put("user_id",user_id);
                jbSend.put("response_head",response_head);
                jbSend.put("response_body",response_body);
                responseParas=jbSend.toString();
            }
            else{
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            }
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //3.上游设备检测下游设备是否在线
    public JSONObject HeartBeatCheckDownLoad(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="HeartBeatCheckDownLoad";
        String esbInterfCode="HeartBeatCheckDownLoad";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        Integer code=0;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(esbInterfCode)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //4.上游通知下游离线登入
    public JSONObject BcOffLineLoginInDownLoad(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="BcOffLineLoginInDownLoad";
        String esbInterfCode="BcOffLineLoginInDownLoad";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=0;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            jbRecvBody=jsonParas.getJSONObject("request_body");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr,station_code " +
                    "from sys_fmod_station where enable_flag='Y' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("DEVICE",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="未找到设备基础数据,AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.判定登入或者登出
            long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr=itemListStation.get(0).get("station_attr").toString();
            station_code=itemListStation.get(0).get("station_code").toString();

            String user_id=jbRecvBody.getString("user_id");
            String dept_id=jbRecvBody.getString("dept_id");
            String shift_id=jbRecvBody.getString("shift_id");
            String nick_name=jbRecvBody.getString("nick_name");
            eapDyInterfCommon.CheckInOrOut(true,station_id,station_code,station_attr,user_id,dept_id,shift_id,nick_name);

            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //5.EAP询问是否在线
    public JSONObject AreYouThere(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="AreYouThere";
        String esbInterfCode="AreYouThere";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        Integer code=0;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(esbInterfCode)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.获取EAP的IP地址
            String eap_ip="127.0.0.1";
            String sqlEapIp="select " +
                    "COALESCE(center_host_1,'') center_host_1," +
                    "COALESCE(center_host_2,'') center_host_2 " +
                    "from sys_core_center LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListEapIp=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlEapIp,false,request,apiRoutePath);
            if(itemListEapIp!=null && itemListEapIp.size()>0){
                String center_host_1=itemListEapIp.get(0).get("center_host_1").toString();
                String center_host_2=itemListEapIp.get(0).get("center_host_2").toString();
                if(center_host_2==null || center_host_2.equals("")){
                    eap_ip=center_host_1;
                }
                else{
                    eap_ip=center_host_2;
                }
            }

            //4.返回数据
            JSONObject jbSend=new JSONObject();
            JSONObject response_head=eapDyInterfCommon.CreateResponseHead(function_name,request_uuid,"OK",code,"");
            JSONObject response_body=new JSONObject();
            response_body.put("now",CFuncUtilsSystem.GetNowDateTime(""));
            response_body.put("ip",eap_ip);
            jbSend.put("response_head",response_head);
            jbSend.put("response_body",response_body);
            responseParas=jbSend.toString();

            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //EAP远程修改CimMode
    public JSONObject CIMModeChangeCommand(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="CIMModeChangeCommand";
        String esbInterfCode="CIMModeChangeCommand";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        Integer code=0;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(esbInterfCode)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String station_id=itemListStation.get(0).get("station_id").toString();
            String station_attr=itemListStation.get(0).get("station_attr").toString();

            //3.cim_mode
            JSONObject jbRecvBody=jsonParas.getJSONObject("request_body");
            String cim_mode=jbRecvBody.getString("cim_mode");
            String user_id=jbRecvBody.getString("user_id");
            //需要先请求CimMode再变更
            String auto_value="0";
            String model_value="0";
            if(cim_mode.equals("Semi-Auto")){
                auto_value="1";
            }
            if(cim_mode.equals("Remote")){
                auto_value="1";
                model_value="1";
            }
            String url = "http://127.0.0.1:9090/aisEsbApi/eap/project/dy/interf/send/CimModeChangeReport";
            JSONObject jbParas = new JSONObject();
            jbParas.put("station_id", station_id);
            jbParas.put("station_code", station_code);
            jbParas.put("station_attr", station_attr);
            jbParas.put("auto_value", auto_value);
            jbParas.put("model_value", model_value);
            JSONObject jbRecv = eapDyInterfCommon.PostJbBackJb(url, jbParas);
            Integer recv_code=jbRecv.getInteger("code");
            String recv_msg=jbRecv.getString("msg");
            if(recv_code!=0){
                code=recv_code;
                errorMsg=recv_msg;
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            if(cim_mode.equals("Local")){
                errorMsg=opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                           "Plc","PlcConfig","OnOffLine",user_id,"0",true);
                if(!errorMsg.equals("")){
                    code=-3;
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
            }
            else if(cim_mode.equals("Semi-Auto")){
                errorMsg=opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Plc","PlcConfig","OnOffLine",user_id,"1",true);
                if(!errorMsg.equals("")){
                    code=-4;
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
                errorMsg=opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Plc","PlcConfig","SysModel",user_id,"0",true);
                if(!errorMsg.equals("")){
                    code=-5;
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
            }
            else if(cim_mode.equals("Remote")){
                errorMsg=opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Plc","PlcConfig","OnOffLine",user_id,"1",true);
                if(!errorMsg.equals("")){
                    code=-6;
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
                errorMsg=opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Plc","PlcConfig","SysModel",user_id,"1",true);
                if(!errorMsg.equals("")){
                    code=-7;
                    responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                    jbResult.put("responseParas",responseParas);
                    jbResult.put("successFlag",false);
                    jbResult.put("message",errorMsg);
                    return jbResult;
                }
                //需要做自动登出动作
                eapDyInterfCommon.CheckInOrOut(false,Long.parseLong(station_id),station_code,station_attr,
                        "","","","");
            }

            //4.返回数据
            responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"OK",code,"");

            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //EAP请求校正时间
    public JSONObject DateTimeCalibration(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="DateTimeCalibration";
        String esbInterfCode="DateTimeCalibration";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        Integer code=0;
        String request_uuid= CFuncUtilsSystem.GetOnlySign("");
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("request_head");
            request_uuid=jbRecvHeader.getString("trx_id");
            String function_name=jbRecvHeader.getString("function_name");
            String station_code=jbRecvHeader.getString("eqp_id");

            //1.判断方法是否一致
            if(!function_name.equals(esbInterfCode)){
                code=-1;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_code='"+station_code+"'";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=-2;
                errorMsg="传递的设备编号{"+station_code+"}不存在AIS设备基础数据,AIS系统拒绝执行";
                responseParas=eapDyInterfCommon.CreateResponseHead01(function_name,request_uuid,"NG",code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //3.获取EAP的IP地址
            String eap_ip="127.0.0.1";
            String sqlEapIp="select " +
                    "COALESCE(center_host_1,'') center_host_1," +
                    "COALESCE(center_host_2,'') center_host_2 " +
                    "from sys_core_center LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListEapIp=cFuncDbSqlExecute.ExecSelectSql("EAP",sqlEapIp,false,request,apiRoutePath);
            if(itemListEapIp!=null && itemListEapIp.size()>0){
                String center_host_1=itemListEapIp.get(0).get("center_host_1").toString();
                String center_host_2=itemListEapIp.get(0).get("center_host_2").toString();
                if(center_host_2==null || center_host_2.equals("")){
                    eap_ip=center_host_1;
                }
                else{
                    eap_ip=center_host_2;
                }
            }

            JSONObject jbRecvBody=jsonParas.getJSONObject("request_body");
            String nowDateTime=jbRecvBody.getString("now");
            String ip=jbRecvBody.getString("ip");
            if(nowDateTime!=null && !nowDateTime.equals("")){
                //更改本地时间
                String[] lst=nowDateTime.split(" ",-1);
                String dataStr_=lst[0];
                String timeStr_=lst[1];
                String cmd = " cmd /c date " + dataStr_;
                Runtime.getRuntime().exec(cmd);
                cmd = " cmd /c time " + timeStr_;
                Runtime.getRuntime().exec(cmd);
            }

            //4.返回数据
            JSONObject jbSend=new JSONObject();
            JSONObject response_head=eapDyInterfCommon.CreateResponseHead(function_name,request_uuid,"OK",code,"");
            JSONObject response_body=new JSONObject();
            response_body.put("now",CFuncUtilsSystem.GetNowDateTime(""));
            response_body.put("ip",eap_ip);
            jbSend.put("response_head",response_head);
            jbSend.put("response_body",response_body);
            responseParas=jbSend.toString();

            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=-99;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapDyInterfCommon.CreateResponseHead01(funcName,request_uuid,"NG",code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
