package com.api.pack.core.ccd;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CCDLblBarcodeResultMessageContentItem
{
    @ApiModelProperty(value = "标签识别序号")
    @JsonProperty("BarCodeIndex")
    @JSONField(name = "BarCodeIndex")
    private Integer barCodeIndex;

    @ApiModelProperty(value = "标签识别结果")
    @JsonProperty("BarCodeValue")
    @JSONField(name = "BarCodeValue")
    private String barCodeValue;
}
