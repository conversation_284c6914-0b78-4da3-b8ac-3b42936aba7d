package com.api.eap.project.xinai;

import com.alibaba.fastjson.JSONObject;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 芯爱曝光机接口处理
 * 1.基板2DC读取结果通知
 * 2.投入基板的2DC和投入番号的通知
 * 3.基板强制投入的通知
 * </p>
 *
 * <AUTHOR>
 * @since 2023-06-19
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/xinai/interf/ldi/send")
public class EapXinaiLdiSendController {
    @Autowired
    private EapXinaiLdiSendFunc eapXinaiLdiSendFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.基板2DC读取结果通知
    @RequestMapping(value = "/EapXinaiLdiPanel2DCRead", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapXinaiLdiPanel2DCRead(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/xinai/interf/ldi/send/EapXinaiLdiPanel2DCRead";
        String transResult="";
        String errorMsg="";
        try{
            String panel_barcode=jsonParas.getString("panel_barcode");
            String panel_index=jsonParas.getString("panel_index");
            String decision=eapXinaiLdiSendFunc.Panel2DCRead(panel_barcode,panel_index);
            //若不允许给出报警提示
            if(!decision.equals("1")){
                String cim_msg="LDI不允许板件{"+panel_barcode+"}投入,请人工尝试点击强制放行请求";
                opCommonFunc.SaveCimMessage(0l,"1","2","LDI",cim_msg,5);
            }
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,decision,"",0);
        }
        catch (Exception ex){
            errorMsg= "基板2DC读取结果通知异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.投入基板的2DC和投入番号的通知
    @RequestMapping(value = "/EapXinaiLdiPanelTransferCommand", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapXinaiLdiPanelTransferCommand(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/xinai/interf/ldi/send/EapXinaiLdiPanelTransferCommand";
        String transResult="";
        String errorMsg="";
        try{
            String panel_barcode=jsonParas.getString("panel_barcode");
            String panel_index=jsonParas.getString("panel_index");
            eapXinaiLdiSendFunc.PanelTransferCommand(panel_barcode,panel_index);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "投入基板的2DC和投入番号的通知异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.基板强制投入的通知
    @RequestMapping(value = "/EapXinaiLdiForcedPanelLoading", method = {RequestMethod.POST,RequestMethod.GET})
    public String EapXinaiLdiForcedPanelLoading(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="eap/project/xinai/interf/ldi/send/EapXinaiLdiForcedPanelLoading";
        String transResult="";
        String errorMsg="";
        try{
            String panel_barcode=jsonParas.getString("panel_barcode");
            String panel_index=jsonParas.getString("panel_index");
            eapXinaiLdiSendFunc.ForcedPanelLoading(panel_barcode,panel_index);
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,"1","",0);
        }
        catch (Exception ex){
            errorMsg= "基板强制投入的通知异常"+ex.getMessage();
            transResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
