package com.api.dcs.project.fjrm.api;

import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 库存公共方法
 * 1.根据废料框编码查询废料框信息
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025-4-9
 */
@Service
public class DcsFjrmWasteBoxCommonFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;

    //1.根据废料框编码查询废料框信息
    public Map<String, Object> GetWasteBoxInfoByWasteBoxCode(String waste_box_code) throws Exception{
        Map<String, Object> mapItem=null;
        String sqlModelSel="select waste_box_id, " +
                "COALESCE(waste_box_code,'') waste_box_code," +
                "COALESCE(waste_box_des,'') waste_box_des," +
                "COALESCE(waste_box_width,0) waste_box_width, " +
                "COALESCE(waste_box_height,0) waste_box_height " +
                "from b_dcs_fmod_waste_box " +
                "where enable_flag='Y' "+
                "and waste_box_code='"+waste_box_code+"' ";
        List<Map<String, Object>> itemListBox=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlModelSel,
                false,null,"");
        if(itemListBox!=null && itemListBox.size()>0){
            mapItem=itemListBox.get(0);
        }
        return mapItem;
    }

}
