package com.api.eap.project.cj;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.core.share.PlanCommonFunc;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 * 放板机生产计划处理逻辑
 * 1.
 * 2.
 * 3.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-10-30
 */
@RestController
@Slf4j
@RequestMapping("/eap/main")
public class EapCjMasterPlanController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private PlanCommonFunc planCommonFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //判断是否存在任务
    @RequestMapping(value = "/EapMainPlanExistJudge", method = {RequestMethod.POST, RequestMethod.GET})
    public String zbxc(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/main/EapMainPlanExistJudge";
        String selectResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String station_attr = jsonParas.getString("station_attr");
            long station_id_long = Long.parseLong(station_id);
            String group_id = "";
            List<Map<String, Object>> itemList = new ArrayList<>();
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            String group_lot_status = "PLAN";
            if (jsonParas.containsKey("in_or_out")) {
                group_lot_status = "out".equals(jsonParas.getString("in_or_out")) ? "WORK" : "PLAN";
            }
            queryBigData.addCriteria(Criteria.where("group_lot_status").is(group_lot_status));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            List<Document> documents = mongoTemplate.find(queryBigData, Document.class, apsPlanTable);
            List<Map<String, Object>> collect = documents.stream().map(m -> {
                Map<String, Object> resultMap = new HashMap<String, Object>();
                resultMap.putAll(m);
                return resultMap;
            }).collect(Collectors.toList());
            String carryStatus = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr, "Eap", "EapStatus", "CarryStatus" + Integer.parseInt(port_code));
            if (CollectionUtils.isEmpty(collect)) {
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WAIT"));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                List<Document> waitDocuments = mongoTemplate.find(queryBigData, Document.class, apsPlanTable);
                carryStatus = CollectionUtils.isEmpty(waitDocuments) ? result : carryStatus;
                if (!StringUtils.isEmpty(carryStatus)) {
                    JSONObject json = new JSONObject();
                    json.put("carry_status", carryStatus);
                    Document document = waitDocuments.stream().findFirst().get();
                    Integer plan_lot_count = document.getInteger("plan_lot_count");
                    String lot_num = document.getString("lot_num");
                    JSONArray jsonArray = new JSONArray();
                    JSONObject lotInfo = new JSONObject();
                    lotInfo.put("lot_count", plan_lot_count);
                    lotInfo.put("lot_id", lot_num);
                    jsonArray.add(lotInfo);
                    json.put("lot_info", jsonArray);
                    result = json.toJSONString();
                }
            } else {
                Document document = documents.stream().findFirst().get();
                Integer plan_lot_count = document.getInteger("plan_lot_count");
                JSONObject json = new JSONObject();
                json.put("carry_status", carryStatus);
                String lot_num = document.getString("lot_num");
                JSONArray jsonArray = new JSONArray();
                JSONObject lotInfo = new JSONObject();
                lotInfo.put("lot_count", plan_lot_count);
                lotInfo.put("lot_id", lot_num);
                jsonArray.add(lotInfo);
                json.put("lot_info", jsonArray);
                result = json.toJSONString();
            }
            selectResult = CFuncUtilsLayUiResut.GetStandJson(true, collect, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "判断是否存在放板任务异常" + ex.getMessage();
            selectResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //修改任务状态
    @RequestMapping(value = "/ChangePlanStatus", method = {RequestMethod.POST, RequestMethod.GET})
    public String ChangePlanStatus(@RequestBody JSONObject jsonParas, HttpServletRequest request) {
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            Long stationId = jsonParas.getLong("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            Query query = new Query();
            query.addCriteria(Criteria.where("station_id").is(stationId));
            query.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            query.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "WORK");
            mongoTemplate.updateMulti(query, updateBigData, apsPlanTable);
            return CFuncUtilsLayUiResut.GetStandJson(true, null, "", "", 0);
        } catch (Exception e) {
            errorMsg = "参数错误" + e.getMessage();
            return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
    }

    //变更板件状态
    @RequestMapping(value = "/EapCoreMainPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreMainPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/main/EapCoreLoadPlanPanelCheckAndSave";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        String mePanelQueueTable = "a_eap_me_main_panel_queue";
        JSONObject result = new JSONObject();
        //获取板件队列，以时间升序
        try {
            Long station_id = jsonParas.getLong("station_id");
            String in_or_out = jsonParas.getString("in_or_out");
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                return CFuncUtilsLayUiResut.GetErrorJson("查询不到当前可用订单任务");
            }
            Document docItemBigData = iteratorBigData.next();
            iteratorBigData.close();
            String lot_num = docItemBigData.getString("lot_num");
            String plan_id = docItemBigData.getString("plan_id");
            String task_from = docItemBigData.getString("task_from");
            String group_lot_num = docItemBigData.getString("group_lot_num");
            String lot_short_num = docItemBigData.getString("lot_short_num");
            Integer lot_index = docItemBigData.getInteger("lot_index");
            String port_code = docItemBigData.getString("port_code");
            //任务计划数量
            Integer plan_lot_count = docItemBigData.getInteger("plan_lot_count");
            //变更任务完成数量
            Integer finish_count = docItemBigData.getInteger(in_or_out + "_finish_count");
            Integer finish_ok_count = docItemBigData.getInteger(in_or_out + "_finish_ok_count");
            Integer panel_index = 0;
            //查询板件队列
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where(in_or_out + "_use_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "item_date_val"));
            List<JSONObject> panelQueue = mongoTemplate.find(queryBigData, JSONObject.class, mePanelQueueTable);
            if (CollectionUtils.isEmpty(panelQueue)) {
                //计划数量
                result.put("plan_lot_count", plan_lot_count);
                //ok数量
                result.put("finish_ok_count", finish_ok_count);
                //完板数量
                result.put("finish_count", finish_count);
                return CFuncUtilsLayUiResut.GetStandJson(true, null, result.toJSONString(), "", 0);
            }
            JSONObject jsonObject = panelQueue.stream().findFirst().get();
            String panel_barcode = jsonObject.getString("panel_barcode");
            //查询板件履历
            Query countQuery = new Query();
            countQuery.addCriteria(Criteria.where("station_id").is(station_id));
            countQuery.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            countQuery.addCriteria(Criteria.where("plan_id").in(plan_id));
            countQuery.addCriteria(Criteria.where("in_or_out").in(in_or_out));
            Long stationFlowCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(countQuery.getQueryObject());
            panel_index = stationFlowCount.intValue() + 1;
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where(in_or_out + "_use_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
            Update update = new Update();
            update.set(in_or_out + "_use_flag", "Y");
            update.set(in_or_out + "_use_date", CFuncUtilsSystem.GetMongoDataValue(CFuncUtilsSystem.GetMongoISODate("")));
            //将板件队列中的标记进行变更
            mongoTemplate.updateMulti(queryBigData, update, mePanelQueueTable);
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", "OK");
            mapBigDataRow.put("in_or_out", in_or_out);
            mapBigDataRow.put("dummy_flag", "N");
            mapBigDataRow.put("panel_flag", "OK");
            mapBigDataRow.put("offline_flag", "N");
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新任务数量
            finish_count += 1;
            finish_ok_count += 1;
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            Update apsPlanUpdate = new Update();
            apsPlanUpdate.set(in_or_out + "_finish_ok_count", finish_count);
            apsPlanUpdate.set(in_or_out + "_finish_count", finish_count);
            if ("in".equals(in_or_out) && docItemBigData.getInteger(in_or_out + "_finish_count") == 0) {
                apsPlanUpdate.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            //将板件队列中的标记进行变更
            mongoTemplate.updateMulti(queryBigData, apsPlanUpdate, apsPlanTable);
            //返回计划数量以及当前数量
            //计划数量
            result.put("plan_lot_count", plan_lot_count);
            //ok数量
            result.put("finish_ok_count", finish_ok_count);
            result.put("panel_barcode", panel_barcode);
            result.put("panel_index", panel_index);
            //完板数量
            result.put("finish_count", finish_count);
            return CFuncUtilsLayUiResut.GetStandJson(true, null, result.toJSONString(), "", 0);
        } catch (Exception e) {
            errorMsg = "参数错误" + e.getMessage();
            return CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
    }

    //放板机Panel校验
    @RequestMapping(value = "/EapCoreLoadPlanPanelCheckAndSave", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapCoreLoadPlanPanelCheckAndSave(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/core/load/EapCoreLoadPlanPanelCheckAndSave";
        String transResult = "";
        String errorMsg = "";
        String meUserTable = "a_eap_me_station_user";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanDTable = "a_eap_aps_plan_d";
        String meStationFlowTable = "a_eap_me_station_flow";
        String result = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String group_lot_num = jsonParas.getString("group_lot_num");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String tray_barcode = jsonParas.getString("tray_barcode");//tray盘码
            String ng_auto_pass_value = jsonParas.getString("ng_auto_pass_value");//NG自动越过标识,1为启用
            String ng_manual_pass_value = jsonParas.getString("ng_manual_pass_value");//1为手工越过
            String panel_model_value = jsonParas.getString("panel_model_value");//有无panel模式,1为有panel模式
            String onecar_multybatch_value = jsonParas.getString("one_car_multybatch_value");//一车多批多模式,1为一车多批,2为一批多车
            String onecar_multybatch_check_value = jsonParas.getString("onecar_multybatch_check_value");//一车多批模式时,校验方式:1按批次顺序判断、2不按顺序判断
            String manual_judge_code = jsonParas.getString("manual_judge_code");//是否为人工干预,1人工输入模式，2重读、3强制越过
            String inspect_flag = jsonParas.getString("inspect_flag");//是否为首检模式,Y为是
            String dummy_flag = jsonParas.getString("dummy_flag");//是否为Dummy板
            String eap_flag = jsonParas.getString("eap_flag");//是否为EAP判定结果,若为EAP判断,则完全通过EAP判定CODE处理
            String eap_ng_code = jsonParas.getString("eap_ng_code");//EAP判定结果代码
            String face_code2 = jsonParas.getString("face_code");//存储A面B面,1:A,2:B

            //防止null数据
            String panel_flag = "N";
            Integer panel_ng_code = 0;
            String panel_ng_msg = "";
            String panel_status = "OK";
            String user_name = "";
            if (group_lot_num == null) group_lot_num = "";
            if (panel_barcode == null) panel_barcode = "";
            if (panel_barcode.equals("FFFFFFFF")) panel_barcode = "NoRead";
            if (tray_barcode == null) tray_barcode = "";
            if (ng_auto_pass_value == null) ng_auto_pass_value = "";
            if (ng_manual_pass_value == null) ng_manual_pass_value = "";
            if (panel_model_value == null) panel_model_value = "";
            if (panel_model_value.equals("1")) panel_flag = "Y";
            if (onecar_multybatch_value == null) onecar_multybatch_value = "";
            if (onecar_multybatch_check_value == null) onecar_multybatch_check_value = "";
            if (manual_judge_code == null || manual_judge_code.equals("")) manual_judge_code = "0";
            if (inspect_flag == null || inspect_flag.equals("")) inspect_flag = "N";
            if (dummy_flag == null || dummy_flag.equals("")) dummy_flag = "N";
            if (eap_flag == null || eap_flag.equals("")) eap_flag = "N";
            if (eap_ng_code == null || eap_ng_code.equals("")) eap_ng_code = "0";//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
            if (face_code2 == null || face_code2.equals("")) face_code2 = "0";
            Integer face_code2_int = Integer.parseInt(face_code2);

            long station_id_long = Long.parseLong(station_id);

            //1.获取当前用户信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("checkout_flag").is("N"));
            queryBigData.with(Sort.by(Sort.Direction.DESC, "item_date_val"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(meUserTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                user_name = docItemBigData.getString("user_name");
                iteratorBigData.close();
            }

            //1.判断是否存在PLAN状态下group_lot_num,若存在则进行更新为WORK状态
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("PLAN"));
            Update updateBigData = new Update();
            updateBigData.set("group_lot_status", "WORK");
            mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);

            //查询该工单下全部的plan_id
            List<String> lstPlanId = new ArrayList<>();
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(5).iterator();
            while (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lstPlanId.add(docItemBigData.getString("plan_id"));
            }
            if (iteratorBigData.hasNext()) iteratorBigData.close();

            //2.获取当前计划中或者执行中的子任务
            String plan_id = "";
            String task_from = "";
            String lot_num = "";
            String lot_short_num = "";
            Integer lot_index = 1;
            String port_code = "";
            String material_code = "";
            String pallet_num = "";
            String pallet_type = "";
            String lot_level = "";
            Integer fp_index = 0;
            Double panel_length = 0d;
            Double panel_width = 0d;
            Double panel_tickness = 0d;
            Integer plan_lot_count = 0;
            Integer finish_count = 0;
            Integer finish_ok_count = 0;
            Integer finish_ng_count = 0;
            Integer face_code = 0;
            Integer pallet_use_count = 0;
            Integer inspect_finish_count = 0;
            Integer panel_index = 0;
            String old_plan_status = "";

            String[] lot_status_list = new String[]{"PLAN", "WORK"};
            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
            queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
            queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
            queryBigData.addCriteria(Criteria.where("lot_status").in(lot_status_list));
            queryBigData.with(Sort.by(Sort.Direction.ASC, "lot_index"));
            iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (!iteratorBigData.hasNext()) {
                //判断是否得到的计划无,说明有可能提前完成了,需要查找最后一个FINISH任务
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                queryBigData.addCriteria(Criteria.where("lot_status").is("FINISH"));
                queryBigData.with(Sort.by(Sort.Direction.DESC, "lot_index"));
                iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            }
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                plan_id = docItemBigData.getString("plan_id");
                task_from = docItemBigData.getString("task_from");
                lot_num = docItemBigData.getString("lot_num");
                lot_short_num = docItemBigData.getString("lot_short_num");
                lot_index = docItemBigData.getInteger("lot_index");
                port_code = docItemBigData.getString("port_code");
                material_code = docItemBigData.getString("material_code");
                pallet_num = docItemBigData.getString("pallet_num");
                pallet_type = docItemBigData.getString("pallet_type");
                lot_level = docItemBigData.getString("lot_level");
                panel_length = docItemBigData.getDouble("panel_length");
                panel_width = docItemBigData.getDouble("panel_width");
                panel_tickness = docItemBigData.getDouble("panel_tickness");
                plan_lot_count = docItemBigData.getInteger("plan_lot_count");
                finish_count = docItemBigData.getInteger("finish_count");
                finish_ok_count = docItemBigData.getInteger("finish_ok_count");
                finish_ng_count = docItemBigData.getInteger("finish_ng_count");
                face_code = docItemBigData.getInteger("face_code");
                pallet_use_count = docItemBigData.getInteger("pallet_use_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                old_plan_status = docItemBigData.getString("lot_status");
                iteratorBigData.close();
            }

            if (face_code2_int > 0) face_code = face_code2_int;//若有传递值,则用传递值

            //未找到任务报错
            if (plan_id.equals("")) {
                errorMsg = "未找到生产任务,请先导入生产任务";
                transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return transResult;
            }

            queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            panel_index = finish_count + 1;
            Date item_date = CFuncUtilsSystem.GetMongoISODate("");
            long item_date_val = CFuncUtilsSystem.GetMongoDataValue(item_date);
            String station_flow_id = CFuncUtilsSystem.CreateUUID(true);
            Map<String, Object> mapBigDataRow = new HashMap<>();
            mapBigDataRow.put("item_date", item_date);
            mapBigDataRow.put("item_date_val", item_date_val);
            mapBigDataRow.put("station_flow_id", station_flow_id);
            mapBigDataRow.put("station_id", station_id_long);
            mapBigDataRow.put("plan_id", plan_id);
            mapBigDataRow.put("task_from", task_from);
            mapBigDataRow.put("group_lot_num", group_lot_num);
            mapBigDataRow.put("lot_num", lot_num);
            mapBigDataRow.put("lot_short_num", lot_short_num);
            mapBigDataRow.put("lot_index", lot_index);
            mapBigDataRow.put("port_code", port_code);
            mapBigDataRow.put("material_code", material_code);
            mapBigDataRow.put("pallet_num", pallet_num);
            mapBigDataRow.put("pallet_type", pallet_type);
            mapBigDataRow.put("lot_level", lot_level);
            mapBigDataRow.put("fp_index", fp_index);
            mapBigDataRow.put("panel_barcode", panel_barcode);
            mapBigDataRow.put("panel_length", panel_length);
            mapBigDataRow.put("panel_width", panel_width);
            mapBigDataRow.put("panel_tickness", panel_tickness);
            mapBigDataRow.put("panel_index", panel_index);
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            mapBigDataRow.put("inspect_flag", inspect_flag);
            mapBigDataRow.put("dummy_flag", dummy_flag);
            mapBigDataRow.put("manual_judge_code", manual_judge_code);
            mapBigDataRow.put("panel_flag", panel_flag);
            mapBigDataRow.put("user_name", user_name);
            mapBigDataRow.put("eap_flag", eap_flag);
            mapBigDataRow.put("tray_barcode", tray_barcode);
            mapBigDataRow.put("face_code", face_code);
            mapBigDataRow.put("offline_flag", "N");

            //4.若为Dummy板则直接先存储
            if (dummy_flag.equals("Y")) {
                result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," + panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + ",WORK";//过站ID+批次号+载具号+排序+条码+错误代码+首检数量+tray码+子批状态
                mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
                return transResult;
            }
            //5.是否为EAP判断,若为EAP判断则按照EAP判断结果来定义
            if (eap_flag.equals("Y")) {
                panel_ng_code = Integer.parseInt(eap_ng_code);
                if (panel_ng_code != 0) {
                    panel_status = "NG";
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
                if ((ng_auto_pass_value.equals("1") || ng_manual_pass_value.equals("1")) && panel_status.equals("NG")) {
                    panel_status = "NG_PASS";
                    panel_ng_code = 4;
                    panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                }
            }
            //6.是否为AIS判断则需要判断混板逻辑
            else {
                //6.1 有panel模式
                if (panel_model_value.equals("1")) {
                    if (ng_manual_pass_value.equals("1")) {
                        panel_status = "NG_PASS";
                        panel_ng_code = 4;
                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                    } else {
                        if (panel_barcode.equals("NoRead") || panel_barcode.equals("")) {
                            panel_status = "NG";
                            panel_ng_code = 2;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                        } else {
                            long pnListCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigData.getQueryObject());
                            if (pnListCount > 0) {
                                Query queryBigDataD = new Query();
                                queryBigDataD.addCriteria(Criteria.where("plan_id").is(plan_id));
                                queryBigDataD.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                long okCount = mongoTemplate.getCollection(apsPlanDTable).countDocuments(queryBigDataD.getQueryObject());
                                if (okCount <= 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            } else {
                                if (!lot_short_num.equals("")) {//采用简码判断
                                    if (!panel_barcode.contains(lot_short_num)) {
                                        if (ng_auto_pass_value.equals("1")) {
                                            panel_status = "NG_PASS";
                                            panel_ng_code = 4;
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        } else {
                                            panel_status = "NG";
                                            panel_ng_code = 1;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                            panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                        }
                                    }
                                }
                            }
                            //判断是否母批下条码重码
                            if (panel_status.equals("OK")) {
                                Query queryBigDataFlow = new Query();
                                queryBigDataFlow.addCriteria(Criteria.where("station_id").is(station_id_long));
                                queryBigDataFlow.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_status").is("OK"));
                                queryBigDataFlow.addCriteria(Criteria.where("panel_barcode").is(panel_barcode));
                                queryBigDataFlow.addCriteria(Criteria.where("plan_id").in(lstPlanId));
                                long panelCount = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryBigDataFlow.getQueryObject());
                                if (panelCount > 0) {
                                    if (ng_auto_pass_value.equals("1")) {
                                        panel_status = "NG_PASS";
                                        panel_ng_code = 4;
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    } else {
                                        panel_status = "NG";
                                        panel_ng_code = 3;//0为正常,1为混板,2为读码失败板,3重码,4强制越过,5其他定义
                                        panel_ng_msg = planCommonFunc.getPanelNgMsg(panel_ng_code);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            //更新数据
            mapBigDataRow.put("panel_status", panel_status);
            mapBigDataRow.put("panel_ng_code", panel_ng_code);
            mapBigDataRow.put("panel_ng_msg", panel_ng_msg);
            //插入到过站数据
            mongoTemplate.insert(mapBigDataRow, meStationFlowTable);
            //更新完工数量
            finish_count++;
            if (panel_status.equals("NG")) finish_ng_count++;
            else finish_ok_count++;
            String lot_status = "WORK";
            if (finish_count >= plan_lot_count) lot_status = "FINISH";
            String lot_status2 = lot_status;
            if (old_plan_status.equals("FINISH")) lot_status2 = "FINISH2";
            updateBigData = new Update();
            updateBigData.set("lot_status", lot_status);
            updateBigData.set("finish_count", finish_count);
            updateBigData.set("finish_ok_count", finish_ok_count);
            updateBigData.set("finish_ng_count", finish_ng_count);
            if (finish_count == 1) {
                updateBigData.set("task_start_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            if (lot_status.equals("FINISH")) {
                updateBigData.set("task_end_time", CFuncUtilsSystem.GetNowDateTime(""));
            }
            mongoTemplate.updateFirst(queryBigData, updateBigData, apsPlanTable);
            //更新首检数量
            if (inspect_flag.equals("Y")) {//首检必须要全部合格件,否则再次从0开始
                if (panel_status.equals("OK") || panel_status.equals("NG_PASS")) inspect_finish_count++;
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id_long));
                queryBigData.addCriteria(Criteria.where("group_lot_num").is(group_lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").is("WORK"));
                updateBigData = new Update();
                updateBigData.set("inspect_finish_count", inspect_finish_count);
                mongoTemplate.updateMulti(queryBigData, updateBigData, apsPlanTable);
            }
            //返回数据
            result = station_flow_id + "," + lot_num + "," + pallet_num + "," + panel_index + "," + panel_barcode + "," + panel_ng_code + "," + inspect_finish_count + "," + tray_barcode + "," + lot_status2;//过站ID+批次号+载具号+排序+条码+错误代码+首检完成数量+tray码+子批状态
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, result, "", 0);
        } catch (Exception ex) {
            errorMsg = "异常" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

}
