
package com.api.eap.project.dy.p1.wcf;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>paramVerifyResponseItem complex type的 Java 类。
 * 
 * <p>以下模式片段指定包含在此类中的预期内容。
 * 
 * <pre>
 * &lt;complexType name="paramVerifyResponseItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="response_head" type="{http://tempuri.org/}responseHead" minOccurs="0"/&gt;
 *         &lt;element name="response_body" type="{http://tempuri.org/}paramVerifyResponseBody" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "paramVerifyResponseItem", propOrder = {
    "responseHead",
    "responseBody"
})
public class ParamVerifyResponseItem {

    @XmlElement(name = "response_head")
    protected ResponseHead responseHead;
    @XmlElement(name = "response_body")
    protected ParamVerifyResponseBody responseBody;

    /**
     * 获取responseHead属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ResponseHead }
     *     
     */
    public ResponseHead getResponseHead() {
        return responseHead;
    }

    /**
     * 设置responseHead属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ResponseHead }
     *     
     */
    public void setResponseHead(ResponseHead value) {
        this.responseHead = value;
    }

    /**
     * 获取responseBody属性的值。
     * 
     * @return
     *     possible object is
     *     {@link ParamVerifyResponseBody }
     *     
     */
    public ParamVerifyResponseBody getResponseBody() {
        return responseBody;
    }

    /**
     * 设置responseBody属性的值。
     * 
     * @param value
     *     allowed object is
     *     {@link ParamVerifyResponseBody }
     *     
     */
    public void setResponseBody(ParamVerifyResponseBody value) {
        this.responseBody = value;
    }

}
