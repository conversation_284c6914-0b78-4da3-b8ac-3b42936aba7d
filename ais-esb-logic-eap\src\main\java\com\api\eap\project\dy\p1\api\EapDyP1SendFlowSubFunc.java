package com.api.eap.project.dy.p1.api;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsRest;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.api.eap.project.dy.EapDyInterfCommon;
import com.api.eap.project.dy.p1.wcf.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.util.*;

/**
 * <p>
 * 定颖EAP流程接口(Sub)公共方法
 * 8.PanelDataUploadReport:[接口]每片Panel收放台車事件上報
 * 9.WIPTrackingReport:[接口]每一lot完板上报
 * </p>
 *
 * <AUTHOR>
 * @since 2023-09-11
 */
@Service
@Slf4j
public class EapDyP1SendFlowSubFunc {
    @Autowired
    private CFuncUtilsRest cFuncUtilsRest;
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private EapDyInterfCommon eapDyInterfCommon;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapDyP1InterfCommon eapDyP1InterfCommon;


    //8.[接口]每片Panel收放台車事件上報
    public JSONObject PanelDataUploadReport(Long station_id, String station_code, String panel_id, String slot_no,
                                            JSONArray item_attr_list, String offline_flag, String local_flag,
                                            String ccd_no, String lot_num, String prod_id, String prod_version,
                                            String process_code, JSONObject attr_else,
                                            String holding_time, String over_times, Integer hold_result) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="PanelDataUploadReport";
        String esbInterfCode="PanelDataUploadReport";
        String token="";
        String requestParas="";
        String responseParas="";
        JSONObject postParas=new JSONObject();
        JSONObject request_body=new JSONObject();
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);

            //创建实例
            com.api.eap.project.dy.p1.wcf.DyService service=new com.api.eap.project.dy.p1.wcf.DyService();
            DyServiceSoap serviceSoap= service.getDyServiceSoap();

            //1.创建参数
            String panel_id2=panel_id;
            if(panel_id2==null || panel_id2.equals("") || panel_id2.equals("FFFFFFFF")) panel_id2="NoRead";
            String keep_reason="0";
            if(offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";

            postParas = new JSONObject();
            RequestHead requestHead=eapDyP1InterfCommon.CreateRequestHeader02(funcName, station_code);
            PanelDataUploadReportRequestBody requestBody=new PanelDataUploadReportRequestBody();
            requestBody.setReportDt(CFuncUtilsSystem.GetNowDateTime(""));
            requestBody.setKeepReason(keep_reason);//0直接上传、1离线、2超时
            requestBody.setPanelId(panel_id2);
            requestBody.setSlotNo(slot_no);
            PanelDataUploadReportRequestEDC panelDataUploadReportRequestEDC=new PanelDataUploadReportRequestEDC();
            List<Item> itemListEdc=new ArrayList<>();
            for(int i=0;i<item_attr_list.size();i++){
                JSONObject jbItem=item_attr_list.getJSONObject(i);
                String item_id=jbItem.getString("item_id");
                String item_value=jbItem.getString("item_value");
                Item item=new Item();
                item.setItemId(item_id);
                item.setItemValue(item_value);
                itemListEdc.add(item);
            }
            ArrayOfItem arrayOfItem=new ArrayOfItem();
            arrayOfItem.setItem(itemListEdc);
            panelDataUploadReportRequestEDC.setEdc(arrayOfItem);
            requestBody.setEdcInfos(panelDataUploadReportRequestEDC);
            JSONObject request_head=(JSONObject)JSONObject.toJSON(requestHead);
            request_body=(JSONObject)JSONObject.toJSON(requestBody);
            postParas.put("request_head",request_head);
            postParas.put("request_body",request_body);
            requestParas = postParas.toString();

            //必须确保先进先出
            String offLineTable = "a_eap_me_interf_offline";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("up_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("function_name").is(esbInterfCode));
            long offCount = mongoTemplate.getCollection(offLineTable).countDocuments(queryBigData.getQueryObject());
            if(offCount>0){
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "储存排队数据成功");
                return jbResult;
            }

            //2.若为离线,则放入离线上报
            Boolean isOffSave=false;
            if(offline_flag.equals("Y") || local_flag.equals("Y")) isOffSave=true;
            if(isOffSave){//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id,esbInterfCode,postParas);
                jbResult.put("isSaveFlag",false);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message","存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            PanelDataUploadReportResponseItem panelDataUploadReportResponseItem =serviceSoap.panelDataUploadReport(requestHead,requestBody);
            JSONObject jsonObjectBack=(JSONObject)JSONObject.toJSON(panelDataUploadReportResponseItem);
            responseParas = jsonObjectBack.toString();
            eapDyP1InterfCommon.CheckResponseHeader(panelDataUploadReportResponseItem.getResponseHead());
            JSONObject response_body=new JSONObject();

            //成功
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("responseBody",response_body);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送成功");
        }
        catch (Exception ex){
            if (ex.getMessage().indexOf("timed out")!=-1) {
                request_body.put("keepReason", "2");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            else {
                request_body.put("keepReason", "1");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            errorMsg="发生异常:"+ex.getMessage();
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //9.[接口]每一lot完板上报
    public JSONObject WIPTrackingReport(Long station_id,String station_code,String user_name,String dept_id,String shift_id,
                                        String start_date,String end_date,String lot_num,int task_count,int lot_count,
                                        int lot_finish_count,String material_code,String lot_short_num,String lot_version,
                                        String prod_mode,String attribute1,String attribute2,
                                        JSONArray item_attr_list,String offline_flag,String local_flag,
                                        JSONObject attr_else) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="WIPTrackingReport";
        String esbInterfCode="WIPTrackingReport";
        String token="";
        String requestParas="";
        String responseParas="";
        JSONObject postParas=new JSONObject();
        JSONObject request_body=new JSONObject();
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);

            //创建实例
            com.api.eap.project.dy.p1.wcf.DyService service=new com.api.eap.project.dy.p1.wcf.DyService();
            DyServiceSoap serviceSoap= service.getDyServiceSoap();

            //1.创建参数
            String keep_reason="0";
            if(offline_flag.equals("Y") || local_flag.equals("Y")) keep_reason = "1";

            postParas = new JSONObject();
            RequestHead requestHead=eapDyP1InterfCommon.CreateRequestHeader(funcName, station_code);
            WipTrackingReportRequestBody requestBody=new WipTrackingReportRequestBody();
            GregorianCalendar cal = new GregorianCalendar();
            XMLGregorianCalendar date1 = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
            requestBody.setReportDt(date1);
            requestBody.setKeepReason(keep_reason);//0直接上传、1离线、2超时
            requestBody.setLotId(lot_num);
            EdcInfos edcInfos=new EdcInfos();
            List<Item> itemListEdc=new ArrayList<>();
            ArrayOfItem arrayOfItem=new ArrayOfItem();
            //共用
            Item item=new Item();
            item.setItemId("REPORT_DATE");
            item.setItemValue(CFuncUtilsSystem.GetNowDateTime("yyyy-MM-dd"));
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("PROD_ID");
            item.setItemValue(material_code);
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("OUTPUT_QTY");
            item.setItemValue(String.valueOf(lot_count));
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("START_DT");
            item.setItemValue(start_date);
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("END_DT");
            item.setItemValue(end_date);
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("USER_ID");
            item.setItemValue(user_name);
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("DEPT_ID");
            item.setItemValue(dept_id);
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("SHIFT_ID");
            item.setItemValue(shift_id);
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("T001");
            item.setItemValue(String.valueOf(lot_finish_count));
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("T002");
            item.setItemValue(lot_version);
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("T003");
            item.setItemValue(attribute1);
            itemListEdc.add(item);
            item=new Item();
            item.setItemId("T004");
            item.setItemValue(attribute2);
            itemListEdc.add(item);
            for(int i=0;i<item_attr_list.size();i++){
                JSONObject jbItem=item_attr_list.getJSONObject(i);
                String item_id=jbItem.getString("item_id");
                String item_value=jbItem.getString("item_value");
                if(item_id.equals("S003")){
                    item=new Item();
                    item.setItemId("T005");
                    item.setItemValue(item_value);
                    itemListEdc.add(item);
                }
                else if(item_id.equals("S004")){
                    item=new Item();
                    item.setItemId("T006");
                    item.setItemValue(item_value);
                    itemListEdc.add(item);
                }
                else if(item_id.equals("S005")){
                    item=new Item();
                    item.setItemId("T007");
                    item.setItemValue(item_value);
                    itemListEdc.add(item);
                }
            }
            arrayOfItem.setItem(itemListEdc);
            edcInfos.setEdc(arrayOfItem);
            requestBody.setEdcInfos(edcInfos);
            JSONObject request_head=(JSONObject)JSONObject.toJSON(requestHead);
            request_body=(JSONObject)JSONObject.toJSON(requestBody);
            postParas.put("request_head",request_head);
            postParas.put("request_body",request_body);
            requestParas = postParas.toString();

            //必须确保先进先出
            String offLineTable = "a_eap_me_interf_offline";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("up_flag").is("N"));
            queryBigData.addCriteria(Criteria.where("function_name").is(esbInterfCode));
            long offCount = mongoTemplate.getCollection(offLineTable).countDocuments(queryBigData.getQueryObject());
            if(offCount>0){
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
                jbResult.put("isSaveFlag", false);
                jbResult.put("requestParas", requestParas);
                jbResult.put("responseParas", responseParas);
                jbResult.put("successFlag", true);
                jbResult.put("message", "储存排队数据成功");
                return jbResult;
            }

            //2.若为离线,则放入离线上报
            Boolean isOffSave=false;
            if(offline_flag.equals("Y") || local_flag.equals("Y")) isOffSave=true;
            if(isOffSave){//离线模式下不直接上报到EAP
                opCommonFunc.SaveUnFinishInterfData(station_id,esbInterfCode,postParas);
                jbResult.put("isSaveFlag",false);
                jbResult.put("requestParas",requestParas);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",true);
                jbResult.put("message","存储离线数据成功");
                return jbResult;
            }

            //3.根据接口查询url
            String sqlInterf="select COALESCE(esb_prod_intef_url,'') esb_prod_intef_url " +
                    "from sys_core_esb_interf " +
                    "where esb_interf_code='"+esbInterfCode+"' and enable_flag='Y' LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemList=cFuncDbSqlExecute.ExecSelectSql("AIS",sqlInterf,false,null,"");
            if(itemList==null || itemList.size()<=0){
                errorMsg="接口表中未配置接口代码为{"+esbInterfCode+"}基础数据";
                throw new Exception(errorMsg);
            }
            String esb_prod_intef_url=itemList.get(0).get("esb_prod_intef_url").toString();
            if(esb_prod_intef_url==null || esb_prod_intef_url.equals("")){
                errorMsg="接口表中配置接口代码为{"+esbInterfCode+"}基础数据PROD-URL为空";
                throw new Exception(errorMsg);
            }

            //3.发送数据
            Response wipTrackingReportResponseItem =serviceSoap.wipTrackingReport(requestHead,requestBody);
            JSONObject jsonObjectBack=(JSONObject)JSONObject.toJSON(wipTrackingReportResponseItem);
            responseParas = jsonObjectBack.toString();
            eapDyP1InterfCommon.CheckResponseHeader(wipTrackingReportResponseItem.getResponseHead());
            JSONObject response_body=new JSONObject();

            //成功
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("responseBody",response_body);
            jbResult.put("successFlag",true);
            jbResult.put("message","发送成功");
        }
        catch (Exception ex){
            if (ex.getMessage().indexOf("timed out")!=-1) {
                request_body.put("keepReason", "2");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            else {
                request_body.put("keepReason", "1");
                postParas.put("request_body", request_body);
                opCommonFunc.SaveUnFinishInterfData(station_id, esbInterfCode, postParas);
            }
            errorMsg="发生异常:"+ex.getMessage();
            jbResult.put("requestParas",requestParas);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
