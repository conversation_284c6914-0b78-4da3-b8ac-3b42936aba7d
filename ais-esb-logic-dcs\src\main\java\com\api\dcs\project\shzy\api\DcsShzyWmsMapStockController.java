package com.api.dcs.project.shzy.api;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.mongodb.client.MongoCursor;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 上海中冶WMS
 * 1.获取分拣入库任务
 * 2.获取分拣入库库位信息
 * 3.判断分拣入库任务是否已经完成
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-9
 */
@RestController
@Slf4j
@RequestMapping("/dcs/project/shzy/api/stock")
public class DcsShzyWmsMapStockController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private DcsShzyWmsMapStockFunc dcsShzyWmsMapStockFunc;

    //1.获取分拣入库任务
    @RequestMapping(value = "/DcsShzyWmsGetFjStockInTask", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsShzyWmsGetFjStockInTask(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/shzy/api/stock/DcsShzyWmsGetFjStockInTask";
        String selectResult="";
        String errorMsg="";
        String wmsFjTaskTable = "b_dcs_wms_fj_task";
        try{
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("task_status").is("PLAN"));
            queryBigData.with(Sort.by(Sort.Direction.ASC,"_id"));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(wmsFjTaskTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            List<Map<String, Object>> itemList=new ArrayList<>();
            Map<String, Object> mapItem=new HashMap<>();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                mapItem.put("task_id",docItemBigData.getString("fj_task_id"));
                mapItem.put("task_num",docItemBigData.getString("task_num"));
                mapItem.put("material_code",docItemBigData.getString("material_code"));
                mapItem.put("project_code",docItemBigData.getString("project_code"));
                mapItem.put("material_length",docItemBigData.getInteger("material_length"));
                mapItem.put("material_width",docItemBigData.getInteger("material_width"));
                mapItem.put("material_thickness",docItemBigData.getInteger("material_thickness"));
                itemList.add(mapItem);
                iteratorBigData.close();
            }
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "获取分拣入库任务异常:"+ex;
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //2.获取分拣入库库位信息
    @RequestMapping(value = "/DcsShzyWmsGetFjStockInMapCell", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsShzyWmsGetFjStockInMapCell(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/shzy/api/stock/DcsShzyWmsGetFjStockInMapCell";
        String selectResult="";
        String errorMsg="";
        String wmsFjTaskTable = "b_dcs_wms_fj_task";
        try{
            String stock_group_code=jsonParas.getString("stock_group_code");
            String task_id=jsonParas.getString("task_id");
            String task_num="";
            String project_code="";
            String material_code="";
            Integer material_length=0;
            Integer material_width=0;
            Integer material_thickness=0;
            Integer off_set_x=0;
            Integer off_set_y=0;
            String kit_flag="N";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("fj_task_id").is(task_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(wmsFjTaskTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if(iteratorBigData.hasNext()){
                Document docItemBigData = iteratorBigData.next();
                task_num=docItemBigData.getString("task_num");
                project_code=docItemBigData.getString("project_code");
                material_code=docItemBigData.getString("material_code");
                material_length=docItemBigData.getInteger("material_length");
                material_width=docItemBigData.getInteger("material_width");
                material_thickness=docItemBigData.getInteger("material_thickness");
                off_set_x=docItemBigData.getInteger("off_set_x");
                off_set_y=docItemBigData.getInteger("off_set_y");
                kit_flag=docItemBigData.getString("kit_flag");
                iteratorBigData.close();
            }
            else{
                errorMsg="未能根据分拣入库任务ID{"+task_id+"}查询到分拣入库任务";
                selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            //1.查询是否有现成库存进行存储
            String sqlStockGroup="select " +
                    "start_position_z,stock_max_height,dd_way,x_math_type,y_math_type " +
                    "from b_dcs_wms_map_stock_group " +
                    "where stock_group_code='"+stock_group_code+"'";
            List<Map<String, Object>> itemListStockGroup=cFuncDbSqlExecute.ExecSelectSql("WMS", sqlStockGroup,
                    false,request,apiRoutePath);
            if(itemListStockGroup==null || itemListStockGroup.size()<=0){
                errorMsg="未能根据库区{"+stock_group_code+"}查询基础设置信息";
                selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                return selectResult;
            }
            Integer start_position_z=Integer.parseInt(itemListStockGroup.get(0).get("start_position_z").toString());
            Integer stock_max_height=Integer.parseInt(itemListStockGroup.get(0).get("stock_max_height").toString());
            String dd_way=itemListStockGroup.get(0).get("dd_way").toString();
            String sqlStock="";
            String temp_cell_flag="N";
            if(!kit_flag.equals("Y")) temp_cell_flag="Y";
            if(dd_way.equals("MATERIAL")){//按照物料类型
                sqlStock="select " +
                        "a.stock_id,a.stock_code,a.position_x,a.position_y,b.location_z," +
                        "a.start_cell_row,a.end_cell_row,a.start_cell_col,a.end_cell_col " +
                        "from b_dcs_wms_map_me_stock a inner join b_dcs_wms_map_me_stock_d b " +
                        "on a.stock_id=b.stock_id " +
                        "where a.stock_status='NORMAL' and a.full_flag='N' and a.lock_flag='N' " +
                        "and a.stock_group_code='"+stock_group_code+"' " +
                        "and a.temp_flag='"+temp_cell_flag+"' " +
                        "and b.material_code='"+material_code+"' " +
                        "and b.lot_num='"+project_code+"' " +
                        "order by a.start_cell_row,a.start_cell_col,b.stock_index desc " +
                        "LIMIT 1 OFFSET 0";
            }
            else{
                sqlStock="select " +
                        "a.stock_id,a.stock_code,a.position_x,a.position_y,b.location_z," +
                        "a.start_cell_row,a.end_cell_row,a.start_cell_col,a.end_cell_col " +
                        "from b_dcs_wms_map_me_stock a inner join b_dcs_wms_map_me_stock_d b " +
                        "on a.stock_id=b.stock_id " +
                        "where a.stock_status='NORMAL' and a.full_flag='N' and a.lock_flag='N' " +
                        "and a.stock_group_code='"+stock_group_code+"' " +
                        "and a.temp_flag='"+temp_cell_flag+"' " +
                        "and a.m_length="+material_length+" and a.m_width="+material_width+" " +
                        "order by a.start_cell_row,a.start_cell_col,b.stock_index desc " +
                        "LIMIT 1 OFFSET 0";
            }
            Long stock_id=0L;
            String stock_code="";
            Integer position_x=0;
            Integer position_y=0;
            Integer location_z=0;
            Integer start_cell_row=0;
            Integer end_cell_row=0;
            Integer start_cell_col=0;
            Integer end_cell_col=0;
            List<Map<String, Object>> itemListStock=cFuncDbSqlExecute.ExecSelectSql("WMS", sqlStock,
                    false,request,apiRoutePath);
            if(itemListStock!=null && itemListStock.size()>0){
                stock_id=Long.parseLong(itemListStock.get(0).get("stock_id").toString());
                stock_code=itemListStock.get(0).get("stock_code").toString();
                position_x=Integer.parseInt(itemListStock.get(0).get("position_x").toString());
                position_y=Integer.parseInt(itemListStock.get(0).get("position_y").toString());
                location_z=Integer.parseInt(itemListStock.get(0).get("location_z").toString());
                start_cell_row=Integer.parseInt(itemListStock.get(0).get("start_cell_row").toString());
                end_cell_row=Integer.parseInt(itemListStock.get(0).get("end_cell_row").toString());
                start_cell_col=Integer.parseInt(itemListStock.get(0).get("start_cell_col").toString());
                end_cell_col=Integer.parseInt(itemListStock.get(0).get("end_cell_col").toString());
                location_z=location_z+material_thickness;
            }
            else{
                JSONObject jbMapCell=dcsShzyWmsMapStockFunc.GetMapStockCell(stock_group_code,material_length,material_width,temp_cell_flag);
                if(jbMapCell==null){
                    errorMsg="未能根据库区{"+stock_group_code+"}查询到存储库位存储位置";
                    selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
                    return selectResult;
                }
                start_cell_row=jbMapCell.getInteger("start_cell_row");
                end_cell_row=jbMapCell.getInteger("end_cell_row");
                start_cell_col=jbMapCell.getInteger("start_cell_col");
                end_cell_col=jbMapCell.getInteger("end_cell_col");
                stock_code=stock_group_code+"-"+
                        "R"+start_cell_row+"C"+start_cell_col+"-"+
                        "R"+end_cell_row+"C"+end_cell_col;
                position_x=jbMapCell.getInteger("position_x");
                position_y=jbMapCell.getInteger("position_y");
                position_x+=off_set_x;
                position_y+=off_set_y;
                location_z=start_position_z+material_thickness;
            }
            //2.更新任务信息
            Update updateBigData = new Update();
            updateBigData.set("task_status", "WORK");
            updateBigData.set("stock_group_code", stock_group_code);
            updateBigData.set("start_cell_row", start_cell_row);
            updateBigData.set("end_cell_row", end_cell_row);
            updateBigData.set("start_cell_col", start_cell_col);
            updateBigData.set("end_cell_col", end_cell_col);
            updateBigData.set("position_x", position_x);
            updateBigData.set("position_y", position_y);
            updateBigData.set("position_z", location_z);
            updateBigData.set("stock_code", stock_code);
            updateBigData.set("start_date", CFuncUtilsSystem.GetNowDateTime(""));
            updateBigData.set("stock_id", stock_id);
            mongoTemplate.updateFirst(queryBigData, updateBigData, wmsFjTaskTable);
            //3.锁定地图库位单格
            if(stock_id<=0L){
                dcsShzyWmsMapStockFunc.LockMapStockCell("WMS",task_num,stock_group_code,
                        start_cell_row,end_cell_row, start_cell_col,end_cell_col,"Y");
            }
            else{
                //4.锁定库位
                String sqlUpdateStock="update b_dcs_wms_map_me_stock set " +
                        "last_updated_by='WMS'," +
                        "last_update_date='"+ CFuncUtilsSystem.GetNowDateTime("") +"'," +
                        "lock_flag='Y' " +
                        "where stock_id="+stock_id+"";
                cFuncDbSqlExecute.ExecUpdateSql("WMS",sqlUpdateStock,false,request,apiRoutePath);
            }
            //5.合成返回
            List<Map<String, Object>> itemList=new ArrayList<>();
            Map<String, Object> mapItem=new HashMap<>();
            mapItem.put("stock_id",stock_id);
            mapItem.put("stock_code",stock_code);
            mapItem.put("position_x",position_x);
            mapItem.put("position_y",position_y);
            mapItem.put("position_z",location_z);
            mapItem.put("start_cell_row",start_cell_row);
            mapItem.put("end_cell_row",end_cell_row);
            mapItem.put("start_cell_col",start_cell_col);
            mapItem.put("end_cell_col",end_cell_col);
            mapItem.put("temp_cell_flag",temp_cell_flag);
            itemList.add(mapItem);
            selectResult= CFuncUtilsLayUiResut.GetStandJson(true,itemList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "获取分拣入库库位信息异常:"+ex;
            selectResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

    //3.判断分拣入库任务是否已经完成
    @RequestMapping(value = "/DcsShzyWmsIsFjStockInTaskFinish", method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsShzyWmsIsFjStockInTaskFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/project/shzy/api/stock/DcsShzyWmsIsFjStockInTaskFinish";
        String transResult="";
        String errorMsg="";
        String wmsFjTaskTable = "b_dcs_wms_fj_task";
        String finish_flag="N";
        try{
            String task_id=jsonParas.getString("task_id");
            String[] task_status_list=new String[]{"PLAN","WORK"};
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("fj_task_id").is(task_id));
            queryBigData.addCriteria(Criteria.where("task_status").in(task_status_list));
            long taskCount =  mongoTemplate.getCollection(wmsFjTaskTable).countDocuments(queryBigData.getQueryObject());
            if(taskCount<=0) finish_flag="Y";
            transResult= CFuncUtilsLayUiResut.GetStandJson(true,null,finish_flag,"",0);
        }
        catch (Exception ex){
            errorMsg= "判断分拣入库任务是否已经完成异常:"+ex;
            transResult= CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }
}
