package com.api.eap.project.thailand.guanghe;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.db.CFuncDbSqlResolve;
import com.api.common.utils.CFuncUtilsCellScada;
import com.api.common.utils.CFuncUtilsLayUiResut;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import com.mongodb.client.MongoCursor;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * <p>
 * (泰国广合)投收扳机标准发送流程接口
 * 1.[接口]请求叫料
 * 2.[接口]准备生产报告
 * 3.[接口]开始生产报告
 * 4.EAP综合读码上报
 * 5.EAP子批次完板上报
 * 6.EAP综合母批完板上报
 * 7.[接口]请求卸料报告
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-28
 */
@RestController
@Slf4j
@RequestMapping("/eap/project/thailand/guanghe/interf/send")
public class EapTlGhSendFlowController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private CFuncDbSqlResolve cFuncDbSqlResolve;
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private CFuncUtilsCellScada cFuncUtilsCellScada;
    @Autowired
    private EapTlGhSendFlowFunc eapTlGhSendFlowFunc;
    @Autowired
    private OpCommonFunc opCommonFunc;

    //1.[接口]请求叫料
    @RequestMapping(value = "/LoadRequestReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhLoadRequestReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/LoadRequestReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String request_mode = jsonParas.getString("request_mode");
            eapTlGhSendFlowFunc.LoadRequestReport(station_code, port_code, request_mode);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "EAP请求叫料异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //2.[接口]准备生产报告
    @RequestMapping(value = "/LotReadyReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhLotReadyReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/LotReadyReport";
        String transResult = "";
        String errorMsg = "";
        String need_wait_eap_result = "Y";
        String apsPlanTable = "a_eap_aps_plan";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String group_id = jsonParas.getString("group_id");
            String plan_id = jsonParas.getString("plan_id");
            String allow_start_type = jsonParas.getString("allow_start_type");//0:不请求,1:按载具请求,2:按Lot请求,3:按Lot读码第一片
            if (allow_start_type.equals("0")) {
                need_wait_eap_result = "N";
                transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, need_wait_eap_result, "", 0);
                return transResult;
            }

            //查询数据
            Integer lot_index = 0;
            String lot_num = "";
            String pallet_num = "";
            Integer plan_lot_num = 0;
            String item_info = "";
            String recipe_id = "";
            String vehicleno_eq = "";
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(Long.parseLong(station_id)));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).find(queryBigData.getQueryObject()).
                    sort(queryBigData.getSortObject()).noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                Document docItemBigData = iteratorBigData.next();
                lot_index = docItemBigData.getInteger("lot_index");
                lot_num = docItemBigData.getString("lot_num");
                pallet_num = docItemBigData.getString("pallet_num");
                plan_lot_num = docItemBigData.getInteger("plan_lot_num");
                item_info = docItemBigData.getString("item_info");
                iteratorBigData.close();
            }
            if (item_info != null && !item_info.equals("")) {
                JSONObject jbItemInfo = JSONObject.parseObject(item_info);
                recipe_id = jbItemInfo.getString("recipeid") == null ? "" : jbItemInfo.getString("recipeid");
                vehicleno_eq = jbItemInfo.getString("vehiclenoEQ") == null ? "" : jbItemInfo.getString("vehiclenoEQ");
            }
            if (allow_start_type.equals("1")) {
                if (lot_index > 1) {
                    need_wait_eap_result = "N";
                    transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, need_wait_eap_result, "", 0);
                    return transResult;
                }
            }
            eapTlGhSendFlowFunc.LotReadyReport(station_code, port_code, lot_num, pallet_num, String.valueOf(plan_lot_num), recipe_id, vehicleno_eq);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, need_wait_eap_result, "", 0);
        } catch (Exception ex) {
            errorMsg = "准备生产报告请求异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //3.[接口]开始生产报告
    @RequestMapping(value = "/LotStartReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhLotStartReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/LotStartReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String lot_num = jsonParas.getString("lot_num");
            String pallet_num = jsonParas.getString("pallet_num");
            String plan_lot_num = jsonParas.getString("plan_lot_num");
            String recipe_id = jsonParas.getString("attribute1");
            String vehicleno_eq = jsonParas.getString("attribute2");
            String requestmode = jsonParas.getString("requestmode");
            eapTlGhSendFlowFunc.LotStartReport(station_code, port_code, lot_num, pallet_num, plan_lot_num, recipe_id, vehicleno_eq, requestmode);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "开始生产报告异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //4.EAP综合读码上报
    @RequestMapping(value = "/EapTlGhPanelReadReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhPanelReadReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/EapTlGhPanelReadReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String apsPlanBTable = "a_eap_aps_plan_d";
        String meUserTable = "a_eap_me_station_user";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String plan_id = jsonParas.getString("plan_id");
            String station_flow_id = jsonParas.getString("station_flow_id");
            String lot_num = jsonParas.getString("lot_num");
            String pallet_num = jsonParas.getString("pallet_num");
            String port_code = jsonParas.getString("port_code");
            String panel_index = jsonParas.getString("panel_index");
            Integer panel_ng_code = jsonParas.getInteger("panel_ng_code");
            String panel_barcode = jsonParas.getString("panel_barcode");
            Integer panel_model = jsonParas.getInteger("panel_model");
            Integer sys_model = jsonParas.getInteger("sys_model");
            String dummy_flag = jsonParas.getString("dummy_flag");
            String inspect_flag = jsonParas.getString("inspect_flag");
            String aysn_lot_unLoad = jsonParas.getString("aysn_lot_unLoad");
            String first_flag = jsonParas.getString("first_flag");
            String SideExitMode = jsonParas.getString("SideExitMode");

            if (first_flag == null) {
                if (panel_index.equals("1")) first_flag = "Y";
                else first_flag = "N";
            }

            String station_code = "";
            String station_attr = "";
            String mid_flag = "N";//制中件片，N：批量片
            String lot_type = "1";//1.正常批次读码，2：侧出暂存机未出首件结果的批次读码
            String recipe_id = "";
            String vehicleno_eq = "";
            String panel_status = "NG";
            Integer plan_lot_num = 0;
            if (panel_ng_code == 0) panel_status = "OK";
            if (panel_ng_code == 4) panel_status = "NG_PASS";

            //查询工位信息
            String sqlStation = "select " +
                    "station_code,COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + " " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                    false, null, "");
            if (itemListStation != null && itemListStation.size() > 0) {
                station_code = itemListStation.get(0).get("station_code").toString();
                station_attr = itemListStation.get(0).get("station_attr").toString();
            }

            //读码上报
            if (sys_model > 0 && plan_id != null && !plan_id.equals("")) {
                Boolean isCanSend = false;
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).
                        find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                        noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    isCanSend = true;
                    Document docItemBigData = iteratorBigData.next();
                    plan_lot_num = docItemBigData.getInteger("plan_lot_count");
                    String item_info = docItemBigData.getString("item_info");
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jbItemInfo = JSONObject.parseObject(item_info);
                        recipe_id = jbItemInfo.getString("recipeid") == null ? "" : jbItemInfo.getString("recipeid");
                        vehicleno_eq = jbItemInfo.getString("vehiclenoEQ") == null ? "" : jbItemInfo.getString("vehiclenoEQ");
                    }
                    iteratorBigData.close();
                }
                if (isCanSend) {
                    //1.若是第一片则上报开始生产报告
                    if (first_flag.equals("Y")) {
                        if (station_attr.equals("Load") && SideExitMode == null) {
                            eapTlGhSendFlowFunc.LotStartReport(station_code, port_code, lot_num, pallet_num,
                                    String.valueOf(plan_lot_num), recipe_id, vehicleno_eq, station_attr.toLowerCase());
                        }
                    }
                    //2.读码上报事件
                    if (dummy_flag.equals("N")) {
                        eapTlGhSendFlowFunc.PanelReadReport(station_code, port_code, lot_num, panel_barcode, inspect_flag,
                                mid_flag, first_flag, lot_type, recipe_id, panel_status);
                    }
                }
            }

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "板件读码报告异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.EAP子批次完板上报
    @RequestMapping(value = "/EapTlGhSubLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhSubLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/EapTlGhSubLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            Integer sys_model = jsonParas.getInteger("sys_model");
            String plan_id = jsonParas.getString("plan_id");
            String requestmode = jsonParas.getString("requestmode");
            String aisNgPortFlag = jsonParas.getString("aisNgPortFlag");
            if (aisNgPortFlag == null || aisNgPortFlag.equals("")) aisNgPortFlag = "N";
            String last_lot_flag = "N";//一批多车收板，是否是最后一车

            Boolean isExistLot = false;
            String lot_num = "";
            String pallet_num = "";
            Integer finish_count = 0;
            Integer inspect_finish_count = 0;
            Integer target_lot_count = 0;
            String item_info = "";
            String recipe_id = "";
            String vehicleno_eq = "";
            JSONArray finish_result = new JSONArray();
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件
            String station_attr = "";
            String station_offline_flag = "N";//最后一个工位标识
            String heatnumber = "";//炉批
            //查询工位属性
            String sqlStation = "select " +
                    "COALESCE(station_attr,'') station_attr,COALESCE(offline_flag,'N') offline_flag " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + " " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                    false, null, "");
            if (itemListStation != null && itemListStation.size() > 0) {
                station_attr = itemListStation.get(0).get("station_attr").toString();
                station_offline_flag = itemListStation.get(0).get("offline_flag").toString();
            }

            //根据子批查询信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).
                    find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                    noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                isExistLot = true;
                Document docItemBigData = iteratorBigData.next();
                lot_num = docItemBigData.getString("lot_num");
                pallet_num = docItemBigData.getString("pallet_num");
                finish_count = docItemBigData.getInteger("finish_ok_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                target_lot_count = docItemBigData.getInteger("target_lot_count");
                item_info = docItemBigData.getString("item_info");
                iteratorBigData.close();
            }

            //若存在
            if (isExistLot) {
                if (item_info != null && !item_info.equals("")) {
                    JSONObject jbItemInfo = JSONObject.parseObject(item_info);
                    recipe_id = jbItemInfo.getString("recipeid") == null ? "" : jbItemInfo.getString("recipeid");
                    vehicleno_eq = jbItemInfo.getString("vehiclenoEQ") == null ? "" : jbItemInfo.getString("vehiclenoEQ");
                    firstmode = jbItemInfo.getString("firstmode") == null ? "" : jbItemInfo.getString("firstmode");
                    if (firstmode.equals("2"))
                        finish_count = inspect_finish_count;
                    String heatnumberListStr = jbItemInfo.getString("heatnumberList");
                    if (heatnumberListStr != null) {
                        JSONArray heatnumberList = JSONArray.parseArray(heatnumberListStr);
                        if (heatnumberList != null && heatnumberList.size() > 0) {
                            JSONObject jbItem = heatnumberList.getJSONObject(0);
                            heatnumber = jbItem.getString("heatnumber");
                        }
                    }
                }
                //查询过站明细
                String[] panel_status = new String[]{"OK", "NG_PASS"};
                if (aisNgPortFlag.equals("Y")) panel_status = new String[]{"NG"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                queryBigData.addCriteria(Criteria.where("port_code").is(port_code));
                if (!aisNgPortFlag.equals("Y")) {
                    queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                    queryBigData.addCriteria(Criteria.where("straight_flag").ne("Y"));
                }
                queryBigData.addCriteria(Criteria.where("panel_status").in(panel_status));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).
                        find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                        noCursorTimeout(true).batchSize(100).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    String panel_barcode = docItemBigData.getString("panel_barcode");
                    String panel_attr = docItemBigData.getString("panel_attr");
                    JSONObject jbDetail = new JSONObject();
                    jbDetail.put("pnlid", panel_barcode);
                    jbDetail.put("flag", panel_attr);
                    finish_result.add(jbDetail);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
                //线尾收板工位&所有板件都是首件板=端口号给100
                if (station_offline_flag.equals("Y")) {
                    Query queryCount = new Query();
                    queryCount.addCriteria(Criteria.where("station_id").is(station_id));
                    queryCount.addCriteria(Criteria.where("plan_id").is(plan_id));
                    queryCount.addCriteria(Criteria.where("panel_attr").is("1"));
                    long count = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryCount.getQueryObject());
                    if (target_lot_count == (int) count) {
                        port_code = "100";
                    }
                }
                finish_count = finish_result.size();//完板数量
            }

            //需要判断是否是一批多车中的最后一车
            if (lot_num != null && !lot_num.equals("")) {
                String[] lot_status_list = new String[]{"PLAN", "WORK"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("lot_num").is(lot_num));
                queryBigData.addCriteria(Criteria.where("group_lot_status").in(lot_status_list));
                long planCount = mongoTemplate.getCollection(apsPlanTable).countDocuments(queryBigData.getQueryObject());
                if (planCount <= 0) {
                    last_lot_flag = "Y";
                } else {
                    last_lot_flag = "N";
                }
            }
            //判断是否是ng工位退载具工序
            String NgOutProcess = cFuncDbSqlResolve.GetParameterValue("NgOutProcess");
            if (NgOutProcess.equals("Y")) {
                //NG工位退载具，需要上报endtype=0
                if (aisNgPortFlag.equals("Y")) last_lot_flag = "Y";
                else last_lot_flag = "N";
                try {
                    //获取eap下发的点位上的载具码
                    pallet_num = opCommonFunc.ReadCellOneRedisValue(station_code, station_attr, "Eap", "EapOutStatus", "PalletNum" + port_code);
                } catch (Exception ex) {
                }
            }
            //上报WIP
            eapTlGhSendFlowFunc.LotEndReport(station_code, port_code, lot_num, pallet_num, firstmode,
                    finish_count, recipe_id, vehicleno_eq, finish_result, requestmode, last_lot_flag, heatnumber);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "EAP子批次完板上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //5.EAP子批次完板上报-配板机
    @RequestMapping(value = "/EapTlGhSubLotFinishReport2", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhSubLotFinishReport2(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/EapTlGhSubLotFinishReport2";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            Integer sys_model = jsonParas.getInteger("sys_model");
            String plan_id = jsonParas.getString("plan_id");
            String requestmode = jsonParas.getString("requestmode");
            String portFinishTypeValue = jsonParas.getString("portFinishTypeValue");

            String last_lot_flag = "N";//一批多车收板，是否是最后一车

            Boolean isExistLot = false;
            String lot_num = "";
            String pallet_num = "";
            Integer finish_count = 0;
            Integer inspect_finish_count = 0;
            Integer target_lot_count = 0;
            String item_info = "";
            String recipe_id = "";
            String vehicleno_eq = "";
            JSONArray finish_result = new JSONArray();
            String firstmode = "0";//首件模式：0：不做首件，1：停机首件，2：下料首件
            String station_attr = "";
            String station_offline_flag = "N";//最后一个工位标识
            String heatnumber = "";//炉批
            //查询工位属性
            String sqlStation = "select " +
                    "COALESCE(station_attr,'') station_attr,COALESCE(offline_flag,'N') offline_flag " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + " " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                    false, null, "");
            if (itemListStation != null && itemListStation.size() > 0) {
                station_attr = itemListStation.get(0).get("station_attr").toString();
                station_offline_flag = itemListStation.get(0).get("offline_flag").toString();
            }

            //根据子批查询信息
            Query queryBigData = new Query();
            queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
            queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
            MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).
                    find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                    noCursorTimeout(true).batchSize(1).iterator();
            if (iteratorBigData.hasNext()) {
                isExistLot = true;
                Document docItemBigData = iteratorBigData.next();
                lot_num = docItemBigData.getString("lot_num");
                pallet_num = docItemBigData.getString("pallet_num");
                finish_count = docItemBigData.getInteger("finish_ok_count");
                inspect_finish_count = docItemBigData.getInteger("inspect_finish_count");
                target_lot_count = docItemBigData.getInteger("target_lot_count");
                item_info = docItemBigData.getString("item_info");
                iteratorBigData.close();
            }

            //若存在
            if (isExistLot) {
                if (item_info != null && !item_info.equals("")) {
                    JSONObject jbItemInfo = JSONObject.parseObject(item_info);
                    recipe_id = jbItemInfo.getString("recipeid") == null ? "" : jbItemInfo.getString("recipeid");
                    vehicleno_eq = jbItemInfo.getString("vehiclenoEQ") == null ? "" : jbItemInfo.getString("vehiclenoEQ");
                    firstmode = jbItemInfo.getString("firstmode") == null ? "" : jbItemInfo.getString("firstmode");
                    if (firstmode.equals("2"))
                        finish_count = inspect_finish_count;

                    //配套收板机完批的时候，将heatnumberList中的工单(layerlot)传到lotno字段，上报给MES
                    String heatnumberListStr = jbItemInfo.getString("heatnumberList");
                    if (heatnumberListStr != null) {
                        JSONArray heatnumberList = JSONArray.parseArray(heatnumberListStr);
                        if (heatnumberList != null && heatnumberList.size() > 0) {
                            JSONObject jbItem = heatnumberList.getJSONObject(0);
                            lot_num = jbItem.getString("layerlot");
                            heatnumber = jbItem.getString("heatnumber");
                        }
                    }
                }
                //查询过站明细
                String[] panel_status = new String[]{"OK", "NG_PASS"};
                queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                queryBigData.addCriteria(Criteria.where("dummy_flag").is("N"));
                queryBigData.addCriteria(Criteria.where("straight_flag").ne("Y"));
                queryBigData.addCriteria(Criteria.where("panel_status").in(panel_status));
                queryBigData.with(Sort.by(Sort.Direction.ASC, "_id"));
                iteratorBigData = mongoTemplate.getCollection(meStationFlowTable).
                        find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                        noCursorTimeout(true).batchSize(100).iterator();
                while (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    String panel_barcode = docItemBigData.getString("panel_barcode");
                    String panel_attr = docItemBigData.getString("panel_attr");
                    JSONObject jbDetail = new JSONObject();
                    jbDetail.put("pnlid", panel_barcode);
                    jbDetail.put("flag", panel_attr);
                    finish_result.add(jbDetail);
                }
                if (iteratorBigData.hasNext()) iteratorBigData.close();
                //线尾收板工位&所有板件都是首件板=端口号给100
                if (station_offline_flag.equals("Y")) {
                    Query queryCount = new Query();
                    queryCount.addCriteria(Criteria.where("station_id").is(station_id));
                    queryCount.addCriteria(Criteria.where("plan_id").is(plan_id));
                    queryCount.addCriteria(Criteria.where("panel_attr").is("1"));
                    long count = mongoTemplate.getCollection(meStationFlowTable).countDocuments(queryCount.getQueryObject());
                    if (target_lot_count == (int) count) {
                        port_code = "100";
                    }
                }
                finish_count = finish_result.size();//完板数量
            }

            //需要判断是否是一批多车中的最后一车
            if (portFinishTypeValue.equals("1")) {
                last_lot_flag = "Y";
            } else {
                last_lot_flag = "N";
            }
            //上报WIP
            eapTlGhSendFlowFunc.LotEndReport(station_code, port_code, lot_num, pallet_num, firstmode,
                    finish_count, recipe_id, vehicleno_eq, finish_result, requestmode, last_lot_flag, heatnumber);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "EAP子批次完板上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //6.EAP综合母批完板上报
    @RequestMapping(value = "/EapTlGhFinalLotFinishReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhFinalLotFinishReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/EapTlGhFinalLotFinishReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_id = jsonParas.getString("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            Integer sys_model = jsonParas.getInteger("sys_model");
            String OneCarMultyLotFlag = jsonParas.getString("OneCarMultyLotFlag");//一车多批模式(0一车一批、1一车多批、2一批多车)
            String plan_id_list = jsonParas.getString("plan_id_list");
            String out_code = jsonParas.getString("out_code");
            String requestmode = jsonParas.getString("requestmode");
            String aisNgPortFlag = jsonParas.getString("aisNgPortFlag");
            String local_flag = "Y";
            if (sys_model > 0) local_flag = "N";
            if (plan_id_list != null && !plan_id_list.equals("")) {
                String[] planIdList = plan_id_list.split(",", -1);
                if (planIdList != null && planIdList.length > 0) {
                    for (int i = 0; i < planIdList.length; i++) {
                        String plan_id = planIdList[i];
                        if (plan_id != null && !plan_id.equals("")) {
                            JSONObject jsonParas2 = new JSONObject();
                            jsonParas2.put("station_id", station_id);
                            jsonParas2.put("station_code", station_code);
                            jsonParas2.put("port_code", port_code);
                            jsonParas2.put("sys_model", sys_model);
                            jsonParas2.put("plan_id", plan_id);
                            jsonParas2.put("out_code", out_code);
                            jsonParas2.put("local_flag", local_flag);
                            jsonParas2.put("requestmode", requestmode);
                            jsonParas2.put("aisNgPortFlag", aisNgPortFlag);
                            EapTlGhSubLotFinishReport(jsonParas2, request);
                        }
                    }
                }
            }
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "EAP综合母批完板上报异常:" + ex.getMessage();
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //8.[接口]请求卸料报告
    @RequestMapping(value = "/UnLoadRequestReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhUnLoadRequestReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/UnLoadRequestReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meStationFlowTable = "a_eap_me_station_flow";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String pre_call = jsonParas.getString("pre_call");
            String unload_type = jsonParas.getString("unload_type");
            String plan_id_list = jsonParas.getString("plan_id_list");
            String requestmode = jsonParas.getString("requestmode");
            if (pre_call == null || pre_call.equals("")) pre_call = "0";
            if (unload_type == null || unload_type.equals("")) unload_type = "0";

            String number = jsonParas.getString("number");//配板机剩余数量
            if (number == null) number = "0";
            String station_attr = "";

            //查询工位属性
            String sqlStation = "select " +
                    "COALESCE(station_attr,'') station_attr " +
                    "from sys_fmod_station " +
                    "where station_id=" + station_id + " " +
                    "LIMIT 1 OFFSET 0";
            List<Map<String, Object>> itemListStation = cFuncDbSqlExecute.ExecSelectSql("AIS", sqlStation,
                    false, null, "");
            if (itemListStation != null && itemListStation.size() > 0) {
                station_attr = itemListStation.get(0).get("station_attr").toString();
            }

            //查询数据,若是放扳机则不需要
            JSONArray arrlist = new JSONArray();
            if (station_attr.equals("UnLoad")) {
                if (plan_id_list != null && !plan_id_list.equals("")) {
                    String[] planIdList = plan_id_list.split(",", -1);
                    if (planIdList != null && planIdList.length > 0) {
                        for (int i = 0; i < planIdList.length; i++) {
                            String plan_id = planIdList[i];
                            if (plan_id != null && !plan_id.equals("")) {
                                //收扳机不能用group_id来做处理,等收板机开发的时候再来修改
                                Query queryBigData = new Query();
                                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).
                                        find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                                        noCursorTimeout(true).batchSize(1).iterator();
                                if (iteratorBigData.hasNext()) {
                                    Document docItemBigData = iteratorBigData.next();
                                    JSONObject jbItem = new JSONObject();
                                    jbItem.put("lotno", docItemBigData.getString("lot_num"));
                                    jbItem.put("number", String.valueOf(docItemBigData.getInteger("finish_ok_count")));
                                    jbItem.put("carrierid", docItemBigData.getString("pallet_num"));
                                    String item_info = docItemBigData.getString("item_info");
                                    String recipe_id = "";
                                    String vehicleno_eq = "";
                                    if (item_info != null && !item_info.equals("")) {
                                        JSONObject jbItemInfo = JSONObject.parseObject(item_info);
                                        recipe_id = jbItemInfo.getString("recipeid") == null ? "" : jbItemInfo.getString("recipeid");
                                        vehicleno_eq = jbItemInfo.getString("vehiclenoEQ") == null ? "" : jbItemInfo.getString("vehiclenoEQ");
                                    }
                                    jbItem.put("recipeid", recipe_id);
                                    jbItem.put("vehiclenoEQ", vehicleno_eq);

                                    JSONArray pnlids = new JSONArray();
                                    //读取过站数据
                                    String[] panel_status_list = new String[]{"OK", "NG_PASS"};
                                    Query queryFlowBigData = new Query();
                                    queryFlowBigData.addCriteria(Criteria.where("station_id").is(station_id));
                                    queryFlowBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                                    queryFlowBigData.addCriteria(Criteria.where("panel_status").in(panel_status_list));
                                    List<Map> panelList = mongoTemplate.find(queryFlowBigData, Map.class, meStationFlowTable);
                                    if (panelList != null && panelList.size() > 0) {
                                        for (int j = 0; j < panelList.size(); j++) {
                                            String panel_barcode = panelList.get(j).get("panel_barcode").toString();
                                            pnlids.add(panel_barcode);
                                        }
                                    }
                                    jbItem.put("pnlids", pnlids);
                                    arrlist.add(jbItem);
                                    iteratorBigData.close();
                                }
                            }
                        }
                    }
                }
            }

            //配板机需要上报剩余数量
            String PbjLoadFlag = cFuncDbSqlResolve.GetParameterValue("PbjLoadFlag");
            if (PbjLoadFlag.equals("Y")) {
                JSONObject jbItem = new JSONObject();
                jbItem.put("lotno", "");
                jbItem.put("number", number);
                jbItem.put("carrierid", "");
                jbItem.put("recipeid", "");
                jbItem.put("vehiclenoEQ", "");
                JSONArray pnlids = new JSONArray();
                jbItem.put("pnlids", pnlids);
                arrlist.add(jbItem);
            }

            Integer rtn_code = eapTlGhSendFlowFunc.UnLoadRequestReport(station_code, port_code, pre_call, unload_type, arrlist, requestmode);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, rtn_code.toString(), "", 0);
        } catch (Exception ex) {
            errorMsg = "请求卸料报告异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //9.设备读板件ID请求批次信息
    @RequestMapping(value = "/EapTlGhPanelInfoRequestReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhPanelInfoRequestReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/EapTlGhPanelInfoRequestReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String panel_barcode = jsonParas.getString("panel_barcode");
            String lot_num = jsonParas.getString("lot_num");
            String inspect_flag = "N";
            String mid_flag = "N";//制中件片，N：批量片
            String first_flag = "Y";
            String lot_type = "1";
            String recipe_id = "";
            String panel_status = "OK";

            //设备读板件ID请求批次信息
            eapTlGhSendFlowFunc.PanelReadReport(station_code, port_code, lot_num, panel_barcode, inspect_flag,
                    mid_flag, first_flag, lot_type, recipe_id, panel_status);

            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "设备读板件ID请求批次信息异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //9.读载具ID报告事件
    @RequestMapping(value = "/EapTlGhCarriertLotInfoRequest", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhCarriertLotInfoRequest(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/EapTlGhCarriertLotInfoRequest";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            String pallet_num = jsonParas.getString("pallet_num");
            String port_code = jsonParas.getString("port_code");
            //设备读板件ID请求批次信息
            eapTlGhSendFlowFunc.CarriertLotInfoRequest(station_code, pallet_num, port_code);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "读载具ID报告事件异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //10.设备空闲端口数上报--配套机
    @RequestMapping(value = "/EapTlGhPortQtyReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhPortQtyReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/EapTlGhPortQtyReport";
        String transResult = "";
        String errorMsg = "";
        try {
            String station_code = jsonParas.getString("station_code");
            JSONArray port_list = jsonParas.getJSONArray("port_list");
            //设备读板件ID请求批次信息
            eapTlGhSendFlowFunc.PortQtyReport(station_code, port_list);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "设备空闲端口数上报异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }

    //11.配套外层批完工报告--配套机
    @RequestMapping(value = "/EapTlGhLayerLotEndReport", method = {RequestMethod.POST, RequestMethod.GET})
    public String EapTlGhLayerLotEndReport(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception {
        String apiRoutePath = "eap/project/thailand/guanghe/interf/send/EapTlGhLayerLotEndReport";
        String transResult = "";
        String errorMsg = "";
        String apsPlanTable = "a_eap_aps_plan";
        String meUpRecordTable = "a_eap_me_up_record";
        try {
            Long station_id = jsonParas.getLong("station_id");
            String station_code = jsonParas.getString("station_code");
            String port_code = jsonParas.getString("port_code");
            String plan_id = jsonParas.getString("plan_id");
            String recipe_id = "";
            String heatnumber = "";
            String layerlot = "";
            String vehicleno_eq = "";
            String setsqty = "0";
            if (plan_id != null && !plan_id.equals("")) {
                Query queryBigData = new Query();
                queryBigData.addCriteria(Criteria.where("station_id").is(station_id));
                queryBigData.addCriteria(Criteria.where("plan_id").is(plan_id));
                MongoCursor<Document> iteratorBigData = mongoTemplate.getCollection(apsPlanTable).
                        find(queryBigData.getQueryObject()).sort(queryBigData.getSortObject()).
                        noCursorTimeout(true).batchSize(1).iterator();
                if (iteratorBigData.hasNext()) {
                    Document docItemBigData = iteratorBigData.next();
                    String item_info = docItemBigData.getString("item_info");
                    if (item_info != null && !item_info.equals("")) {
                        JSONObject jbItemInfo = JSONObject.parseObject(item_info);
                        recipe_id = jbItemInfo.getString("recipeid") == null ? "" : jbItemInfo.getString("recipeid");
                        vehicleno_eq = jbItemInfo.getString("vehiclenoEQ") == null ? "" : jbItemInfo.getString("vehiclenoEQ");
                        JSONArray heatnumberList = jbItemInfo.getJSONArray("heatnumberList");
                        if (heatnumberList != null && heatnumberList.size() > 0) {
                            JSONObject jbItem = heatnumberList.getJSONObject(0);
                            heatnumber = jbItem.getString("heatnumber");
                            layerlot = jbItem.getString("layerlot");
                            setsqty = jbItem.getString("setsqty");
                        }
                    }
                }
            }
            JSONArray ink_lot_list = new JSONArray();//内层批信息
            //1.根据外层批号在a_eap_me_up_record查询内层批读码信息
            Query queryRecord = new Query();
            queryRecord.addCriteria(Criteria.where("station_id").is(station_id));
            queryRecord.addCriteria(Criteria.where("layerlot").is(layerlot));
            queryRecord.with(Sort.by(Sort.Direction.ASC, "lot_num"));
            MongoCursor<Document> iteratorRecord = mongoTemplate.getCollection(meUpRecordTable).
                    find(queryRecord.getQueryObject()).sort(queryRecord.getSortObject()).
                    noCursorTimeout(true).batchSize(100).iterator();
            Map<String, JSONArray> mapLot = new HashMap<>();
            while (iteratorRecord.hasNext()) {
                Document document = iteratorRecord.next();
                String lot_num = document.getString("lot_num");
                String panel_barcode = document.getString("panel_barcode");
                if (!mapLot.containsKey(lot_num)) {
                    JSONArray Pnllist = new JSONArray();
                    Pnllist.add(panel_barcode);
                    mapLot.put(lot_num, Pnllist);
                } else {
                    JSONArray Pnllist = mapLot.get(lot_num);
                    Pnllist.add(panel_barcode);
                    mapLot.put(lot_num, Pnllist);
                }
            }
            if (iteratorRecord.hasNext()) iteratorRecord.close();
            Set<String> keys = mapLot.keySet();
            for (String key : keys) {
                JSONArray Pnllist = mapLot.get(key);
                JSONObject ink_lot = new JSONObject();
                ink_lot.put("lotno", key);
                ink_lot.put("number", Pnllist.size());
                ink_lot.put("Pnllist", Pnllist);
                ink_lot_list.add(ink_lot);
            }
            //2.设备读板件ID请求批次信息
            eapTlGhSendFlowFunc.LayerLotEndReport(station_code, port_code, recipe_id, heatnumber, layerlot, setsqty, vehicleno_eq, ink_lot_list);
            transResult = CFuncUtilsLayUiResut.GetStandJson(true, null, "1", "", 0);
        } catch (Exception ex) {
            errorMsg = "配套外层批完工报告异常:" + ex;
            transResult = CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return transResult;
    }


}
