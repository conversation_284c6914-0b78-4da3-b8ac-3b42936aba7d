package com.api.pack.core.pile;

import com.alibaba.fastjson.annotation.JSONField;
import com.api.base.Const;
import com.api.base.IMongoBasic;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Transient;
import org.springframework.data.domain.Example;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

/**
 * <p>
 * Pile
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-18
 */
@ApiModel(value = "Pile", description = "包信息")
@Document("a_pack_me_pile")
@Data
@EqualsAndHashCode(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
public class Pile extends IMongoBasic
{
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "ID")
    @JsonProperty("pile_id")
    @JSONField(name = "pile_id")
    @Field("pile_id")
    private String pileId;

    @ApiModelProperty(value = "打包条码")
    @JsonProperty("pile_barcode")
    @JSONField(name = "pile_barcode")
    @Field("pile_barcode")
    private String pileBarcode;

    @ApiModelProperty(value = "客户条码")
    @JsonProperty("custom_barcode")
    @JSONField(name = "custom_barcode")
    @Field("custom_barcode")
    private String customBarcode;

    @ApiModelProperty(value = "订单号")
    @JsonProperty("lot_num")
    @JSONField(name = "lot_num")
    @Field("lot_num")
    private String lotNum;

    @ApiModelProperty(value = "料号（型号）")
    @JsonProperty("model_type")
    @JSONField(name = "model_type")
    @Field("model_type")
    private String modelType;

    @ApiModelProperty(value = "订单中打包序号")
    @JsonProperty("pile_index")
    @JSONField(name = "pile_index")
    @Field("pile_index")
    private Integer pileIndex;

    @ApiModelProperty(value = "打包中SET总数")
    @JsonProperty("array_count")
    @JSONField(name = "array_count")
    @Field("array_count")
    private Integer arrayCount;

    @ApiModelProperty(value = "是否尾箱")
    @JsonProperty("trunk_flag")
    @JSONField(name = "trunk_flag")
    @Field("trunk_flag")
    private String trunkFlag;

    @ApiModelProperty(value = "打包重量")
    @JsonProperty("pile_weight")
    @JSONField(name = "pile_weight")
    @Field("pile_weight")
    private Double pileWeight;

    @ApiModelProperty(value = "打包人员")
    @JsonProperty("pile_user")
    @JSONField(name = "pile_user")
    @Field("pile_user")
    private String pileUser;

    @ApiModelProperty(value = "PC计划SET集合")
    @JsonProperty("pc_array_list")
    @JSONField(name = "pc_array_list")
    @Field("pc_array_list")
    private String pcArrayList;

    @ApiModelProperty(value = "PLC反馈SET集合")
    @JsonProperty("plc_array_list")
    @JSONField(name = "plc_array_list")
    @Field("plc_array_list")
    private String plcArrayList;

    @ApiModelProperty(value = "OK/NG")
    @JsonProperty("pile_status")
    @JSONField(name = "pile_status")
    @Field("pile_status")
    private String pileStatus;

    @ApiModelProperty(value = "打包验证NG代码")
    @JsonProperty("pile_ng_code")
    @JSONField(name = "pile_ng_code")
    @Field("pile_ng_code")
    private Integer pileNgCode;

    @ApiModelProperty(value = "打包验证NG描述")
    @JsonProperty("pile_ng_msg")
    @JSONField(name = "pile_ng_msg")
    @Field("pile_ng_msg")
    private String pileNgMsg;

    @ApiModelProperty(value = "是否解绑")
    @JsonProperty("unbind_flag")
    @JSONField(name = "unbind_flag")
    @Field("unbind_flag")
    private String unbindFlag;

    @ApiModelProperty(value = "解绑人")
    @JsonProperty("unbind_user")
    @JSONField(name = "unbind_user")
    @Field("unbind_user")
    private String unbindUser;

    @ApiModelProperty(value = "解绑时间")
    @JsonProperty("unbind_time")
    @JSONField(name = "unbind_time")
    @Field("unbind_time")
    private String unbindTime;

    @ApiModelProperty(value = "解绑方式说明")
    @JsonProperty("unbind_way")
    @JSONField(name = "unbind_way")
    @Field("unbind_way")
    private String unbindWay;

    @ApiModelProperty(value = "重工标记")
    @JsonProperty("retry_flag")
    @JSONField(name = "retry_flag")
    @Field("retry_flag")
    private String retryFlag;

    @ApiModelProperty(value = "计划ID")
    @JsonProperty("plan_id")
    @JSONField(name = "plan_id")
    @Field("plan_id")
    private String planId;

    public static Pile byPileBarcode(String pileBarcode, PileService service)
    {
        return service.findOneByPileBarcodeAndEnableFlag(pileBarcode, null);
    }

    public static Pile byPileBarcodeAndPlanId(String pileBarcode, String planId, PileService service)
    {
        Pile query = new Pile();
        query.setPileBarcode(pileBarcode);
        query.setPlanId(planId);
        return service.findOne(Example.of(query)).orElse(null);
    }

    public static List<Pile> listByPlanId(String planId, PileService service)
    {
        Pile query = new Pile();
        query.setPlanId(planId);
        return service.findAll(Example.of(query));
    }

    public static boolean isEmptyByPlanId(String planId, PileService service)
    {
        Pile query = new Pile();
        query.setPlanId(planId);
        query.setEnableFlag(Const.FLAG_Y);
        return service.count(Example.of(query)) == 0;
    }

    @Transient
    public boolean isTail()
    {
        return Const.FLAG_Y.equals(this.trunkFlag);
    }
}
