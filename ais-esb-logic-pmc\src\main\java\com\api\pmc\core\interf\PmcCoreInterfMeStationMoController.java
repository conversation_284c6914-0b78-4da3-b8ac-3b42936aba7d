package com.api.pmc.core.interf;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsLayUiResut;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 接口
 * 1.
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-16
 */
@RestController
@Slf4j
@RequestMapping("/pmc/core/interf")
public class PmcCoreInterfMeStationMoController {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;

    //1.获取订单顺序
    @RequestMapping(value = "/PmcCoreMeStationMoQueue", method = {RequestMethod.POST,RequestMethod.GET})
    public String PmcCoreMeStationMoQueue(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="pmc/core/interf/PmcCoreMeStationMoQueue";
        String selectResult="";
        String errorMsg="";
        try{
            log.info("获取订单顺序:"+jsonParas.toString());

            //获取参数
            String request_uuid=jsonParas.getString("request_uuid");//请求GUID（唯一标识码）
            String request_time=jsonParas.getString("request_time");//请求时间
            String request_attr=jsonParas.getString("request_attr");//预留请求属性
            String data_from_sys=jsonParas.getString("data_from_sys");//来源系统
            String make_order=jsonParas.getString("make_order");//订单号
            String work_center_code=jsonParas.getString("work_center_code");//车间（ZA:总装、TA:涂装、HA:焊装、CA:冲压）
            String station_code=jsonParas.getString("station_code");//工位号
            //返回结果
            List<Map<String, Object>> queueList=new ArrayList<>();
            //1、获取工位生产订单
            Integer mo_work_order=0;//生产订单顺序,针对订单进行排序
            String sqlStationMo="select COALESCE(mo_work_order,0) mo_work_order " +
                    "from d_pmc_me_station_mo " +
                    "where set_sign='NONE' " +
                    "and station_code='"+station_code+"' "+
                    "and make_order='"+make_order+"' "+
                    "LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStationMo=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlStationMo,false,null,apiRoutePath);
            if(itemListStationMo!=null && itemListStationMo.size()>0){
                mo_work_order=Integer.parseInt(itemListStationMo.get(0).get("mo_work_order").toString());
            }
            //2、获取工位生产订单(下一个)
            String make_order_next="";
            String mo_work_order_next="";
            String sqlStationMoNext="select COALESCE(make_order,'') make_order," +
                    "COALESCE(mo_work_order,0) mo_work_order " +
                    "from d_pmc_me_station_mo " +
                    "where work_status='PLAN' " +
                    "and set_sign='NONE' " +
                    "and mo_work_order > "+ mo_work_order +" " +
                    "and station_code='"+station_code+"' ";
            if(make_order!=null && !make_order.equals("")) {
                sqlStationMoNext+="and make_order<>'"+make_order+"' ";
            }
            sqlStationMoNext+="order by mo_work_order LIMIT 1 OFFSET 0 ";
            List<Map<String,Object>> itemListStationMoNext=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlStationMoNext,false,null,apiRoutePath);
            if(itemListStationMoNext!=null && itemListStationMoNext.size()>0){
                make_order_next=itemListStationMoNext.get(0).get("make_order").toString();
                mo_work_order_next=itemListStationMoNext.get(0).get("mo_work_order").toString();
            }
            //3、获取线首信息
            String main_material_code_next="";
            String dms_next="";
            String sqlOnline="select COALESCE(white_car_adjust,'') main_material_code," +
                    "COALESCE(dms,'') dms " +
                    "from d_pmc_me_flow_online " +
                    "where enable_flag='Y' " +
                    "and work_center_code='"+work_center_code+"' "+
                    "and make_order='"+make_order_next+"' "+
                    "limit 1 offset 0";
            List<Map<String,Object>> itemListOnline=cFuncDbSqlExecute.ExecSelectSql(station_code,sqlOnline,false,null,apiRoutePath);
            if(itemListOnline!=null && itemListOnline.size()>0){
                main_material_code_next=itemListOnline.get(0).get("main_material_code").toString();
                dms_next=itemListOnline.get(0).get("dms").toString();
            }
            //4、返回结果
            Map<String, Object> queueItem=new HashMap<>();
            queueItem.put("make_order",make_order_next);
            queueItem.put("mo_work_order",mo_work_order_next);
            queueItem.put("main_material_code",main_material_code_next);
            queueItem.put("dms",dms_next);
            queueList.add(queueItem);
            selectResult=CFuncUtilsLayUiResut.GetStandJson(true,queueList,"","",0);
        }
        catch (Exception ex){
            errorMsg= "获取订单顺序异常"+ex.getMessage();
            selectResult=CFuncUtilsLayUiResut.GetErrorJson(errorMsg);
        }
        return selectResult;
    }

}
