package com.api.dcs.core.interf.cut;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.dcs.core.interf.DcsInterfCommon;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

/**
 * <p>
 * 切割接受流程对外接口
 * 1.接受切割完成
 * </p>
 *
 * <AUTHOR>
 * @since 2023-11-28
 */
@RestController
@Slf4j
@RequestMapping("/dcs/core/interf/cut/recv")
public class DcsCoreCutRecvController {
    @Autowired
    private CFuncLogInterf cFuncLogInterf;
    @Autowired
    private DcsCoreCutRecvFunc dcsCoreCutRecvFunc;
    @Autowired
    private DcsInterfCommon dcsInterfCommon;

    //1.接受切割完成
    @RequestMapping(value = "/DcsCoreCutRecvTaskFinish",produces = "application/json;charset=utf-8",  method = {RequestMethod.POST,RequestMethod.GET})
    public String DcsCoreCutRecvTaskFinish(@RequestBody JSONObject jsonParas, HttpServletRequest request) throws Exception{
        String apiRoutePath="dcs/core/interf/cut/recv/DcsCoreCutRecvTaskFinish";
        String startDate= CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult=dcsCoreCutRecvFunc.CutRecvTaskFinish(jsonParas,request,apiRoutePath);
        Boolean prodFlag=true;
        Boolean isSaveFlag=jbResult.getBoolean("isSaveFlag");
        String esbInterfCode=jbResult.getString("esbInterfCode");
        String token=jbResult.getString("token");
        String requestParas=jbResult.getString("requestParas");
        String responseParas=jbResult.getString("responseParas");
        Boolean successFlag=jbResult.getBoolean("successFlag");
        String message=jbResult.getString("message");
        String moID=jbResult.getString("moID");
        Integer code=jbResult.getInteger("code");
        String endDate= CFuncUtilsSystem.GetNowDateTime("");
        //记录日志
        if(isSaveFlag){
            cFuncLogInterf.Insert(startDate, endDate, esbInterfCode, prodFlag,token,requestParas,responseParas,
                    successFlag,  message, request);
            //记录任务事件
            if(moID!=null && !moID.equals("")){
                dcsInterfCommon.InsertApsTaskEvent(moID,token,esbInterfCode,"接受切割完成",
                        responseParas,requestParas,code,message,successFlag,startDate,endDate,"");
            }
        }
        return responseParas;
    }
}
