package com.api.eap.project.xinai;

import com.alibaba.fastjson.JSONObject;
import com.api.common.db.CFuncDbSqlExecute;
import com.api.common.utils.CFuncUtilsSystem;
import com.api.eap.core.share.OpCommonFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * <p>
 * 芯爱曝光机接受流程功能函数
 * 1.PanelLoadingMode:接受曝光机基板搬入模式通知
 * 2.ForcedPanelLoading2DCReply:接受曝光机是否可以投入以及2DC
 * </p>
 *
 * <AUTHOR>
 * @since 2024-04-18
 */
@Service
@Slf4j
public class EapXinaiLdiRecvFunc {
    @Autowired
    private CFuncDbSqlExecute cFuncDbSqlExecute;
    @Autowired
    private OpCommonFunc opCommonFunc;
    @Autowired
    private EapXinaiLdiInterfCommon eapXinaiLdiInterfCommon;

    //1.接受曝光机基板搬入模式通知
    public JSONObject PanelLoadingMode(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="PanelLoadingMode";
        String esbInterfCode="LdiPanelLoadingMode";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=1;
        String request_uuid= CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmssSSSSSS");
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("Header");
            jbRecvBody=jsonParas.getJSONObject("Body");
            request_uuid=jbRecvHeader.getString("TransactionID");
            String function_name=jbRecvHeader.getString("MessageName");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=0;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,station_code " +
                    "from sys_fmod_station " +
                    "where station_attr='Load' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("LDI",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=0;
                errorMsg="AIS未配置Load属性工位信息,AIS系统拒绝执行";
                responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String station_code=itemListStation.get(0).get("station_code").toString();
            Long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr="Load";

            //3.判断Mode
            if(jbRecvBody==null || !jbRecvBody.containsKey("Mode")){
                code=0;
                errorMsg="LDI未传递Mode字段,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0","-1","LDI",errorMsg,5);
                responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            Integer mode=jbRecvBody.getInteger("Mode");
            if(mode!=0 && mode!=1 && mode!=2){
                code=0;
                errorMsg="LDI传递Mode数据{"+mode+"}只能在0-2区间,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0","-1","LDI",errorMsg,5);
                responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //写入到PLC
            errorMsg=opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                        "Plc","PlcStatus","LdiModel",
                        "LDI",String.valueOf(mode),true);
            if(!errorMsg.equals("")){
                code=0;
                opCommonFunc.SaveCimMessage(station_id,"0","-2","LDI",errorMsg,5);
                responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //前端提醒
            String cim_msg="LDI通知放扳机";
            if(mode==0) cim_msg+="{投入停止}";
            else if(mode==2) cim_msg+="{Run-2DC}";
            else cim_msg+="{普通模式}";
            opCommonFunc.SaveCimMessage(station_id,"0","0","LDI",cim_msg,5);

            //返回
            responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=0;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapXinaiLdiInterfCommon.CreateResponse(funcName,request_uuid,jbRecvBody,code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }

    //2.接受LDI基板投入可否和2DC通知
    public JSONObject ForcedPanelLoading2DCReply(JSONObject jsonParas, HttpServletRequest request, String apiRoutePath) throws Exception{
        JSONObject jbResult=new JSONObject();
        String errorMsg="";
        String funcName="ForcedPanelLoading2DCReply";
        String esbInterfCode="LdiForcedPanelLoading2DCReply";
        String token="";
        String requestParas=jsonParas.toString();
        String responseParas="";
        JSONObject jbRecvHeader=null;
        JSONObject jbRecvBody=null;
        Integer code=1;
        String request_uuid= CFuncUtilsSystem.GetNowDateTime("yyyyMMddHHmmssSSSSSS");
        try{
            jbResult.put("isSaveFlag",true);
            jbResult.put("esbInterfCode",esbInterfCode);
            jbResult.put("token",token);
            jbResult.put("requestParas",requestParas);

            //接受参数
            jbRecvHeader=jsonParas.getJSONObject("Header");
            jbRecvBody=jsonParas.getJSONObject("Body");
            request_uuid=jbRecvHeader.getString("TransactionID");
            String function_name=jbRecvHeader.getString("MessageName");

            //1.判断方法是否一致
            if(!function_name.equals(funcName)){
                code=0;
                errorMsg="方法名{"+function_name+"}不为{"+funcName+"},AIS系统拒绝执行";
                responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //2.判断工位是否存在
            String sqlStation="select " +
                    "station_id,station_code " +
                    "from sys_fmod_station " +
                    "where station_attr='Load' " +
                    "order by station_id LIMIT 1 OFFSET 0";
            List<Map<String,Object>> itemListStation=cFuncDbSqlExecute.ExecSelectSql("LDI",sqlStation,false,request,apiRoutePath);
            if(itemListStation==null || itemListStation.size()<=0){
                code=0;
                errorMsg="AIS未配置Load属性工位信息,AIS系统拒绝执行";
                responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }
            String station_code=itemListStation.get(0).get("station_code").toString();
            Long station_id=Long.parseLong(itemListStation.get(0).get("station_id").toString());
            String station_attr="Load";

            //3.判断Mode
            if(jbRecvBody==null || !jbRecvBody.containsKey("Decision")){
                code=0;
                errorMsg="LDI未传递Mode字段,AIS系统拒绝执行";
                opCommonFunc.SaveCimMessage(station_id,"0","-1","LDI",errorMsg,5);
                responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            Integer decision=jbRecvBody.getInteger("Decision");

            //写入到PLC
            errorMsg=opCommonFunc.WriteCellOneTagValue(station_code,station_attr,
                    "Ais","AisStatus","LdiForceDecision",
                    "LDI",String.valueOf(decision),true);
            if(!errorMsg.equals("")){
                code=0;
                opCommonFunc.SaveCimMessage(station_id,"0","-2","LDI",errorMsg,5);
                responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,errorMsg);
                jbResult.put("responseParas",responseParas);
                jbResult.put("successFlag",false);
                jbResult.put("message",errorMsg);
                return jbResult;
            }

            //前端提醒
            String cim_msg="LDI通知放扳机传输段";
            String screen_control="0";
            if(decision==1) cim_msg+="{允许强制投入}";
            else{
                cim_msg+="{不允许强制投入},请人工尝试再次请求强制放行或者直接取走板件";
                screen_control="1";
            }
            opCommonFunc.SaveCimMessage(station_id,screen_control,"0","LDI",cim_msg,5);

            //返回
            responseParas=eapXinaiLdiInterfCommon.CreateResponse(function_name,request_uuid,jbRecvBody,code,"");
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",true);
            jbResult.put("message","接受成功");
        }
        catch (Exception ex){
            code=0;
            errorMsg="接口发生未知异常:"+ex.getMessage();
            responseParas=eapXinaiLdiInterfCommon.CreateResponse(funcName,request_uuid,jbRecvBody,code,errorMsg);
            jbResult.put("responseParas",responseParas);
            jbResult.put("successFlag",false);
            jbResult.put("message",errorMsg);
        }
        return jbResult;
    }
}
