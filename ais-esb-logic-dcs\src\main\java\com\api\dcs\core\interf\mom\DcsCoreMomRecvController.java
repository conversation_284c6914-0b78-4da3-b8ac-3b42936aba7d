package com.api.dcs.core.interf.mom;

import com.alibaba.fastjson.JSONObject;
import com.api.common.log.CFuncLogInterf;
import com.api.common.utils.CFuncUtilsSystem;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@Slf4j
@RequestMapping("/dcs/core/interf/mom/recv")
public class DcsCoreMomRecvController {
    @Resource
    private DcsCoreMomRecvFunc dcsCoreMomRecvFunc;

    @Resource
    private CFuncLogInterf cFuncLogInterf;

    @RequestMapping("/receiveMomProductionPlan")
    public String receiveMomProductionPlan(@RequestBody JSONObject jsonObject) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsCoreMomRecvFunc.receiveMomProductionPlan(jsonObject);
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        Integer code = jbResult.getInteger("code");
        cFuncLogInterf.Insert(startDate, endDate, "receiveMomProductionPlan", false, null, jsonObject.toJSONString(), jbResult.toJSONString(), Integer.valueOf(0).equals(code) ? true : false, jbResult.getString("msg"), null);
        return jbResult.toJSONString();
    }

    @RequestMapping("/cancelMomProductionPlan")
    public String cancelMomProductionPlan(@RequestBody JSONObject jsonObject) throws Exception {
        String startDate = CFuncUtilsSystem.GetNowDateTime("");
        JSONObject jbResult = dcsCoreMomRecvFunc.cancelMomProductionPlan(jsonObject);
        String endDate = CFuncUtilsSystem.GetNowDateTime("");
        Integer code = jbResult.getInteger("code");
        cFuncLogInterf.Insert(startDate, endDate, "cancelMomProductionPlan", false, null, jsonObject.toJSONString(), jbResult.toJSONString(), Integer.valueOf(0).equals(code) ? true : false, jbResult.getString("msg"), null);
        return jbResult.toJSONString();
    }
}
